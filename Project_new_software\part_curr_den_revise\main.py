'''
@Project ：pythonProject 
@File    ：main.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/11/22 9:47
主要用于进行电流密度图的生成, 相较于main，对电流密度图的背景稍作修改
'''
import time
from numpy.linalg import *
from function.cur_den_sim import *
from function.fun_colletion import *
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap


def main(mcg_data):
    """
    :param mcg_data: 单一时刻的磁场数据为一个1*36的向量
    :return:
    """
    "mcg_data插值"
    bz_ori = mcg_data * 10 ** (-12)
    x, y, bz_inter = mcg_interpolate(bz_ori)
    bz_inter = bz_inter[0, :, :]

    """
        生成电流密度图
    """
    object_2 = CurrentDensity(-bz_inter[::-1], x, y, 0)
    q_x, q_y = object_2.cur_density()
    normal_con = max(np.max(np.abs(q_x)), np.max(np.abs(q_y)))
    q_x, q_y = q_x / normal_con, q_y / normal_con
    x_1, y_1 = x[2::8, 2::8], y[2::8, 2::8]
    q_x_1, q_y_1 = q_x[2::8, 2::8], q_y[2::8, 2::8]
    strength = np.sqrt(q_x ** 2 + q_y ** 2)
    row, col = divmod(np.argmax(strength), q_x.shape[1])

    """
        求磁场正磁极和负磁极的中心连线
    """
    max_bz_loc, min_bz_loc = np.argmax(bz_inter[::-1]), np.argmin(bz_inter[::-1])
    max_row, max_col = divmod(max_bz_loc, bz_inter.shape[1])
    min_row, min_col = divmod(min_bz_loc, bz_inter.shape[1])
    center_point = np.array([[(x[max_row][max_col] + x[min_row][min_col]) / 2,
                              (y[max_row][max_col] + y[min_row][min_col]) / 2]])
    vector = np.array([[(x[max_row][max_col] - x[min_row][min_col]),
                        (y[max_row][max_col] - y[min_row][min_col])]])
    max_min_radius = norm(vector) / 2
    max_point = np.array([[x[row][col], y[row][col]]])
    distance = norm(center_point - max_point)

    if norm(center_point - max_point) < max_min_radius / 2:
        ...
    else:
        x_flat, y_flat = x.reshape(x.shape[0] ** 2, 1), y.reshape(y.shape[0] ** 2, 1)
        all_point = np.hstack((x_flat, y_flat))
        all_dis = norm(all_point - center_point, axis=1)
        """
         初始算法，只计算中心点, 作为电流最大的点
        """
        # min_dis_loc = np.argmin(all_dis)
        # row, col = divmod(min_dis_loc, q_x.shape[1])
        """
          改进算法，计算中心点扫描区域内，电流最大的点
        """
        mask = all_dis < max_min_radius / 2  # 选取在半径内的点
        index_set = np.argwhere(mask)
        strength_1 = strength.flatten()
        stren_set = strength_1[index_set[:, 0]]
        chose_index = index_set[np.argmax(stren_set)]
        row, col = divmod(chose_index[0], q_x.shape[1])    # 求出电流强度最大的点

    "作图"
    str_1 = strength[2::8, 2::8]
    fig, ax1 = plt.subplots()
    # 定义颜色映射的颜色列表
    ax1.set_facecolor((25 / 255, 25 / 255, 25 / 255))
    fig.patch.set_facecolor((25 / 255, 25 / 255, 25 / 255))
    colors = [(0, 'yellow'), (1, 'red')]  # 在0、0.5和1处分别定义红、绿、蓝三种颜色
    # 创建自定义颜色映射
    cmap_custom = LinearSegmentedColormap.from_list('custom_colormap', colors)
    ax1.pcolormesh(x, y, strength, cmap=cmap_custom)
    quiver1 = ax1.quiver(x_1, y_1, q_x_1, q_y_1, width=0.0025, scale=15, color='black')
    # quiver1 = ax1.quiver(x_1, y_1, q_x_1, q_y_1, width=0.005, headlength=3.5, headaxislength=3, scale=15, color='black')
    quiver2 = ax1.quiver(x[row][col], y[row][col], q_x[row][col], q_y[row][col], width=0.010, scale=10,
                         color='black')
    ax1.set_xlim([-0.1, 20.1])
    ax1.set_ylim([-0.1, 20.1])
    ax1.set_xticks([])
    ax1.set_yticks([])
    ax1.set_aspect('equal')
    plt.savefig('cur_den.png', facecolor=fig.get_facecolor(), edgecolor='none', bbox_inches='tight', dpi=300)

if __name__ == '__main__':
    start_time = time.time()
    mcg_data = np.array([0.592578, 1.393776, 2.199441, 3.099023, 1.816884, 0.801566,
                         0.997516, 2.898056, 5.087041, 4.513909, 2.251532, 1.014558,
                         0.907813, 2.807073, 3.351294, -2.161837, -1.087883, 0.176905,
                         0.188084, -0.340515, -2.789730, -7.080690,	-5.155373, -1.606127,
                         0.191104, -0.859270, -3.062833, -4.420899,	-3.380085, -1.273779,
                         0.197325, -0.462841, -1.110405, -1.428688,	-1.260064, -0.569730])
    main(mcg_data)
    end_time = time.time()
    print('operator time = %f' % (end_time - start_time))
