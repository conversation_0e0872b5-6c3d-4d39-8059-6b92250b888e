'''
@Project ：ML_Pipline 
@File    ：time_seq_features.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/8/19 上午10:58 
@Discribe：基于tsfresh的时序特征提取类。
'''
import os

import numpy as np
import pandas as pd
from tsfresh import extract_features as ts_extract_features
from tsfresh.feature_extraction.feature_calculators import (
    mean, variance, median, maximum, minimum,
    linear_trend, # Returns list of dicts, need to parse
    autocorrelation,
    sample_entropy,
    number_peaks
)


class TimeSeqFeatures:
    """基于tsfresh的时序特征提取类。"""

    def __init__(self, basic_args_dict, label):
        self.basic_args_dict = basic_args_dict
        self.label = label

    @staticmethod
    def decompose_and_calculate(matrix):
        # Ensure the matrix is 100x100
        if matrix.shape != (100, 100):
            return "The input matrix is not 100x100 in size."
        # Define quarter size
        quarter_size = 50

        # Split the matrix into 4 quarters
        Q1 = matrix[:quarter_size, :quarter_size]
        Q2 = matrix[:quarter_size, quarter_size:]
        Q3 = matrix[quarter_size:, :quarter_size]
        Q4 = matrix[quarter_size:, quarter_size:]

        # Extract 25x25 matrix from the center of each quarter
        sub_size = 25
        Q1_sub = Q1[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]
        Q2_sub = Q2[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]
        Q3_sub = Q3[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]
        Q4_sub = Q4[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]

        # Extract 25x25 matrix from the center of the whole matrix
        center_sub = matrix[50 - sub_size // 2:50 + sub_size // 2, 50 - sub_size // 2:50 + sub_size // 2]

        # Calculate means
        means = [Q1_sub.mean(), Q2_sub.mean(), Q3_sub.mean(), Q4_sub.mean(), center_sub.mean()]

        return means

    def get_custom_fc_parameters(self, version="v1"):
        custom_fc_parameters = {}
        sample_rate = 1000
        if version == "v1":
            # FFT Coefficients
            fft_coefficient_params = [
                {"attr": "real", "coeff": 33}, {"attr": "real", "coeff": 6},
                {"attr": "real", "coeff": 13}, {"attr": "real", "coeff": 50},
                {"attr": "abs", "coeff": 43}, {"attr": "angle", "coeff": 8},
                {"attr": "imag", "coeff": 25}
            ]
            # Aggregated Linear Trend
            agg_linear_trend_params = [
                {"attr": "stderr", "chunk_len": 10, "f_agg": "mean"},
                {"attr": "intercept", "chunk_len": 5, "f_agg": "var"}
            ]
            # Update parameters
            custom_fc_parameters["agg_linear_trend"] = agg_linear_trend_params
            custom_fc_parameters["fft_coefficient"] = fft_coefficient_params
        elif version == "v2":
            custom_fc_parameters = {
                "fft_coefficient": [
                    {"attr": "real", "coeff": 5, "sample_rate": sample_rate},
                    {"attr": "real", "coeff": 10, "sample_rate": sample_rate},
                    {"attr": "real", "coeff": 20, "sample_rate": sample_rate},
                    {"attr": "imag", "coeff": 5, "sample_rate": sample_rate},
                    {"attr": "imag", "coeff": 10, "sample_rate": sample_rate},
                    {"attr": "imag", "coeff": 20, "sample_rate": sample_rate},
                    {"attr": "abs", "coeff": 5, "sample_rate": sample_rate},
                    {"attr": "abs", "coeff": 10, "sample_rate": sample_rate},
                    {"attr": "abs", "coeff": 20, "sample_rate": sample_rate}
                ],
                "agg_linear_trend": [
                    {"attr": "stderr", "chunk_len": 20, "f_agg": "mean"},
                    {"attr": "intercept", "chunk_len": 10, "f_agg": "var"},
                    {"attr": "slope", "chunk_len": 15, "f_agg": "max"}
                ],
                "mean": None,
                "variance": None,
                "absolute_sum_of_changes": None,
                # "absolute_energy": None,
                "mean_abs_change": None,
                "number_peaks": [{"n": 5}],  # 5个峰值
                "sample_entropy": None,
                "energy_ratio_by_chunks": [{"num_segments": 5, "segment_focus": 2}]
            }
        return custom_fc_parameters

    def process_loc(self, args,n_jobs=1):
        """
        :param n_jobs: 这里并行为1最快。。
        :param args: (file_name, time, id, signal_channels, loc)
        :return: ts_fresh features
        """
        file_name, time, id, signal_channels, loc = args
        # signal_channels_avg = np.mean(signal_channels, axis=0)
        df = pd.DataFrame()
        df['time'] = time
        df['id'] = id
        df['id'] = df['id'].astype(str)
        df['value'] = signal_channels

        custom_fc_parameters = self.get_custom_fc_parameters(version="v2")
        features = ts_extract_features(df, column_id="id", column_sort="time",
                                       default_fc_parameters=custom_fc_parameters, disable_progressbar=True,n_jobs=n_jobs)
        features['filename'] = file_name
        featurename = features.columns.tolist()
        featurename = [x + '_' + loc for x in featurename]
        features.columns = featurename
        return features

    # @functools.cache
    def get_ts_features(self, basic_args_dict=None, label=None) -> pd.DataFrame:

        basic_args_dict = basic_args_dict or self.basic_args_dict
        # todo 这里注意  用t-onset代替了p   属于遗留问题 要谨慎解决。
        result_peak = basic_args_dict.get('result_peak', {})
        if 't-onset' in result_peak and 't-end' in result_peak:
            # p, t_end = result_peak['t-onset'], result_peak['t-end']
            p, t_end = result_peak['p-head'], result_peak['t-end']
        else:
            p, t_end = 0, -1

        file_name = os.path.basename(basic_args_dict['file_path']).replace('.txt', '')
        interpolated_data = basic_args_dict['interpolated_data'].copy()[p:t_end]

        n = len(interpolated_data)
        times = list(range(n))
        ids = [1] * n
        means = [self.decompose_and_calculate(np.array(interpolated_data[i])) for i in range(n)]
        group_indices_tmp = [[m[0], m[1], m[2], m[3], m[4]] for m in means]
        group_indices = list(map(list, zip(*group_indices_tmp)))
        locs = ['1', '2', '3', '4', 'center']

        # Parallel processing for locs
        args_list = [(file_name, times, ids, indices, loc) for indices, loc in zip(group_indices, locs)]

        # 使用 Pool 进行并行处理
        #
        # with ThreadPoolExecutor() as executor:
        #     dfs = list(executor.map(self.process_loc, args_list))

        dfs = []
        for indices, loc in zip(group_indices, locs):
            args = (file_name, times, ids, indices, loc)
            df = self.process_loc(args)
            dfs.append(df)

        df_ts = pd.concat(dfs, axis=1)
        df_ts['MCG_ID'] = df_ts['filename_center']
        df_ts['label'] = label
        df_ts = df_ts.drop(columns=['filename_center', 'filename_1', 'filename_2', 'filename_3', 'filename_4'], axis=1)

        return df_ts




class TimeSeqFeaturesNew:
    """基于tsfresh的时序特征提取类。"""

    def __init__(self, basic_args_dict, label):
        self.basic_args_dict = basic_args_dict
        self.label = label

    @staticmethod
    def decompose_and_calculate(matrix):
        # Ensure the matrix is 100x100
        if matrix.shape != (100, 100):
            return "The input matrix is not 100x100 in size."
        # Define quarter size
        quarter_size = 50

        # Split the matrix into 4 quarters
        Q1 = matrix[:quarter_size, :quarter_size]
        Q2 = matrix[:quarter_size, quarter_size:]
        Q3 = matrix[quarter_size:, :quarter_size]
        Q4 = matrix[quarter_size:, quarter_size:]

        # Extract 25x25 matrix from the center of each quarter
        sub_size = 25
        Q1_sub = Q1[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]
        Q2_sub = Q2[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]
        Q3_sub = Q3[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]
        Q4_sub = Q4[quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2,
                 quarter_size // 2 - sub_size // 2:quarter_size // 2 + sub_size // 2]

        # Extract 25x25 matrix from the center of the whole matrix
        center_sub = matrix[50 - sub_size // 2:50 + sub_size // 2, 50 - sub_size // 2:50 + sub_size // 2]

        # Calculate means
        means = [Q1_sub.mean(), Q2_sub.mean(), Q3_sub.mean(), Q4_sub.mean(), center_sub.mean()]

        return means

    @staticmethod
    def extract_regional_spatial_summaries(matrix: np.ndarray,
                                           block_size: tuple = (25, 25),
                                           stride: tuple = (10, 10)) -> dict:
        """
        Decomposes a matrix into blocks (like a convolution) and calculates
        a set of predefined statistics for each block.

        Args:
            matrix (np.ndarray): The input 2D matrix (e.g., 100x100).
            block_size (tuple): (height, width) of the block.
            stride (tuple): (row_stride, col_stride) for moving the block.

        Returns:
            dict: A dictionary where keys are like "BLOCK_R{r}_C{c}_STAT" and
                  values are the calculated statistics.
        """
        if matrix.ndim != 2:
            raise ValueError("Input matrix must be 2D.")

        matrix_h, matrix_w = matrix.shape
        block_h, block_w = block_size
        stride_h, stride_w = stride

        regional_features = {}

        row_idx = 0
        for r in range(0, matrix_h - block_h + 1, stride_h):
            col_idx = 0
            for c in range(0, matrix_w - block_w + 1, stride_w):
                block = matrix[r: r + block_h, c: c + block_w]

                if block.size == 0: continue

                # Calculate desired statistics for this block
                # Keep these concise and informative
                block_mean = np.mean(block)
                block_std = np.std(block)
                block_max = np.max(block)
                block_min = np.min(block)
                # from scipy.stats import skew, kurtosis # Ensure import
                # block_skew = skew(block.flatten())
                # block_kurt = kurtosis(block.flatten())

                base_name = f"BLOCK_R{row_idx}_C{col_idx}"
                regional_features[f"{base_name}_MEAN"] = block_mean
                regional_features[f"{base_name}_STD"] = block_std
                regional_features[f"{base_name}_MAX"] = block_max
                regional_features[f"{base_name}_MIN"] = block_min
                # regional_features[f"{base_name}_SKEW"] = block_skew
                # regional_features[f"{base_name}_KURT"] = block_kurt

                col_idx += 1
            row_idx += 1

        return regional_features

    @staticmethod
    def extract_concise_temporal_features(time_series: np.ndarray, sample_rate: float = 1.0) -> dict:
        """
        Extracts a concise set of temporal features from a 1D time series.

        Args:
            time_series (np.ndarray): The input 1D time series.
            sample_rate (float): Sampling rate of the time series, for FFT.

        Returns:
            dict: Dictionary of temporal features.
        """
        if not isinstance(time_series, np.ndarray) or time_series.ndim != 1 or time_series.size < 2:
            # Return NaNs for all expected features if series is too short or invalid
            # This ensures consistent output structure
            nan_features = {}
            expected_feature_bases = ["MEAN", "VARIANCE", "MEDIAN", "MAX", "MIN",
                                      "TREND_SLOPE", "AUTOCORR_LAG1", "SAMPLE_ENTROPY", "NUM_PEAKS_N5",
                                      "FFT_ABS_FREQ1HZ", "FFT_ABS_FREQ5HZ"]  # Example FFTs
            for base in expected_feature_bases:
                nan_features[f"TEMPORAL_{base}"] = np.nan
            return nan_features

        features = {}

        # --- Always Calculate ---
        features["TEMPORAL_MEAN"] = mean(time_series)  # Average activation/value of the block's stat over time

        if len(time_series) >= 2:
            x_coords = np.arange(len(time_series))
            try:  # np.polyfit can fail with NaNs/Infs
                slope, _ = np.polyfit(x_coords, time_series, 1)
                features["TEMPORAL_TREND_SLOPE"] = slope
            except np.linalg.LinAlgError:
                features["TEMPORAL_TREND_SLOPE"] = np.nan

            try:
                features["TEMPORAL_AUTOCORR_LAG1"] = autocorrelation(time_series, lag=1)
            except Exception:
                features["TEMPORAL_AUTOCORR_LAG1"] = np.nan
        else:
            features["TEMPORAL_TREND_SLOPE"] = np.nan
            features["TEMPORAL_AUTOCORR_LAG1"] = np.nan

        try:
            features["TEMPORAL_SAMPLE_ENTROPY"] = sample_entropy(time_series)
        except Exception:
            features["TEMPORAL_SAMPLE_ENTROPY"] = np.nan

        # --- Calculate these only if it's a key block OR if we decide they are always needed ---
        # This `is_key_block` is a new concept to control feature explosion.
        # Alternatively, calculate them always and then decide to aggregate later.
        # For now, let's assume we calculate fewer temporal features overall.

        # features["TEMPORAL_VARIANCE"] = variance(time_series) # Potentially redundant with StatFeatures
        # features["TEMPORAL_MEDIAN"] = median(time_series)   # Potentially redundant
        # features["TEMPORAL_MAX"] = maximum(time_series)     # Potentially redundant
        # features["TEMPORAL_MIN"] = minimum(time_series)     # Potentially redundant

        try:
            # Number of peaks can be informative about oscillatory behavior or events
            features["TEMPORAL_NUM_PEAKS_N3"] = number_peaks(time_series, n=3)  # Reduced n for conciseness
        except Exception:
            features["TEMPORAL_NUM_PEAKS_N3"] = np.nan

        # FFT: Maybe one dominant frequency component or spectral entropy
        if len(time_series) > 1 and sample_rate > 0:
            n = len(time_series)
            fft_values_abs = np.abs(np.fft.rfft(time_series)[1:])  # Exclude DC component
            fft_freqs = np.fft.rfftfreq(n, d=1. / sample_rate)[1:]

            if fft_values_abs.size > 0:
                try:
                    dominant_freq_idx = np.argmax(fft_values_abs)
                    features["TEMPORAL_FFT_DOMINANT_FREQ"] = fft_freqs[dominant_freq_idx]
                    features["TEMPORAL_FFT_DOMINANT_FREQ_MAG"] = fft_values_abs[dominant_freq_idx]
                except ValueError:  # handles empty fft_values_abs if it somehow occurs
                    features["TEMPORAL_FFT_DOMINANT_FREQ"] = np.nan
                    features["TEMPORAL_FFT_DOMINANT_FREQ_MAG"] = np.nan
            else:
                features["TEMPORAL_FFT_DOMINANT_FREQ"] = np.nan
                features["TEMPORAL_FFT_DOMINANT_FREQ_MAG"] = np.nan
        else:
            features["TEMPORAL_FFT_DOMINANT_FREQ"] = np.nan
            features["TEMPORAL_FFT_DOMINANT_FREQ_MAG"] = np.nan

        return features

    def get_custom_fc_parameters(self, version="v1"):
        custom_fc_parameters = {}
        sample_rate = 1000
        if version == "v1":
            # FFT Coefficients
            fft_coefficient_params = [
                {"attr": "real", "coeff": 33}, {"attr": "real", "coeff": 6},
                {"attr": "real", "coeff": 13}, {"attr": "real", "coeff": 50},
                {"attr": "abs", "coeff": 43}, {"attr": "angle", "coeff": 8},
                {"attr": "imag", "coeff": 25}
            ]
            # Aggregated Linear Trend
            agg_linear_trend_params = [
                {"attr": "stderr", "chunk_len": 10, "f_agg": "mean"},
                {"attr": "intercept", "chunk_len": 5, "f_agg": "var"}
            ]
            # Update parameters
            custom_fc_parameters["agg_linear_trend"] = agg_linear_trend_params
            custom_fc_parameters["fft_coefficient"] = fft_coefficient_params
        elif version == "v2":
            custom_fc_parameters = {
                "fft_coefficient": [
                    {"attr": "real", "coeff": 5, "sample_rate": sample_rate},
                    {"attr": "real", "coeff": 10, "sample_rate": sample_rate},
                    {"attr": "real", "coeff": 20, "sample_rate": sample_rate},
                    {"attr": "imag", "coeff": 5, "sample_rate": sample_rate},
                    {"attr": "imag", "coeff": 10, "sample_rate": sample_rate},
                    {"attr": "imag", "coeff": 20, "sample_rate": sample_rate},
                    {"attr": "abs", "coeff": 5, "sample_rate": sample_rate},
                    {"attr": "abs", "coeff": 10, "sample_rate": sample_rate},
                    {"attr": "abs", "coeff": 20, "sample_rate": sample_rate}
                ],
                "agg_linear_trend": [
                    {"attr": "stderr", "chunk_len": 20, "f_agg": "mean"},
                    {"attr": "intercept", "chunk_len": 10, "f_agg": "var"},
                    {"attr": "slope", "chunk_len": 15, "f_agg": "max"}
                ],
                "mean": None,
                "variance": None,
                "absolute_sum_of_changes": None,
                # "absolute_energy": None,
                "mean_abs_change": None,
                "number_peaks": [{"n": 5}],  # 5个峰值
                "sample_entropy": None,
                "energy_ratio_by_chunks": [{"num_segments": 5, "segment_focus": 2}]
            }
        return custom_fc_parameters

    def process_loc(self, args, n_jobs=1):
        """
        :param n_jobs: Parallelism for tsfresh feature extraction.
        :param args: (file_name, time_indices, id_column_values, signal_values_for_tsfresh, region_code)
        :return: ts_fresh features DataFrame with standardized names.
        """
        file_name, time_indices, id_column_values, signal_values_for_tsfresh, region_code_original = args

        df_for_tsfresh = pd.DataFrame()
        df_for_tsfresh['time'] = time_indices
        df_for_tsfresh['id'] = id_column_values  # Should all be the same 'id' for this single series
        df_for_tsfresh['id'] = df_for_tsfresh['id'].astype(str)  # tsfresh expects string IDs if not numeric
        df_for_tsfresh['value'] = signal_values_for_tsfresh  # This is the time series

        custom_fc_parameters = self.get_custom_fc_parameters(version="v2")  # Assuming v2 is default

        # Suppress the specific PerformanceWarning from tsfresh if it's noisy and understood
        # import warnings
        # from tsfresh.utilities.warnings import PerformanceWarning
        # warnings.filterwarnings("ignore", category=PerformanceWarning)

        extracted_features_df = ts_extract_features(
            df_for_tsfresh,
            column_id="id",
            column_sort="time",
            column_value="value",  # Explicitly specify value column
            default_fc_parameters=custom_fc_parameters,
            disable_progressbar=True,
            show_warnings=False,
            n_jobs=n_jobs
        )
        # extracted_features_df will have one row, columns are tsfresh feature names

        # Standardize feature names
        loc_to_region_map = {
            '1': "Q1_CENTER", '2': "Q2_CENTER", '3': "Q3_CENTER",
            '4': "Q4_CENTER", 'center': "MATRIX_CENTER"
        }
        region_prefix = loc_to_region_map.get(region_code_original, f"UNKNOWNREGION_{region_code_original}")

        renamed_columns = {}
        for col in extracted_features_df.columns:
            # col is like 'value__fft_coefficient__attr_"real"_coeff_33'
            # We want TSFRESH_Q1_CENTER_value__fft_coefficient__attr_"real"_coeff_33
            renamed_columns[col] = f"TSFRESH_{region_prefix}_{col}"

        extracted_features_df.rename(columns=renamed_columns, inplace=True)

        # Add filename as a simple column, not part of the feature name,
        # as it's an identifier for the whole row of features.
        # This will be merged later. For now, let process_loc return only features.
        # extracted_features_df['filename_marker_for_merge'] = file_name # Or handle merge key outside

        return extracted_features_df
    # @functools.cache

    def get_ts_features(self, basic_args_dict=None, label=None,
                                   block_size=(25, 25), stride=(25, 25),  # Non-overlapping 4x4 blocks
                                   assumed_matrix_timeseries_sample_rate=1.0,
                                   aggregate_block_features=True  # New parameter
                                   ) -> pd.DataFrame:
        basic_args_dict = basic_args_dict or self.basic_args_dict
        current_label = label or self.label

        # --- 1. Get segmented time series of matrices ---
        result_peak = basic_args_dict.get('result_peak', {})
        p_start_key = 'p-head'  # Or 't-onset' - RESOLVE THIS!
        t_end_key = 't-end'
        p_start_index, t_end_index = 0, -1  # Defaults

        if p_start_key in result_peak and t_end_key in result_peak:
            p_start_index = result_peak[p_start_key]
            t_end_index = result_peak[t_end_key]
        else:
            print(f"Warning: Missing peaks for segmentation. Using full data for custom ts features.")

        file_id = os.path.basename(basic_args_dict.get('file_path', 'unknown_file')).replace('.txt', '')

        interpolated_data_raw = basic_args_dict.get('interpolated_data')
        if interpolated_data_raw is None:
            raise ValueError("interpolated_data not found.")

        if t_end_index == -1 or t_end_index > len(interpolated_data_raw):
            segmented_matrices = interpolated_data_raw[p_start_index:]
        else:
            segmented_matrices = interpolated_data_raw[p_start_index: t_end_index]

        if len(segmented_matrices) < 2:  # Need at least 2 time points for most temporal features
            print(
                f"Warning: Segmented matrix series too short for {file_id} ({len(segmented_matrices)} points). Returning minimal ID/label.")
            return pd.DataFrame({'MCG_ID': [file_id], 'label': [current_label]})

        # --- 2. For each matrix in time, extract regional spatial summaries ---
        # This results in a list of dictionaries. Each dict is spatial features for one time point.
        all_spatial_summaries_over_time = []
        for matrix_at_t in segmented_matrices:
            matrix_np = np.array(matrix_at_t)  # Ensure numpy array
            if matrix_np.shape != (100, 100):  # Or make this configurable
                print(f"Warning: Matrix for {file_id} not 100x100. Skipping custom ts features.")
                return pd.DataFrame({'MCG_ID': [file_id], 'label': [current_label]})

            spatial_summary_at_t = self.extract_regional_spatial_summaries(matrix_np, block_size, stride)
            all_spatial_summaries_over_time.append(spatial_summary_at_t)

        if not all_spatial_summaries_over_time:
            print(f"Warning: No spatial summaries generated for {file_id}. Skipping.")
            return pd.DataFrame({'MCG_ID': [file_id], 'label': [current_label]})

        # --- 3. Transform into multiple time series ---
        # Each key in spatial_summary_at_t (e.g., "BLOCK_R0_C0_MEAN") becomes a time series.
        # Create a DataFrame from the list of dicts for easier manipulation
        df_spatial_summaries_time = pd.DataFrame(all_spatial_summaries_over_time)

        all_block_temporal_features_list = []  # List to store feature dicts for each block's time series

        # --- 4. For each of these time series, extract concise temporal features ---
        for spatial_feature_name_col in df_spatial_summaries_time.columns:
            single_time_series = df_spatial_summaries_time[spatial_feature_name_col].values

            temporal_features_for_series = self.extract_concise_temporal_features(
                single_time_series,
                sample_rate=assumed_matrix_timeseries_sample_rate
            )  # Now returns fewer features

            # Add identifiers to this dict for later aggregation
            temporal_features_for_series[
                'original_spatial_source'] = spatial_feature_name_col  # e.g., BLOCK_R0_C0_SPATIALMEAN
            all_block_temporal_features_list.append(temporal_features_for_series)

        if not all_block_temporal_features_list:
            print(f"Warning: No block temporal features generated for {file_id}.")
            return pd.DataFrame({'MCG_ID': [file_id], 'label': [current_label]})

        # --- 5. Combine and Name OR Aggregate ---
        if not aggregate_block_features:
            # Original approach: create one very wide feature vector
            final_features_dict_wide = {}
            for block_feature_dict in all_block_temporal_features_list:
                spatial_source = block_feature_dict.pop('original_spatial_source')
                parts = spatial_source.split('_')  # BLOCK_R0_C0_SPATIALMEAN -> ["BLOCK", "R0", "C0", "SPATIALMEAN"]
                region_id_str = f"{parts[0]}_{parts[1]}_{parts[2]}"
                spatial_stat_str = parts[
                    3]  # Already uppercase "SPATIALMEAN" if named so by extract_regional_spatial_summaries

                for temporal_key, temporal_val in block_feature_dict.items():
                    temporal_stat_str = temporal_key.replace("TEMPORAL_", "").upper()
                    feat_name = f"TSREGIONAL_{region_id_str}_{spatial_stat_str}_{temporal_stat_str}"
                    final_features_dict_wide[feat_name] = temporal_val
            df_final_features = pd.DataFrame([final_features_dict_wide])

        else:  # --- AGGREGATE ACROSS BLOCKS ---
            df_all_block_temporal_features = pd.DataFrame(all_block_temporal_features_list)
            # Columns: TEMPORAL_MEAN, TEMPORAL_SLOPE, ..., original_spatial_source

            final_aggregated_features_dict = {}

            # Group by the type of spatial statistic that generated the time series
            # e.g., group all "SPATIALMEAN" series together, all "SPATIALSTD" series together.
            # This requires `original_spatial_source` to be parsable for this.
            # Example: "BLOCK_R0_C0_SPATIALMEAN" -> spatial_stat_type = "SPATIALMEAN"
            df_all_block_temporal_features['spatial_stat_type'] = df_all_block_temporal_features[
                'original_spatial_source'].apply(
                lambda x: x.split('_')[-1]  # Assumes "SPATIALMEAN" is last part
            )

            # These are the temporal feature columns (e.g., TEMPORAL_MEAN, TEMPORAL_TREND_SLOPE)
            temporal_feature_cols_to_agg = [col for col in df_all_block_temporal_features.columns
                                            if col.startswith("TEMPORAL_")]

            for spatial_type in df_all_block_temporal_features['spatial_stat_type'].unique():
                df_subset_by_spatial_type = df_all_block_temporal_features[
                    df_all_block_temporal_features['spatial_stat_type'] == spatial_type
                    ]

                for temp_feat_col in temporal_feature_cols_to_agg:
                    values_to_agg = df_subset_by_spatial_type[temp_feat_col].dropna()
                    if values_to_agg.empty:
                        mean_val, std_val, max_val, min_val = np.nan, np.nan, np.nan, np.nan
                    else:
                        mean_val = np.mean(values_to_agg)
                        std_val = np.std(values_to_agg)
                        max_val = np.max(values_to_agg)
                        min_val = np.min(values_to_agg)

                    temporal_stat_suffix = temp_feat_col.replace("TEMPORAL_", "").upper()  # e.g., MEAN, TREND_SLOPE

                    # Naming: TSAGGR_{SPATIAL_STAT_TYPE}_{TEMPORAL_STAT_TYPE}_{AGGREGATE_STAT}
                    final_aggregated_features_dict[
                        f"TSAGGR_{spatial_type.upper()}_{temporal_stat_suffix}_MEANOFBLOCKS"] = mean_val
                    final_aggregated_features_dict[
                        f"TSAGGR_{spatial_type.upper()}_{temporal_stat_suffix}_STDOFBLOCKS"] = std_val
                    final_aggregated_features_dict[
                        f"TSAGGR_{spatial_type.upper()}_{temporal_stat_suffix}_MAXOFBLOCKS"] = max_val
                    final_aggregated_features_dict[
                        f"TSAGGR_{spatial_type.upper()}_{temporal_stat_suffix}_MINOFBLOCKS"] = min_val

            df_final_features = pd.DataFrame([final_aggregated_features_dict])

        df_final_features['MCG_ID'] = file_id
        df_final_features['label'] = current_label

        return df_final_features
    # Remove or deprecate get_custom_fc_parameters and process_loc if they are no longer used.
    # The original get_ts_features would be replaced by get_ts_features_v_cnn_like