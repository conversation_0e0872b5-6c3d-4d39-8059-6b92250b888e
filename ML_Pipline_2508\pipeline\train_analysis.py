"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_train_analysis
date: 2025/1/8 上午10:11
desc:
    对数据集分类性能的整体评估把握， 训练 保存模型
"""

# Standard libraries
import os
import time
import json
import warnings
import pickle
from datetime import datetime
from pathlib import Path
from typing import Union, Dict

# Data processing and analysis
import numpy as np
import pandas as pd
from scipy.stats import gaussian_kde
from tqdm.auto import tqdm

# Data preprocessing
from sklearn.preprocessing import StandardScaler

# Model selection and evaluation
from sklearn.model_selection import (
    StratifiedKFold,
    cross_val_score
)
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    roc_curve,
    confusion_matrix
)

# Machine learning models
from sklearn.svm import SVC
from sklearn.ensemble import (
    RandomForestClassifier
)
import xgboost as xgb
from catboost import CatBoostClassifier
import optuna

# Dimensionality reduction
from sklearn.decomposition import PCA

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import gridspec
import shap

# Custom modules
from model_trainer.data_augmentor import (
    StratifiedBalancedDataAugmentor
)
from model_trainer.model_factory import ModelFactory
from feature_selector.feature_selector import FeatureSelector
from model_trainer.train_manager import (
    ModelTrainer,
    refresh_clinic_features,
    CustomStratifiedKFold,
    condition_policy,
    ModelEvaluator,
    Condition_config,
    get_features
)
from temp_scripts.script_paradox_analysis import (
    attr_weighted_training_with_conflict
)

# Configuration
warnings.filterwarnings('ignore')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['SimHei'],
    'axes.unicode_minus': False
})

DELETE_SY = Condition_config.USE_EXTRA_FEATURE  # 是否删除上海十院数据
DELETE_NH = Condition_config.USE_EXTRA_FEATURE  # 是否删除广东南海CT备注为1的数据
RECUR_301 = Condition_config.USE_EXTRA_FEATURE  # 是否对因北京慢血流为1而原造影结论为0的数据，综合标签改回0
META_LABLE = Condition_config.USE_EXTRA_FEATURE  # 是否对'综合的标签'为1的数据，修改其为'造影结论'为1
USE_EXTRA_FEATURE = Condition_config.USE_EXTRA_FEATURE  # 是否使用额外特征
HOSPITAL_MAPPING = Condition_config.HOSPITAL_MAPPING
REVERSE_HOSPITAL_MAPPING = Condition_config.REVERSE_HOSPITAL_MAPPING


class TrainAnalysis:
    def __init__(self, excel_path: str, feature_dir: str, use_smote: bool = True,
                 use_mixup: bool = True,
                 selected_features_path="./files/saved_features/"):

        self.use_smote = use_smote
        self.use_mixup = use_mixup
        self.excel_path = excel_path
        self.feature_dir = Path(feature_dir)
        self.data = None
        self.features_df = None  # 存储完整特征DataFrame
        self.feature_names = None  # 特征名列表
        self.results = {}
        self.X_selected_scaled = None
        self.selected_features = None
        self.y = None
        self.clinic_imputer = None
        self.simple_imputer = None
        self.scaler = None
        self.selected_features_path = selected_features_path

    def append_curci_features(self, append_cur=True, append_ci=True, features_folder="./files/saved_features/"):
        """
        加载特征pkl文件，并追加到self.features_df。
        修改版：确保合并不会增加行数。
        """
        if append_cur:
            # 加载电流源特征
            cur_feature_path = os.path.join(features_folder, "cur_feature_df_all3923.pkl")
            try:
                cur_feature_df = pd.read_pickle(cur_feature_path)
            except FileNotFoundError:
                print(f"文件 {cur_feature_path} 未找到。")
                return
            except Exception as e:
                print(f"加载文件 {cur_feature_path} 时发生错误: {e}")
                return

            # --- 新增代码：在合并前对 mcg_file 列进行去重 ---
            # 记录原始行数
            original_rows = len(cur_feature_df)
            # 基于 'mcg_file' 列去重，保留第一个出现的记录
            cur_feature_df.drop_duplicates(subset=['mcg_file'], keep='first', inplace=True)
            # 记录去重后的行数
            deduped_rows = len(cur_feature_df)
            if original_rows > deduped_rows:
                print(
                    f"警告：'cur_feature_df' 中发现并移除了 {original_rows - deduped_rows} 个基于 'mcg_file' 的重复项。")
            # --- 新增代码结束 ---

            self.features_df = pd.merge(self.features_df, cur_feature_df, on="mcg_file", how="left")
            print(f"加载了{len(cur_feature_df.columns)}个电流源特征")

        if append_ci:
            # 加载参数特征
            ci_feature_path = os.path.join(features_folder, "ci_feature_df_all3923.pkl")
            try:
                ci_feature_df = pd.read_pickle(ci_feature_path)
            except FileNotFoundError:
                print(f"文件 {ci_feature_path} 未找到。")
                return
            except Exception as e:
                print(f"加载文件 {ci_feature_path} 时发生错误: {e}")
                return
            # --- 新增代码：在合并前对 mcg_file 列进行去重 ---
            original_rows = len(ci_feature_df)
            ci_feature_df.drop_duplicates(subset=['mcg_file'], keep='first', inplace=True)
            deduped_rows = len(ci_feature_df)
            if original_rows > deduped_rows:
                print(f"警告：'ci_feature_df' 中发现并移除了 {original_rows - deduped_rows} 个基于 'mcg_file' 的重复项。")
            # --- 新增代码结束 ---

            self.features_df = pd.merge(self.features_df, ci_feature_df, on="mcg_file", how="left")
            print(f"加载了{len(ci_feature_df.columns)}个参数特征")

        print("加载额外特征成功")
        return

    def load_data(self):
        """加载Excel数据和特征"""
        print("Loading data...")
        # 读取Excel
        df = pd.read_excel(self.excel_path, sheet_name='训练集')

        self.data = df[['心磁号', '造影结论', '标注一致性', '人工',
                        '临床特征-所在医院', '心磁质量', '是否极不一致']].copy()

        self.features_df = get_features(self.feature_dir, self.data['心磁号'].tolist())
        print(f"抽取本批次使用 {len(self.features_df)} 个心磁文件的特征")

        # ===================== 新增：加载电流源特征与参数特征 ============================
        if USE_EXTRA_FEATURE:
            self.append_curci_features()
        # ===================== 新增：从表中重新加载所有临床特征 ============================
        self.features_df = refresh_clinic_features(feature_df=self.features_df, mcg_ids=self.data['心磁号'],
                                                   excel_path="D:\std data\\20250227版本\测试新补充数据.xlsx",
                                                   sheet_name="测试用")
        # ========== 新增：计算BMI ==========
        if 'clinic_height' in self.features_df.columns and 'clinic_weight' in self.features_df.columns:
            # 将空值替换为-1
            height = self.features_df['clinic_height'].fillna(-1)
            weight = self.features_df['clinic_weight'].fillna(-1)

            # 计算BMI，对于任意输入参数为-1的情况,BMI也设为-1
            bmi = np.where(
                (height > 0) & (weight > 0),
                weight / ((height / 100) ** 2),  # 身高单位转换为米
                -1
            )
            self.features_df['clinic_bmi'] = bmi
            print("已添加clinic_bmi列")
        # ========== 新增：对 clinic_hospital 生成独热编码 ==========
        # 保留原始列（确保已存在）
        if 'clinic_hospital' in self.features_df.columns:
            # 生成独热编码（prefix为 clinic_hospital_onehot）
            onehot_cols = pd.get_dummies(
                self.features_df['clinic_hospital'],
                prefix='clinic_hospital_onehot'
            )
            # 合并到特征矩阵
            print("clinic_hospital独热编码列数:", len(onehot_cols.columns))
            print("clinic_hospital独热编码列名:", onehot_cols.columns)
            self.features_df = pd.concat([self.features_df, onehot_cols], axis=1)

        # ========================================================
        # 获取特征列名
        self.feature_names = [col for col in self.features_df.columns if col != 'mcg_file']
        print(f"Loaded {len(self.feature_names)} features for {len(self.data)} samples")

    def select_features_binary(self):
        """
        特征选择方法

        Returns:
            tuple: (已选择的特征DataFrame, 特征名称列表)
        """
        # 准备数据
        X = self.features_df[self.feature_names]
        y = self.data['造影结论']

        # 特征预处理
        X, X_imputed, clinic_imputer, simple_imputer, y = self.fill_features(X, y)
        X_imputed = pd.DataFrame(X_imputed, columns=X.columns, index=X.index)

        fs = FeatureSelector(self.excel_path, str(self.feature_dir))
        _, selected_features = fs.select_features(X=X_imputed, y=y, n_features=600)

        # 无论选择与否，临床特征都需要被保留
        remain_clinic_features = [col for col in self.features_df.columns
                                  if col.startswith('clinic') and col not in selected_features]
        selected_features = selected_features + remain_clinic_features
        X_selected = X_imputed[selected_features]

        # 标准化
        scaler = StandardScaler()
        X_selected_scaled = scaler.fit_transform(X_selected)

        # 保存数据到属性
        self.X_selected_scaled = X_selected_scaled
        self.selected_features = selected_features
        self.y = y
        self.clinic_imputer = clinic_imputer
        self.simple_imputer = simple_imputer
        self.scaler = scaler
        return X_selected_scaled, selected_features, y

    def fill_features(self, X, y):
        """
        特征选择方法，对clinic开头的特征使用XGBoost填补并限制在已有值中，其他特征使用简单填补
        """

        print(f"原始特征数量: {X.shape[1]}")
        # 删除全空列
        X = X.replace([np.inf, -np.inf], [1e6, -1e6])  # 处理无穷值
        X = X.loc[:, X.isnull().sum() < len(X)]
        print(f"删除全空列后的特征数量: {X.shape[1]}")
        # 区分clinic特征和其他特征
        clinic_features = [col for col in X.columns if col.startswith('clinic')]
        other_features = [col for col in X.columns if not col.startswith('clinic')]

        print(f"\n需要严格填补的clinic特征数量: {len(clinic_features)}")
        print(f"使用简单填补的其他特征数量: {len(other_features)}")

        # 打印缺失值信息
        missing_info = X.isnull().sum()
        print("\n特征缺失值统计:")
        for feature, missing_count in missing_info.items():
            if missing_count > 0:
                missing_percent = (missing_count / len(X)) * 100
                feature_type = "clinic特征" if feature.startswith('clinic') else "其他特征"
                print(f"{feature_type} '{feature}': {missing_count}个缺失值 ({missing_percent:.2f}%)")

        # 分别处理两类特征
        X_imputed = X.copy()
        clinic_imputer = None
        simple_imputer = None
        # 1. 处理clinic特征 - 使用XGBoost填补并限制取值
        if clinic_features:
            print("\n开始处理clinic特征...")
            # 用模型预测临床特征 ------------------------------------
            # # 存储每个clinic特征的已有值
            # clinic_values = {col: X[col].dropna().unique()
            #                  for col in clinic_features}
            # # 使用自定义填补器处理clinic特征
            # clinic_imputer = CustomXGBImputer(
            #     feature_values=clinic_values,
            #     estimator=xgb.XGBRegressor(
            #         objective='reg:squarederror',
            #         random_state=42,
            #         n_estimators=100,
            #         max_depth=6
            #     ),
            #     random_state=42,
            #     max_iter=10,
            #     verbose=0
            # )
            #
            # X_clinic = X[clinic_features]
            # clinic_imputer.fit(X_clinic)
            # X_clinic_imputed = pd.DataFrame(
            #     clinic_imputer.transform(X_clinic),
            #     columns=clinic_features,
            #     index=X.index
            # )
            # ----------------------------------------------
            # 直接用-1填充clinic特征的缺失值
            X_clinic_imputed = X[clinic_features].fillna(-1)

            # 更新填补结果
            X_imputed[clinic_features] = X_clinic_imputed

        # 2. 处理其他特征 - 使用简单填补(中值)
        if other_features:
            print("\n开始处理其他特征...")
            # simple_imputer = SimpleImputer(strategy='median')
            # X_imputed = simple_imputer.fit_transform(X_imputed)
            X_imputed = X_imputed.fillna(0)  # X_selected.median())

        print("\n所有特征填补完成")

        # 处理无穷值
        return X, X_imputed, clinic_imputer, simple_imputer, y

    def _get_version_path(self, version):
        """
        获取特定版本的存储路径
        Args:
            version: 版本标识
        Returns:
            str: 版本对应的完整路径
        """
        if version == "temp":
            version = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        return os.path.join(self.selected_features_path, version)

    def _save_features(self, version):
        """
        保存特征选择的结果
        Args:
            version: 版本标识
        """
        version_path = self._get_version_path(version)
        os.makedirs(version_path, exist_ok=True)

        feature_data = {
            'X_selected_scaled': self.X_selected_scaled,
            'selected_features': self.selected_features,
            'y': self.y,
            'clinic_imputer': self.clinic_imputer,
            'simple_imputer': self.simple_imputer,
            'scaler': self.scaler
        }

        with open(os.path.join(version_path, 'features.pkl'), 'wb') as f:
            pickle.dump(feature_data, f)

    def _load_features(self, version, label_text='造影结论'):
        """
        加载特定版本的特征选择结果，并添加clinic开头的特征

        Args:
            version: 特征版本标识，可以是单个版本号或版本号列表

        Returns:
            bool: 加载是否成功
        """
        # 处理版本为列表的情况
        if isinstance(version, list):
            # 存储所有加载的特征
            all_features = []
            success = False

            # 尝试加载每个版本的特征
            for ver in version:
                if self._load_single_version_features(ver):
                    success = True
                    all_features.extend(self.selected_features)

            if not success:
                return False

            # 去重
            self.selected_features = list(dict.fromkeys(all_features))

            # 从当前数据中获取特征
            X_selected = self.features_df[self.selected_features]

            # 数据预处理
            X_selected_clean = X_selected.fillna(
                {col: -1 if col.startswith('clinic') else 0 for col in X_selected.columns})

            scaler = StandardScaler()
            self.X_selected_scaled = scaler.fit_transform(X_selected_clean)

            # 获取目标变量
            self.y = self.data[label_text]

            return True
        else:
            # 单个版本的处理逻辑
            return self._load_single_version_features(version, label_text)

    def _load_single_version_features(self, version, label_text='造影结论'):
        """
        加载单个版本的特征选择结果

        Args:
            version: 单个特征版本标识

        Returns:
            bool: 加载是否成功
        """
        version_path = self._get_version_path(version)
        feature_path = os.path.join(version_path, 'features.pkl')

        if not os.path.exists(feature_path):
            return False

        try:
            with open(feature_path, 'rb') as f:
                feature_data = pickle.load(f)

            # 获取保存的特征列名
            saved_features = feature_data['selected_features']

            if 'clinic_hospital' in self.features_df.columns:

                # 生成独热编码（确保前缀与训练时一致）
                onehot_cols = pd.get_dummies(
                    self.features_df['clinic_hospital'],
                    prefix='clinic_hospital_onehot'
                )

                # 强制对齐训练时的独热编码列（处理未知类别）
                # 使用合并后的特征列表，确保所有需要的独热编码列都被生成
                expected_onehot_columns = [
                    col for col in saved_features
                    if col.startswith('clinic_hospital_onehot_')
                ]

                # 仅保留self.features_df中不存在的列
                new_cols = [col for col in expected_onehot_columns if col not in self.features_df.columns]

                if new_cols:
                    # 对齐新列名,填充0
                    onehot_cols = onehot_cols.reindex(columns=new_cols, fill_value=0)
                    # 合并新列
                    self.features_df = pd.concat([self.features_df, onehot_cols], axis=1)

            # 获取所有以clinic开头的特征(并且未被选择的)
            clinic_features = [col for col in self.features_df.columns
                               if col.startswith('clinic') and col not in saved_features]

            # self.features_df中 以cur_/ci_为开头的都要被额外纳入（以不重复纳入的形式） =================================
            cur_ci_features = [col for col in self.features_df.columns if
                               col.startswith("cur_") or col.startswith("ci_")]
            # 合并特征列表
            # self.selected_features = list(set(saved_features + clinic_features))  # + cur_ci_features))
            self.selected_features = saved_features
            # 从当前数据中获取特征
            X_selected = self.features_df[self.selected_features]

            # ===============================================================================================
            # 数据预处理
            X_selected_clean = X_selected.fillna(
                {col: -1 if col.startswith('clinic') else 0 for col in X_selected.columns})

            scaler = StandardScaler()
            self.X_selected_scaled = scaler.fit_transform(X_selected_clean)

            # 获取目标变量
            self.y = self.data[label_text]
            # print("使用标注一致性做标签")
            # self.y = self.data['标注一致性']

            return True
        except Exception as e:
            print(f"加载特征数据失败: {str(e)}")
            return False

    def prepare_for_training(self, evaluation_results):
        """
        检查以及加载训练所需的x/y 确保其完成前置的scaler/补缺等操作
        :return:
        """

        if self.X_selected_scaled is None or self.selected_features is None or self.y is None:
            print("重新生成特征")
            self.X_selected_scaled, self.selected_features, self.y = self.select_features_binary()

            # 获取用于特征缩放的scaler
        X_selected = self.features_df[evaluation_results['selected_features']]
        # 找出clinic开头的特征名
        X_selected_clean = X_selected.fillna({col: -1 if col.startswith('clinic') else 0 for col in X_selected.columns})

        scaler = StandardScaler()
        scaler.fit(X_selected_clean)  # 新适配的scaler 返回从 X_selected_clean中fit的scaler 和 X_selected_scaled 二选一

        X_selected_scaled = self.X_selected_scaled  # 原已保存或加载的 scaled数据
        y = self.y

        return X_selected_clean, scaler, y

    def train_and_save_model(self, evaluation_results, version=None, description=None):
        """
        在全量数据上训练最佳模型并保存
        Args:
            evaluation_results: evaluate_models_binary的返回结果
            version: 可选的版本标识
            description: 可选的版本描述
        Returns:
            tuple: (训练好的最佳模型, 版本号)
        """
        # self = analysis
        X_selected_clean, scaler, y = self.prepare_for_training(evaluation_results)

        # 初始化模型训练器
        trainer = ModelTrainer()

        # 训练并保存模型
        best_model, version = trainer.train_best_model(
            evaluation_results,
            X_selected_clean,
            y,
            scaler,
            evaluation_results['selected_features'],
            version=version,
            description=description,
            # optimize_params=True
        )

        return best_model, version

    def train_and_save_optuna_weighted_model(self, evaluation_results, random_seed=42, version=None,
                                             description=None,
                                             save_best_fold=True, n_trials=500,
                                             study_name="weighted_training_study_0416",
                                             timeout=None, optimize_xgb_params=True):
        """
        使用Optuna优化加权参数，训练集成模型并保存 - 改进版

        Args:
            evaluation_results: evaluate_models_binary的返回结果
            random_seed: 随机种子
            version: 可选的版本标识
            description: 可选的版本描述
            save_best_fold: 是否保存最佳交叉验证折的模型，如果为False则在全量数据上训练
            n_trials: Optuna优化尝试次数
            study_name: Optuna研究名称
            timeout: Optuna优化超时时间（秒）
            optimize_xgb_params: 是否同时优化XGBoost的关键参数（默认为False）

        Returns:
            tuple: (训练好的最佳模型, 版本号)
        """

        # 准备数据
        X_selected_clean, scaler, y = self.prepare_for_training(evaluation_results)
        X_selected_scaled = scaler.transform(X_selected_clean)
        features_df = self.features_df.copy()
        data = self.data.copy()
        best_model = evaluation_results['best_model']

        # 提取XGBoost参数（如果可用）
        if hasattr(best_model, 'get_params'):
            # 复制参数并移除不需要的参数
            params = best_model.get_params().copy()
            # 移除不需要的参数
            for param in ['base_score', 'booster', 'callbacks', 'importance_type',
                          'missing', 'n_jobs', 'verbose', 'early_stopping_rounds']:
                if param in params:
                    params.pop(param)
        else:
            # 默认XGBoost参数
            params = {
                'n_estimators': 600,
                'learning_rate': 0.01,
                'max_depth': 6,
                'min_child_weight': 1,
                'subsample': 0.9,
                'colsample_bytree': 0.9,
                'gamma': 0.05,
            }

        # 准备交叉时的分层信息
        hospitals = features_df['clinic_hospital']
        clinic_diabetes = features_df['clinic_diabetes']
        clinic_symp = features_df['clinic_symp']
        clinic_age = features_df['clinic_age']
        clinic_gender = features_df['clinic_gender']
        clinic_hypertension = features_df['clinic_hypertension']
        clinic_hyperlipidemia = features_df['clinic_hyperlipidemia']
        clinic_intervention = features_df['clinic_intervention']
        consistensy = data['标注一致性']

        def gen_all_cv_folds(trail):
            # 生成3个不同的随机种子
            # random_seeds = [random_seed + trail.number, random_seed + trail.number + 100, random_seed + trail.number + 200]
            random_seeds = [random_seed, random_seed + 100, random_seed + 200]
            print(f"使用3个随机种子进行评估: {random_seeds}")
            # 预先为每个种子生成交叉验证折，以确保所有trial使用相同的数据划分
            all_cv_folds = []
            for seed in random_seeds:
                custom_cv = CustomStratifiedKFold(n_splits=5, shuffle=True, random_state=seed)
                cv_folds = list(
                    custom_cv.split(X_selected_scaled, y, consistensy, hospitals, clinic_hypertension, clinic_gender,
                                    clinic_hyperlipidemia, clinic_intervention, clinic_diabetes, clinic_symp))
                all_cv_folds.extend(
                    [(seed, fold_idx, train_idx, test_idx) for fold_idx, (train_idx, test_idx) in enumerate(cv_folds)])

            print(f"总共生成 {len(all_cv_folds)} 个交叉验证折 (3个种子 x 5折)")
            y_array = np.array(y)
            return all_cv_folds, y_array

        # 定义Optuna优化目标函数
        def objective(trial):
            # 如果启用XGBoost参数优化，则优化XGBoost的关键参数
            if optimize_xgb_params:
                # 优化最具影响力的XGBoost参数（选择5个）
                xgb_params = {
                    'n_estimators': trial.suggest_int('n_estimators', 100, 1000, step=100),
                    'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.3, log=True),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0)
                }
                # 合并原始参数和新优化的参数
                current_params = params.copy()
                current_params.update(xgb_params)
            else:
                # 使用原始参数
                current_params = params

            # 构建属性加权参数
            attribute_weights = {
                'combinations': []  # 确保combinations键存在
            }

            # 医院列表
            # hospitals_list = ['上海六院', '上海十院', '北京301', '广东南海']
            hospitals_list = ['上海六院', '上海中山', '北京301', '广东南海', '安医']

            # 为每个医院-标签状态-一致性状态的组合优化权重参数（共16个参数）
            for hospital in hospitals_list:
                # 短名称用于参数命名
                short_name = hospital.replace('上海', 'sh').replace('北京', 'bj').replace('广东', 'gd')

                # 阳性且一致
                weight_pos_consistent = trial.suggest_float(f'{short_name}_pos_consistent', 0.1, 5.0)
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 1),  # 阳性
                        ('标注一致性', 1)  # 一致
                    ],
                    'weight': weight_pos_consistent
                })

                # 阳性但不一致
                weight_pos_inconsistent = trial.suggest_float(f'{short_name}_pos_inconsistent', 0.1, 5.0)
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 1),  # 阳性
                        ('标注一致性', 0)  # 不一致
                    ],
                    'weight': weight_pos_inconsistent
                })

                # 阴性且一致
                weight_neg_consistent = trial.suggest_float(f'{short_name}_neg_consistent', 0.1, 5.0)
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 0),  # 阴性
                        ('标注一致性', 1)  # 一致
                    ],
                    'weight': weight_neg_consistent
                })

                # 阴性但不一致
                weight_neg_inconsistent = trial.suggest_float(f'{short_name}_neg_inconsistent', 0.1, 5.0)
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 0),  # 阴性
                        ('标注一致性', 0)  # 不一致
                    ],
                    'weight': weight_neg_inconsistent
                })

            # 心磁质量权重 - 额外的2个参数
            quality_good_weight = trial.suggest_float('quality_good_weight', 0.5, 2.0)
            quality_poor_weight = trial.suggest_float('quality_poor_weight', 0.5, 2.0)
            # attribute_weights['心磁质量'] = {'可用': quality_good_weight, '稍差': quality_poor_weight}
            attribute_weights['心磁质量'] = {'可用': quality_good_weight, '优': quality_good_weight,
                                             '稍差': quality_poor_weight, '偏差': quality_poor_weight,
                                             '良': quality_poor_weight}

            hypertension_yes_weight = trial.suggest_float('hypertension_yes_weight', 0.5, 2.0)
            hypertension_no_weight = trial.suggest_float('hypertension_no_weight', 0.5, 2.0)
            attribute_weights['临床特征-高血压'] = {1: hypertension_yes_weight, 0: hypertension_no_weight}

            # 添加矛盾度加权的alpha参数优化
            conflict_alpha = trial.suggest_float('conflict_alpha', 0.01, 8.0)

            # # 极不一致样本权重
            # extremely_inconsistent_weight = trial.suggest_float('extremely_inconsistent_weight', 0.01, 3.0)
            # attribute_weights['combinations'].append({
            #     'conditions': [
            #         ('是否极不一致', 1)  # 极不一致样本
            #     ],
            #     'weight': extremely_inconsistent_weight
            # })

            # 注释掉投票权重参数
            # voting_weight = trial.suggest_float('voting_weight', 1.0, 3.0)

            # 保存每个折的评估结果
            fold_accuracies = []
            all_cv_folds, y_array = gen_all_cv_folds(trial)
            # 执行交叉验证 - 对所有种子和折进行评估
            for seed, fold_idx, train_idx, test_idx in all_cv_folds:
                # 分割数据
                X_train_fold, X_test_fold = X_selected_scaled[train_idx], X_selected_scaled[test_idx]
                y_train_fold, y_test_fold = y_array[train_idx], y_array[test_idx]

                # 准备属性数据
                attribute_data_train = data.iloc[train_idx]

                # 使用当前参数训练模型，包含矛盾度加权，注释掉投票权重
                fold_results = attr_weighted_training_with_conflict(
                    X_train_fold, y_train_fold,
                    X_test_fold, y_test_fold,
                    attribute_data=attribute_data_train,
                    attribute_weights=attribute_weights,
                    params=current_params,  # 使用可能被优化的参数
                    conflict_alpha=conflict_alpha
                    # voting_weight=voting_weight  # 注释掉投票权重
                )

                # 记录加权模型的准确率
                fold_accuracies.append(fold_results['weighted_accuracy'])  # 以最佳提升为优化目标

            # 返回所有折的平均准确率作为优化目标
            mean_accuracy = sum(fold_accuracies) / len(fold_accuracies)
            return mean_accuracy

        # 创建Optuna研究
        print("\n=== 开始使用Optuna进行加权参数优化 ===")
        study = optuna.create_study(
            study_name=study_name,
            direction="maximize",
            sampler=optuna.samplers.TPESampler(
                n_startup_trials=50,  # 初始随机探索次数（总trial数的10-20%）
                multivariate=True,  # 启用参数关联优化（适合医院/标签组合参数的协同效应）
                group=True,  # 对分类变量（如医院名称）分组处理
                seed=42
            ),
            pruner=optuna.pruners.MedianPruner(
                n_startup_trials=10,  # 前10次试验不剪枝
                n_warmup_steps=20,  # 观察20步后再评估
            ),
            storage=optuna.storages.RDBStorage(
                url="sqlite:///hospital_weights_xgb.db"  # SQLite持久化存储
            ),
            load_if_exists=True
        )

        # 运行优化
        study.optimize(objective, n_trials=n_trials, timeout=timeout)

        # 打印最佳参数
        print("\n=== Optuna优化结果 ===")
        print(f"最佳准确率: {study.best_value:.4f}")
        print("最佳参数:")

        # 分类显示最佳参数
        print("-- 属性加权参数:")
        for param_name, param_value in study.best_params.items():
            if param_name not in ['n_estimators', 'learning_rate', 'max_depth', 'subsample', 'colsample_bytree']:
                print(f"  {param_name}: {param_value}")

        # 如果优化了XGBoost参数，单独显示
        if optimize_xgb_params:
            print("-- XGBoost参数:")
            for param_name in ['n_estimators', 'learning_rate', 'max_depth', 'subsample', 'colsample_bytree']:
                if param_name in study.best_params:
                    print(f"  {param_name}: {study.best_params[param_name]}")

        # 构建最佳属性加权参数
        best_attribute_weights = {
            'combinations': [],
            '心磁质量': {
                '可用': study.best_params['quality_good_weight'],
                '稍差': study.best_params['quality_poor_weight']
            },
            '临床特征-高血压': {
                1: study.best_params['hypertension_yes_weight'],
                0: study.best_params['hypertension_no_weight']
            }
        }

        hospitals_list = ['上海六院', '上海中山', '北京301', '广东南海', '安医']
        # 为每个医院-标签状态-一致性状态的组合设置最佳权重
        for hospital in hospitals_list:
            # 短名称用于参数命名
            short_name = hospital.replace('上海', 'sh').replace('北京', 'bj').replace('广东', 'gd')

            # 阳性且一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 1),  # 阳性
                    ('标注一致性', 1)  # 一致
                ],
                'weight': study.best_params[f'{short_name}_pos_consistent']
            })

            # 阳性但不一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 1),  # 阳性
                    ('标注一致性', 0)  # 不一致
                ],
                'weight': study.best_params[f'{short_name}_pos_inconsistent']
            })

            # 阴性且一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 0),  # 阴性
                    ('标注一致性', 1)  # 一致
                ],
                'weight': study.best_params[f'{short_name}_neg_consistent']
            })

            # 阴性但不一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 0),  # 阴性
                    ('标注一致性', 0)  # 不一致
                ],
                'weight': study.best_params[f'{short_name}_neg_inconsistent']
            })
        # 获取最佳矛盾度参数和投票权重
        best_conflict_alpha = study.best_params['conflict_alpha']
        # 获取优化后的XGBoost参数（如果启用了参数优化）
        if optimize_xgb_params:
            best_xgb_params = {}
            for param_name in ['n_estimators', 'learning_rate', 'max_depth', 'subsample', 'colsample_bytree']:
                if param_name in study.best_params:
                    best_xgb_params[param_name] = study.best_params[param_name]

            # 更新XGBoost参数
            params.update(best_xgb_params)
            print("\n优化后的XGBoost参数:")
            for param_name, param_value in best_xgb_params.items():
                print(f"  {param_name}: {param_value}")

        # 使用最佳参数进行最终模型训练
        print("\n=== 使用最佳参数进行多种子五折交叉验证 ===")
        print(f"矛盾度alpha: {best_conflict_alpha}")

        # 初始化保存结果的字典
        cv_results = {
            'fold_models': [],
            'fold_metrics': [],
            'best_fold': None,
            'best_seed': None,
            'best_score': 0
        }

        best_weighted_model = None
        best_weighted_score = 0

        all_cv_folds, y_array = gen_all_cv_folds(study.best_trial)
        # 对每个种子和折进行最终评估
        for seed, fold_idx, train_idx, test_idx in all_cv_folds:
            print(f"\n--- 种子 {seed}, 折 {fold_idx + 1}/5 ---")

            # 分割数据
            X_train_fold, X_test_fold = X_selected_scaled[train_idx], X_selected_scaled[test_idx]
            y_train_fold, y_test_fold = y_array[train_idx], y_array[test_idx]

            # 准备属性数据
            attribute_data_train = data.iloc[train_idx]

            # 使用最佳参数训练模型，注释掉投票权重
            fold_results = attr_weighted_training_with_conflict(
                X_train_fold, y_train_fold,
                X_test_fold, y_test_fold,
                attribute_data=attribute_data_train,
                attribute_weights=best_attribute_weights,
                params=params,
                conflict_alpha=best_conflict_alpha
                # voting_weight=best_voting_weight  # 注释掉投票权重
            )

            # 保存折结果
            cv_results['fold_models'].append(fold_results['weighted_model'])
            cv_results['fold_metrics'].append({
                'seed': seed,
                'fold': fold_idx + 1,
                'base_accuracy': fold_results['base_accuracy'],
                'weighted_accuracy': fold_results['weighted_accuracy'],
                # 注释掉XGB权重指标
                # 'weighted_accuracy_xgb': fold_results['weighted_accuracy_xgb'],
                'improvement': fold_results['improvement'],
                'correction_stats': fold_results['correction_stats']
            })

            # 更新最佳折
            if fold_results['weighted_accuracy'] > best_weighted_score:
                best_weighted_score = fold_results['weighted_accuracy']
                best_weighted_model = fold_results['weighted_model']
                cv_results['best_fold'] = fold_idx + 1
                cv_results['best_seed'] = seed
                cv_results['best_score'] = best_weighted_score

        # 打印交叉验证结果摘要
        print("\n=== 多种子五折交叉验证结果 ===")
        for metric in cv_results['fold_metrics']:
            print(f"种子 {metric['seed']}, 折 {metric['fold']}: 基准准确率={metric['base_accuracy']:.4f}, "
                  # 注释掉XGB准确率输出
                  # f"加权XGB准确率={metric['weighted_accuracy_xgb']:.4f}, "
                  f"加权准确率={metric['weighted_accuracy']:.4f}, "
                  f"改进={metric['improvement']:.4f}")

        print(
            f"\n最佳结果: 种子 {cv_results['best_seed']}, 折 {cv_results['best_fold']}, 准确率: {cv_results['best_score']:.4f}")

        # 最终模型：选择最佳折模型或在全量数据上训练，并比较加权前后的模型
        if save_best_fold:
            print("\n=== 比较最佳折的加权前后模型性能 ===")
            # 找到最佳折的基础模型和加权模型
            best_fold_index = next(i for i, m in enumerate(cv_results['fold_metrics'])
                                   if m['seed'] == cv_results['best_seed'] and m['fold'] == cv_results['best_fold'])
            best_fold_base_accuracy = cv_results['fold_metrics'][best_fold_index]['base_accuracy']
            best_fold_weighted_accuracy = cv_results['fold_metrics'][best_fold_index]['weighted_accuracy']

            # 比较基础模型与加权模型性能
            if best_fold_base_accuracy > best_fold_weighted_accuracy:
                print(f"基础模型性能更好: {best_fold_base_accuracy:.4f} vs 加权模型: {best_fold_weighted_accuracy:.4f}")
                # 需要从 fold_results 中获取基础模型，在 attr_weighted_training_with_conflict 函数中应该返回了基础模型
                # 假设有个 base_models 列表存储了每个折的基础模型
                final_model = cv_results.get('base_models', [])[best_fold_index]
                final_metrics = {
                    'accuracy': best_fold_base_accuracy,
                    'model_source': f'best_base_model_seed_{cv_results["best_seed"]}_fold_{cv_results["best_fold"]}',
                    'is_weighted': False
                }
            else:
                print(f"加权模型性能更好: {best_fold_weighted_accuracy:.4f} vs 基础模型: {best_fold_base_accuracy:.4f}")
                final_model = best_weighted_model
                final_metrics = {
                    'accuracy': best_weighted_score,
                    'model_source': f'best_weighted_model_seed_{cv_results["best_seed"]}_fold_{cv_results["best_fold"]}',
                    'is_weighted': True
                }
        else:
            print("\n=== 在全量数据上训练最终模型并比较加权前后性能 ===")
            # 在全量数据上训练模型
            attribute_data_full = data

            final_results = attr_weighted_training_with_conflict(
                X_selected_scaled, y,
                X_selected_scaled, y,  # 使用相同数据作为测试集（仅为了保持API一致）
                attribute_data=attribute_data_full,
                attribute_weights=best_attribute_weights,
                params=params,
                conflict_alpha=best_conflict_alpha
            )

            # 比较全量数据上的基础模型与加权模型性能
            if final_results['base_accuracy'] > final_results['weighted_accuracy']:
                print(
                    f"基础模型性能更好: {final_results['base_accuracy']:.4f} vs 加权模型: {final_results['weighted_accuracy']:.4f}")
                final_model = final_results['base_model']  # 假设 attr_weighted_training_with_conflict 函数返回了基础模型
                final_metrics = {
                    'accuracy': final_results['base_accuracy'],
                    'model_source': 'full_data_base_model',
                    'is_weighted': False
                }
            else:
                print(
                    f"加权模型性能更好: {final_results['weighted_accuracy']:.4f} vs 基础模型: {final_results['base_accuracy']:.4f}")
                final_model = final_results['weighted_model']
                final_metrics = {
                    'accuracy': final_results['weighted_accuracy'],
                    'model_source': 'full_data_weighted_model',
                    'is_weighted': True
                }

        trainer = ModelTrainer()

        # 保存模型到文件系统
        if not hasattr(trainer, 'version_info'):
            trainer.version_info = {'versions': {}, 'latest_version': None}

        # 如果未指定版本，则自动生成版本号
        if version is None:
            date_str = datetime.now().strftime('%Y%m%d')
            existing_versions = [v for v in trainer.version_info['versions'].keys()
                                 if v.startswith(date_str)]
            version = f"{date_str}_{len(existing_versions) + 1}"

        version_dir = trainer._get_version_dir(version)
        if not os.path.exists(version_dir):
            os.makedirs(version_dir)

        print(f"\n=== 保存模型 版本:{version} ===")

        # 保存模型和相关文件
        files_to_save = {
            'model.pkl': final_model,
        }

        if scaler is not None:
            files_to_save['scaler.pkl'] = scaler

        if evaluation_results['selected_features'] is not None:
            files_to_save['features.pkl'] = evaluation_results['selected_features']

        for filename, obj in files_to_save.items():
            filepath = os.path.join(version_dir, filename)
            with open(filepath, 'wb') as f:
                pickle.dump(obj, f)

        # 在训练集上评估模型性能
        print("\n=== 训练集性能评估 ===")
        if hasattr(final_model, 'predict_proba'):
            train_probs = final_model.predict_proba(X_selected_scaled)
            train_preds = (train_probs[:, 1] > 0.5).astype(int)
        else:
            train_preds = final_model.predict(X_selected_scaled)
            train_probs = None

        # 计算指标
        train_metrics = {
            'accuracy': accuracy_score(y, train_preds),
            'precision': precision_score(y, train_preds),
            'recall': recall_score(y, train_preds),
            'specificity': recall_score(y, train_preds, pos_label=0),
            'f1': f1_score(y, train_preds),
        }

        # 如果有概率预测，添加AUC
        if train_probs is not None and train_probs.shape[1] > 1:
            train_metrics['auc'] = roc_auc_score(y, train_probs[:, 1])

        # 打印评估指标
        print("\n训练集评估指标:")
        for metric, value in train_metrics.items():
            print(f"{metric.capitalize()}: {value:.4f}")

        # 打印混淆矩阵
        conf_matrix = confusion_matrix(y, train_preds)
        print("\n混淆矩阵:")
        print("          预测        Negative  Positive")
        print(f"实际 Negative  {conf_matrix[0, 0]:8d} {conf_matrix[0, 1]:8d}")
        print(f"实际 Positive  {conf_matrix[1, 0]:8d} {conf_matrix[1, 1]:8d}")

        # 计算错误率
        error_mask = train_preds != y
        error_count = np.sum(error_mask)
        error_rate = error_count / len(y)
        print(f"\n错误预测样本数: {error_count}")
        print(f"错误率: {error_rate:.4f}")

        # 创建元数据
        metadata = {
            'version': version,
            'model_type': 'OptunaEnsembleModelV2',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'description': description,
            'train_metrics': train_metrics,
            'feature_count': len(evaluation_results['selected_features']),
            'sample_count': len(y),
            'class_distribution': pd.Series(y).value_counts().to_dict(),
            'error_rate': float(error_rate),
            'cv_results': {
                'random_seeds': [random_seed + study.best_trial.number, random_seed + study.best_trial.number + 100,
                                 random_seed + study.best_trial.number + 200],
                'best_seed': cv_results['best_seed'],
                'best_fold': cv_results['best_fold'],
                'best_score': cv_results['best_score'],
                'fold_metrics': cv_results['fold_metrics']
            },
            'optuna_results': {
                'best_params': study.best_params,
                'best_value': study.best_value,
                'n_trials': n_trials,
                'conflict_alpha': best_conflict_alpha,
                # 注释掉投票权重信息
                # 'voting_weight': best_voting_weight
            },
            'xgb_params_optimized': optimize_xgb_params
        }

        # 更新版本信息
        trainer.version_info['versions'][version] = metadata
        trainer.version_info['latest_version'] = version
        trainer._save_version_info()

        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj

        # 转换数据
        converted_info = convert_numpy_types(metadata)
        # 保存版本元数据
        metadata_path = os.path.join(version_dir, 'metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(converted_info, f, ensure_ascii=False, indent=2)

        print(f"\n模型和配置已保存到版本目录: {version_dir}")
        print(f"版本信息:")
        print(f"- 版本: {version}")
        print(f"- 模型: OptunaEnsembleModelV2 (含矛盾度加权)")
        print(f"- 矛盾度alpha: {best_conflict_alpha}")
        # 注释掉投票权重输出
        # print(f"- 投票权重: {best_voting_weight}")
        if optimize_xgb_params:
            print(f"- XGBoost参数已优化")
        print(f"- Optuna优化: {n_trials}次尝试")
        print(f"- 交叉验证: 3种子×5折，最佳种子={cv_results['best_seed']}，最佳折={cv_results['best_fold']}")
        print(f"- 创建时间: {metadata['created_at']}")
        if description:
            print(f"- 描述: {description}")

        return final_model, version

    def train_and_save_meta_weighted_model(self, evaluation_results, random_seed=42, version=None,
                                           description=None,
                                           save_best_fold=True, n_iterations=200,
                                           meta_learning_rate=0.1,
                                           batch_size=10,
                                           subtask_count=5000,
                                           timeout=None, optimize_xgb_params=False):
        """
        使用元学习优化加权参数，训练集成模型并保存

        Args:
            evaluation_results: evaluate_models_binary的返回结果
            random_seed: 随机种子
            version: 可选的版本标识
            description: 可选的版本描述
            save_best_fold: 是否保存最佳交叉验证折的模型，如果为False则在全量数据上训练
            n_iterations: 元学习迭代次数
            meta_learning_rate: 元学习率
            batch_size: 每次更新使用的子任务数
            subtask_count: 要构建的子任务总数
            timeout: 优化超时时间（秒）
            optimize_xgb_params: 是否同时优化XGBoost的关键参数（默认为False）

        Returns:
            tuple: (训练好的最佳模型, 版本号)
        """
        # 准备数据
        X_selected_clean, scaler, y = self.prepare_for_training(evaluation_results)
        X_selected_scaled = scaler.transform(X_selected_clean)
        features_df = self.features_df.copy()
        data = self.data.copy()
        best_model = evaluation_results['best_model']

        # 获取最佳模型的超参 - 在开始时固定
        best_model = evaluation_results['best_model']

        # 提取XGBoost参数（如果可用）
        if hasattr(best_model, 'get_params'):
            # 复制参数并移除不需要的参数
            params = best_model.get_params().copy()
            # 移除不需要的参数
            for param in ['base_score', 'booster', 'callbacks', 'importance_type',
                          'missing', 'n_jobs', 'verbose', 'early_stopping_rounds']:
                if param in params:
                    params.pop(param)
        else:
            # 默认XGBoost参数
            params = {
                'n_estimators': 600,
                'learning_rate': 0.01,
                'max_depth': 6,
                'min_child_weight': 1,
                'subsample': 0.9,
                'colsample_bytree': 0.9,
                'gamma': 0.05,
            }

        # 准备交叉时的分层信息
        hospitals = features_df['clinic_hospital']
        clinic_diabetes = features_df['clinic_diabetes']
        clinic_symp = features_df['clinic_symp']
        clinic_age = features_df['clinic_age']
        clinic_gender = features_df['clinic_gender']
        clinic_hypertension = features_df['clinic_hypertension']
        clinic_hyperlipidemia = features_df['clinic_hyperlipidemia']
        clinic_intervention = features_df['clinic_intervention']
        consistensy = data['标注一致性']

        # ======= 元学习特有的代码部分开始 =======

        # 初始化元学习参数（META Parameters）
        meta_params = {
            # 医院-标签-一致性权重（与原始代码相同的16个参数）
            'sh六院_pos_consistent': 1.0,
            'sh六院_pos_inconsistent': 1.0,
            'sh六院_neg_consistent': 1.0,
            'sh六院_neg_inconsistent': 1.0,
            'sh中山_pos_consistent': 1.0,
            'sh中山_pos_inconsistent': 1.0,
            'sh中山_neg_consistent': 1.0,
            'sh中山_neg_inconsistent': 1.0,
            'bj301_pos_consistent': 1.0,
            'bj301_pos_inconsistent': 1.0,
            'bj301_neg_consistent': 1.0,
            'bj301_neg_inconsistent': 1.0,
            'gd南海_pos_consistent': 1.0,
            'gd南海_pos_inconsistent': 1.0,
            'gd南海_neg_consistent': 1.0,
            'gd南海_neg_inconsistent': 1.0,
            '安医_pos_consistent': 1.0,
            '安医_pos_inconsistent': 1.0,
            '安医_neg_consistent': 1.0,
            '安医_neg_inconsistent': 1.0,
            # 心磁质量权重
            'quality_good_weight': 1.0,
            'quality_poor_weight': 1.0,
            # 矛盾度加权alpha参数
            'conflict_alpha': 1.0,
            'hypertension_yes_weight': 1.0,
            'hypertension_no_weight': 1.0,
            # 极不一致样本权重
        }

        # 如果启用XGBoost参数优化，则添加XGBoost参数到元学习参数中
        if optimize_xgb_params:
            meta_params.update({
                'n_estimators': params['n_estimators'],
                'learning_rate': params['learning_rate'],
                'max_depth': params['max_depth'],
                'subsample': params['subsample'],
                'colsample_bytree': params['colsample_bytree'],
            })

        # 定义子任务生成函数
        def generate_subtasks(count, random_state=42):
            """
            生成子任务用于元学习
            每个子任务是原始数据的子集，具有不同的属性分布
            """
            print(f"正在生成 {count} 个子任务...")
            np.random.seed(random_state)

            all_subtasks = []
            total_samples = len(y)

            # 为了生成多样化的子任务，使用不同的策略
            strategies = [
                # 随机采样，但保持类别平衡
                'balanced_random',
                # 按医院分组
                'hospital_stratified',
                # 按一致性分组
                'consistency_stratified',
                # 采样困难样本
                'hard_samples',
                # 随机权重测试
                'random_weights'
            ]

            strategy_weights = [0.3, 0.2, 0.2, 0.2, 0.1]  # 不同策略的比例
            strategy_counts = [int(count * w) for w in strategy_weights]
            # 确保总和等于count
            strategy_counts[-1] += count - sum(strategy_counts)

            subtask_id = 0

            # 1. 平衡随机采样
            for _ in range(strategy_counts[0]):
                # 为每类选择最少30%的样本
                pos_indices = np.random.choice(np.where(y == 1)[0],
                                               size=max(30, int(np.random.uniform(0.3, 0.7) * np.sum(y == 1))),
                                               replace=False)
                neg_indices = np.random.choice(np.where(y == 0)[0],
                                               size=max(30, int(np.random.uniform(0.3, 0.7) * np.sum(y == 0))),
                                               replace=False)

                indices = np.concatenate([pos_indices, neg_indices])
                np.random.shuffle(indices)

                # 随机生成一组权重，作为任务特定的权重
                task_weights = {k: np.random.uniform(0.1, 5.0) for k in meta_params.keys()}

                all_subtasks.append({
                    'id': subtask_id,
                    'indices': indices,
                    'strategy': 'balanced_random',
                    'task_weights': task_weights
                })
                subtask_id += 1

            # 2. 医院分层采样
            # hospital_list = ['上海六院', '上海十院', '北京301', '广东南海']
            hospital_list = ['上海六院', '上海中山', '北京301', '广东南海', '安医']

            for _ in range(strategy_counts[1]):
                # 随机选择1-3个医院
                n_hospitals = np.random.randint(1, 4)
                selected_hospitals = np.random.choice(hospital_list, size=n_hospitals, replace=False)

                # 获取所选医院的样本索引
                hospital_indices = []
                for hospital in selected_hospitals:
                    hospital_indices.extend(np.where(hospitals == hospital)[0])

                if len(hospital_indices) > 0:
                    # 确保至少有一些阳性和阴性样本
                    hospital_indices = np.array(hospital_indices)
                    pos_indices = hospital_indices[y[hospital_indices] == 1]
                    neg_indices = hospital_indices[y[hospital_indices] == 0]

                    # 对每个类别进行随机采样
                    pos_sample_size = min(len(pos_indices),
                                          max(10, int(np.random.uniform(0.3, 1.0) * len(pos_indices))))
                    neg_sample_size = min(len(neg_indices),
                                          max(10, int(np.random.uniform(0.3, 1.0) * len(neg_indices))))

                    if pos_sample_size > 0 and neg_sample_size > 0:
                        sampled_pos = np.random.choice(pos_indices, size=pos_sample_size, replace=False)
                        sampled_neg = np.random.choice(neg_indices, size=neg_sample_size, replace=False)
                        indices = np.concatenate([sampled_pos, sampled_neg])

                        # 生成偏向所选医院的权重
                        task_weights = {k: np.random.uniform(0.1, 5.0) for k in meta_params.keys()}
                        for hospital in selected_hospitals:
                            short_name = hospital.replace('上海', 'sh').replace('北京', 'bj').replace('广东', 'gd')
                            # 提高所选医院的权重
                            for suffix in ['_pos_consistent', '_pos_inconsistent', '_neg_consistent',
                                           '_neg_inconsistent']:
                                task_weights[f'{short_name}{suffix}'] = np.random.uniform(1.0, 5.0)

                        all_subtasks.append({
                            'id': subtask_id,
                            'indices': indices,
                            'strategy': 'hospital_stratified',
                            'selected_hospitals': selected_hospitals,
                            'task_weights': task_weights
                        })
                        subtask_id += 1

            # 3. 一致性分层采样
            for _ in range(strategy_counts[2]):
                # 随机决定是偏向一致还是不一致的样本
                focus_on_consistent = np.random.choice([True, False])

                if focus_on_consistent:
                    # 偏向一致样本
                    consistent_indices = np.where(consistensy == 1)[0]
                    inconsistent_indices = np.where(consistensy == 0)[0]

                    # 选择大部分一致样本和少量不一致样本
                    n_consistent = min(len(consistent_indices),
                                       max(30, int(np.random.uniform(0.5, 0.9) * len(consistent_indices))))
                    n_inconsistent = min(len(inconsistent_indices),
                                         max(10, int(np.random.uniform(0.1, 0.3) * len(inconsistent_indices))))

                    sampled_consistent = np.random.choice(consistent_indices, size=n_consistent, replace=False)
                    sampled_inconsistent = np.random.choice(inconsistent_indices, size=n_inconsistent, replace=False)

                    task_weights = {k: np.random.uniform(0.1, 5.0) for k in meta_params.keys()}
                    # 提高一致样本的权重
                    for hospital in hospital_list:
                        short_name = hospital.replace('上海', 'sh').replace('北京', 'bj').replace('广东', 'gd')
                        task_weights[f'{short_name}_pos_consistent'] = np.random.uniform(2.0, 5.0)
                        task_weights[f'{short_name}_neg_consistent'] = np.random.uniform(2.0, 5.0)
                        task_weights[f'{short_name}_pos_inconsistent'] = np.random.uniform(0.1, 2.0)
                        task_weights[f'{short_name}_neg_inconsistent'] = np.random.uniform(0.1, 2.0)
                else:
                    # 偏向不一致样本
                    consistent_indices = np.where(consistensy == 1)[0]
                    inconsistent_indices = np.where(consistensy == 0)[0]

                    # 选择少量一致样本和大部分不一致样本
                    n_consistent = min(len(consistent_indices),
                                       max(10, int(np.random.uniform(0.1, 0.3) * len(consistent_indices))))
                    n_inconsistent = min(len(inconsistent_indices),
                                         max(30, int(np.random.uniform(0.5, 0.9) * len(inconsistent_indices))))

                    sampled_consistent = np.random.choice(consistent_indices, size=n_consistent, replace=False)
                    sampled_inconsistent = np.random.choice(inconsistent_indices, size=n_inconsistent, replace=False)

                    task_weights = {k: np.random.uniform(0.1, 5.0) for k in meta_params.keys()}
                    # 提高不一致样本的权重
                    for hospital in hospital_list:
                        short_name = hospital.replace('上海', 'sh').replace('北京', 'bj').replace('广东', 'gd')
                        task_weights[f'{short_name}_pos_consistent'] = np.random.uniform(0.1, 2.0)
                        task_weights[f'{short_name}_neg_consistent'] = np.random.uniform(0.1, 2.0)
                        task_weights[f'{short_name}_pos_inconsistent'] = np.random.uniform(2.0, 5.0)
                        task_weights[f'{short_name}_neg_inconsistent'] = np.random.uniform(2.0, 5.0)

                indices = np.concatenate([sampled_consistent, sampled_inconsistent])

                all_subtasks.append({
                    'id': subtask_id,
                    'indices': indices,
                    'strategy': 'consistency_stratified',
                    'focus_on_consistent': focus_on_consistent,
                    'task_weights': task_weights
                })
                subtask_id += 1

            # 5. 随机权重测试
            for _ in range(strategy_counts[4]):
                # 完全随机的样本
                n_samples = max(60, int(np.random.uniform(0.1, 0.3) * total_samples))
                indices = np.random.choice(total_samples, size=n_samples, replace=False)

                # 完全随机的权重
                task_weights = {k: np.random.uniform(0.1, 5.0) for k in meta_params.keys()}

                all_subtasks.append({
                    'id': subtask_id,
                    'indices': indices,
                    'strategy': 'random_weights',
                    'task_weights': task_weights
                })
                subtask_id += 1

            print(
                f"共生成 {len(all_subtasks)} 个子任务, 策略分布: {[sum(1 for t in all_subtasks if t['strategy'] == s) for s in strategies]}")
            return all_subtasks

        # 定义元学习的评估函数
        def evaluate_params_on_subtask(subtask, current_meta_params, X_data, y_data, attribute_data, base_params):
            """
            在给定子任务上评估当前元参数的性能
            返回准确率和梯度信息
            """
            # 获取子任务的索引
            indices = subtask['indices']
            X_subtask = X_data[indices]

            # 确保y_data是numpy数组
            if isinstance(y_data, pd.Series):
                y_data = y_data.values
            y_subtask = y_data[indices]

            attr_subtask = attribute_data.iloc[indices]

            # 70/30拆分为训练集和验证集
            n_samples = len(indices)
            indices_array = np.arange(n_samples)  # 使用从0开始的索引
            n_train = int(0.7 * n_samples)

            # 使用从0开始的索引进行训练/验证集拆分
            train_idx = np.random.choice(indices_array, size=n_train, replace=False)
            val_idx = np.array([i for i in indices_array if i not in train_idx])

            # 使用这些索引来切分子集数据
            X_train = X_subtask[train_idx]
            y_train = y_subtask[train_idx]
            attr_train = attr_subtask.iloc[train_idx]

            X_val = X_subtask[val_idx]
            y_val = y_subtask[val_idx]

            # 构建属性加权参数
            attribute_weights = {
                'combinations': [],
                '心磁质量': {
                    '可用': current_meta_params['quality_good_weight'],
                    '优': current_meta_params['quality_good_weight'],
                    '稍差': current_meta_params['quality_poor_weight'],
                    '偏差': current_meta_params['quality_poor_weight'],
                    '良': current_meta_params['quality_poor_weight'],

                },
                '临床特征-高血压': {
                    1: current_meta_params['hypertension_yes_weight'],
                    0: current_meta_params['hypertension_no_weight']
                }
            }

            # 医院列表
            # hospitals_list = ['上海六院', '上海十院', '北京301', '广东南海']
            hospitals_list = ['上海六院', '上海中山', '北京301', '广东南海', '安医']

            # 为每个医院-标签状态-一致性状态的组合设置权重
            for hospital in hospitals_list:
                # 短名称用于参数命名
                short_name = hospital.replace('上海', 'sh').replace('北京', 'bj').replace('广东', 'gd')

                # 阳性且一致
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 1),  # 阳性
                        ('标注一致性', 1)  # 一致
                    ],
                    'weight': current_meta_params[f'{short_name}_pos_consistent']
                })

                # 阳性但不一致
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 1),  # 阳性
                        ('标注一致性', 0)  # 不一致
                    ],
                    'weight': current_meta_params[f'{short_name}_pos_inconsistent']
                })

                # 阴性且一致
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 0),  # 阴性
                        ('标注一致性', 1)  # 一致
                    ],
                    'weight': current_meta_params[f'{short_name}_neg_consistent']
                })

                # 阴性但不一致
                attribute_weights['combinations'].append({
                    'conditions': [
                        ('临床特征-所在医院', hospital),
                        ('造影结论', 0),  # 阴性
                        ('标注一致性', 0)  # 不一致
                    ],
                    'weight': current_meta_params[f'{short_name}_neg_inconsistent']
                })

            # 获取矛盾度参数
            conflict_alpha = current_meta_params['conflict_alpha']

            # 如果启用XGBoost参数优化，则更新XGBoost参数
            current_params = base_params.copy()
            if optimize_xgb_params:
                # 严格检查和约束所有XGBoost参数的范围
                xgb_param_constraints = {
                    'n_estimators': (100, 1000, int),  # (最小值, 最大值, 类型转换函数)
                    'learning_rate': (0.01, 0.3, float),
                    'max_depth': (3, 10, int),
                    'subsample': (0.1, 0.999, float),  # 避免使用1.0，给一点余量
                    'colsample_bytree': (0.1, 0.999, float),
                    'gamma': (0, 10, float),
                    'min_child_weight': (1, 10, float)
                }

                for param_name, (min_val, max_val, type_func) in xgb_param_constraints.items():
                    if param_name in current_meta_params:
                        # 先应用范围约束，再转换类型
                        constrained_value = max(min_val, min(max_val, current_meta_params[param_name]))
                        current_params[param_name] = type_func(constrained_value)

            # 使用当前参数训练模型
            results = attr_weighted_training_with_conflict(
                X_train, y_train,
                X_val, y_val,
                attribute_data=attr_train,
                attribute_weights=attribute_weights,
                params=current_params,
                conflict_alpha=conflict_alpha
            )

            # 获取当前参数的性能
            accuracy = results['weighted_accuracy']
            base_accuracy = results['base_accuracy']

            # 计算梯度 - 关键部分
            gradients = {}

            # 计算任务特定权重与当前权重的差异
            # 这个差异作为一个"推荐"的方向，但最终是否采纳取决于性能反馈
            task_weights = subtask['task_weights']

            for param_name, current_value in current_meta_params.items():
                task_value = task_weights.get(param_name, current_value)
                raw_diff = task_value - current_value

                # 如果性能提升，我们更倾向于向任务推荐方向移动
                # 如果性能下降，我们更倾向于远离当前方向
                if accuracy > base_accuracy:
                    # 性能提升 - 向任务权重方向移动
                    # 提升越大，移动越快
                    improvement_factor = (accuracy - base_accuracy) * 5  # 放大因子
                    gradients[param_name] = raw_diff * improvement_factor
                else:
                    # 性能下降 - 稍微远离当前方向
                    # 这里的关键思想是：当前方向不好，需要尝试其他方向
                    # 我们不是简单地反向，而是通过探索不同的方向
                    decline_factor = (base_accuracy - accuracy) * 3  # 放大因子

                    # 关键改进：引入探索性随机扰动
                    # 这里不是简单地反向，而是根据性能下降程度引入随机扰动
                    exploration_range = min(0.2, abs(raw_diff) + 0.05)  # 有限的探索范围
                    random_component = np.random.uniform(-exploration_range, exploration_range)

                    # 综合考虑反向信号和随机探索
                    gradients[param_name] = -raw_diff * decline_factor * 0.5 + random_component

            return {
                'accuracy': accuracy,
                'base_accuracy': base_accuracy,
                'improvement': accuracy - base_accuracy,
                'gradients': gradients
            }

        # 生成子任务
        all_subtasks = generate_subtasks(subtask_count, random_seed)

        # 元学习训练循环
        print("\n=== 开始元学习训练循环 ===")
        best_accuracy = 0
        best_params = meta_params.copy()
        training_history = []

        start_time = time.time()
        for iteration in range(n_iterations):
            # 检查超时
            if timeout and time.time() - start_time > timeout:
                print(f"已达到超时限制 {timeout}秒，提前结束训练")
                break

            # 随机选择一批子任务
            batch_tasks = np.random.choice(all_subtasks, size=min(batch_size, len(all_subtasks)), replace=False)

            # 打印本轮迭代开始信息
            print(f"\n--- 迭代 {iteration + 1}/{n_iterations} 开始 ---")
            print("当前参数值:")
            # 分组显示参数，使输出更有组织性
            param_groups = {
                "医院-一致性权重": [p for p in meta_params.keys() if
                                    any(h in p for h in ['sh六院', 'sh中山', 'bj301', 'gd南海', '安医'])],
                "心磁质量权重": ['quality_good_weight', 'quality_poor_weight'],
                "其他权重": ['conflict_alpha'],
                "高血压": ['hypertension_yes_weight', 'hypertension_no_weight']
            }

            for group_name, param_list in param_groups.items():
                print(f"  {group_name}:")
                for param in param_list:
                    print(f"    {param}: {meta_params[param]:.4f}")

            # 评估当前参数在所选子任务上的性能，并收集梯度信息
            batch_results = []
            print("\n子任务评估结果:")
            for i, task in enumerate(batch_tasks):
                result = evaluate_params_on_subtask(
                    task, meta_params, X_selected_scaled, y, data, params
                )
                batch_results.append(result)
                print(f"  任务 {i + 1}/{len(batch_tasks)} (策略: {task['strategy']}): "
                      f"基准准确率={result['base_accuracy']:.4f}, "
                      f"加权准确率={result['accuracy']:.4f}, "
                      f"改进={result['improvement']:.4f}")

            # 计算平均梯度并更新参数
            avg_gradients = {}
            for param_name in meta_params.keys():
                avg_gradients[param_name] = np.mean([r['gradients'].get(param_name, 0) for r in batch_results])

            print("\n计算的平均梯度:")
            for group_name, param_list in param_groups.items():
                print(f"  {group_name}:")
                for param in param_list:
                    print(f"    {param}: {avg_gradients[param]:.6f}")

            # 参数更新
            print("\n参数更新:")
            for param_name, gradient in avg_gradients.items():
                old_value = meta_params[param_name]
                # 应用学习率并执行更新
                meta_params[param_name] += meta_learning_rate * gradient

                if optimize_xgb_params:
                    if param_name == 'n_estimators':
                        meta_params[param_name] = max(100, min(1000, meta_params[param_name]))
                        meta_params[param_name] = int(meta_params[param_name])
                    elif param_name == 'learning_rate':
                        meta_params[param_name] = max(0.01, min(0.3, meta_params[param_name]))
                    elif param_name == 'max_depth':
                        meta_params[param_name] = max(3, min(10, meta_params[param_name]))
                        meta_params[param_name] = int(meta_params[param_name])
                    elif param_name == 'subsample':
                        meta_params[param_name] = max(0.1, min(0.999, meta_params[param_name]))
                    elif param_name == 'colsample_bytree':
                        meta_params[param_name] = max(0.1, min(0.999, meta_params[param_name]))
                    elif param_name == 'gamma':
                        meta_params[param_name] = max(0, min(10, meta_params[param_name]))
                    elif param_name == 'min_child_weight':
                        meta_params[param_name] = max(1, min(10, meta_params[param_name]))

                    # 处理其他权重参数
                elif param_name in ['conflict_alpha']:
                    meta_params[param_name] = max(0.01, min(8.0, meta_params[param_name]))
                elif param_name in ['quality_good_weight', 'quality_poor_weight']:
                    meta_params[param_name] = max(0.2, min(2.0, meta_params[param_name]))
                elif 'consistent' in param_name or 'inconsistent' in param_name:
                    meta_params[param_name] = max(0.05, min(5.0, meta_params[param_name]))

                new_value = meta_params[param_name]
                change = new_value - old_value
                direction = "增加" if change > 0 else "减少" if change < 0 else "不变"

                # 只打印有变化的参数
                if abs(change) > 0.0001:
                    print(f"    {param_name}: {old_value:.4f} -> {new_value:.4f} ({direction} {abs(change):.4f})")

            # 在所有任务上评估平均性能
            avg_accuracy = np.mean([r['accuracy'] for r in batch_results])
            avg_improvement = np.mean([r['improvement'] for r in batch_results])

            # 更新最佳参数
            if avg_accuracy > best_accuracy:
                best_accuracy = avg_accuracy
                best_params = meta_params.copy()
                print(f"\n发现新的最佳参数，准确率: {best_accuracy:.4f}, 平均提升: {avg_improvement:.4f}")

            print(f"\n--- 迭代 {iteration + 1}/{n_iterations} 完成 ---")
            print(f"平均准确率: {avg_accuracy:.4f}, 平均提升: {avg_improvement:.4f}")

            # 计算权重统计
            weights_below_one = sum(1 for v in meta_params.values() if v < 1.0)
            weights_above_one = sum(1 for v in meta_params.values() if v > 1.0)
            print(f"权重分布: {weights_below_one}个小于1.0, {weights_above_one}个大于1.0")

            # 记录历史
            training_history.append({
                'iteration': iteration,
                'avg_accuracy': avg_accuracy,
                'avg_improvement': avg_improvement,
                'params': meta_params.copy(),
                'weights_below_one': weights_below_one,
                'weights_above_one': weights_above_one
            })

        total_time = time.time() - start_time
        print(f"\n=== 元学习完成，总用时: {total_time:.2f}秒 ===")
        print(f"最佳准确率: {best_accuracy:.4f}")

        # 打印最佳参数
        print("\n=== 元学习优化结果 ===")
        print("-- 属性加权参数:")
        for param_name, param_value in best_params.items():
            if param_name not in ['n_estimators', 'learning_rate', 'max_depth', 'subsample', 'colsample_bytree']:
                print(f"  {param_name}: {param_value}")

        # 如果优化了XGBoost参数，单独显示
        if optimize_xgb_params:
            print("-- XGBoost参数:")
            for param_name in ['n_estimators', 'learning_rate', 'max_depth', 'subsample', 'colsample_bytree']:
                if param_name in best_params:
                    print(f"  {param_name}: {best_params[param_name]}")

        # 构建最佳属性加权参数
        best_attribute_weights = {
            'combinations': [],
            '心磁质量': {
                '可用': best_params['quality_good_weight'],
                '优': best_params['quality_good_weight'],
                '稍差': best_params['quality_poor_weight'],
                '偏差': best_params['quality_poor_weight'],
                '良': best_params['quality_poor_weight'],
            },
            '临床特征-高血压': {
                1: best_params['hypertension_yes_weight'],
                0: best_params['hypertension_no_weight']
            }

        }

        # 医院列表
        hospitals_list = ['上海六院', '上海中山', '北京301', '广东南海', '安医']

        # 为每个医院-标签状态-一致性状态的组合设置最佳权重
        for hospital in hospitals_list:
            # 短名称用于参数命名
            short_name = hospital.replace('上海', 'sh').replace('北京', 'bj').replace('广东', 'gd')

            # 阳性且一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 1),  # 阳性
                    ('标注一致性', 1)  # 一致
                ],
                'weight': best_params[f'{short_name}_pos_consistent']
            })

            # 阳性但不一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 1),  # 阳性
                    ('标注一致性', 0)  # 不一致
                ],
                'weight': best_params[f'{short_name}_pos_inconsistent']
            })

            # 阴性且一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 0),  # 阴性
                    ('标注一致性', 1)  # 一致
                ],
                'weight': best_params[f'{short_name}_neg_consistent']
            })

            # 阴性但不一致
            best_attribute_weights['combinations'].append({
                'conditions': [
                    ('临床特征-所在医院', hospital),
                    ('造影结论', 0),  # 阴性
                    ('标注一致性', 0)  # 不一致
                ],
                'weight': best_params[f'{short_name}_neg_inconsistent']
            })

        # 获取最佳矛盾度参数
        best_conflict_alpha = best_params['conflict_alpha']

        # 获取优化后的XGBoost参数（如果启用了参数优化）
        if optimize_xgb_params:
            best_xgb_params = {}
            for param_name in ['n_estimators', 'learning_rate', 'max_depth', 'subsample', 'colsample_bytree']:
                if param_name in best_params:
                    best_xgb_params[param_name] = best_params[param_name]

            # 更新XGBoost参数
            params.update(best_xgb_params)
            print("\n优化后的XGBoost参数:")
            for param_name, param_value in best_xgb_params.items():
                print(f"  {param_name}: {param_value}")

        # 准备交叉验证，与原代码类似
        def gen_all_cv_folds():
            # 生成3个不同的随机种子
            random_seeds = [random_seed, random_seed + 100, random_seed + 200]
            print(f"使用3个随机种子进行评估: {random_seeds}")
            # 预先为每个种子生成交叉验证折，以确保所有trial使用相同的数据划分
            all_cv_folds = []
            for seed in random_seeds:
                custom_cv = CustomStratifiedKFold(n_splits=5, shuffle=True, random_state=seed)
                cv_folds = list(
                    custom_cv.split(X_selected_scaled, y, consistensy, hospitals, clinic_hypertension, clinic_gender,
                                    clinic_hyperlipidemia, clinic_intervention, clinic_diabetes, clinic_symp))
                all_cv_folds.extend(
                    [(seed, fold_idx, train_idx, test_idx) for fold_idx, (train_idx, test_idx) in enumerate(cv_folds)])

            print(f"总共生成 {len(all_cv_folds)} 个交叉验证折 (3个种子 x 5折)")
            y_array = np.array(y)
            return all_cv_folds, y_array

        # 使用最佳参数进行最终模型训练
        print("\n=== 使用最佳元学习参数进行多种子五折交叉验证 ===")
        print(f"矛盾度alpha: {best_conflict_alpha}")

        # 初始化保存结果的字典
        cv_results = {
            'fold_models': [],
            'fold_metrics': [],
            'base_models': [],
            'best_fold': None,
            'best_seed': None,
            'best_score': 0
        }

        best_weighted_model = None
        best_weighted_score = 0

        all_cv_folds, y_array = gen_all_cv_folds()
        # 对每个种子和折进行最终评估
        # 2. 在交叉验证循环中，保存每一折的基础模型和加权模型
        for seed, fold_idx, train_idx, test_idx in all_cv_folds:
            print(f"\n--- 种子 {seed}, 折 {fold_idx + 1}/5 ---")

            # 分割数据
            X_train_fold, X_test_fold = X_selected_scaled[train_idx], X_selected_scaled[test_idx]
            y_train_fold, y_test_fold = y_array[train_idx], y_array[test_idx]

            # 准备属性数据
            attribute_data_train = data.iloc[train_idx]

            # 使用最佳参数训练模型
            fold_results = attr_weighted_training_with_conflict(
                X_train_fold, y_train_fold,
                X_test_fold, y_test_fold,
                attribute_data=attribute_data_train,
                attribute_weights=best_attribute_weights,
                params=params,
                conflict_alpha=best_conflict_alpha
            )

            # 保存折结果 - 同时保存基础模型和加权模型
            cv_results['fold_models'].append(fold_results['weighted_model'])
            cv_results['base_models'].append(fold_results['base_model'])  # 添加这一行，确保保存基础模型
            cv_results['fold_metrics'].append({
                'seed': seed,
                'fold': fold_idx + 1,
                'base_accuracy': fold_results['base_accuracy'],
                'weighted_accuracy': fold_results['weighted_accuracy'],
                'improvement': fold_results['improvement'],
                'correction_stats': fold_results['correction_stats']
            })

            # 更新最佳折
            if fold_results['weighted_accuracy'] > best_weighted_score:
                best_weighted_score = fold_results['weighted_accuracy']
                best_weighted_model = fold_results['weighted_model']
                cv_results['best_fold'] = fold_idx + 1
                cv_results['best_seed'] = seed
                cv_results['best_score'] = best_weighted_score

        # 打印交叉验证结果摘要
        print("\n=== 多种子五折交叉验证结果 ===")
        for metric in cv_results['fold_metrics']:
            print(f"种子 {metric['seed']}, 折 {metric['fold']}: 基准准确率={metric['base_accuracy']:.4f}, "
                  f"加权准确率={metric['weighted_accuracy']:.4f}, "
                  f"改进={metric['improvement']:.4f}")

        print(
            f"\n最佳结果: 种子 {cv_results['best_seed']}, 折 {cv_results['best_fold']}, 准确率: {cv_results['best_score']:.4f}")

        # 最终模型：选择最佳折模型或在全量数据上训练，并比较加权前后的模型
        if save_best_fold:
            print("\n=== 比较最佳折的加权前后模型性能 ===")
            # 找到最佳折的基础模型和加权模型
            best_fold_index = next(i for i, m in enumerate(cv_results['fold_metrics'])
                                   if m['seed'] == cv_results['best_seed'] and m['fold'] == cv_results['best_fold'])
            best_fold_base_accuracy = cv_results['fold_metrics'][best_fold_index]['base_accuracy']
            best_fold_weighted_accuracy = cv_results['fold_metrics'][best_fold_index]['weighted_accuracy']

            # 比较基础模型与加权模型性能
            if best_fold_base_accuracy > best_fold_weighted_accuracy:
                print(f"基础模型性能更好: {best_fold_base_accuracy:.4f} vs 加权模型: {best_fold_weighted_accuracy:.4f}")
                # 需要从 fold_results 中获取基础模型，在 attr_weighted_training_with_conflict 函数中应该返回了基础模型
                # 假设有个 base_models 列表存储了每个折的基础模型
                final_model = cv_results.get('base_models', [])[best_fold_index]
                final_metrics = {
                    'accuracy': best_fold_base_accuracy,
                    'model_source': f'best_base_model_seed_{cv_results["best_seed"]}_fold_{cv_results["best_fold"]}',
                    'is_weighted': False
                }
            else:
                print(f"加权模型性能更好: {best_fold_weighted_accuracy:.4f} vs 基础模型: {best_fold_base_accuracy:.4f}")
                final_model = best_weighted_model
                final_metrics = {
                    'accuracy': best_weighted_score,
                    'model_source': f'best_weighted_model_seed_{cv_results["best_seed"]}_fold_{cv_results["best_fold"]}',
                    'is_weighted': True
                }
        else:
            print("\n=== 在全量数据上训练最终模型并比较加权前后性能 ===")
            # 在全量数据上训练模型
            attribute_data_full = data

            final_results = attr_weighted_training_with_conflict(
                X_selected_scaled, y,
                X_selected_scaled, y,  # 使用相同数据作为测试集（仅为了保持API一致）
                attribute_data=attribute_data_full,
                attribute_weights=best_attribute_weights,
                params=params,
                conflict_alpha=best_conflict_alpha
            )

            # 比较全量数据上的基础模型与加权模型性能
            if final_results['base_accuracy'] > final_results['weighted_accuracy']:
                print(
                    f"基础模型性能更好: {final_results['base_accuracy']:.4f} vs 加权模型: {final_results['weighted_accuracy']:.4f}")
                final_model = final_results['base_model']  # 假设 attr_weighted_training_with_conflict 函数返回了基础模型
                final_metrics = {
                    'accuracy': final_results['base_accuracy'],
                    'model_source': 'full_data_base_model',
                    'is_weighted': False
                }
            else:
                print(
                    f"加权模型性能更好: {final_results['weighted_accuracy']:.4f} vs 基础模型: {final_results['base_accuracy']:.4f}")
                final_model = final_results['weighted_model']
                final_metrics = {
                    'accuracy': final_results['weighted_accuracy'],
                    'model_source': 'full_data_weighted_model',
                    'is_weighted': True
                }

        trainer = ModelTrainer()

        # 保存模型到文件系统
        if not hasattr(trainer, 'version_info'):
            trainer.version_info = {'versions': {}, 'latest_version': None}

        # 如果未指定版本，则自动生成版本号
        if version is None:
            date_str = datetime.now().strftime('%Y%m%d')
            existing_versions = [v for v in trainer.version_info['versions'].keys()
                                 if v.startswith(date_str)]
            version = f"{date_str}_{len(existing_versions) + 1}"

        version_dir = trainer._get_version_dir(version)
        if not os.path.exists(version_dir):
            os.makedirs(version_dir)

        print(f"\n=== 保存模型 版本:{version} ===")

        # 保存模型和相关文件
        files_to_save = {
            'model.pkl': final_model,
        }

        if scaler is not None:
            files_to_save['scaler.pkl'] = scaler

        if evaluation_results['selected_features'] is not None:
            files_to_save['features.pkl'] = evaluation_results['selected_features']

        for filename, obj in files_to_save.items():
            filepath = os.path.join(version_dir, filename)
            with open(filepath, 'wb') as f:
                pickle.dump(obj, f)

        # 在训练集上评估模型性能
        print("\n=== 训练集性能评估 ===")
        if hasattr(final_model, 'predict_proba'):
            train_probs = final_model.predict_proba(X_selected_scaled)
            train_preds = (train_probs[:, 1] > 0.5).astype(int)
        else:
            train_preds = final_model.predict(X_selected_scaled)
            train_probs = None

        # 计算指标
        train_metrics = {
            'accuracy': accuracy_score(y, train_preds),
            'precision': precision_score(y, train_preds),
            'recall': recall_score(y, train_preds),
            'specificity': recall_score(y, train_preds, pos_label=0),
            'f1': f1_score(y, train_preds),
        }

        # 如果有概率预测，添加AUC
        if train_probs is not None and train_probs.shape[1] > 1:
            train_metrics['auc'] = roc_auc_score(y, train_probs[:, 1])

        # 打印评估指标
        print("\n训练集评估指标:")
        for metric, value in train_metrics.items():
            print(f"{metric.capitalize()}: {value:.4f}")

        # 打印混淆矩阵
        conf_matrix = confusion_matrix(y, train_preds)
        print("\n混淆矩阵:")
        print("          预测        Negative  Positive")
        print(f"实际 Negative  {conf_matrix[0, 0]:8d} {conf_matrix[0, 1]:8d}")
        print(f"实际 Positive  {conf_matrix[1, 0]:8d} {conf_matrix[1, 1]:8d}")

        # 计算错误率
        error_mask = train_preds != y
        error_count = np.sum(error_mask)
        error_rate = error_count / len(y)
        print(f"\n错误预测样本数: {error_count}")
        print(f"错误率: {error_rate:.4f}")

        # 创建元数据
        metadata = {
            'version': version,
            'model_type': 'MetaLearningEnsembleModel',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'description': description,
            'train_metrics': train_metrics,
            'feature_count': len(evaluation_results['selected_features']),
            'sample_count': len(y),
            'class_distribution': pd.Series(y).value_counts().to_dict(),
            'error_rate': float(error_rate),
            'cv_results': {
                'random_seeds': [random_seed, random_seed + 100, random_seed + 200],
                'best_seed': cv_results['best_seed'],
                'best_fold': cv_results['best_fold'],
                'best_score': cv_results['best_score'],
                'fold_metrics': cv_results['fold_metrics']
            },
            'meta_learning_results': {
                'best_params': best_params,
                'best_value': best_accuracy,
                'n_iterations': n_iterations,
                'meta_learning_rate': meta_learning_rate,
                'conflict_alpha': best_conflict_alpha,
                'subtask_count': subtask_count,
                'training_time': total_time
            },
            'xgb_params_optimized': optimize_xgb_params
        }

        # 更新版本信息
        trainer.version_info['versions'][version] = metadata
        trainer.version_info['latest_version'] = version
        trainer._save_version_info()

        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj

        # 转换数据
        converted_info = convert_numpy_types(metadata)
        # 保存版本元数据
        metadata_path = os.path.join(version_dir, 'metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(converted_info, f, ensure_ascii=False, indent=2)

        print(f"\n模型和配置已保存到版本目录: {version_dir}")
        print(f"版本信息:")
        print(f"- 版本: {version}")
        print(f"- 模型: MetaLearningEnsembleModel (元学习加权模型)")
        print(f"- 矛盾度alpha: {best_conflict_alpha}")
        if optimize_xgb_params:
            print(f"- XGBoost参数已优化")
        print(f"- 元学习参数: {n_iterations}次迭代, {subtask_count}个子任务")
        print(f"- 交叉验证: 3种子×5折，最佳种子={cv_results['best_seed']}，最佳折={cv_results['best_fold']}")
        print(f"- 创建时间: {metadata['created_at']}")
        if description:
            print(f"- 描述: {description}")

        return final_model, version

    @staticmethod
    def model_with_latent_moe(X_selected_scaled, y, hospitals, clinic_diabetes, clinic_symp, clinic_age,
                              selected_features, consistency_values, models, metrics_display=None):
        """
        基于潜在路由的混合专家模型 - 训练时使用consistency信息，但推理时不依赖它

        参数:
        X_selected_scaled: 标准化后的特征矩阵
        y: 目标变量
        hospitals: 样本所属医院标识
        consistency_values: 二值一致性标志(0/1)，仅用于训练
        models: 候选模型字典
        其他参数: 与原代码保持一致的其他特征和参数
        """
        from sklearn.linear_model import LogisticRegression
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.base import clone
        from sklearn.model_selection import StratifiedKFold
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
        import numpy as np

        print("\n" + "=" * 80)
        print(f"{'开始潜在路由MOE模型构建':^80}")
        print("=" * 80)
        print("\n基于潜在路由的混合专家模型 - 训练时利用consistency信息，但推理时不依赖它")

        # 统计consistency分布
        consistency_0_count = np.sum(consistency_values == 0)
        consistency_1_count = np.sum(consistency_values == 1)
        consistency_0_ratio = consistency_0_count / len(consistency_values) * 100
        consistency_1_ratio = consistency_1_count / len(consistency_values) * 100

        print(f"\n一致性(consistency)分布:")
        print(f"consistency=0: {consistency_0_count}个样本 ({consistency_0_ratio:.1f}%)")
        print(f"consistency=1: {consistency_1_count}个样本 ({consistency_1_ratio:.1f}%)")
        print(f"注意: consistency仅作为训练辅助信息，推理时不可用")

        # 验证consistency反向预测特性
        print("\n=== 验证consistency=0样本的反向预测特性 ===")
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        consistency_0_auc = []
        consistency_1_auc = []

        for train_idx, test_idx in skf.split(X_selected_scaled, y):
            X_train, X_test = X_selected_scaled[train_idx], X_selected_scaled[test_idx]
            y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
            consistency_train, consistency_test = consistency_values[train_idx], consistency_values[test_idx]

            # 训练一个总体模型
            model = LogisticRegression(random_state=42)
            model.fit(X_train, y_train)

            # 评估在不同consistency组上的表现
            if np.sum(consistency_test == 0) > 10:
                y_prob_0 = model.predict_proba(X_test[consistency_test == 0])[:, 1]
                auc_0 = roc_auc_score(y_test.iloc[consistency_test == 0], y_prob_0)
                consistency_0_auc.append(auc_0)

            if np.sum(consistency_test == 1) > 10:
                y_prob_1 = model.predict_proba(X_test[consistency_test == 1])[:, 1]
                auc_1 = roc_auc_score(y_test.iloc[consistency_test == 1], y_prob_1)
                consistency_1_auc.append(auc_1)

        if consistency_0_auc:
            avg_auc_0 = np.mean(consistency_0_auc)
            print(f"总体模型在consistency=0样本上的平均AUC: {avg_auc_0:.4f}")
            if avg_auc_0 < 0.5:
                print(f"确认: consistency=0样本确实表现出反向预测特性 (AUC < 0.5)")
                reverse_predictions = True
            else:
                print(f"注意: consistency=0样本未表现出明显的反向预测特性 (AUC >= 0.5)")
                reverse_predictions = False

        if consistency_1_auc:
            avg_auc_1 = np.mean(consistency_1_auc)
            print(f"总体模型在consistency=1样本上的平均AUC: {avg_auc_1:.4f}")

        # 收集所有中心
        unique_hospitals = np.unique(hospitals)
        print(f"\n医院中心数量: {len(unique_hospitals)}")

        print("\n=== 步骤1: 训练专家模型 ===")

        # 为每个中心训练两种专家模型 (分别针对consistency=0和consistency=1)
        experts = []

        for hospital in unique_hospitals:
            hospital_mask = hospitals == hospital
            X_hospital = X_selected_scaled[hospital_mask]
            y_hospital = y[hospital_mask]
            consistency_hospital = consistency_values[hospital_mask]

            print(f"\n{'=' * 20} 医院: {hospital} {'=' * 20}")
            print(f"样本总数: {len(X_hospital)}")
            print(f"consistency=0: {np.sum(consistency_hospital == 0)}")
            print(f"consistency=1: {np.sum(consistency_hospital == 1)}")

            # 检查样本量是否足够
            if len(X_hospital) < 20:
                print(f"警告: 样本量不足 ({len(X_hospital)} < 20)，跳过此中心")
                continue

            # 针对consistency=0样本训练专家模型
            if np.sum(consistency_hospital == 0) >= 10:
                try:
                    print("\n训练针对consistency=0的专家模型...")
                    mask_0 = consistency_hospital == 0
                    X_0 = X_hospital[mask_0]
                    y_0 = y_hospital.iloc[mask_0]

                    if len(np.unique(y_0)) < 2:
                        print("警告: 类别不足，无法训练模型")
                    else:
                        # 选择最佳模型
                        best_model = None
                        best_score = 0
                        best_name = None

                        for model_name, model in models.items():
                            if len(y_0) < 30:
                                # 对于小样本，简单评估
                                cloned_model = clone(model)
                                if reverse_predictions:
                                    cloned_model.fit(X_0, 1 - y_0)  # 反转标签训练
                                    score = cross_val_score(cloned_model, X_0, 1 - y_0, cv=3, scoring='roc_auc').mean()
                                else:
                                    cloned_model.fit(X_0, y_0)
                                    score = cross_val_score(cloned_model, X_0, y_0, cv=3, scoring='roc_auc').mean()
                                print(f"{model_name}: AUC = {score:.4f}")
                            else:
                                # 对于较大样本，使用交叉验证
                                cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
                                scores = []
                                for train_idx, val_idx in cv.split(X_0, y_0):
                                    X_train, X_val = X_0[train_idx], X_0[val_idx]
                                    y_train, y_val = y_0.iloc[train_idx], y_0.iloc[val_idx]

                                    cloned_model = clone(model)
                                    if reverse_predictions:
                                        cloned_model.fit(X_train, 1 - y_train)
                                        y_prob = 1 - cloned_model.predict_proba(X_val)[:, 1]
                                    else:
                                        cloned_model.fit(X_train, y_train)
                                        y_prob = cloned_model.predict_proba(X_val)[:, 1]

                                    scores.append(roc_auc_score(y_val, y_prob))
                                score = np.mean(scores)
                                print(f"{model_name}: AUC = {score:.4f}")

                            if score > best_score:
                                best_score = score
                                best_name = model_name
                                best_model = clone(models[model_name])

                        if best_model is not None:
                            print(f"最佳模型: {best_name}, AUC = {best_score:.4f}")

                            # 使用全部consistency=0的数据训练最终模型
                            if reverse_predictions:
                                best_model.fit(X_0, 1 - y_0)
                            else:
                                best_model.fit(X_0, y_0)

                            experts.append({
                                'hospital': hospital,
                                'consistency_target': 0,
                                'model': best_model,
                                'model_name': best_name,
                                'performance': best_score,
                                'reverse_predictions': reverse_predictions and consistency_hospital == 0,
                                'sample_count': len(X_0)
                            })
                        else:
                            print("未找到合适的模型")
                except Exception as e:
                    print(f"训练专家模型时出错: {str(e)}")
            else:
                print("consistency=0样本不足，跳过此专家")

            # 针对consistency=1样本训练专家模型
            if np.sum(consistency_hospital == 1) >= 10:
                try:
                    print("\n训练针对consistency=1的专家模型...")
                    mask_1 = consistency_hospital == 1
                    X_1 = X_hospital[mask_1]
                    y_1 = y_hospital.iloc[mask_1]

                    if len(np.unique(y_1)) < 2:
                        print("警告: 类别不足，无法训练模型")
                    else:
                        # 选择最佳模型
                        best_model = None
                        best_score = 0
                        best_name = None

                        for model_name, model in models.items():
                            if len(y_1) < 30:
                                # 对于小样本，简单评估
                                cloned_model = clone(model)
                                cloned_model.fit(X_1, y_1)
                                score = cross_val_score(cloned_model, X_1, y_1, cv=3, scoring='roc_auc').mean()
                                print(f"{model_name}: AUC = {score:.4f}")
                            else:
                                # 对于较大样本，使用交叉验证
                                cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
                                scores = []
                                for train_idx, val_idx in cv.split(X_1, y_1):
                                    X_train, X_val = X_1[train_idx], X_1[val_idx]
                                    y_train, y_val = y_1.iloc[train_idx], y_1.iloc[val_idx]

                                    cloned_model = clone(model)
                                    cloned_model.fit(X_train, y_train)
                                    y_prob = cloned_model.predict_proba(X_val)[:, 1]
                                    scores.append(roc_auc_score(y_val, y_prob))
                                score = np.mean(scores)
                                print(f"{model_name}: AUC = {score:.4f}")

                            if score > best_score:
                                best_score = score
                                best_name = model_name
                                best_model = clone(models[model_name])

                        if best_model is not None:
                            print(f"最佳模型: {best_name}, AUC = {best_score:.4f}")

                            # 使用全部consistency=1的数据训练最终模型
                            best_model.fit(X_1, y_1)

                            experts.append({
                                'hospital': hospital,
                                'consistency_target': 1,
                                'model': best_model,
                                'model_name': best_name,
                                'performance': best_score,
                                'reverse_predictions': False,
                                'sample_count': len(X_1)
                            })
                        else:
                            print("未找到合适的模型")
                except Exception as e:
                    print(f"训练专家模型时出错: {str(e)}")
            else:
                print("consistency=1样本不足，跳过此专家")

        print(f"\n成功训练的专家模型数量: {len(experts)}")
        for i, expert in enumerate(experts):
            print(f"{i + 1}. 医院: {expert['hospital']}, 目标consistency: {expert['consistency_target']}, "
                  f"模型: {expert['model_name']}, AUC: {expert['performance']:.4f}, "
                  f"样本数: {expert['sample_count']}")

        print("\n=== 步骤2: 训练潜在路由模型 ===")

        # 我们需要一个路由模型来预测样本应该由哪个专家处理
        # 这个模型在训练时使用consistency作为监督信号，但推理时不依赖它

        if len(experts) < 2:
            print("专家模型数量不足，无法构建MOE系统")
            return None

        print("\n训练隐式路由器以捕捉样本与专家的最佳匹配关系")
        print("这个路由器训练时使用consistency信息，但在推理时不依赖它")

        # 初始化评估指标
        moe_metrics = {
            'accuracy': [], 'precision': [], 'recall': [],
            'f1': [], 'auc': [], 'spe': []
        }

        # 使用5折交叉验证评估MOE模型
        moe_cv = CustomStratifiedKFold(n_splits=5, shuffle=True, random_state=42)

        # 保存每折的路由模型用于分析
        fold_routers = []

        for fold, (train_idx, test_idx) in enumerate(
                moe_cv.split(X_selected_scaled, y, hospitals, clinic_diabetes, clinic_symp, clinic_age), 1):
            print(f"\n--- 潜在路由MOE评估 Fold {fold}/5 ---")

            # 划分数据
            X_train = X_selected_scaled[train_idx]
            y_train = y.iloc[train_idx]
            hospitals_train = hospitals[train_idx]
            consistency_train = consistency_values[train_idx]

            X_test = X_selected_scaled[test_idx]
            y_test = y.iloc[test_idx]
            hospitals_test = hospitals[test_idx]
            consistency_test = consistency_values[test_idx]

            print(f"训练集: {len(X_train)}样本, 测试集: {len(X_test)}样本")

            # 步骤2.1: 为每个样本找到最佳专家
            print("\n为训练样本标记最佳专家...")

            # 创建标签：对于每个样本，标记哪个专家最适合它
            # 这里使用启发式方法：优先选择样本所属医院和consistency匹配的专家
            best_expert_idx = np.zeros(len(X_train), dtype=int)

            for i, (hospital, consistency) in enumerate(zip(hospitals_train, consistency_train)):
                # 找出匹配该样本医院和consistency的所有专家
                matching_experts = [j for j, e in enumerate(experts)
                                    if e['hospital'] == hospital and e['consistency_target'] == consistency]

                if matching_experts:
                    # 如果有完全匹配的专家，选择性能最好的一个
                    best_match = max(matching_experts, key=lambda j: experts[j]['performance'])
                    best_expert_idx[i] = best_match
                else:
                    # 如果没有完全匹配的专家，优先匹配consistency，其次匹配医院
                    consistency_matching = [j for j, e in enumerate(experts)
                                            if e['consistency_target'] == consistency]

                    if consistency_matching:
                        best_match = max(consistency_matching, key=lambda j: experts[j]['performance'])
                        best_expert_idx[i] = best_match
                    else:
                        hospital_matching = [j for j, e in enumerate(experts)
                                             if e['hospital'] == hospital]

                        if hospital_matching:
                            best_match = max(hospital_matching, key=lambda j: experts[j]['performance'])
                            best_expert_idx[i] = best_match
                        else:
                            # 如果没有任何匹配，选择整体性能最好的专家
                            best_match = max(range(len(experts)), key=lambda j: experts[j]['performance'])
                            best_expert_idx[i] = best_match

            # 统计最佳专家分配情况
            expert_counts = np.bincount(best_expert_idx, minlength=len(experts))
            print("专家分配情况:")
            for i, count in enumerate(expert_counts):
                if count > 0:
                    expert = experts[i]
                    print(
                        f"专家 {i} (医院: {expert['hospital']}, 一致性: {expert['consistency_target']}): {count}个样本")

            # 步骤2.2: 训练路由模型
            print("\n训练特征驱动的路由模型...")

            # 使用RandomForest作为路由器，它可以学习复杂的特征关系
            router = RandomForestClassifier(n_estimators=100, random_state=42)
            router.fit(X_train, best_expert_idx)

            # 保存路由模型用于分析
            fold_routers.append(router)

            # 查看路由模型的特征重要性
            if hasattr(router, 'feature_importances_'):
                top_features = np.argsort(router.feature_importances_)[-10:][::-1]
                print("\n路由模型的Top10重要特征:")
                for i, feat_idx in enumerate(top_features):
                    if feat_idx < len(selected_features):
                        feat_name = selected_features[feat_idx]
                        print(f"{i + 1}. {feat_name}: {router.feature_importances_[feat_idx]:.4f}")

            # 步骤2.3: 使用路由模型和专家进行预测
            print("\n结合路由模型和专家模型进行预测...")

            # 获取路由模型的预测 - 哪个专家最适合处理每个测试样本
            expert_probs = router.predict_proba(X_test)

            # 获取每个专家对每个测试样本的预测
            all_expert_preds = np.zeros((len(X_test), len(experts)))

            for i, expert in enumerate(experts):
                try:
                    # 获取专家模型预测
                    probs = expert['model'].predict_proba(X_test)[:, 1]

                    # 如果是需要反转预测的专家
                    if expert['reverse_predictions']:
                        probs = 1 - probs

                    all_expert_preds[:, i] = probs
                except Exception as e:
                    print(f"专家 {i} 预测失败: {str(e)}")
                    all_expert_preds[:, i] = 0.5  # 填充默认值

            # 计算最终预测 - 专家预测的加权平均
            # 使用路由模型的预测概率作为权重
            y_prob_final = np.sum(all_expert_preds * expert_probs, axis=1)
            y_pred = (y_prob_final > 0.5).astype(int)

            # 计算评估指标
            metrics = {
                'accuracy': accuracy_score(y_test, y_pred),
                'precision': precision_score(y_test, y_pred),
                'recall': recall_score(y_test, y_pred),
                'spe': recall_score(y_test, y_pred, pos_label=0),
                'f1': f1_score(y_test, y_pred),
                'auc': roc_auc_score(y_test, y_prob_final)
            }

            # 存储指标
            for metric, value in metrics.items():
                moe_metrics[metric].append(value)

            # 打印当前折的结果
            print("\n当前折MOE结果:")
            for metric, value in metrics.items():
                print(f"{metric}: {value:.4f}")

            # 分析路由结果
            print("\n路由模型分析:")

            # 查看路由模型对测试样本的分配
            expert_assignment = np.argmax(expert_probs, axis=1)
            expert_assignment_counts = np.bincount(expert_assignment, minlength=len(experts))

            print("测试样本的专家分配情况:")
            for i, count in enumerate(expert_assignment_counts):
                if count > 0 and i < len(experts):
                    expert = experts[i]
                    expert_test_samples = X_test[expert_assignment == i]
                    print(f"专家 {i} (医院: {expert['hospital']}, 目标一致性: {expert['consistency_target']}): "
                          f"{count}个样本 ({count / len(X_test) * 100:.1f}%)")

                    # 检查这些被分配给专家的样本中有多少实际上是consistency匹配的
                    if np.sum(expert_assignment == i) > 0:
                        consistency_match_ratio = np.mean(
                            consistency_test[expert_assignment == i] == expert['consistency_target'])
                        print(f"  - 被分配样本中consistency匹配率: {consistency_match_ratio:.2f}")

            # 按consistency分组评估
            print("\n按consistency分组的性能:")
            for c_value in [0, 1]:
                c_mask = consistency_test == c_value
                if np.sum(c_mask) > 10 and len(np.unique(y_test[c_mask])) > 1:
                    c_auc = roc_auc_score(y_test[c_mask], y_prob_final[c_mask])
                    c_acc = accuracy_score(y_test[c_mask], y_pred[c_mask])
                    print(f"consistency={c_value}: 样本数={np.sum(c_mask)}, AUC={c_auc:.4f}, ACC={c_acc:.4f}")

            # 按医院分组评估
            print("\n按医院分组的性能:")
            for hospital in unique_hospitals:
                hospital_mask = hospitals_test == hospital
                if np.sum(hospital_mask) > 10 and len(np.unique(y_test[hospital_mask])) > 1:
                    hospital_auc = roc_auc_score(y_test[hospital_mask], y_prob_final[hospital_mask])
                    hospital_acc = accuracy_score(y_test[hospital_mask], y_pred[hospital_mask])
                    print(f"{hospital}: 样本数={np.sum(hospital_mask)}, AUC={hospital_auc:.4f}, ACC={hospital_acc:.4f}")

        # 计算平均指标
        moe_mean_metrics = {
            metric: {
                'mean': np.mean(values),
                'std': np.std(values)
            }
            for metric, values in moe_metrics.items()
        }

        # 打印最终MOE结果
        print("\n" + "=" * 60)
        print(f"{'潜在路由MOE最终结果':^60}")
        print("=" * 60)

        if metrics_display is None:
            metrics_display = {
                'auc': 'AUC',
                'accuracy': 'ACC',
                'recall': 'SEN',
                'spe': 'SPE',
                'f1': 'F1',
                'precision': 'PPV',
            }

        for metric_name, display_name in metrics_display.items():
            if metric_name in moe_mean_metrics:
                value = moe_mean_metrics[metric_name]
                print(f"{display_name}: {value['mean']:.4f} (±{value['std']:.4f})")

        # 分析路由器
        if fold_routers:
            all_importances = np.zeros((len(fold_routers), len(selected_features)))

            for i, router in enumerate(fold_routers):
                if hasattr(router, 'feature_importances_'):
                    all_importances[i, :] = router.feature_importances_[:len(selected_features)]

            mean_importances = np.mean(all_importances, axis=0)
            top_features = np.argsort(mean_importances)[-10:][::-1]

            print("\n路由器的平均Top10重要特征:")
            for i, feat_idx in enumerate(top_features):
                if feat_idx < len(selected_features):
                    feat_name = selected_features[feat_idx]
                    print(f"{i + 1}. {feat_name}: {mean_importances[feat_idx]:.4f}")

        # 构建MOE结果
        moe_results = {
            'metrics': moe_mean_metrics,
            'method': 'latent_routing_moe',
            'experts': experts,
            'successful_experts': len(experts),
            'consistency_distribution': {
                0: consistency_0_count,
                1: consistency_1_count
            },
            'reverse_predictions_used': reverse_predictions,
            'router_feature_importance': mean_importances if fold_routers else None
        }

        print("\n=== 潜在路由MOE评估完成 ===")
        print("注意: 此MOE模型在推理时不依赖consistency，完全基于其他特征进行路由")

        return moe_results

    def ensure_features_selected(self, version=None):
        if version is None:
            # 使用当前已加载的特征
            if self.X_selected_scaled is not None and self.selected_features is not None and self.y is not None:
                print("使用已加载的特征")
                X_selected_scaled = self.X_selected_scaled
                selected_features = self.selected_features
                y = self.y
            else:
                # 如果没有加载的特征，则重新生成并保存为临时版本
                print("重新生成特征")
                X_selected_scaled, selected_features, y = self.select_features_binary()
                self.X_selected_scaled = X_selected_scaled
                self.selected_features = selected_features
                self.y = y
                self._save_features("temp")
        elif version == "temp":
            # 强制重新生成特征
            print("重新生成特征")
            X_selected_scaled, selected_features, y = self.select_features_binary()
            self.X_selected_scaled = X_selected_scaled
            self.selected_features = selected_features
            self.y = y
            self._save_features(version)
        else:
            # 尝试加载指定版本的特征(可能是单个版本或多个版本列表)
            if not self._load_features(version, ):
                print("重新生成特征")
                # 如果加载失败，重新生成并保存
                X_selected_scaled, selected_features, y = self.select_features_binary()
                self.X_selected_scaled = X_selected_scaled
                self.selected_features = selected_features
                self.y = y
                # 如果是列表，保存为temp版本
                save_version = "temp" if isinstance(version, list) else version
                self._save_features(save_version)
            else:
                if isinstance(version, list):
                    print(f"使用合并版本 {', '.join(version)} 的特征")
                else:
                    print(f"使用版本 {version} 的特征")
                X_selected_scaled = self.X_selected_scaled
                selected_features = self.selected_features
                y = self.y
        return X_selected_scaled, selected_features, y

    def evaluate_models_binary(self, version=None, by_center=0):
        """
        评估多个模型的性能，将数据折划分放在外层循环
        Args:
            version: 特征版本标识。如果为None,使用已加载的特征；
                    如果为"temp",重新生成特征；
                    其他值则尝试加载对应版本的特征
        Returns:
            dict: 包含评估结果的字典
        """
        from sklearn.base import clone
        X_selected_scaled, selected_features, y = self.ensure_features_selected(version)

        # 特征分析 =================================
        # self = analysis
        # from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler, PowerTransformer
        # from feature_analysis import analyze_features_by_class, explore_and_normalize_features,compare_normalization_methods
        # X_selected_scaled = self.features_df[self.feature_names][selected_features].fillna(0)
        # # 对其进行Robust缩放
        # # X_selected_scaled = PowerTransformer(method='yeo-johnson').fit_transform(X_selected_scaled)
        # normalized_data = explore_and_normalize_features(X_selected_scaled, y=y, batch_size=40)
        # # 比较不同归一化方法在特定特征上的效果（带标签）
        # compare_normalization_methods(X_selected_scaled,feature_names=["T_ch27_area_pos"], y=y, n_features=5)
        # # 专门针对分类任务的特征分析
        # feature_importance = analyze_features_by_class(X_selected_scaled, y, top_n=15)
        #
        # explore_and_normalize_analysis(X_selected_scaled,batch_size=20) # 来自于grok
        # 创建所有模型
        # 如果by_center =True,则 models的include_stacking参数为False
        # ================================================================

        # 测试： 剔除临床特征的尝试--------------
        # 获取不包含'clinic'的特征的索引
        # clinic_indices = [i for i, feature in enumerate(selected_features) if 'clinic' in feature.lower()]
        # # 根据索引筛选特征值矩阵
        # X_selected_scaled = X_selected_scaled[:, clinic_indices]
        # # 筛选特征名列表
        # selected_features = [f for f in self.selected_features if 'clinic' in f.lower()]
        # -----------------------------------

        models = ModelFactory.create_all_models(
            random_state=42,
            include_stacking=True  # not by_center  # not by_center #
        )

        # 存储每个模型的结果
        results = {model_name: {'fold_metrics': {
            'accuracy': [], 'precision': [], 'recall': [], 'f1': [], 'auc': [], 'spe': []
        }, 'fold_results': [], 'hospital_metrics': {}} for model_name in models.keys()}

        best_model = None
        best_auc = 0
        best_tree_model = None
        best_tree_auc = 0

        # 创建增强器实例
        augmentor = StratifiedBalancedDataAugmentor(
            balance_method='smote',
            noise_method='random_perturbation',
            verbose=True
        )
        # 初始化动态集成选择器
        # des_selector = DynamicEnsembleSelector(
        #     k_neighbors=7,
        #     min_ensemble_size=3,
        #     max_ensemble_size=5
        # )
        hospitals = self.features_df['clinic_hospital']
        clinic_diabetes = self.features_df['clinic_diabetes']
        clinic_symp = self.features_df['clinic_symp']
        clinic_age = self.features_df['clinic_age']
        clinic_gender = self.features_df['clinic_gender']
        clinic_hypertension = self.features_df['clinic_hypertension']
        clinic_hyperlipidemia = self.features_df['clinic_hyperlipidemia']
        clinic_intervention = self.features_df['clinic_intervention']

        consistensy = self.data['标注一致性']

        # 仅使用局部数据建模时。y的索引会发生变化，需要重新设置索引
        y.index = range(len(y))
        # 添加医院级别的评估指标
        for model_name in results:
            unique_hospitals = pd.Series(hospitals).unique()
            results[model_name]['hospital_metrics'] = {
                hospital: {'accuracy': [], 'auc': []} for hospital in unique_hospitals
            }

            # 获取所有中心
        unique_hospitals = pd.Series(hospitals).unique()

        if by_center == 1:
            # <editor-fold desc="分中心建模">
            print("\n=== 开始分中心建模评估 ===")
            # 初始化分中心结果字典
            center_results = {
                hospital: {
                    'success': False,  # 标记该中心是否成功建模
                    'error': None,  # 记录失败原因
                    'results': None  # 存储建模结果
                } for hospital in unique_hospitals
            }

            # 对每个中心分别建模
            for hospital in unique_hospitals:
                try:
                    print(f"\n{'=' * 20} 建模中心: {hospital} {'=' * 20}")

                    # 筛选该中心数据
                    hospital_mask = hospitals == hospital
                    X_center = X_selected_scaled[hospital_mask]
                    y_center = y[hospital_mask]

                    # 检查数据是否满足建模条件
                    if len(np.unique(y_center)) < 2:
                        raise ValueError(f"中心 {hospital} 的数据类别数少于2，无法建模")

                    if len(y_center) < 20:  # 设置最小样本量阈值
                        raise ValueError(f"中心 {hospital} 的样本量过少({len(y_center)})，无法可靠建模")

                    print(f"样本数量: {len(y_center)}")
                    print("类别分布:")
                    print(pd.Series(y_center).value_counts())

                    # 初始化该中心的结果字典
                    results = {
                        model_name: {
                            'fold_metrics': {
                                'accuracy': [], 'precision': [], 'recall': [],
                                'f1': [], 'auc': [], 'spe': []
                            },
                            'fold_results': []
                        } for model_name in models.keys()
                    }

                    # 使用StratifiedKFold进行交叉验证
                    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

                    # 进行交叉验证
                    for fold, (train_idx, test_idx) in enumerate(skf.split(X_center, y_center), 1):
                        print(f"\n--- Fold {fold}/5 ---")

                        # 划分训练集和测试集
                        X_train, X_test = X_center[train_idx], X_center[test_idx]
                        y_train, y_test = y_center.iloc[train_idx], y_center.iloc[test_idx]

                        # 数据增强（单中心版本）
                        X_train_processed, y_train_processed = augmentor.balance_by_center(
                            X_train, y_train,
                            selected_features=selected_features,
                            center_col='clinic_hospital',
                            target_ratio=0.8,
                            noise_ratio=0.05
                        )

                        # 存储当前折的所有模型预测结果
                        fold_predictions = {}

                        # 内层循环：对每个模型进行评估
                        for model_name, model in models.items():
                            print(f"\n--- 评估 {model_name} ---")
                            try:
                                # 训练模型
                                if isinstance(model, (xgb.XGBClassifier, CatBoostClassifier)):
                                    eval_set = [(X_test, y_test.values)]
                                    model.fit(X_train_processed, y_train_processed,
                                              eval_set=eval_set,
                                              verbose=False)
                                else:
                                    model.fit(X_train_processed, y_train_processed)

                                # 获取预测概率
                                y_prob = model.predict_proba(X_test)
                                fold_predictions[model_name] = y_prob

                                y_prob_final = y_prob[:, 1]
                                # 基于概率进行预测
                                y_pred = (y_prob_final > 0.5).astype(int)

                                # 计算评估指标
                                metrics = {
                                    'accuracy': accuracy_score(y_test, y_pred),
                                    'precision': precision_score(y_test, y_pred),
                                    'recall': recall_score(y_test, y_pred),
                                    'spe': recall_score(y_test, y_pred, pos_label=0),
                                    'f1': f1_score(y_test, y_pred),
                                    'auc': roc_auc_score(y_test, y_prob_final)
                                }

                                # 存储指标
                                for metric, value in metrics.items():
                                    results[model_name]['fold_metrics'][metric].append(value)

                                # 计算并存储ROC曲线数据
                                fpr, tpr, _ = roc_curve(y_test, y_prob_final)
                                results[model_name]['fold_results'].append((fpr, tpr, metrics['auc']))

                                # 打印当前折的结果
                                for metric, value in metrics.items():
                                    print(f"{metric}: {value:.4f}")

                            except Exception as e:
                                print(f"警告：{model_name} 在第 {fold} 折出现错误: {str(e)}")
                                continue

                        # <editor-fold desc="准备结果">
                        # 计算每个模型的平均指标
                    for model_name in models.keys():
                        metrics = results[model_name]['fold_metrics']
                        mean_metrics = {
                            metric: {
                                'mean': np.mean(values),
                                'std': np.std(values)
                            }
                            for metric, values in metrics.items()
                        }
                        results[model_name]['metrics'] = mean_metrics

                        # 更新最佳模型
                        current_auc = mean_metrics['auc']['mean']
                        if current_auc > best_auc:
                            best_auc = current_auc
                            best_model = (model_name, models[model_name])

                        # 更新最佳树模型
                        if isinstance(models[model_name], (xgb.XGBClassifier, RandomForestClassifier)):
                            if current_auc > best_tree_auc:
                                best_tree_auc = current_auc
                                best_tree_model = (model_name, models[model_name])

                        # 打印最终评估结果
                    print("\n" + "=" * 60)
                    print(f"{'最终评估结果':^60}")
                    print("=" * 60)
                    print(f"\n最佳模型: {best_model[0]}")
                    print(f"最佳AUC: {best_auc:.4f}")

                    # 定义要展示的指标及其显示名称
                    metrics_display = {
                        'auc': 'AUC',
                        'accuracy': 'ACC',
                        'recall': 'SEN',  # Sensitivity
                        'spe': 'SPE',
                        'f1': 'F1',
                        'precision': 'PPV',
                    }

                    # 打印表头
                    print("\n模型性能比较:")
                    print("-" * 100)
                    header = "Model Name".ljust(30) + "".join(f"{name:>12}" for name in metrics_display.values())
                    print(header)
                    print("-" * 100)

                    # 按AUC降序排序并打印所有指标
                    sorted_models = sorted(
                        results.items(),
                        key=lambda x: x[1]['metrics']['auc']['mean'],
                        reverse=True
                    )

                    for model_name, model_results in sorted_models:
                        # 截断过长的模型名称
                        truncated_name = (model_name[:27] + '...') if len(model_name) > 30 else model_name
                        line = truncated_name.ljust(30)

                        # 添加每个指标
                        for metric_key in metrics_display.keys():
                            metric = model_results['metrics'][metric_key]
                            mean = metric['mean']
                            std = metric['std']
                            # 格式化为"mean±std"的形式
                            metric_str = f"{mean:.3f}±{std:.3f}"
                            line += f"{metric_str:>12}"

                        print(line)

                    print("-" * 100)

                    # 如果存在best_tree_model，打印其信息
                    if best_tree_model:
                        print(f"\n最佳树模型: {best_tree_model[0]}")
                        metrics = results[best_tree_model[0]]['metrics']
                        for metric_name, display_name in metrics_display.items():
                            if metric_name in metrics:
                                value = metrics[metric_name]
                                print(f"{display_name}: {value['mean']:.4f} (±{value['std']:.4f})")
                    # 计算特征重要性
                    feature_importance = None
                    if best_tree_model and hasattr(best_tree_model[1], 'feature_importances_'):
                        feature_importance = pd.DataFrame({
                            'feature': selected_features,
                            'importance': best_tree_model[1].feature_importances_
                        }).sort_values('importance', ascending=False)
                        print("\n特征重要性（前20个）:")
                        print(feature_importance.head(20))
                    # SHAP分析
                    shap_values = None
                    if best_tree_model:
                        print(f"\n使用最佳树模型 {best_tree_model[0]} 计算SHAP值")
                        explainer = shap.TreeExplainer(best_tree_model[1])
                        shap_values = explainer.shap_values(X_selected_scaled)
                    else:
                        print("\n注意：没有找到合适的树模型来计算SHAP值")

                    # PCA降维
                    pca = PCA(n_components=2)
                    features_2d = pca.fit_transform(X_selected_scaled)
                    hospital_final_result = {
                        'best_model_name': best_model[0],
                        'best_model': best_model[1],
                        'best_auc': best_auc,
                        'best_tree_model_name': best_tree_model[0] if best_tree_model else None,
                        'best_tree_model': best_tree_model[1] if best_tree_model else None,
                        'all_results': results,
                        'fold_results': results[best_model[0]]['fold_results'],
                        'shap_values': shap_values,
                        'shap_data': X_selected_scaled,
                        'selected_features': selected_features,
                        'pca_results': {
                            'transformed': features_2d,
                            'explained_variance_ratio': pca.explained_variance_ratio_
                        },
                        'y': y
                    }
                    # </editor-fold>

                    center_results[hospital]['success'] = True
                    center_results[hospital]['results'] = hospital_final_result

                except Exception as e:
                    print(f"警告：中心 {hospital} 建模失败: {str(e)}")
                    center_results[hospital]['error'] = str(e)
                    continue

            from sklearn.linear_model import LogisticRegression
            from sklearn.base import clone
            print("\n=== 开始中心特异性Stacking集成评估 ===")
            print("每个中心模型将专注于其自身数据，并在集成时给予本中心样本更高权重")

            # 收集每个中心的最优模型
            center_best_models = {}
            for hospital, result in center_results.items():
                if result['success']:
                    center_best_models[hospital] = result['results']['best_model']
            print(f"成功收集 {len(center_best_models)} 个中心的模型")

            if len(center_best_models) > 1:  # 确保至少有两个模型可以集成
                try:
                    # 初始化评估指标
                    ensemble_metrics = {
                        'accuracy': [], 'precision': [], 'recall': [],
                        'f1': [], 'auc': [], 'spe': []
                    }

                    # 初始化元模型 (可以根据需要调整)
                    meta_model = LogisticRegression(random_state=42)

                    # 使用5折交叉验证评估stacking效果
                    ensemble_cv = CustomStratifiedKFold(n_splits=5, shuffle=True, random_state=42)

                    print("\n开始交叉验证评估中心特异性Stacking...")
                    for fold, (train_idx, test_idx) in enumerate(
                            ensemble_cv.split(X_selected_scaled, y, hospitals, clinic_diabetes, clinic_symp,
                                              clinic_age), 1):
                        print(f"\n--- Stacking评估 Fold {fold}/5 ---")

                        # 划分训练和测试数据
                        X_train = X_selected_scaled[train_idx]
                        y_train = y.iloc[train_idx]
                        hospitals_train = hospitals[train_idx]
                        X_test = X_selected_scaled[test_idx]
                        y_test = y.iloc[test_idx]
                        hospitals_test = hospitals[test_idx]

                        print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")

                        # 创建训练集的元特征矩阵
                        meta_features_train = np.zeros((len(X_train), len(center_best_models)))
                        # 创建测试集的元特征矩阵
                        meta_features_test = np.zeros((len(X_test), len(center_best_models)))

                        # 添加中心权重特征（指示样本是否来自该中心）
                        center_weights_train = np.zeros((len(X_train), len(center_best_models)))
                        center_weights_test = np.zeros((len(X_test), len(center_best_models)))

                        # 为每个中心的模型生成预测
                        for i, (hospital, model) in enumerate(center_best_models.items()):
                            print(f"\n处理中心 {hospital} 的模型...")

                            # 找出训练集中属于该中心的样本
                            hospital_mask_train = hospitals_train == hospital
                            hospital_X_train = X_train[hospital_mask_train]
                            hospital_y_train = y_train[hospital_mask_train]

                            # 检查该中心在训练集中是否有足够样本
                            if len(hospital_X_train) < 10:
                                print(
                                    f"警告: 中心 {hospital} 在当前训练折中样本过少 ({len(hospital_X_train)}), 使用全部训练集")
                                hospital_X_train = X_train
                                hospital_y_train = y_train
                            else:
                                print(f"中心 {hospital} 在训练集中有 {len(hospital_X_train)} 个样本")

                            try:
                                # 克隆模型
                                cloned_model = clone(model)

                                # 训练模型 - 主要使用自己中心的数据
                                print(f"使用 {len(hospital_X_train)} 个样本训练中心 {hospital} 的模型")
                                cloned_model.fit(hospital_X_train, hospital_y_train)

                                # 对整个训练集进行预测
                                probs_train = cloned_model.predict_proba(X_train)[:, 1]
                                meta_features_train[:, i] = probs_train

                                # 设置中心权重 - 对本中心样本权重更高
                                center_weights_train[:, i] = np.where(hospitals_train == hospital, 3.0, 0.5)

                                # 对测试集进行预测
                                probs_test = cloned_model.predict_proba(X_test)[:, 1]
                                meta_features_test[:, i] = probs_test

                                # 设置测试集中心权重
                                center_weights_test[:, i] = np.where(hospitals_test == hospital, 3.0, 0.5)

                                print(f"中心 {hospital} 模型预测成功")
                                # 打印一些统计信息
                                hospital_mask_test = hospitals_test == hospital
                                if np.any(hospital_mask_test):
                                    test_auc = roc_auc_score(y_test[hospital_mask_test],
                                                             probs_test[hospital_mask_test])
                                    print(f"中心 {hospital} 模型在自身测试样本上的AUC: {test_auc:.4f}")

                                overall_auc = roc_auc_score(y_test, probs_test)
                                print(f"中心 {hospital} 模型在所有测试样本上的AUC: {overall_auc:.4f}")

                            except Exception as e:
                                print(f"中心 {hospital} 模型处理失败: {str(e)}")
                                # 如果模型失败，填充0作为预测值
                                meta_features_train[:, i] = 0
                                meta_features_test[:, i] = 0

                        # 合并预测特征和中心权重特征
                        print("\n合并预测特征和中心权重特征...")
                        combined_meta_features_train = np.hstack([
                            meta_features_train,
                            center_weights_train,
                            meta_features_train * center_weights_train  # 交互特征
                        ])

                        combined_meta_features_test = np.hstack([
                            meta_features_test,
                            center_weights_test,
                            meta_features_test * center_weights_test  # 交互特征
                        ])

                        print(
                            f"元特征维度 - 训练集: {combined_meta_features_train.shape}, 测试集: {combined_meta_features_test.shape}")

                        # 训练元模型
                        print("训练元模型...")
                        meta_model.fit(combined_meta_features_train, y_train)

                        # 检查元模型系数
                        if hasattr(meta_model, 'coef_'):
                            n_models = len(center_best_models)
                            print("\n元模型系数分析:")
                            for i, (hospital, _) in enumerate(center_best_models.items()):
                                pred_coef = meta_model.coef_[0][i]
                                weight_coef = meta_model.coef_[0][i + n_models]
                                interact_coef = meta_model.coef_[0][i + 2 * n_models]
                                print(
                                    f"中心 {hospital} - 预测系数: {pred_coef:.4f}, 权重系数: {weight_coef:.4f}, 交互系数: {interact_coef:.4f}")

                        # 使用元模型进行预测
                        print("使用元模型进行预测...")
                        y_prob_final = meta_model.predict_proba(combined_meta_features_test)[:, 1]
                        y_pred = meta_model.predict(combined_meta_features_test)

                        # 计算评估指标
                        metrics = {
                            'accuracy': accuracy_score(y_test, y_pred),
                            'precision': precision_score(y_test, y_pred),
                            'recall': recall_score(y_test, y_pred),
                            'spe': recall_score(y_test, y_pred, pos_label=0),
                            'f1': f1_score(y_test, y_pred),
                            'auc': roc_auc_score(y_test, y_prob_final)
                        }

                        # 存储指标
                        for metric, value in metrics.items():
                            ensemble_metrics[metric].append(value)

                        # 打印当前折的结果
                        print("\n当前折Stacking结果:")
                        for metric, value in metrics.items():
                            print(f"{metric}: {value:.4f}")

                        # 分中心评估结果
                        print("\n各中心测试样本上的性能:")
                        for hospital in center_best_models.keys():
                            center_mask = hospitals_test == hospital
                            if np.any(center_mask) and len(np.unique(y_test[center_mask])) > 1:
                                center_metrics = {
                                    'accuracy': accuracy_score(y_test[center_mask], y_pred[center_mask]),
                                    'auc': roc_auc_score(y_test[center_mask], y_prob_final[center_mask])
                                }
                                print(
                                    f"中心 {hospital} - 样本数: {np.sum(center_mask)}, ACC: {center_metrics['accuracy']:.4f}, AUC: {center_metrics['auc']:.4f}")
                            else:
                                print(f"中心 {hospital} - 测试集中样本不足或没有多类别")

                    # 计算平均指标
                    ensemble_mean_metrics = {
                        metric: {
                            'mean': np.mean(values),
                            'std': np.std(values)
                        }
                        for metric, values in ensemble_metrics.items()
                    }

                    # 打印最终集成结果
                    print("\n" + "=" * 60)
                    print(f"{'中心特异性Stacking集成最终结果':^60}")
                    print("=" * 60)

                    for metric_name, display_name in metrics_display.items():
                        if metric_name in ensemble_mean_metrics:
                            value = ensemble_mean_metrics[metric_name]
                            print(f"{display_name}: {value['mean']:.4f} (±{value['std']:.4f})")

                    # 将集成结果添加到 final_result
                    ensemble_results = {
                        'metrics': ensemble_mean_metrics,
                        'method': 'center_specific_stacking',
                        'meta_model': meta_model,
                        'n_models': len(center_best_models),
                        'center_models': center_best_models
                    }

                    print("\n=== 中心特异性Stacking集成评估完成 ===")

                except Exception as e:
                    print(f"Stacking集成评估过程出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    ensemble_results = {
                        'error': str(e)
                    }
            else:
                print("可用于集成的模型数量不足")
                ensemble_results = {
                    'error': "可用于集成的模型数量不足"
                }

            # 汇总所有中心的结果
            final_result = {
                'mode': 'by_center',
                'center_results': center_results,
                'summary': {
                    'success_centers': sum(1 for h in center_results if center_results[h]['success']),
                    'failed_centers': sum(1 for h in center_results if not center_results[h]['success'])
                },
                'ensemble_results': ensemble_results
            }
            # </editor-fold>
        elif by_center == 2:
            # <editor-fold desc="潜在路由MOE模式">
            print("\n=== 开始潜在路由MOE模式 ===")
            center_results = {
                hospital: {
                    'success': False,  # 标记该中心是否成功建模
                    'error': None,  # 记录失败原因
                    'results': None  # 存储建模结果
                } for hospital in unique_hospitals
            }
            metrics_display = {
                'auc': 'AUC',
                'accuracy': 'ACC',
                'recall': 'SEN',  # Sensitivity
                'spe': 'SPE',
                'f1': 'F1',
                'precision': 'PPV',
            }
            # 获取consistency值
            if 'consistency' in selected_features:
                # 如果consistency已经在特征中，提取出来
                consistency_idx = selected_features.index('consistency')
                consistency_values = X_selected_scaled[:, consistency_idx].astype(int)
                print(f"从特征矩阵中提取consistency (索引: {consistency_idx})")
            else:
                # 如果需要从其他地方获取consistency，请相应调整
                print("注意: 未找到consistency特征，请确保已正确提供")
                consistency_values = np.array(consistensy)  # np.zeros(len(y), dtype=int)  # 占位，实际使用时替换

            # 调用MOE函数
            moe_results = self.model_with_latent_moe(
                X_selected_scaled, y, hospitals, clinic_diabetes, clinic_symp, clinic_age,
                selected_features, consistency_values, models, metrics_display
            )

            # 将MOE结果添加到final_result
            final_result = {
                'mode': 'latent_routing_moe',
                'center_results': center_results,  # 保留原有中心结果
                'moe_results': moe_results,
                'summary': {
                    'success_centers': sum(1 for h in center_results if center_results[h]['success']),
                    'failed_centers': sum(1 for h in center_results if not center_results[h]['success']),
                    'successful_experts': moe_results['successful_experts'] if moe_results else 0,
                    'consistency_reverse_prediction': moe_results['reverse_predictions_used'] if moe_results else False
                }
            }
            # </editor-fold>
        elif by_center == 3:
            # 测试方法； 双层建模
            # 双层模型建模逻辑
            print("\n=== 开始双层模型建模评估 ===")
            print(f"特征数量: {X_selected_scaled.shape[1]}")
            print(f"样本数量: {X_selected_scaled.shape[0]}")
            print("类别分布:")
            print(pd.Series(y).value_counts(normalize=True))
            print("\n")

            # 初始化结果存储
            results = {model_name: {
                'fold_metrics': {metric: [] for metric in ['accuracy', 'precision', 'recall', 'spe', 'f1', 'auc']},
                'fold_results': [],
                'hospital_metrics': {hospital: {'auc': []} for hospital in pd.Series(hospitals).unique()}
            } for model_name in models.keys()}

            # 添加双层模型的结果存储
            results['TwoLayerModel'] = {
                'fold_metrics': {metric: [] for metric in ['accuracy', 'precision', 'recall', 'spe', 'f1', 'auc']},
                'fold_results': [],
                'hospital_metrics': {hospital: {'auc': []} for hospital in pd.Series(hospitals).unique()}
            }

            # 设置置信度阈值，低于此阈值的样本被认为是低置信度
            LOW_CONFIDENCE_THRESHOLD = 0.6  # 可调整，例如0.6~0.7之间
            best_auc = 0
            best_model = None
            best_tree_auc = 0
            best_tree_model = None

            # 为两层模型都选择XGBoost
            # 第一层模型
            primary_model = xgb.XGBClassifier(
                learning_rate=0.1,
                n_estimators=500,
                max_depth=4,
                min_child_weight=1,
                gamma=0,
                subsample=0.8,
                colsample_bytree=0.8,
                objective='binary:logistic',
                random_state=42
            )
            primary_model_name = "TwoLayerModel_Primary"

            # 第二层模型 - 使用不同参数的XGBoost
            second_model = xgb.XGBClassifier(
                learning_rate=0.05,  # 较小的学习率
                n_estimators=700,  # 更多的树
                max_depth=3,  # 较浅的树
                min_child_weight=2,  # 更保守的分裂
                gamma=0.1,
                subsample=0.9,
                colsample_bytree=0.8,
                objective='binary:logistic',
                random_state=43  # 不同的随机种子
            )
            second_model = SVC(
                kernel='rbf',
                C=1.0,
                probability=True,
                random_state=42
            )
            second_model_name = "TwoLayerModel_Secondary"

            print(f"第一层模型: {primary_model_name}")
            print(f"第二层模型: {second_model_name}")

            # 外层循环：遍历每一折数据
            custom_cv = CustomStratifiedKFold(n_splits=5, shuffle=True, random_state=42)
            for fold, (train_idx, test_idx) in enumerate(
                    custom_cv.split(X_selected_scaled, y, consistensy, hospitals, clinic_hypertension, clinic_gender,
                                    clinic_hyperlipidemia, clinic_intervention, clinic_diabetes, clinic_symp), 1):

                print(f"\n{'=' * 20} Fold {fold}/5 {'=' * 20}")
                print(f"\n训练集索引范围: {min(train_idx)}-{max(train_idx)}")
                print(f"测试集索引范围: {min(test_idx)}-{max(test_idx)}")

                # 确保索引没有重叠
                assert len(set(train_idx) & set(test_idx)) == 0, "训练集和测试集有重叠"

                # 划分训练集和测试集
                X_train, X_test = X_selected_scaled[train_idx], X_selected_scaled[test_idx]
                y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
                hospitals_train = hospitals.iloc[train_idx]
                diabetes_train = clinic_diabetes.iloc[train_idx]
                symp_train = clinic_symp.iloc[train_idx]
                age_train = clinic_age.iloc[train_idx]
                hospitals_test = hospitals.iloc[test_idx]

                # 打印当前折的中心分布情况
                print("\n当前折各中心的样本分布:")
                train_hospital_stats = hospitals_train.value_counts()
                test_hospital_stats = hospitals_test.value_counts()
                print("\n训练集中心分布:")
                for hospital, count in train_hospital_stats.items():
                    print(f"中心 {hospital}: {count} 样本")
                print("\n测试集中心分布:")
                for hospital, count in test_hospital_stats.items():
                    print(f"中心 {hospital}: {count} 样本")

                # 指定要剔除的中心
                exclude_hospitals = []  # 这里设置要剔除的中心列表

                # 剔除指定中心的训练数据
                exclude_mask = ~hospitals_train.isin(exclude_hospitals)
                X_train = X_train[exclude_mask]
                y_train = y_train[exclude_mask]
                hospitals_train = hospitals_train[exclude_mask]

                print(f"\n剔除中心 {exclude_hospitals} 后的训练集分布:")
                for hospital, count in hospitals_train.value_counts().items():
                    print(f"中心 {hospital}: {count} 样本")

                # 创建分层验证集用于评估第一层模型和获取低置信度样本
                from sklearn.model_selection import StratifiedShuffleSplit
                sss = StratifiedShuffleSplit(n_splits=1, test_size=0.1, random_state=42)
                train_final_idx, val_idx = next(sss.split(X_train, y_train))

                # 划分最终训练集和验证集
                X_train_final = X_train[train_final_idx]
                X_val = X_train[val_idx]
                y_train_final = y_train.iloc[train_final_idx]
                y_val = y_train.iloc[val_idx]
                hospitals_train_final = hospitals_train.iloc[train_final_idx]
                hospitals_val = hospitals_train.iloc[val_idx]

                # 打印每折的分布情况
                print("\n当前折的分布情况:")
                print("训练集:")
                for hospital in pd.Series(hospitals_train).unique():
                    mask = hospitals_train == hospital
                    print(f"医院 {hospital}:")
                    print(pd.Series(y_train[mask]).value_counts())

                print("\n测试集:")
                for hospital in pd.Series(hospitals_test).unique():
                    mask = hospitals_test == hospital
                    print(f"医院 {hospital}:")
                    print(pd.Series(y_test[mask]).value_counts())

                # 不应用增强
                X_train_processed, y_train_processed = X_train_final, y_train_final

                # 存储当前折的所有模型预测结果
                fold_predictions = {}

                # 训练第一层模型
                print(f"\n--- 训练第一层模型 ---")
                eval_set = [(X_val, y_val.values)]
                primary_model.fit(X_train_processed, y_train_processed,
                                  eval_set=eval_set,
                                  verbose=False)

                # 收集低置信度样本（通过内部交叉验证）
                print("\n--- 收集低置信度样本 ---")
                low_confidence_X = []
                low_confidence_y = []

                # 设置内部5折交叉验证，找出低置信度样本n_splits=1, test_size=0.1, random_state=42
                inner_cv = StratifiedShuffleSplit(n_splits=5, test_size=0.2, random_state=43)

                # 对训练集进行内部交叉验证，收集所有低置信度样本
                for inner_fold, (inner_train_idx, inner_test_idx) in enumerate(
                        inner_cv.split(X_train, y_train), 1):

                    print(f"\n内部交叉验证 - 折 {inner_fold}/5")

                    # 划分内部训练集和测试集
                    X_inner_train = X_train[inner_train_idx]
                    X_inner_test = X_train[inner_test_idx]
                    y_inner_train = y_train.iloc[inner_train_idx]
                    y_inner_test = y_train.iloc[inner_test_idx]

                    # 训练内部模型
                    inner_model = clone(primary_model)
                    inner_eval_set = [(X_inner_test, y_inner_test.values)]
                    inner_model.fit(X_inner_train, y_inner_train,
                                    eval_set=inner_eval_set,
                                    verbose=False)

                    # 获取内部测试集的预测概率
                    inner_probs = inner_model.predict_proba(X_inner_test)[:, 1]

                    # 识别低置信度样本
                    confidence_mask = (inner_probs >= (0.5 - LOW_CONFIDENCE_THRESHOLD / 2)) & (
                            inner_probs <= (0.5 + LOW_CONFIDENCE_THRESHOLD / 2))

                    inner_low_conf_X = X_inner_test[confidence_mask]
                    inner_low_conf_y = y_inner_test.iloc[confidence_mask]

                    print(f"在内部折 {inner_fold} 中找到 {len(inner_low_conf_X)} 个低置信度样本")

                    # 添加到低置信度样本集合
                    if len(inner_low_conf_X) > 0:
                        low_confidence_X.append(inner_low_conf_X)
                        low_confidence_y.append(inner_low_conf_y)
                from imblearn.over_sampling import SMOTE
                smote = SMOTE()
                # 合并所有低置信度样本
                second_model_trained = False
                if low_confidence_X:
                    X_low_conf = np.vstack(low_confidence_X)
                    y_low_conf = pd.concat(low_confidence_y)
                    # X_low_conf, y_low_conf = smote.fit_resample(X_low_conf, y_low_conf)
                    # print(f"SMOTE后低置信度样本数量: {len(X_low_conf)}")
                    print(
                        f"\n总共收集到 {len(X_low_conf)} 个低置信度样本 ({len(X_low_conf) / len(X_train) * 100:.2f}% 的训练数据)")
                    print(f"低置信度样本类别分布: {y_low_conf.value_counts(normalize=True)}")
                    # 添加第一层模型在低置信度样本上的错误分析
                    low_conf_probs = primary_model.predict_proba(X_low_conf)[:, 1]
                    low_conf_pred = (low_conf_probs > 0.5).astype(int)
                    low_conf_errors = y_low_conf != low_conf_pred
                    print(f"\n第一层模型在低置信度训练样本上的错误率: {np.mean(low_conf_errors):.4f}")
                    print(f"错误样本数量: {np.sum(low_conf_errors)}")
                    print(f"错误样本类别分布: {y_low_conf[low_conf_errors].value_counts(normalize=True)}")
                    # 训练第二层模型
                    print(f"\n--- 训练第二层模型 ---")
                    try:
                        # 如果低置信度样本过少或者类别不平衡，可以考虑应用过采样
                        if len(X_low_conf) > 20 and len(np.unique(y_low_conf)) > 1:  # 确保有足够样本和两个类别
                            low_conf_eval_set = [(X_low_conf, y_low_conf.values)]
                            second_model.fit(X_low_conf, y_low_conf)
                            second_model_trained = True
                            print("第二层模型训练成功")
                        else:
                            print("低置信度样本不足或类别单一，不训练第二层模型")
                    except Exception as e:
                        print(f"训练第二层模型时出错: {str(e)}")
                else:
                    print("没有收集到低置信度样本，不训练第二层模型")

                # 双层模型在测试集上的预测
                print("\n--- 使用双层模型进行测试集预测 ---")

                # 使用第一层模型获取预测概率
                primary_probs = primary_model.predict_proba(X_test)[:, 1]

                # 创建最终预测数组
                final_probs = primary_probs.copy()

                # 识别测试集中的低置信度样本
                test_low_conf_mask = (primary_probs >= (0.5 - LOW_CONFIDENCE_THRESHOLD / 2)) & (
                        primary_probs <= (0.5 + LOW_CONFIDENCE_THRESHOLD / 2))
                low_conf_count = np.sum(test_low_conf_mask)

                print(
                    f"测试集中发现 {low_conf_count} 个低置信度样本 ({low_conf_count / len(X_test) * 100:.2f}% 的测试数据)")

                # 如果有低置信度样本且第二层模型已训练，则使用第二层模型进行预测
                if low_conf_count > 0 and second_model_trained:
                    X_test_low_conf = X_test[test_low_conf_mask]

                    # 使用第二层模型预测低置信度样本
                    second_probs = second_model.predict_proba(X_test_low_conf)[:, 1]

                    # 更新最终预测
                    final_probs[test_low_conf_mask] = second_probs
                    print(f"已用第二层模型更新 {low_conf_count} 个低置信度样本的预测")

                    # 添加低置信度样本的详细分析
                    y_test_low_conf = y_test[test_low_conf_mask]
                    primary_pred_low_conf = (primary_probs[test_low_conf_mask] > 0.5).astype(int)
                    second_pred_low_conf = (second_probs > 0.5).astype(int)

                    print("\n第一层模型在低置信度样本上的表现:")
                    print(f"准确率: {accuracy_score(y_test_low_conf, primary_pred_low_conf):.4f}")
                    print(f"F1分数: {f1_score(y_test_low_conf, primary_pred_low_conf):.4f}")
                    print(f"AUC: {roc_auc_score(y_test_low_conf, primary_probs[test_low_conf_mask]):.4f}")

                    print("\n第二层模型在低置信度样本上的表现:")
                    print(f"准确率: {accuracy_score(y_test_low_conf, second_pred_low_conf):.4f}")
                    print(f"F1分数: {f1_score(y_test_low_conf, second_pred_low_conf):.4f}")
                    print(f"AUC: {roc_auc_score(y_test_low_conf, second_probs):.4f}")

                    # 类别分布
                    print(f"\n低置信度样本真实类别分布: {pd.Series(y_test_low_conf).value_counts(normalize=True)}")

                # 基于最终概率进行预测
                final_pred = (final_probs > 0.5).astype(int)

                # 计算双层模型的评估指标
                metrics = {
                    'accuracy': accuracy_score(y_test, final_pred),
                    'precision': precision_score(y_test, final_pred),
                    'recall': recall_score(y_test, final_pred),
                    'spe': recall_score(y_test, final_pred, pos_label=0),
                    'f1': f1_score(y_test, final_pred),
                    'auc': roc_auc_score(y_test, final_probs)
                }

                # 存储双层模型的指标
                for metric, value in metrics.items():
                    results['TwoLayerModel']['fold_metrics'][metric].append(value)

                # 计算并存储ROC曲线数据
                fpr, tpr, _ = roc_curve(y_test, final_probs)
                results['TwoLayerModel']['fold_results'].append((fpr, tpr, metrics['auc']))

                # 打印当前折的双层模型结果
                print("\n双层模型评估结果:")
                for metric, value in metrics.items():
                    print(f"{metric}: {value:.4f}")

                # 评估每个中心的结果
                for hospital in pd.Series(hospitals_test).unique():
                    mask = hospitals_test == hospital
                    if sum(mask) > 0:  # 确保该医院有测试样本
                        hospital_probs = final_probs[mask]
                        hospital_true = y_test[mask]
                        if len(np.unique(hospital_true)) > 1:  # 确保有两个类别才计算AUC
                            results['TwoLayerModel']['hospital_metrics'][hospital]['auc'].append(
                                roc_auc_score(hospital_true, hospital_probs)
                            )

                # 同时评估原始模型(与你原有代码的内层循环相同)
                for model_name, model in models.items():
                    print(f"\n--- 评估 {model_name} ---")
                    try:
                        # 训练模型
                        if isinstance(model, (xgb.XGBClassifier, CatBoostClassifier)):
                            eval_set = [(X_val, y_val.values)]
                            model.fit(X_train_processed, y_train_processed,
                                      eval_set=eval_set,
                                      verbose=False)
                        else:
                            model.fit(X_train_processed, y_train_processed)

                        # 获取预测概率
                        y_prob = model.predict_proba(X_test)
                        fold_predictions[model_name] = y_prob

                        y_prob_final = y_prob[:, 1]
                        # 基于概率进行预测
                        y_pred = (y_prob_final > 0.5).astype(int)

                        # 计算评估指标
                        metrics = {
                            'accuracy': accuracy_score(y_test, y_pred),
                            'precision': precision_score(y_test, y_pred),
                            'recall': recall_score(y_test, y_pred),
                            'spe': recall_score(y_test, y_pred, pos_label=0),
                            'f1': f1_score(y_test, y_pred),
                            'auc': roc_auc_score(y_test, y_prob_final)
                        }

                        # 存储指标
                        for metric, value in metrics.items():
                            results[model_name]['fold_metrics'][metric].append(value)

                        # 计算并存储ROC曲线数据
                        fpr, tpr, _ = roc_curve(y_test, y_prob_final)
                        results[model_name]['fold_results'].append((fpr, tpr, metrics['auc']))

                        # 打印当前折的结果
                        for metric, value in metrics.items():
                            print(f"{metric}: {value:.4f}")

                    except Exception as e:
                        print(f"警告：{model_name} 在第 {fold} 折出现错误: {str(e)}")
                        continue

                    for hospital in pd.Series(hospitals_test).unique():
                        mask = hospitals_test == hospital
                        if sum(mask) > 0:  # 确保该医院有测试样本
                            y_pred_prob = model.predict_proba(X_test[mask])[:, 1]
                            y_true = y_test[mask]
                            if len(np.unique(y_true)) > 1:  # 确保有两个类别才计算AUC
                                results[model_name]['hospital_metrics'][hospital]['auc'].append(
                                    roc_auc_score(y_true, y_pred_prob)
                                )

            # 计算每个模型的平均指标
            for model_name in list(models.keys()) + ['TwoLayerModel']:
                metrics = results[model_name]['fold_metrics']
                mean_metrics = {
                    metric: {
                        'mean': np.mean(values),
                        'std': np.std(values)
                    }
                    for metric, values in metrics.items()
                }
                results[model_name]['metrics'] = mean_metrics

                # 更新最佳模型
                current_auc = mean_metrics['auc']['mean']
                if current_auc > best_auc:
                    best_auc = current_auc
                    best_model = (model_name, models.get(model_name, 'TwoLayerModel'))

                # 更新最佳树模型
                if model_name != 'TwoLayerModel' and isinstance(models[model_name],
                                                                (xgb.XGBClassifier, RandomForestClassifier)):
                    if current_auc > best_tree_auc:
                        best_tree_auc = current_auc
                        best_tree_model = (model_name, models[model_name])

            # 打印最终评估结果
            print("\n" + "=" * 60)
            print(f"{'最终评估结果':^60}")
            print("=" * 60)
            print(f"\n最佳模型: {best_model[0]}")
            print(f"最佳AUC: {best_auc:.4f}")

            # 定义要展示的指标及其显示名称
            metrics_display = {
                'auc': 'AUC',
                'accuracy': 'ACC',
                'recall': 'SEN',  # Sensitivity
                'spe': 'SPE',
                'f1': 'F1',
                'precision': 'PPV',
            }

            # 打印表头
            print("\n模型性能比较:")
            print("-" * 100)
            header = "Model Name".ljust(30) + "".join(f"{name:>12}" for name in metrics_display.values())
            print(header)
            print("-" * 100)

            # 按AUC降序排序并打印所有指标
            sorted_models = sorted(
                results.items(),
                key=lambda x: x[1]['metrics']['auc']['mean'],
                reverse=True
            )

            for model_name, model_results in sorted_models:
                # 截断过长的模型名称
                truncated_name = (model_name[:27] + '...') if len(model_name) > 30 else model_name
                line = truncated_name.ljust(30)

                # 添加每个指标
                for metric_key in metrics_display.keys():
                    metric = model_results['metrics'][metric_key]
                    mean = metric['mean']
                    std = metric['std']
                    # 格式化为"mean±std"的形式
                    metric_str = f"{mean:.3f}±{std:.3f}"
                    line += f"{metric_str:>12}"

                print(line)

            print("-" * 100)

            # 如果存在best_tree_model，打印其信息
            if best_tree_model:
                print(f"\n最佳树模型: {best_tree_model[0]}")
                metrics = results[best_tree_model[0]]['metrics']
                for metric_name, display_name in metrics_display.items():
                    if metric_name in metrics:
                        value = metrics[metric_name]
                        print(f"{display_name}: {value['mean']:.4f} (±{value['std']:.4f})")

            # 计算特征重要性
            feature_importance = None
            if best_tree_model and hasattr(best_tree_model[1], 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': selected_features,
                    'importance': best_tree_model[1].feature_importances_
                }).sort_values('importance', ascending=False)
                print("\n特征重要性（前20个）:")
                print(feature_importance.head(20))

            # SHAP分析
            shap_values = None
            if best_tree_model:
                print(f"\n使用最佳树模型 {best_tree_model[0]} 计算SHAP值")
                explainer = shap.TreeExplainer(best_tree_model[1])
                shap_values = explainer.shap_values(X_selected_scaled)
            else:
                print("\n注意：没有找到合适的树模型来计算SHAP值")

            # 双层模型分析
            print("\n双层模型分析:")
            print("-" * 60)

            if 'TwoLayerModel' in results:
                two_layer_auc = results['TwoLayerModel']['metrics']['auc']['mean']
                print(f"双层模型AUC: {two_layer_auc:.4f}")

                # 添加第一层模型的基准AUC（假设primary_model_name在models中）
                if primary_model_name in results:
                    primary_auc = results[primary_model_name]['metrics']['auc']['mean']
                    print(f"第一层模型AUC: {primary_auc:.4f}")
                    print(f"性能提升 (双层 vs 第一层): {two_layer_auc - primary_auc:.4f}")
                else:
                    # 如果第一层模型未单独评估，可以直接计算
                    primary_auc = roc_auc_score(y_test, primary_probs)
                    print(f"第一层模型AUC (基于当前测试集): {primary_auc:.4f}")
                    print(
                        f"性能提升 (双层 vs 第一层): {results['TwoLayerModel']['metrics']['auc']['mean'] - primary_auc:.4f}")

                # 分析低置信度样本的改进
                print("\n低置信度样本分析:")
                print(f"低置信度阈值设置为: {LOW_CONFIDENCE_THRESHOLD}")
                print(f"在每个折平均有 {low_conf_count / 5:.1f} 个低置信度样本")

            print("-" * 60)

            # PCA降维
            pca = PCA(n_components=2)
            features_2d = pca.fit_transform(X_selected_scaled)

            # 准备最终结果
            # 如果双层模型是最佳模型，需要特殊处理
            if best_model[0] == 'TwoLayerModel':
                # 双层模型没有直接的实例，所以我们创建一个复合模型对象
                class TwoLayerModelWrapper:
                    def __init__(self, primary_model, secondary_model, threshold):
                        self.primary_model = primary_model
                        self.secondary_model = secondary_model
                        self.threshold = threshold

                    def predict(self, X):
                        primary_probs = self.primary_model.predict_proba(X)[:, 1]
                        final_probs = primary_probs.copy()

                        # 识别低置信度样本
                        low_conf_mask = (primary_probs >= (0.5 - self.threshold / 2)) & (
                                primary_probs <= (0.5 + self.threshold / 2))

                        if np.sum(low_conf_mask) > 0:
                            # 使用第二层模型预测低置信度样本
                            X_low_conf = X[low_conf_mask]
                            secondary_probs = self.secondary_model.predict_proba(X_low_conf)[:, 1]
                            final_probs[low_conf_mask] = secondary_probs

                        return (final_probs > 0.5).astype(int)

                    def predict_proba(self, X):
                        primary_probs = self.primary_model.predict_proba(X)
                        final_probs_pos = primary_probs[:, 1].copy()

                        # 识别低置信度样本
                        low_conf_mask = (final_probs_pos >= (0.5 - self.threshold / 2)) & (
                                final_probs_pos <= (0.5 + self.threshold / 2))

                        if np.sum(low_conf_mask) > 0:
                            # 使用第二层模型预测低置信度样本
                            X_low_conf = X[low_conf_mask]
                            secondary_probs = self.secondary_model.predict_proba(X_low_conf)
                            final_probs_pos[low_conf_mask] = secondary_probs[:, 1]

                        # 重建完整的概率数组
                        final_probs = np.zeros_like(primary_probs)
                        final_probs[:, 1] = final_probs_pos
                        final_probs[:, 0] = 1 - final_probs_pos
                        return final_probs

                # 创建双层模型包装器
                two_layer_model = TwoLayerModelWrapper(primary_model, second_model, LOW_CONFIDENCE_THRESHOLD)
                best_model = (best_model[0], two_layer_model)

            # 创建最终结果字典
            final_result = {
                'best_model_name': best_model[0],
                'best_model': best_model[1],
                'best_auc': best_auc,
                'best_tree_model_name': best_tree_model[0] if best_tree_model else None,
                'best_tree_model': best_tree_model[1] if best_tree_model else None,
                'all_results': results,
                'fold_results': results[best_model[0]]['fold_results'],
                'shap_values': shap_values,
                'shap_data': X_selected_scaled,
                'selected_features': selected_features,
                'pca_results': {
                    'transformed': features_2d,
                    'explained_variance_ratio': pca.explained_variance_ratio_
                },
                'y': y,
                'two_layer_info': {
                    'primary_model': primary_model,
                    'secondary_model': second_model if second_model_trained else None,
                    'confidence_threshold': LOW_CONFIDENCE_THRESHOLD
                }
            }

            print("\n结果准备完成，可以进行后续分析和可视化。")
        else:
            # <editor-fold desc="多中心联合建模">
            # 原有的多中心建模逻辑
            print("\n=== 开始多中心联合建模评估 ===")
            print(f"特征数量: {X_selected_scaled.shape[1]}")
            print(f"样本数量: {X_selected_scaled.shape[0]}")
            print("类别分布:")
            print(pd.Series(y).value_counts(normalize=True))
            print("\n")

            # 外层循环：遍历每一折数据
            custom_cv = CustomStratifiedKFold(n_splits=5, shuffle=True, random_state=42)
            for fold, (train_idx, test_idx) in enumerate(
                    custom_cv.split(X_selected_scaled, y, consistensy, hospitals, clinic_hypertension, clinic_gender,
                                    clinic_hyperlipidemia, clinic_intervention, clinic_diabetes, clinic_symp), 1):
                #
                print(f"\n{'=' * 20} Fold {fold}/5 {'=' * 20}")

                print(f"\n训练集索引范围: {min(train_idx)}-{max(train_idx)}")
                print(f"测试集索引范围: {min(test_idx)}-{max(test_idx)}")

                # 确保索引没有重叠
                assert len(set(train_idx) & set(test_idx)) == 0, "训练集和测试集有重叠"
                # 划分训练集和测试集
                X_train, X_test = X_selected_scaled[train_idx], X_selected_scaled[test_idx]
                y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
                hospitals_train = hospitals.iloc[train_idx]
                diabetes_train = clinic_diabetes.iloc[train_idx]
                symp_train = clinic_symp.iloc[train_idx]
                age_train = clinic_age.iloc[train_idx]
                hospitals_test = hospitals.iloc[test_idx]

                # <editor-fold desc="剔除部分训练数据">
                # 打印当前折的中心分布情况
                print("\n当前折各中心的样本分布:")
                train_hospital_stats = hospitals_train.value_counts()
                test_hospital_stats = hospitals_test.value_counts()
                print("\n训练集中心分布:")
                for hospital, count in train_hospital_stats.items():
                    print(f"中心 {hospital}: {count} 样本")
                print("\n测试集中心分布:")
                for hospital, count in test_hospital_stats.items():
                    print(f"中心 {hospital}: {count} 样本")

                # 指定要剔除的中心
                exclude_hospitals = []  # 这里设置要剔除的中心列表

                # 剔除指定中心的训练数据
                exclude_mask = ~hospitals_train.isin(exclude_hospitals)  # 使用 isin 来处理多个中心
                X_train = X_train[exclude_mask]
                y_train = y_train[exclude_mask]
                hospitals_train = hospitals_train[exclude_mask]

                print(f"\n剔除中心 {exclude_hospitals} 后的训练集分布:")
                for hospital, count in hospitals_train.value_counts().items():
                    print(f"中心 {hospital}: {count} 样本")

                # </editor-fold>
                # 进一步划分训练集，留出验证集
                # 使用分层采样划分训练集和验证集

                # 创建复合标签用于分层抽样
                combined_labels = [f"{h}_{l}" for h, l in zip(hospitals_train, y_train)]

                # 初始化分层采样器
                from sklearn.model_selection import StratifiedShuffleSplit
                # cus = CustomStratifiedKFold(n_splits=1, test_size=0.1, shuffle=True, random_state=42)
                sss = StratifiedShuffleSplit(n_splits=1, test_size=0.1, random_state=42)
                # 获取训练集和验证集的索引
                train_idx, val_idx = next(sss.split(X_train, y_train, hospitals_train))

                # 使用索引划分数据
                X_train_final = X_train[train_idx]
                X_val = X_train[val_idx]
                y_train_final = y_train.iloc[train_idx]
                y_val = y_train.iloc[val_idx]
                hospitals_train_final = hospitals_train.iloc[train_idx]
                hospitals_val = hospitals_train.iloc[val_idx]

                # 打印每折的分布情况
                print("\n当前折的分布情况:")
                print("训练集:")
                for hospital in pd.Series(hospitals_train).unique():
                    mask = hospitals_train == hospital
                    print(f"医院 {hospital}:")
                    print(pd.Series(y_train[mask]).value_counts())

                print("\n测试集:")
                for hospital in pd.Series(hospitals_test).unique():
                    mask = hospitals_test == hospital
                    print(f"医院 {hospital}:")
                    print(pd.Series(y_test[mask]).value_counts())

                # 查看按中心的分布统计
                # stats = augmentor.get_stratified_stats(X_train_final, y_train_final, selected_features,
                #                                        center_col='clinic_hospital')
                # print("Initial distribution by center:", stats)

                # 应用增强
                # X_train_processed, y_train_processed = augmentor.balance_by_center(
                #     X_train_final, y_train_final,
                #     selected_features=selected_features,
                #     center_col='clinic_hospital',
                #     target_ratio=0.8,  # 剔除医院时，需要数量对齐
                #     noise_ratio=0
                # )

                # 不应用增强
                X_train_processed, y_train_processed = X_train_final, y_train_final
                # print(X_train_final)

                # 存储当前折的所有模型预测结果
                fold_predictions = {}

                # 内层循环：对每个模型进行评估
                for model_name, model in models.items():
                    print(f"\n--- 评估 {model_name} ---")
                    try:
                        # 训练模型
                        if isinstance(model, (xgb.XGBClassifier, CatBoostClassifier)):
                            eval_set = [(X_val, y_val.values)]
                            model.fit(X_train_processed, y_train_processed,
                                      eval_set=eval_set,
                                      verbose=False)
                        else:
                            model.fit(X_train_processed, y_train_processed)

                        # 获取预测概率
                        y_prob = model.predict_proba(X_test)
                        fold_predictions[model_name] = y_prob

                        # 使用动态集成选择器
                        if len(fold_predictions) > 40:
                            # todo 待优化 暂时停用
                            try:
                                print(f"\nApplying DES methods with {len(fold_predictions)} models")

                                # 对训练集、验证集和测试集进行PCA降维
                                pca = PCA(n_components=0.98)  # 保留95%的方差
                                X_train_pca = pca.fit_transform(X_train_processed)
                                X_val_pca = pca.transform(X_val)
                                X_test_pca = pca.transform(X_test)

                                print(f"Original dimension: {X_train_processed.shape[1]}")
                                print(f"Reduced dimension: {X_train_pca.shape[1]}")
                                print(f"Explained variance ratio: {np.sum(pca.explained_variance_ratio_):.4f}")

                                # 标准化降维后的特征
                                scaler = StandardScaler()
                                X_train_scaled = scaler.fit_transform(X_train_pca)
                                X_val_scaled = scaler.transform(X_val_pca)
                                X_test_scaled = scaler.transform(X_test_pca)

                                # 初始化不同的DES方法
                                des_methods = {
                                    # 'KNORA-E': KNORAE(k=5, random_state=42),
                                    # 'KNORA-U': KNORAU(k=5, random_state=42),
                                    # 'DES-P': DESP(k=5, random_state=42),
                                    # 'RRC': RRC(k=5, random_state=42),
                                }

                                des_results = {}

                                # 获取基分类器列表（将字典转换为列表）
                                base_classifiers = list(models.values())

                                # 测试每种DES方法
                                for method_name, des_method in des_methods.items():
                                    try:
                                        print(f"\nTesting {method_name}...")

                                        # 拟合DES方法
                                        des_method.fit(X_train_scaled, y_train_processed)

                                        # 使用验证集校准DES
                                        des_method.DFP_mask = None
                                        des_method.processed_dsel = False
                                        des_method.DSEL_data_ = X_val_scaled
                                        des_method.DSEL_target_ = y_val

                                        # 获取预测结果
                                        des_proba = des_method.predict_proba(X_test_scaled)
                                        des_pred = des_method.predict(X_test_scaled)

                                        # 计算评估指标
                                        metrics = {
                                            'accuracy': accuracy_score(y_test, des_pred),
                                            'precision': precision_score(y_test, des_pred),
                                            'recall': recall_score(y_test, des_pred),
                                            'f1': f1_score(y_test, des_pred),
                                            'auc': roc_auc_score(y_test, des_proba[:, 1])
                                        }

                                        des_results[method_name] = {
                                            'metrics': metrics,
                                            'predictions': des_proba
                                        }

                                        print(f"{method_name} Results:")
                                        for metric_name, value in metrics.items():
                                            print(f"{metric_name}: {value:.4f}")

                                    except Exception as e:
                                        print(f"Error in {method_name}: {str(e)}")
                                        continue

                                # 选择表现最好的DES方法
                                best_des = max(des_results.items(),
                                               key=lambda x: x[1]['metrics']['auc'])
                                best_method_name, best_result = best_des

                                print(f"\nSelected best DES method: {best_method_name}")
                                print(f"Best DES AUC: {best_result['metrics']['auc']:.4f}")

                                # 使用最佳DES方法的预测结果
                                y_prob_final = best_result['predictions'][:, 1]

                            except Exception as e:
                                print(f"DES failed: {str(e)}")
                                y_prob_final = y_prob[:, 1]
                        else:
                            y_prob_final = y_prob[:, 1]
                        # 基于概率进行预测
                        y_pred = (y_prob_final > 0.5).astype(int)

                        # 计算评估指标
                        metrics = {
                            'accuracy': accuracy_score(y_test, y_pred),
                            'precision': precision_score(y_test, y_pred),
                            'recall': recall_score(y_test, y_pred),
                            'spe': recall_score(y_test, y_pred, pos_label=0),
                            'f1': f1_score(y_test, y_pred),
                            'auc': roc_auc_score(y_test, y_prob_final)
                        }

                        # 存储指标
                        for metric, value in metrics.items():
                            results[model_name]['fold_metrics'][metric].append(value)

                        # 计算并存储ROC曲线数据
                        fpr, tpr, _ = roc_curve(y_test, y_prob_final)
                        results[model_name]['fold_results'].append((fpr, tpr, metrics['auc']))

                        # 打印当前折的结果
                        for metric, value in metrics.items():
                            print(f"{metric}: {value:.4f}")

                    except Exception as e:
                        print(f"警告：{model_name} 在第 {fold} 折出现错误: {str(e)}")
                        continue

                    for hospital in pd.Series(hospitals_test).unique():
                        mask = hospitals_test == hospital
                        if sum(mask) > 0:  # 确保该医院有测试样本
                            y_pred_prob = model.predict_proba(X_test[mask])[:, 1]
                            y_true = y_test[mask]
                            if len(np.unique(y_true)) > 1:  # 确保有两个类别才计算AUC
                                results[model_name]['hospital_metrics'][hospital]['auc'].append(
                                    roc_auc_score(y_true, y_pred_prob)
                                )
            # <editor-fold desc="准备结果">
            # 计算每个模型的平均指标
            for model_name in models.keys():
                metrics = results[model_name]['fold_metrics']
                mean_metrics = {
                    metric: {
                        'mean': np.mean(values),
                        'std': np.std(values)
                    }
                    for metric, values in metrics.items()
                }
                results[model_name]['metrics'] = mean_metrics

                # 更新最佳模型
                current_auc = mean_metrics['auc']['mean']
                if current_auc > best_auc:
                    best_auc = current_auc
                    best_model = (model_name, models[model_name])

                # 更新最佳树模型
                if isinstance(models[model_name], (xgb.XGBClassifier, RandomForestClassifier)):
                    if current_auc > best_tree_auc:
                        best_tree_auc = current_auc
                        best_tree_model = (model_name, models[model_name])

            # 打印最终评估结果
            print("\n" + "=" * 60)
            print(f"{'最终评估结果':^60}")
            print("=" * 60)
            print(f"\n最佳模型: {best_model[0]}")
            print(f"最佳AUC: {best_auc:.4f}")

            # 定义要展示的指标及其显示名称
            metrics_display = {
                'auc': 'AUC',
                'accuracy': 'ACC',
                'recall': 'SEN',  # Sensitivity
                'spe': 'SPE',
                'f1': 'F1',
                'precision': 'PPV',
            }

            # 打印表头
            print("\n模型性能比较:")
            print("-" * 100)
            header = "Model Name".ljust(30) + "".join(f"{name:>12}" for name in metrics_display.values())
            print(header)
            print("-" * 100)

            # 按AUC降序排序并打印所有指标
            sorted_models = sorted(
                results.items(),
                key=lambda x: x[1]['metrics']['auc']['mean'],
                reverse=True
            )

            for model_name, model_results in sorted_models:
                # 截断过长的模型名称
                truncated_name = (model_name[:27] + '...') if len(model_name) > 30 else model_name
                line = truncated_name.ljust(30)

                # 添加每个指标
                for metric_key in metrics_display.keys():
                    metric = model_results['metrics'][metric_key]
                    mean = metric['mean']
                    std = metric['std']
                    # 格式化为"mean±std"的形式
                    metric_str = f"{mean:.3f}±{std:.3f}"
                    line += f"{metric_str:>12}"

                print(line)

            print("-" * 100)

            # 如果存在best_tree_model，打印其信息
            if best_tree_model:
                print(f"\n最佳树模型: {best_tree_model[0]}")
                metrics = results[best_tree_model[0]]['metrics']
                for metric_name, display_name in metrics_display.items():
                    if metric_name in metrics:
                        value = metrics[metric_name]
                        print(f"{display_name}: {value['mean']:.4f} (±{value['std']:.4f})")
            # 计算特征重要性
            feature_importance = None
            if best_tree_model and hasattr(best_tree_model[1], 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': selected_features,
                    'importance': best_tree_model[1].feature_importances_
                }).sort_values('importance', ascending=False)
                print("\n特征重要性（前20个）:")
                print(feature_importance.head(20))
            # SHAP分析
            shap_values = None
            if best_tree_model:
                print(f"\n使用最佳树模型 {best_tree_model[0]} 计算SHAP值")
                explainer = shap.TreeExplainer(best_tree_model[1])
                shap_values = explainer.shap_values(X_selected_scaled)
            else:
                print("\n注意：没有找到合适的树模型来计算SHAP值")

            # PCA降维
            pca = PCA(n_components=2)
            features_2d = pca.fit_transform(X_selected_scaled)
            final_result = {
                'best_model_name': best_model[0],
                'best_model': best_model[1],
                'best_auc': best_auc,
                'best_tree_model_name': best_tree_model[0] if best_tree_model else None,
                'best_tree_model': best_tree_model[1] if best_tree_model else None,
                'all_results': results,
                'fold_results': results[best_model[0]]['fold_results'],
                'shap_values': shap_values,
                'shap_data': X_selected_scaled,
                'selected_features': selected_features,
                'pca_results': {
                    'transformed': features_2d,
                    'explained_variance_ratio': pca.explained_variance_ratio_
                },
                'y': y
            }
            # </editor-fold>
            # </editor-fold>

        return final_result

    def load_version_features(self, version=None):
        """
        评估多个模型的性能，将数据折划分放在外层循环
        Args:
            version: 特征版本标识。如果为None,使用已加载的特征；
                    如果为"temp",重新生成特征；
                    其他值则尝试加载对应版本的特征
        Returns:
            dict: 包含评估结果的字典
        """

        # <editor-fold desc="确保特征选择已完成">
        try:
            if version is None:
                # 使用当前已加载的特征
                if self.X_selected_scaled is not None and self.selected_features is not None and self.y is not None:
                    print("使用已加载的特征")
                    X_selected_scaled = self.X_selected_scaled
                    selected_features = self.selected_features
                    y = self.y
                else:
                    # 如果没有加载的特征，则重新生成并保存为临时版本
                    print("重新生成特征")
                    X_selected_scaled, selected_features, y = self.select_features_binary()
                    self.X_selected_scaled = X_selected_scaled
                    self.selected_features = selected_features
                    self.y = y
                    self._save_features("temp")
            elif version == "temp":
                # 强制重新生成特征
                print("重新生成特征")
                X_selected_scaled, selected_features, y = self.select_features_binary()
                self.X_selected_scaled = X_selected_scaled
                self.selected_features = selected_features
                self.y = y
                self._save_features(version)
            else:
                # 尝试加载指定版本的特征(可能是单个版本或多个版本列表)
                if not self._load_features(version):
                    print("重新生成特征")
                    # 如果加载失败，重新生成并保存
                    X_selected_scaled, selected_features, y = self.select_features_binary()
                    self.X_selected_scaled = X_selected_scaled
                    self.selected_features = selected_features
                    self.y = y
                    # 如果是列表，保存为temp版本
                    save_version = "temp" if isinstance(version, list) else version
                    self._save_features(save_version)
                else:
                    if isinstance(version, list):
                        print(f"使用合并版本 {', '.join(version)} 的特征")
                    else:
                        print(f"使用版本 {version} 的特征")
                    X_selected_scaled = self.X_selected_scaled
                    selected_features = self.selected_features
                    y = self.y
            # </editor-fold>
            return True
        except Exception as e:
            print(f"加载特征失败: {str(e)}")
            return False

    # results2 = evaluate_models_binary(analysis2)
    def visualize_results(self, results):
        """可视化分析结果"""
        # 1. PCA可视化
        fig = plt.figure(figsize=(15, 15))
        gs = gridspec.GridSpec(3, 3)
        ax_main = fig.add_subplot(gs[1:, :-1])
        ax_top = fig.add_subplot(gs[0, :-1])
        ax_right = fig.add_subplot(gs[1:, -1])

        features_2d = results['pca_results']['transformed']
        y = results['y']
        results['shap_data'] = pd.DataFrame(results['shap_data'], columns=results['selected_features'])
        shortened_feature_names = {col: col[:30] + '...' if len(col) > 30 else col
                                   for col in results['shap_data'].columns}
        results['shap_data'].rename(columns=shortened_feature_names, inplace=True)

        # 绘制主散点图和边缘分布
        pos_mask = y == 1
        neg_mask = y == 0

        ax_main.scatter(features_2d[pos_mask, 0], features_2d[pos_mask, 1],
                        c='#337DFF', marker='o', s=50, label='Positive',
                        alpha=0.7, edgecolors='white', linewidth=0.5)
        ax_main.scatter(features_2d[neg_mask, 0], features_2d[neg_mask, 1],
                        c='#FF5733', marker='x', s=50, label='Negative',
                        alpha=0.7, linewidth=2)

        # PC1和PC2的边缘分布
        for label, color, style in [(1, '#337DFF', '-'), (0, '#FF5733', '--')]:
            mask = y == label
            # PC1分布
            sns.kdeplot(data=features_2d[mask, 0], ax=ax_top, color=color,
                        linestyle=style, alpha=0.4, fill=True, linewidth=2)
            # PC2分布
            kde = gaussian_kde(features_2d[mask, 1])
            y_range = np.linspace(features_2d[:, 1].min(), features_2d[:, 1].max(), 100)
            density = kde(y_range)
            ax_right.plot(density, y_range, color=color, linestyle=style,
                          alpha=0.4, linewidth=2)
            ax_right.fill_betweenx(y_range, 0, density, color=color, alpha=0.2)

        ax_main.set_title('2D PCA Visualization', fontsize=14, pad=20)
        ax_main.set_xlabel(f'PC1 (Variance: {results["pca_results"]["explained_variance_ratio"][0]:.2f})')
        ax_main.set_ylabel(f'PC2 (Variance: {results["pca_results"]["explained_variance_ratio"][1]:.2f})')
        ax_main.grid(True, alpha=0.3)
        ax_main.legend(bbox_to_anchor=(1.05, -0.1))

        ax_top.set_title('PC1 Distribution', fontsize=12)
        ax_right.set_title('PC2 Distribution', fontsize=12)
        ax_top.set_xticks([])
        ax_right.set_xticks([])
        plt.show()

        # 2. ROC曲线 - 显示所有fold
        plt.figure(figsize=(10, 8))
        mean_tpr = np.zeros([100])
        mean_fpr = np.linspace(0, 1, 100)

        for i, (fpr, tpr, auc) in enumerate(results['fold_results'], 1):
            # 插值到统一的fpr点
            interp_tpr = np.interp(mean_fpr, fpr, tpr)
            mean_tpr += interp_tpr

            plt.plot(fpr, tpr, alpha=0.3, color='blue',
                     label=f'Fold {i} (AUC = {auc:.3f})')

        mean_tpr /= len(results['fold_results'])
        mean_auc = np.mean([x[2] for x in results['fold_results']])

        plt.plot(mean_fpr, mean_tpr, 'r-',
                 label=f'Mean ROC (AUC = {mean_auc:.3f})',
                 linewidth=2)
        plt.plot([0, 1], [0, 1], '--', color='gray')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves for All Folds')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

        # 3. SHAP值可视化
        # 3.1 Summary Plot
        plt.figure(figsize=(10, 8))
        shap.summary_plot(results['shap_values'],
                          results['shap_data'],
                          plot_type="bar",
                          max_display=10,
                          show=False)
        plt.title('Feature Importance (SHAP values)', pad=20)
        plt.tight_layout()
        plt.show()

        # 3.2 Beeswarm Plot
        plt.figure()
        shap.summary_plot(results['shap_values'],
                          results['shap_data'],
                          max_display=40,
                          show=True)
        plt.title('SHAP Value Distribution', pad=20)
        # plt.tight_layout()
        plt.show()

        # 3.3 Dependence Plot for top features
        feature_importance = np.abs(results['shap_values']).mean(0)
        top_features = results['shap_data'].columns[np.argsort(-feature_importance)[:3]]

        plt.figure(figsize=(15, 5))
        for i, feature in enumerate(top_features, 1):
            plt.subplot(1, 3, i)
            shap.dependence_plot(
                str(feature),  # 确保feature是字符串
                results['shap_values'],
                results['shap_data'],
                interaction_index=None,  # 明确设置interaction_index为None
                show=False
            )
            plt.title(f'SHAP Dependence Plot\n{feature}')
        plt.tight_layout()
        plt.show()

    def process_clinic_features(
            self,
            features_df: pd.DataFrame,
            clinic_data: Dict[str, Union[str, int, float, None]]
    ) -> pd.DataFrame:
        """
        Process and add clinical features to the feature DataFrame.

        Args:
            features_df: DataFrame containing existing features
            clinic_data: Dictionary of clinical data

        Returns:
            DataFrame with added clinical features
        """
        default_values = {
            'clinic_height': np.nan,
            'clinic_weight': np.nan,
            'clinic_gender': np.nan,
            'clinic_age': np.nan,
            'clinic_smoking': np.nan,
            'clinic_drinking': np.nan,
            'clinic_hypertension': np.nan,
            'clinic_hyperlipidemia': np.nan,
            'clinic_intervention': np.nan,
            'clinic_hospital': np.nan,
            'clinic_diabetes': np.nan,
            'clinic_symp': np.nan
        }

        # Update with provided values
        for key, value in clinic_data.items():
            default_values[key] = value if value is not None else np.nan

        # Process height
        height = default_values['clinic_height']
        if pd.notna(height):
            if isinstance(height, (int, float, np.integer, np.floating)) and height > 0:
                default_values['clinic_height'] = float(height)
            else:
                default_values['clinic_height'] = -1

        # Process weight
        weight = default_values['clinic_weight']
        if pd.notna(weight):
            if isinstance(weight, (int, float, np.integer, np.floating)) and weight > 0:
                default_values['clinic_weight'] = float(weight)
            else:
                default_values['clinic_weight'] = -1

        # Process gender
        gender = default_values['clinic_gender']
        if pd.notna(gender):
            if isinstance(gender, str) and gender in ['男', '女']:
                default_values['clinic_gender'] = 1 if gender == '男' else 0
            elif gender in [0, 1]:
                default_values['clinic_gender'] = int(gender)
            else:
                default_values['clinic_gender'] = -1

        # Process age
        age = default_values['clinic_age']
        if pd.notna(age):
            if isinstance(age, (int, float, np.integer, np.floating)) and age > 0:
                default_values['clinic_age'] = float(age)
            else:
                default_values['clinic_age'] = -1

        # Process smoking
        smoking = default_values['clinic_smoking']
        if pd.notna(smoking):
            if isinstance(smoking, (int, float, np.integer, np.floating)):
                default_values['clinic_smoking'] = float(smoking)
            else:
                default_values['clinic_smoking'] = -1

        # Process drinking
        drinking = default_values['clinic_drinking']
        if pd.notna(drinking):
            if isinstance(drinking, (int, float, np.integer, np.floating)):
                default_values['clinic_drinking'] = float(drinking)
            else:
                default_values['clinic_drinking'] = -1

        # Process hypertension
        hypertension = default_values['clinic_hypertension']
        if pd.notna(hypertension):
            if isinstance(hypertension, (int, float, np.integer, np.floating)):
                default_values['clinic_hypertension'] = float(hypertension)
            else:
                default_values['clinic_hypertension'] = -1

        # Process hyperlipidemia
        hyperlipidemia = default_values['clinic_hyperlipidemia']
        if pd.notna(hyperlipidemia):
            if isinstance(hyperlipidemia, (int, float, np.integer, np.floating)):
                default_values['clinic_hyperlipidemia'] = float(hyperlipidemia)
            else:
                default_values['clinic_hyperlipidemia'] = -1

        # Process intervention
        intervention = default_values['clinic_intervention']
        if pd.notna(intervention):
            if isinstance(intervention, (int, float, np.integer, np.floating)):
                default_values['clinic_intervention'] = float(intervention)
            else:
                default_values['clinic_intervention'] = -1

        # Process hospital
        hospital = default_values['clinic_hospital']
        if pd.notna(hospital):
            if isinstance(hospital, str) and hospital in self.HOSPITAL_MAPPING:
                default_values['clinic_hospital'] = self.HOSPITAL_MAPPING[hospital]
            elif isinstance(hospital, int) and hospital in self.HOSPITAL_MAPPING.values():
                default_values['clinic_hospital'] = hospital
            else:
                default_values['clinic_hospital'] = -1

        # Process diabetes
        diabetes = default_values['clinic_diabetes']
        if pd.notna(diabetes):
            if isinstance(diabetes, (int, float, np.integer, np.floating)):
                default_values['clinic_diabetes'] = float(diabetes)
            else:
                default_values['clinic_diabetes'] = -1

        # Process symp
        symp = default_values['clinic_symp']
        if pd.notna(symp):
            if isinstance(symp, (int, float, np.integer, np.floating)):
                default_values['clinic_symp'] = float(symp)
            else:
                default_values['clinic_symp'] = -1

        # Add processed values to DataFrame
        for key, value in default_values.items():
            features_df[key] = value

        return features_df

    def refresh_clinic_features(self, feature_df):

        try:
            df_info = pd.read_excel(self.excel_path, sheet_name='训练测试集')
            mcg_ids = df_info['心磁号'].astype(str).tolist()

            # 准备结果列表
            results = []
            # 遍历Excel中的心磁号
            for mcg_id in tqdm(mcg_ids, desc="处理文件"):
                info_row = df_info[df_info['心磁号'].astype(str) == mcg_id]
                clinic_data = {
                    'clinic_height': info_row['临床特征-身高'].iloc[0] if '临床特征-身高' in df_info.columns else None,
                    'clinic_weight': info_row['临床特征-体重'].iloc[0] if '临床特征-体重' in df_info.columns else None,
                    'clinic_gender': info_row['临床特征-性别'].iloc[0] if '临床特征-性别' in df_info.columns else None,
                    'clinic_age': info_row['临床特征-年龄'].iloc[0] if '临床特征-年龄' in df_info.columns else None,
                    'clinic_smoking': info_row['临床特征-吸烟'].iloc[0] if '临床特征-吸烟' in df_info.columns else None,
                    'clinic_drinking': info_row['临床特征-饮酒'].iloc[
                        0] if '临床特征-饮酒' in df_info.columns else None,
                    'clinic_hypertension': info_row['临床特征-高血压'].iloc[
                        0] if '临床特征-高血压' in df_info.columns else None,
                    'clinic_hyperlipidemia': info_row['临床特征-高脂血症'].iloc[
                        0] if '临床特征-高脂血症' in df_info.columns else None,
                    'clinic_intervention': info_row['临床特征-既往介入'].iloc[
                        0] if '临床特征-既往介入' in df_info.columns else None,
                    'clinic_hospital': info_row['临床特征-所在医院'].iloc[
                        0] if '临床特征-所在医院' in df_info.columns else None,
                    'clinic_diabetes': info_row['临床特征-糖尿病'].iloc[
                        0] if '临床特征-糖尿病' in df_info.columns else None,
                    'clinic_symp': info_row['临床特征-典型症状'].iloc[
                        0] if '临床特征-典型症状' in df_info.columns else None
                }
                # 清理临床数据字典，移除None值
                clinic_data = {k: v for k, v in clinic_data.items() if v is not None}
                return self.process_clinic_features(feature_df, clinic_data)
        except:
            pass


def plot_classifier_hospital_performance(result, by_hospital=True, figsize=(15, 7)):
    """
    绘制分类器在不同医院的AUC性能图表

    参数:
    - result: 包含分类器结果的字典
    - by_hospital: 布尔值，True则以医院为主要分组，False则以分类器为主要分组
    - figsize: 图表大小
    """
    # 提取所有分类器和医院名称
    plt.style.use('seaborn-darkgrid')
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    classifiers = list(result.keys())
    hospitals = sorted(list(HOSPITAL_MAPPING.values()))  # 使用映射中的数字排序

    # 准备数据
    means = {}  # 存储均值
    stds = {}  # 存储标准差

    # 计算每个分类器在每个医院的均值和标准差
    for clf in classifiers:
        means[clf] = {}
        stds[clf] = {}
        for hospital in hospitals:
            try:
                auc_scores = result[clf]['hospital_metrics'][hospital]['auc']  # 修正数据访问方式
                means[clf][hospital] = np.mean(auc_scores)
                stds[clf][hospital] = np.std(auc_scores)
            except:
                print(f"Error for {clf} at hospital {hospital}")
                auc_scores = [0.5]  # 用0.5填充缺失值
                means[clf][hospital] = np.mean(auc_scores)
                stds[clf][hospital] = np.std(auc_scores)

    # 设置绘图样式
    fig, ax = plt.subplots(figsize=figsize)

    # 使用ColorBrewer方案中的Set2，这是一个经过科学验证的易区分配色方案
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

    if by_hospital:
        # 以医院为主要分组绘图
        x = np.arange(len(hospitals))
        width = 0.8 / len(classifiers)

        for i, clf in enumerate(classifiers):
            clf_means = [means[clf][h] for h in hospitals]
            clf_stds = [stds[clf][h] for h in hospitals]

            ax.bar(x + i * width, clf_means, width,
                   label=clf, color=colors[i % len(colors)], alpha=0.7,
                   edgecolor='black', linewidth=1)
            ax.errorbar(x + i * width, clf_means, yerr=clf_stds,
                        fmt='none', ecolor='black', capsize=3, capthick=0.5)

        ax.set_xlabel('医院', fontsize=12, fontweight='bold')
        # 使用实际医院名称
        hospital_names = [REVERSE_HOSPITAL_MAPPING[h] for h in hospitals]
        ax.set_xticks(x + width * (len(classifiers) - 1) / 2)
        ax.set_xticklabels(hospital_names, rotation=45, ha='right')

    else:
        # 以分类器为主要分组绘图
        x = np.arange(len(classifiers))
        width = 0.8 / len(hospitals)

        for i, hospital in enumerate(hospitals):
            hospital_means = [means[clf][hospital] for clf in classifiers]
            hospital_stds = [stds[clf][hospital] for clf in classifiers]

            ax.bar(x + i * width, hospital_means, width,
                   label=REVERSE_HOSPITAL_MAPPING[hospital],
                   color=colors[i % len(colors)], alpha=0.7,
                   edgecolor='black', linewidth=1)
            ax.errorbar(x + i * width, hospital_means, yerr=hospital_stds,
                        fmt='none', ecolor='black', capsize=3, capthick=0.5)

        ax.set_xlabel('分类器', fontsize=12, fontweight='bold')
        ax.set_xticks(x + width * (len(hospitals) - 1) / 2)
        ax.set_xticklabels(classifiers, rotation=45, ha='right')

    ax.set_ylabel('AUC Score', fontsize=12, fontweight='bold')
    ax.set_title('分类器在不同医院的AUC性能比较', fontsize=14, fontweight='bold', pad=20)

    # 设置y轴范围，从0.5开始，因为AUC一般都在0.5以上
    ax.set_ylim(0.5, 1.0)

    # 添加网格线
    ax.yaxis.grid(True, linestyle='--', alpha=0.7)

    # 优化图例位置和样式
    ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left',
              frameon=True, fancybox=True, shadow=True)

    # 调整布局以确保所有元素可见
    plt.tight_layout()
    plt.show()


def plot_new_classifier_hospital_performance(result, by_hospital=True, figsize=(15, 7)):
    """
    绘制分类器在不同医院的AUC性能图表
    用来呈现分中心单独建模后的返回的结果
    参数:
    - result: 包含医院结果的字典，新的数据结构
    - by_hospital: 布尔值，True则以医院为主要分组，False则以分类器为主要分组
    - figsize: 图表大小
    """
    plt.style.use('seaborn-darkgrid')
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 提取所有医院和分类器
    # hospitals = sorted(list(HOSPITAL_MAPPING.values()))
    # print(sorted(list(result['center_results'].keys())))
    hospitals = sorted(list(result.keys()))
    # 从第一个医院的数据中获取分类器列表
    first_hospital = result[hospitals[0]]
    classifiers = list(first_hospital['results']['all_results'].keys())

    # 准备数据
    means = {}  # 存储均值
    stds = {}  # 存储标准差

    # 计算每个分类器在每个医院的均值和标准差
    for hospital in hospitals:
        hospital_data = result[hospital]['results']['all_results']
        for clf in classifiers:
            if clf not in means:
                means[clf] = {}
                stds[clf] = {}

            auc_scores = hospital_data[clf]['fold_metrics']['auc']
            means[clf][hospital] = np.mean(auc_scores)
            stds[clf][hospital] = np.std(auc_scores)

    # 设置绘图样式
    fig, ax = plt.subplots(figsize=figsize)

    # 使用ColorBrewer方案中的Set2，这是一个经过科学验证的易区分配色方案
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

    if by_hospital:
        # 以医院为主要分组绘图
        x = np.arange(len(hospitals))
        width = 0.8 / len(classifiers)

        for i, clf in enumerate(classifiers):
            clf_means = [means[clf][h] for h in hospitals]
            clf_stds = [stds[clf][h] for h in hospitals]

            ax.bar(x + i * width, clf_means, width,
                   label=clf, color=colors[i % len(colors)], alpha=0.7,
                   edgecolor='black', linewidth=1)
            ax.errorbar(x + i * width, clf_means, yerr=clf_stds,
                        fmt='none', ecolor='black', capsize=3, capthick=0.5)

        ax.set_xlabel('医院', fontsize=12, fontweight='bold')
        hospital_names = [REVERSE_HOSPITAL_MAPPING[h] for h in hospitals]
        ax.set_xticks(x + width * (len(classifiers) - 1) / 2)
        ax.set_xticklabels(hospital_names, rotation=45, ha='right')

    else:
        # 以分类器为主要分组绘图
        x = np.arange(len(classifiers))
        width = 0.8 / len(hospitals)

        for i, hospital in enumerate(hospitals):
            hospital_means = [means[clf][hospital] for clf in classifiers]
            hospital_stds = [stds[clf][hospital] for clf in classifiers]

            ax.bar(x + i * width, hospital_means, width,
                   label=REVERSE_HOSPITAL_MAPPING[hospital],
                   color=colors[i % len(colors)], alpha=0.7,
                   edgecolor='black', linewidth=1)
            ax.errorbar(x + i * width, hospital_means, yerr=hospital_stds,
                        fmt='none', ecolor='black', capsize=3, capthick=0.5)

        ax.set_xlabel('分类器', fontsize=12, fontweight='bold')
        ax.set_xticks(x + width * (len(hospitals) - 1) / 2)
        ax.set_xticklabels(classifiers, rotation=45, ha='right')

    ax.set_ylabel('AUC Score', fontsize=12, fontweight='bold')
    ax.set_title('分类器在不同医院的AUC性能比较', fontsize=14, fontweight='bold', pad=20)

    # 设置y轴范围，从0.5开始，因为AUC一般都在0.5以上
    ax.set_ylim(0.5, 1.0)

    # 添加网格线
    ax.yaxis.grid(True, linestyle='--', alpha=0.7)

    # 优化图例位置和样式
    ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left',
              frameon=True, fancybox=True, shadow=True)

    # 调整布局以确保所有元素可见
    plt.tight_layout()
    plt.show()


def get_test_metrics(result_df, external_excel_path, sheet_name='测试集', hospital_thresholds=None):
    """
    计算并格式化展示模型的性能指标，包括对临床特征的身高和体重进行分箱处理

    参数:
    result_df: 包含预测结果的DataFrame
    external_excel_path: 外部Excel文件路径，包含测试集信息
    sheet_name: Excel表格中的工作表名称，默认为'测试集'
    hospital_thresholds: 医院名到阈值的映射字典，默认为None（表示所有医院使用0.5阈值）

    返回:
    dict: 包含所有计算指标的字典
    """
    import pandas as pd
    import numpy as np
    from sklearn.metrics import roc_auc_score, accuracy_score
    from tabulate import tabulate
    import matplotlib.pyplot as plt

    # 读取外部xlsx文件中的测试集sheet
    print(f"正在读取外部文件: {external_excel_path}")
    external_df = pd.read_excel(external_excel_path, sheet_name=sheet_name)

    # -------------- 条件处理 -------------
    external_df = condition_policy(external_df)
    # -------------- 条件处理 -------------

    if sheet_name == '外验集':
        # 如果df的造影结论为空值 则去另一个文件表中读取该心磁号对应的造影结论替代该表的造影结论
        other_df = pd.read_excel(
            'D:\projects_python\对数据进行5折交叉验证的数据划分\split_20250222\\uploads\\20250227_20250227_140200.xlsx',
            sheet_name='总表')
        # 找出空值并用other_df中对应心磁号的造影结论填充
        mask = external_df['造影结论'].isna()
        external_df.loc[mask, '造影结论'] = external_df[mask]['心磁号'].map(
            other_df.set_index('心磁号')['造影结论'])

    # 选择所有需要的临床特征列
    clinical_features = [
        '临床特征-性别', '临床特征-身高', '临床特征-体重', '临床特征-年龄', '临床特征-吸烟', '临床特征-饮酒',
        '临床特征-高血压', '临床特征-糖尿病', '临床特征-高脂血症',
        '临床特征-典型症状', '临床特征-既往介入'
    ]

    # 合并dataframe，包含之前的列和新增的临床特征列
    all_columns = ['心磁号', '心磁质量', '人工', '临床特征-所在医院'] + clinical_features
    df = pd.merge(result_df, external_df[all_columns], on='心磁号', how='left')
    df['造影结论'] = df['心磁号'].map(
        dict(zip(external_df['心磁号'], external_df['造影结论']))
    )

    # 检查是否有缺失值
    missing_count = df[['心磁号', '心磁质量', '人工', '造影结论'] + clinical_features].isna().sum()
    if missing_count.sum() > 0:
        print("\n警告: 数据中存在缺失值")
        print(missing_count[missing_count > 0])

    # 将造影结论转为数值标签
    df['label'] = (df['造影结论'] == 1).astype(int)

    # 创建人工-标签一致性字段
    df['人工-标签一致性'] = (df['人工'] == df['造影结论']).map({True: '一致', False: '不一致'})

    # 添加使用特定阈值进行预测的逻辑
    if hospital_thresholds is not None:
        # 将医院特定的阈值应用到对应的医院
        df['hospital_threshold'] = df['临床特征-所在医院'].map(hospital_thresholds)
        # 对于没有特定阈值的医院，使用默认值0.5
        df['hospital_threshold'].fillna(0.5, inplace=True)
        df['predicted_class'] = (df['predicted_prob'] >= df['hospital_threshold']).astype(int)
    else:
        # 使用统一的0.5阈值进行预测
        df['predicted_class'] = (df['predicted_prob'] >= 0.5).astype(int)

    # 对年龄进行分箱处理
    def age_binning(age):
        if pd.isna(age):
            return '未知'
        elif age < 45:
            return '<45岁'
        elif age < 60:
            return '45-59岁'
        elif age < 75:
            return '60-74岁'
        else:
            return '≥75岁'

    df['年龄分组'] = df['临床特征-年龄'].apply(age_binning)

    # 对身高进行分箱处理
    def height_binning(height):
        if pd.isna(height):
            return '未知'
        elif height < 160:
            return '<160cm'
        elif height < 170:
            return '160-169cm'
        elif height < 180:
            return '170-179cm'
        else:
            return '≥180cm'

    df['身高分组'] = df['临床特征-身高'].apply(height_binning)

    # 对体重进行分箱处理
    def weight_binning(weight):
        if pd.isna(weight):
            return '未知'
        elif weight < 60:
            return '<60kg'
        elif weight < 70:
            return '60-69kg'
        elif weight < 80:
            return '70-79kg'
        else:
            return '≥80kg'

    df['体重分组'] = df['临床特征-体重'].apply(weight_binning)

    # 计算临床特征的空缺率，并找出空缺率大于15%的特征
    missing_rate = {}
    high_missing_features = []

    for col in clinical_features:
        missing_count = df[col].isna().sum()
        missing_rate[col] = missing_count / len(df) * 100
        if missing_rate[col] > 15:
            high_missing_features.append(col)
            print(f"{col} 的空缺率为 {missing_rate[col]:.2f}%, 将进行有无分析")

    # 为空缺率高的特征创建有无分组
    for col in high_missing_features:
        # 创建新的有无列，有值的为"有"，空值的为"无"
        binary_col = f"{col}-有无"
        df[binary_col] = df[col].apply(lambda x: "无" if pd.isna(x) else "有")

    def calc_metrics(y_true, y_pred, y_prob):
        """计算各项性能指标"""
        try:
            auc = roc_auc_score(y_true, y_prob)
        except:
            auc = float('nan')

        acc = accuracy_score(y_true, y_pred)
        # 计算敏感性(真阳性率)
        sen = np.sum((y_pred == 1) & (y_true == 1)) / np.sum(y_true == 1) if np.sum(y_true == 1) > 0 else 0
        # 计算特异性(真阴性率)
        spe = np.sum((y_pred == 0) & (y_true == 0)) / np.sum(y_true == 0) if np.sum(y_true == 0) > 0 else 0
        # 计算样本数量
        sample_count = len(y_true)
        # 计算正例和负例数量
        positive_count = np.sum(y_true == 1)
        negative_count = np.sum(y_true == 0)

        return {
            'AUC': round(auc, 3) if not np.isnan(auc) else 'N/A',
            'ACC': round(acc, 3),
            'SEN': round(sen, 3),
            'SPE': round(spe, 3),
            'N': sample_count,
            'Pos': positive_count,
            'Neg': negative_count
        }

    # 计算总体指标
    overall_metrics = calc_metrics(df['label'], df['predicted_class'], df['predicted_prob'])

    # 添加关于阈值的信息到指标中
    if hospital_thresholds is not None:
        overall_metrics['threshold_type'] = 'hospital_specific'
        overall_metrics['hospital_thresholds'] = hospital_thresholds
    else:
        overall_metrics['threshold_type'] = 'uniform_0.5'

    # 创建一个表格格式的输出
    print("\n" + "=" * 60)
    print("总体性能指标")
    print("=" * 60)

    headers = ["指标", "数值"]
    table_data = [
        ["样本总数", overall_metrics['N']],
        ["阳性样本数", overall_metrics['Pos']],
        ["阴性样本数", overall_metrics['Neg']],
        ["AUC", overall_metrics['AUC']],
        ["准确率(ACC)", f"{overall_metrics['ACC']:.3f}"],
        ["敏感性(SEN)", f"{overall_metrics['SEN']:.3f}"],
        ["特异性(SPE)", f"{overall_metrics['SPE']:.3f}"]
    ]

    # 如果使用的是医院特定阈值，添加这个信息到表格
    if hospital_thresholds is not None:
        table_data.append(["阈值类型", "医院特定阈值"])
    else:
        table_data.append(["阈值类型", "统一阈值0.5"])

    print(tabulate(table_data, headers=headers, tablefmt="grid"))

    # 定义要分析的分组
    basic_groupby_cols = ['心磁质量', '临床特征-所在医院', '人工-标签一致性']
    clinical_groupby_cols = ['临床特征-性别', '年龄分组', '身高分组', '体重分组'] + [col for col in clinical_features if
                                                                                     col not in ['临床特征-性别',
                                                                                                 '临床特征-年龄',
                                                                                                 '临床特征-身高',
                                                                                                 '临床特征-体重']]

    # 添加高空缺率特征的有无分组
    missing_feature_binary_cols = [f"{col}-有无" for col in high_missing_features]

    all_groupby_cols = basic_groupby_cols + clinical_groupby_cols + missing_feature_binary_cols
    group_metrics = {}

    # 为每个分组计算和展示指标
    for col in all_groupby_cols:
        print("\n" + "=" * 60)
        print(f"按【{col}】分组的性能指标")
        print("=" * 60)

        group_metrics[col] = {}

        # 创建带表头的表格
        headers = ["分组", "样本数", "阳性样本", "阴性样本", "AUC", "ACC", "SEN", "SPE"]
        # 如果使用医院特定阈值，添加阈值列
        if hospital_thresholds is not None and col == '临床特征-所在医院':
            headers.append("使用阈值")
        table_data = []

        # 获取该分组的唯一值
        if col == '年龄分组':
            # 按年龄段顺序排序
            age_order = ['<45岁', '45-59岁', '60-74岁', '≥75岁', '未知']
            group_values = [val for val in age_order if val in df[col].unique()]
        elif col == '身高分组':
            height_order = ['<160cm', '160-169cm', '170-179cm', '≥180cm', '未知']
            group_values = [val for val in height_order if val in df[col].unique()]
        elif col == '体重分组':
            weight_order = ['<60kg', '60-69kg', '70-79kg', '≥80kg', '未知']
            group_values = [val for val in weight_order if val in df[col].unique()]
        elif col in ['临床特征-吸烟', '临床特征-饮酒', '临床特征-高血压', '临床特征-糖尿病',
                     '临床特征-高脂血症', '临床特征-典型症状', '临床特征-既往介入']:
            # 处理可能包含数字和字符串混合的列
            unique_values = df[col].unique()
            # 分别处理数字和字符串
            num_values = [val for val in unique_values if isinstance(val, (int, float)) and not pd.isna(val)]
            str_values = [val for val in unique_values if isinstance(val, str)]
            na_values = [val for val in unique_values if pd.isna(val)]

            # 先对数字排序，再添加字符串，最后添加空值
            group_values = sorted(num_values) + sorted(str_values) + na_values
        else:
            # 尝试排序，如果失败则不排序
            try:
                group_values = sorted(df[col].unique())
            except TypeError:
                group_values = list(df[col].unique())

        for group_val in group_values:
            mask = df[col] == group_val
            group_df = df[mask]

            if len(group_df) > 0 and len(group_df['label'].unique()) > 1:  # 确保该组有数据且包含至少两种标签
                metrics = calc_metrics(
                    group_df['label'],
                    group_df['predicted_class'],
                    group_df['predicted_prob']
                )

                # 创建行数据
                row_data = [
                    group_val,
                    metrics['N'],
                    metrics['Pos'],
                    metrics['Neg'],
                    metrics['AUC'],
                    f"{metrics['ACC']:.3f}",
                    f"{metrics['SEN']:.3f}",
                    f"{metrics['SPE']:.3f}"
                ]

                # 如果是医院特定阈值并且当前分组是医院，添加使用的阈值信息
                if hospital_thresholds is not None and col == '临床特征-所在医院':
                    threshold = hospital_thresholds.get(group_val, 0.5)
                    row_data.append(f"{threshold:.3f}")

                table_data.append(row_data)
                group_metrics[col][group_val] = metrics
            elif len(group_df) > 0:
                # 该组只有一种标签，无法计算某些指标
                print(f"警告: 组 '{group_val}' 只包含一种标签，无法计算完整指标")
                metrics = {
                    'N': len(group_df),
                    'Pos': np.sum(group_df['label'] == 1),
                    'Neg': np.sum(group_df['label'] == 0),
                    'ACC': accuracy_score(group_df['label'], group_df['predicted_class']),
                    'AUC': 'N/A',
                    'SEN': 'N/A' if np.sum(group_df['label'] == 1) == 0 else 'N/A',
                    'SPE': 'N/A' if np.sum(group_df['label'] == 0) == 0 else 'N/A'
                }

                # 添加行数据
                row_data = [
                    group_val,
                    metrics['N'],
                    metrics['Pos'],
                    metrics['Neg'],
                    metrics['AUC'],
                    f"{metrics['ACC']:.3f}" if isinstance(metrics['ACC'], float) else metrics['ACC'],
                    metrics['SEN'],
                    metrics['SPE']
                ]

                # 如果是医院特定阈值并且当前分组是医院，添加使用的阈值信息
                if hospital_thresholds is not None and col == '临床特征-所在医院':
                    threshold = hospital_thresholds.get(group_val, 0.5)
                    row_data.append(f"{threshold:.3f}")

                table_data.append(row_data)
                group_metrics[col][group_val] = metrics

        print(tabulate(table_data, headers=headers, tablefmt="grid"))

    # 绘制可视化比较图
    try:
        # 使用指定的颜色方案
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

        # 绘制总体性能指标图
        plt.figure(figsize=(10, 6))
        metrics_names = ['AUC', 'ACC', 'SEN', 'SPE']
        metrics_values = [
            overall_metrics['AUC'] if isinstance(overall_metrics['AUC'], float) else 0,
            overall_metrics['ACC'],
            overall_metrics['SEN'],
            overall_metrics['SPE']
        ]

        plt.bar(metrics_names, metrics_values, color=colors[0], alpha=0.7)
        plt.ylim(0, 1)
        plt.title('模型总体性能', fontsize=14)
        plt.grid(axis='y', linestyle='--', alpha=0.3)

        # 添加数值标签
        for i, v in enumerate(metrics_values):
            plt.text(i, v + 0.02, f'{v:.3f}', ha='center')

        plt.tight_layout()
        plt.show()

        # 绘制基本分组的性能指标
        plot_group_metrics(basic_groupby_cols, group_metrics, colors)

        # 绘制临床特征的性能指标 - 分为多个子图
        plot_clinical_metrics(clinical_groupby_cols, group_metrics, colors)
        # 绘制高空缺率特征的有无分组性能指标

        # if missing_feature_binary_cols:
        #     print("\n" + "=" * 60)
        #     print("高空缺率特征的有无分组性能指标")
        #     print("=" * 60)
        #
        #     # 对于每个高空缺率特征，单独绘制一个图表
        #     for col in missing_feature_binary_cols:
        #         # 调用绘图函数，每次只传一个列名，确保每个特征有独立的图表
        #         plot_group_metrics([col], group_metrics, colors)

    except Exception as e:
        print(f"\n警告: 无法生成可视化图表: {str(e)}")

    # 将标签添加到结果DataFrame中
    result_df['最终标签'] = df['label']

    # 如果使用了医院特定阈值，将其也添加到结果中
    if hospital_thresholds is not None:
        result_df['hospital_threshold'] = df['hospital_threshold']
        result_df['predicted_class'] = df['predicted_class']

    return {
        'overall': overall_metrics,
        'group_metrics': group_metrics,
        'result_df': result_df,
        'threshold_type': 'hospital_specific' if hospital_thresholds else 'uniform_0.5'
    }


def plot_group_metrics(groupby_cols, group_metrics, colors):
    """绘制分组性能指标图表"""
    import matplotlib.pyplot as plt
    import pandas as pd
    import numpy as np

    # 检查是否有足够的分组进行绘图
    if not groupby_cols:
        return

    # 如果只有一个分组，将其放在单独的图中
    if len(groupby_cols) == 1:
        col = groupby_cols[0]
        plt.figure(figsize=(10, 6))

        # 准备数据
        plot_data = []
        group_values = []

        for group_val, metrics in group_metrics[col].items():
            if isinstance(metrics['AUC'], float) or (isinstance(metrics['AUC'], str) and metrics['AUC'] != 'N/A'):
                plot_data.append([
                    float(metrics['AUC']) if isinstance(metrics['AUC'], float) else np.nan,
                    metrics['ACC'],
                    metrics['SEN'],
                    metrics['SPE']
                ])
                group_values.append(str(group_val))

        if plot_data:
            # 创建DataFrame
            plot_df = pd.DataFrame(plot_data,
                                   columns=['AUC', 'ACC', 'SEN', 'SPE'],
                                   index=group_values)

            # 绘制条形图，添加0.7透明度
            plot_df.plot(kind='bar', color=colors[:4], alpha=0.7)
            plt.title(f'按{col}分组的性能指标', fontsize=14)
            plt.xlabel(col, fontsize=12)
            plt.ylabel('指标值', fontsize=12)
            plt.ylim(0, 1)

            # 移动图例位置到图的上方，避免与柱状图重叠
            plt.legend(loc='upper center', bbox_to_anchor=(0.5, 1.15), ncol=4)

            # 添加网格线
            plt.grid(axis='y', linestyle='--', alpha=0.3)
            plt.xticks(rotation=45, ha='right')

            # 为每个条形添加数值标签（仅当值不是NaN时）
            for p in plt.gca().patches:
                if isinstance(p.get_height(), (int, float)) and not np.isnan(p.get_height()):
                    plt.annotate(f'{p.get_height():.2f}',
                                 (p.get_x() + p.get_width() / 2., p.get_height()),
                                 ha='center', va='bottom',
                                 xytext=(0, 3),
                                 textcoords='offset points',
                                 fontsize=9)

            plt.tight_layout()
            plt.show()
        return

    # 多个分组情况
    fig, axes = plt.subplots(1, len(groupby_cols), figsize=(18, 8))

    for i, col in enumerate(groupby_cols):
        ax = axes[i] if len(groupby_cols) > 1 else axes

        # 准备数据
        plot_data = []
        group_values = []

        for group_val, metrics in group_metrics[col].items():
            if isinstance(metrics['AUC'], float) or (isinstance(metrics['AUC'], str) and metrics['AUC'] != 'N/A'):
                plot_data.append([
                    float(metrics['AUC']) if isinstance(metrics['AUC'], float) else np.nan,
                    metrics['ACC'],
                    metrics['SEN'],
                    metrics['SPE']
                ])
                group_values.append(str(group_val))

        if plot_data:
            # 创建DataFrame
            plot_df = pd.DataFrame(plot_data,
                                   columns=['AUC', 'ACC', 'SEN', 'SPE'],
                                   index=group_values)

            # 绘制条形图，添加0.7透明度
            plot_df.plot(kind='bar', ax=ax, color=colors[:4], alpha=0.7)
            ax.set_title(f'按{col}分组的性能指标', fontsize=14)
            ax.set_xlabel(col, fontsize=12)
            ax.set_ylabel('指标值', fontsize=12)
            ax.set_ylim(0, 1)

            # 移动图例位置到图的上方，避免与柱状图重叠
            ax.legend(loc='upper center', bbox_to_anchor=(0.5, 1.15), ncol=4)

            # 添加网格线
            ax.grid(axis='y', linestyle='--', alpha=0.3)
            ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')

            # 为每个条形添加数值标签（仅当值不是NaN时）
            for p in ax.patches:
                if isinstance(p.get_height(), (int, float)) and not np.isnan(p.get_height()):
                    ax.annotate(f'{p.get_height():.2f}',
                                (p.get_x() + p.get_width() / 2., p.get_height()),
                                ha='center', va='bottom',
                                xytext=(0, 3),
                                textcoords='offset points',
                                fontsize=8)

    plt.tight_layout()
    plt.show()


def plot_clinical_metrics(clinical_cols, group_metrics, colors):
    """绘制临床特征性能指标图表"""
    import matplotlib.pyplot as plt
    import pandas as pd
    import numpy as np
    import math

    # 如果没有临床特征，直接返回
    if not clinical_cols:
        return

    # 计算需要的行数和列数
    n_cols = 3  # 每行三个图
    n_rows = math.ceil(len(clinical_cols) / n_cols)

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6 * n_rows))

    # 使axes成为二维数组，即使只有一行或一列
    if n_rows == 1 and n_cols == 1:
        axes = np.array([[axes]])
    elif n_rows == 1:
        axes = axes.reshape(1, -1)
    elif n_cols == 1:
        axes = axes.reshape(-1, 1)

    # 设置子图标题
    fig.suptitle('临床特征分组性能指标', fontsize=16)

    for i, col in enumerate(clinical_cols):
        row = i // n_cols
        col_idx = i % n_cols
        ax = axes[row, col_idx]

        # 准备数据
        plot_data = []
        group_values = []

        for group_val, metrics in group_metrics[col].items():
            if isinstance(metrics['AUC'], float) or (isinstance(metrics['AUC'], str) and metrics['AUC'] != 'N/A'):
                plot_data.append([
                    float(metrics['AUC']) if isinstance(metrics['AUC'], float) else np.nan,
                    metrics['ACC'],
                    metrics['SEN'],
                    metrics['SPE']
                ])
                group_values.append(str(group_val))

        if plot_data:
            # 创建DataFrame
            plot_df = pd.DataFrame(plot_data,
                                   columns=['AUC', 'ACC', 'SEN', 'SPE'],
                                   index=group_values)

            # 绘制条形图，添加0.7透明度
            plot_df.plot(kind='bar', ax=ax, color=colors[:4], alpha=0.7)
            ax.set_title(f'{col}', fontsize=12)
            ax.set_xlabel('')
            ax.set_ylabel('指标值', fontsize=10)
            ax.set_ylim(0, 1)

            # 移动图例位置到图的上方，避免与柱状图重叠
            ax.legend(loc='upper center', bbox_to_anchor=(0.5, 1.15), ncol=4)

            # 添加网格线
            ax.grid(axis='y', linestyle='--', alpha=0.3)
            ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')

            # 为每个条形添加数值标签（仅当值不是NaN时）
            for p in ax.patches:
                if isinstance(p.get_height(), (int, float)) and not np.isnan(p.get_height()):
                    ax.annotate(f'{p.get_height():.2f}',
                                (p.get_x() + p.get_width() / 2., p.get_height()),
                                ha='center', va='bottom',
                                xytext=(0, 3),
                                textcoords='offset points',
                                fontsize=7)

    # 隐藏多余的子图
    for i in range(len(clinical_cols), n_rows * n_cols):
        row = i // n_cols
        col_idx = i % n_cols
        fig.delaxes(axes[row, col_idx])

    plt.tight_layout(rect=[0, 0, 1, 0.96])  # 为suptitle留出空间
    plt.show()


def analyze_errors(result_df, external_excel_path, sheet_name='测试集'):
    """
    分析模型预测错误的样本，找出哪些临床特征或条件更容易导致错误，
    并对假阳性和假阴性进行分开分析

    参数:
    result_df: 包含预测结果的DataFrame
    external_excel_path: 外部Excel文件路径，包含测试集信息
    sheet_name: Excel文件中的sheet名称，默认为'测试集'

    返回:
    dict: 包含错误分析结果的字典
    """
    import pandas as pd
    import numpy as np
    from tabulate import tabulate
    import matplotlib.pyplot as plt
    import seaborn as sns

    # 读取外部xlsx文件中的测试集sheet
    print(f"正在读取外部文件: {external_excel_path}")
    external_df = pd.read_excel(external_excel_path, sheet_name=sheet_name)

    # 处理外验集情况，同原函数
    if sheet_name == '外验集':
        other_df = pd.read_excel(
            'D:\projects_python\对数据进行5折交叉验证的数据划分\split_20250222\\uploads\\20250227_20250227_140200.xlsx',
            sheet_name='总表')
        mask = external_df['造影结论'].isna()
        external_df.loc[mask, '造影结论'] = external_df[mask]['心磁号'].map(
            other_df.set_index('心磁号')['造影结论'])

    external_df = condition_policy(external_df)

    # 选择所有需要的临床特征列
    clinical_features = [
        '临床特征-性别', '临床特征-身高', '临床特征-体重', '临床特征-年龄', '临床特征-吸烟', '临床特征-饮酒',
        '临床特征-高血压', '临床特征-糖尿病', '临床特征-高脂血症',
        '临床特征-典型症状', '临床特征-既往介入'
    ]

    # 合并dataframe，包含之前的列和新增的临床特征列
    all_columns = ['心磁号', '心磁质量', '人工', '临床特征-所在医院'] + clinical_features
    df = pd.merge(result_df, external_df[all_columns], on='心磁号', how='left')
    df['造影结论'] = df['心磁号'].map(
        dict(zip(external_df['心磁号'], external_df['造影结论']))
    )
    # 检查是否有缺失值
    missing_count = df[['心磁号', '心磁质量', '人工', '造影结论'] + clinical_features].isna().sum()
    if missing_count.sum() > 0:
        print("\n警告: 数据中存在缺失值")
        print(missing_count[missing_count > 0])

    # 将造影结论转为数值标签
    df['label'] = (df['造影结论'] == 1).astype(int)

    # 创建人工-标签一致性字段
    df['人工-标签一致性'] = (df['人工'] == df['造影结论']).map({True: '一致', False: '不一致'})

    # 添加错误类型字段 (FP和FN)
    df['错误类型'] = 'correct'  # 默认为正确

    # 标记假阳性(FP): 预测为1，实际为0
    df.loc[(df['predicted_class'] == 1) & (df['label'] == 0), '错误类型'] = 'false_positive'

    # 标记假阴性(FN): 预测为0，实际为1
    df.loc[(df['predicted_class'] == 0) & (df['label'] == 1), '错误类型'] = 'false_negative'

    # 计算总体错误情况
    total_samples = len(df)
    correct_samples = sum(df['错误类型'] == 'correct')
    fp_samples = sum(df['错误类型'] == 'false_positive')
    fn_samples = sum(df['错误类型'] == 'false_negative')

    error_rate = (fp_samples + fn_samples) / total_samples
    fp_rate = fp_samples / total_samples
    fn_rate = fn_samples / total_samples

    print("\n" + "=" * 60)
    print("错误分析总览")
    print("=" * 60)

    headers = ["指标", "数值"]
    table_data = [
        ["样本总数", total_samples],
        ["正确预测样本数", correct_samples],
        ["正确率", f"{correct_samples / total_samples:.4f} ({correct_samples}/{total_samples})"],
        ["错误预测样本数", fp_samples + fn_samples],
        ["错误率", f"{error_rate:.4f} ({fp_samples + fn_samples}/{total_samples})"],
        ["假阳性(FP)样本数", fp_samples],
        ["假阳性率", f"{fp_rate:.4f} ({fp_samples}/{total_samples})"],
        ["假阴性(FN)样本数", fn_samples],
        ["假阴性率", f"{fn_rate:.4f} ({fn_samples}/{total_samples})"],
        ["假阳性/假阴性比例", f"{fp_samples / fn_samples:.2f}" if fn_samples > 0 else "N/A"]
    ]

    print(tabulate(table_data, headers=headers, tablefmt="grid"))

    # 对年龄、身高、体重进行分箱处理，与原函数相同
    def age_binning(age):
        if pd.isna(age):
            return '未知'
        elif age < 45:
            return '<45岁'
        elif age < 60:
            return '45-59岁'
        elif age < 75:
            return '60-74岁'
        else:
            return '≥75岁'

    df['年龄分组'] = df['临床特征-年龄'].apply(age_binning)

    def height_binning(height):
        if pd.isna(height):
            return '未知'
        elif height < 160:
            return '<160cm'
        elif height < 170:
            return '160-169cm'
        elif height < 180:
            return '170-179cm'
        else:
            return '≥180cm'

    df['身高分组'] = df['临床特征-身高'].apply(height_binning)

    def weight_binning(weight):
        if pd.isna(weight):
            return '未知'
        elif weight < 60:
            return '<60kg'
        elif weight < 70:
            return '60-69kg'
        elif weight < 80:
            return '70-79kg'
        else:
            return '≥80kg'

    df['体重分组'] = df['临床特征-体重'].apply(weight_binning)

    # 计算临床特征的空缺率，并找出空缺率大于15%的特征
    missing_rate = {}
    high_missing_features = []

    for col in clinical_features:
        missing_count = df[col].isna().sum()
        missing_rate[col] = missing_count / len(df) * 100
        if missing_rate[col] > 15:
            high_missing_features.append(col)
            print(f"{col} 的空缺率为 {missing_rate[col]:.2f}%, 将进行有无分析")

    # 为空缺率高的特征创建有无分组
    for col in high_missing_features:
        binary_col = f"{col}-有无"
        df[binary_col] = df[col].apply(lambda x: "无" if pd.isna(x) else "有")

    # 定义要分析的分组
    basic_groupby_cols = ['心磁质量', '临床特征-所在医院', '人工-标签一致性']
    clinical_groupby_cols = ['临床特征-性别', '年龄分组', '身高分组', '体重分组'] + [col for col in clinical_features if
                                                                                     col not in ['临床特征-性别',
                                                                                                 '临床特征-年龄',
                                                                                                 '临床特征-身高',
                                                                                                 '临床特征-体重']]

    # 添加高空缺率特征的有无分组
    missing_feature_binary_cols = [f"{col}-有无" for col in high_missing_features]

    all_groupby_cols = basic_groupby_cols + clinical_groupby_cols + missing_feature_binary_cols
    error_analysis = {}

    # 准备分组数据收集
    group_plot_data = {}

    # 对各分组进行错误分析
    for col in all_groupby_cols:
        print("\n" + "=" * 60)
        print(f"按【{col}】分组的错误分析")
        print("=" * 60)

        error_analysis[col] = {}

        # 创建表格
        headers = ["分组", "样本数", "正确数", "正确率", "错误数", "错误率", "假阳性(FP)", "假阳性率", "假阴性(FN)",
                   "假阴性率", "FP/FN比例"]
        table_data = []

        # 获取该分组的唯一值并排序
        if col == '年龄分组':
            # 按年龄段顺序排序
            age_order = ['<45岁', '45-59岁', '60-74岁', '≥75岁', '未知']
            group_values = [val for val in age_order if val in df[col].unique()]
        elif col == '身高分组':
            height_order = ['<160cm', '160-169cm', '170-179cm', '≥180cm', '未知']
            group_values = [val for val in height_order if val in df[col].unique()]
        elif col == '体重分组':
            weight_order = ['<60kg', '60-69kg', '70-79kg', '≥80kg', '未知']
            group_values = [val for val in weight_order if val in df[col].unique()]
        elif col in ['临床特征-吸烟', '临床特征-饮酒', '临床特征-高血压', '临床特征-糖尿病',
                     '临床特征-高脂血症', '临床特征-典型症状', '临床特征-既往介入']:
            # 处理可能包含数字和字符串混合的列
            unique_values = df[col].unique()
            # 分别处理数字和字符串
            num_values = [val for val in unique_values if isinstance(val, (int, float)) and not pd.isna(val)]
            str_values = [val for val in unique_values if isinstance(val, str)]
            na_values = [val for val in unique_values if pd.isna(val)]

            # 先对数字排序，再添加字符串，最后添加空值
            group_values = sorted(num_values) + sorted(str_values) + na_values
        else:
            # 尝试排序，如果失败则不排序
            try:
                group_values = sorted(df[col].unique())
            except TypeError:
                group_values = list(df[col].unique())

        for group_val in group_values:
            group_df = df[df[col] == group_val]

            if len(group_df) > 0:
                # 计算各指标
                group_total = len(group_df)
                group_correct = sum(group_df['错误类型'] == 'correct')
                group_error = group_total - group_correct
                group_fp = sum(group_df['错误类型'] == 'false_positive')
                group_fn = sum(group_df['错误类型'] == 'false_negative')

                group_correct_rate = group_correct / group_total if group_total > 0 else 0
                group_error_rate = group_error / group_total if group_total > 0 else 0
                group_fp_rate = group_fp / group_total if group_total > 0 else 0
                group_fn_rate = group_fn / group_total if group_total > 0 else 0
                group_fp_fn_ratio = group_fp / group_fn if group_fn > 0 else float('inf')

                # 添加行数据
                table_data.append([
                    group_val,
                    group_total,
                    group_correct,
                    f"{group_correct_rate:.4f}",
                    group_error,
                    f"{group_error_rate:.4f}",
                    group_fp,
                    f"{group_fp_rate:.4f}",
                    group_fn,
                    f"{group_fn_rate:.4f}",
                    f"{group_fp_fn_ratio:.2f}" if group_fp_fn_ratio != float('inf') else "N/A"
                ])

                # 保存分组错误分析结果
                error_analysis[col][group_val] = {
                    'total': group_total,
                    'correct': group_correct,
                    'error': group_error,
                    'false_positive': group_fp,
                    'false_negative': group_fn,
                    'correct_rate': group_correct_rate,
                    'error_rate': group_error_rate,
                    'fp_rate': group_fp_rate,
                    'fn_rate': group_fn_rate,
                    'fp_fn_ratio': group_fp_fn_ratio if group_fp_fn_ratio != float('inf') else None
                }

        print(tabulate(table_data, headers=headers, tablefmt="grid"))

        # 收集绘图数据（只保存重要特征的数据用于后续合并绘图）
        if (col in basic_groupby_cols or col in ['临床特征-性别', '年龄分组', '身高分组', '体重分组',
                                                 '临床特征-高血压', '临床特征-糖尿病', '临床特征-高脂血症']) and len(
            table_data) > 0:
            groups = [row[0] for row in table_data]
            error_rates = [float(row[5]) for row in table_data]
            fp_rates = [float(row[7]) for row in table_data]
            fn_rates = [float(row[9]) for row in table_data]

            # 将数据保存到字典中
            group_plot_data[col] = {
                'groups': groups,
                'error_rates': error_rates,
                'fp_rates': fp_rates,
                'fn_rates': fn_rates
            }

    # 绘制分组错误率的综合子图
    try:
        print("\n" + "=" * 60)
        print("错误率综合分析（多特征）")
        print("=" * 60)

        # 选择要显示的特征（优先选择样本分布较为均匀的重要特征）
        # 按优先级排序挑选特征
        priority_features = ['心磁质量', '临床特征-所在医院', '临床特征-性别', '年龄分组',
                             '临床特征-高血压', '临床特征-糖尿病', '临床特征-高脂血症']

        # 筛选出存在于group_plot_data中的优先特征
        plot_features = [feat for feat in priority_features if feat in group_plot_data]

        # 限制最多显示6个特征
        if len(plot_features) > 6:
            plot_features = plot_features[:6]

        if len(plot_features) > 0:
            # 计算子图行列数
            n_features = len(plot_features)
            n_cols = min(3, n_features)  # 最多3列
            n_rows = (n_features + n_cols - 1) // n_cols  # 向上取整计算行数

            # 创建大图和子图
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(16, 5 * n_rows))

            # 处理单行或单列的情况
            if n_rows == 1 and n_cols == 1:
                axes = np.array([axes])
            if n_rows == 1:
                axes = axes.reshape(1, -1)
            if n_cols == 1:
                axes = axes.reshape(-1, 1)

            # 在每个子图中绘制对应特征的错误率
            for i, feature in enumerate(plot_features):
                row_idx = i // n_cols
                col_idx = i % n_cols
                ax = axes[row_idx, col_idx]

                data = group_plot_data[feature]
                groups = data['groups']
                error_rates = data['error_rates']
                fp_rates = data['fp_rates']
                fn_rates = data['fn_rates']

                # 创建分组索引
                x = np.arange(len(groups))
                width = 0.25

                # 绘制条形图
                ax.bar(x - width, error_rates, width, label='总错误率', color='#d62728')
                ax.bar(x, fp_rates, width, label='假阳性率', color='#ff7f0e')
                ax.bar(x + width, fn_rates, width, label='假阴性率', color='#1f77b4')

                ax.set_xlabel('分组', fontsize=10)
                ax.set_ylabel('错误率', fontsize=10)
                ax.set_title(f'【{feature}】分组错误率', fontsize=12)
                ax.set_xticks(x)
                ax.set_xticklabels(groups, rotation=45, ha='right', fontsize=8)
                ax.set_ylim(0, max(max(error_rates), max(fp_rates), max(fn_rates)) * 1.2)

                # 简化数值标签，只显示在总错误率上
                for i, v in enumerate(error_rates):
                    ax.text(i - width, v + 0.01, f'{v:.3f}', ha='center', va='bottom', fontsize=6)

                ax.grid(axis='y', linestyle='--', alpha=0.3)

                # 只在第一个子图显示图例
                if row_idx == 0 and col_idx == 0:
                    ax.legend(fontsize=8)

            # 隐藏空白子图
            for i in range(len(plot_features), n_rows * n_cols):
                row_idx = i // n_cols
                col_idx = i % n_cols
                fig.delaxes(axes[row_idx, col_idx])

            plt.tight_layout()
            plt.show()

        else:
            print("没有足够的分组数据用于绘制综合图表")

    except Exception as e:
        print(f"\n警告: 无法生成分组错误率的综合图表: {str(e)}")

    # 多特征组合错误率热力图分析 (整合版)
    # 多特征组合错误率热力图分析 (修复版)
    try:
        print("\n" + "=" * 60)
        print("多特征组合错误率热力图综合分析（修复版）")
        print("=" * 60)

        # 指定特别关注的特征列表
        interested_features = [
            '临床特征-性别', '年龄分组', '临床特征-高血压', '临床特征-糖尿病',
            '临床特征-高脂血症', '临床特征-典型症状', '临床特征-既往介入',
            '身高分组', '体重分组', '临床特征-吸烟', '临床特征-饮酒'
        ]

        # 特别确保这两个特征被包含
        mandatory_features = ['临床特征-所在医院', '人工-标签一致性']

        # 从所有特征中筛选实际存在的特征
        available_features = []
        for feature in interested_features + mandatory_features:
            if feature in df.columns:
                # 检查该特征的唯一值数量，过多或过少都不利于热力图展示
                unique_vals = df[feature].dropna().nunique()
                if 2 <= unique_vals <= 10:  # 限制在2-10个唯一值
                    group_counts = df[feature].value_counts()
                    if group_counts.min() >= 3:  # 确保每个分组至少有3个样本
                        available_features.append(feature)

        # 删除重复项并确保mandatory_features优先出现
        available_features = mandatory_features + [f for f in available_features if f not in mandatory_features]
        available_features = list(dict.fromkeys(available_features))  # 移除重复项

        if len(available_features) < 2:
            print("没有足够的有效特征进行组合分析")
            return

        print(f"将使用以下{len(available_features)}个特征进行热力图分析:")
        for i, feat in enumerate(available_features):
            print(f"{i + 1}. {feat}")

        # 所有可能的特征对组合
        feature_pairs = []

        # 首先添加mandatory_features与其他所有特征的组合
        for m_feat in mandatory_features:
            if m_feat in available_features:
                for other_feat in [f for f in available_features if f != m_feat]:
                    feature_pairs.append((m_feat, other_feat))

        # 然后添加感兴趣特征中两两组合（如果需要）
        interested_avail = [f for f in interested_features if f in available_features]
        if len(interested_avail) >= 2:
            import itertools
            remaining_pairs = list(itertools.combinations(interested_avail, 2))

            # 从remaining_pairs中排除已经在feature_pairs中的组合
            existing_pairs = set([(a, b) for a, b in feature_pairs] + [(b, a) for a, b in feature_pairs])
            remaining_pairs = [(a, b) for a, b in remaining_pairs if
                               (a, b) not in existing_pairs and (b, a) not in existing_pairs]

            feature_pairs.extend(remaining_pairs[:max(0, 15 - len(feature_pairs))])  # 最多添加到总共15对

        # 限制总特征对数量，避免生成过多图表
        if len(feature_pairs) > 15:
            print(f"特征对数量过多，将限制为15对")
            feature_pairs = feature_pairs[:15]

        print(f"\n将分析以下{len(feature_pairs)}对特征组合:")
        for i, (feat1, feat2) in enumerate(feature_pairs):
            print(f"{i + 1}. {feat1} vs {feat2}")

        # 如果有太多热力图，分批次显示
        batch_size = 4
        num_batches = (len(feature_pairs) + batch_size - 1) // batch_size

        # 对每批特征对创建热力图
        for batch in range(num_batches):
            batch_start = batch * batch_size
            batch_end = min((batch + 1) * batch_size, len(feature_pairs))
            current_pairs = feature_pairs[batch_start:batch_end]

            # 计算子图网格
            n_pairs = len(current_pairs)
            n_cols = min(2, n_pairs)  # 最多2列
            n_rows = (n_pairs + n_cols - 1) // n_cols  # 向上取整计算行数

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 7 * n_rows))

            # 处理axes的不同形状情况
            if n_rows == 1 and n_cols == 1:
                axes = np.array([[axes]])
            elif n_rows == 1:
                axes = axes.reshape(1, -1)
            elif n_cols == 1:
                axes = axes.reshape(-1, 1)

            for pair_idx, (feature1, feature2) in enumerate(current_pairs):
                row_idx = pair_idx // n_cols
                col_idx = pair_idx % n_cols
                ax = axes[row_idx, col_idx]

                print(f"\n分析特征组合 {feature1} vs {feature2}:")

                # 获取每个特征的唯一值（包括处理空值）
                # 修复排序问题：先获取所有值，然后分别处理数字和字符串
                try:
                    # 先获取所有唯一值
                    values1_raw = df[feature1].fillna('未知').unique()
                    values2_raw = df[feature2].fillna('未知').unique()

                    # 分类处理 feature1 的值
                    values1 = []
                    # 先检查是不是特殊的有序特征
                    if feature1 == '年龄分组':
                        age_order = ['<45岁', '45-59岁', '60-74岁', '≥75岁', '未知']
                        values1 = [val for val in age_order if val in values1_raw]
                    elif feature1 == '身高分组':
                        height_order = ['<160cm', '160-169cm', '170-179cm', '≥180cm', '未知']
                        values1 = [val for val in height_order if val in values1_raw]
                    elif feature1 == '体重分组':
                        weight_order = ['<60kg', '60-69kg', '70-79kg', '≥80kg', '未知']
                        values1 = [val for val in weight_order if val in values1_raw]
                    else:
                        # 一般处理：分离数字和字符串类型，分别排序后合并
                        num_values = []
                        str_values = []
                        for val in values1_raw:
                            if val == '未知':
                                continue
                            try:
                                # 尝试转换为浮点数
                                num_val = float(val)
                                num_values.append(num_val)
                            except (ValueError, TypeError):
                                # 非数字的就添加到字符串列表
                                str_values.append(val)

                        # 分别排序
                        num_values.sort()
                        str_values.sort()

                        # 合并所有值，先数字，后字符串，最后是'未知'
                        values1 = [str(val) for val in num_values] + str_values
                        if '未知' in values1_raw:
                            values1.append('未知')

                    # 分类处理 feature2 的值
                    values2 = []
                    # 先检查是不是特殊的有序特征
                    if feature2 == '年龄分组':
                        age_order = ['<45岁', '45-59岁', '60-74岁', '≥75岁', '未知']
                        values2 = [val for val in age_order if val in values2_raw]
                    elif feature2 == '身高分组':
                        height_order = ['<160cm', '160-169cm', '170-179cm', '≥180cm', '未知']
                        values2 = [val for val in height_order if val in values2_raw]
                    elif feature2 == '体重分组':
                        weight_order = ['<60kg', '60-69kg', '70-79kg', '≥80kg', '未知']
                        values2 = [val for val in weight_order if val in values2_raw]
                    else:
                        # 一般处理：分离数字和字符串类型，分别排序后合并
                        num_values = []
                        str_values = []
                        for val in values2_raw:
                            if val == '未知':
                                continue
                            try:
                                # 尝试转换为浮点数
                                num_val = float(val)
                                num_values.append(num_val)
                            except (ValueError, TypeError):
                                # 非数字的就添加到字符串列表
                                str_values.append(val)

                        # 分别排序
                        num_values.sort()
                        str_values.sort()

                        # 合并所有值，先数字，后字符串，最后是'未知'
                        values2 = [str(val) for val in num_values] + str_values
                        if '未知' in values2_raw:
                            values2.append('未知')

                    # 确保我们有值可用于热力图
                    if not values1:
                        values1 = ['未知'] if '未知' in values1_raw else [str(v) for v in values1_raw]

                    if not values2:
                        values2 = ['未知'] if '未知' in values2_raw else [str(v) for v in values2_raw]

                    # 创建热力图数据
                    error_matrix = np.zeros((len(values1), len(values2)))
                    fp_matrix = np.zeros((len(values1), len(values2)))
                    fn_matrix = np.zeros((len(values1), len(values2)))
                    sample_counts = np.zeros((len(values1), len(values2)))
                    confidence_matrix = np.zeros((len(values1), len(values2)))  # 添加置信度矩阵

                    # 填充热力图数据
                    for i, val1 in enumerate(values1):
                        for j, val2 in enumerate(values2):
                            # 考虑空值情况
                            if val1 == '未知':
                                group_df1 = df[df[feature1].isna()]
                            else:
                                # 需要处理数字和字符串的比较
                                try:
                                    num_val1 = float(val1)
                                    # 如果能转为数字，就按数字筛选
                                    group_df1 = df[df[feature1] == num_val1]
                                    if len(group_df1) == 0:  # 如果没找到，按字符串查找
                                        group_df1 = df[df[feature1] == val1]
                                except ValueError:
                                    # 不能转为数字，按原值筛选
                                    group_df1 = df[df[feature1] == val1]

                            if val2 == '未知':
                                group_df2 = group_df1[group_df1[feature2].isna()]
                            else:
                                # 需要处理数字和字符串的比较
                                try:
                                    num_val2 = float(val2)
                                    # 如果能转为数字，就按数字筛选
                                    group_df2 = group_df1[group_df1[feature2] == num_val2]
                                    if len(group_df2) == 0:  # 如果没找到，按字符串查找
                                        group_df2 = group_df1[group_df1[feature2] == val2]
                                except ValueError:
                                    # 不能转为数字，按原值筛选
                                    group_df2 = group_df1[group_df1[feature2] == val2]

                            group_total = len(group_df2)
                            sample_counts[i, j] = group_total

                            # 只有当样本数量足够时才计算错误率
                            min_samples = 3  # 降低阈值以显示更多数据
                            if group_total >= min_samples:
                                group_error = sum(group_df2['错误类型'] != 'correct')
                                group_fp = sum(group_df2['错误类型'] == 'false_positive')
                                group_fn = sum(group_df2['错误类型'] == 'false_negative')

                                error_rate = group_error / group_total
                                fp_rate = group_fp / group_total if group_total > 0 else 0
                                fn_rate = group_fn / group_total if group_total > 0 else 0

                                # 计算平均置信度 (与0.5的距离)
                                if '预测置信度' not in group_df2.columns:
                                    group_df2['预测置信度'] = np.abs(group_df2['predicted_prob'] - 0.5)

                                avg_confidence = group_df2['预测置信度'].mean()

                                error_matrix[i, j] = error_rate
                                fp_matrix[i, j] = fp_rate
                                fn_matrix[i, j] = fn_rate
                                confidence_matrix[i, j] = avg_confidence
                            else:
                                error_matrix[i, j] = np.nan
                                fp_matrix[i, j] = np.nan
                                fn_matrix[i, j] = np.nan
                                confidence_matrix[i, j] = np.nan

                    # 创建掩码
                    mask = np.isnan(error_matrix)

                    # 创建注释矩阵
                    annot_matrix = np.zeros((len(values1), len(values2)), dtype=object)
                    for i in range(len(values1)):
                        for j in range(len(values2)):
                            if not np.isnan(error_matrix[i, j]):
                                error = error_matrix[i, j]
                                fp = fp_matrix[i, j]
                                fn = fn_matrix[i, j]
                                count = int(sample_counts[i, j])
                                conf = confidence_matrix[i, j]

                                # 简化标签以适应更小的格子
                                annot_matrix[i, j] = f'E:{error:.2f}\nF:{fp:.2f}/{fn:.2f}\nN:{count}'
                            else:
                                annot_matrix[i, j] = ''

                    # 绘制热力图
                    im = sns.heatmap(error_matrix, annot=annot_matrix, fmt='',
                                     cmap='YlOrRd', mask=mask, annot_kws={"size": 7},
                                     xticklabels=values2, yticklabels=values1,
                                     cbar_kws={'label': '错误率'}, ax=ax)

                    # 为了提高可读性，调整x轴和y轴标签
                    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha="right", fontsize=8)
                    ax.set_yticklabels(ax.get_yticklabels(), rotation=0, fontsize=8)

                    # 添加边框标记:
                    # 红色表示高错误率，黄色表示小样本量
                    for i in range(len(values1)):
                        for j in range(len(values2)):
                            if not np.isnan(error_matrix[i, j]):
                                # 样本数少于10的用黄色边框标记
                                if sample_counts[i, j] < 10:
                                    ax.add_patch(plt.Rectangle((j, i), 1, 1, fill=False, edgecolor='yellow', lw=1.5))

                                # 错误率高于平均值的用红色边框标记
                                valid_errors = error_matrix[~np.isnan(error_matrix)]
                                if len(valid_errors) > 0:
                                    mean_error = np.mean(valid_errors)
                                    if error_matrix[i, j] > mean_error:
                                        ax.add_patch(plt.Rectangle((j, i), 1, 1, fill=False, edgecolor='red', lw=1.5))

                    ax.set_title(f'{feature1} 和 {feature2} 组合的错误率分析', fontsize=10)
                    ax.set_xlabel(feature2, fontsize=9)
                    ax.set_ylabel(feature1, fontsize=9)

                except Exception as e:
                    ax.text(0.5, 0.5, f"无法分析该组合\n{str(e)}",
                            ha='center', va='center', fontsize=10, transform=ax.transAxes,
                            bbox=dict(facecolor='yellow', alpha=0.3))
                    ax.set_title(f'{feature1} 和 {feature2} 组合分析失败', fontsize=10)
                    print(f"处理 {feature1} vs {feature2} 时出错: {str(e)}")

            # 隐藏空白子图
            for i in range(len(current_pairs), n_rows * n_cols):
                row_idx = i // n_cols
                col_idx = i % n_cols
                fig.delaxes(axes[row_idx, col_idx])

            # 添加全局标题和图例说明
            fig.suptitle('多特征组合错误率热力图分析\n(红框=高错误率, 黄框=小样本量<10)', fontsize=14, y=1.02)
            fig.text(0.5, 0.01, 'E=错误率, F=假阳性/假阴性率, N=样本数', ha='center', fontsize=10)

            plt.tight_layout()
            plt.subplots_adjust(top=0.9)  # 为全局标题留出空间
            plt.show()

            print(f"已展示第{batch + 1}/{num_batches}批热力图")

        # 额外添加一个针对医院和人工标签一致性的详细热力图
        # 这个热力图会更大、更详细
        try:
            if '临床特征-所在医院' in df.columns and '人工-标签一致性' in df.columns:
                print("\n" + "=" * 60)
                print("医院与人工标签一致性的详细热力图分析")
                print("=" * 60)

                # 获取唯一值
                hospitals_raw = df['临床特征-所在医院'].fillna('未知').unique()
                consistencies_raw = df['人工-标签一致性'].fillna('未知').unique()

                # 处理医院数据
                hospitals = []
                num_hospitals = []
                str_hospitals = []

                for h in hospitals_raw:
                    if h == '未知':
                        continue
                    try:
                        num_h = float(h)
                        num_hospitals.append(num_h)
                    except (ValueError, TypeError):
                        str_hospitals.append(h)

                num_hospitals.sort()
                str_hospitals.sort()

                hospitals = [str(h) for h in num_hospitals] + str_hospitals
                if '未知' in hospitals_raw:
                    hospitals.append('未知')

                # 处理一致性数据
                consistencies = [c for c in consistencies_raw if c != '未知']
                consistencies.sort()
                if '未知' in consistencies_raw:
                    consistencies.append('未知')

                # 创建热力图数据
                error_matrix = np.zeros((len(hospitals), len(consistencies)))
                sample_counts = np.zeros((len(hospitals), len(consistencies)))

                # 填充数据
                for i, hospital in enumerate(hospitals):
                    for j, consistency in enumerate(consistencies):
                        if hospital == '未知':
                            hosp_df = df[df['临床特征-所在医院'].isna()]
                        else:
                            try:
                                num_hosp = float(hospital)
                                hosp_df = df[df['临床特征-所在医院'] == num_hosp]
                                if len(hosp_df) == 0:
                                    hosp_df = df[df['临床特征-所在医院'] == hospital]
                            except ValueError:
                                hosp_df = df[df['临床特征-所在医院'] == hospital]

                        if consistency == '未知':
                            group_df = hosp_df[hosp_df['人工-标签一致性'].isna()]
                        else:
                            group_df = hosp_df[hosp_df['人工-标签一致性'] == consistency]

                        group_total = len(group_df)
                        sample_counts[i, j] = group_total

                        if group_total >= 3:
                            group_error = sum(group_df['错误类型'] != 'correct')
                            error_rate = group_error / group_total
                            error_matrix[i, j] = error_rate
                        else:
                            error_matrix[i, j] = np.nan

                # 创建详细的注释矩阵
                mask = np.isnan(error_matrix)
                detailed_annot = np.zeros((len(hospitals), len(consistencies)), dtype=object)

                for i in range(len(hospitals)):
                    for j in range(len(consistencies)):
                        if not np.isnan(error_matrix[i, j]):
                            count = int(sample_counts[i, j])
                            error = error_matrix[i, j]

                            # 计算FP和FN
                            if hospitals[i] == '未知':
                                hosp_df = df[df['临床特征-所在医院'].isna()]
                            else:
                                try:
                                    num_hosp = float(hospitals[i])
                                    hosp_df = df[df['临床特征-所在医院'] == num_hosp]
                                    if len(hosp_df) == 0:
                                        hosp_df = df[df['临床特征-所在医院'] == hospitals[i]]
                                except ValueError:
                                    hosp_df = df[df['临床特征-所在医院'] == hospitals[i]]

                            if consistencies[j] == '未知':
                                group_df = hosp_df[hosp_df['人工-标签一致性'].isna()]
                            else:
                                group_df = hosp_df[hosp_df['人工-标签一致性'] == consistencies[j]]

                            fp = sum(group_df['错误类型'] == 'false_positive') / count if count > 0 else 0
                            fn = sum(group_df['错误类型'] == 'false_negative') / count if count > 0 else 0

                            detailed_annot[i, j] = f'错误率: {error:.2f}\nFP: {fp:.2f} FN: {fn:.2f}\n样本数: {count}'
                        else:
                            detailed_annot[i, j] = ''

                # 创建大图
                plt.figure(figsize=(12, max(8, len(hospitals) * 0.5)))

                # 绘制热力图
                sns.heatmap(error_matrix, annot=detailed_annot, fmt='',
                            cmap='YlOrRd', mask=mask, annot_kws={"size": 9},
                            xticklabels=consistencies, yticklabels=hospitals,
                            cbar_kws={'label': '错误率'})

                # 添加边框标记
                valid_errors = error_matrix[~np.isnan(error_matrix)]
                if len(valid_errors) > 0:
                    mean_error = np.mean(valid_errors)

                    for i in range(len(hospitals)):
                        for j in range(len(consistencies)):
                            if not np.isnan(error_matrix[i, j]):
                                # 样本数少于10的用黄色边框标记
                                if sample_counts[i, j] < 10:
                                    plt.gca().add_patch(
                                        plt.Rectangle((j, i), 1, 1, fill=False, edgecolor='yellow', lw=2))

                                # 错误率高于平均值的用红色边框标记
                                if error_matrix[i, j] > mean_error:
                                    plt.gca().add_patch(plt.Rectangle((j, i), 1, 1, fill=False, edgecolor='red', lw=2))

                plt.title('医院与人工标签一致性的错误率分析\n(红框=高错误率, 黄框=小样本量<10)', fontsize=14)
                plt.xlabel('人工-标签一致性', fontsize=12)
                plt.ylabel('临床特征-所在医院', fontsize=12)
                plt.tight_layout()
                plt.show()
        except Exception as e:
            print(f"\n警告: 医院与人工标签一致性分析失败: {str(e)}")

        print("\n热力图分析完成！")

    except Exception as e:
        print(f"\n警告: 多特征组合分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

    # 错误样本的概率分布和置信度分析（合并为一个图表）
    try:
        print("\n" + "=" * 60)
        print("错误预测样本的概率分布与置信度分析")
        print("=" * 60)

        # 创建2行2列的子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 预测概率分布 (左上)
        # 分别绘制正确和错误预测样本的概率分布
        sns.histplot(df[df['错误类型'] == 'correct']['predicted_prob'],
                     bins=20, alpha=0.6, color='green', label='正确预测', kde=True, ax=axes[0, 0])

        # 提取错误样本
        error_df = df[df['错误类型'] != 'correct']
        fp_df = error_df[error_df['错误类型'] == 'false_positive']
        fn_df = error_df[error_df['错误类型'] == 'false_negative']

        # 绘制FP和FN的概率分布
        if len(fp_df) > 0:
            sns.histplot(fp_df['predicted_prob'], bins=20, alpha=0.6, color='orange',
                         label='假阳性(FP)', kde=True, ax=axes[0, 0])

        if len(fn_df) > 0:
            sns.histplot(fn_df['predicted_prob'], bins=20, alpha=0.6, color='blue',
                         label='假阴性(FN)', kde=True, ax=axes[0, 0])

        axes[0, 0].set_xlabel('预测概率', fontsize=12)
        axes[0, 0].set_ylabel('样本数量', fontsize=12)
        axes[0, 0].set_title('不同类型样本的预测概率分布', fontsize=14)
        axes[0, 0].legend()
        axes[0, 0].grid(alpha=0.3)

        # 2. 预测概率与真实标签关系图 (右上)
        # 散点图：x轴为预测概率，y轴为真实标签（抖动处理）
        correct_df = df[df['错误类型'] == 'correct']

        # 为了可视化效果，对y值添加少量随机抖动
        np.random.seed(42)  # 设置随机种子以确保结果可复现
        jitter = 0.05

        # 绘制正确预测的样本
        axes[0, 1].scatter(correct_df['predicted_prob'],
                           correct_df['label'] + np.random.uniform(-jitter, jitter, size=len(correct_df)),
                           alpha=0.5, color='green', label='正确预测')

        # 绘制假阳性样本
        if len(fp_df) > 0:
            axes[0, 1].scatter(fp_df['predicted_prob'],
                               fp_df['label'] + np.random.uniform(-jitter, jitter, size=len(fp_df)),
                               alpha=0.7, color='orange', marker='x', s=50, label='假阳性(FP)')

        # 绘制假阴性样本
        if len(fn_df) > 0:
            axes[0, 1].scatter(fn_df['predicted_prob'],
                               fn_df['label'] + np.random.uniform(-jitter, jitter, size=len(fn_df)),
                               alpha=0.7, color='blue', marker='x', s=50, label='假阴性(FN)')

        # 绘制决策阈值线
        axes[0, 1].axvline(x=0.5, color='red', linestyle='--', alpha=0.7, label='决策阈值(0.5)')

        axes[0, 1].set_xlabel('预测概率', fontsize=12)
        axes[0, 1].set_ylabel('真实标签 (有抖动)', fontsize=12)
        axes[0, 1].set_title('预测概率与真实标签的关系', fontsize=14)
        axes[0, 1].set_yticks([0, 1])
        axes[0, 1].set_yticklabels(['阴性(0)', '阳性(1)'])
        axes[0, 1].legend()
        axes[0, 1].grid(alpha=0.3)

        # 3. 计算预测置信度分析 (左下)
        # 计算预测置信度（与0.5的距离）
        df['预测置信度'] = np.abs(df['predicted_prob'] - 0.5)

        # 按置信度将样本分为高、中、低三组
        confidence_bins = [0, 0.15, 0.3, 0.5]
        confidence_labels = ['低置信度(0-0.15)', '中置信度(0.15-0.3)', '高置信度(0.3-0.5)']

        df['置信度分组'] = pd.cut(df['预测置信度'], bins=confidence_bins, labels=confidence_labels)

        # 计算各置信度组的错误率
        conf_error_rates = []

        for conf_group in confidence_labels:
            group_df = df[df['置信度分组'] == conf_group]
            if len(group_df) > 0:
                group_error = sum(group_df['错误类型'] != 'correct')
                group_fp = sum(group_df['错误类型'] == 'false_positive')
                group_fn = sum(group_df['错误类型'] == 'false_negative')

                error_rate = group_error / len(group_df)
                fp_rate = group_fp / len(group_df)
                fn_rate = group_fn / len(group_df)

                conf_error_rates.append([
                    conf_group,
                    len(group_df),
                    group_error,
                    f"{error_rate:.4f}",
                    group_fp,
                    f"{fp_rate:.4f}",
                    group_fn,
                    f"{fn_rate:.4f}"
                ])

        # 提取数据
        conf_groups = [row[0] for row in conf_error_rates]
        error_rates = [float(row[3]) for row in conf_error_rates]
        fp_rates = [float(row[5]) for row in conf_error_rates]
        fn_rates = [float(row[7]) for row in conf_error_rates]

        # 创建分组索引
        x = np.arange(len(conf_groups))
        width = 0.25

        # 绘制条形图
        axes[1, 0].bar(x - width, error_rates, width, label='总错误率', color='#d62728')
        axes[1, 0].bar(x, fp_rates, width, label='假阳性率(FP)', color='#ff7f0e')
        axes[1, 0].bar(x + width, fn_rates, width, label='假阴性率(FN)', color='#1f77b4')

        axes[1, 0].set_xlabel('预测置信度', fontsize=12)
        axes[1, 0].set_ylabel('错误率', fontsize=12)
        axes[1, 0].set_title('预测置信度与错误率的关系', fontsize=14)
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(conf_groups)

        # 添加数值标签
        for i, v in enumerate(error_rates):
            axes[1, 0].text(i - width, v + 0.01, f'{v}', ha='center', va='bottom', fontsize=10)
        for i, v in enumerate(fp_rates):
            axes[1, 0].text(i, v + 0.01, f'{v}', ha='center', va='bottom', fontsize=10)
        for i, v in enumerate(fn_rates):
            axes[1, 0].text(i + width, v + 0.01, f'{v}', ha='center', va='bottom', fontsize=10)

        axes[1, 0].legend()
        axes[1, 0].grid(axis='y', linestyle='--', alpha=0.3)

        # 4. 置信度的样本分布 (右下)
        # 展示各置信度区间的样本数量和错误率
        sample_counts = [int(row[1]) for row in conf_error_rates]
        error_counts = [int(row[2]) for row in conf_error_rates]

        # 创建堆叠条形图
        correct_counts = [s - e for s, e in zip(sample_counts, error_counts)]

        # 绘制堆叠条形图
        axes[1, 1].bar(conf_groups, correct_counts, color='green', alpha=0.7, label='正确预测样本')
        axes[1, 1].bar(conf_groups, error_counts, bottom=correct_counts, color='red', alpha=0.7, label='错误预测样本')

        # 添加数值标签和错误率
        for i, (c, e, rate) in enumerate(zip(correct_counts, error_counts, error_rates)):
            total = c + e
            # 样本总数
            axes[1, 1].text(i, total / 2, f'总数: {total}\n错误率: {rate:.2f}',
                            ha='center', va='center', fontsize=10, color='black')

        axes[1, 1].set_xlabel('预测置信度', fontsize=12)
        axes[1, 1].set_ylabel('样本数量', fontsize=12)
        axes[1, 1].set_title('不同置信度区间的样本分布', fontsize=14)
        axes[1, 1].legend()
        axes[1, 1].grid(axis='y', linestyle='--', alpha=0.3)

        plt.tight_layout()
        plt.show()

        # 显示置信度与错误率的关系表格
        print("\n预测置信度与错误率的关系:")

        conf_headers = ["置信度分组", "样本数", "错误数", "错误率", "假阳性数", "假阳性率", "假阴性数", "假阴性率"]
        print(tabulate(conf_error_rates, headers=conf_headers, tablefmt="grid"))

        # 计算错误样本的预测概率统计量
        print("\n预测概率统计:")

        # 计算各组的统计量
        correct_stats = df[df['错误类型'] == 'correct']['predicted_prob'].describe()

        if len(fp_df) > 0:
            fp_stats = fp_df['predicted_prob'].describe()
        else:
            fp_stats = pd.Series([np.nan] * 8, index=['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max'])

        if len(fn_df) > 0:
            fn_stats = fn_df['predicted_prob'].describe()
        else:
            fn_stats = pd.Series([np.nan] * 8, index=['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max'])

        # 创建统计量表格
        stats_table = []
        stats_headers = ["统计量", "正确预测", "假阳性(FP)", "假阴性(FN)"]

        for stat in ['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max']:
            stats_table.append([
                stat,
                f"{correct_stats[stat]:.4f}" if stat != 'count' else int(correct_stats[stat]),
                f"{fp_stats[stat]:.4f}" if stat != 'count' and not np.isnan(fp_stats[stat]) else int(
                    fp_stats[stat]) if stat == 'count' else "N/A",
                f"{fn_stats[stat]:.4f}" if stat != 'count' and not np.isnan(fn_stats[stat]) else int(
                    fn_stats[stat]) if stat == 'count' else "N/A"
            ])

        print(tabulate(stats_table, headers=stats_headers, tablefmt="grid"))

    except Exception as e:
        print(f"\n警告: 无法完成错误样本的概率分布与置信度分析: {str(e)}")

    # 返回错误分析结果
    return {
        'overall_error': {
            'total_samples': total_samples,
            'correct_samples': correct_samples,
            'error_samples': fp_samples + fn_samples,
            'false_positive': fp_samples,
            'false_negative': fn_samples,
            'error_rate': error_rate,
            'fp_rate': fp_rate,
            'fn_rate': fn_rate
        },
        'group_error_analysis': error_analysis
    }


def visualize_subgroups(result_df, external_excel_path, sheet_name='测试集', interested_features=None):
    """
    对模型预测的子群体进行高级可视化分析，呈现错误分布模式

    参数:
    result_df: 包含预测结果的DataFrame
    external_excel_path: 外部Excel文件路径，包含测试集信息
    sheet_name: Excel文件中的sheet名称，默认为'测试集'
    interested_features: 感兴趣的特征列表，如果提供，只分析这些特征的组合

    返回:
    dict: 包含子群体分析结果的字典
    """
    import pandas as pd
    import numpy as np
    from tabulate import tabulate
    import matplotlib.pyplot as plt
    import seaborn as sns
    import warnings
    warnings.filterwarnings('ignore')  # 忽略可视化相关警告

    print("\n" + "=" * 60)
    print("子群体错误分析高级可视化")
    print("=" * 60)

    # 读取外部xlsx文件中的测试集sheet
    print(f"正在读取外部文件: {external_excel_path}")
    external_df = pd.read_excel(external_excel_path, sheet_name=sheet_name)

    # 处理外验集情况
    if sheet_name == '外验集':
        other_df = pd.read_excel(
            'D:\projects_python\对数据进行5折交叉验证的数据划分\split_20250222\\uploads\\20250227_20250227_140200.xlsx',
            sheet_name='总表')
        mask = external_df['造影结论'].isna()
        external_df.loc[mask, '造影结论'] = external_df[mask]['心磁号'].map(
            other_df.set_index('心磁号')['造影结论'])

    # 选择所有需要的临床特征列
    clinical_features = [
        '临床特征-性别', '临床特征-身高', '临床特征-体重', '临床特征-年龄', '临床特征-吸烟', '临床特征-饮酒',
        '临床特征-高血压', '临床特征-糖尿病', '临床特征-高脂血症',
        '临床特征-典型症状', '临床特征-既往介入'
    ]

    # 合并dataframe，包含之前的列和新增的临床特征列
    all_columns = ['心磁号', '心磁质量', '人工', '临床特征-所在医院'] + clinical_features
    df = pd.merge(result_df, external_df[all_columns], on='心磁号', how='left')
    df['造影结论'] = external_df['造影结论']

    # 将造影结论转为数值标签
    df['label'] = (df['造影结论'] == 1).astype(int)

    # 创建人工-标签一致性字段
    df['人工-标签一致性'] = (df['人工'] == df['造影结论']).map({True: '一致', False: '不一致'})

    # 添加错误类型字段 (FP和FN)
    df['错误类型'] = 'correct'  # 默认为正确

    # 标记假阳性(FP): 预测为1，实际为0
    df.loc[(df['predicted_class'] == 1) & (df['label'] == 0), '错误类型'] = 'false_positive'

    # 标记假阴性(FN): 预测为0，实际为1
    df.loc[(df['predicted_class'] == 0) & (df['label'] == 1), '错误类型'] = 'false_negative'

    # 对年龄进行分箱处理
    def age_binning(age):
        if pd.isna(age):
            return '未知'
        elif age < 45:
            return '<45岁'
        elif age < 60:
            return '45-59岁'
        elif age < 75:
            return '60-74岁'
        else:
            return '≥75岁'

    df['年龄分组'] = df['临床特征-年龄'].apply(age_binning)

    # 对身高进行分箱处理
    def height_binning(height):
        if pd.isna(height):
            return '未知'
        elif height < 160:
            return '<160cm'
        elif height < 170:
            return '160-169cm'
        elif height < 180:
            return '170-179cm'
        else:
            return '≥180cm'

    df['身高分组'] = df['临床特征-身高'].apply(height_binning)

    # 对体重进行分箱处理
    def weight_binning(weight):
        if pd.isna(weight):
            return '未知'
        elif weight < 60:
            return '<60kg'
        elif weight < 70:
            return '60-69kg'
        elif weight < 80:
            return '70-79kg'
        else:
            return '≥80kg'

    df['体重分组'] = df['临床特征-体重'].apply(weight_binning)

    # 计算预测置信度（与0.5的距离）
    df['预测置信度'] = np.abs(df['predicted_prob'] - 0.5)

    # 计算临床特征的空缺率，并找出空缺率大于15%的特征
    missing_rate = {}
    high_missing_features = []

    for col in clinical_features:
        missing_count = df[col].isna().sum()
        missing_rate[col] = missing_count / len(df) * 100
        if missing_rate[col] > 15:
            high_missing_features.append(col)
            print(f"{col} 的空缺率为 {missing_rate[col]:.2f}%, 将进行有无分析")

    # 为空缺率高的特征创建有无分组
    for col in high_missing_features:
        # 创建新的有无列，有值的为"有"，空值的为"无"
        binary_col = f"{col}-有无"
        df[binary_col] = df[col].apply(lambda x: "无" if pd.isna(x) else "有")

    # 定义要分析的分组
    basic_groupby_cols = ['心磁质量', '临床特征-所在医院', '人工-标签一致性']
    clinical_groupby_cols = ['临床特征-性别', '年龄分组', '身高分组', '体重分组'] + [col for col in clinical_features if
                                                                                     col not in ['临床特征-性别',
                                                                                                 '临床特征-年龄',
                                                                                                 '临床特征-身高',
                                                                                                 '临床特征-体重']]

    # 添加高空缺率特征的有无分组
    missing_feature_binary_cols = [f"{col}-有无" for col in high_missing_features]

    all_groupby_cols = basic_groupby_cols + clinical_groupby_cols + missing_feature_binary_cols

    # 优先选择的重要特征
    if interested_features is not None:
        # 使用用户提供的特征列表
        priority_features = interested_features
        print(f"将只分析以下{len(priority_features)}个指定特征:")
        for i, feat in enumerate(priority_features):
            print(f"{i + 1}. {feat}")
    else:
        # 默认特征列表
        priority_features = ['临床特征-性别', '年龄分组', '临床特征-高血压', '临床特征-糖尿病',
                             '临床特征-高脂血症', '心磁质量', '临床特征-典型症状', '临床特征-既往介入',
                             '身高分组', '体重分组', '临床特征-吸烟', '临床特征-饮酒',
                             '临床特征-所在医院', '人工-标签一致性']

    # 筛选符合要求的特征
    valid_features = []
    for col in priority_features:
        if col in all_groupby_cols:
            # 检查该特征的分组数量，限制在2-6个分组，确保可视化效果
            group_counts = df[col].value_counts()
            if 2 <= len(group_counts) <= 6 and min(group_counts) >= 5:
                valid_features.append(col)
                if len(valid_features) >= 6:  # 最多选择6个特征
                    break

    # 确保特征足够
    if len(valid_features) < 2:
        print("没有足够的有效特征进行组合分析")
        return {}

    # 得到所有可能的特征对组合
    import itertools
    feature_pairs = list(itertools.combinations(valid_features, 2))

    # 限制分析的特征对数量
    if len(feature_pairs) > 8:
        feature_pairs = feature_pairs[:8]

    print(f"将分析以下{len(feature_pairs)}对特征组合:")
    for i, (feat1, feat2) in enumerate(feature_pairs):
        print(f"{i + 1}. {feat1} vs {feat2}")

    # 收集所有子群体的数据
    all_subgroups = []

    # 遍历所有特征对组合
    for pair_idx, (feature1, feature2) in enumerate(feature_pairs):
        # 遍历所有可能的特征值组合
        for val1 in df[feature1].fillna('未知').unique():
            for val2 in df[feature2].fillna('未知').unique():
                # 提取该子群体
                if val1 == '未知':
                    group_df1 = df[df[feature1].isna()]
                else:
                    group_df1 = df[df[feature1] == val1]

                if val2 == '未知':
                    group_df2 = group_df1[group_df1[feature2].isna()]
                else:
                    group_df2 = group_df1[group_df1[feature2] == val2]

                group_total = len(group_df2)

                # 只有当样本数量足够时才添加
                if group_total >= 5:
                    group_error = sum(group_df2['错误类型'] != 'correct')
                    group_fp = sum(group_df2['错误类型'] == 'false_positive')
                    group_fn = sum(group_df2['错误类型'] == 'false_negative')

                    error_rate = group_error / group_total
                    fp_rate = group_fp / group_total
                    fn_rate = group_fn / group_total

                    # 计算平均置信度
                    avg_confidence = group_df2['预测置信度'].mean()

                    display_val1 = "未知" if pd.isna(val1) or val1 == '未知' else val1
                    display_val2 = "未知" if pd.isna(val2) or val2 == '未知' else val2

                    # 创建子群体名称
                    subgroup_name = f"{feature1}={display_val1}, {feature2}={display_val2}"

                    all_subgroups.append({
                        'subgroup': subgroup_name,
                        'feature1': feature1,
                        'value1': display_val1,
                        'feature2': feature2,
                        'value2': display_val2,
                        'sample_count': group_total,
                        'error_count': group_error,
                        'error_rate': error_rate,
                        'fp_rate': fp_rate,
                        'fn_rate': fn_rate,
                        'avg_confidence': avg_confidence
                    })

    # 创建子群体DataFrame
    subgroups_df = pd.DataFrame(all_subgroups)

    if len(subgroups_df) == 0:
        print("未找到有效的子群体，无法进行分析")
        return {}

    # 按错误率排序
    subgroups_df = subgroups_df.sort_values('error_rate', ascending=False)

    # =====================================================================
    # 1. 平行坐标图分析
    # =====================================================================
    print("\n" + "=" * 60)
    print("1. 平行坐标图多特征分析")
    print("=" * 60)

    try:
        # 筛选有足够样本的子群体数据
        filtered_subgroups = subgroups_df[subgroups_df['sample_count'] >= 20].copy()

        if len(filtered_subgroups) >= 5:
            # 准备平行坐标图数据
            parallel_data = []

            for idx, row in filtered_subgroups.iterrows():
                # 提取特征1和特征2的值
                feature1, value1 = row['feature1'], row['value1']
                feature2, value2 = row['feature2'], row['value2']

                # 简化特征名称以便显示
                feature1_short = feature1.replace('临床特征-', '')
                feature2_short = feature2.replace('临床特征-', '')

                # 创建子群体记录
                record = {
                    'subgroup_id': f"SG{idx}",
                    'subgroup': f"{feature1_short}={value1}, {feature2_short}={value2}",
                    'error_rate': row['error_rate'],
                    'sample_count': row['sample_count'],
                    'avg_confidence': row['avg_confidence'],
                    feature1_short: value1,
                    feature2_short: value2
                }

                parallel_data.append(record)

            # 创建平行坐标图DataFrame
            parallel_df = pd.DataFrame(parallel_data)

            # 提取所有唯一特征
            all_features = set()
            for record in parallel_data:
                all_features.update(record.keys())

            # 移除非特征列
            feature_cols = [f for f in all_features if f not in
                            ['subgroup_id', 'subgroup', 'error_rate',
                             'sample_count', 'avg_confidence']]

            # 只保留有多个值的特征
            feature_cols = [f for f in feature_cols if len(parallel_df[f].unique()) > 1]

            if len(feature_cols) >= 2:
                # 绘制平行坐标图
                plt.figure(figsize=(14, 9))  # 增加高度以适应底部的colorbar

                # 使用错误率作为颜色映射
                cmap = plt.cm.YlOrRd
                norm = plt.Normalize(parallel_df['error_rate'].min(),
                                     parallel_df['error_rate'].max())

                # 为每个子群体绘制一条线
                for i, (idx, row) in enumerate(parallel_df.iterrows()):
                    # 提取特征值和错误率
                    feature_values = [row[f] for f in feature_cols]
                    error_rate = row['error_rate']
                    sample_count = row['sample_count']

                    # 设置线宽根据样本数，颜色根据错误率
                    lw = 1 + 4 * (sample_count / parallel_df['sample_count'].max())
                    color = cmap(norm(error_rate))

                    # 绘制线
                    plt.plot(range(len(feature_cols)), feature_values,
                             linewidth=lw, color=color, alpha=0.7)

                # 只为前10个高错误率子群体添加标签，避免拥挤
                top10_df = parallel_df.sort_values('error_rate', ascending=False).head(10)
                for i, (idx, row) in enumerate(top10_df.iterrows()):
                    feature_values = [row[f] for f in feature_cols]
                    error_rate = row['error_rate']
                    sample_count = row['sample_count']
                    color = cmap(norm(error_rate))

                    # 添加文本标签 - 在右侧
                    plt.text(len(feature_cols) - 1 + 0.1, feature_values[-1],
                             f"SG{i + 1}: e={error_rate:.2f}, n={int(sample_count)}",
                             fontsize=8, weight='bold',  # color=color,
                             ha='left', va='center')

                # 设置坐标轴
                plt.xticks(range(len(feature_cols)), feature_cols, rotation=45)
                plt.grid(True, alpha=0.3)

                # 添加颜色条 - 放在底部
                ax = plt.gca()
                sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
                sm.set_array([])
                cbar = plt.colorbar(sm, orientation='horizontal', pad=0.15)
                cbar.set_label('错误率', fontsize=10)

                # 使用简化图例，显示在左上方
                plt.figtext(0.01, 0.99, "线宽 ∝ 样本数量\n颜色 = 错误率",
                            fontsize=10, ha="left", va="top",
                            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

                plt.title('子群体特征值与错误率的平行坐标图', fontsize=14)

                # 保留右边和上边的空间，确保文本标签可见
                plt.subplots_adjust(right=0.85, top=0.9)
                plt.show()

                # 输出子群体ID对照表
                print("\n子群体ID对照表:")
                id_table = []
                for idx, row in parallel_df.iterrows():
                    id_table.append([
                        row['subgroup_id'],
                        row['subgroup'],
                        int(row['sample_count']),
                        f"{row['error_rate']:.4f}"
                    ])

                print(tabulate(id_table,
                               headers=["ID", "子群体描述", "样本数", "错误率"],
                               tablefmt="grid"))
            else:
                print("没有足够的多值特征用于平行坐标图")
        else:
            print("没有足够的子群体用于平行坐标图分析")
    except Exception as e:
        print(f"平行坐标图分析出错: {str(e)}")

    # =====================================================================
    # 2. 树状图分析
    # =====================================================================
    print("\n" + "=" * 60)
    print("2. 子群体错误率树状图分析")
    print("=" * 60)

    try:
        # 尝试导入squarify库
        try:
            import squarify
        except ImportError:
            print("squarify库未安装，无法绘制树状图。可以使用pip安装: pip install squarify")
            raise ImportError("squarify库未安装")

        # 筛选有足够样本的子群体
        filtered_subgroups = subgroups_df[subgroups_df['sample_count'] >= 15].copy()

        if len(filtered_subgroups) > 0:
            # 按错误率排序
            filtered_subgroups = filtered_subgroups.sort_values('error_rate', ascending=False)

            # 限制最多显示30个子群体
            if len(filtered_subgroups) > 30:
                display_subgroups = pd.concat([
                    filtered_subgroups.head(20),  # 前20个高错误率
                    filtered_subgroups.tail(10)  # 后10个低错误率
                ])
                display_subgroups = display_subgroups.sort_values('error_rate', ascending=False)
            else:
                display_subgroups = filtered_subgroups

            # 准备树状图数据
            labels = [f"{i + 1}.{row['subgroup'][:40]}..." if len(row['subgroup']) > 40
                      else f"{i + 1}.{row['subgroup']}"
                      for i, (_, row) in enumerate(display_subgroups.iterrows())]

            sizes = display_subgroups['sample_count'].values
            colors = plt.cm.YlOrRd(
                (display_subgroups['error_rate'] - display_subgroups['error_rate'].min()) /
                (display_subgroups['error_rate'].max() - display_subgroups['error_rate'].min())
            )

            # 创建树状图
            plt.figure(figsize=(32, 20))

            # 绘制树状图
            squarify.plot(sizes=sizes, label=labels, alpha=0.8, color=colors)

            # 添加颜色条
            sm = plt.cm.ScalarMappable(cmap=plt.cm.YlOrRd,
                                       norm=plt.Normalize(vmin=display_subgroups['error_rate'].min(),
                                                          vmax=display_subgroups['error_rate'].max()))
            sm.set_array([])
            cbar = plt.colorbar(sm)
            cbar.set_label('错误率', fontsize=10)

            plt.axis('off')
            plt.title('子群体错误率树状图 (大小=样本数量, 颜色=错误率)', fontsize=14)
            plt.tight_layout()
            plt.show()

            # 输出子群体编号对照表
            print("\n树状图子群体编号对照表:")
            tree_table = []
            for i, (_, row) in enumerate(display_subgroups.iterrows()):
                tree_table.append([
                    i + 1,
                    row['subgroup'],
                    int(row['sample_count']),
                    f"{row['error_rate']:.4f}",
                    f"{row['avg_confidence']:.4f}"
                ])

            print(tabulate(tree_table,
                           headers=["编号", "子群体描述", "样本数", "错误率", "平均置信度"],
                           tablefmt="grid"))
        else:
            print("没有足够的子群体用于树状图分析")
    except Exception as e:
        print(f"树状图分析出错: {str(e)}")

    # =====================================================================
    # 3. 特征值错误率分析
    # =====================================================================
    print("\n" + "=" * 60)
    print("3. 特征值错误率分析")
    print("=" * 60)

    try:
        # 筛选有足够样本的子群体
        filtered_subgroups = subgroups_df[subgroups_df['sample_count'] >= 10].copy()

        if len(filtered_subgroups) >= 5:
            # 提取所有特征值
            feature_values = {}

            for _, row in filtered_subgroups.iterrows():
                feature1, value1 = row['feature1'], row['value1']
                feature2, value2 = row['feature2'], row['value2']

                # 清理特征名称
                feature1 = feature1.replace('临床特征-', '')
                feature2 = feature2.replace('临床特征-', '')

                # 记录特征值
                feat_val1 = f"{feature1}={value1}"
                feat_val2 = f"{feature2}={value2}"

                for feat_val in [feat_val1, feat_val2]:
                    if feat_val not in feature_values:
                        feature_values[feat_val] = {
                            'count': 0,
                            'error_sum': 0,
                            'sample_sum': 0
                        }

                    feature_values[feat_val]['count'] += 1
                    feature_values[feat_val]['error_sum'] += row['error_rate'] * row['sample_count']
                    feature_values[feat_val]['sample_sum'] += row['sample_count']

            # 计算每个特征值的平均错误率
            feature_stats = []
            for feat_val, stats in feature_values.items():
                avg_error = stats['error_sum'] / stats['sample_sum'] if stats['sample_sum'] > 0 else 0
                feature_stats.append({
                    'feature_value': feat_val,
                    'occurrence': stats['count'],
                    'avg_error': avg_error,
                    'sample_sum': stats['sample_sum']
                })

            # 按平均错误率排序
            feature_stats_df = pd.DataFrame(feature_stats)
            feature_stats_df = feature_stats_df.sort_values('avg_error', ascending=False)

            # 显示特征值错误率
            feature_table = []
            for _, row in feature_stats_df.iterrows():
                feature_table.append([
                    row['feature_value'],
                    int(row['occurrence']),
                    int(row['sample_sum']),
                    f"{row['avg_error']:.4f}"
                ])

            print(tabulate(feature_table,
                           headers=["特征值", "出现次数", "样本总数", "平均错误率"],
                           tablefmt="grid"))

            # 绘制特征值错误率条形图
            # 只显示出现次数>=3的特征值
            frequent_features = feature_stats_df[feature_stats_df['occurrence'] >= 3].copy()
            frequent_features = frequent_features.sort_values('avg_error', ascending=False)

            if len(frequent_features) > 0:
                # 限制显示的特征值数量
                if len(frequent_features) > 30:
                    plot_features = pd.concat([
                        frequent_features.head(15),  # 高错误率的15个
                        frequent_features.tail(15)  # 低错误率的15个
                    ])
                    plot_features = plot_features.sort_values('avg_error', ascending=False)
                else:
                    plot_features = frequent_features

                plt.figure(figsize=(14, 8))

                # 简化特征值标签
                labels = [f"{i + 1}.{fv[:30]}..." if len(fv) > 30 else f"{i + 1}.{fv}"
                          for i, fv in enumerate(plot_features['feature_value'])]

                # 绘制条形图
                bars = plt.barh(range(len(plot_features)),
                                plot_features['avg_error'],
                                color=plt.cm.YlOrRd(plot_features['avg_error'] /
                                                    plot_features['avg_error'].max()))

                # 添加数值标签
                for i, (_, row) in enumerate(plot_features.iterrows()):
                    plt.text(row['avg_error'] + 0.01, i,
                             f"{row['avg_error']:.3f} (n={int(row['sample_sum'])})",
                             va='center', fontsize=8)

                plt.xlabel('平均错误率', fontsize=12)
                plt.yticks(range(len(plot_features)), labels, fontsize=9)
                plt.title('特征值的平均错误率排名', fontsize=14)
                plt.grid(axis='x', alpha=0.3)
                plt.tight_layout()
                plt.show()

                # 输出图中编号对照表
                print("\n特征值编号对照表:")
                feature_id_table = []
                for i, (_, row) in enumerate(plot_features.iterrows()):
                    feature_id_table.append([
                        i + 1,
                        row['feature_value'],
                        int(row['occurrence']),
                        int(row['sample_sum']),
                        f"{row['avg_error']:.4f}"
                    ])

                print(tabulate(feature_id_table,
                               headers=["编号", "特征值", "出现次数", "样本总数", "平均错误率"],
                               tablefmt="grid"))
            else:
                print("没有足够的高频特征值用于分析")
        else:
            print("没有足够的子群体用于特征值错误率分析")

        # =====================================================================
        # 4. 子群体错误率和置信度分布分析
        # =====================================================================

        print("\n" + "=" * 60)
        print("4. 子群体错误率分布分析")
        print("=" * 60)

        # 创建2x2的子图面板
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 错误率分布直方图 (左上)
        sns.histplot(filtered_subgroups['error_rate'], kde=True,
                     bins=15, color='red', alpha=0.6, ax=axes[0, 0])

        # 添加均值和中位数线
        mean_error = filtered_subgroups['error_rate'].mean()
        median_error = filtered_subgroups['error_rate'].median()

        axes[0, 0].axvline(mean_error, color='black', linestyle='--',
                           label=f'均值={mean_error:.3f}')
        axes[0, 0].axvline(median_error, color='green', linestyle='--',
                           label=f'中位数={median_error:.3f}')

        axes[0, 0].set_xlabel('错误率', fontsize=10)
        axes[0, 0].set_ylabel('子群体数量', fontsize=10)
        axes[0, 0].set_title('子群体错误率分布', fontsize=12)
        axes[0, 0].legend()

        # 2. 错误率与样本数量的散点图 (右上)
        axes[0, 1].scatter(filtered_subgroups['sample_count'],
                           filtered_subgroups['error_rate'],
                           alpha=0.7, c=filtered_subgroups['error_rate'],
                           cmap='YlOrRd', s=50)

        # 添加拟合曲线
        try:
            from scipy import stats
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                filtered_subgroups['sample_count'],
                filtered_subgroups['error_rate'])

            x = np.linspace(filtered_subgroups['sample_count'].min(),
                            filtered_subgroups['sample_count'].max(), 100)
            y = slope * x + intercept
            axes[0, 1].plot(x, y, 'b--', alpha=0.5)
            axes[0, 1].text(0.05, 0.95, f'r={r_value:.3f}, p={p_value:.3f}',
                            transform=axes[0, 1].transAxes, fontsize=9,
                            bbox=dict(facecolor='white', alpha=0.7))
        except:
            pass

        axes[0, 1].set_xlabel('样本数量', fontsize=10)
        axes[0, 1].set_ylabel('错误率', fontsize=10)
        axes[0, 1].set_title('样本数量与错误率的关系', fontsize=12)

        # 3. 假阳性率与假阴性率散点图 (左下)
        scatter = axes[1, 0].scatter(filtered_subgroups['fp_rate'],
                                     filtered_subgroups['fn_rate'],
                                     alpha=0.7,
                                     c=filtered_subgroups['error_rate'],
                                     s=filtered_subgroups['sample_count'] / 5,
                                     cmap='YlOrRd')

        # 添加对角线
        max_rate = max(filtered_subgroups['fp_rate'].max(),
                       filtered_subgroups['fn_rate'].max())
        axes[1, 0].plot([0, max_rate], [0, max_rate], 'k--', alpha=0.3,
                        label='FP=FN')

        # 添加图例
        legend1 = axes[1, 0].legend(*scatter.legend_elements(num=5),
                                    loc="upper left", title="错误率")
        axes[1, 0].add_artist(legend1)

        # 样本数量图例
        sizes = [20, 50, 100, 200]
        for size in sizes:
            axes[1, 0].scatter([], [], s=size / 5, c='gray', alpha=0.5,
                               label=f'n={size}')

        axes[1, 0].legend(loc='upper right', title='样本数量')

        axes[1, 0].set_xlabel('假阳性率', fontsize=10)
        axes[1, 0].set_ylabel('假阴性率', fontsize=10)
        axes[1, 0].set_title('假阳性率与假阴性率的关系', fontsize=12)

        # 4. 子群体置信度分布图 (右下)
        sns.histplot(filtered_subgroups['avg_confidence'], kde=True,
                     bins=15, color='blue', alpha=0.6, ax=axes[1, 1])

        # 添加均值和中位数线
        mean_conf = filtered_subgroups['avg_confidence'].mean()
        median_conf = filtered_subgroups['avg_confidence'].median()

        axes[1, 1].axvline(mean_conf, color='black', linestyle='--',
                           label=f'均值={mean_conf:.3f}')
        axes[1, 1].axvline(median_conf, color='green', linestyle='--',
                           label=f'中位数={median_conf:.3f}')

        axes[1, 1].set_xlabel('平均置信度', fontsize=10)
        axes[1, 1].set_ylabel('子群体数量', fontsize=10)
        axes[1, 1].set_title('子群体平均置信度分布', fontsize=12)
        axes[1, 1].legend()

        plt.tight_layout()
        plt.show()

        # 输出子群体错误率分布统计信息
        print("\n子群体错误率分布统计信息:")
        error_stats = filtered_subgroups['error_rate'].describe()

        stats_table = []
        for stat in ['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max']:
            stats_table.append([
                stat,
                f"{error_stats[stat]:.4f}" if stat != 'count' else int(error_stats[stat])
            ])

        print(tabulate(stats_table,
                       headers=["统计量", "错误率"],
                       tablefmt="grid"))

    except Exception as e:
        print(f"子群体错误率分析出错: {str(e)}")
        import traceback
        traceback.print_exc()

    # 返回分析结果
    return {
        'subgroups_count': len(subgroups_df),
        'avg_error_rate': subgroups_df['error_rate'].mean() if len(subgroups_df) > 0 else None,
        'features_analyzed': valid_features
    }


if __name__ == "__main__":
    # 加载数据集 =======================================================================================================
    analysis = TrainAnalysis(
        feature_dir="../files/saved_features/file_features_V250303/",
        excel_path='D:\std data\\20250227版本\\加质控-内测样本分层处理-结果20250227.xlsx',
        # excel_path='D:\std data\\20250107微循环IMR实验\AMR阳性+对照.xlsx',
    )
    # 加载数据
    analysis.load_data()
    self = analysis
    # analysis.load_version_features(version="feature0303_PureBoruta_0227data_train_test")

    # results = analysis.train_and_save_moe_model2(expert_mode = "hospital_only")
    # results = train_and_save_moe_model2(analysis,)

    # 1. 评估模型 ======================================================================================================
    # results2 = evaluate_models_binary(analysis, by_center=3)
    # results2 = evaluate_models_binary(analysis,version="feature0303_PureBoruta_0227data",by_center=False)
    results = analysis.evaluate_models_binary(version="feature0303_DeepBoruta_0327addtrain")
    results = analysis.evaluate_models_binary(version="feature0303_PureBoruta_0227data_train_test")

    # 结果的分中心测评可视化；
    # plot_classifier_hospital_performance(result=results2['all_results'], by_hospital=True)
    # plot_new_classifier_hospital_performance(results2['center_results'], by_hospital=True)
    # analysis.visualize_results(results)

    # # 分析矛盾样本-----------------------------------------------------------------------------

    # # 1. 加载数据
    # X_selected_scaled = analysis.X_selected_scaled  # 已有的特征数据
    # selected_features = analysis.selected_features  # 已有的特征名称列表
    # # # 从selected_features中删除包含'clinic'的特征，且从X_selected_scaled中删除对应的列
    # selected_features = [feat for feat in selected_features if 'clinic' not in feat]
    # X_selected_scaled = X_selected_scaled[:, [i for i, feat in enumerate(selected_features) if 'clinic' not in feat]]
    # y = analysis.y                 # 已有的标签数据
    # data = analysis.data               # 包含心磁号的原始数据
    # -----> 转到 script_paradox_analysis脚本处理

    # 测试双层模型 ======================================================================================================
    # dm = train_double_model(analysis,
    #     feature_version=["feature0303_PureBoruta_0227data(1')","feature0303_PureBoruta_0227data(1''3)"],
    #         threshold1 = 0.5, threshold2 = 0.5,version = "v2.0.51" )
    # probs, preds, version, results_df = evaluator.predict_new_data(
    #     feature_dir="./files/saved_features/file_features_V250303/",
    #     excel_path='D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx',
    #     version='v2.0.51',  # 可选，不指定则使用最新版本
    #     sheet_name='测试集'
    # )
    #
    # res = get_test_metrics(result_df=results_df, external_excel_path='D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx'
    #                        , sheet_name='测试集')

    # 2. 使用评估结果训练和保存最佳模型,可以指定版本和描述 ====================================================================
    # best_model = train_and_save_model(analysis,results2,version="v1.2",description="测试版本300特征，使用SMOTE平衡")

    best_model, version = analysis.train_and_save_optuna_weighted_model(
        results,
        random_seed=42,
        version="v2.2.0",
        description="数据:0227版数据,训练+补充数据"
                    "原始特征:0303版特征(自动时刻点0.5)"
                    "选择特征:训测PureBoruta,追加cur/ci特征(原训练数据) "
                    "模型:XGB,optuna"
                    "增强:无",
        n_trials=0,
        study_name="hospital_weight_optimization7",

        # meta_learning_rate=1,
        # batch_size=5,
        # subtask_count=5000,

        optimize_xgb_params=False, save_best_fold=True
    )

    # 3. 列出所有版本号 准备数据 进行推理===================================================================================
    # trainer = ModelTrainer()
    # trainer.list_versions()
    # 4. 外部验证模型
    evaluator = ModelEvaluator()  # 读取过数据后会缓存 不用重新定义

    # 2. 预测新数据
    probs, preds, version, results_df = evaluator.predict_new_data(
        feature_dir="../files/saved_features/file_features_V250303/",
        excel_path='D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx',
        version='v2.1.11',  # 可选，不指定则使用最新版本
        sheet_name='外验集'
    )

    res = get_test_metrics(result_df=results_df,
                           external_excel_path='D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx'
                           , sheet_name='外验集')  #

    analyze_errors(results_df, 'D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx',
                   sheet_name='外验集')
    #
    # visualize_subgroups(results_df, 'D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx',
    #                     sheet_name='外验集',
    #                     interested_features=[
    #                         '临床特征-糖尿病',  '临床特征-典型症状', '临床特征-既往介入',
    #                         '临床特征-所在医院', '人工-标签一致性']
    #                     )

    # eva_results = find_optimal_threshold(results_df, metric='accuracy')
    # 3. 保存预测结果
    evaluator.save_predictions(results_df, './files/results/predictions2.0.61_补充测试集.xlsx')


