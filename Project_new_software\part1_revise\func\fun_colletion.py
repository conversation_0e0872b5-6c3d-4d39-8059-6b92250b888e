'''
@Project ：pythonProject 
@File    ：fun_colletion.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/7/29 9:39 
'''
import math
import numpy as np
from numpy.linalg import *
pi = math.pi


"""
用于插值
"""


def cubic_splines(x, ys):
    """
    三次样条插值函数
    :param x: 输入数组 X
    :param ys: 输入数组 Y
    :return z: 数据点处的二阶导数
    """
    ys = np.reshape(ys, (-1, 6))
    dy = np.diff(ys, axis=-1)
    dx = np.diff(x)

    n = x.shape[0]
    A = np.zeros((n, n))
    B = np.zeros(shape=(ys.shape[0], n))
    A[0, 0] = 1
    A[n - 1, n - 1] = 1
    for i in range(1, n - 1):
        A[i, i - 1] = dx[i - 1]
        A[i, i] = 2 * (dx[i - 1] + dx[i])
        A[i, i + 1] = dx[i]
        B[:, i] = 6 * ((dy[:, i] / dx[i]) - (dy[:, i - 1] / dx[i - 1]))

    As = np.tile(A, (B.shape[0], 1, 1))
    zs = np.linalg.solve(As, B)
    return zs


def interpolates(x, y, z, x_new):
    """
    插值函数
    :param x: 输入数组 X
    :param y: 输入数组 Y
    :param z: 数据点处的二阶导数
    :param x_new: 要求插值的点
    :return y_new : 对应的插值值
    """
    n = x.shape[0]
    h = np.diff(x)
    idx = np.searchsorted(x, x_new) - 1
    idx = np.clip(idx, 0, n-2)

    A = (x[idx + 1] - x_new) / h[idx]
    B = 1 - A
    C = (1 / 6) * (A ** 3 - A) * h[idx] ** 2
    D = (1 / 6) * (B ** 3 - B) * h[idx] ** 2
    y_new = A * y[:, idx] + B * y[:, idx + 1] + C * z[:, idx] + D * z[:, idx + 1]
    return y_new


def mcg_interpolate(d_ori):
    X = np.array([0, 4, 8, 12, 16, 20])
    Y = np.array([0, 4, 8, 12, 16, 20])
    values = np.linspace(0, 20, 100 + 1)[1:]
    Xi = np.tile(values, (100, 1))
    Yi = Xi.T
    Bs = d_ori.reshape((-1, 6, 6))

    Zi = np.zeros((Bs.shape[0], Xi.shape[0], Xi.shape[1]))
    z_x = np.array([cubic_splines(X, Bs[:, i, :]) for i in range(Bs.shape[1])])
    z_y = np.zeros((Bs.shape[0], 6, 100))

    for i in range(Xi.shape[0]):
        for k in range(Bs.shape[1]):
            z_y[:, k, :] = interpolates(X, Bs[:, k, :], z_x[k], Xi[i, :])

        for j in range(Xi.shape[1]):
            Zi[:, i, j] = interpolates(Y, z_y[:, :, j], cubic_splines(Y, z_y[:, :, j]), Yi[i, j])

    return Zi

def magnetic_gradient(d_ori, xi, yi):
    """
    用于计算并画出time_Q_S时刻内磁图的的梯度图，便于分析与观察
    :param d_ori: 单一时刻的磁图数据
    :param xi:   x方向的插值点
    :param yi:   y方向上的插值点
    :return:    单一时刻有插值磁图所得的梯度图
    """
    zi = d_ori[::-1]
    dzx = np.diff(zi, axis=1)  # 每列差分
    last_column = dzx[:, dzx.shape[1] - 1]  # 由于x方向差分，少了一列，将最后点列的差分值等同于前一列，由于网格够小，误差也较小
    dzx = np.column_stack((dzx, last_column))
    dzy = np.diff(zi, axis=0)  # 每行差分
    last_row = dzy[dzy.shape[0] - 1, :]  # 由于y方向差分，少了一行，将最后点行的差分值等同于前一行，由于网格够小，误差也较小
    dzy = np.row_stack((dzy, last_row))
    dx = np.diff(xi, axis=1)
    dx = np.column_stack((dx, dx[:, dx.shape[1] - 1]))
    dy = np.diff(yi, axis=0)
    dy = np.row_stack((dy, dy[dy.shape[0] - 1, :]))
    diffz_x, diffz_y = dzx / dx, dzy / dy
    q = diffz_x ** 2 + diffz_y ** 2
    h, w = q.shape
    position = np.argmax(q)
    index_i, index_j = position // w, position % w
    x, y = xi[index_i][index_j], yi[index_i][index_j]
    return x, y

"""
计算引导场矩阵
"""


def jac_1(position, jac_x, jac_y, jac_z):  # 参数与上函数一致, 输出分别为均匀场和梯度
    """
    参数与上函数一致, 输出分别为均匀场和梯度
    :param position:
    :param jac_x:
    :param jac_y:
    :param jac_z:
    :return: 计算磁场Bz在Qx和Qy方向上的梯度, 也可以看出导联场矩阵的一行
    """
    mu0 = 4 * pi * 10 ** (-7)
    r = ((jac_x - position[:, 0]) ** 2 + (jac_y - position[:, 1]) ** 2 + (jac_z - position[:, 2]) ** 2) ** 0.5

    diff_bz_qx = mu0 / (4 * pi * r ** 3) * (jac_y - position[:, 1])
    diff_bz_qy = -mu0 / (4 * pi * r ** 3) * (jac_x - position[:, 0])
    # diff_bz_qz = 0
    return diff_bz_qx, diff_bz_qy  # , diff_bz_qz


def dev_lead_field_1(position, dl, z):  # 生成导联场矩阵, 这里l*l就是像素点个数, 可以作为已知磁场强度点的个数
    """
    生成导联场矩阵，这里应该是36*2
    :param position:
    :param z: MCG探测器所在的 z轴 位置
    :param dl: 将探测器横坐标0-200 分为l个点 ， dl为依次l个点的横坐标
    :return: 导联场矩阵 points.shape[0] * 36 * 3
    第一维度的个数为传入position的个数，为了快速计算一般为points.shape[0], 后面维度代表每个点的引导场矩阵
    磁图的记录与前面一样从左到右，从下到上
    2024/03/29验证无误
    """
    len_1 = len(dl)
    lf = np.empty((position.shape[0], len_1 * len_1, 3))
    for i in range(len_1):
        for j in range(len_1):
            # bz_all.append(bz(position, dl[j], dl[ll-1-i], z, volume))  # 点源相对不同点生成的磁场向量

            # diff_bz_qx, diff_bz_qy = jac(position, dl[i], dl_1[len_2-1-j], z) # 该部分好像有问题，进行修改
            diff_bz_qx, diff_bz_qy = jac_1(position, dl[j], dl[i], z)
            lf[:, len_1 * i + j, 0] = diff_bz_qx
            lf[:, len_1 * i + j, 1] = diff_bz_qy
            lf[:, len_1 * i + j, 2] = diff_bz_qy * 0
    return lf


"""
相关性及子空间计算
"""


def sig_subspace(b):
    """
    :param b: MCG数据  1 * 36
    :return: signal subspace
    """
    b_1 = b.reshape(b.shape[0], 1)
    b_1_t = b.reshape(1, b.shape[0])
    bb = b_1 @ b_1_t
    u, sigma, vt = np.linalg.svd(bb)
    u_1 = u[:, 0:1]
    return u_1


def sub_corr_1(lead_matrix, sign_subspace):
    """
    :param lead_matrix: 导联场矩阵 36*2
    :param sign_subspace: 信号子空间， 一般为36 * n, n 为根据幸好子空间所给定的维数
    :return: subspace correlation
    """
    u_lead, s_lead, vt_lead = np.linalg.svd(lead_matrix)
    num_u_lead = lead_matrix.shape[1]
    corr = u_lead[:, 0:num_u_lead].T @ sign_subspace
    u_c, s_c, vt_c = np.linalg.svd(corr)
    x = vt_lead.T @ np.diag(1 / s_lead) @ u_c
    miu = s_c[0]
    x[0:2, 0] = x[0:2, 0] / np.linalg.norm(x[0:2, 0])
    return miu, x[:, 0]


"""
关于点集的一些计算
"""


def get_long_axis(coord1):
    """
    :return: 求出心室的长轴方向
    """
    data = coord1[:, 0:2]
    pca = PCA(n_components=2)
    pca.fit(data)
    # 主成分的方向就是数据的“长轴”方向
    long_axis_dir = pca.components_[0]
    return long_axis_dir


def helen_formula(a, b, c):
    edge_1 = norm(a-b, axis=1)
    egde_2 = norm(a-c, axis=1)
    edge_3 = norm(b-c, axis=1)
    p = (edge_1 + egde_2 + edge_3) / 2
    s = np.sqrt(p * (p - edge_1 * (p - egde_2) * (p - edge_3)))
    return s


def get_treta_center(a_1, a_2, a_3, a_4):
    """
    计算四面体的内切圆的圆心，并进行分割得到新的四面体， a_1,a_2,a_3,a_4为四面体的四个顶点
    :param a_1:
    :param a_2:
    :param a_3:
    :param a_4:
    :return:
    """
    s_1 = helen_formula(a_2, a_3, a_4)
    s_2 = helen_formula(a_1, a_3, a_4)
    s_3 = helen_formula(a_1, a_2, a_4)
    s_4 = helen_formula(a_1, a_2, a_3)
    x = (a_1[:, 0] * s_1 + a_2[:, 0] * s_2 + a_3[:, 0] * s_3 + a_4[:, 0] * s_4) \
        / (s_1 + s_2 + s_3 + s_4)
    y = (a_1[:, 1] * s_1 + a_2[:, 1] * s_2 + a_3[:, 1] * s_3 + a_4[:, 1] * s_4) \
        / (s_1 + s_2 + s_3 + s_4)
    z = (a_1[:, 2] * s_1 + a_2[:, 2] * s_2 + a_3[:, 2] * s_3 + a_4[:, 2] * s_4) \
        / (s_1 + s_2 + s_3 + s_4)
    return np.column_stack((x, y, z))


def voxel_downsample(points, voxel_size):
    """
    对三维点云进行体素网格降采样。

    参数:
        points (np.ndarray): (N, 3) 形状的数组，表示三维点云。
        voxel_size (float): 体素的大小。

    返回:
        downsampled_points (np.ndarray): 降采样后的点云。
    """
    # 计算每个点在体素网格中的索引
    indices = (points // voxel_size).astype(np.int32)

    # 使用唯一索引来找到唯一的体素
    unique_indices, inverse_indices = np.unique(indices, axis=0, return_inverse=True)

    # 对于每个唯一的体素，找到其中的点，并计算质心作为代表性点
    downsampled_points = np.zeros((len(unique_indices), 3))
    for i, idx in enumerate(unique_indices):
        voxel_points = points[inverse_indices == i]
        if voxel_points.size > 0:
            downsampled_points[i] = voxel_points.mean(axis=0)

    return downsampled_points, inverse_indices


