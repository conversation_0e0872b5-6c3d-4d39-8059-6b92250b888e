Stack trace:
Frame         Function      Args
0007FFFF3460  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF3460, 0007FFFF2360) msys-2.0.dll+0x1FE8E
0007FFFF3460  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF3738) msys-2.0.dll+0x67F9
0007FFFF3460  000210046832 (000210286019, 0007FFFF3318, 0007FFFF3460, 000000000000) msys-2.0.dll+0x6832
0007FFFF3460  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF3460  000210068E24 (0007FFFF3470, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF3740  00021006A225 (0007FFFF3470, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF2F540000 ntdll.dll
7FFF2DAE0000 KERNEL32.DLL
7FFF2CAB0000 KERNELBASE.dll
7FFF2D5A0000 USER32.dll
7FFF2D090000 win32u.dll
000210040000 msys-2.0.dll
7FFF2ED00000 GDI32.dll
7FFF2CEA0000 gdi32full.dll
7FFF2CFE0000 msvcp_win.dll
7FFF2C810000 ucrtbase.dll
7FFF2F440000 advapi32.dll
7FFF2E310000 msvcrt.dll
7FFF2D930000 sechost.dll
7FFF2D790000 RPCRT4.dll
7FFF2BDB0000 CRYPTBASE.DLL
7FFF2D0C0000 bcryptPrimitives.dll
7FFF2E2D0000 IMM32.DLL
