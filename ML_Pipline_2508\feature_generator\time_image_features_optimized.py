"""
@Project ：ML_Pipline
@File    ：time_image_features_optimized.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2024/8/19 上午10:03
@Discribe：
    优化后的图特征在时域上的特征提取类。
    简化了调用链，减少了不必要的并行处理，增强了错误处理。
"""
import os
import time
import logging
from typing import Dict, List, Tuple, Optional
from multiprocessing import Pool

import numpy as np
import pandas as pd

# 添加缺失的导入
try:
    import cv2
except ImportError:
    logging.warning("cv2未安装，图像处理功能将受限")
    cv2 = None

try:
    from scipy.stats import skew, kurtosis
    from scipy.ndimage import uniform_filter
except ImportError:
    logging.warning("scipy未安装，某些统计功能将受限")
    skew = kurtosis = uniform_filter = None

try:
    from tsfresh.feature_extraction.feature_calculators import sample_entropy
except ImportError:
    logging.warning("tsfresh未安装，样本熵计算将被跳过")
    sample_entropy = None

from feature_generator.utils.frame_feature_extraction import (
    split_result, temporal_interp_data
)
from feature_generator.utils.calculate_params import (
    get_current, process_poles, dfs_optimized, get_area, 
    get_longest_shortest_line, get_perimeter
)
from feature_generator.utils.writemp import statpn


class TimeImageFeaturesOptimized:
    """优化后的图特征在时域上的特征提取类"""
    
    # 类常量
    DEFAULT_SEGMENT_LENGTHS = {
        'tt': 130, 'qr': 20, 'rs': 20, 'st': 150, 'pt': 320
    }
    
    DEFAULT_TASKS = [
        ('t-onset', 't-end', DEFAULT_SEGMENT_LENGTHS['tt'], 'tt_dim_3'),
        ('q-peak', 'r-peak', DEFAULT_SEGMENT_LENGTHS['qr'], 'qr_dim_3'),
        ('r-peak', 's-peak', DEFAULT_SEGMENT_LENGTHS['rs'], 'rs_dim_3'),
        ('s-peak', 't-end', DEFAULT_SEGMENT_LENGTHS['st'], 'st_dim_3'),
    ]
    
    # 压缩的特征基础集合
    GLOBAL_FEATURES = [
        "MAXMCGVALRAW", "MINMCGVALRAW", "MEANMCGVALRAW",
        "POLARSTD", "GRADIENTMAGNITUDEMEAN", "GRADIENTSTD", 
        "CURVATUREMEAN", "LOCALVARIANCEMEAN"
    ]
    
    CURRENT_FEATURES = ["MAXCURRENTMAGNITUDE", "MAXCURRENTANGLE"]
    
    POLE_FEATURES = ["MCGS", "K", "ANGLE", "VALUE", "CIRCLERANGE"]
    
    TEMPORAL_FEATURES = [
        "MEAN", "STD", "MAX", "MIN", "SKEW", "KURT",
        "MEANABSDIFF", "PROPINCR", "TURNPNTRATIO", "SHAPEFACTOR",
        "TRENDSLOPE"
        # 移除SAMPLEENTROPY，因为经常产生inf值且不稳定
    ]

    def __init__(self, basic_args_dict: Dict, label: str, w_list: List[float], 
                 num_chunks: int = 15, enable_parallel: bool = True):
        """
        初始化时间图像特征提取器
        
        Args:
            basic_args_dict: 包含data_frame和result_peak的基础参数字典
            label: 数据标签
            w_list: 分位数列表
            num_chunks: 数据块数量（用于并行处理）
            enable_parallel: 是否启用并行处理
        """
        self.basic_args_dict = basic_args_dict
        self.label = label
        self.w_list = w_list
        self.num_chunks = num_chunks
        self.enable_parallel = enable_parallel
        self.tasks = self.DEFAULT_TASKS.copy()
        
        # 验证输入
        self._validate_inputs()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)

    def _validate_inputs(self):
        """验证输入参数"""
        if not self.basic_args_dict:
            raise ValueError("basic_args_dict不能为空")
        
        required_keys = ['data_frame', 'result_peak']
        for key in required_keys:
            if key not in self.basic_args_dict:
                raise ValueError(f"basic_args_dict中缺少必需的键: {key}")
        
        if not self.w_list:
            raise ValueError("w_list不能为空")
        
        if any(w <= 0 or w >= 1 for w in self.w_list):
            raise ValueError("w_list中的值必须在(0,1)范围内")

    def extract_features(self, file_path: Optional[str] = None) -> pd.DataFrame:
        """
        主要的特征提取方法
        
        Args:
            file_path: 文件路径（可选，用于生成文件ID）
            
        Returns:
            包含所有提取特征的DataFrame
        """
        try:
            start_time = time.time()
            
            # 1. 数据预处理
            signal_data, mcg_file_id = self._preprocess_data(file_path)
            
            # 2. 波段重采样和归一化
            normalized_segments = self._resample_and_normalize(signal_data)
            
            # 3. 帧特征提取
            frame_features = self._extract_frame_features(normalized_segments)
            
            # 4. 时间特征提取
            temporal_features = self._extract_temporal_features(frame_features)
            
            # 5. 组装最终结果
            result_df = self._assemble_final_features(temporal_features, mcg_file_id)
            
            self.logger.info(f"特征提取完成，耗时: {time.time() - start_time:.2f}s")
            return result_df
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {str(e)}")
            # 返回最小DataFrame以保持一致性
            mcg_id = self._get_file_id(file_path)
            return pd.DataFrame({'mcg_file': [mcg_id], 'label': [self.label]})

    def _preprocess_data(self, file_path: Optional[str]) -> Tuple[np.ndarray, str]:
        """预处理输入数据"""
        data_frame = self.basic_args_dict['data_frame']
        
        # 验证数据框结构
        if data_frame.shape[1] < 2:
            raise ValueError("data_frame至少需要2列（时间列+信号列）")
        
        # 提取信号通道（跳过第一列时间列）
        signal_channels = np.array(data_frame.iloc[:, 1:]).T
        
        # 验证信号形状
        if signal_channels.shape[0] != 36:
            self.logger.warning(f"期望36个通道，实际得到{signal_channels.shape[0]}个")
        
        mcg_file_id = self._get_file_id(file_path)
        
        return signal_channels, mcg_file_id

    def _get_file_id(self, file_path: Optional[str]) -> str:
        """获取文件ID"""
        if file_path:
            return os.path.basename(file_path).replace('.txt', '')
        elif 'file_path' in self.basic_args_dict:
            return os.path.basename(self.basic_args_dict['file_path']).replace('.txt', '')
        else:
            return 'unknown_file'

    def _resample_and_normalize(self, signal_channels: np.ndarray) -> List[np.ndarray]:
        """波段重采样和归一化"""
        result_peak = self.basic_args_dict['result_peak']
        
        # 重采样各个波段
        resampled_segments = []
        for start_label, end_label, duration, _ in self.tasks:
            try:
                segment = temporal_interp_data(
                    signal_channels=signal_channels,
                    start_times=result_peak[start_label],
                    end_times=result_peak[end_label],
                    threshold=duration
                )
                resampled_segments.append(segment)
            except KeyError as e:
                self.logger.warning(f"峰值标签{e}未找到，跳过该波段")
                continue
            except Exception as e:
                self.logger.warning(f"波段重采样失败: {str(e)}")
                continue
        
        # 归一化
        normalized_segments = self._unified_normalization(resampled_segments)
        
        return normalized_segments

    @staticmethod
    def _unified_normalization(segments: List[np.ndarray]) -> List[np.ndarray]:
        """统一归一化方法"""
        normalized_segments = []
        for segment in segments:
            max_abs = np.max(np.abs(segment))
            if max_abs > 0:
                normalized_segments.append(segment / max_abs)
            else:
                normalized_segments.append(segment)
        return normalized_segments

    def _extract_frame_features(self, normalized_segments: List[np.ndarray]) -> Dict:
        """提取帧特征"""
        if not normalized_segments:
            return {}

        # 连接所有段
        concatenated_data = np.concatenate(normalized_segments, axis=1)

        # 决定是否使用并行处理
        total_frames = concatenated_data.shape[1]
        use_parallel = (self.enable_parallel and
                       total_frames > 100 and
                       self.num_chunks > 1)

        if use_parallel:
            frame_features = self._extract_frame_features_parallel(concatenated_data)
        else:
            frame_features = self._extract_frame_features_serial(concatenated_data)

        # 按波段分组特征
        return self._group_features_by_segment(frame_features)

    def _extract_frame_features_parallel(self, data: np.ndarray) -> List[Dict]:
        """并行提取帧特征"""
        chunks = split_result(data, self.num_chunks)
        all_features = []

        # 限制进程数
        max_processes = min(self.num_chunks, os.cpu_count() or 1, 4)

        with Pool(processes=max_processes) as pool:
            chunk_results = pool.starmap(
                self._process_data_chunk,
                [(chunk, self.w_list) for chunk in chunks if chunk.size > 0]
            )

            for chunk_result in chunk_results:
                all_features.extend(chunk_result)

        return all_features

    def _extract_frame_features_serial(self, data: np.ndarray) -> List[Dict]:
        """串行提取帧特征"""
        return self._process_data_chunk(data, self.w_list)

    def _process_data_chunk(self, data_chunk: np.ndarray, w_list: List[float]) -> List[Dict]:
        """处理数据块，提取每帧特征"""
        if cv2 is None:
            raise ImportError("cv2未安装，无法进行图像处理")

        n_frames = data_chunk.shape[1]
        frame_features = []

        for i in range(n_frames):
            try:
                # 重塑为6x6
                frame_6x6 = data_chunk[:, i].reshape(6, 6)

                # 调整大小到100x100
                frame_100x100 = cv2.resize(
                    frame_6x6, (100, 100),
                    interpolation=cv2.INTER_CUBIC
                )

                # 提取单帧特征
                features = self._extract_single_frame_features(
                    frame_100x100, frame_6x6, w_list
                )
                frame_features.append(features)

            except Exception as e:
                self.logger.warning(f"帧{i}特征提取失败: {str(e)}")
                # 添加空特征字典
                frame_features.append({})

        return frame_features

    def _extract_single_frame_features(self, frame_fine: np.ndarray,
                                     frame_original: np.ndarray,
                                     w_list: List[float]) -> Dict:
        """提取单帧的所有特征"""
        features = {}

        # 1. 全局特征
        features.update(self._extract_global_features(frame_fine, frame_original))

        # 2. 分位数相关特征
        for w in w_list:
            features.update(self._extract_quantile_features(frame_fine, w))

        return features

    def _extract_global_features(self, frame_fine: np.ndarray,
                               frame_original: np.ndarray) -> Dict:
        """提取全局特征"""
        features = {}

        try:
            # 基本统计特征
            features['FRAME_MAXMCGVALRAW'] = float(np.max(frame_original))
            features['FRAME_MINMCGVALRAW'] = float(np.min(frame_original))
            features['FRAME_MEANMCGVALRAW'] = float(np.mean(frame_original))

            # 极坐标标准差
            if cv2 is not None:
                center_y, center_x = frame_fine.shape[0] // 2, frame_fine.shape[1] // 2
                max_radius = min(center_x, center_y)
                if max_radius > 0:
                    polar_img = cv2.warpPolar(
                        frame_fine, (max_radius, 360),
                        (center_x, center_y), max_radius,
                        cv2.WARP_POLAR_LINEAR
                    )
                    features['FRAME_POLARSTD'] = float(np.std(polar_img))
                else:
                    features['FRAME_POLARSTD'] = 0.0
            else:
                features['FRAME_POLARSTD'] = 0.0

            # 梯度特征
            if cv2 is not None:
                gx = cv2.Sobel(frame_fine, cv2.CV_64F, 1, 0, ksize=3)
                gy = cv2.Sobel(frame_fine, cv2.CV_64F, 0, 1, ksize=3)
                gradient_mag = np.sqrt(gx**2 + gy**2)
                features['FRAME_GRADIENTMAGNITUDEMEAN'] = float(np.mean(gradient_mag))
                features['FRAME_GRADIENTSTD'] = float(np.std(gradient_mag))
            else:
                features['FRAME_GRADIENTMAGNITUDEMEAN'] = 0.0
                features['FRAME_GRADIENTSTD'] = 0.0

            # 曲率特征（简化版）
            if cv2 is not None:
                gx = cv2.Sobel(frame_fine, cv2.CV_64F, 1, 0, ksize=3)
                gy = cv2.Sobel(frame_fine, cv2.CV_64F, 0, 1, ksize=3)
                # 简化的曲率计算
                curvature = np.abs(gx * gy) / (gx**2 + gy**2 + 1e-9)
                features['FRAME_CURVATUREMEAN'] = float(np.mean(curvature))
            else:
                features['FRAME_CURVATUREMEAN'] = 0.0

            # 局部方差
            if uniform_filter is not None:
                local_mean = uniform_filter(frame_fine, size=5, mode='reflect')
                local_sqr_mean = uniform_filter(frame_fine**2, size=5, mode='reflect')
                local_var = np.maximum(0, local_sqr_mean - local_mean**2)
                features['FRAME_LOCALVARIANCEMEAN'] = float(np.mean(local_var))
            else:
                features['FRAME_LOCALVARIANCEMEAN'] = 0.0

        except Exception as e:
            self.logger.warning(f"全局特征提取失败: {str(e)}")
            # 填充默认值
            for feature_name in self.GLOBAL_FEATURES:
                features[f'FRAME_{feature_name}'] = 0.0

        return features

    def _extract_quantile_features(self, frame_fine: np.ndarray, w: float) -> Dict:
        """提取分位数相关特征"""
        features = {}

        try:
            # 计算阈值
            pos_data = frame_fine[frame_fine > 1e-9]
            neg_data = np.abs(frame_fine[frame_fine < -1e-9])

            w_p = np.quantile(pos_data, w) if len(pos_data) > 0 else 0.0
            w_n = np.quantile(neg_data, w) if len(neg_data) > 0 else 0.0

            # 创建过滤后的图像
            filtered_frame = np.zeros_like(frame_fine)
            if w_p > 1e-9:
                mask_pos = frame_fine > w_p
                filtered_frame[mask_pos] = frame_fine[mask_pos]
            if w_n > 1e-9:
                mask_neg = frame_fine < -w_n
                filtered_frame[mask_neg] = frame_fine[mask_neg]

            # 电流特征
            if np.any(filtered_frame != 0):
                max_magni, max_angle, _, _ = get_current(filtered_frame)
                features[f'FRAME_MAXCURRENTMAGNITUDE_Q{str(w).replace(".", "P")}'] = float(max_magni)
                features[f'FRAME_MAXCURRENTANGLE_Q{str(w).replace(".", "P")}'] = float(max_angle)
            else:
                features[f'FRAME_MAXCURRENTMAGNITUDE_Q{str(w).replace(".", "P")}'] = 0.0
                features[f'FRAME_MAXCURRENTANGLE_Q{str(w).replace(".", "P")}'] = 0.0

            # 极点特征
            self._extract_pole_features(filtered_frame, w, w_p, w_n, features)

        except Exception as e:
            self.logger.warning(f"分位数{w}特征提取失败: {str(e)}")
            # 填充默认值
            q_suffix = f'Q{str(w).replace(".", "P")}'
            for feature_name in self.CURRENT_FEATURES:
                features[f'FRAME_{feature_name}_{q_suffix}'] = 0.0
            for pole_type in ['POS', 'NEG']:
                for feature_name in self.POLE_FEATURES:
                    features[f'FRAME_{feature_name}_{q_suffix}_{pole_type}_POLE0'] = 0.0

        return features

    def _extract_pole_features(self, filtered_frame: np.ndarray, w: float,
                             w_p: float, w_n: float, features: Dict):
        """提取极点特征"""
        q_suffix = f'Q{str(w).replace(".", "P")}'

        # 处理正极点
        if w_p > 1e-9 and np.any(filtered_frame > 0):
            try:
                pos_poles = process_poles(statpn(filtered_frame, vmin=w_p), filtered_frame)[0]
                self._process_single_pole(pos_poles, 'POS', q_suffix, w_p, filtered_frame, features)
            except Exception as e:
                self.logger.warning(f"正极点处理失败: {str(e)}")

        # 处理负极点
        if w_n > 1e-9 and np.any(filtered_frame < 0):
            try:
                neg_poles = process_poles(statpn(filtered_frame, vmin=w_n), filtered_frame)[1]
                self._process_single_pole(neg_poles, 'NEG', q_suffix, w_n, filtered_frame, features)
            except Exception as e:
                self.logger.warning(f"负极点处理失败: {str(e)}")

    def _process_single_pole(self, poles: List, pole_type: str, q_suffix: str,
                           threshold: float, filtered_frame: np.ndarray, features: Dict):
        """处理单个极点"""
        # 初始化默认值
        for feature_name in self.POLE_FEATURES:
            features[f'FRAME_{feature_name}_{q_suffix}_{pole_type}_POLE0'] = 0.0

        if not poles or len(poles) == 0:
            return

        try:
            pole_info = poles[0]  # 只处理主要极点
            pole_val, center_x, center_y = pole_info[0], int(pole_info[1]) - 1, int(pole_info[2]) - 1

            # DFS获取区域
            region_coords = dfs_optimized(filtered_frame, center_x, center_y, threshold, w=0.5)

            if not region_coords or len(region_coords) < 4:
                return

            # 创建区域掩码
            region_mask = np.zeros_like(filtered_frame)
            for r, c in region_coords:
                r_int, c_int = int(r), int(c)
                if 0 <= r_int < filtered_frame.shape[0] and 0 <= c_int < filtered_frame.shape[1]:
                    region_mask[r_int, c_int] = filtered_frame[r_int, c_int]

            # 计算几何特征
            area = get_area(region_mask)
            if area < 4:
                return

            max_len, min_len, angle = get_longest_shortest_line(region_mask, center_x, center_y)
            perimeter, k_elongation, *_ = get_perimeter(region_mask)

            # 存储特征
            features[f'FRAME_MCGS_{q_suffix}_{pole_type}_POLE0'] = float(area)
            features[f'FRAME_K_{q_suffix}_{pole_type}_POLE0'] = float(k_elongation)
            features[f'FRAME_ANGLE_{q_suffix}_{pole_type}_POLE0'] = float(angle)
            features[f'FRAME_VALUE_{q_suffix}_{pole_type}_POLE0'] = float(pole_val)

            if perimeter > 1e-9:
                circularity = (4 * np.pi * area) / (perimeter**2)
                features[f'FRAME_CIRCLERANGE_{q_suffix}_{pole_type}_POLE0'] = float(circularity)

        except Exception as e:
            self.logger.warning(f"{pole_type}极点几何特征计算失败: {str(e)}")

    def _group_features_by_segment(self, frame_features: List[Dict]) -> Dict:
        """按波段分组特征"""
        grouped_features = {}
        current_idx = 0

        for _, _, duration, task_key in self.tasks:
            segment_code = task_key.split('_')[0].upper()

            # 获取该段的帧特征
            segment_frames = frame_features[current_idx:current_idx + duration]

            # 重命名特征键
            renamed_frames = []
            for frame_dict in segment_frames:
                renamed_dict = {}
                for key, value in frame_dict.items():
                    new_key = f"MFMIMGTS_{segment_code}_{key}"
                    renamed_dict[new_key] = value
                renamed_frames.append(renamed_dict)

            grouped_features[task_key] = renamed_frames
            current_idx += duration

        return grouped_features

    def _extract_temporal_features(self, frame_features: Dict) -> pd.DataFrame:
        """提取时间特征"""
        all_temporal_features = []

        for w in self.w_list:
            w_features = self._extract_temporal_features_for_w(frame_features, w)
            if not w_features.empty:
                all_temporal_features.append(w_features)

        if not all_temporal_features:
            return pd.DataFrame()

        return pd.concat(all_temporal_features, axis=1)

    def _extract_temporal_features_for_w(self, frame_features: Dict, w: float) -> pd.DataFrame:
        """为特定w值提取时间特征"""
        all_features = {}
        w_suffix = f"W{str(w).replace('.', 'P')}"

        for task_key, frames_list in frame_features.items():
            if not frames_list:
                continue

            # 转换为DataFrame
            df = pd.DataFrame(frames_list)

            # 选择相关特征
            selected_features = self._select_features_for_w(df, w)

            if selected_features.empty:
                continue

            # 计算时间统计
            temporal_stats = self._calculate_temporal_statistics(selected_features, w_suffix)
            all_features.update(temporal_stats)

        return pd.DataFrame(all_features, index=[0]) if all_features else pd.DataFrame()

    def _select_features_for_w(self, df: pd.DataFrame, w: float) -> pd.DataFrame:
        """为特定w值选择相关特征"""
        if df.empty:
            return pd.DataFrame()

        # 选择全局特征和对应w值的特征
        q_suffix = f'Q{str(w).replace(".", "P")}'
        selected_cols = []

        for col in df.columns:
            # 全局特征（不依赖w值）
            if any(global_feat in col for global_feat in self.GLOBAL_FEATURES):
                selected_cols.append(col)
            # w值相关特征
            elif q_suffix in col:
                selected_cols.append(col)

        return df[selected_cols] if selected_cols else pd.DataFrame()

    def _calculate_temporal_statistics(self, df: pd.DataFrame, w_suffix: str) -> Dict:
        """计算时间统计特征"""
        features = {}

        for col in df.columns:
            col_data = pd.to_numeric(df[col], errors='coerce').dropna()

            if len(col_data) < 3:
                # 数据太少，填充合理的默认值以保持特征数量一致
                features[f"{col}_MEAN_{w_suffix}"] = 0.0
                features[f"{col}_STD_{w_suffix}"] = 0.0
                features[f"{col}_MAX_{w_suffix}"] = 0.0
                features[f"{col}_MIN_{w_suffix}"] = 0.0
                features[f"{col}_SKEW_{w_suffix}"] = 0.0
                features[f"{col}_KURT_{w_suffix}"] = 0.0
                features[f"{col}_MEANABSDIFF_{w_suffix}"] = 0.0
                features[f"{col}_PROPINCR_{w_suffix}"] = 0.5  # 中性值
                features[f"{col}_TURNPNTRATIO_{w_suffix}"] = 0.0
                features[f"{col}_SHAPEFACTOR_{w_suffix}"] = 0.0
                features[f"{col}_TRENDSLOPE_{w_suffix}"] = 0.0
                continue

            try:
                # 基本统计
                features[f"{col}_MEAN_{w_suffix}"] = float(np.mean(col_data))
                features[f"{col}_STD_{w_suffix}"] = float(np.std(col_data))
                features[f"{col}_MAX_{w_suffix}"] = float(np.max(col_data))
                features[f"{col}_MIN_{w_suffix}"] = float(np.min(col_data))

                # 分布统计 - 改进的SKEW和KURT计算
                skew_val, kurt_val = self._calculate_robust_distribution_stats(col_data)
                features[f"{col}_SKEW_{w_suffix}"] = skew_val
                features[f"{col}_KURT_{w_suffix}"] = kurt_val

                # 差分统计
                diff_data = np.diff(col_data)
                if len(diff_data) > 0:
                    features[f"{col}_MEANABSDIFF_{w_suffix}"] = float(np.mean(np.abs(diff_data)))
                    features[f"{col}_PROPINCR_{w_suffix}"] = float(np.mean(diff_data > 0))
                else:
                    features[f"{col}_MEANABSDIFF_{w_suffix}"] = np.nan
                    features[f"{col}_PROPINCR_{w_suffix}"] = np.nan

                # 形态学统计
                turning_points = self._find_turning_points(col_data)
                features[f"{col}_TURNPNTRATIO_{w_suffix}"] = len(turning_points) / len(col_data)
                features[f"{col}_SHAPEFACTOR_{w_suffix}"] = self._calculate_shape_factor(col_data)

                # 趋势斜率
                try:
                    x_coords = np.arange(len(col_data))
                    slope, _ = np.polyfit(x_coords, col_data, 1)
                    features[f"{col}_TRENDSLOPE_{w_suffix}"] = float(slope)
                except (np.linalg.LinAlgError, ValueError):
                    features[f"{col}_TRENDSLOPE_{w_suffix}"] = np.nan

                # 移除样本熵计算，因为经常产生inf值
                # 如果需要熵特征，可以考虑使用更稳定的替代方案

            except Exception as e:
                self.logger.warning(f"列{col}的时间统计计算失败: {str(e)}")
                # 填充合理的默认值以保持特征数量一致
                features[f"{col}_MEAN_{w_suffix}"] = 0.0
                features[f"{col}_STD_{w_suffix}"] = 0.0
                features[f"{col}_MAX_{w_suffix}"] = 0.0
                features[f"{col}_MIN_{w_suffix}"] = 0.0
                features[f"{col}_SKEW_{w_suffix}"] = 0.0
                features[f"{col}_KURT_{w_suffix}"] = 0.0
                features[f"{col}_MEANABSDIFF_{w_suffix}"] = 0.0
                features[f"{col}_PROPINCR_{w_suffix}"] = 0.5
                features[f"{col}_TURNPNTRATIO_{w_suffix}"] = 0.0
                features[f"{col}_SHAPEFACTOR_{w_suffix}"] = 0.0
                features[f"{col}_TRENDSLOPE_{w_suffix}"] = 0.0

        return features

    @staticmethod
    def _find_turning_points(data: np.ndarray) -> np.ndarray:
        """查找转折点"""
        if len(data) < 3:
            return np.array([])

        diffs = np.diff(np.sign(np.diff(data)))
        return np.where(np.abs(diffs) > 0)[0] + 1

    @staticmethod
    def _calculate_shape_factor(data: np.ndarray) -> float:
        """计算形状因子"""
        if np.std(data) == 0:
            return 0.0

        rms = np.sqrt(np.mean(data**2))
        mean_abs = np.mean(np.abs(data))

        return rms / mean_abs if mean_abs != 0 else 0.0

    @staticmethod
    def _calculate_robust_distribution_stats(col_data: pd.Series) -> Tuple[float, float]:
        """
        稳健的分布统计计算，确保SKEW和KURT总是返回有效值

        Args:
            col_data: 输入数据序列

        Returns:
            Tuple[float, float]: (skew_value, kurtosis_value)
        """
        # 转换为numpy数组
        data_array = np.asarray(col_data)

        # 多层质量检查

        # 检查1: 数据点数量（至少5个点才能可靠计算分布统计）
        if len(data_array) < 5:
            return 0.0, 0.0  # 数据太少，返回中性值

        # 检查2: 数据方差（避免常数序列）
        data_std = np.std(data_array)
        if data_std < 1e-10:
            return 0.0, 0.0  # 常数序列，偏度为0，峰度为正态分布值

        # 检查3: 唯一值数量（至少3个不同值）
        unique_values = len(np.unique(data_array))
        if unique_values < 3:
            return 0.0, 0.0  # 值太少，无法计算有意义的分布统计

        # 检查4: 数据范围
        data_range = np.ptp(data_array)  # peak-to-peak
        if data_range < 1e-10:
            return 0.0, 0.0  # 范围太小，视为常数

        # 尝试计算SKEW和KURT
        try:
            if skew is not None and kurtosis is not None:
                # 使用scipy计算
                skew_val = float(skew(data_array))
                kurt_val = float(kurtosis(data_array, fisher=True))  # Fisher=True返回超额峰度

                # 检查5: 结果有效性（合理范围检查）
                # 偏度通常在[-3, 3]范围内，峰度通常在[-2, 10]范围内
                if not (np.isfinite(skew_val) and abs(skew_val) < 50):
                    skew_val = 0.0

                if not (np.isfinite(kurt_val) and abs(kurt_val) < 50):
                    kurt_val = 0.0

                return skew_val, kurt_val
            else:
                # scipy不可用，使用简化计算
                return TimeImageFeaturesOptimized._calculate_simple_distribution_stats(data_array)

        except Exception as e:
            # 计算失败，返回默认值
            return 0.0, 0.0

    @staticmethod
    def _calculate_simple_distribution_stats(data_array: np.ndarray) -> Tuple[float, float]:
        """
        简化的分布统计计算（当scipy不可用时）

        Args:
            data_array: 输入数据数组

        Returns:
            Tuple[float, float]: (skew_value, kurtosis_value)
        """
        try:
            # 标准化数据
            mean_val = np.mean(data_array)
            std_val = np.std(data_array)

            if std_val < 1e-10:
                return 0.0, 0.0

            standardized = (data_array - mean_val) / std_val

            # 计算三阶和四阶矩
            n = len(standardized)
            m3 = np.mean(standardized**3)
            m4 = np.mean(standardized**4)

            # 简化的偏度和峰度
            skew_val = m3
            kurt_val = m4 - 3.0  # 超额峰度

            # 范围检查
            if not np.isfinite(skew_val) or abs(skew_val) > 50:
                skew_val = 0.0
            if not np.isfinite(kurt_val) or abs(kurt_val) > 50:
                kurt_val = 0.0

            return float(skew_val), float(kurt_val)

        except Exception:
            return 0.0, 0.0

    def _assemble_final_features(self, temporal_features: pd.DataFrame, mcg_file_id: str) -> pd.DataFrame:
        """组装最终特征"""
        if temporal_features.empty:
            return pd.DataFrame({'mcg_file': [mcg_file_id], 'label': [self.label]})

        # 添加元数据
        temporal_features.insert(0, "label", self.label)
        temporal_features.insert(0, 'mcg_file', mcg_file_id)

        return temporal_features


# 使用示例和测试函数
def create_optimized_extractor(basic_args_dict: Dict, label: str,
                             w_list: List[float] = None,
                             enable_parallel: bool = True) -> TimeImageFeaturesOptimized:
    """
    创建优化的特征提取器

    Args:
        basic_args_dict: 基础参数字典
        label: 数据标签
        w_list: 分位数列表，默认[0.5]
        enable_parallel: 是否启用并行处理

    Returns:
        优化的特征提取器实例
    """
    if w_list is None:
        w_list = [0.5]

    return TimeImageFeaturesOptimized(
        basic_args_dict=basic_args_dict,
        label=label,
        w_list=w_list,
        enable_parallel=enable_parallel
    )
