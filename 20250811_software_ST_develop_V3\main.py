from utils.utils_20250219 import *
from utils.cal_stsegs import get_mcg36
from utils.gettimes1 import gettime

class STSegmentAnalyzer:
    def __init__(self, rows=6, cols=6):
        self.name = None
        self.rows = rows
        self.cols = cols
        self.thresholds = {
            # 极值比异常时的三级阈值
            'J_elevation_1': 0.5,  # 幅值<3时：J点抬高阈值
            'J_depression_1': -0.5,  # 幅值<3时：J点压低阈值

            'J_elevation_2': 0.3,  # 3≤幅值<9时：J点抬高阈值
            'J_depression_2': -0.4,  # 3≤幅值<9时：J点压低阈值

            'J_elevation_3': 0.4,  # 幅值≥10时：J点抬高阈值
            'J_depression_3': -0.4,  # 幅值≥10时：J点压低阈值

            'Amp_threshold_1': 3,  # 幅值第一个临界值
            'Amp_threshold_2': 9,  # 幅值第二个临界值

            # 极值比正常时的4级阈值
            'J_elevation_4': 0.5,  # 幅值<3时：J点抬高阈值
            'J_depression_4': -0.5,  # 幅值<3时：J点压低阈值

            'J_elevation_5': 0.2,  # 3<幅值<8时：J点抬高阈值
            'J_depression_5': -0.2,  # 3≤幅值<8时：J点压低阈值

            'J_elevation_6': 0.4,  # 8≤幅值<20时：J点抬高阈值
            'J_depression_6': -0.4,  # 8≤幅值<20时：J点压低阈值

            'J_elevation_7': 1,  # 幅值≥20时：J点抬高阈值
            'J_depression_7': -1,  # 幅值≥20时：J点压低阈值

            'Amp_threshold_3': 5,  # 幅值第一个临界值
            'Amp_threshold_4': 8,  # 幅值第二个临界值
            'Amp_threshold_5': 20,  # 幅值第三个临界值

            'min_leads': 3,  # 最少相邻异常通道数
            'sustained_ratio': 0.80,  # ST段持续时间阈值（80%）
            'st_measure_point': 80,  # J点后测量窗口(ms)
            'ratio_low': 0.5,  # 极值比下限  0.75
            'ratio_high': 2,  # 极值比上限   1.33
            'special_channels': [13, 14, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36],
            'T_inver_threshold': 0.1,  # 0.01
        }

        self._initialize_regions()
        self._initialize_adjacent_leads()

    def _initialize_regions(self):
        """基于网格尺寸动态初始化空间区域"""
        # 计算区域边界
        top_rows = max(1, round(self.rows / 3))  # 上方区域：前1/3的行
        bottom_rows = max(1, round(self.rows / 3))  # 下方区域：后1/3的行
        right_cols = max(1, round(self.cols / 3))  # 右边区域：前1/3的列
        left_cols = max(1, round(self.cols / 3))  # 左边区域：后1/3的列

        # 计算中间区域边界（从1/6到5/6）
        middle_start_row = max(1, round(self.rows / 6))
        middle_end_row = self.rows - middle_start_row
        middle_start_col = max(1, round(self.cols / 6))
        middle_end_col = self.cols - middle_start_col

        # 初始化各个区域的集合
        self.spatial_regions = {
            1: set(),  # 上方区域
            2: set(),  # 下方区域
            3: set(),  # 右边区域
            4: set(),  # 左边区域
            5: set()  # 中间区域
        }

        def get_channel(row, col):
            return row * self.cols + col + 1
        for row in range(self.rows):
            for col in range(self.cols):
                channel = get_channel(row, col)
                # 上方区域
                if row < top_rows:
                    self.spatial_regions[1].add(channel)
                # 下方区域
                if row >= self.rows - bottom_rows:
                    self.spatial_regions[2].add(channel)
                # 右边区域
                if col < left_cols:
                    self.spatial_regions[3].add(channel)
                # 左边区域
                if col >= self.cols - right_cols:
                    self.spatial_regions[4].add(channel)

                # 中间区域
                if (middle_start_row <= row < middle_end_row and
                        middle_start_col <= col < middle_end_col):
                    self.spatial_regions[5].add(channel)

        # 初始化每个通道的主要归属区域
        self.channel_primary_region = {}
        for channel in range(1, self.rows * self.cols + 1):
            self.channel_primary_region[channel] = {region_num for region_num, region in self.spatial_regions.items() if channel in region}

    def _initialize_adjacent_leads(self):
        """动态初始化相邻导联关系"""
        self.adjacent_leads = {}
        for row in range(self.rows):
            for col in range(self.cols):
                current = row * self.cols + col + 1
                adjacent = set()
                # 检查8个可能的相邻位置
                for dr in [-1, 0, 1]:
                    for dc in [-1, 0, 1]:
                        if dr == 0 and dc == 0:
                            continue
                        new_row = row + dr
                        new_col = col + dc
                        if (0 <= new_row < self.rows and 0 <= new_col < self.cols):
                            adjacent.add(new_row * self.cols + new_col + 1)
                self.adjacent_leads[current] = list(adjacent)

    def analyze_lead(self, lead_data, Ph, Sr, Th, Tr, Pr, Qh, Tp, tmp):
        """分析单个通道的ST段形态"""
        # 1. 计算基线
        l1, l2, l3 = lead_data[Tr:], lead_data[:Ph], lead_data[Pr:Qh]
        baseline = max(min(round(cal_iqr0(cal_iqr0(l1 + l2) + l3), 6), 0.15), -0.15)

        # 2. 获取ST段数据
        st_start = Sr
        st_end = min(Sr + self.thresholds['st_measure_point'], Th)
        st_segment = lead_data[st_start:st_end]
        st_data = [x - baseline for x in st_segment]

        # 3. 计算ST段的特征
        signal_amplitude = max(lead_data[Qh:Sr]) - min(lead_data[Qh:Sr])  # QRS段内的幅值
        j_point_deviation = st_data[0]
        st_max = max(st_data)
        st_min = min(st_data)

        is_t_inverted = tmp in {3, 4, 5}  # 检测T波是否倒置或者双向  tmp：12345=直立/低平/正负/负正/倒置
        j_t_data = lead_data[st_start:Th]  # 获取J点到T波峰之间的数据
        j_to_th_data = [x - baseline for x in j_t_data]  # 去除基线漂移
        return {
            'st_data': st_data,  # ST段数据
            'signal_amplitude': signal_amplitude,  # 信号幅值
            'J_deviation': j_point_deviation,  # J点偏移量
            'st_max': st_max,  # ST段最大值
            'st_min': st_min,  # ST段最小值
            'is_t_inverted': is_t_inverted,  # T波是否倒置
            'j_to_t_data': j_to_th_data,  # J点到T波起始之间的数据
        }

    def _find_connected_leads(self, leads_dict):
        """深度优先搜索(DFS)算法找出相邻的异常通道群"""
        def dfs(lead, visited, current_group):
            visited.add(lead)
            current_group.add(lead)
            for neighbor in self.adjacent_leads[lead]:
                if neighbor not in visited and neighbor in leads_dict:
                    dfs(neighbor, visited, current_group)
            return current_group

        visited = set()
        connected_groups = []
        for lead in leads_dict:
            if lead not in visited:
                current_group = set()
                connected_group = dfs(lead, visited, current_group)
                if len(connected_group) >= self.thresholds['min_leads']:
                    connected_groups.append(connected_group)

        return connected_groups

    def _analyze_with_abnormal_ratio_thresholds(self, lead_results):
        """当极值比在异常范围内时使用的三级阈值策略"""
        elevation_leads = {}
        depression_leads = {}
        morphology_counts = {'elevation': 0, 'depression': 0, 'normal': 0}
        for result in lead_results:
            lead_num = result['lead_number']
            # 根据信号幅值确定阈值
            if result['signal_amplitude'] < self.thresholds['Amp_threshold_1']:
                elevation_threshold = self.thresholds['J_elevation_1']
                depression_threshold = self.thresholds['J_depression_1']
            elif self.thresholds['Amp_threshold_1'] <= result['signal_amplitude'] < self.thresholds['Amp_threshold_2']:
                elevation_threshold = self.thresholds['J_elevation_2']
                depression_threshold = self.thresholds['J_depression_2']
            else:
                elevation_threshold = self.thresholds['J_elevation_3']
                depression_threshold = self.thresholds['J_depression_3']

            if lead_num in self.thresholds['special_channels'] and result['is_t_inverted']:
                if result['j_to_t_data']:
                    j_to_t_data = result['j_to_t_data']
                    total_points = len(j_to_t_data)
                    elevated_ratio = sum(
                        1 for x in j_to_t_data if x >= self.thresholds['T_inver_threshold']) / total_points
                    depressed_ratio = sum(
                        1 for x in j_to_t_data if x <= -self.thresholds['T_inver_threshold']) / total_points
                    # 更新morphology结果
                    result['elevated_ratio'] = elevated_ratio
                    result['depressed_ratio'] = depressed_ratio
                    # 判断是否有?%的点保持同向变化
                    if elevated_ratio >= self.thresholds['sustained_ratio']:
                        elevation_leads[lead_num] = result['J_deviation']
                        result['morphology'] = 'elevation'
                        morphology_counts['elevation'] += 1
                    elif (depressed_ratio >= self.thresholds['sustained_ratio'] and result['J_deviation'] <= -
                    self.thresholds['T_inver_threshold']):
                        depression_leads[lead_num] = result['J_deviation']
                        result['morphology'] = 'depression'
                        morphology_counts['depression'] += 1
                    else:
                        result['morphology'] = 'normal'
                        morphology_counts['normal'] += 1
                    continue

            # 首先检查J点是否达到阈值  只有当J点达到阈值时，才进一步检查ST段数据的比例
            j_point_deviation = result['J_deviation']
            j_elevation_qualified = j_point_deviation >= elevation_threshold
            j_depression_qualified = j_point_deviation <= depression_threshold
            result['elevated_ratio'] = 0
            result['depressed_ratio'] = 0
            if j_elevation_qualified:  # J点抬高达到阈值，检查ST段后续数据的抬高比例
                total_points = len(result['st_data'])
                elevated_ratio = sum(1 for x in result['st_data'] if x >= elevation_threshold) / total_points
                result['elevated_ratio'] = elevated_ratio
                if elevated_ratio >= self.thresholds['sustained_ratio']:
                    elevation_leads[lead_num] = result['J_deviation']
                    result['morphology'] = 'elevation'
                    morphology_counts['elevation'] += 1
                else:
                    result['morphology'] = 'normal'
                    morphology_counts['normal'] += 1

            elif j_depression_qualified:  # J点压低达到阈值，检查ST段后续数据的压低比例
                total_points = len(result['st_data'])
                depressed_ratio = sum(1 for x in result['st_data'] if x <= depression_threshold) / total_points
                result['depressed_ratio'] = depressed_ratio
                if depressed_ratio >= self.thresholds['sustained_ratio']:
                    depression_leads[lead_num] = result['J_deviation']
                    result['morphology'] = 'depression'
                    morphology_counts['depression'] += 1
                else:
                    result['morphology'] = 'normal'
                    morphology_counts['normal'] += 1
            else:  # J点既未达到抬高阈值也未达到压低阈值，直接判定为正常
                result['morphology'] = 'normal'
                morphology_counts['normal'] += 1

        elevation_groups = self._find_connected_leads(elevation_leads)
        depression_groups = self._find_connected_leads(depression_leads)
        elevation_channels = sorted([lead for group in elevation_groups for lead in group])
        depression_channels = sorted([lead for group in depression_groups for lead in group])

        return {
            'elevation_leads': [(lead, elevation_leads[lead]) for lead in elevation_channels],
            'depression_leads': [(lead, depression_leads[lead]) for lead in depression_channels],
            'elevation_groups': [list(group) for group in elevation_groups],
            'depression_groups': [list(group) for group in depression_groups],
            'morphology_counts': morphology_counts,
            'lead_results': lead_results
        }

    def _analyze_with_normal_ratio_thresholds(self, lead_results):
        """当极值比在正常范围内时使用的四级阈值策略,且增加T波倒置特殊判断"""
        elevation_leads = {}
        depression_leads = {}
        morphology_counts = {'elevation': 0, 'depression': 0, 'normal': 0}
        for result in lead_results:
            lead_num = result['lead_number']  # 特殊处理T波倒置的通道
            # 根据信号幅值确定阈值
            if result['signal_amplitude'] <= self.thresholds['Amp_threshold_3']:
                elevation_threshold = self.thresholds['J_elevation_4']
                depression_threshold = self.thresholds['J_depression_4']
            elif self.thresholds['Amp_threshold_3'] < result['signal_amplitude'] <= self.thresholds['Amp_threshold_4']:
                elevation_threshold = self.thresholds['J_elevation_5']
                depression_threshold = self.thresholds['J_depression_5']
            elif self.thresholds['Amp_threshold_4'] < result['signal_amplitude'] <= self.thresholds['Amp_threshold_5']:
                elevation_threshold = self.thresholds['J_elevation_6']
                depression_threshold = self.thresholds['J_depression_6']
            else:
                elevation_threshold = self.thresholds['J_elevation_7']
                depression_threshold = self.thresholds['J_depression_7']

            if lead_num in self.thresholds['special_channels'] and result['is_t_inverted']:
                if result['j_to_t_data']:
                    j_to_t_data = result['j_to_t_data']
                    total_points = len(j_to_t_data)
                    elevated_ratio = sum(
                        1 for x in j_to_t_data if x >= self.thresholds['T_inver_threshold']) / total_points
                    depressed_ratio = sum(
                        1 for x in j_to_t_data if x <= -self.thresholds['T_inver_threshold']) / total_points
                    # 更新morphology结果
                    result['elevated_ratio'] = elevated_ratio
                    result['depressed_ratio'] = depressed_ratio
                    # 判断是否有?%的点保持同向变化
                    if elevated_ratio >= self.thresholds['sustained_ratio']:
                        elevation_leads[lead_num] = result['J_deviation']
                        result['morphology'] = 'elevation'
                        morphology_counts['elevation'] += 1
                    elif (depressed_ratio >= self.thresholds['sustained_ratio'] and result['J_deviation'] <= -
                    self.thresholds['T_inver_threshold']):
                        depression_leads[lead_num] = result['J_deviation']
                        result['morphology'] = 'depression'
                        morphology_counts['depression'] += 1
                    else:
                        result['morphology'] = 'normal'
                        morphology_counts['normal'] += 1

                    continue

            # 首先检查J点是否达到阈值  只有当J点达到阈值时，才进一步检查ST段数据的比例
            j_point_deviation = result['J_deviation']
            j_elevation_qualified = j_point_deviation >= elevation_threshold
            j_depression_qualified = j_point_deviation <= depression_threshold
            result['elevated_ratio'] = 0
            result['depressed_ratio'] = 0

            if j_elevation_qualified:
                # J点抬高达到阈值，检查ST段后续数据的抬高比例
                total_points = len(result['st_data'])
                elevated_ratio = sum(1 for x in result['st_data'] if x >= elevation_threshold) / total_points
                result['elevated_ratio'] = elevated_ratio

                # 判断ST段是否有足够比例的点保持抬高
                if elevated_ratio >= self.thresholds['sustained_ratio']:
                    elevation_leads[lead_num] = result['J_deviation']
                    result['morphology'] = 'elevation'
                    morphology_counts['elevation'] += 1
                else:
                    result['morphology'] = 'normal'
                    morphology_counts['normal'] += 1

            elif j_depression_qualified:
                # J点压低达到阈值，检查ST段后续数据的压低比例
                total_points = len(result['st_data'])
                depressed_ratio = sum(1 for x in result['st_data'] if x <= depression_threshold) / total_points
                result['depressed_ratio'] = depressed_ratio

                # 判断ST段是否有足够比例的点保持压低
                if depressed_ratio >= self.thresholds['sustained_ratio']:
                    depression_leads[lead_num] = result['J_deviation']
                    result['morphology'] = 'depression'
                    morphology_counts['depression'] += 1
                else:
                    result['morphology'] = 'normal'
                    morphology_counts['normal'] += 1
            else:
                # J点既未达到抬高阈值也未达到压低阈值，直接判定为正常
                result['morphology'] = 'normal'
                morphology_counts['normal'] += 1

        elevation_groups = self._find_connected_leads(elevation_leads)
        depression_groups = self._find_connected_leads(depression_leads)

        elevation_channels = sorted([lead for group in elevation_groups for lead in group])
        depression_channels = sorted([lead for group in depression_groups for lead in group])

        return {
            'elevation_leads': [(lead, elevation_leads[lead]) for lead in elevation_channels],
            'depression_leads': [(lead, depression_leads[lead]) for lead in depression_channels],
            'elevation_groups': [list(group) for group in elevation_groups],
            'depression_groups': [list(group) for group in depression_groups],
            'morphology_counts': morphology_counts,
            'lead_results': lead_results
        }

    def analyze_case(self, processed_file, Ph, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr):
        """分析单个case的ST段形态"""
        # 1. 时刻点
        # Ph, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr = ts[0], ts[2], ts[3], ts[4], ts[5], ts[6], ts[7], ts[8], ts[9], ts[10], ts[11], ts[12]

        # 2. 获取36通道心磁数据
        mcg_data = get_mcg36(processed_file, 0)

        # 3. 找到T波峰位置，检测是否T波倒置，获取J点到T波峰之间的数据
        tms = [Qp, Rp, Sp, To, Tp, Te]
        rts = Twave_types(processed_file, tms)  # 12345=直立/低平/正负/负正/倒置

        # 3. 分析所有导联，收集ST段数据
        lead_results = []
        for lead_idx in range(36):
            tmp = rts[lead_idx]
            result = self.analyze_lead(mcg_data[lead_idx], Ph, Sr, Th, Tr, Pr, Qh, Tp, tmp)
            result['lead_number'] = lead_idx + 1
            lead_results.append(result)

        # 4. 计算极值比
        st_length = len(lead_results[0]['st_data'])
        point_ratios = []
        for time_idx in range(st_length):
            # 收集所有通道在该时间点的值
            time_point_values = [lead['st_data'][time_idx] for lead in lead_results]
            # 计算该时间点的最大抬高和最大压低
            max_up = max([v for v in time_point_values if v > 0], default=0)
            max_down = abs(min([v for v in time_point_values if v < 0], default=0))
            # 计算该时间点的极值比
            if max_down != 0 and max_up != 0:
                point_ratios.append(max_up / max_down)

        # 计算中位数极值比
        ratio = float('inf')
        if point_ratios:
            point_ratios.sort()
            mid = len(point_ratios) // 2
            if len(point_ratios) % 2 == 0:
                ratio = (point_ratios[mid - 1] + point_ratios[mid]) / 2
            else:
                ratio = point_ratios[mid]

        # 5. 根据极值比选择分析策略
        if not (self.thresholds['ratio_low'] <= ratio <= self.thresholds['ratio_high']):
            # 极值比异常，使用三个阈值的分档策略
            final_result = self._analyze_with_abnormal_ratio_thresholds(lead_results)
        else:
            # 极值比正常，使用单一阈值策略
            final_result = self._analyze_with_normal_ratio_thresholds(lead_results)

        final_result['st_ratio'] = round(ratio, 3)

        # 6. 确定主要类型
        if final_result['elevation_groups'] and final_result['depression_groups']:
            final_result['main_type'] = 'elevation&depression'
        elif final_result['elevation_groups']:
            final_result['main_type'] = 'elevation'
        elif final_result['depression_groups']:
            final_result['main_type'] = 'depression'
        else:
            final_result['main_type'] = 'normal'

        # 7. 确定异常区域
        abnormal_regions = set()
        if final_result['main_type'] != 'normal':
            all_abnormal_channels = []
            if final_result['elevation_leads']:
                all_abnormal_channels.extend([lead[0] for lead in final_result['elevation_leads']])
            if final_result['depression_leads']:
                all_abnormal_channels.extend([lead[0] for lead in final_result['depression_leads']])

            # 检查每个异常通道所属的区域
            for channel in all_abnormal_channels:
                for region_id, region_channels in self.spatial_regions.items():
                    if channel in region_channels:
                        abnormal_regions.add(region_id)

            # 映射区域ID到区域名称
            region_names = {
                1: "上方区域",
                2: "下方区域",
                3: "被试右边区域",
                4: "被试左边区域",
                5: "中间区域"
            }
            final_result['abnormal_regions'] = [region_names[region_id] for region_id in sorted(abnormal_regions)]
        else:
            final_result['abnormal_regions'] = []

        return final_result

    def output_analysis_result(self, result):
        """
        输出ST段分析的简化结果
        Returns:
        When ST segment is normal:
        - st_status: 0
        - empty lists for channels and regions
        - st_ratio: 极值比

        When ST segment is abnormal:
        - st_status: 抬高=1, 压低=-1, 既有抬高又有压低=2
        - elevation_channels: ST段抬高的通道列表
        - depression_channels: ST段压低的通道列表
        - elevation_regions: ST段抬高的区域列表
        - depression_regions: ST段压低的区域列表
        - st_ratio: 极值比
        """
        # 确定ST段状态
        if result['main_type'] == 'elevation':
            st_status = 1
        elif result['main_type'] == 'depression':
            st_status = -1
        elif result['main_type'] == 'elevation&depression':
            st_status = 2
        else:  # normal
            st_status = 0

        # 初始化结果字典
        output = {
            'ST_Diagnosis': st_status,
            'st_ratio': result['st_ratio']
        }

        # 如果ST段正常
        if st_status == 0:
            output.update({
                'elevation_channels': [],
                'depression_channels': [],
                'ST_Elevation_Area': [],
                'ST_Depression_Area': []
            })
        # 如果ST段非正常，添加详细信息
        else:
            # 获取抬高和压低的通道
            elevation_channels = []
            depression_channels = []

            if result['elevation_leads']:
                elevation_channels = sorted(set([lead[0] for lead in result['elevation_leads']]))

            if result['depression_leads']:
                depression_channels = sorted(set([lead[0] for lead in result['depression_leads']]))

            # 确定抬高和压低的区域
            elevation_regions = set()
            depression_regions = set()

            # 区域名称映射
            region_names = {
                1: "上方区域",
                2: "下方区域",
                3: "右边区域",
                4: "左边区域",
                5: "中间区域"
            }

            # 检查每个抬高通道所属的区域
            for channel in elevation_channels:
                for region_id, region_channels in self.spatial_regions.items():
                    if channel in region_channels:
                        elevation_regions.add(region_id)

            # 检查每个压低通道所属的区域
            for channel in depression_channels:
                for region_id, region_channels in self.spatial_regions.items():
                    if channel in region_channels:
                        depression_regions.add(region_id)

            # 转换区域ID为区域名称
            elevation_region_names = [region_names[region_id] for region_id in sorted(elevation_regions)]
            depression_region_names = [region_names[region_id] for region_id in sorted(depression_regions)]
            output.update({
                'elevation_channels': elevation_channels,
                'depression_channels': depression_channels,
                'ST_Elevation_Area': elevation_region_names,
                'ST_Depression_Area': depression_region_names
            })

        return output


if __name__ == "__main__":
    start_time = time.time()
    mcg_file = 'mcg/AHYK_2023_000046.txt'  # 原始心磁数据
    Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr = 148, 178, 208, 314, 326, 346, 366, 400, 475, 475, 558, 620, 665

    analyzer = STSegmentAnalyzer()
    result = analyzer.analyze_case(mcg_file, Ph, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr)
    output = analyzer.output_analysis_result(result)
    end_time = time.time()
    execution_time = end_time - start_time
    print(output['ST_Diagnosis'],
          output['elevation_channels'],
          output['depression_channels'],
          output['ST_Elevation_Area'],
          output['ST_Depression_Area'])
    print(f"代码运行时间：{execution_time:.4f} 秒")