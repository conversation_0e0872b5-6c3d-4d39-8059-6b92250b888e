"""
简化的特征一致性测试脚本
专注于特征生成和基本比较
"""

import pandas as pd
import pickle
import numpy as np
import os
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, '.')
from main_v2 import DeploymentPipeline


def test_single_file_features():
    """测试单个文件的特征生成"""
    print("🔍 简化特征一致性测试")
    
    # 测试文件
    txt_file = 'files/tests/test_txts/GDNH_2023_000434.txt'
    filename = Path(txt_file).stem
    
    print(f"\n=== 测试文件: {filename} ===")
    
    # 创建pipeline
    pipeline = DeploymentPipeline(model_version='v2.5.03')
    
    # 空的临床数据
    empty_clinical_data = {
        'clinic_height': 0,
        'clinic_weight': 0,
        'clinic_gender': 0,
        'clinic_age': 0,
        'clinic_smoking': 0,
        'clinic_drinking': 0,
        'clinic_hypertension': 0,
        'clinic_hyperlipidemia': 0,
        'clinic_intervention': 0,
        'clinic_hospital': 0,
        'clinic_diabetes': 0,
        'clinic_symp': 0
    }
    
    try:
        # 1. 生成基础特征
        print("\n1️⃣ 生成基础特征")
        base_result = pipeline.generate_base_features(txt_file, empty_clinical_data)
        if isinstance(base_result, str):
            base_df = pd.read_pickle(base_result)
        else:
            base_df = base_result
        print(f"   基础特征: {base_df.shape}")
        print(f"   前5个特征: {list(base_df.columns[:5])}")
        
        # 2. 生成cur特征
        print("\n2️⃣ 生成cur特征")
        cur_df = pipeline.generate_cur_features(txt_file)
        print(f"   cur特征: {cur_df.shape}")
        print(f"   前5个特征: {list(cur_df.columns[:5])}")
        
        # 3. 生成ci特征
        print("\n3️⃣ 生成ci特征")
        ci_df = pipeline.generate_ci_features(txt_file)
        print(f"   ci特征: {ci_df.shape}")
        print(f"   前5个特征: {list(ci_df.columns[:5])}")
        
        # 4. 模型预测
        print("\n4️⃣ 模型预测")
        prob, pred = pipeline.predict_single_file(txt_file, empty_clinical_data)
        print(f"   预测概率: {prob:.4f}")
        print(f"   预测类别: {pred}")
        
        # 5. 特征统计
        print("\n5️⃣ 特征统计")
        total_features = base_df.shape[1] + cur_df.shape[1] + ci_df.shape[1] - 3  # 减去3个mcg_file
        print(f"   总特征数: {total_features}")
        print(f"   基础特征: {base_df.shape[1]}")
        print(f"   cur特征: {cur_df.shape[1]}")
        print(f"   ci特征: {ci_df.shape[1]}")
        
        # 6. 保存当前生成的特征用于比较
        print("\n6️⃣ 保存特征用于比较")
        output_dir = Path("files/tests/current_features")
        output_dir.mkdir(exist_ok=True)
        
        base_df.to_pickle(output_dir / f"{filename}_base.pkl")
        cur_df.to_pickle(output_dir / f"{filename}_cur.pkl")
        ci_df.to_pickle(output_dir / f"{filename}_ci.pkl")
        
        print(f"   特征已保存到: {output_dir}")
        
        return {
            'base': base_df,
            'cur': cur_df,
            'ci': ci_df,
            'prediction': {'prob': prob, 'pred': pred}
        }
        
    except Exception as e:
        print(f"❌ 特征生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def compare_with_historical():
    """尝试与历史特征比较"""
    print("\n=== 与历史特征比较 ===")
    
    try:
        # 尝试加载历史cur特征
        print("📊 加载历史cur特征...")
        cur_hist = pd.read_pickle('files/tests/saved_outer_features/cur_feature_df_all3923.pkl')
        print(f"   历史cur特征: {cur_hist.shape}")
        
        # 查找GDNH_2023_000434的历史特征
        target_sample = cur_hist[cur_hist['mcg_file'] == 'GDNH_2023_000434']
        if len(target_sample) == 0:
            target_sample = cur_hist[cur_hist['mcg_file'].str.contains('GDNH_2023_000434', na=False)]
        
        if len(target_sample) > 0:
            print(f"   找到历史样本: {target_sample.shape}")
            print(f"   历史cur特征名: {list(target_sample.columns[:10])}")
        else:
            print("   未找到对应的历史样本")
            
    except Exception as e:
        print(f"   历史特征加载失败: {e}")


def main():
    """主函数"""
    print("🚀 开始简化特征一致性测试")
    
    # 1. 测试特征生成
    result = test_single_file_features()
    
    if result:
        print("\n✅ 特征生成测试成功")
        
        # 2. 尝试与历史特征比较
        compare_with_historical()
        
        # 3. 总结
        print("\n📋 测试总结:")
        print(f"   ✅ 基础特征: {result['base'].shape[1]}个")
        print(f"   ✅ cur特征: {result['cur'].shape[1]}个")
        print(f"   ✅ ci特征: {result['ci'].shape[1]}个")
        print(f"   ✅ 预测概率: {result['prediction']['prob']:.4f}")
        print(f"   ✅ 预测类别: {result['prediction']['pred']}")
        
        # 4. 与历史数据对比
        print("\n📊 与历史数据对比:")
        print("   历史cur特征: 3923样本 × 81特征")
        print("   历史ci特征: 3912样本 × 472特征")
        print("   历史基础特征: 1样本 × 4579特征")
        print(f"   当前cur特征: 1样本 × {result['cur'].shape[1]}特征")
        print(f"   当前ci特征: 1样本 × {result['ci'].shape[1]}特征")
        print(f"   当前基础特征: 1样本 × {result['base'].shape[1]}特征")
        
        # 5. 一致性评估
        print("\n🎯 一致性评估:")
        cur_match = result['cur'].shape[1] == 81
        ci_match = abs(result['ci'].shape[1] - 472) <= 2  # 允许2个特征的差异
        base_match = result['base'].shape[1] == 4579
        
        print(f"   cur特征数量一致: {'✅' if cur_match else '❌'}")
        print(f"   ci特征数量一致: {'✅' if ci_match else '❌'}")
        print(f"   基础特征数量一致: {'✅' if base_match else '❌'}")
        
        if cur_match and ci_match and base_match:
            print("\n🎉 特征生成与历史数据基本一致！")
        else:
            print("\n⚠️ 特征生成与历史数据存在差异，需要进一步检查")
    
    else:
        print("\n❌ 特征生成测试失败")
    
    print("\n🏁 测试完成")


if __name__ == '__main__':
    main()
