"""
@ Author: <PERSON>
@ E-mail: <EMAIL>
@ Date: 2025/5/30 19:28
结合包络线和单通道分析来判断Q波和S波的存在性
包含QR RS转动异常指数计算类
"""

from scipy.signal import find_peaks, savgol_filter
from utils.gettimes import *
import matplotlib
from utils.gettimes1 import *
from utils.utils_20250219 import *
import os
from utils.gettimes1 import gettime as time1
from utils.gettimes import gettime as time2
from utils.gettimes2 import cal_times0 as time3
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False
import numpy as np
import math


class QRSMorphologyAnalyzer:
    """QRS波形形态分析器"""

    def __init__(self):
        self.min_prominence_ratio = 0.05
        self.min_prominence = 0.15
        self.min_amplitude_pt = 0.5  # 有效波幅值0.25
        self.deep_valley_ratio = 0.6  # 波谷必须在Q(S)波幅值的60%以内才算足够深
        self.large_QS_ratio = 0.75  # 如果Q/S达到了R波的75%强度，就算大Q/S波

    # ==================== 包络线处理 ====================
    def extract_envelope(self, signals, envelope_type):
        """提取包络线（合并上下包络线提取逻辑）"""
        signals_array = np.array(signals)

        if envelope_type == "upper":
            processed_signals = np.maximum(signals_array, 0)
            envelope = np.max(processed_signals, axis=0)
        else:  # lower
            processed_signals = np.minimum(signals_array, 0)
            envelope = np.min(processed_signals, axis=0)

        std = np.std(processed_signals, axis=0)
        return envelope, std

    def smooth_envelope(self, envelope, window_length=5, poly_order=2):
        """平滑包络线"""
        if len(envelope) < window_length:
            window_length = len(envelope) if len(envelope) % 2 == 1 else len(envelope) - 1
        if window_length < poly_order + 1:
            poly_order = max(1, window_length - 1)
        return savgol_filter(envelope, window_length, poly_order)

    # ==================== 波峰检测和过滤 ====================
    def detect_peaks_in_envelope(self, envelope, min_height_ratio, min_amplitude_pt, envelope_type="lower"):
        """在指定包络线中检测波峰"""
        if len(envelope) < 3:
            return []

        max_val = np.max(np.abs(envelope))
        min_height = max_val * min_height_ratio

        if envelope_type == "lower":  # 对于下包络线，检测负向波峰
            peaks, properties = find_peaks(-envelope, height=min_height, distance=1, prominence=min_height * 0.2)
        else:  # 对于上包络线，检测正向波峰
            peaks, properties = find_peaks(envelope, height=min_height, distance=1, prominence=min_height * 0.2)

        # 过滤幅度不足的波峰
        valid_peaks = [peak for peak in peaks if abs(envelope[peak]) >= min_amplitude_pt]
        return valid_peaks

    def filter_peaks_by_distance_and_amplitude(self, peaks, envelope, r_peak_relative_pos, min_distance, wave_type):
        """根据与R波峰的距离和幅度过滤波峰"""
        if not peaks:
            return []

        valid_peaks = []
        for peak in peaks:
            # 检查距离
            distance_valid = (
                    (wave_type == 'Q' and peak < r_peak_relative_pos - min_distance) or
                    (wave_type == 'S' and peak > r_peak_relative_pos + min_distance)
            )

            # 检查幅度
            amplitude_valid = abs(envelope[peak]) >= self.min_amplitude_pt

            if distance_valid and amplitude_valid:
                valid_peaks.append(peak)

        return valid_peaks

    # ==================== 波峰质量评估 ====================
    def find_valley_value(self, envelope, peak_idx, wave_type, search_radius=15):
        """寻找波谷值（提取公共逻辑）"""
        if wave_type == "Q":  # 在Q波峰右侧寻找最浅的valley
            search_start = peak_idx + 1
            search_end = min(len(envelope), peak_idx + search_radius + 1)
        else:  # 在S波峰左侧寻找最浅的valley
            search_start = max(0, peak_idx - search_radius)
            search_end = peak_idx

        # 寻找最浅的valley（最接近基线的点）
        valley_value = min(
            abs(envelope[i]) for i in range(search_start, search_end)) if search_start < search_end else float('inf')

        # 如果没有找到有效的valley，使用边界值
        if valley_value == float('inf'):
            if wave_type == "Q":
                valley_value = abs(envelope[search_end - 1]) if search_end > 0 else 0
            else:
                valley_value = abs(envelope[search_start]) if search_start < len(envelope) else 0

        return valley_value

    def evaluate_peak_quality(self, envelope, peak_idx, r_peak_idx, wave_type):
        """评估波峰质量"""
        if peak_idx < 1 or peak_idx >= len(envelope) - 1 or r_peak_idx < 0 or r_peak_idx >= len(envelope):
            return False, 0, 0, False, False

        peak_value = abs(envelope[peak_idx])
        r_wave_value = abs(envelope[r_peak_idx])

        if peak_value < self.min_amplitude_pt:
            return False, 0, 0, False, False

        # 寻找波谷值
        valley_value = self.find_valley_value(envelope, peak_idx, wave_type)

        # 计算突出度相关指标
        prominence = peak_value - valley_value
        prominence_ratio = prominence / peak_value if peak_value > 0 else 0
        exceed_r_wave = peak_value > r_wave_value
        is_large_wave = peak_value >= r_wave_value * self.large_QS_ratio

        # 判断波谷是否足够深
        valley_deep_enough = True
        if exceed_r_wave:
            valley_deep_enough = valley_value <= self.deep_valley_ratio * peak_value
        elif is_large_wave:
            valley_deep_enough = valley_value <= r_wave_value * self.deep_valley_ratio

        # 基础质量检查
        base_quality_check = (
                (prominence >= self.min_prominence * 0.8) or
                (prominence_ratio >= self.min_prominence_ratio * 0.8 and peak_value >= self.min_amplitude_pt)
        )

        # 最终质量判断
        is_quality_peak = base_quality_check and valley_deep_enough

        return is_quality_peak, prominence, prominence_ratio, exceed_r_wave, valley_deep_enough

    # ==================== 波峰分析统一处理 ====================
    def analyze_envelope_peaks(self, envelope, r_peak_relative_pos, min_distance, wave_type, envelope_name):
        """统一处理包络线波峰分析"""
        envelope_type = "lower" if envelope_name == "下" else "upper"

        # 检测所有波峰
        all_peaks = self.detect_peaks_in_envelope(
            envelope, min_height_ratio=0.03, min_amplitude_pt=self.min_amplitude_pt, envelope_type=envelope_type
        )

        # 过滤有效波峰
        valid_peaks = self.filter_peaks_by_distance_and_amplitude(
            all_peaks, envelope, r_peak_relative_pos, min_distance, wave_type
        )

        # 评估波峰质量
        quality_peaks = []
        low_quality_peaks = []
        exceed_r_info = []

        for peak in valid_peaks:
            is_quality, prominence, prominence_ratio, exceed_r_wave, valley_deep_enough = self.evaluate_peak_quality(
                envelope, peak, r_peak_relative_pos, wave_type
            )

            peak_info = {
                'peak': peak, 'exceed_r_wave': exceed_r_wave, 'valley_deep_enough': valley_deep_enough,
                'prominence': prominence, 'prominence_ratio': prominence_ratio
            }

            if is_quality:
                quality_peaks.append(peak)
            else:
                low_quality_peaks.append(peak)

            if exceed_r_wave:
                exceed_r_info.append(peak_info)

        return {
            'all_peaks_count': len(all_peaks),
            'quality_peaks': quality_peaks,
            'low_quality_peaks': low_quality_peaks,
            'exceed_r_info': exceed_r_info,
            'excluded_peaks_count': len(all_peaks) - len(valid_peaks)
        }

    # ==================== Q波时刻一致性检查 ====================
    def check_q_wave_timing_consistency(self, signals, region_start, region_end, min_amplitude_pt):
        """检查各通道Q波峰时刻的一致性"""
        region_length = region_end - region_start + 1
        if region_length < 3:
            return False, "区域太短"

        q_peak_times = []

        # 检测各通道Q波峰
        for signal in signals:
            channel_region = signal[region_start:region_end + 1]
            min_idx = np.argmin(channel_region)
            min_value = channel_region[min_idx]

            if abs(min_value) >= min_amplitude_pt:
                # 检查是否为有效波峰
                is_peak = True
                if min_idx > 0 and channel_region[min_idx - 1] <= min_value:
                    is_peak = False
                if min_idx < len(channel_region) - 1 and channel_region[min_idx + 1] <= min_value:
                    is_peak = False

                if is_peak:
                    absolute_time = region_start + min_idx
                    q_peak_times.append(absolute_time)

        if len(q_peak_times) < len(signals) * 0.3:
            return False, f"检测到Q波峰的通道太少({len(q_peak_times)}/{len(signals)})"

        # 检查时刻一致性
        q_peak_times = np.array(q_peak_times)
        max_count_in_range = 0
        best_range_start = None

        for start_time in range(int(np.min(q_peak_times)), int(np.max(q_peak_times)) + 1):
            end_time = start_time + 10
            count_in_range = np.sum((q_peak_times >= start_time) & (q_peak_times < end_time))
            if count_in_range > max_count_in_range:
                max_count_in_range = count_in_range
                best_range_start = start_time

        consistency_ratio = max_count_in_range / len(q_peak_times)
        is_consistent = (max_count_in_range >= len(q_peak_times) * 0.3)

        consistency_info = {
            '总通道数': len(signals),
            '检测到Q波峰的通道数': len(q_peak_times),
            '最佳10点范围': f"{best_range_start}-{best_range_start + 10}",
            '范围内Q波峰数量': max_count_in_range,
            'Q波峰一致性比例': consistency_ratio,
            '判定结果': '时刻一致' if is_consistent else '时刻不一致'
        }

        return is_consistent, consistency_info

    # ==================== 状态判断 ====================
    def determine_wave_status_combined(self, upper_analysis, lower_analysis, wave_type,
                                       signals=None, region_start=None, region_end=None):
        """根据上下包络线的波峰质量综合确定波形状态"""
        # 检查波谷深度
        shallow_valley_count = 0
        for analysis in [upper_analysis, lower_analysis]:
            for info in analysis['exceed_r_info']:
                if not info['valley_deep_enough']:
                    shallow_valley_count += 1

        # 如果存在波谷不够深的情况，直接判定为融合波
        if shallow_valley_count > 0:
            fusion_status = "QR_fusion" if wave_type == "Q" else "RS_fusion"
            return fusion_status, None

        # 统计波峰数量
        total_quality_peaks = len(upper_analysis['quality_peaks']) + len(lower_analysis['quality_peaks'])
        total_low_quality_peaks = len(upper_analysis['low_quality_peaks']) + len(lower_analysis['low_quality_peaks'])

        timing_info = None
        if total_quality_peaks > 0:
            return "normal", timing_info
        elif total_low_quality_peaks > 0:
            initial_fusion_status = "QR_fusion" if wave_type == "Q" else "RS_fusion"

            # Q波时刻一致性检查
            if wave_type == "Q" and signals is not None and region_start is not None and region_end is not None:
                is_consistent, timing_info = self.check_q_wave_timing_consistency(
                    signals, region_start, region_end, self.min_amplitude_pt)
                if is_consistent:
                    return "normal", timing_info
                else:
                    return initial_fusion_status, timing_info
            else:
                return initial_fusion_status, timing_info
        else:
            return "pending", timing_info

    # ==================== 理由生成 ====================
    def get_reasoning_combined(self, status, upper_analysis, lower_analysis, min_distance, wave_type, timing_info=None):
        """生成分析理由"""
        total_quality = len(upper_analysis['quality_peaks']) + len(lower_analysis['quality_peaks'])
        total_low_quality = len(upper_analysis['low_quality_peaks']) + len(lower_analysis['low_quality_peaks'])
        excluded_total = upper_analysis['excluded_peaks_count'] + lower_analysis['excluded_peaks_count']

        # 排除信息
        exclusion_note = ""
        if excluded_total > 0:
            exclusion_note = f"(排除R波{min_distance}点缓冲区内和幅度<0.25pt的波峰: 上包络线{upper_analysis['excluded_peaks_count']}个, 下包络线{lower_analysis['excluded_peaks_count']}个)"

        direction = "右侧" if wave_type == "Q" else "左侧"

        # 超过R波信息
        total_exceed_r = len(upper_analysis['exceed_r_info']) + len(lower_analysis['exceed_r_info'])
        exceed_r_info = ""
        if total_exceed_r > 0:
            shallow_valleys = sum(1 for info in upper_analysis['exceed_r_info'] + lower_analysis['exceed_r_info']
                                  if not info['valley_deep_enough'])
            if shallow_valleys > 0:
                exceed_r_info = f"; 其中{total_exceed_r}个波峰超过R波，{shallow_valleys}个波谷不够深(>{self.deep_valley_ratio}pt)"
            else:
                exceed_r_info = f"; 其中{total_exceed_r}个波峰超过R波，但波谷都足够深(<={self.deep_valley_ratio}pt)"

        # 时刻一致性信息
        timing_note = ""
        if wave_type == "Q" and timing_info is not None:
            # 确保 timing_info 是字典类型
            if isinstance(timing_info, dict):
                if status == "normal" and total_quality == 0:
                    timing_note = f"; 时刻一致性检查: {timing_info['范围内Q波峰数量']}/{timing_info['检测到Q波峰的通道数']}个Q波峰在{timing_info['最佳10点范围']}范围内，判定为时刻一致"
                elif timing_info['判定结果'] == '时刻不一致':
                    timing_note = f"; 时刻一致性检查: {timing_info['范围内Q波峰数量']}/{timing_info['检测到Q波峰的通道数']}个Q波峰在{timing_info['最佳10点范围']}范围内，时刻不一致"
                elif timing_info.get('错误信息'):
                    timing_note = f"; 时刻一致性检查: {timing_info['错误信息']}"
            else:
                # 如果 timing_info 是字符串，直接使用
                timing_note = f"; 时刻一致性检查: {timing_info}"

        # 生成最终理由
        if status == "normal":
            if total_quality > 0:
                detail = self._get_peak_detail_string(upper_analysis['quality_peaks'], lower_analysis['quality_peaks'])
                return f"检测到{total_quality}个高质量波峰({detail}){exclusion_note}{exceed_r_info}{timing_note} → 与R波{direction}分离度足够，波形正常"
            else:
                return f"包络线分析检测到{total_low_quality}个低质量波峰，但Q波时刻一致性检查通过{timing_note}{exclusion_note}{exceed_r_info} → 波形正常"
        elif status in ["QR_fusion", "RS_fusion"]:
            fusion_type = "QR融合" if status == "QR_fusion" else "RS融合"
            detail = self._get_peak_detail_string(upper_analysis['low_quality_peaks'],
                                                  lower_analysis['low_quality_peaks'])
            return f"仅检测到{total_low_quality}个低质量波峰({detail}){exclusion_note}{exceed_r_info}{timing_note} → 与R波{direction}分离度不足，{fusion_type}"
        else:  # pending
            return f"未检测到有效波峰{exclusion_note}{exceed_r_info}{timing_note} → 状态待定(波形缺失或与R波融合)"

    def _get_peak_detail_string(self, upper_peaks, lower_peaks):
        """生成波峰详情字符串"""
        total = len(upper_peaks) + len(lower_peaks)
        if total > 1:
            return f"上包络线{len(upper_peaks)}个+下包络线{len(lower_peaks)}个"
        else:
            return f"{'上' if len(upper_peaks) > 0 else '下'}包络线{total}个"

    # ==================== 主要分析方法 ====================
    def analyze_wave_region_combined(self, signals, region_start, region_end, wave_type, r_peak_pos, qrs_duration):
        """波形区域综合分析"""
        region_signals = [sig[region_start:region_end + 1] for sig in signals]
        region_length = region_end - region_start + 1

        # 计算R波峰在当前区域中的相对位置和自适应缓冲距离
        r_peak_relative_pos = r_peak_pos - region_start
        min_distance = math.ceil(qrs_duration * 0.1)

        # 提取并平滑包络线
        upper_env, _ = self.extract_envelope(region_signals, "upper")
        lower_env, _ = self.extract_envelope(region_signals, "lower")
        smooth_upper = self.smooth_envelope(upper_env, window_length=min(7, region_length // 2 * 2 + 1))
        smooth_lower = self.smooth_envelope(lower_env, window_length=min(7, region_length // 2 * 2 + 1))

        # 分析上下包络线
        upper_analysis = self.analyze_envelope_peaks(smooth_upper, r_peak_relative_pos, min_distance, wave_type, "上")
        lower_analysis = self.analyze_envelope_peaks(smooth_lower, r_peak_relative_pos, min_distance, wave_type, "下")

        # 综合判断状态
        status, timing_info = self.determine_wave_status_combined(
            upper_analysis, lower_analysis, wave_type, signals, region_start, region_end
        )

        # 构建分析结果
        analysis_result = {
            '状态': status,
            'QRS区间长度': region_length,
            '上包络线高质量波峰数': len(upper_analysis['quality_peaks']),
            '上包络线低质量波峰数': len(upper_analysis['low_quality_peaks']),
            '下包络线高质量波峰数': len(lower_analysis['quality_peaks']),
            '下包络线低质量波峰数': len(lower_analysis['low_quality_peaks']),
            'R波缓冲距离': min_distance,
            '上包络线高质量波峰位置': upper_analysis['quality_peaks'],
            '上包络线低质量波峰位置': upper_analysis['low_quality_peaks'],
            '下包络线高质量波峰位置': lower_analysis['quality_peaks'],
            '下包络线低质量波峰位置': lower_analysis['low_quality_peaks'],
            '上包络线超过R波信息': upper_analysis['exceed_r_info'],
            '下包络线超过R波信息': lower_analysis['exceed_r_info'],
            'Q波时刻一致性信息': timing_info,
            '原因': self.get_reasoning_combined(status, upper_analysis, lower_analysis, min_distance, wave_type,
                                                timing_info),
            'smooth_upper': smooth_upper,
            'smooth_lower': smooth_lower
        }

        return analysis_result

    def analyze_qrs_morphology(self, signals, Qh, Rp, Sr, Qp=None, Sp=None):
        """QRS波形形态分析 - 使用上下包络线综合分析"""
        qrs_duration = Sr - Qh

        # Q波分析：从Qh到Rp的区域
        q_analysis = self.analyze_wave_region_combined(signals, Qh, Rp, "Q", Rp, qrs_duration)

        # S波分析：从Rp到Sr的区域
        s_analysis = self.analyze_wave_region_combined(signals, Rp, Sr, "S", Rp, qrs_duration)

        result = {
            'Q_status': q_analysis['状态'],
            'S_status': s_analysis['状态'],
            'Q_analysis': q_analysis,
            'S_analysis': s_analysis,
            'qrs_duration': qrs_duration,
            'time_points': {'Qh': Qh, 'Qp': Qp, 'Rp': Rp, 'Sp': Sp, 'Sr': Sr}
        }

        return result

    def analyze_with_morphology(self, mcg_file):
        """完整QRS分析"""
        ts = gettime(mcg_file)
        Qh, Qp, Rp, Sp, Sr = ts[3], ts[4], ts[5], ts[6], ts[7]
        ls36 = get_mcg36(mcg_file, 0)
        result = self.analyze_qrs_morphology(ls36, Qh, Rp, Sr, Qp, Sp)
        report = {
            'qrs_duration': result['qrs_duration'],
            'q_wave_status': result['Q_status'],
            's_wave_status': result['S_status'],
        }

        return report


class QRS_stable_Analyzer:
    """
    结合包络线和单通道分析
    """
    def __init__(self):
        self.chan_num = 2

    def cmpr_qrs_mt1(self, ls36, ts0, ths, dhs, th1):
        '''计算QRS波单通道参数: 1/2/.../12波的类型'''
        Qh, Sr = ts0
        ms0 = []
        l1 = [ls36[i][Qh:Sr + 1] for i in range(len(ls36))]
        for i in range(len(l1)):
            # 基于双比例
            l2 = l1[i]
            M0 = max(max(l2), -min(l2))
            ik0, ik1, ik2 = cal_bfbg0(l2, th1)
            pt0 = sorted(set(ik0 + ik1 + ik2))
            l3 = [round(l2[i1] / M0, 3) for i1 in range(len(l2))]
            QRSs = cmpr_qrspt1(l3, pt0, ths, dhs, ik1, ik2)
            mg = [[l3[ii] for ii in jj] for jj in QRSs]
            fg = cmpr_tpqrs0(mg)
            ms0.append(fg + 1)
        return ms0

    def get_wave_type_name(self, wave_type):
        """获取波形类型名称"""
        wave_types = ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
        if 0 <= wave_type <= 11:
            return wave_types[wave_type]
        return f"Unknown({wave_type})"

    def check_q_s_waves_in_channels(self, qrswv0, target_channels=None):
        """
        检查特定通道中Q波和S波的存在性
        target_channels: 目标通道列表（从1开始计数），默认为[20,21,22,26,27,28,29,32,33,34,35,36]
        返回: (q_wave_exists, s_wave_exists, q_count, s_count, channel_details)
        """
        if target_channels is None:
            target_channels = [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]

        target_indices = [ch - 1 for ch in target_channels]
        q_wave_count = 0
        s_wave_count = 0
        channel_details = []
        for i, idx in enumerate(target_indices):
            if idx < len(qrswv0):
                wave_type = qrswv0[idx]
                wave_name = self.get_wave_type_name(wave_type)
                has_q = self.has_q_wave(wave_type)
                has_s = self.has_s_wave(wave_type)
                if has_q:
                    q_wave_count += 1
                if has_s:
                    s_wave_count += 1
                channel_details.append({
                    'channel': target_channels[i],
                    'wave_type': wave_type,
                    'wave_name': wave_name,
                    'has_q': has_q,
                    'has_s': has_s
                })

        return q_wave_count, s_wave_count, channel_details

    def has_q_wave(self, wave_type):
        """
        判断单通道波形类型中是否包含Q波
        wave_type: 0-11的数字，对应['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
        """
        wave_types = ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
        # 包含Q波的类型：qR(2), QR(3), Qr(4), Q(8), QQ(9), QRS(10), QRSs(11)
        q_wave_types = [2, 3, 4, 8, 9, 10, 11]
        return wave_type in q_wave_types

    def has_s_wave(self, wave_type):
        """
        判断单通道波形类型中是否包含S波
        wave_type: 0-11的数字，对应['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
        """
        wave_types = ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
        # 包含S波的类型：Rs(5), RS(6), rS(7), QRS(10), QRSs(11)
        s_wave_types = [5, 6, 7, 10, 11]
        return wave_type in s_wave_types

    def gettimes(self, mcgfile, noqs=0, g1='', tm=[]):
        '''汇总时刻点算法: 无bug'''
        try:
            rts = time1(mcgfile, noqs, g1, tm)
            # print(1)
            return rts
        except:
            try:
                rts = time2(mcgfile, noqs, g1, tm)
                # print(2)
                return rts
            except:
                if type(mcgfile) == str:
                    doc = open(mcgfile, 'r')
                    mcgfile = doc.readlines()
                    doc.close()
                rts = time3(mcgfile, g1)
                # print(3)
                RR = len(mcgfile)
                rts += [RR]
                if noqs:
                    rts += [0, 0]
                return rts

    def analyze_qrs_with_channel_check(self, mcgfile, ts):
        """
        结合包络线和单通道分析的QRS波分析
        分层判断逻辑：
        1. 包络线检测正常(0) -> 正常
        2. 包络线检测缺失(1) -> 进一步单通道检测：>=3个通道有波 -> 融合，否则 -> 缺失
        3. 包络线检测融合(2) -> 融合
        """
        morphology_analyzer = QRSMorphologyAnalyzer()
        morphology_report = morphology_analyzer.analyze_with_morphology(mcgfile)
        # 提取包络线分析结果
        envelope_q_status = morphology_report['q_wave_status']  # 如 'Q_normal', 'Q_missing'
        envelope_s_status = morphology_report['s_wave_status']  # 如 'S_normal', 'S_missing'

        ls36 = get_mcg36(mcgfile, 0)
        lns = get_lines1(mcgfile)
        ls36n, lnsn = cmpr_TPbase1(mcgfile, '', ts)
        tm1 = self.gettimes(lnsn)[:-1]
        ths1, dhs1 = [0.1, 0.3, 0.13, 0.7, 0.045], [55, 40, 10, 60]
        t0 = round(20 * len(lns) / 600)  # 前后延长20ms(心率60)
        ts1 = [tm1[3] - t0, tm1[7] + t0]

        # 单通道QRS波分析
        qrswv0 = self.cmpr_qrs_mt1(ls36n, ts1, ths1, dhs1, th1=5)

        # 检查特定通道中的Q波和S波
        q_count, s_count, channel_details = self.check_q_s_waves_in_channels(qrswv0)

        # Q波
        if 'normal' in envelope_q_status:  # 包络线检测正常
            final_noq = 0
            q_status = "normal"
        elif 'pending' in envelope_q_status:
            if q_count >= self.chan_num:  # 单通道检测>=2个通道有Q波
                final_noq = 2  # 融合
                q_status = "QR_fusion"
            else:
                final_noq = 1  # 缺失
                q_status = "missing"
        else:
            final_noq = 2  # 融合
            q_status = "QR_fusion"

        # S波
        if 'normal' in envelope_s_status:  # 包络线检测正常
            final_nos = 0
            s_status = "normal"
        elif 'pending' in envelope_s_status:  # 包络线检测缺失
            if s_count >= self.chan_num:  # 单通道检测>=2个通道有S波
                final_nos = 2  # 融合
                s_status = "RS_fusion"
            else:
                final_nos = 1  # 缺失
                s_status = "missing"
        else:  # 'RS_fusion' 包络线检测融合
            final_nos = 2  # 融合
            s_status = "RS_fusion"

        return ls36, q_status, s_status



class QRS_AnomalyScorer:
    """
    QRS段转动异常指标评分系统
    生成0-1之间的异常指标，值越大越异常，值越小越正常
    """
    def __init__(self):
        # QR段异常权重定义（高异常程度的权重更大）
        self.qr_weights = {
            # 高异常程度异常（权重较大）
            "Q起始角度异常": 0.25,
            "负磁极不转": 0.25,
            "QR转动不足": 0.25,
            "R时刻方向异常": 0.25,
            "顺时针转动": 0.20,

            # 低异常程度异常（权重较小）
            "折返转动": 0.10,
            "临近R时刻才转到位": 0.15,
            "角度跳转": 0.10,
            "前后帧跳转": 0.10
        }

        # RS段异常权重定义
        self.rs_weights = {
            "RS段转动不足": 0.9,
            "Sp时刻未转出第一象限": 0.9,
            "Sp时刻角度在禁止区域": 0.9
        }

        # 归一化参数
        self.qr_max_score = 1.0  # QR段最大可能分数（用于归一化）
        self.rs_max_score = 1.0  # RS段最大可能分数（用于归一化）
        self.S_QUADRANT_MAX_1 = 90  # S波所处正常区域最大角度(三峰分明)
        self.MIN_RS_ROTATION_ANGLE = 50  # RS段最小转动角度

    def _sigmoid_normalize(self, score, max_score, steepness=2.0):
        """
        使用sigmoid函数进行归一化
        """
        # 将分数缩放到合适的范围
        scaled_score = (score / max_score) * steepness * 2 - steepness
        # 应用sigmoid函数
        normalized = 1 / (1 + math.exp(-scaled_score))
        return normalized

    def calculate_qr_anomaly_score(self, qr_result):
        """
        计算QR段异常指标
        qr_result: QRS_rotate类返回的QR段分析结果
        float: 0-1之间的异常指标，值越大越异常
        """
        if qr_result["status"] == "正常":
            return 0.0
        if qr_result["status"] == "错误":
            return 1.0

        anomalies = qr_result.get("anomalies", [])
        if not anomalies:
            return 0.0

        # 计算基础异常分数
        base_score = 0.0
        for anomaly in anomalies:
            weight = self.qr_weights.get(anomaly, 0.05)  # 未知异常给默认小权重
            base_score += weight

        # 多异常叠加效应：异常种类越多，额外惩罚越大
        anomaly_count = len(anomalies)
        if anomaly_count > 1:
            # 使用对数增长来避免过度惩罚
            multiplier = 1 + 0.2 * math.log(anomaly_count)
            base_score *= multiplier

        # 根据转动角度进行微调
        total_angle = qr_result.get("total_angle", 0)
        if total_angle < 30:  # 转动角度过小，增加异常程度
            base_score *= 1.3
        elif total_angle > 150:  # 转动角度过大，也增加异常程度
            base_score *= 1.2

        # 归一化到0-1区间
        normalized_score = self._sigmoid_normalize(base_score, self.qr_max_score, steepness=1.5)
        return round(normalized_score, 4)

    def calculate_rs_anomaly_score(self, rs_result):
        """
        计算RS段异常指标 - 根据实际异常值距离标准范围的远近来计算
        """
        if rs_result["status"] == "正常":
            return 0.0
        total_angle = rs_result.get("total_angle", 0)  # RS段总转动角度
        sp_angle = rs_result.get("sp_angle", None)  # Sp时刻的角度
        reason = rs_result.get("reason", "")
        base_score = 0.0

        # 1. RS段转动不足的连续性评分
        if "RS段转动不足" in reason:
            normal_min_angle = self.MIN_RS_ROTATION_ANGLE  # 正常转动的最小角度
            if total_angle < normal_min_angle:  # 计算偏离程度：距离正常范围越远，异常分数越高
                deviation_ratio = (normal_min_angle - total_angle) / normal_min_angle
                rotation_score = min(deviation_ratio ** 1.5, 1.0) * self.rs_weights["RS段转动不足"]  # 使用平方函数让偏离越大惩罚越重
                base_score += rotation_score

        # 2. Sp时刻未转出第一象限的连续性评分
        if "未转出第一象限" in reason and sp_angle is not None:
            # 第一象限：0°-90°
            if 0 <= sp_angle <= 90:
                # 计算到第一象限边界的最小距离
                distance_to_0 = sp_angle  # 到0°的距离
                distance_to_90 = 90 - sp_angle  # 到90°的距离
                min_distance_to_boundary = min(distance_to_0, distance_to_90)
                # 第一象限的"半径"是45°（从中心到边界的距离）
                max_distance_to_boundary = 45
                # 距离边界越远（越靠近45°中心），异常程度越高
                penetration_ratio = 1.0 - (min_distance_to_boundary / max_distance_to_boundary)
                penetration_ratio = max(0.0, min(1.0, penetration_ratio))
                quadrant_score = (penetration_ratio ** 0.8) * self.rs_weights["Sp时刻未转出第一象限"]
                base_score += quadrant_score

        # 3. Sp时刻角度在禁止区域的连续性评分
        if "禁止区域" in reason and sp_angle is not None:
            # 异常区域：315°-135°（跨越0°的连续区域，即第一象限扩展版）
            forbidden_zone = {"start": 315, "end": 135, "severity": 1.0}
            if self._is_angle_in_zone(sp_angle, forbidden_zone["start"], forbidden_zone["end"]):
                # 计算角度在禁止区域中的位置
                # 对于跨越0°的区域，将315°-360°部分映射为-45°-0°，便于计算中心点
                if sp_angle >= 315:
                    adjusted_angle = sp_angle - 360  # 315°-360° -> -45°-0°
                else:
                    adjusted_angle = sp_angle  # 0°-135° 保持不变

                # 区域中心点：(-45° + 135°) / 2 = 45°
                zone_center = 45
                # 区域总宽度：315°到135°的角度跨度 = 180°
                zone_width = 180

                # 计算距离中心点的距离
                distance_from_center = abs(adjusted_angle - zone_center)

                # 距离中心越近，异常程度越高
                penetration_ratio = 1.0 - (distance_from_center / (zone_width / 2))
                penetration_ratio = max(0.0, min(1.0, penetration_ratio))
                forbidden_score = (penetration_ratio ** 0.4) * forbidden_zone["severity"] * self.rs_weights[
                    "Sp时刻角度在禁止区域"]
                base_score += forbidden_score

        # 计算异常类型数量
        anomaly_count = sum([
            1 if "RS段转动不足" in reason else 0,
            1 if "未转出第一象限" in reason else 0,
            1 if "禁止区域" in reason else 0
        ])

        if anomaly_count > 1:
            # 多重异常时适度增加分数
            base_score *= (1.0 + 0.15 * (anomaly_count - 1))

        # 归一化到0-1区间
        normalized_score = self._sigmoid_normalize(base_score, self.rs_max_score, steepness=2.0)
        return round(normalized_score, 2)

    def _is_angle_in_zone(self, angle, start, end):
        """判断角度是否在指定区域内（处理跨越0°的情况）"""
        if start <= end:
            return start <= angle <= end
        else:  # 跨越0°的区域，如315°-45°
            return angle >= start or angle <= end

    def _get_zone_center(self, start, end):
        """计算区域的中心角度"""
        if start <= end:
            return (start + end) / 2
        else:  # 跨越0°的区域
            center = (start + end + 360) / 2
            return center if center <= 360 else center - 360

    def _calculate_angular_distance(self, angle1, angle2):
        """计算两个角度之间的最小角距离"""
        diff = abs(angle1 - angle2)
        return min(diff, 360 - diff)

    def _get_zone_width(self, start, end):
        """计算区域的角度宽度"""
        if start <= end:
            return end - start
        else:  # 跨越0°的区域
            return (360 - start) + end

    def calculate_combined_anomaly_score(self, qr_result, rs_result):
        """
        计算QRS段综合异常指标
        Args:
            qr_result: QR段分析结果
            rs_result: RS段分析结果
        Returns:
            dict: 包含QR、RS和综合异常指标的字典
        """
        qr_score = self.calculate_qr_anomaly_score(qr_result)
        rs_score = self.calculate_rs_anomaly_score(rs_result)
        return {
            "qr_anomaly_score": qr_score,
            "rs_anomaly_score": rs_score,
            "qr_level": self._get_anomaly_level(qr_score),
            "rs_level": self._get_anomaly_level(rs_score)}

    def _get_anomaly_level(self, score):
        """
        根据异常分数确定异常程度等级
        Args:
            score: 异常分数(0-1)
        Returns:
            str: 异常程度等级
        """
        if score < 0.2:
            return "正常"
        elif score < 0.5:
            return "轻微异常"
        elif score < 0.8:
            return "中度异常"
        else:
            return "重度异常"
