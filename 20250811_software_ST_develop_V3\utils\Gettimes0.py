"""
Author: <PERSON><PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2025/02/17 16:40
desc: 整合时刻点生成算法3个版本, SQUID/SQUIDnew/OPM
environment: py36
option:
    心磁文档、时刻点
"""


import sys
from utils.gettimes1 import gettime as time1
from utils.gettimes import gettime as time2
from utils.gettimes2 import cal_times0 as time3


def gettimes(mcgfile, noqs=0, g1='', tm=[]):
    '''汇总时刻点算法: 无bug'''
    # if type(mcgfile)==str:
    #     doc = open(mcgfile, 'r')
    #     mcgfile = doc.readlines()
    #     doc.close()
    # rts = time3(mcgfile, g1)
    # RR = len(mcgfile)
    # rts += [RR]
    # if noqs:
    #     rts += [0, 0]
    # return rts
    
    try:
        rts = time1(mcgfile, noqs, g1, tm)
        # print(1)
        return rts
    except:
        try:
            rts = time2(mcgfile, noqs, g1, tm)
            # print(2)
            return rts
        except:
            if type(mcgfile)==str:
                doc = open(mcgfile, 'r')
                mcgfile = doc.readlines()
                doc.close()
            rts = time3(mcgfile, g1)
            # print(3)
            RR = len(mcgfile)
            rts += [RR]
            if noqs:
                rts += [0, 0]
            return rts


if __name__ == '__main__':
    mcgfile = sys.argv[1]  # id文档
    Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettimes(mcgfile)
    print('Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR:')
    print(Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR)
