'''
@Project ：ML_Pipline
@File    ：stats_vis.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2024/8/1 上午8:55
@Discribe：
'''

from collections import Counter

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from sklearn.manifold import TSNE, Isomap, MDS


# import umap
class FeatureSelectionStats:
    def __init__(self, record_results):
        self.record_results = record_results

    def stat_acc_vs_feature_counts(self):
        """
        传入前面生成的筛选结果记录，生成精度与特征数量的统计图
        :param record_results:
        :return:
        """
        # 提取特征数量
        feature_counts = [result[0] for result in self.record_results[0]]

        # 初始化一个字典来存储各特征数量下的测试精度列表
        accuracy_per_feature_count = {count: [] for count in feature_counts}

        # 遍历每一折的结果
        for fold_results in self.record_results:
            for result in fold_results:
                feature_count, _, test_accuracy, _ = result
                accuracy_per_feature_count[feature_count].append(test_accuracy)

        # 计算平均测试精度并准备数据用于绘图
        average_accuracies = []
        for count in feature_counts:
            accuracies = accuracy_per_feature_count[count]
            average_accuracies.append(np.mean(accuracies))

        # 绘制每一折的测试精度曲线，使线条较淡和较细
        for i, fold_results in enumerate(self.record_results):
            accuracies = [result[2] for result in fold_results]
            plt.plot(feature_counts, accuracies, label=f'Fold {i + 1}', linewidth=1, alpha=0.5)

        # 绘制平均测试精度曲线，使线条更突出
        plt.plot(feature_counts, average_accuracies, label='Average', color='black', linewidth=2)

        # 设置图表标题和标签
        plt.title('Test Accuracy vs. Feature Count')
        plt.xlabel('Feature Count')
        plt.ylabel('Test Accuracy')
        plt.gca().invert_xaxis()  # 因为特征数量是从大到小
        plt.xscale('log')  # 使用对数尺度
        plt.legend()
        plt.grid(True)
        plt.show(block=True)

    def stat_common_feature_counts(self):
        """
        统计通用的，某数量特征使用重复，如 筛选出特征数量为70时在所有折上重复使用的特征名
        :param record_results:
        :return:
        """
        features_at_70 = []
        for fold_results in self.record_results:
            for feature_count, _, _, features in fold_results:
                if feature_count == 70:
                    features_at_70.append(features)

        # 汇总所有折的特征名，并计算每个特征名的出现次数
        all_features = [feature for sublist in features_at_70 for feature in sublist]
        feature_counts = Counter(all_features)

        # 过滤掉出现次数小于等于2次的特征
        filtered_features = {feature: count for feature, count in feature_counts.items() if count > 1}

        # 绘图
        plt.figure(figsize=(10, 8))
        plt.bar(filtered_features.keys(), filtered_features.values())

        # 设置图表标题和标签
        plt.title('Frequency of Feature Names at Feature Count 70 Across Folds (Used >=2 Times)')
        plt.xlabel('Feature Names')
        plt.ylabel('Frequency of Use Across Folds')
        plt.xticks(rotation=30, ha='right', fontsize=8)  # 将特征名标签旋转45度以便阅读

        plt.grid(True)
        plt.show(block=True)


class DimensionalityReductionVisualizer:
    def __init__(self, scaled_data, labels):
        self.scaled_data = scaled_data
        self.labels = labels

    def _create_scatter_plot(self, components, method_name):
        df = pd.DataFrame(data=components, columns=['Component1', 'Component2'])
        df['label'] = self.labels
        plt.figure(figsize=(12, 8))
        sns.set_style("whitegrid")
        scatter = sns.scatterplot(data=df, x='Component1', y='Component2', hue='label',
                                  palette='deep', s=30, alpha=0.7)
        plt.title(f'{method_name} of Training Data', fontsize=20)
        plt.xlabel('First Component', fontsize=14)
        plt.ylabel('Second Component', fontsize=14)
        plt.legend(title='Label', title_fontsize='13', fontsize='11')
        plt.show()

    def pca(self):
        pca = PCA(n_components=2)
        components = pca.fit_transform(self.scaled_data)
        self._create_scatter_plot(components, 'PCA')

    def tsne(self):
        tsne = TSNE(n_components=2, random_state=0)
        components = tsne.fit_transform(self.scaled_data)
        self._create_scatter_plot(components, 't-SNE')

    # def umap(self):
    #     umap_reducer = umap.UMAP(n_components=2, random_state=0)
    #     components = umap_reducer.fit_transform(self.scaled_data)
    #     self._create_scatter_plot(components, 'UMAP')

    def isomap(self):
        isomap = Isomap(n_components=2)
        components = isomap.fit_transform(self.scaled_data)
        self._create_scatter_plot(components, 'Isomap')

    def lda(self):
        lda = LinearDiscriminantAnalysis(n_components=2)
        components = lda.fit_transform(self.scaled_data, self.labels)
        self._create_scatter_plot(components, 'LDA')

    def mds(self):
        mds = MDS(n_components=2, random_state=0)
        components = mds.fit_transform(self.scaled_data)
        self._create_scatter_plot(components, 'MDS')

    def visualize(self, dim_method='pca'):
        dim_method = dim_method.lower()
        if dim_method == 'pca':
            self.pca()
        elif dim_method == 'tsne':
            self.tsne()
        elif dim_method == 'isomap':
            self.isomap()
        elif dim_method == 'lda':
            self.lda()
        elif dim_method == 'mds':
            self.mds()
        else:
            raise ValueError(
                f"Unknown dimension reduction method: {dim_method}. Available methods: 'pca', 'tsne', 'isomap', 'lda', 'mds'.")
