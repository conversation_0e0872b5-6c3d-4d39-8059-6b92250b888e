"""
@ Author: <PERSON>
@ E-mail: <EMAIL>
@ Date: 2024/11/21 9：10
用于作图验证
"""
from mayavi import mlab
from func.ventricle_tool_no import *
from func.fun_colletion import *
pi = math.pi
import meshio
# import time


class Data:
    def __init__(self, file_name):
        self.file_name = file_name

    def read_mcg(self):
        with open(self.file_name, 'r') as ff:
            file = ff.read()  # 读取的是字符串
        da = []
        for i in file.split():  # 把上面字符串记为数字插入
            da.append(float(i))
        datat = []
        for i in range(int(len(da) / 37)):
            datat.append(da[37 * i + 1:37 * (i + 1)])
        mcg_data = np.array(datat)
        return mcg_data


def main_part1(mcg_path, heart_path, time_loc):
    """
    :param mcg_path: 心磁路径
    :param heart_path: 心脏模型路径
    :param time_loc: 时刻点 np.array([Qp, Rp, Sp]), 分别是Q峰，R峰， S峰
    :return:
    """
    mesh_path, lcav_path, lcav_no_path, rcav_path = \
        heart_path + '\\heart_point.msh', heart_path + '\\heart_point_lcav_mesh.msh', \
            heart_path + '\\poisson_mesh_lcav.msh', heart_path + '\\heart_point_rcav_mesh.msh'
    DataProcess = Data(mcg_path)
    mcg_data = DataProcess.read_mcg()
    Qp, Rp, Sp = time_loc[0], time_loc[1], time_loc[2]
    check_time = np.array([Qp, Sp + 1])
    recon = SurfaceRecon(mesh_path, lcav_path, Qp, Rp, Sp, check_time, mcg_data)
    recon.get_mesh()
    recon.model_heart_No_CTA(z_A=57.4, z_B=49.52)
    recon.get_lf()
    result = recon.source_strength()
    result = np.array(result)
    heart_mesh = meshio.read('model/heart_1.obj', file_format='obj')
    heart_points = np.asarray(heart_mesh.points)
    heart_tri = np.asarray(heart_mesh.cells_dict['triangle'])
    mlab.triangular_mesh(heart_points[:, 0], heart_points[:, 1],
                         heart_points[:, 2], heart_tri, representation='mesh')
    mlab.triangular_mesh(recon.lcav_points[:, 0] * 1000 + 200, recon.lcav_points[:, 1] * 1000,
                         recon.lcav_points[:, 2] * 1000, recon.lcav_tri, representation='mesh')
    mlab.points3d(result[:, 1] * 1000, result[:, 2] * 1000, result[:, 3] * 1000, scale_factor=1)
    mlab.view(0, 0)
    mlab.show()
    return np.array(result)


if __name__ == '__main__':
    # start_time = time.time()

    mcg_path = r'data\\PLAG_2024_000059.txt'
    heart_path = 'model'
    time_loc = np.array([311, 335, 361])
    result = main_part1(mcg_path, heart_path, time_loc)
    # print(result)
    # end_time = time.time()
    # print('operator time = %f' % (end_time - start_time))
