"""
测试不同临床风险水平的预测结果
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main_v2 import DeploymentPipeline


def test_clinical_risk_levels():
    """测试不同临床风险水平"""
    print("=== 测试不同临床风险水平 ===")
    
    pipeline = DeploymentPipeline(model_version='v2.5.03')
    txt_file = 'files/txt_files/AHYK_2023_000046.txt'
    
    # 1. 低风险临床特征：年轻、正常BMI、无任何疾病史
    print("\n1️⃣ 低风险临床特征测试")
    low_risk_data = {
        'clinic_height': 170.0,       # 身高170cm
        'clinic_weight': 65.0,        # 体重65kg (BMI=22.5，正常)
        'clinic_gender': 0,           # 女性
        'clinic_age': 30,             # 30岁（年轻）
        'clinic_smoking': 0,          # 不吸烟
        'clinic_drinking': 0,         # 不饮酒
        'clinic_hypertension': 0,     # 无高血压
        'clinic_hyperlipidemia': 0,   # 无高脂血症
        'clinic_intervention': 0,     # 无既往介入
        'clinic_hospital': 3,         # 北京301医院
        'clinic_diabetes': 0,         # 无糖尿病
        'clinic_symp': 0              # 无典型症状
    }
    
    prob1, pred1 = pipeline.predict_single_file(txt_file, low_risk_data)
    risk1 = '高风险' if prob1 > 0.5 else '低风险'
    print(f"低风险临床特征: 概率={prob1:.4f}, 类别={pred1}, 风险={risk1}")
    
    # 2. 中等风险临床特征：中年、轻微超重、部分疾病史
    print("\n2️⃣ 中等风险临床特征测试")
    medium_risk_data = {
        'clinic_height': 170.0,       # 身高170cm
        'clinic_weight': 75.0,        # 体重75kg (BMI=26.0，轻微超重)
        'clinic_gender': 1,           # 男性
        'clinic_age': 50,             # 50岁（中年）
        'clinic_smoking': 0,          # 不吸烟
        'clinic_drinking': 0,         # 不饮酒
        'clinic_hypertension': 1,     # 有高血压
        'clinic_hyperlipidemia': 0,   # 无高脂血症
        'clinic_intervention': 0,     # 无既往介入
        'clinic_hospital': 3,         # 北京301医院
        'clinic_diabetes': 0,         # 无糖尿病
        'clinic_symp': 1              # 有典型症状
    }
    
    prob2, pred2 = pipeline.predict_single_file(txt_file, medium_risk_data)
    risk2 = '高风险' if prob2 > 0.5 else '低风险'
    print(f"中等风险临床特征: 概率={prob2:.4f}, 类别={pred2}, 风险={risk2}")
    
    # 3. 高风险临床特征：老年、超重、多种疾病史
    print("\n3️⃣ 高风险临床特征测试")
    high_risk_data = {
        'clinic_height': 170.0,       # 身高170cm
        'clinic_weight': 85.0,        # 体重85kg (BMI=29.4，超重)
        'clinic_gender': 1,           # 男性
        'clinic_age': 70,             # 70岁（老年）
        'clinic_smoking': 1,          # 吸烟
        'clinic_drinking': 1,         # 饮酒
        'clinic_hypertension': 1,     # 有高血压
        'clinic_hyperlipidemia': 1,   # 有高脂血症
        'clinic_intervention': 1,     # 有既往介入
        'clinic_hospital': 3,         # 北京301医院
        'clinic_diabetes': 1,         # 有糖尿病
        'clinic_symp': 1              # 有典型症状
    }
    
    prob3, pred3 = pipeline.predict_single_file(txt_file, high_risk_data)
    risk3 = '高风险' if prob3 > 0.5 else '低风险'
    print(f"高风险临床特征: 概率={prob3:.4f}, 类别={pred3}, 风险={risk3}")
    
    # 4. 分析结果
    print("\n📊 结果分析:")
    print(f"低风险 → 中等风险: 概率变化 {prob2-prob1:+.4f}")
    print(f"中等风险 → 高风险: 概率变化 {prob3-prob2:+.4f}")
    print(f"低风险 → 高风险: 总概率变化 {prob3-prob1:+.4f}")
    
    # 5. 验证临床特征的影响
    print("\n🔍 临床特征影响验证:")
    if prob1 < prob2 < prob3:
        print("✅ 临床特征正确影响预测结果：低风险 < 中等风险 < 高风险")
    else:
        print("⚠️ 临床特征影响可能不符合预期")
    
    return prob1, prob2, prob3


if __name__ == '__main__':
    test_clinical_risk_levels()
