'''
@Project ：pythonProject 
@File    ：get_Twave_features.py
@IDE     ：PyCharm 
<AUTHOR> Qu
@Date    ：2023/4/17 10:08 
'''

import pandas as pd
import pywt

from feature_generator.utils.get_args import *


def time_features_t(DATA_TEMP_Spline, signal_channels, loc_t_onset, loc_t_peak, loc_t_end, gap_loc, save_path=None,
                    filename=None, imShow=False):
    twave_time_dict = {}
    # 电流密度图
    col_magni = 0
    col_angle = 1
    col_xposition = 2
    col_yposition = 3
    # T波开始 到结束
    loop_set = np.array([x for x in range(loc_t_onset, loc_t_end+1, gap_loc)])

    # 某个时间点的几个重要特征  从 t波开始 到结束 每一个时间点的信息；
    rec_all = np.zeros((len(loop_set), 4))
    # print(loop_set)

    for i, loc in enumerate(loop_set):
        mfm = DATA_TEMP_Spline[loc]
        max_magni, max_angle, max_x_position, max_y_position = pcd_rec(mfm, model='e', local=loc,
                                                                       save_path=save_path, filename=filename,
                                                                       imShow=imShow, interval='tt')
        rec_all[i] = [max_magni, max_angle, max_x_position, max_y_position]

    # T峰最大电流处相关信息(最大电流密度、角度、X坐标，Y坐标)     ok
    mfm = DATA_TEMP_Spline[loc_t_peak]
    # print("loc_t_peak:{}".format(loc_t_peak))
    max_magni, max_angle, max_x_position, max_y_position = pcd_rec(mfm, model='e', local=loc_t_peak,
                                                                   save_path=save_path, filename=filename,
                                                                   imShow=imShow, interval='tt')
    dict_tempt = {
        't_tpeak_max_magni': max_magni,
        't_tpeak_max_angle': max_angle,
        't_tpeak_max_x_position': max_x_position,
        't_tpeak_max_y_position': max_y_position,
    }
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT期间每个时刻点电流强度最大的值(tt最大电流密度均值、求和、标准差、众数、中位数、最小值、最大值、长度、偏度、峰度、幅度范围)
    tt_max_magni = rec_all[:, col_magni]  # In Python, indexing starts from 0
    dict_tempt = statistic_analysis(tt_max_magni, 't_tt_max_magni')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT期间每个时刻点最大电流处的角度         ok
    tt_max_angle = rec_all[:, col_angle]
    dict_tempt = statistic_analysis_no_normaillize(tt_max_angle, 't_tt_max_angle')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)
    # pdb.set_trace()

    # TT期间最大电流处x
    tt_max_x_position = rec_all[:, col_xposition]
    dict_tempt = statistic_analysis_no_normaillize(tt_max_x_position, 't_tt_max_x_position')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT期间最大电流处y
    tt_max_y_position = rec_all[:, col_yposition]
    dict_tempt = statistic_analysis_no_normaillize(tt_max_y_position, 't_tt_max_y_position')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT期间最大电流处相对位移
    rela_disp = np.sqrt(np.sum(np.diff(rec_all[:, [col_xposition, col_yposition]], axis=0) ** 2, axis=1))
    dict_tempt = statistic_analysis_no_normaillize(rela_disp, 't_tt_max_rela_disp')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT间期最大电流处幅值和相位角
    abs_magni = np.sqrt(np.sum(rec_all[:, [col_xposition, col_yposition]] ** 2, axis=1))
    dict_tempt = statistic_analysis(abs_magni, 't_tt_max_abs_magni')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    abs_angle = np.arctan2(rec_all[:, col_yposition], rec_all[:, col_xposition]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle, 't_tt_max_abs_angle')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    ###############################################################################
    #    等磁图
    #  下面是磁场相关计算代码
    ##############################################################################
    # 等磁图
    col_ratio = 0
    col_angle = 1
    col_dist = 2
    col_x_max = 3
    col_y_max = 4
    col_x_min = 5
    col_y_min = 6
    col_area_pos = 7
    col_area_neg = 8

    loop_set = np.arange(loc_t_onset, loc_t_end+1, gap_loc)
    rec_all = np.zeros((len(loop_set), 9))

    for i, loc in enumerate(loop_set):
        mfm = DATA_TEMP_Spline[loc]
        # print(i, loc)
        ratio_minmax, angle_minmax, dist_minmax, x_max_mfm, y_max_mfm, x_min_mfm, y_min_mfm, area_pos_pole, area_neg_pole = pcd_rec(
            mfm, model='m', imShow=False)
        temp = [ratio_minmax, angle_minmax, dist_minmax, x_max_mfm, y_max_mfm, x_min_mfm, y_min_mfm, area_pos_pole,
                area_neg_pole]
        temp = np.array(temp)
        rec_all[i] = temp

    mfm = DATA_TEMP_Spline[loc_t_peak]
    result = pcd_rec(mfm, model='m', imShow=False)

    dict_tempt = {
        't_tpeak_ratio_minmax': result[0],
        't_tpeak_angle_minmax': result[1],
        't_tpeak_dist_minmax': result[2],
        't_tpeak_x_max_mfm': result[3],
        't_tpeak_y_max_mfm': result[4],
        't_tpeak_x_min_mfm': result[5],
        't_tpeak_y_min_mfm': result[6],
        't_tpeak_area_pos_pole': result[7],
        't_tpeak_area_neg_pole': result[8],
    }
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场连线比率
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_ratio], 't_tt_ratio_minmax')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场连线角度
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_angle], 't_tt_angle_minmax')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场连线距离
    dict_tempt = statistic_analysis(rec_all[:, col_dist], 't_tt_dist_minmax')

    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场最大处 x
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_x_max], 't_tt_x_max_mfm')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场最大处 y
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_y_max], 't_tt_y_max_mfm')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场最小处 x
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_x_min], 't_tt_x_min_mfm')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场最小处 y
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_y_min], 't_tt_y_min_mfm')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间正磁场面积
    dict_tempt = statistic_analysis(rec_all[:, col_area_pos], 't_tt_area_pos_pole')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间负磁场面积
    dict_tempt = statistic_analysis(rec_all[:, col_area_neg], 't_tt_area_neg_pole')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间正负磁场面积之比
    dict_tempt = {'t_tt_area_sum_pos_mod_neg': np.mean(rec_all[:, col_area_pos]) / np.mean(rec_all[:, col_area_neg])}
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # 最大最小的中心点 x,y
    center_minmax = 0.5 * np.array(
        (rec_all[:, col_x_max] + rec_all[:, col_x_min], rec_all[:, col_y_max] + rec_all[:, col_y_min])).T
    dict_tempt = statistic_analysis_no_normaillize(center_minmax[:, 0], 't_tt_center_minmax_x')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    dict_tempt = statistic_analysis_no_normaillize(center_minmax[:, 1], 't_tt_center_minmax_y')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间磁场最大点的最远距离
    twave_time_dict['t_tt_max_dist_mfm'] = max_distance(center_minmax)[0]

    # TT 期间正磁场的相对移动
    rela_dist_pos = np.sqrt(np.sum(np.diff(rec_all[:, [col_x_max, col_y_max]], axis=0) ** 2, axis=1))
    dict_tempt = statistic_analysis_no_normaillize(rela_dist_pos, 't_tt_rela_dist_pos')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 期间负磁场的相对移动
    rela_dist_neg = np.sqrt(np.sum(np.diff(rec_all[:, [col_x_min, col_y_min]], axis=0) ** 2, axis=1))
    dict_tempt = statistic_analysis_no_normaillize(rela_dist_neg, 't_tt_rela_dist_neg')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 间期正磁场处幅值和角度
    abs_magni_pos = np.sqrt(np.sum(rec_all[:, [col_x_max, col_y_max]] ** 2, axis=1))
    dict_tempt = statistic_analysis(abs_magni_pos, 't_tt_abs_magni_pos')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    abs_angle_pos = np.arctan2(rec_all[:, col_y_max], rec_all[:, col_x_max]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle_pos, 't_tt_abs_angle_pos')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 间期负磁场处幅值和角度
    abs_magni_neg = np.sqrt(np.sum(rec_all[:, [col_x_min, col_y_min]] ** 2, axis=1))
    dict_tempt = statistic_analysis(abs_magni_neg, 't_tt_abs_magni_neg')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    abs_angle_neg = np.arctan2(rec_all[:, col_y_min], rec_all[:, col_x_min]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle_neg, 't_tt_abs_angle_neg')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    # TT 间期正负极中心处幅值和角度
    abs_magni_center = np.sqrt(np.sum(center_minmax ** 2, axis=1))

    dict_tempt = statistic_analysis(abs_magni_center, 't_tt_abs_magni_center')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    abs_angle_center = np.arctan2(center_minmax[:, 1], center_minmax[:, 0]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle_center, 't_tt_abs_angle_center')
    twave_time_dict = build_dict(twave_time_dict, dict_tempt)

    return twave_time_dict


def time_features_t_new(DATA_TEMP_Spline, signal_channels, loc_t_onset, loc_t_peak, loc_t_end, gap_loc,
                    save_path=None, filename=None, imShow=False):
    twave_time_dict = {}
    loop_set = np.arange(loc_t_onset, loc_t_end + 1, gap_loc)

    # 处理电流密度图
    def process_electric_features():
        features = ['magni', 'angle', 'x_position', 'y_position']
        rec_all = np.zeros((len(loop_set), 4))

        # 计算每个时间点的特征
        for i, loc in enumerate(loop_set):
            rec_all[i] = pcd_rec(DATA_TEMP_Spline[loc], model='e', local=loc,
                                 save_path=save_path, filename=filename, imShow=imShow, interval='tt')[:4]

        # T峰特征
        peak_vals = pcd_rec(DATA_TEMP_Spline[loc_t_peak], model='e', local=loc_t_peak,
                            save_path=save_path, filename=filename, imShow=imShow, interval='tt')
        for name, val in zip(features, peak_vals):
            twave_time_dict[f't_tpeak_max_{name}'] = val

        # 统计分析
        stats = [
            ('max_magni', 0, True),
            ('max_angle', 1, False),
            ('max_x_position', 2, False),
            ('max_y_position', 3, False)
        ]
        for name, col, normalize in stats:
            func = statistic_analysis if normalize else statistic_analysis_no_normaillize
            twave_time_dict.update(func(rec_all[:, col], f't_tt_{name}'))

        # 附加计算
        xy = rec_all[:, 2:4]
        twave_time_dict.update(statistic_analysis_no_normaillize(
            np.sqrt(np.sum(np.diff(xy, axis=0) ** 2, axis=1)), 't_tt_max_rela_disp'))
        twave_time_dict.update(statistic_analysis(
            np.sqrt(np.sum(xy ** 2, axis=1)), 't_tt_max_abs_magni'))
        twave_time_dict.update(statistic_analysis_no_normaillize(
            np.arctan2(xy[:, 1], xy[:, 0]) * (180 / np.pi), 't_tt_max_abs_angle'))

    # 处理等磁图
    def process_magnetic_features():
        features = ['ratio_minmax', 'angle_minmax', 'dist_minmax', 'x_max_mfm', 'y_max_mfm',
                    'x_min_mfm', 'y_min_mfm', 'area_pos_pole', 'area_neg_pole']
        rec_all = np.zeros((len(loop_set), 9))

        # 计算每个时间点的特征
        for i, loc in enumerate(loop_set):
            rec_all[i] = pcd_rec(DATA_TEMP_Spline[loc], model='m', imShow=False)

        # T峰特征
        peak_vals = pcd_rec(DATA_TEMP_Spline[loc_t_peak], model='m', imShow=False)
        for name, val in zip(features, peak_vals):
            twave_time_dict[f't_tpeak_{name}'] = val

        # 统计分析
        stats = [(name, i, i in [2, 7, 8]) for i, name in enumerate(features)]
        for name, col, normalize in stats:
            func = statistic_analysis if normalize else statistic_analysis_no_normaillize
            twave_time_dict.update(func(rec_all[:, col], f't_tt_{name}'))

        # 附加计算
        center_minmax = 0.5 * (rec_all[:, [3, 4]] + rec_all[:, [5, 6]])
        pos_xy, neg_xy = rec_all[:, [3, 4]], rec_all[:, [5, 6]]

        additional_stats = [
            ('area_sum_pos_mod_neg', lambda x: np.mean(x[:, 7]) / np.mean(x[:, 8]), False),
            ('center_minmax_x', lambda x: x[:, 0], False),
            ('center_minmax_y', lambda x: x[:, 1], False),
            ('max_dist_mfm', lambda x: max_distance(x)[0], False),
            ('rela_dist_pos', lambda x: np.sqrt(np.sum(np.diff(x[:, [3, 4]], axis=0) ** 2, axis=1)), False),
            ('rela_dist_neg', lambda x: np.sqrt(np.sum(np.diff(x[:, [5, 6]], axis=0) ** 2, axis=1)), False)
        ]
        for name, func, normalize in additional_stats:
            stat_func = statistic_analysis if normalize else statistic_analysis_no_normaillize
            twave_time_dict.update(stat_func(func(rec_all), f't_tt_{name}'))

        # 正负磁场和中心点的幅值与角度
        for name, xy in [('pos', pos_xy), ('neg', neg_xy), ('center', center_minmax)]:
            magni = np.sqrt(np.sum(xy ** 2, axis=1))
            angle = np.arctan2(xy[:, 1], xy[:, 0]) * (180 / np.pi)
            twave_time_dict.update(statistic_analysis(magni, f't_tt_abs_magni_{name}'))
            twave_time_dict.update(statistic_analysis_no_normaillize(angle, f't_tt_abs_angle_{name}'))

    # 执行分析
    process_electric_features()
    process_magnetic_features()

    return twave_time_dict
def entropy_features_t(signal_channels, loc_t_onset, loc_t_peak, loc_t_end, window_length):
    twave_entropy = {}

    # 提取 T 波区间
    t_waves = signal_channels[:, loc_t_onset:loc_t_end+1]

    # 提取 Shannon 熵特征
    shannon_tempt_all = 0
    shannon_max = 0
    shannon_min = 9999
    for ii in range(t_waves.shape[0]):
        t_wave_channel = t_waves[ii, :]
        # 从 0 到 1 离散化为20个区间
        shannon_tempt = shannon_entropy(t_wave_channel, 0, 1, 20)
        # twave_entropy[f"t_shannon_entropy_{ii + 1}"] = shannon_tempt
        shannon_tempt_all += shannon_tempt
        if shannon_max < shannon_tempt:
            shannon_max = shannon_tempt
        if shannon_min > shannon_tempt:
            shannon_min = shannon_tempt
    twave_entropy[f"t_shannon_entropy_all"] = shannon_tempt_all
    twave_entropy[f"t_shannon_entropy_max"] = shannon_max
    twave_entropy[f"t_shannon_entropy_min"] = shannon_min
    twave_entropy[f"t_shannon_entropy_mean"] = shannon_tempt_all / t_waves.shape[0]

    # 提取基尼系数
    mean_gini, std_gini, gini_mat = gini_coef(t_waves, window_length)
    dict_tempt = statistic_analysis(gini_mat, 't_gini_mat')
    twave_entropy.update(build_dict({}, dict_tempt))

    # 提取奇异值分解熵
    twave_entropy['t_svd_entropy'] = svd_entropy(t_waves)

    return twave_entropy


def frequency_features_t(signal_channels, loc_t_onset, loc_t_peak, loc_t_end):
    twave_freq = {}

    # 提取TT间期
    epsilon = 1e-10
    t_waves = signal_channels[:, loc_t_onset:loc_t_end+1] + epsilon

    # 初始化列表来存储特征
    features_dict = {
        'energy': [],
        'power': [],
        'ratio': [],
        'skewness': [],
        'kurtosis': []
    }

    for i in range(t_waves.shape[0]):
        t_wave_channel = t_waves[i, :]
        coeffs = pywt.wavedec(t_wave_channel, 'db4')

        # 动态解包小波系数
        cA = coeffs[0]
        details = coeffs[1:]
        cD1, cD2, cD3, cD4 = (details + [None] * 4)[:4]

        # 对于 cA 和每个 cD，我们都提取特征
        for idx, coeff in enumerate([cA, cD1, cD2, cD3, cD4], 1):
            if coeff is not None:
                # 重构信号
                xrec = pywt.waverec([coeff] + [None] * (len(coeffs) - idx), 'db4')
                energy = np.sum(coeff ** 2)
                power = np.sqrt(np.sum(xrec ** 2))
                ratio = power / np.sqrt(np.sum(t_wave_channel ** 2))
                features_dict['energy'].append(energy)
                features_dict['power'].append(power)
                features_dict['ratio'].append(ratio)
                # 新增的统计特征
                features_dict['skewness'].append(skew(coeff))
                features_dict['kurtosis'].append(kurtosis(coeff))

    # 封装特征提取和归一化的步骤
    for feature_name, data in features_dict.items():
        twave_freq.update(extract_normalized_features(data, f't_ch_{feature_name}_a4'))

    return twave_freq


def extract_normalized_features(data, prefix):
    data_sorted = np.sort(data)
    min_value = np.percentile(data_sorted, 10)
    max_value = np.percentile(data_sorted, 90)
    normalized_data = (data - min_value) / (max_value - min_value)
    return {
        f'{prefix}_mean': np.mean(normalized_data),
        f'{prefix}_max': np.max(normalized_data),
        f'{prefix}_min': np.min(normalized_data)
    }


def hjorth_parameters(data):
    """
    Hjorth Parameters
    Hjorth参数是由Bo Hjorth在1970年提出的，用于描述电极上的脑电图(EEG)信号的特性。这些参数是：

    活动度 (Activity): 定义为信号的方差。
    机动性 (Mobility): 定义为信号的标准差和信号的导数的标准差之比。
    复杂性 (Complexity): 定义为机动性与信号的导数的机动性之比。

    Args:
        data:

    Returns:

    """
    # Activity
    activity = np.var(data)

    # Mobility
    mobility = np.std(np.diff(data)) / np.std(data)

    # Complexity
    complexity = (np.std(np.diff(np.diff(data))) / np.std(np.diff(data))) / mobility

    return activity, mobility, complexity


def EEG_features(signal_channels):
    group_indices = [
        [1, 2, 3, 7, 8, 9, 13, 14, 15],
        [4, 5, 6, 10, 11, 12, 16, 17, 18],
        [19, 20, 21, 25, 26, 27, 31, 32, 33],
        [22, 23, 24, 28, 29, 30, 34, 35, 36]
    ]
    dict_result = {}
    for i in range(36):
        dict_result[i] = hjorth_parameters(signal_channels[i:i + 1, :])
    df_from_dict = pd.DataFrame.from_dict(dict_result, orient='index', columns=['activity', 'mobility', 'complexity'])
    grouped_data_uploaded = [df_from_dict.iloc[[idx - 1 for idx in group], :].mean() for group in group_indices]
    grouped_df_uploaded = pd.DataFrame(grouped_data_uploaded)
    grouped_df_uploaded.index = ['Group_' + str(i + 1) for i in range(len(group_indices))]
    result_dict = {}
    for column in grouped_df_uploaded.columns:
        for idx, row in grouped_df_uploaded.iterrows():
            key = f"{idx}_{column}"
            result_dict[key] = row[column]

    return result_dict
