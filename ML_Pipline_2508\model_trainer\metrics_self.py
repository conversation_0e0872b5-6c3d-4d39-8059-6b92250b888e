'''
@Project ：002_Code 
@File    ：metrics_self.py
@IDE     ：PyCharm 
<AUTHOR> Qu
@Date    ：2023/7/24 10:40
Direction:
            自定义评价函数
'''
import numpy as np
import seaborn as sns
from imblearn.metrics import geometric_mean_score
from matplotlib import pyplot as plt
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score, \
    average_precision_score, roc_curve, precision_recall_curve, cohen_kappa_score, matthews_corrcoef, fbeta_score


# 损失函数
def evaluate_model(y_true, y_pred, y_pred_prob=None, curve_label=''):
    if len(y_pred_prob.shape) == 2:
        y_pred_prob = y_pred_prob[:, 1]
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred)
    cm = confusion_matrix(y_true, y_pred)
    tn, fp, fn, tp = cm.ravel()
    specificity = tn / (tn + fp)
    ppv = tp / (tp + fp)
    npv = tn / (tn + fn)
    auc = roc_auc_score(y_true, y_pred_prob)
    prc = average_precision_score(y_true, y_pred_prob)
    fpr, tpr, _ = roc_curve(y_true, y_pred_prob)
    precision_curve, recall_curve, _ = precision_recall_curve(y_true, y_pred_prob)
    cohen_kappa = cohen_kappa_score(y_true, y_pred)

    return {
        'roc_auc': auc,
        'accuracy': accuracy,
        'recall': recall,
        'specificity': specificity,
        'precision': precision,
        'f1_score': f1,
        'cohen_kappa': cohen_kappa,
        'prc': prc,
        'ppv': ppv,
        'npv': npv,
        'tp': tp,
        'fp': fp,
        'tn': tn,
        'fn': fn,
        'roc_curve': {'fpr': fpr, 'tpr': tpr, 'curve_label': curve_label},
        'prc_curve': {'precision': precision_curve, 'recall': recall_curve, 'curve_label': curve_label}
    }


def plot_metrics(train_metrics, test_metrics, methord=''):
    # ROC curve
    plt.figure(figsize=(5, 4))

    plt.subplot(221)
    for metrics, label in zip([train_metrics, test_metrics], [methord + ' Train', methord + ' Test']):
        fpr = metrics['roc_curve']['fpr']
        tpr = metrics['roc_curve']['tpr']
        plt.plot(fpr, tpr, label='{} {}'.format(metrics['roc_curve']['curve_label'], label))
    plt.plot([0, 1], [0, 1], color='navy', linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver operating characteristic')
    plt.legend(loc="lower right")

    # PRC curve
    plt.subplot(222)
    for metrics, label in zip([train_metrics, test_metrics], [methord + ' Train', methord + ' Test']):
        precision = metrics['prc_curve']['precision']
        recall = metrics['prc_curve']['recall']
        plt.plot(recall, precision, label='{} {}'.format(metrics['prc_curve']['curve_label'], label))
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall curve')
    plt.legend(loc="lower right")

    # Histogram of predictions
    plt.subplot(223)
    for metrics, label in zip([train_metrics, test_metrics], ['Train', 'Test']):
        y_pred_prob = metrics['roc_curve']['tpr']  # This should be your predicted probabilities
        sns.histplot(y_pred_prob, bins=10, label=label, kde=False)
    plt.xlabel('Predicted probability')
    plt.ylabel('Frequency')
    plt.title('Histogram of predicted probabilities')
    plt.legend()

    plt.tight_layout()
    plt.show()


# 寻找阈值
def find_best_threshold(y_true, y_prob, metric='roc_auc'):
    thresholds = np.linspace(0, 1, 100)
    best_threshold = None
    best_metric = 0

    for threshold in thresholds:
        y_pred = (y_prob >= threshold).astype(int)
        metric_value = evaluate_metric(y_true, y_pred, metric)

        if metric_value > best_metric:
            best_metric = metric_value
            best_threshold = threshold

    return best_threshold


def evaluate_metric(y_true, y_pred, metric):
    # 根据需要选择评估指标
    if metric == 'roc_auc' or metric == 'auc':
        return roc_auc_score(y_true, y_pred)
    elif metric == 'prc_auc':
        return average_precision_score(y_true, y_pred)
    elif metric == 'accuracy':
        return accuracy_score(y_true, y_pred)
    elif metric == 'precision':
        return precision_score(y_true, y_pred)
    elif metric == 'recall':
        return recall_score(y_true, y_pred)
    elif metric == 'f1':
        return f1_score(y_true, y_pred)
    elif metric == 'fb':
        return fbeta_score(y_true, y_pred, beta=1.25)
    elif metric == 'mcc':
        return matthews_corrcoef(y_true, y_pred)
    elif metric == 'G-mean':
        return geometric_mean_score(y_true, y_pred)
    else:
        raise ValueError("Invalid metric specified.")


def evaluate_and_plot(y, y_pred, y_pred_prob, methord='Normal',show = False):
    if len(y_pred_prob.shape) == 2:
        y_pred_prob = y_pred_prob[:, 1]
    # 1. 计算评估指标
    metrics = {
        'roc_auc': np.round(roc_auc_score(y, y_pred_prob), 3),
        'accuracy': np.round(accuracy_score(y, y_pred), 3),
        'precision': np.round(precision_score(y, y_pred), 3),
        'recall': np.round(recall_score(y, y_pred), 3),
        'f1_score': np.round(f1_score(y, y_pred), 3),
        'specificity': np.round((confusion_matrix(y, y_pred)[0, 0] / (confusion_matrix(y, y_pred)[0, 0] + confusion_matrix(y, y_pred)[0, 1])),3)
    }

    # 2. 绘制 ROC 曲线
    if show:
        plt.figure(figsize=(15, 5))

        fpr, tpr, _ = roc_curve(y, y_pred_prob)
        plt.subplot(1, 3, 1)
        plt.plot(fpr, tpr, label=f"ROC curve (AUC = {metrics['roc_auc']:.2f})")
        plt.plot([0, 1], [0, 1], color='navy', linestyle='--')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(f'ROC Curve {methord}')
        plt.legend(loc='lower right')

        # 3. 绘制 PRC 曲线
        precision_curve, recall_curve, _ = precision_recall_curve(y, y_pred_prob)
        plt.subplot(1, 3, 2)
        plt.plot(recall_curve, precision_curve, label="PR curve")
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title(f'Precision-Recall Curve {methord}')
        plt.legend(loc='lower right')

        # 4. 绘制预测概率的概率密度图
        plt.subplot(1, 3, 3)
        sns.histplot(y_pred_prob, bins=10, kde=False)
        plt.xlabel('Predicted probability')
        plt.ylabel('Density')
        plt.title(f'Density Plot of Predicted Probabilities {methord}')

        plt.tight_layout()
        plt.show()

    return metrics


def Probability_Value_Remapping(y, y_pred, y_pred_prob, metric='accuracy'):
    best_threshold = find_best_threshold(y, y_pred_prob, metric=metric)
    y_pred_prob_new = remap_probabilities(y_pred_prob, best_threshold)
    y_pred_new = np.where(y_pred_prob_new >= 0.5, 1, 0)
    metrics = evaluate_and_plot(y, y_pred_new, y_pred_prob_new, 'best_threshold')
    return best_threshold, metrics


def remap_probabilities(y_pred_prob, best_threshold):
    """
    Remap probabilities based on best threshold.

    Arguments:
    - y_pred_prob: The predicted probabilities for the positive class.
    - best_threshold: The best threshold.

    Returns:
    - Remapped probabilities.
    """
    # For probabilities <= best_threshold, map linearly to [0, 0.5]
    lower_half = y_pred_prob[y_pred_prob <= best_threshold]
    lower_half = (lower_half / best_threshold) * 0.5

    # For probabilities > best_threshold, map linearly to [0.5, 1]
    upper_half = y_pred_prob[y_pred_prob > best_threshold]
    upper_half = 0.5 + ((upper_half - best_threshold) / (1 - best_threshold) * 0.5)

    # Combine the remapped probabilities
    remapped_prob = np.zeros_like(y_pred_prob)
    remapped_prob[y_pred_prob <= best_threshold] = lower_half
    remapped_prob[y_pred_prob > best_threshold] = upper_half

    return remapped_prob

