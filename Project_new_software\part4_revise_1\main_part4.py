"""
@ Author: <PERSON>
@ E-mail: <EMAIL>
@ Date: 2024/11/13 10:15
"""

from func.fun_colletion import *
from numpy.linalg import *
import meshio
import time
import numpy as np
import matplotlib.pyplot as plt
import multiprocessing


def sig_subspace(b):
    """
    :param b: MCG数据  1 * 36
    :param noise_power: 噪声功率，一般为sigma ** 2
    :return: signal subspace
    """
    b_1 = b.reshape(b.shape[0], 1)
    b_1_t = b.reshape(1, b.shape[0])
    bb = b_1 @ b_1_t
    u, sigma, vt = np.linalg.svd(bb)
    u_1 = u[:, 0:1]
    return u_1

class Data:
    def __init__(self, file_name):
        self.file_name = file_name

    def read_mcg(self):
        with open(self.file_name, 'r') as ff:
            file = ff.read()  # 读取的是字符串
        da = []
        for i in file.split():  # 把上面字符串记为数字插入
            da.append(float(i))
        datat = []
        for i in range(int(len(da) / 37)):
            datat.append(da[37 * i + 1:37 * (i + 1)])
        mcg_data = np.array(datat)
        return mcg_data


class SourceReconstruction():
    """
    :param mcg_data: n * 36的心磁平均数据
    :param R: R时刻(计算按采集软件的程序)
    :param check_time: 该事件为一个二维的array， 用于记录想查看的起始点和终点
    :param name：被试名字
    :param z_A: 被试A心脏前壁到探测器距离
    :return:
    """
    def __init__(self, Q, R, heart_path, check_time, mcg_data):
        self.dir, self.strength = [], []
        self.heart_z_3 = None
        self.heart_y_3 = None
        self.heart_x_3 = None
        self.dl = np.arange(0, 0.21, 0.04)
        self.d_z_max = None
        self.size_point = None
        self.check_time = check_time
        self.mcg_data = mcg_data
        self.lf_cav, self.u_lead_lcav = None, None
        self.mcg_data_interpolate = None  # mcg_interpolate(self.mcg_data)
        self.lf = None
        self.heart_path = heart_path
        self.Q, self.R = Q, R

    def draw_pic_music(self):
        size = self.strength.shape[0]
        intensity = self.strength.reshape((size, 1))
        self.cur_dir = self.dir * intensity * 2

        # 归一化
        max_length = np.max(np.sqrt(np.sum(self.cur_dir ** 2, axis=1)))
        self.cur_dir = self.cur_dir / max_length
        num_vectors = size - 0
        quarter_point = int(num_vectors // 4)
        light_blue = np.array([0.9, 0.95, 1.0])  # 浅蓝色
        medium_blue = np.array([0.5, 0.7, 0.9])  # 淡蓝色
        dark_blue = np.array([0.18, 0.58, 0.88])  # 蓝色

        colors = np.zeros((num_vectors, 3))
        colors[:quarter_point] = light_blue  # 前1/4时段为浅蓝色
        colors[quarter_point:2 * quarter_point] = medium_blue  # 1/4到2/4时段为淡蓝色
        colors[2 * quarter_point:] = dark_blue  # 最后1/4时段为蓝色

        fig = plt.figure(figsize=(12, 12))
        ax = fig.add_subplot(111)
        ax.set_facecolor((25 / 255, 25 / 255, 25 / 255))
        fig.patch.set_facecolor((25 / 255, 25 / 255, 25 / 255))

        # 添加淡灰色圆形背景
        circle_fill = plt.Circle((0, 0), 1, color='gray', alpha=0.15, fill=True)
        circle_edge = plt.Circle((0, 0), 1, color='gray', alpha=0.6, fill=False, linewidth=1)
        ax.add_patch(circle_fill)
        ax.add_patch(circle_edge)

        # 绘制圆内的灰色坐标轴，长度限制在[-1, 1]
        ax.plot([-1, 1], [0, 0], color='gray', lw=1.2, alpha=0.6)  # x轴
        ax.plot([0, 0], [-1, 1], color='gray', lw=1.2, alpha=0.6)  # y轴

        start_point = np.array([0, 0])
        for i in range(num_vectors):
            ax.plot([start_point[0], self.cur_dir[i, 0]], [start_point[1], self.cur_dir[i, 1]], color=colors[i],
                    linewidth=2)

        # 设置坐标轴范围为[-1.2, 1.2]以留出一些边距
        ax.set_xlim([-1.2, 1.2])
        ax.set_ylim([-1.2, 1.2])
        ax.set_xticks([])
        ax.set_yticks([])
        ax.set_aspect('equal')
        plt.savefig('TT.jpg',
                    facecolor=fig.get_facecolor(),
                    edgecolor='none',
                    bbox_inches='tight',
                    dpi=300)

        return fig, ax

    def tran_x_y_fun(self):
        q, r = self.Q, self.R
        qr_range = np.arange(q, r)
        text = np.zeros((len(qr_range), 3))
        x_1 = np.mgrid[0.2:20.1: 0.2]
        y_1 = np.mgrid[0.2:20.1:0.2]
        [xi, yi] = np.meshgrid(x_1, y_1)
        self.mcg_data_interpolate = mcg_interpolate(self.mcg_data[self.Q:self.R, :])
        for k in range(self.R - self.Q):
            b = self.mcg_data_interpolate[k] * 10 ** (-12)
            index_x, index_y = magnetic_gradient(b, xi, yi)
            text[k][0] = k
            text[k][1] = index_x * 10
            text[k][2] = index_y * 10
        text_min_index = np.argmin(text[:, 2])
        while text_min_index <= len(qr_range) - 2:
            if (text[text_min_index][2] == text[text_min_index + 1][2] and
                    text[text_min_index + 1][1] > text[text_min_index][1]):
                text_min_index += 1
            else:
                if text[text_min_index][1] < np.max(text[:, 1]) - 20:
                    self.tran_x, self.tran_y = np.max(text[:, 1]) - 10, text[text_min_index][2]
                    return self.tran_x, self.tran_y
                    break
                else:
                    self.tran_x, self.tran_y = text[text_min_index][1], text[text_min_index][2]
                    return self.tran_x, self.tran_y
                    break
        self.tran_x, self.tran_y = text[text_min_index][1], text[text_min_index][2]

    def heart_tip(self, width, x_2, y_2, z_2, size_point):
        distance = 0
        standard_point = np.array([0, width, 0])
        index = 0
        max_x = np.max(x_2)
        for point_i in range(size_point):
            point = np.array([x_2[point_i], y_2[point_i], z_2[point_i]])
            distance_2 = np.abs(x_2[point_i] - max_x)
            distance_1 = np.linalg.norm(standard_point - point[0:3])
            if distance_1 > distance and distance_2 < 15:
                distance = distance_1
                point_index = index
            index += 1
        inten = np.zeros(size_point)
        inten[point_index] = 1
        location = np.array([x_2[point_index], y_2[point_index], z_2[point_index], point_index])
        return location

    def cta_mcg(self, z_A, z_B):
        self.Length, self.width, self.num_layers = 139.80, 176.6, 233
        heart_mesh = meshio.read(self.heart_path, file_format='gmsh')  # 表面及内部网格
        self.heart_points = np.asarray(heart_mesh.points * 100)
        self.size_point = self.heart_points.shape[0]

        self.d_z_max = (self.width + 40) / 1000
        self.heart_points[:, 0] = self.heart_points[:, 0] / 512 * self.width
        self.heart_points[:, 1] = self.heart_points[:, 1] / 512 * self.width
        self.heart_points[:, 2] = self.heart_points[:, 2] / self.num_layers * self.Length
        self.heart_points = self.heart_points[:, [1, 2, 0]]

        # CT真实心脏点坐标转换成MCG下心脏点坐标
        self.tran_x_y_fun()
        self.location = self.heart_tip(self.width, self.heart_points[:, 0], self.heart_points[:, 1],
                                       self.heart_points[:, 2], self.heart_points.shape[0])
        self.heart_points[:, 0] = self.heart_points[:, 0] - self.location[0] + self.tran_x + 20
        self.heart_points[:, 1] = self.heart_points[:, 1] - self.location[1] + self.tran_y - 20
        self.heart_points[:, 2] = self.heart_points[:, 2] - [z_B - z_A] * (self.heart_points.shape[0])
        self.heart_points = self.heart_points / 1000

    def get_lf(self):
        """
        :return: 左心室，包括室间隔的分割
        """
        self.lf_cav = np.zeros((36, self.heart_points.shape[0] * 2))
        self.u_lead_lcav = np.zeros((36, self.heart_points.shape[0] * 2))
        t_1 = dev_lead_field_1(self.heart_points, self.dl, self.d_z_max)
        t_2 = dev_lead_field_1(self.heart_points, self.dl, self.d_z_max + 0.05)
        t_3 = dev_lead_field_1(self.heart_points, self.dl, self.d_z_max + 0.10)
        t = t_1 - 2 * t_2 + t_3
        for i in range(self.heart_points.shape[0]):
            lf = t[i, :, :]
            self.lf_cav[:, 2 * i: 2 * i + 2] = lf[:, 0:2]
            u_lead, s_lead, vt_lead = np.linalg.svd(self.lf_cav[:, 2 * i: 2 * i + 2])
            self.u_lead_lcav[:, 2 * i: 2 * i + 2] = u_lead[:, 0:2]

    def sub_corr(self, lead_matrix, sig_subspace):
        """
        :param lead_matrix: 导联场矩阵 36*2
        :param sig_subspace: 子空间， 一般为36 * n, n 为根据信号子空间所给定的维数
        :return: subspace correlation
        """
        # P = sig_subspace @ sig_subspace.T
        # first_miu = np.linalg.norm(P @ lead_matrix[:, 0]) ** 2 / np.linalg.norm(lead_matrix[:, 0]) ** 2
        # second_miu = np.linalg.norm(P @ lead_matrix[:, 1]) ** 2 / np.linalg.norm(lead_matrix[:, 1]) ** 2
        # miu = max(first_miu, second_miu)
        u_lead, s_lead, vt_lead = np.linalg.svd(lead_matrix)
        num_u_lead = lead_matrix.shape[1]
        C = u_lead[:, 0:num_u_lead].T @ sig_subspace
        u_c, s_c, vt_c = np.linalg.svd(C)
        x = vt_lead.T @ np.diag(1 / s_lead) @ u_c
        if num_u_lead == 2:
            x[0:2, 0] = x[0:2, 0] / np.linalg.norm(x[0:2, 0])
        else:
            x[0:2, 0] = x[0:2, 0] / np.linalg.norm(x[0:2, 0])
            x[2:4, 0] = x[2:4, 0] / np.linalg.norm(x[2:4, 0])
        # x[2:4] = x[2:4] / np.linalg.norm(x[2:4])
        miu = s_c[0]
        return miu, x[:, 0]

    def rap_music(self, b):
        """
        :param b: 磁场 b
        :param ii: 时刻 ii
        :return: 对于单点的点源还原
        """
        b_1 = b.reshape((36, 1))
        signal_subspace = sig_subspace(b_1)  # 求出磁场b_1的特征向量
        if self.lf_cav is None:
            self.get_lf()

        corr = signal_subspace.T @ self.u_lead_lcav
        corr_all = np.sqrt(corr[0, 0::2] ** 2 + corr[0, 1::2] ** 2)
        max_index = np.argmax(corr_all)
        pro_matrix = self.lf_cav[:, 2 * max_index: 2 * max_index + 2]
        max_miu, max_x1 = sub_corr_1(pro_matrix, signal_subspace)
        cur_dir = max_x1.reshape(1, 2)
        pro_matrix_1 = pro_matrix @ max_x1.reshape(2, 1)
        intensity = pro_matrix_1.T @ b / norm(pro_matrix_1) ** 2
        return [cur_dir[0][0], cur_dir[0][1]], [intensity[0]]

    def source_strength(self):
        self.time_range = list(range(self.check_time[0], self.check_time[1]))
        for ii in self.time_range:
            b_mat = (-self.mcg_data[ii, :] * 10 ** (-12)).reshape(6, 6)
            b_mat_1 = b_mat[::-1]
            b = b_mat_1.flatten()
            cur_dir, intensity = self.rap_music(b)
            self.dir.append(cur_dir)
            self.strength.append(intensity)
        self.dir, self.strength = np.array(self.dir), np.array(self.strength)
        self.draw_pic_music()

def main_part4(mcg_path, heart_path, time_loc):
    """
    :param mcg_path: 心磁路径
    :param heart_path: 心脏模型路径
    :param time_loc: 时刻点 np.array([Qp, Rp, To,Tp,Te]), 分别是Q峰，R峰，T初，T峰，T末
    :return:
    """
    heart_path = heart_path + '\\heart_point.msh'
    Qp, Rp, To, Tp, Te = time_loc[0], time_loc[1], time_loc[2], time_loc[3], time_loc[4]
    DataProcess = Data(mcg_path)
    mcg_data = DataProcess.read_mcg()
    check_time = np.array([round((To + Tp) / 2), round((Tp + Te) / 2) + 1])
    reconstruction = SourceReconstruction(Qp, Rp, heart_path, check_time, mcg_data=mcg_data)
    reconstruction.cta_mcg(z_A=57.4, z_B=49.52)
    reconstruction.source_strength()


if __name__ == '__main__':
    start_time = time.time()
    heart_path = r'model'

    'example 1'
    # mcg_path = r'data\\PLAG_2024_000059.txt'
    # time_loc = np.array([311, 335, 516, 597, 647])

    'example 2'
    mcg_path = r'data/BJ_TT_000516.txt'
    time_loc = np.array([324, 359, 558, 645, 683])
    main_part4(mcg_path, heart_path, time_loc)

    end_time = time.time()
    print('operator time = %f' % (end_time - start_time))

