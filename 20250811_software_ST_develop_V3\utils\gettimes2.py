"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/01/18 16:40
desc: 小工具
    解压缩
    提取id和时刻点
environment: pfafn36
run:
    python utils.py
option:
    结果目录、心磁文档、时刻点文档, plt.show()
"""


# import zipfile
# import pandas as pd
import math
import numpy as np
from numpy.linalg import *
import re
# from shapely.geometry import Polygon, MultiLineString
# from shapely.geometry.multipolygon import MultiPolygon
# from shapely.ops import unary_union
# import os
# # import cv2
# import sys
# import time
# import ast
# import matplotlib.pyplot as plt
# import scipy.special
from scipy import signal
from scipy.ndimage import gaussian_filter1d
# import scipy.stats as stats
# from scipy.stats import trim_mean
# import itertools
# import pickle
# import random
# from sklearn.cluster import KMeans

# from utils.StatPNs import statpn
# from utils.Gettimes0 import gettimes
# from utils.cal_interpolates import cal_interpolate, cal_interpolate0, mcg_interpolate1
# from utils.space_wave import SpaceWave
# from utils.time_wave import TimeWave
# from utils.data import Data


def hb_lis0(l0):
    '''合并多维列表的全部元素为一维'''
    if type(l0) == list:
        l1 = []
        for i in range(len(l0)):
            l1 += hb_lis0(l0[i])
        return l1
    else:
        return [l0]


def shaixuan_10channel(rms, ls36, rg, bl1=0.3, bl2=0.55, th=10, bz=10, nm=10):
    '''筛选较大通道: rms最大位置R0, 前后rg帧取绝对值最大值, 取最大的nm通道'''
    rg = max(rg, round(len(ls36[0])*rg/1000))  # 前后rg=5**帧内
    th = max(th, round(len(ls36[0])*th/1000))  # 前后rg=5**帧内
    ik1, ik2 = round(len(rms)*bl1), round(len(rms)*bl2)  # 定位区间
    rms0 = rms[ik1:ik2+1]
    rpeaks, _ = signal.find_peaks(np.array(rms0), distance=th)
    vs = [rms0[i] for i in rpeaks]
    if vs == []:
        t0 = rms0.index(max(rms0))+ik1  # 最大波位置R0
    else:
        t0 = rpeaks[np.argmax(vs)]+ik1
    rms0 = [v0*bz/max(rms) for v0 in rms]  # RMS的bz=10*值规范化
    # 较大通道筛选
    i1, i2 = max(t0-rg,0), min(t0+rg+1,len(ls36[0]))
    idx, ls10 = cal_lsrms10(ls36, i1, i2, nm=10)
    return [t0, rms0, idx, ls10]


def cal_std(rms0, M0, rg2, i1=0, i2=0, i3=0, i4=0, bz=10, vv=5):
    '''计算信号波的规范化std: bz规范化+拟合, i1234两段取std, 当前帧为均值, rg2步长'''
    # rms = [abs(v0*bz/M0) for v0 in rms0]  # bz=10*值规范化
    rms = [v0*bz/M0 for v0 in rms0]  # bz=10*值规范化
    y_smooth = gaussian_filter1d(np.array(rms), sigma=vv)  # sigma5*高斯拟合
    rks, _ = signal.find_peaks(y_smooth)
    std = [0 for _ in range(len(rms))]  # 定义0
    for i in range(i1, i2):
        x0 = y_smooth[i]
        xs = y_smooth[max(0, i-rg2):i]
        std[i] = np.sqrt(np.sum((xs - x0) ** 2) / (len(xs) - 1 + 0.000001))
    for i in range(i3, i4):
        x0 = y_smooth[i]
        xs = y_smooth[i+1:min(len(rms), i+rg2+1)]
        std[i] = np.sqrt(np.sum((xs - x0) ** 2) / (len(xs) - 1 + 0.000001))
    std = [min(bz, v0) for v0 in std]
    QhSrs, _ = signal.find_peaks(-np.array(std))  # 标准差波谷——
    QhSrs = [i for i in QhSrs if i not in rks]
    return [std, QhSrs]


def cal_wendingdian(rms0, t0, rg1=100, rg2=20, rg3=20, rg4=30, bz=10, vv=5, td=[0.2,0.5,1]):
    '''计算QRS复合的最远稳定点QhSr: rms10值规范化, 高斯拟合, 最大值前后的20帧标准差
    std<0.5,10帧<0.5, 定位后Qh-20/Sr+30
    若无标准差波谷, 可能报错, 无Qh/Sr'''
    # 1.统计稳定点
    rg1 = max(rg1, round(len(rms0)*rg1/1000))
    rg2 = max(rg2, round(len(rms0)*rg2/1000))
    std, QhSrs = cal_std(rms0, rms0[t0], rg2, max(0,t0-rg1), t0, t0, min(len(rms0),t0+rg1+1))
    # 2.筛选稳定点
    Qh = []  # 逆序稳定点
    Sr = []
    td0, td1, td2 = td
    for i in range(len(QhSrs)):
        j = len(QhSrs)-1-i
        if QhSrs[j]<t0:
            i1 = QhSrs[j]
            if std[i1]<td1 and max(std[max(0,round(i1-rg2/2)):i1])<td1:
                Qh.append(i1)
        if QhSrs[i]>t0:
            i1 = QhSrs[i]
            if std[i1]<td1 and max(std[i1:min(len(std),round(i1+rg2/2+1))])<td1:
                Sr.append(i1)
    if Qh == []:
        # print('no < 0.5')
        Qh = [i for i in QhSrs if i<t0 and std[i]<td2]
        if Qh == []:
            # print('no < 1')
            Qh = [i for i in QhSrs if i<t0]
        Qh.reverse()
    if Sr == []:
        # print('no < 0.5')
        Sr = [i for i in QhSrs if i>t0 and std[i]<td2]
        if Sr == []:
            # print('no < 1')
            Sr = [i for i in QhSrs if i>t0]
    # 3.固定稳定点
    rg3 = max(rg3, round(len(rms0)*rg3/1000))
    rg4 = max(rg4, round(len(rms0)*rg4/1000))
    if Qh == []:
        Qh0 = max(0,t0-rg1)
    else:
        Qh00 = Qh[0]-rg3
        Qh0 = Qh00
        for i in Qh:
            if i<=Qh00:
                break
            if max(std[max(0,i-rg2):i])<td0:
                Qh0 = i
                break
    if Sr == []:
        Sr0 = min(len(rms0),t0+rg1+1)
    else:
        Sr00 = Sr[0]+rg4
        Sr0 = Sr00
        for i in Sr:
            if i>=Sr00:
                break
            if max(std[i:min(len(std),i+rg2+1)])<td0:
                Sr0=i
                break
    return [Qh0, Sr0, Qh, Sr, QhSrs]


def cal_jzd0(numbers, reverse=0, nm=3, bl=1):
    '''计算集中点: nm最小数量'''
    jzd, p0 = -1, numbers
    if len(numbers) < nm:
        return [jzd, numbers]
    # 数据排序
    sorted_numbers = np.sort(numbers)
    if sorted_numbers[-1]-sorted_numbers[0]<=nm+2:
        filtered_numbers = sorted_numbers
    else:
        # 计算四分位数
        # position_Q1 = (len(sorted_numbers) + 1) / 4
        # Q1 = sorted_numbers[math.ceil(position_Q1) - 1]
        # Q3 = sorted_numbers[math.floor(3*position_Q1) - 1]
        Q1 = np.percentile(sorted_numbers, 25)
        Q3 = np.percentile(sorted_numbers, 75)
        # 计算四分位距
        IQR = Q3 - Q1
        # 确定离散点
        outliers = sorted_numbers[(sorted_numbers < Q1 - bl * IQR) | (sorted_numbers > Q3 + bl * IQR)]
        # 排除离散点
        filtered_numbers = sorted_numbers[~np.isin(sorted_numbers, outliers)]
        # print(numbers, filtered_numbers)
    if len(filtered_numbers) >= nm:
        if reverse:
            filtered_numbers = filtered_numbers.tolist()
            filtered_numbers.reverse()
        values, counts = np.unique(filtered_numbers, return_counts=True)
        if max(counts) >= nm:  # 中位数-向下
            # modes = values[counts == np.max(counts)]
            # jzd = modes[math.ceil((len(modes)+1)/2) - 1]
            jzd = values[np.argmax(counts)]
            p0 = [i for i in filtered_numbers if i== jzd]
        else:
            frequent_value = [sum(1 for v in filtered_numbers if abs(v-v0)<nm) for v0 in filtered_numbers]
            # print(frequent_value)
            if max(frequent_value) >= nm:
                filtered_numbers = np.array(filtered_numbers)
                modes = filtered_numbers[frequent_value == np.max(frequent_value)]
                jzd = modes[math.floor((len(modes)+1)/2) - 1]
                p0 = [i for i in filtered_numbers if abs(i-jzd)<nm]
                # print('now', filtered_numbers, frequent_value, modes, jzd)
                # print(numbers, frequent_value, jzd)
                # jzd = filtered_numbers[np.argmax(frequent_value)]
    return [jzd, p0]


def cal_suyuanS(Sps, Spts, Sp0s, QRS0, nm1=3):
    '''计算S波的溯源筛选'''
    for i in range(1, len(Sps)):  # 循环剔除n-1次
        Sps1, Spts1 = Sps.copy(), Spts.copy()
        for j in range(1, len(Sps1)):  # 遍历第2-波是否剔除
            p0 = Spts1[j-1]
            p1 = list(set(Spts1[j]))
            p2 = []
            for k1 in p1:
                for ps in QRS0:
                    if k1 in ps:
                        k2 = ps.index(k1)
                        p2 += ps[max(0,k2-1):k2]
            if len([i1 for i1 in p2 if i1 in p0])<nm1:  # 至少溯源nm1=3个点
                Sps1.pop(j)
                Spts1.pop(j)
                Sp0s.pop(j)
                break
        if Sps1==Sps:
            break
        else:
            Sps, Spts = Sps1.copy(), Spts1.copy()
    return [Sps, Spts, Sp0s]


def cal_suyuanQ(Qps, Qpts, Qp0s, QRS0, nm1=3):
    '''计算Q波的溯源筛选'''
    for i in range(1, len(Qps)):  # 循环剔除n-1次
        Qps1, Qpts1 = Qps.copy(), Qpts.copy()
        for j in range(1, len(Qps1)):  # 遍历第2-波是否剔除
            p0 = Qpts1[j-1]
            p1 = list(set(Qpts1[j]))
            # print(p0, p1)
            p2 = []
            for k1 in p1:
                for ps in QRS0:
                    if k1 in ps:
                        k2 = ps.index(k1)
                        p2 += ps[k2+1:min(len(ps),k2+2)]
            if len([i1 for i1 in p2 if i1 in p0])<nm1:  # 至少溯源nm1=3个点
                Qps1.pop(j)
                Qpts1.pop(j)
                Qp0s.pop(j)
                break
        if Qps1==Qps:
            break
        else:
            Qps, Qpts = Qps1.copy(), Qpts1.copy()
    return [Qps, Qpts, Qp0s]


def cal_beixuanbo(ls10, Qh0, Sr0, t0, th=10, th2=70, nm=2, bls=[3.2,0.01,0.01,0.015,0.014,0.02,0.04,0.07]):
    '''计算9通道备选QRS波: 截取QRS段, 9通道绝对值的最大波峰, 前后最多2个峰'''
    # 1.统计abs波峰点集
    th = max(th, round(len(ls10[0])*th/1000))
    nm = max(nm, round(len(ls10[0])*nm/1000))
    QRS0 = []
    for l0 in ls10:
        l0 = l0[Qh0:Sr0+1]
        pks, _ = signal.find_peaks(np.abs(l0), distance=th)  # 最小距离
        if len(pks) == 0:
            l0 = list(np.abs(l0))
            QRS0.append([l0.index(max(l0))])
        else:
            vs = [abs(l0[j]) for j in pks]
            i0 = np.argmax(vs)
            ps = [pks[j] for j in range(max(0,i0-nm), min(i0+nm+1, len(vs)))]
            QRS0.append(ps)
    # 2.1 R0集中点
    i0 = t0-Qh0
    p0 = []
    for i in range(len(QRS0)):
        cz = [abs(j-i0) for j in QRS0[i]]
        if min(cz)<th:
            t = QRS0[i][np.argmin(cz)]
            p0.append(t)
    Rpts = p0
    R0, p0 = cal_jzd0(p0)
    # Rp0s = p0
    # 2.2 Sp/Qp集中点
    pts = sorted(set(hb_lis0(QRS0)))
    pts = [i for i in pts if i not in p0]
    Sps, i1, Spts, Sp0s = [], R0+th, [], []
    for i in pts:
        if i > i1:  # i0后逐步添加备选S波
            p0 = []
            for j in range(len(QRS0)):
                p0 += [k for k in QRS0[j] if k>=i and k-i<th][:1]
            S0, p0 = cal_jzd0(p0)
            if S0 != -1:
                p0 = []
                for j in range(len(QRS0)):
                    l0 = [k for k in QRS0[j] if k>i1]
                    cz = [abs(k-S0) for k in l0]
                    if cz!=[]:
                        if min(cz)<th:
                            t = l0[np.argmin(cz)]
                            p0.append(t)
                Spts.append(p0)  # 记录集中点
                S1, p0 = cal_jzd0(p0)
                Sp0s.append(p0)
                i1 = S1+th/5
                if S1 == -1:
                    S1 = S0
                    # print('Error!')
                Sps.append(S0)  # 添加S波
    Qps, i1, Qpts, Qp0s = [], R0-th, [], []
    pts.reverse()
    for i in pts:
        if i < i1:  # i0前逐步添加备选Q波
            p0 = []
            for j in range(len(QRS0)):
                p0 += [k for k in QRS0[j] if k<=i and i-k<th][-1:]
            Q0, p0 = cal_jzd0(p0, 1)  # 逆序最集中点
            if Q0 != -1:
                # p1 = p0.copy()
                p0 = []
                for j in range(len(QRS0)):
                    l0 = [k for k in QRS0[j] if k<i1]
                    cz = [abs(k-Q0) for k in l0]
                    if cz!=[]:
                        if min(cz)<th:
                            cz.reverse()
                            t = l0[len(cz)-1-np.argmin(cz)]
                            p0.append(t)
                Qpts.append(p0)  # 记录集中点
                Q1, p0 = cal_jzd0(p0, 1)
                Qp0s.append(p0)
                i1 = Q1-th/5
                if Q1== -1:
                    Q1=Q0
                    # print('Error!')
                Qps.append(Q0)  # 添加Q波
    # 3.集中点溯源筛选
    Qps = sorted(set(Qps))
    Sps = sorted(set(Sps))
    Qps.reverse()
    Sps1, Qps1 = Sps.copy(), Qps.copy()
    Sps, Spts, Sp0s = cal_suyuanS(Sps1, Spts, Sp0s, QRS0)
    Qps, Qpts, Qp0s = cal_suyuanQ(Qps1, Qpts, Qp0s, QRS0)
    Qps = Qps[:min(len(Qps),2)]
    Sps = Sps[:min(len(Sps),2)]
    # 4.确定Q/S单波
    t00, t11 = R0-th, R0+th  # 最近Qh/Sr
    M0 = max(np.abs(hb_lis0([[ls10[i][j] for j in range(Qh0,Sr0+1)] for i in range(len(ls10))])))
    Qps2, Sps2 = [], []
    fg = [[0, 0], [0, 0]]
    bl1, bl2, bl3, bl4, bl5, bl6, bl7, bl8=bls
    if len(Qps)>0:
        v0 = cal_fuzhi(Qps[0], Qh0, Qp0s[0], QRS0, ls10, M0)
        Qps2 = [Qps[0]]
        if len(Qps)==2:
            v1 = cal_fuzhi(Qps[1], Qh0, Qp0s[1], QRS0, ls10, M0)
            if v1/v0>bl1:
                Qps2 = [Qps[1]]
                v0 = v1
                Qp0s[0] = Qp0s[1]
            if v0<bl2:
                Qps2 = []
            else:
                t00 = min([t00]+Qp0s[0])
        else:
            if v0<bl5:
                Qps2 = []
            else:
                t00 = min([t00]+Qp0s[0])
    if len(Sps)>0:
        v0 = cal_fuzhi(Sps[0], Qh0, Sp0s[0], QRS0, ls10, M0)
        Sps2 = [Sps[0]]
        if len(Sps)==2:
            v1 = cal_fuzhi(Sps[1], Qh0, Sp0s[1], QRS0, ls10, M0)
            if v1/v0>bl1:
                Sps2 = [Sps[1]]
                v0=v0
                Sp0s[0] = Sp0s[1]
            if v0<bl3:
                Sps2 = []
            else:
                t11 = max([t11]+Sp0s[0])
        else:
            if v0<bl4:
                Sps2 = []
            else:
                t11 = max([t11]+Sp0s[0])
    # 5.补充单Q/S波
    M0 = max(np.abs(hb_lis0([[ls10[i][j] for j in range(Qh0,Sr0+1)] for i in range(len(ls10))])))
    pts = sorted(set(Rpts))
    i1 = R0-th
    if Qps2 == []:
        p0 = []
        for i in range(len(QRS0)):
            l0 = [j for j in QRS0[i] if j in pts]  # 溯源R0单通道波峰点
            if l0 != []:
                l1 = [j for j in QRS0[i] if j<i1 and j<l0[0]][-1:]
                if l1 != []:  # 溯源点前的波峰点
                    v0 = round(abs(ls10[i][l1[0]+Qh0]/M0),3)
                    if v0>=bl5:
                        p0.append(l1[0])
        if len(p0) in [1,2]:
            Qps2 = [max(p0)]
            # t00 = min(t00, max(p0))
        elif len(p0)>2:
            a, b = find_peak_point(p0)
            Qps2 = [a]
            # t00 = min([t00]+b)
        t00 = min([t00]+p0)
    th2 = max(th2, round(len(ls10[0])*th2/1000))  # 最远S控制
    i1 = R0+th
    if Sps2 == []:
        p0 = []
        for i in range(len(QRS0)):
            l0 = [j for j in QRS0[i] if j in pts]
            if l0 != []:
                l1 = [j for j in QRS0[i] if j>i1 and j>l0[-1]][:1]
                if l1 != []:
                    v0 = round(abs(ls10[i][l1[0]+Qh0]/M0),3)
                    if v0>=bl6 and l1[0]-R0<th2:
                        p0.append(l1[0])
        if len(p0) in [1,2]:
            Sps2 = [min(p0)]
            # t11 = max(t11, min(p0))
        elif len(p0)>2:
            a, b = find_peak_point(p0, 1)
            Sps2 = [a]
            # t11 = max([t11]+b)
        t11 = max([t11]+p0)
    # 6.无Q/S波时,截断幅值
    M0 = max(np.abs(hb_lis0([[ls10[i][j] for j in range(Qh0,Sr0+1)] for i in range(len(ls10))])))
    if Qps2 == []:
        v0s = []
        for i in range(R0-th):
            j = R0-th-1-i
            v0 = max([round(abs(ls10[k][j+Qh0]/M0),3) for k in range(len(ls10))])
            v0s.append(v0)
            if v0<bl7:
                Qps2 = [j]
                t00 = min(t00, j)
                break
        if Qps2==[]:
            i0 = 0
            if v0s:
                i0 = v0s.index(min(v0s))
            Qps2 = [R0-th-1-i0]
            # print('no Q')
    if Sps2 == []:
        v0s = []
        for j in range(R0+th+1, Sr0):
            v0 = max([round(abs(ls10[k][j+Qh0]/M0),3) for k in range(len(ls10))])
            v0s.append(v0)
            if v0<bl8:
                Sps2 = [j]
                t11 = max(t11, j)
                break
        if Sps2==[]:
            i0 = 0
            if v0s:
                i0 = v0s.index(min(v0s))
            Sps2 = [R0+th+1+i0]
            # print('no S')
    t00 = round(t00-th/2)
    t11 = round(t11+th/2)
    
    Qps = [i+Qh0 for i in Qps2]
    Sps = [i+Qh0 for i in Sps2]
    Qps1 = [i+Qh0 for i in Qps1]
    Sps1 = [i+Qh0 for i in Sps1]
    # return [R0+Qh0, Sps, Qps, Sps1, Qps1, fg, t00+Qh0, t11+Qh0]
    return [R0+Qh0, Sps, Qps, t00+Qh0, t11+Qh0]


def find_peak_point(numbers, md=0):
    # 从大到小排序
    numbers.sort(reverse=True)
    # 计算相邻数字的差值
    differences = [numbers[i] - numbers[i + 1] for i in range(len(numbers) - 1)]
    # 将差值从小到大排序
    differences.sort()
    # 选择最小差值作为近邻距离
    min_diff = differences[0] if differences else 0
    # 统计每个数值的近邻数量
    near_count = {num: 0 for num in numbers}
    for num in numbers:
        for neighbor in [num - min_diff, num + min_diff]:
            if neighbor in numbers:
                near_count[num] += 1
    # 选择近邻数量最多的数值作为聚点
    max_near_count = max(near_count.values())
    peak_points = [num for num, count in near_count.items() if count == max_near_count]
    # 如果有多个数值的近邻数量相同，选择其中最大的数值
    peak_point = max(peak_points)
    if md:
        peak_point = min(peak_points)
    nums = [i for i in [peak_point-min_diff,peak_point+min_diff] if i in numbers]
    return [peak_point, nums]


def cal_fuzhi(Q0, Qh0, p0, QRS0, ls10, M0):
    '''多Q波时, 计算单个Q集中点的所在通道的最大幅值'''
    vs = []
    for i in range(len(QRS0)):
        if [j for j in p0 if j in QRS0[i]] != []:
            vs.append(round(abs(ls10[i][Q0+Qh0]/M0),3))
    v0 = max(vs)
    return v0


def cal_QhSr(rms, R0, i2, i3, Qh0, Sr0, ls10, rg0=20, rg1=8, rg2=8, th=10, bz=10, vv=5, nm=4):
    '''计算Qh/Sr'''
    rg0 = max(rg0, round(len(ls10[0])*rg0/1000))
    rg1 = max(rg1, round(len(ls10[0])*rg1/1000))
    rg2 = max(rg2, round(len(ls10[0])*rg2/1000))
    th = max(th, round(len(ls10[0])*th/1000))
    i1 = Qh0-rg1
    i4 = Sr0+rg2
    pts1, pts2 = [], []
    for i in range(len(ls10)):
        l0 = ls10[i].copy()
        M0 = max([abs(l0[j]) for j in range(Qh0, Sr0)])
        # std, QhSrs = cal_std(l0, M0, rg0, i1, i2, i3, i4)
        std, QhSrs = cal_std(l0, M0, rg0, i1, i2, i2, i2)
        p1 = [j for j in QhSrs if j in range(i1, round(i2+th/2))]
        pts1.append(p1)
    pts11 = hb_lis0(pts1)
    if len(pts11)==0:
        Qh = Qh0
    elif len(pts11)<=nm:
        Qh = find_peak_point(pts11)[0]
    else:
        ls = sorted(set(pts11))
        ls.reverse()
        jzd = []
        for i in range(len(ls)):
            jld = hb_lis0([[j for j in l0 if j<=ls[i] and j>=ls[i]-10][-1:] for l0 in pts1])
            if len(jld)>nm:  # 多于nm=4个近邻点
                jzd = [find_peak_point(jld)[0]]
                break
        if jzd == []:
            Qh = find_peak_point(pts11)[0]
        else:
            for i in range(3):
                jld = []
                for l0 in pts1:
                    l0 = [j for j in l0 if abs(j-jzd[0])<th]
                    ds = [abs(j-jzd[0]) for j in l0]
                    if len(ds)>0:
                        if sum(1 for j in ds if j==min(ds))==1:
                            jld.append(l0[np.argmin(ds)])
                jzd2 = find_peak_point(jld)[0]
                if jzd2==jzd[0]:
                    break
                else:
                    get_rms0
                    jzd = [jzd2]
            Qh = jzd[0]
    # 定位Sr
    rms10 = cal_rms(ls10)
    rks, _ = signal.find_peaks(-np.array(rms10))
    Srs = [j for j in rks if j in range(round(i3-th/2), i4)]
    Sr = Sr0
    if Srs:
        Sr = Srs[0]
    return [Qh, Sr]


def cal_PpTp(ls36, rms, R0, Qh, Sr, th1=100, th2=80, th=10, bl1=0.05, bl2=0.95, vv=5):
    '''计算Pp/Tp'''
    th1 = max(th1, round(len(rms)*th1/1000))
    th2 = max(th2, round(len(rms)*th2/1000))  # P波前后最大80
    th = max(th, round(len(rms)*th/1000))
    # P波定位
    i1 = round(len(rms)*bl1)
    k2 = max(i1, min(R0-th1,Qh-th))
    if len(rms)<500:
        k2 = max(i1, Qh-th)
    rms0 = rms[i1:k2+1]
    y_smooth = gaussian_filter1d(np.array(rms0), sigma=vv)
    rpeaks, _ = signal.find_peaks(y_smooth, distance=th)
    vs = [rms0[i] for i in rpeaks]
    if vs == []:
        Pp = rms0.index(max(rms0))+i1  # 最大波位置R0
    else:
        Pp = rpeaks[np.argmax(vs)]+i1
    # PhPr
    Ph0, Pr0 = max(0, Pp-th2), min([Pp+th2, R0-th1, Qh-th])  # 最远
    if len(rms)<500:
        Pr0 = min(Pp+th2, Qh-th)
    l0 = [ls36[i][Pp] for i in range(len(ls36))]
    M0 = max(np.abs(l0))
    if max(l0)>-min(l0):
        l1 = [10*max([ls36[i][j] for i in range(len(ls36))])/M0 for j in range(len(ls36[0]))]
    else:
        l1 = [-10*min([ls36[i][j] for i in range(len(ls36))])/M0 for j in range(len(ls36[0]))]
    y_smooth = gaussian_filter1d(np.array(l1), sigma=vv)
    ds = [0.5, 1]
    std, PhPrs = cal_std(l1, M0, 20, Ph0, Pp, Pp, Pr0)
    Ph, Pr = Ph0, Pr0
    l1 = [i for i in PhPrs if i>Ph0 and i<=Pp-th]
    if l1:
        l2 = [i for i in l1 if std[i]<ds[0]]
        if l2:
            Ph = l2[-1]
        else:
            l3 = [i for i in l1 if std[i]<ds[1]]
            if l3:
                Ph = l3[-1]
            else:
                Ph = l1[-1]
    l1 = [i for i in PhPrs if i<Pr0 and i>=Pp+th]
    if l1:
        l2 = [i for i in l1 if std[i]<ds[0]]
        if l2:
            Pr = l2[0]
        else:
            l3 = [i for i in l1 if std[i]<ds[1]]
            if l3:
                Pr = l3[0]
            else:
                Pr = l1[0]
    # T波定位
    i2 = round(len(rms)*bl2)
    k1 = min(i2, max(R0+th1,Sr+th))
    rms0 = rms[k1:i2+1]
    rpeaks, _ = signal.find_peaks(np.array(rms0), distance=th)
    vs = [rms0[i] for i in rpeaks]
    if vs == []:
        Tp = rms0.index(max(rms0))+k1  # 最大波位置R0
    else:
        Tp = rpeaks[np.argmax(vs)]+k1
    return [Pp, Tp, Ph, Pr]


def cal_rms(ls10):
    '''计算RMS'''
    rms = []
    for i in range(len(ls10[0])):
        asimg = np.array([ls10[j][i] for j in range(len(ls10))])
        rms.append(np.sqrt(np.sum(asimg**2, axis=0) / asimg.shape[0]))
    return rms


def cal_lsrms10(ls36, i1, i2, nm=10):
    '''计算ls10, rms10'''
    lis1 = [[i, max([abs(ls36[i][j]) for j in range(i1,i2)])] for i in range(len(ls36))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1*data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    idx = [int(lis1[i][0]) for i in range(nm)]  # 筛选nm=10*个通道
    ls10 = [ls36[i] for i in idx]  # 10通道波
    return idx, ls10


def cal_ToTe(ls36, rms0, Tp, Sr, rg=5, nm=10, th1=100, th2=80, th=10, bl=[0.5,0.2], ds=[120,140,60,130,80]):
    '''计算ToTe'''
    # 较大通道筛选
    rg = max(rg, round(len(ls36[0])*rg/1000))  # 前后rg=5**帧内
    i1, i2 = max(Tp-rg,0), min(Tp+rg+1,len(ls36[0]))
    idx, ls10 = cal_lsrms10(ls36, i1, i2, nm=10)
    rms = cal_rms(ls10)
    # 计算ToTe
    th1 = max(th1, round(len(rms)*th1/1000))
    th = max(th, round(len(rms)*th/1000))
    bl1, bl2 = bl
    dis1, dis2, dis3, dis4, dis5 = ds
    dis1 = min(dis1, round(len(rms)*dis1/1000))
    i1, i2 = min(Tp, max(Tp-th1,Sr+th)), min(Tp+th1,len(rms)-1)
    l0 = hb_lis0([l0[i1:i2+1] for l0 in ls36])
    M0 = max(np.abs(l0))
    M1 = max(rms[i1:i2+1])
    To1, To2, Te1, Te2 = [], [], [], []
    i1 = max(i1,Tp-dis1)
    k2 = max(Tp-th,i1)+1
    for i in range(i1, k2):
        j = k2-1+i1-i
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl1:
            To1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for i in range(i1, k2):
        j = k2-1+i1-i
        if rms[j]/M1>bl1:
            To2 = [[j, rms[j]]]
        else:
            break
    To = max([i1]+[i for i,_ in To1+To2])
    i2 = min(i2,Tp+dis1)+1
    k1 = min(Tp+th,i2)
    for j in range(k1, i2):
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl1:
            Te1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for j in range(k1, i2):
        if rms[j]/M1>bl1:
            Te2 = [[j, rms[j]]]
        else:
            break
    Te = min([i2]+[i for i,_ in Te1+Te2])
    # 计算ThTr
    th2 = max(th2, round(len(rms)*th2/1000))
    dis2 = max(dis2, round(len(rms)*dis2/1000))
    dis3 = max(dis3, round(len(rms)*dis3/1000))
    dis4 = max(dis4, round(len(rms)*dis4/1000))
    dis5 = max(dis5, round(len(rms)*dis5/1000))
    Th1, Th2, Tr1, Tr2 = [], [], [], []
    i1 = min(To, max([Sr+th2, Tp-dis2, To-dis3]))
    k2 = max(To, i1)+1
    for i in range(i1, k2):
        j = k2-1+i1-i
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl2:
            Th1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for i in range(i1, k2):
        j = k2-1+i1-i
        if rms[j]/M1>bl2:
            Th2 = [[j, rms[j]]]
        else:
            break
    Th = max([i1]+[i for i,_ in Th1+Th2])
    i2 = max(Te, min([len(rms)-1, Tp+dis4, Te+dis5]))+1
    k1 = min(Te, i2-1)
    for j in range(k1, i2):
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl2:
            Tr1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for j in range(k1, i2):
        if rms[j]/M1>bl2:
            Tr2 = [[j, rms[j]]]
        else:
            break
    Tr = min([i2]+[i for i,_ in Tr1+Tr2])
    stds = [0.5, 1]
    std, ThTrs = cal_std(rms, rms[Tp], 20, i1, Tp, Tp, i2)
    Ths1 = [i for i in ThTrs if i<k2 and std[i]<stds[0]]
    if Ths1:
        Th = max(Th, Ths1[-1])
    else:
        Th = max([Th]+[i for i in ThTrs if i<k2 and std[i]<stds[1]][-1:])
    Trs1 = [i for i in ThTrs if i>k1 and std[i]<stds[0]]
    if Trs1:
        Tr = min(Tr, Trs1[0])
    else:
        Tr = min([Tr]+[i for i in ThTrs if i>k1 and std[i]<stds[1]][:1])
    return [To, Te, Th, Tr]


def cal_QpSp(Qh, Sr, Qps, Sps):
    '''规范QpSp'''
    Qp, Sp = Qh, Sr
    l0 = [i for i in Qps if i>Qh]
    l1 = [i for i in Sps if i<Sr]
    if l0:
        Qp = l0[0]
    if l1:
        Sp = l1[0]
    return [Qp, Sp]


def cal_times0(f0, g1=''):
    '''计算时刻点20250121: 主要针对OPM, 不易出错'''
    tms = []
    if type(f0)==str:
        ls36 = get_mcg36(f0, 0)
        rms = get_rms0(f0)
    else:
        ls36 = [get_item(f0, i+1, [], jd=6) for i in range(36)]
        rms = []
        for i in range(len(f0)):
            asimg = np.array(f0[i].split()[1:])
            asimg = asimg.astype(float)  # str转float
            vrms = np.sqrt(np.sum(asimg**2, axis=0) / asimg.shape[0])
            rms.append(vrms)
    # 计算
    t0, rms0, idx, ls10 = shaixuan_10channel(rms, ls36, 5)  # 10通道
    Qh0, Sr0, _, _, _ = cal_wendingdian(rms0, t0, 100, 20, 20, 30)
    Rp, Sps, Qps, t00, t11 = cal_beixuanbo(ls10, Qh0, Sr0, t0)
    Qh, Sr = cal_QhSr(rms0, Rp, t00, t11, Qh0, Sr0, ls10)
    Pp, Tp, Ph, Pr = cal_PpTp(ls36, rms0, Rp, Qh, Sr)
    To, Te, Th, Tr = cal_ToTe(ls36, rms, Tp, Sr)
    Qp, Sp = cal_QpSp(Qh, Sr, Qps, Sps)
    # tms = [Ph, Pp, Pr, Qh, Qps[0], Rp, Sps[0], Sr, Th, To, Tp, Te, Tr]
    tms = [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr]
    # # 绘制
    # M0, M1 = get_mai0(ls36)
    # N = len(ls36[0])
    # x = np.arange(N)
    # clis = np.linspace(0, 1, 38)  # 36 去除首尾
    # plt.figure()
    # plt.style.use('bmh')
    # plt.rcParams['savefig.dpi'] = 512  # 保存像素640*480*dpi/100
    # if tms:
    #     plt.xticks(tms, tms, fontsize=4)  # x刻度
    #     for i in tms:
    #         plt.plot([i, i], [-M1, M0], linewidth=0.5, alpha=0.5, c='r')
    # # plt.yticks(ytks, ytks, fontsize=4)
    # for i1 in range(len(ls36)):  # 时间波组图
    #     c = plt.get_cmap('gist_rainbow')(clis[i1])  # 36
    #     plt.plot(x, np.array(ls36[i1]), color=c, linewidth=0.5)  # 36
    # plt.plot([0, N-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    # if rms:
    #     plt.plot(x, rms, color='r', linewidth=1)
    # if g1:
    #     plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    # # plt.show()
    # plt.close()
    return tms


def get_mai0(ls36):
    '''周期最大幅值'''
    M0 = max([max(ls36[i]) for i in range(len(ls36))])
    M1 = -min([min(ls36[i]) for i in range(len(ls36))])
    return [M0, M1]


def diff(l0, l1, iks=0):
    '''两个值/列表作差'''
    if type(l0) == list:
        if type(iks) == list:
            l2 = []
            for i in range(len(iks)):
                l2.append(diff(l0, l1, iks[i]))
            return l2
        else:
            return l0[iks]-l1[iks]
    else:
        return l0-l1


def get_mcg36(f0, md=1):
    '''获取36条心磁信息: N*36, 精度%.6f'''
    l1 = [get_item(get_lines1(f0), i+1, [], jd=6) for i in range(36)]
    if md:  # N*36
        l1 = [[l1[i][j] for i in range(len(l1))] for j in range(len(l1[0]))]
    return l1

def get_rms0(f0):
    mcgdata, lrms = get_lines1(f0), []
    for i in range(len(mcgdata)):
        asimg = np.array(mcgdata[i].split()[1:])
        asimg = asimg.astype(float)  # str转float
        vrms = np.sqrt(np.sum(asimg**2, axis=0) / asimg.shape[0])
        lrms.append(vrms)
    return lrms


def get_split(strs0):
    '''获取分割后的字符串列表'''
    strlis00 = re.split(' |,|\t', strs0.strip())
    strlis00 = rm_null(strlis00)
    return strlis00


def get_item(f2ls, ik=0, lis0=[], jd=3):
    '''提取文档列表的第ik个元素'''
    lis0 = []
    for i4 in range(len(f2ls)):
        row0 = get_split(f2ls[i4])
        lis0.append(get_item_real(row0[ik], jd=jd))
    return lis0


def get_item_real(item, jd=3):
    try:
        return int(item)
    except:
        try:
            item = float(item)
            return get_round(item, jd)
        except:
            if '\'' in item:  # 去除所有字符串符号''
                return get_item_real(item[1:-1])
            else:
                return str(item)


def get_lines(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    return lines


def get_lines1(files):
    '''获取文档内容, 去除换行府'''
    lines = get_lines(files)
    lines0 = []
    for i3 in range(len(lines)):
        lines0.append(lines[i3].replace('\n', ''))
    return lines0


def get_round(lis0, ik=3):
    '''精确到ik位小数, ik为list时按照列表'''
    if type(lis0) == list:
        return [get_round(lis0[i3], ik[i3]) for i3 in range(len(ik))]
    else:  # 单个
        if ik == 0:
            return round(lis0)
        else:
            return round(lis0, ik)


def rm_null(liebiao):
    '''去除列表中的空字符'''
    while "" in liebiao:
        liebiao.remove("")
    return liebiao
