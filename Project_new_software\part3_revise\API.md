
## 可视化

### 概述
本程序主要是实现心磁TT段心脏上的电流源还原, 主函数main_part3


### 参数说明
无

### 主函数输入及输出
直接调用 main_part3.py文件中的函数 main_part3(mcg_path, heart_path, time_loc)即可
输入：分别为mcg_path, heart_path, time_loc
其中 mcg_path是心磁路径, 文件后缀为'.txt'文件
heart_path为心脏模型所在的文件夹，比如本part3路径下的'model'
time_loc是时刻点的向量，time_loc=np.array([Qp, Rp, To, Tp, Te]), 分别为Q峰，R峰，T初，T峰，T末
输出： (1) 心脏需要移动的位置，这是一个1*3的向量，分别代表x,y,z需要移动的位置
      (2) 向量 N * 3 的向量, 其中N代表选取的TT段时刻点长度, 每一行代表每个点的坐标(单位为米). 总体代表TT段整体时刻电流源的位置
### 例子

```
例1
    mcg_path = r'data/BJ_TT_001327.txt'
    time_loc = np.array([278, 305, 510, 580, 612])
    heart_path = 'model'
    result = main_part3(mcg_path, heart_path, time_loc)

运行时间为0.9321s
[28.30620435 62.98265381  7.88]
输出为 单位为米
[[0.13359918 0.1448598  0.11551835]
 [0.13531574 0.1448598  0.11660154]
 [0.13531574 0.1448598  0.11660154]
 [0.13531574 0.1448598  0.11660154]
 [0.13531574 0.1448598  0.11660154]
 [0.13739359 0.1448598  0.11784871]
 [0.13739359 0.1448598  0.11784871]
 [0.13739359 0.1448598  0.11784871]
 [0.13739359 0.1448598  0.11784871]
 [0.14115225 0.14283748 0.12089218]
 [0.14115225 0.14283748 0.12089218]
 [0.14115225 0.14283748 0.12089218]
 [0.14338629 0.14050834 0.12296022]
 [0.14338629 0.14050834 0.12296022]
 [0.14338629 0.14277117 0.1218664 ]
 [0.14338629 0.14277117 0.1218664 ]
 [0.14338629 0.14277117 0.1218664 ]
 [0.14338629 0.14277117 0.1218664 ]
 [0.14338629 0.14277117 0.1218664 ]
 [0.14240758 0.1448598  0.12032929]
 [0.14142887 0.14704516 0.11826443]
 [0.14142887 0.14704516 0.11826443]
 [0.13868075 0.15070214 0.11464411]
 [0.13650482 0.15166977 0.11268669]
 [0.13534927 0.15418615 0.11072927]
 [0.13471391 0.15632525 0.10914252]
 [0.13164175 0.15976468 0.10611452]
 [0.13164175 0.15976468 0.10611452]
 [0.17087505 0.11421495 0.09898474]
 [0.17000089 0.11421495 0.09604861]
 [0.17000089 0.11421495 0.09604861]
 [0.17000089 0.11421495 0.09604861]
 [0.17000089 0.11421495 0.09604861]
 [0.17000089 0.11421495 0.09604861]
 [0.17000089 0.11421495 0.09604861]
 [0.16912673 0.11421495 0.09311247]
 [0.16912673 0.11421495 0.09311247]
 [0.16883277 0.11371135 0.09311247]
 [0.16883277 0.11371135 0.09311247]
 [0.16883277 0.11218789 0.09604861]
 [0.16883277 0.11218789 0.09604861]
 [0.16687535 0.11150417 0.09311247]
 [0.16664144 0.11024632 0.09502836]
 [0.16664144 0.11024632 0.09502836]
 [0.16664144 0.11024632 0.09502836]
 [0.16664144 0.11024632 0.09502836]
 [0.16491793 0.10828135 0.09506989]
 [0.16470246 0.10705459 0.09714744]
 [0.16470246 0.10705459 0.09714744]
 [0.16470246 0.10705459 0.09714744]
 [0.16470246 0.10705459 0.09714744]
 [0.16296051 0.10428406 0.09898474]]
```





