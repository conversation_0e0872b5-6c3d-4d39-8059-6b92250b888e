"""
特征一致性测试脚本
测试我们生成的特征与历史验证特征的一致性
"""

import pandas as pd
import pickle
import numpy as np
import os
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, '.')

# 直接导入需要的模块，避免matplotlib问题
from feature_generator.main_feature_generator import FeatureGenerator


def load_pickle_safe(file_path):
    """安全加载pickle文件，处理pandas版本兼容性问题"""
    try:
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    except Exception as e1:
        print(f'标准加载失败: {e1}')
        try:
            return pd.read_pickle(file_path)
        except Exception as e2:
            print(f'pandas加载也失败: {e2}')
            return None


def load_historical_features():
    """加载历史验证的特征"""
    print("=== 加载历史验证特征 ===")
    
    # 加载cur特征
    cur_data = load_pickle_safe('files/tests/saved_outer_features/cur_feature_df_all3923.pkl')
    print(f"历史cur特征: {cur_data.shape if cur_data is not None else 'None'}")
    
    # 加载ci特征
    ci_data = load_pickle_safe('files/tests/saved_outer_features/ci_feature_df_all3923.pkl')
    print(f"历史ci特征: {ci_data.shape if ci_data is not None else 'None'}")
    
    # 加载基础特征
    base_files = [
        'files/tests/saved_base_features/GDNH_2023_000434.pkl',
        'files/tests/saved_base_features/GDNH_2023_000437.pkl',
        'files/tests/saved_base_features/GDNH_2023_000438.pkl',
        'files/tests/saved_base_features/GDNH_2023_000450.pkl'
    ]
    
    base_data = {}
    for file in base_files:
        if os.path.exists(file):
            data = load_pickle_safe(file)
            if data is not None:
                filename = Path(file).stem
                base_data[filename] = data
                print(f"历史基础特征 {filename}: {data.shape}")
    
    return cur_data, ci_data, base_data


def generate_current_features(txt_files):
    """使用当前流程生成特征"""
    print("\n=== 使用当前流程生成特征 ===")

    # 直接使用核心模块
    feature_generator = FeatureGenerator()

    current_features = {}

    for txt_file in txt_files:
        if os.path.exists(txt_file):
            filename = Path(txt_file).stem
            print(f"\n处理文件: {filename}")

            try:
                # 生成基础特征
                print("  正在生成基础特征...")
                base_features = feature_generator.get_all(txt_file)
                base_df = pd.DataFrame([base_features])
                print(f"  基础特征: {base_df.shape}")

                # 生成cur特征
                print("  正在生成cur特征...")
                cur_df = generate_cur_features_direct(txt_file)
                print(f"  cur特征: {cur_df.shape}")

                # 生成ci特征
                print("  正在生成ci特征...")
                ci_df = generate_ci_features_direct(txt_file)
                print(f"  ci特征: {ci_df.shape}")

                current_features[filename] = {
                    'base': base_df,
                    'cur': cur_df,
                    'ci': ci_df
                }

            except Exception as e:
                print(f"  生成特征失败: {e}")
                import traceback
                traceback.print_exc()

    return current_features


def generate_cur_features_direct(txt_file):
    """直接生成cur特征"""
    try:
        # 切换到cur_features目录
        cur_features_dir = Path(__file__).parent / "outer_features" / "cur_features"
        original_cwd = os.getcwd()

        try:
            os.chdir(cur_features_dir)
            sys.path.insert(0, str(cur_features_dir))
            from main import MCGAnalyzer

            analyzer = MCGAnalyzer()
            abs_txt_file = os.path.abspath(os.path.join(original_cwd, txt_file))
            result = analyzer.analyze_file(abs_txt_file)

            # 转换为DataFrame
            mcg_file = Path(txt_file).stem
            if isinstance(result, dict):
                cur_features = {'mcg_file': mcg_file}
                for key, value in result.items():
                    cur_features[f'cur_{key}'] = value
                return pd.DataFrame([cur_features])
            else:
                return pd.DataFrame({'mcg_file': [mcg_file]})

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"cur特征生成失败: {e}")
        return pd.DataFrame({'mcg_file': [Path(txt_file).stem]})


def generate_ci_features_direct(txt_file):
    """直接生成ci特征"""
    try:
        # 切换到ci_features目录
        ci_features_dir = Path(__file__).parent / "outer_features" / "ci_features"
        original_cwd = os.getcwd()

        try:
            os.chdir(ci_features_dir)
            sys.path.insert(0, str(ci_features_dir))

            # 导入ci_features的main模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("ci_main", ci_features_dir / "main.py")
            ci_main = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(ci_main)

            abs_txt_file = os.path.abspath(os.path.join(original_cwd, txt_file))
            ci_features_dict = ci_main.generate_ci_features_with_names(abs_txt_file)

            # 添加mcg_file
            mcg_file = Path(txt_file).stem
            ci_features = {'mcg_file': mcg_file}
            ci_features.update(ci_features_dict)

            return pd.DataFrame([ci_features])

        finally:
            os.chdir(original_cwd)

    except Exception as e:
        print(f"ci特征生成失败: {e}")
        return pd.DataFrame({'mcg_file': [Path(txt_file).stem]})


def compare_features(historical, current, feature_type):
    """比较特征的一致性"""
    print(f"\n=== 比较{feature_type}特征一致性 ===")
    
    if historical is None:
        print(f"历史{feature_type}特征为空，跳过比较")
        return
    
    # 比较特征名称
    hist_columns = set(historical.columns)
    
    results = {}
    
    for filename, features in current.items():
        if feature_type not in features:
            continue
            
        current_df = features[feature_type]
        current_columns = set(current_df.columns)
        
        print(f"\n文件: {filename}")
        print(f"  历史特征数: {len(hist_columns)}")
        print(f"  当前特征数: {len(current_columns)}")
        
        # 特征名称比较
        common_features = hist_columns & current_columns
        hist_only = hist_columns - current_columns
        current_only = current_columns - hist_columns
        
        print(f"  共同特征: {len(common_features)}")
        print(f"  历史独有: {len(hist_only)}")
        print(f"  当前独有: {len(current_only)}")
        
        if hist_only:
            print(f"  历史独有特征: {list(hist_only)[:10]}{'...' if len(hist_only) > 10 else ''}")
        if current_only:
            print(f"  当前独有特征: {list(current_only)[:10]}{'...' if len(current_only) > 10 else ''}")
        
        # 查找对应的历史样本
        if 'mcg_file' in historical.columns:
            hist_sample = historical[historical['mcg_file'] == filename]
            if len(hist_sample) == 0:
                hist_sample = historical[historical['mcg_file'].str.contains(filename, na=False)]
        else:
            hist_sample = historical.iloc[0:1]  # 取第一个样本
        
        if len(hist_sample) > 0:
            # 数值比较
            value_diffs = []
            for feature in list(common_features)[:20]:  # 只比较前20个特征
                if feature == 'mcg_file':
                    continue
                    
                try:
                    hist_val = float(hist_sample[feature].iloc[0])
                    curr_val = float(current_df[feature].iloc[0])
                    diff = abs(hist_val - curr_val)
                    rel_diff = diff / (abs(hist_val) + 1e-10)
                    
                    if diff > 1e-6:  # 如果差异大于阈值
                        value_diffs.append({
                            'feature': feature,
                            'hist_val': hist_val,
                            'curr_val': curr_val,
                            'abs_diff': diff,
                            'rel_diff': rel_diff
                        })
                except:
                    pass
            
            if value_diffs:
                print(f"  数值差异特征数: {len(value_diffs)}")
                for diff in value_diffs[:5]:  # 只显示前5个
                    print(f"    {diff['feature']}: 历史={diff['hist_val']:.6f}, 当前={diff['curr_val']:.6f}, 差异={diff['abs_diff']:.6f}")
            else:
                print(f"  数值完全一致！")
        
        results[filename] = {
            'common_features': len(common_features),
            'hist_only': len(hist_only),
            'current_only': len(current_only),
            'value_diffs': len(value_diffs) if 'value_diffs' in locals() else 0
        }
    
    return results


def test_model_predictions(current_features):
    """测试模型预测结果"""
    print("\n=== 测试模型预测结果 ===")
    
    pipeline = DeploymentPipeline(model_version='v2.5.03')
    
    # 空的临床数据
    empty_clinical_data = {
        'clinic_height': 0,
        'clinic_weight': 0,
        'clinic_gender': 0,
        'clinic_age': 0,
        'clinic_smoking': 0,
        'clinic_drinking': 0,
        'clinic_hypertension': 0,
        'clinic_hyperlipidemia': 0,
        'clinic_intervention': 0,
        'clinic_hospital': 0,
        'clinic_diabetes': 0,
        'clinic_symp': 0
    }
    
    for filename in current_features.keys():
        txt_file = f'files/tests/test_txts/{filename}.txt'
        if os.path.exists(txt_file):
            try:
                prob, pred = pipeline.predict_single_file(txt_file, empty_clinical_data)
                print(f"  {filename}: 概率={prob:.4f}, 类别={pred}")
            except Exception as e:
                print(f"  {filename}: 预测失败 - {e}")


def main():
    """主函数"""
    print("🔍 特征一致性测试开始")
    
    # 1. 加载历史特征
    hist_cur, hist_ci, hist_base = load_historical_features()
    
    # 2. 获取测试文件列表
    test_txt_files = [
        'files/tests/test_txts/GDNH_2023_000434.txt',
        'files/tests/test_txts/GDNH_2023_000437.txt',
        'files/tests/test_txts/GDNH_2023_000438.txt',
        'files/tests/test_txts/GDNH_2023_000440.txt'
    ]
    
    # 3. 生成当前特征
    current_features = generate_current_features(test_txt_files)
    
    # 4. 比较特征一致性
    if hist_cur is not None:
        compare_features(hist_cur, current_features, 'cur')
    
    if hist_ci is not None:
        compare_features(hist_ci, current_features, 'ci')
    
    # 比较基础特征
    for filename, hist_base_df in hist_base.items():
        if filename in current_features:
            print(f"\n=== 比较基础特征一致性: {filename} ===")
            current_base = current_features[filename]['base']
            
            hist_columns = set(hist_base_df.columns)
            current_columns = set(current_base.columns)
            
            common_features = hist_columns & current_columns
            print(f"  历史特征数: {len(hist_columns)}")
            print(f"  当前特征数: {len(current_columns)}")
            print(f"  共同特征: {len(common_features)}")
            print(f"  历史独有: {len(hist_columns - current_columns)}")
            print(f"  当前独有: {len(current_columns - hist_columns)}")
    
    # 5. 测试模型预测
    test_model_predictions(current_features)
    
    print("\n✅ 特征一致性测试完成")


if __name__ == '__main__':
    main()
