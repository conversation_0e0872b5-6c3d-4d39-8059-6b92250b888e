
## TT段向量可视化

### 概述

本程序主要是计算TT段电流源的向量，并生成可视化图, 新版本的作图呈现圆盘内部画图


### 主函数输入及输出
主函数：直接调用文件main_part4.py中的main_part4(mcg_path, heart_path, time_loc)
输入：分别为mcg_path, heart_path, time_loc
其中 mcg_path是心磁路径, 文件后缀为'.txt'文件
heart_path为心脏模型所在的文件夹，比如本part4路径下的'model'
time_loc是时刻点的向量，time_loc=np.array([Qp, Rp, To, Tp, Te]), 分别为Q峰，R峰，T初，T峰，T末

输出：被试的TT段向量图 TT.jpg 生成在目录下



### 例子

```
    mcg_path = r'data\\PLAG_2024_000059.txt'
    heart_path = r'model'
    Qp, Rp, To, Tp, Te = 311, 335, 516, 597, 647
    time_loc = np.array([Qp, Rp, To, Tp, Te])
    main_part4(mcg_path, heart_path, time_loc)


运行时间为2.194848s


