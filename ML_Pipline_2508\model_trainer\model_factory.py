"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: model_factory
date: 2025/1/24 下午2:06
desc: 
"""

from dataclasses import dataclass
from typing import List, Set

import numpy as np
import xgboost as xgb
from sklearn.ensemble import (
    RandomForestClassifier,
    GradientBoostingClassifier,
    ExtraTreesClassifier,
    StackingClassifier
)
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (roc_auc_score, accuracy_score)
from sklearn.neural_network import MLPClassifier
from sklearn.svm import SVC

from model_trainer.models import CRFXGBClassifier
from temp_scripts.dynamicEnsembleSelector import BartClassifierWrapper


class ModelFactory:
    """模型工厂类，用于创建和管理各种模型"""

    @staticmethod
    def create_base_models(random_state=42, regression=False):
        """
        创建基础模型字典

        Args:
            random_state: 随机种子

        Returns:
            dict: 模型字典
        """
        if regression:
            # 首先是基础模型的定义// 本次建模暂不考虑回归问题
            base_models = {
                'XGBoost-Default': xgb.XGBRegressor(
                    n_estimators=300,
                    learning_rate=0.001,
                    max_depth=3,
                    min_child_weight=3,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    gamma=0.1,
                    random_state=random_state,
                ),
                'XGBoost-Deep': xgb.XGBRegressor(
                    n_estimators=600,
                    learning_rate=0.01,
                    max_depth=6,
                    min_child_weight=1,
                    subsample=0.9,
                    colsample_bytree=0.9,
                    gamma=0.05,
                    random_state=random_state,
                ),
                'BART': BartClassifierWrapper(
                    n_trees=50,
                    n_chains=4,
                    n_samples=200,
                    n_burn=100,
                ),

            }
        else:
            base_models = {
                # 'XGBoost-Default': xgb.XGBClassifier(
                #     n_estimators=300,
                #     learning_rate=0.001,
                #     max_depth=3,
                #     min_child_weight=3,
                #     subsample=0.8,
                #     colsample_bytree=0.8,
                #     gamma=0.1,
                #     random_state=random_state,
                # ),
                'XGBoost-Deep': xgb.XGBClassifier(
                    n_estimators=600,
                    learning_rate=0.01,
                    max_depth=6,
                    min_child_weight=1,
                    subsample=0.9,
                    colsample_bytree=0.9,
                    gamma=0.05,
                    random_state=random_state,
                    # early_stopping_rounds=50,
                ),
                # 'CRFXGB_S1.5': CRFXGBClassifier(sigma=1.5
                #                                 ),
                # 'CRFXGB_S2.5': CRFXGBClassifier(sigma=2.5
                #                                 ),
                # 'CRFXGB_S3.5': CRFXGBClassifier(sigma=3.0
                #                                 ),
                # 'NGBoost': NGBClassifier(
                #     n_estimators=600,
                #     learning_rate=0.01,
                #     random_state=random_state
                # ),
                'GradientBoosting': GradientBoostingClassifier(
                    n_estimators=300,
                    learning_rate=0.01,
                    max_depth=3,
                    min_samples_split=5,
                    random_state=random_state
                ),
                # 'ExtraTrees': ExtraTreesClassifier(
                #     n_estimators=300,
                #     max_depth=6,
                #     min_samples_split=5,
                #     random_state=random_state
                # ),
                'MLP': MLPClassifier(
                    hidden_layer_sizes=(100, 50),
                    activation='relu',
                    solver='adam',
                    alpha=0.0001,
                    learning_rate='adaptive',
                    max_iter=500,
                    random_state=random_state
                ),
                # 'RandomForest-Default': RandomForestClassifier(
                #     n_estimators=300,
                #     max_depth=3,
                #     min_samples_split=5,
                #     random_state=42
                # ),
                'RandomForest-Deep': RandomForestClassifier(
                    n_estimators=500,
                    max_depth=8,
                    min_samples_split=2,
                    max_features='sqrt',
                    random_state=42
                ),
                'LogisticRegression': LogisticRegression(
                    C=1.0,
                    random_state=42,
                    max_iter=1000
                ),
                'SVM': SVC(
                    kernel='rbf',
                    C=1.0,
                    probability=True,
                    random_state=42
                ),
            # 'BART': BartClassifierWrapper(
            #         n_trees=150,
            #         n_chains=6,
            #         n_samples=200,
            #         n_burn=300,
            #     ),
            # 'CatBoost': CatBoostClassifier(
            #     iterations=300,
            #     learning_rate=0.001,
            #     depth=3,
            #     random_state=42,
            #     verbose=False
            # ),
            }
        return base_models
    @staticmethod
    def create_base_models_new(random_state=42, ):
        """
        创建基础模型字典

        Args:
            random_state: 随机种子

        Returns:
            dict: 模型字典
        """
        base_models = {
            'XGBoost-Deep': xgb.XGBClassifier(
                n_estimators=600,
                learning_rate=0.01,
                max_depth=6,
                min_child_weight=1,
                subsample=0.9,
                colsample_bytree=0.9,
                gamma=0.05,
                random_state=random_state,
                # early_stopping_rounds=50,
            ),
            'CRFXGB_S1.5': CRFXGBClassifier(sigma=1.5),
            'CRFXGB_S2.0': CRFXGBClassifier(sigma=2.0),
            'CRFXGB_S2.5': CRFXGBClassifier(sigma=2.5),
            'CRFXGB_S3.0': CRFXGBClassifier(sigma=3.0),
            'CRFXGB_S3.5': CRFXGBClassifier(sigma=3.5),
            'CRFXGB_S4.0': CRFXGBClassifier(sigma=4.0),
            'CRFXGB_S4.5': CRFXGBClassifier(sigma=4.5),
            'CRFXGB_S5.0': CRFXGBClassifier(sigma=5.0),
            'ExtraTrees': ExtraTreesClassifier(
                n_estimators=500,
                max_depth=6,
                min_samples_split=5,
                random_state=random_state
            ),
        }
        return base_models

    @staticmethod
    def create_stacking_model(base_models, random_state=42):
        """
        创建两种不同策略的Stacking集成模型

        Strategy 0: CRF(4个) + XGBoost-Deep + ExtraTrees + RandomForest-Deep
        Strategy 1: [CRF(4个) + XGBoost-Deep + RandomForest-Deep + ExtraTrees]voter + MLP + SVM + LR

        Args:
            base_models: 基础模型字典
            random_state: 随机种子

        Returns:
            dict: 包含两种stacking策略的字典
        """
        from sklearn.ensemble import VotingClassifier
        # 定义meta学习器
        meta_learner = LogisticRegression(random_state=random_state)

        # Strategy 0: 直接组合策略
        strategy0_models = [
            ('crf_s1.5', base_models['CRFXGB_S1.5']),
            ('crf_s2.5', base_models['CRFXGB_S2.5']),
            ('crf_s3.5', base_models['CRFXGB_S3.5']),
            ('xgb_deep', base_models['XGBoost-Deep']),
            ('et', base_models['ExtraTrees']),
            ('rf_deep', base_models['RandomForest-Deep'])
        ]

        # Strategy 1: 混合voter策略
        # 首先创建tree-based voter
        tree_voter_models = [
            ('crf_s1.5', base_models['CRFXGB_S1.5']),
            ('crf_s2.5', base_models['CRFXGB_S2.5']),
            ('crf_s3.5', base_models['CRFXGB_S3.5']),
            ('xgb_deep', base_models['XGBoost-Deep']),
            ('rf_deep', base_models['RandomForest-Deep']),
            ('et', base_models['ExtraTrees'])
        ]

        tree_voter = VotingClassifier(
            estimators=tree_voter_models,
            voting='soft',
            weights=[1, 1, 1, 1, 1, 2]  # 给予XGBoost更高权重
        )

        strategy1_models = [
            ('tree_voter', tree_voter),
            ('mlp', base_models['MLP']),
            ('svm', base_models['SVM']),
            ('lr', base_models['LogisticRegression'])
        ]

        # 创建两种策略的stacking模型
        stacking_models = {}

        # Strategy 0
        stacking_models['Stacking-Strategy0'] = StackingClassifier(
            estimators=strategy0_models,
            final_estimator=meta_learner,
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )

        # Strategy 1
        stacking_models['Stacking-Strategy1'] = StackingClassifier(
            estimators=strategy1_models,
            final_estimator=meta_learner,
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )

        return stacking_models

    @staticmethod
    def create_stacking_model_new(base_models, random_state=42):
        """
        创建多种策略的Stacking集成模型，包括原始堆叠、加权堆叠和分层堆叠

        Args:
            base_models: 基础模型字典
            random_state: 随机种子

        Returns:
            dict: 包含多种stacking策略的字典
        """
        from sklearn.ensemble import  VotingClassifier

        # 定义XGBoost meta学习器参数
        meta_params = {
            'max_depth': 2,  # 较浅的树深度避免过拟合
            'learning_rate': 0.1,  # 小学习率确保稳定性
            'n_estimators': 30,  # 适中的树数量
            'min_child_weight': 3,  # 控制过拟合
            'subsample': 0.8,  # 随机采样防止过拟合
            'colsample_bytree': 0.8,  # 特征采样
            'gamma': 0.1,  # 分裂所需的最小损失减少值
            'reg_alpha': 0.1,  # L1正则化
            'reg_lambda': 1,  # L2正则化
        }
        meta_learner = xgb.XGBClassifier(**meta_params, random_state=random_state)
        meta_learner2 = LogisticRegression(random_state=random_state)

        # 定义基础模型列表（Strategy 0）
        strategy0_models = [
            ('crf_s1.5', base_models['CRFXGB_S1.5']),
            ('crf_s2.0', base_models['CRFXGB_S2.0']),
            ('crf_s2.5', base_models['CRFXGB_S2.5']),
            ('crf_s3.0', base_models['CRFXGB_S3.0']),
            ('crf_s3.5', base_models['CRFXGB_S3.5']),
            ('crf_s4.0', base_models['CRFXGB_S4.0']),
            ('crf_s4.5', base_models['CRFXGB_S4.5']),
            ('crf_s5.0', base_models['CRFXGB_S5.0']),
            ('xgb_deep', base_models['XGBoost-Deep']),
            ('et', base_models['ExtraTrees']),
        ]

        # 创建存储不同策略的字典
        stacking_models = {}

        # 4. 分层堆叠（Hierarchical Stacking）
        high_spe_models = [
            ('et', base_models['ExtraTrees']),
            ('xgb_deep', base_models['XGBoost-Deep']),
        ]
        high_sen_models = [
            ('crf_s1.5', base_models['CRFXGB_S1.5']),
            ('crf_s2.0', base_models['CRFXGB_S2.0']),
            ('crf_s2.5', base_models['CRFXGB_S2.5']),
            ('crf_s3.0', base_models['CRFXGB_S3.0']),
            ('crf_s4.0', base_models['CRFXGB_S4.0']),
            ('crf_s4.5', base_models['CRFXGB_S4.5']),
            ('crf_s5.0', base_models['CRFXGB_S5.0']),
            ('crf_s3.5', base_models['CRFXGB_S3.5']),
        ]

        # 第一层：高SPE和SEN模型
        high_spe_voter = VotingClassifier(
            estimators=[
                ('et', base_models['ExtraTrees']),
                ('xgb', xgb.XGBClassifier(random_state=random_state)),
            ],
            voting='soft',
            weights=[0.4, 0.6],  # 突出 ExtraTrees 的 SPE
            n_jobs=-1
        )
        high_sen_voter = VotingClassifier(
            estimators=[
                ('crf_s1.5', base_models['CRFXGB_S1.5']),
                ('crf_s2.0', base_models['CRFXGB_S2.0']),
                ('crf_s2.5', base_models['CRFXGB_S2.5']),
                ('crf_s3.0', base_models['CRFXGB_S3.0']),
                ('crf_s4.0', base_models['CRFXGB_S4.0']),
                ('crf_s4.5', base_models['CRFXGB_S4.5']),
                ('crf_s5.0', base_models['CRFXGB_S5.0']),
                ('xgb_deep', base_models['XGBoost-Deep']),
            ],
            voting='soft',
            weights=[0.125] * 8,  # 平均分配
            n_jobs=-1
        )

        hierarchical_models = [
            ('high_spe_voter', high_spe_voter),
            ('high_sen_voter', high_sen_voter),
        ]
        stacking_models['Hierarchical_Stacking'] = StackingClassifier(
            estimators=hierarchical_models,
            final_estimator=LogisticRegression(random_state=random_state),
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )


        # 1. 原始堆叠模型（Stacking with XGBoost meta-learner）
        stacking_models['Stacking'] = StackingClassifier(
            estimators=strategy0_models,
            final_estimator=meta_learner,
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )

        # 2. 原始堆叠模型（Stacking with LogisticRegression meta-learner）
        stacking_models['Stacking_lr'] = StackingClassifier(
            estimators=strategy0_models,
            final_estimator=meta_learner2,
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )

        # 3. 加权堆叠（Weighted Stacking）
        # 赋予ExtraTrees较高权重以突出其SPE优势，其他模型平分剩余权重
        weights = [0.1] * len(strategy0_models)  # 默认权重
        for i, (name, _) in enumerate(strategy0_models):
            if name == 'et':  # ExtraTrees
                weights[i] = 0.3  # 提高ExtraTrees权重
        # 调整其他模型权重，使总和为1
        remaining_weight = (1.0 - 0.3) / (len(strategy0_models) - 1)
        for i, (name, _) in enumerate(strategy0_models):
            if name != 'et':
                weights[i] = remaining_weight

        stacking_models['Weighted_Stacking'] = VotingClassifier(
            estimators=strategy0_models,
            voting='soft',  # 使用概率预测加权平均
            weights=weights,
            n_jobs=-1
        )



        return stacking_models

    @staticmethod
    def create_stacking_model_old(base_models, random_state=42):
        """
        创建多个不同配置的Stacking集成模型

        Args:
            base_models: 基础模型字典
            random_state: 随机种子

        Returns:
            dict: 包含多个stacking变体的字典
        """
        from sklearn.ensemble import VotingClassifier
        # 定义不同的基模型组合
        base_model_combinations = {
            'standard': [
                ('xgb', base_models['XGBoost-Deep']),
                ('gb', base_models['GradientBoosting']),
                ('rf', base_models['RandomForest-Default']),
                ('lr', base_models['LogisticRegression']),
                ('svm', base_models['SVM']),
                ('mlp', base_models['MLP'])
            ],
        }

        # 定义不同的meta学习器
        meta_learners = {
            'lr': LogisticRegression(random_state=random_state),
        }

        # 创建不同的stacking模型
        stacking_models = {}

        # 1. 标准Stacking（保持原有的默认版本）
        stacking_models['Stacking'] = StackingClassifier(
            estimators=base_model_combinations['standard'],
            final_estimator=LogisticRegression(random_state=random_state),
            cv=5,
            stack_method='predict_proba',
            n_jobs=5
        )

        # 2. Stacking with Voting # 这里的投票还可以优化一下。。。
        voter = VotingClassifier(
            estimators=base_model_combinations['standard'],
            voting='soft',
            weights=[1, 1, 1, 1, 1, 1]
        )

        hybrid_estimators = [
            ('voter', voter),
            ('xgb', base_models['XGBoost-Deep']),
            ('rf', base_models['RandomForest-Default']),
            ('mlp', base_models['MLP'])

        ]

        stacking_models['Stacking-Voting-Hybrid'] = StackingClassifier(
            estimators=hybrid_estimators,
            final_estimator=meta_learners['lr'],
            cv=5,
            stack_method='predict_proba',
            n_jobs=5
        )

        return stacking_models
    @staticmethod
    def create_stacking_model_v0422(base_models, random_state=42):
        """
        创建多个不同配置的Stacking集成模型

        Args:
            base_models: 基础模型字典
            random_state: 随机种子

        Returns:
            dict: 包含多个stacking变体的字典
        """
        from sklearn.ensemble import VotingClassifier
        # 定义不同的基模型组合
        base_model_combinations = {
            'standard': [
                # ('rf', base_models['RandomForest-Deep']),
                ('lr', base_models['LogisticRegression']),
                ('svm', base_models['SVM']),
                ('mlp', base_models['MLP'])
            ],
        }

        # 定义不同的meta学习器
        meta_learners = {
            'lr': LogisticRegression(random_state=random_state),
        }

        # 创建不同的stacking模型
        stacking_models = {}

        # 1. 标准Stacking（保持原有的默认版本）
        stacking_models['Stacking'] = StackingClassifier(
            estimators=base_model_combinations['standard'],
            final_estimator=LogisticRegression(random_state=random_state),
            cv=5,
            stack_method='predict_proba',
            n_jobs=5
        )

        return stacking_models

    @classmethod
    def create_all_models(cls, random_state=42, include_stacking=True):
        """
        创建所有模型，包括基础模型和集成模型

        Args:
            random_state: 随机种子
            include_stacking: 是否包含stacking模型

        Returns:
            dict: 所有模型的字典
        """
        # 获取基础模型
        models = cls.create_base_models(random_state)

        # 添加stacking模型
        if include_stacking:
            stacking_model = cls.create_stacking_model_v0422(models, random_state)
            models.update(stacking_model)

        return models

    @classmethod

    def create_all_models_new(cls, random_state=42, include_stacking=True):
        """
        创建所有模型，包括基础模型和集成模型

        Args:
            random_state: 随机种子
            include_stacking: 是否包含stacking模型

        Returns:
            dict: 所有模型的字典
        """
        # 获取基础模型
        models = cls.create_base_models_new(random_state)

        # 添加stacking模型
        if include_stacking:
            stacking_model = cls.create_stacking_model_new(models, random_state)
            models.update(stacking_model)

        return models


@dataclass
class ModelMetrics:
    auc: List[float]
    acc: List[float]
    sen: List[float]
    spe: List[float]
    selected_features: Set[str]  # 添加特征名称记录

    def get_means(self):
        return {
            'auc': np.mean(self.auc),
            'acc': np.mean(self.acc),
            'sen': np.mean(self.sen),
            'spe': np.mean(self.spe)
        }

    def get_stds(self):
        return {
            'auc': np.std(self.auc),
            'acc': np.std(self.acc),
            'sen': np.std(self.sen),
            'spe': np.std(self.spe)
        }


class LatentRoutingMOEModel:
    """潜在路由MOE模型封装类 - 训练时利用consistency，推理时不依赖"""

    def __init__(self, experts, router, selected_features, scaler=None, threshold=0.5):
        """
        初始化MOE模型

        参数:
            experts: 专家模型列表，每个专家是一个字典
            router: 路由模型，用于预测样本应由哪个专家处理
            selected_features: 模型使用的特征列表
            scaler: 特征标准化器
            threshold: 分类阈值，默认0.5
        """
        self.experts = experts
        self.router = router
        self.selected_features = selected_features
        self.scaler = scaler
        self.threshold = threshold

        # 统计专家分布
        hospitals = set()
        consistency_targets = {0: 0, 1: 0}
        for expert in self.experts:
            hospitals.add(expert.get('hospital', 'unknown'))
            target = expert.get('consistency_target', -1)
            if target in consistency_targets:
                consistency_targets[target] += 1

        self.hospital_count = len(hospitals)
        self.consistency_distribution = consistency_targets

        # 保存重要的调试信息
        self.debug_info = {
            'expert_count': len(experts),
            'hospitals': list(hospitals),
            'consistency_distribution': consistency_targets
        }

    def predict_proba(self, X):
        """
        预测样本的概率

        参数:
            X: 特征矩阵，应与训练时使用的特征对应

        返回:
            概率矩阵 [[p(阴性), p(阳性)], ...]
        """
        # 应用特征缩放(如果提供了scaler)
        # if self.scaler is not None:
        #     X = self.scaler.transform(X)

        # 1. 路由模型预测每个专家的权重
        expert_probs = self.router.predict_proba(X)

        # 2. 所有专家独立生成预测
        all_expert_preds = np.zeros((X.shape[0], len(self.experts)))

        for i, expert in enumerate(self.experts):
            try:
                # 获取专家模型预测
                expert_model = expert['model']
                probs = expert_model.predict_proba(X)[:, 1]

                # 如果是需要反转预测的专家，反转概率
                if expert.get('reverse_predictions', False):
                    probs = 1 - probs

                all_expert_preds[:, i] = probs
            except Exception as e:
                print(f"专家 {i} 预测失败: {str(e)}")
                all_expert_preds[:, i] = 0.5  # 填充默认值

        # 3. 加权组合所有专家的预测
        y_prob_1 = np.sum(all_expert_preds * expert_probs, axis=1)

        # 构建完整概率矩阵 [[p(阴性), p(阳性)], ...]
        y_prob = np.vstack([1 - y_prob_1, y_prob_1]).T

        return y_prob

    def predict(self, X):
        """
        预测样本的类别

        参数:
            X: 特征矩阵

        返回:
            预测的类别标签 (0或1)
        """
        y_prob = self.predict_proba(X)
        return (y_prob[:, 1] > self.threshold).astype(int)

    def get_expert_metrics(self, X, y):
        """
        获取每个专家模型在数据集上的表现

        参数:
            X: 特征矩阵
            y: 真实标签

        返回:
            每个专家的性能指标字典
        """
        # 应用特征缩放(如果提供了scaler)
        if self.scaler is not None:
            X = self.scaler.transform(X)

        metrics = []

        for i, expert in enumerate(self.experts):
            try:
                # 获取专家模型预测
                probs = expert['model'].predict_proba(X)[:, 1]

                # 如果是需要反转预测的专家，反转概率
                if expert.get('reverse_predictions', False):
                    probs = 1 - probs

                preds = (probs > 0.5).astype(int)

                # 计算指标
                expert_metrics = {
                    'expert_id': i,
                    'hospital': expert.get('hospital', 'unknown'),
                    'consistency_target': expert.get('consistency_target', -1),
                    'accuracy': accuracy_score(y, preds),
                    'auc': roc_auc_score(y, probs)
                }
                metrics.append(expert_metrics)
            except Exception as e:
                print(f"专家 {i} 指标计算失败: {str(e)}")

        return metrics


class DoubleModel:
    def __init__(self, model1, model2, selected_features1, selected_features2,
                 threshold1=0.5, threshold2=0.5, scaler1=None, scaler2=None):
        """
        双层级模型
        Args:
            model1: 一级模型
            model2: 二级模型
            selected_features1: 一级模型使用的特征
            selected_features2: 二级模型使用的特征
            threshold1: 一级模型的决策阈值 (越低越敏感，提高SEN)
            threshold2: 二级模型的决策阈值 (越高越特异，提高SPE)
            scaler1: 一级模型的缩放器
            scaler2: 二级模型的缩放器
        """
        self.model1 = model1
        self.model2 = model2
        self.selected_features1 = selected_features1
        self.selected_features2 = selected_features2
        self.threshold1 = threshold1
        self.threshold2 = threshold2
        self.scaler1 = scaler1
        self.scaler2 = scaler2
        self.classes_ = np.array([0, 1])  # 兼容scikit-learn接口

        # 为了兼容原有加载预测代码，暴露一级模型的特征
        self.selected_features = selected_features1

    def predict(self, X):
        """
        使用双层级模型进行预测
        Args:
            X: 特征矩阵
        Returns:
            预测结果: 0(阴性)或1(阳性)
        """
        # 获取概率
        probas = self.predict_proba(X)
        # 基于概率和阈值做最终决策
        return (probas[:, 1] > 0.5).astype(int)  # 使用默认0.5阈值以兼容原始代码

    def predict_with_thresholds(self, X):
        """
        使用自定义阈值进行预测
        Args:
            X: 特征矩阵
        Returns:
            预测结果: 0(阴性)或1(阳性)
        """
        # 第一级预测概率
        level1_proba = self.model1.predict_proba(X)

        # 使用自定义阈值决定哪些样本进入第二级
        first_level_positives = level1_proba[:, 1] > self.threshold1

        # 初始化为全阴性
        final_predictions = np.zeros(X.shape[0], dtype=int)

        # 对第一级预测为阳性的样本进行第二级预测
        if np.any(first_level_positives):
            X_level2 = X[first_level_positives]
            level2_proba = self.model2.predict_proba(X_level2)

            # 使用第二级阈值做决策
            second_level_predictions = (level2_proba[:, 1] > self.threshold2).astype(int)

            # 更新最终结果
            final_predictions[first_level_positives] = second_level_predictions

        return final_predictions

    def predict_proba(self, X):
        """
        预测样本属于各个类别的概率
        Args:
            X: 特征矩阵
        Returns:
            概率矩阵: [[p(阴性), p(阳性)], ...]
        """
        # 第一级预测概率
        level1_proba = self.model1.predict_proba(X)

        # 使用自定义阈值决定哪些样本进入第二级
        first_level_positives = level1_proba[:, 1] > self.threshold1

        # 初始化最终概率，默认使用第一级结果
        final_proba = level1_proba.copy()

        # 对第一级预测为阳性的样本进行第二级预测
        if np.any(first_level_positives):
            X_level2 = X[first_level_positives]
            level2_proba = self.model2.predict_proba(X_level2)

            # 整合两级模型的概率
            # 方法1: 直接使用第二级模型的概率替换第一级
            final_proba[first_level_positives] = level2_proba

        return final_proba

    def get_params(self, deep=True):
        """兼容sklearn接口"""
        return {
            "model1": self.model1,
            "model2": self.model2,
            "selected_features1": self.selected_features1,
            "selected_features2": self.selected_features2,
            "threshold1": self.threshold1,
            "threshold2": self.threshold2,
            "scaler1": self.scaler1,
            "scaler2": self.scaler2
        }

    def set_params(self, **parameters):
        """兼容sklearn接口"""
        for parameter, value in parameters.items():
            setattr(self, parameter, value)
        return self
