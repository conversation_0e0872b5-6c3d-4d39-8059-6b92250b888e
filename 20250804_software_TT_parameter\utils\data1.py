import numpy as np

# from time_loc import removenull, drawqhsrrp, denoisemw, maindl, statedges, tuningedges, statrmsmi, statpeaks, statwaves
from utils.Gettimes0 import gettimes

class Data:
    def __init__(self, path: str):
        self.path = path
        self.data = None
        self.rms = None
        self.time_loc = None

    def read_data(self):
        if self.data is not None:
            return
        self.data = np.loadtxt(self.path, skiprows=0, delimiter='\t').T[1:]
        self.rms = np.sum(self.data * self.data / self.data.shape[0], axis=0)
        return

    def read_time(self):
        if self.time_loc is not None:
            return
        mcg_file = self.path
        Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettimes(mcg_file)
        self.time_loc = {'Ph': Ph, 'Pp': Pp, 'Pr': Pr, 'Qh': Qh,
                         'Qp': Qp, 'Rp': Rp, 'Sp': Sp, 'Sr': Sr,
                         'Th': Th, 'To': To, 'Tp': Tp, 'Te': Te,
                        #  'Tr': Tr, 'cnt': len(xss), }
                         'Tr': Tr}
        return

    def get(self):
        if self.data is None:
            self.read_data()
        if self.time_loc is None:
            self.read_time()
        return self.data, self.time_loc, self.rms

    def draw():
        return 