from index import *

def generate_ci_features_with_names(txt_file_path):
    """
    生成ci特征，返回带正确特征名的字典
    Args:
        txt_file_path: txt文件路径
    Returns:
        dict: 包含所有ci特征的字典，特征名为ci_前缀+原始特征名
    """
    try:
        # 使用与main.py相同的逻辑
        mess_path = './messages'  # 注意这里使用相对路径
        matrixes = cal_interpolate(txt_file_path)
        tms = []  # 空列表，自动计算时刻点
        txs = []  # 空列表，自动计算时刻点名称

        # 生成完整的特征字典
        mtss = gn_mts0(txt_file_path, matrixes, mess_path, tms, txs)

        # 转换为带ci_前缀的特征字典
        ci_features = {}
        for key, value in mtss.items():
            ci_features[f'ci_{key}'] = value

        return ci_features

    except Exception as e:
        print(f"生成ci特征时出错: {e}")
        import traceback
        traceback.print_exc()
        return {}

if __name__ == '__main__':
    # ### *.参数生成的时间加速: 471+2
    # metr_path = './metrics'
    # data_path = './datasets'
    mess_path = './cb/messages'
    mfmfile = './BJ_TT_001100.txt'
    matrixes=cal_interpolate(mfmfile)
    tms = []# [282, 303, 325, 486, 545, 609]
    txs = [] #['Qp', 'Rp', 'Sp', 'To', 'Tp', 'Te']

    mtss = gn_mts0(mfmfile, matrixes, mess_path, tms, txs)
    l2 = list(mtss.values())[:471] # 这里是ci特征
    # 如果需要保存下来
    # write_str(str(l2), './BJ_TT_001100_mts.txt') # 保存下来 结果1 str
    # write_str(str(mtss), './BJ_TT_001100_mtdc.txt') # 保存下来 结果2 字典

