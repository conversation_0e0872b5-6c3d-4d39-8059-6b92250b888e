"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: dataAug
date: 2025/1/22 下午3:48
desc: 
"""
import warnings
from typing import Dict, List, Tuple
from typing import Union

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import xgboost as xgb
from lightgbm import LGBMClassifier
from sklearn.datasets import make_classification
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold
from sklearn.model_selection import cross_validate  # 添加import
from sklearn.neighbors import NearestNeighbors

warnings.filterwarnings('ignore')

class DataAugmentor:
    """
    高级数据增强工具类，支持多种数据增强策略
    """

    def __init__(
            self,
            augmentation_methods: List[str] = None,
            random_state: int = 42,
            verbose: bool = True
    ):
        """
        初始化数据增强器

        Args:
            augmentation_methods: 要使用的增强方法列表
            random_state: 随机种子
            verbose: 是否打印详细信息
        """
        self.random_state = random_state
        self.verbose = verbose
        np.random.seed(random_state)

        # 默认的增强方法
        self.available_methods = {
            # 基于采样的方法
            'smote': self._smote_augment,
            'adasyn': self._adasyn_augment,
            'borderline_smote': self._borderline_smote,
            'random_oversampling': self._random_oversample,
            'smote-tomek': self._smote_tomek,
            # 基于混合的方法
            'mixup': self._mixup_augment,
            'rsp': self._random_sample_pairing,
            'semantic_mixing': self._semantic_mixing,

            # 基于噪声的方法
            'gaussian_noise': self._gaussian_noise_augment,
            'random_perturbation': self._random_perturbation,

            # 基于特征的方法
            'feature_shuffle': self._feature_shuffle,

            # 基于邻域的方法
            'knn_based': self._knn_based_augment,
            'cluster_based': self._cluster_based_augment,
        }

        self.active_methods = (augmentation_methods if augmentation_methods
                               else ['smote',])

        # 方法特定的参数
        self.method_params = {
            'smote': {'k_neighbors': 5, 'sampling_strategy': 'auto'},
            'mixup': {'alpha': 0.2},
            'gaussian_noise': {'scale': 0.1},
            'knn_based': {'k_neighbors': 5},
            'borderline_smote': {
                'sampling_strategy': 'auto',
                'k_neighbors': 5,
                'kind': 'borderline-1'
            },
            'random_oversampling': {
                'sampling_strategy': 'auto'
            },
            'adasyn':{
                'sampling_strategy': 'auto',
                'k_neighbors': 5,
            }
        }

    def _smote_tomek(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        SMOTE-Tomek 过采样
        结合SMOTE和Tomek链接的过采样方法
        """
        try:
            from imblearn.combine import SMOTETomek
            from imblearn.over_sampling import SMOTE
            from imblearn.under_sampling import TomekLinks
            from imblearn.over_sampling import BorderlineSMOTE

            params = self.method_params.get('smote-tomek', {
                'sampling_strategy': 'auto',
                'smote': {'k_neighbors': 5},
                'tomek': {'sampling_strategy': 'auto'}
            })

            sampler = SMOTETomek(
                sampling_strategy=params['sampling_strategy'],
                smote=SMOTE(),
                tomek=TomekLinks(
                    sampling_strategy=params['tomek']['sampling_strategy']
                )
            )
            X_aug, y_aug = sampler.fit_resample(X, y)

            if self.verbose:
                print(f"SMOTE-Tomek: Generated {len(X_aug) - len(X)} new samples")

            return X_aug, y_aug
        except Exception as e:
            if self.verbose:
                print(f"SMOTE-Tomek augmentation failed: {str(e)}")
            return X, y
    def set_params(self, method: str, **params):
        """设置特定方法的参数"""
        if method in self.available_methods:
            self.method_params[method].update(params)
        else:
            raise ValueError(f"Unknown method: {method}")

    def _check_input(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """验证和预处理输入数据"""
        if isinstance(X, pd.DataFrame):
            X = X.values
        if isinstance(y, pd.Series):
            y = y.values

        if len(X) != len(y):
            raise ValueError("X and y must have the same length")

        return X, y

    def _adasyn_augment(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        ADASYN (Adaptive Synthetic) 采样
        为难分类的样本生成更多的合成样本
        """
        try:
            from imblearn.over_sampling import ADASYN
            params = self.method_params.get('adasyn', {
                'sampling_strategy': 'auto',
                'k_neighbors': 5,
            })

            sampler = ADASYN(
                sampling_strategy=params['sampling_strategy'],
                n_neighbors=params['k_neighbors'],
                random_state=self.random_state
            )
            X_aug, y_aug = sampler.fit_resample(X, y)

            if self.verbose:
                print(f"ADASYN: Generated {len(X_aug) - len(X)} new samples")

            return X_aug, y_aug
        except Exception as e:
            if self.verbose:
                print(f"ADASYN augmentation failed: {str(e)}")
            return X, y

    def _borderline_smote(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Borderline SMOTE
        重点关注类别边界附近的样本
        """
        try:
            from imblearn.over_sampling import BorderlineSMOTE
            params = self.method_params.get('borderline_smote', {})

            print(params)
            sampler = BorderlineSMOTE(
                sampling_strategy=params['sampling_strategy'],
                k_neighbors=params['k_neighbors'],
                kind=params['kind'],
                random_state=self.random_state
            )
            X_aug, y_aug = sampler.fit_resample(X, y)

            if self.verbose:
                print(f"Borderline SMOTE: Generated {len(X_aug) - len(X)} new samples")

            return X_aug, y_aug
        except Exception as e:
            if self.verbose:
                print(f"Borderline SMOTE augmentation failed: {str(e)}")
            return X, y

    def _knn_based_augment(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于KNN的数据增强
        在近邻样本之间生成新样本
        """
        try:
            params = self.method_params.get('knn_based', {
                'k_neighbors': 5,
                'samples_per_class': 100
            })

            X_new, y_new = [], []

            # 对每个类别分别处理
            for label in np.unique(y):
                mask = y == label
                X_class = X[mask]

                if len(X_class) < params['k_neighbors']:
                    continue

                # 找到k近邻
                nn = NearestNeighbors(n_neighbors=params['k_neighbors'])
                nn.fit(X_class)
                distances, indices = nn.kneighbors(X_class)

                # 生成新样本
                for _ in range(params['samples_per_class']):
                    idx = np.random.randint(len(X_class))
                    neighbor_idx = np.random.choice(indices[idx][1:])  # 排除自身

                    # 在两个样本之间随机插值
                    alpha = np.random.random()
                    new_sample = X_class[idx] * alpha + X_class[neighbor_idx] * (1 - alpha)

                    X_new.append(new_sample)
                    y_new.append(label)

            if len(X_new) > 0:
                return (np.vstack([X, np.array(X_new)]),
                        np.concatenate([y, np.array(y_new)]))
            return X, y

        except Exception as e:
            if self.verbose:
                print(f"KNN-based augmentation failed: {str(e)}")
            return X, y

    def _cluster_based_augment(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于聚类的数据增强
        在同一簇内生成新样本
        """
        try:
            from sklearn.cluster import KMeans
            params = self.method_params.get('cluster_based', {
                'n_clusters': 5,
                'samples_per_cluster': 20
            })

            X_new, y_new = [], []

            # 对每个类别分别进行聚类
            for label in np.unique(y):
                mask = y == label
                X_class = X[mask]

                if len(X_class) < params['n_clusters']:
                    continue

                # 聚类
                kmeans = KMeans(
                    n_clusters=min(params['n_clusters'], len(X_class)),
                    random_state=self.random_state
                )
                cluster_labels = kmeans.fit_predict(X_class)

                # 在每个簇内生成新样本
                for cluster_idx in range(kmeans.n_clusters):
                    cluster_mask = cluster_labels == cluster_idx
                    X_cluster = X_class[cluster_mask]

                    if len(X_cluster) < 2:
                        continue

                    # 计算簇的统计特征
                    mean = np.mean(X_cluster, axis=0)
                    std = np.std(X_cluster, axis=0)

                    # 生成新样本
                    for _ in range(params['samples_per_cluster']):
                        # 使用高斯分布生成新样本
                        new_sample = np.random.normal(mean, std)
                        X_new.append(new_sample)
                        y_new.append(label)

            if len(X_new) > 0:
                return (np.vstack([X, np.array(X_new)]),
                        np.concatenate([y, np.array(y_new)]))
            return X, y

        except Exception as e:
            if self.verbose:
                print(f"Cluster-based augmentation failed: {str(e)}")
            return X, y

    def _random_perturbation(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        随机扰动增强
        对样本进行随机特征扰动
        """
        try:
            params = self.method_params.get('random_perturbation', {
                'perturbation_ratio': 0.05,  # 扰动强度
                'n_samples': len(X)  # 生成的新样本数量
            })

            # 计算每个特征的标准差
            feature_std = np.std(X, axis=0)
            # 生成扰动
            noise = np.random.randn(params['n_samples'], X.shape[1]) * \
                    feature_std * params['perturbation_ratio']

            # 添加扰动生成新样本
            X_perturbed = X + noise

            return np.vstack([X, X_perturbed]), np.concatenate([y, y])

        except Exception as e:
            if self.verbose:
                print(f"Random perturbation failed: {str(e)}")
            return X, y

    def _random_oversample(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        随机过采样
        简单复制少数类样本
        """
        try:
            from imblearn.over_sampling import RandomOverSampler
            params = self.method_params.get('random_oversampling', {
                'sampling_strategy': 'auto'
            })

            sampler = RandomOverSampler(
                sampling_strategy=params['sampling_strategy'],
                random_state=self.random_state
            )
            X_aug, y_aug = sampler.fit_resample(X, y)

            if self.verbose:
                print(f"Random Oversampling: Generated {len(X_aug) - len(X)} new samples")

            return X_aug, y_aug

        except Exception as e:
            if self.verbose:
                print(f"Random oversampling failed: {str(e)}")
            return X, y

    def _smote_augment(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """SMOTE过采样"""
        try:
            from imblearn.over_sampling import SMOTE
            params = self.method_params['smote']
            sampler = SMOTE(
                sampling_strategy=params['sampling_strategy'],
                k_neighbors=params['k_neighbors'],
                random_state=self.random_state
            )
            X_aug, y_aug = sampler.fit_resample(X, y)
            return X_aug, y_aug
        except Exception as e:
            if self.verbose:
                print(f"SMOTE augmentation failed: {str(e)}")
            return X, y

    def _mixup_augment(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Mixup增强"""
        alpha = self.method_params['mixup']['alpha']
        if alpha <= 0:
            return X, y

        batch_size = len(X)
        weights = np.random.beta(alpha, alpha, batch_size)
        index = np.random.permutation(batch_size)

        X_mixed = (weights.reshape(-1, 1) * X +
                   (1 - weights.reshape(-1, 1)) * X[index])
        y_mixed = (weights * y + (1 - weights) * y[index]).astype(y.dtype)

        return X_mixed, y_mixed

    def _random_sample_pairing(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """随机样本配对"""
        index = np.random.permutation(len(X))
        X_paired = (X + X[index]) / 2
        return np.vstack([X, X_paired]), np.concatenate([y, y])

    def _semantic_mixing(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """语义保持的混合"""
        nn = NearestNeighbors(n_neighbors=5)
        nn.fit(X)

        X_new, y_new = [], []
        for label in np.unique(y):
            mask = y == label
            X_class = X[mask]

            if len(X_class) < 5:
                continue

            distances, indices = nn.kneighbors(X_class)
            weights = np.exp(-distances) / np.sum(np.exp(-distances), axis=1, keepdims=True)

            X_semantic = np.sum(X_class[indices] * weights[:, :, np.newaxis], axis=1)
            X_new.append(X_semantic)
            y_new.extend([label] * len(X_semantic))

        return (np.vstack([X, np.vstack(X_new)]),
                np.concatenate([y, np.array(y_new)]))

    def _gaussian_noise_augment(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """高斯噪声增强"""
        scale = self.method_params['gaussian_noise']['scale']
        noise = np.random.normal(0, scale, X.shape)
        X_noisy = X + noise
        return np.vstack([X, X_noisy]), np.concatenate([y, y])

    def _feature_shuffle(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """特征随机打乱"""
        X_shuffled = X.copy()
        for i in range(X.shape[1]):
            np.random.shuffle(X_shuffled[:, i])
        return np.vstack([X, X_shuffled]), np.concatenate([y, y])

    def preprocess_data(
            self,
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series],
            methods: List[str] = None,
            sample_ratio: float = 0.5,
            cumulative: bool = True
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        应用选定的数据增强方法

        Args:
            X: 特征矩阵
            y: 标签向量
            methods: 要使用的增强方法列表，如果为None则使用初始化时指定的方法
            sample_ratio: 每种方法产生的新样本占原始样本的比例
            cumulative: 是否采用累积模式，即每个方法基于前一个方法的结果进行操作

        Returns:
            tuple: (增强后的特征矩阵, 增强后的标签向量)
        """
        X, y = self._check_input(X, y)
        methods = methods or self.active_methods

        if cumulative:
            return self._preprocess_cumulative(X, y, methods, sample_ratio)
        else:
            return self._preprocess_independent(X, y, methods, sample_ratio)

    def _preprocess_independent(
            self,
            X: np.ndarray,
            y: np.ndarray,
            methods: List[str],
            sample_ratio: float
    ) -> Tuple[np.ndarray, np.ndarray]:
        """独立模式：每个方法基于原始数据生成新样本"""

        # 计算每个方法需要生成的样本数
        n_samples = int(len(X) * sample_ratio)

        # 初始化增强后的数据集为原始数据
        X_augmented, y_augmented = X.copy(), y.copy()

        if self.verbose:
            print(f"Independent mode - Target samples per method: {n_samples}")

        for method in methods:
            if method not in self.available_methods:
                warnings.warn(f"Unknown method {method}, skipping...")
                continue

            if self.verbose:
                print(f"\nApplying {method}...")

            try:
                # 生成新样本
                X_new, y_new = self.available_methods[method](X, y)

                # 获取新生成的样本（排除原始样本）
                X_new_only = X_new[len(X):]
                y_new_only = y_new[len(y):]

                if len(X_new_only) == 0:
                    warnings.warn(f"Method {method} didn't generate any new samples")
                    continue

                if len(X_new_only) <= n_samples:
                    # 如果生成的样本数少于目标数量，全部使用
                    X_to_add = X_new_only
                    y_to_add = y_new_only
                    if self.verbose:
                        print(f"Using all {len(X_new_only)} samples generated by {method}")
                else:
                    # 如果生成的样本数超过目标数量，随机选择
                    indices = np.random.choice(
                        len(X_new_only),
                        n_samples,
                        replace=False
                    )
                    X_to_add = X_new_only[indices]
                    y_to_add = y_new_only[indices]
                    if self.verbose:
                        print(f"Randomly selected {n_samples} from {len(X_new_only)} samples generated by {method}")

                # 添加选定的样本
                X_augmented = np.vstack([X_augmented, X_to_add])
                y_augmented = np.concatenate([y_augmented, y_to_add])

                if self.verbose:
                    print(f"Added {len(X_to_add)} samples using {method}")

            except Exception as e:
                warnings.warn(f"Error applying {method}: {str(e)}")
                continue

        return X_augmented, y_augmented

    def _preprocess_cumulative(
            self,
            X: np.ndarray,
            y: np.ndarray,
            methods: List[str],
            sample_ratio: float
    ) -> Tuple[np.ndarray, np.ndarray]:
        """累积模式：每个方法基于前一个方法的结果生成新样本"""

        # 初始数据集大小
        original_size = len(X)

        # 当前工作数据集
        X_current, y_current = X.copy(), y.copy()

        if self.verbose:
            print(f"Cumulative mode - Initial samples: {len(X_current)}")

        for method in methods:
            if method not in self.available_methods:
                warnings.warn(f"Unknown method {method}, skipping...")
                continue

            if self.verbose:
                print(f"\nApplying {method}...")

            try:
                # 基于当前数据集生成新样本
                X_new, y_new = self.available_methods[method](X_current, y_current)

                # 获取新生成的样本（排除当前数据集）
                X_new_only = X_new[len(X_current):]
                y_new_only = y_new[len(y_current):]

                if len(X_new_only) == 0:
                    warnings.warn(f"Method {method} didn't generate any new samples")
                    continue

                # 计算这个方法要添加的样本数（基于原始数据集大小）
                n_samples = int(original_size * sample_ratio)

                if len(X_new_only) <= n_samples:
                    # 如果生成的样本数少于目标数量，全部使用
                    X_to_add = X_new_only
                    y_to_add = y_new_only
                    if self.verbose:
                        print(f"Using all {len(X_new_only)} samples generated by {method}")
                else:
                    # 如果生成的样本数超过目标数量，随机选择
                    indices = np.random.choice(
                        len(X_new_only),
                        n_samples,
                        replace=False
                    )
                    X_to_add = X_new_only[indices]
                    y_to_add = y_new_only[indices]
                    if self.verbose:
                        print(f"Randomly selected {n_samples} from {len(X_new_only)} samples generated by {method}")

                # 更新当前工作数据集
                X_current = np.vstack([X_current, X_to_add])
                y_current = np.concatenate([y_current, y_to_add])

                if self.verbose:
                    print(f"Added {len(X_to_add)} samples using {method}")
                    print(f"Current dataset size: {len(X_current)}")

            except Exception as e:
                warnings.warn(f"Error applying {method}: {str(e)}")
                continue

        if self.verbose:
            print(f"\nFinal results:")
            print(f"Original samples: {original_size}")
            print(f"Augmented samples: {len(X_current)}")
            print("Class distribution:")
            print(pd.Series(y_current).value_counts())

        return X_current, y_current
    def get_available_methods(self) -> List[str]:
        """获取所有可用的增强方法"""
        return list(self.available_methods.keys())

    def get_method_params(self, method: str) -> Dict:
        """获取特定方法的当前参数"""
        return self.method_params.get(method, {})


import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union
from collections import Counter, defaultdict


class StratifiedBalancedDataAugmentor(DataAugmentor):
    """
    分层平衡的数据增强器
    支持按中心（医院）分组进行平衡，并提供详细的分布统计
    """

    def __init__(
            self,
            balance_method: str = 'smote',
            noise_method: str = 'random_perturbation',
            random_state: int = 42,
            verbose: bool = True
    ):
        """
        初始化分层平衡数据增强器

        Args:
            balance_method: 用于平衡的方法，默认为'smote'
            noise_method: 用于噪声增强的方法，默认为'random_perturbation'
            random_state: 随机种子
            verbose: 是否打印详细信息
        """
        super().__init__(
            augmentation_methods=[balance_method, noise_method],
            random_state=random_state,
            verbose=verbose
        )

        self.balance_method = balance_method
        self.noise_method = noise_method

        # 设置保守的噪声参数
        if noise_method == 'random_perturbation':
            self.method_params['random_perturbation'] = {
                'perturbation_ratio': 0.02,
                'n_samples': None  # 将在运行时动态设置
            }
    @staticmethod
    def get_stratified_stats(
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series],
            selected_features: List[str],
            center_col: Union[str, int]
    ) -> Dict:
        """
        获取按中心分层的类别分布统计

        Args:
            X: 特征矩阵
            y: 标签向量
            center_col: 中心（医院）列名或索引。如果X是DataFrame则为列名，如果是ndarray则为索引
        """

        # 如果是numpy array，先转换为DataFrame
        if isinstance(X, np.ndarray):
            X = pd.DataFrame(X, columns=selected_features)
        # 确保y也是DataFrame或Series，且索引与X一致
        if not isinstance(y, pd.Series):
            y = pd.Series(y, index=X.index)

        print("X shape:", X.shape)
        print("y shape:", y.shape if hasattr(y, 'shape') else len(y))
        print("X index:", X.index)
        print("y index:", y.index if hasattr(y, 'index') else "No index")

        centers = X[center_col]

        stats = {
            'global': {
                'total': len(y),
                'class_dist': Counter(y)
            },
            'by_center': defaultdict(dict)
        }

        # 按中心统计
        for center in np.unique(centers):
            # 使用布尔索引前确保索引一致
            mask = (centers == center).values  # 使用.values获取numpy数组
            center_y = y.iloc[mask] if isinstance(y, pd.Series) else y[mask]

            stats['by_center'][center] = {
                'total': len(center_y),
                'class_dist': Counter(center_y),
                'imbalance_ratio': max(Counter(center_y).values()) / min(Counter(center_y).values())
            }

        return stats

    def _random_perturbation(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        修复的随机扰动方法
        确保新样本数量计算正确
        """
        try:
            params = self.method_params.get('random_perturbation', {
                'perturbation_ratio': 0.2
            })

            # 使用指定的n_samples或默认生成与输入相同数量的样本
            n_samples = params.get('n_samples', len(X))

            # 计算每个特征的标准差
            feature_std = np.std(X, axis=0)

            # 生成扰动样本
            noise = np.random.randn(n_samples, X.shape[1]) * \
                    feature_std * params['perturbation_ratio']

            # 从原始样本中随机选择基础样本
            base_indices = np.random.choice(len(X), n_samples, replace=True)
            X_base = X[base_indices]
            y_base = y[base_indices]

            # 添加扰动生成新样本
            X_perturbed = X_base + noise

            if self.verbose:
                print(f"Generated {n_samples} perturbed samples")

            return np.vstack([X, X_perturbed]), np.concatenate([y, y_base])

        except Exception as e:
            if self.verbose:
                print(f"Random perturbation failed: {str(e)}")
            return X, y

    def balance_by_center(
            self,
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series],
            selected_features: List[str],
            center_col: Union[str, int],
            target_ratio: Union[float, List[float]] = 1.0,
            noise_ratio: float = 0.2
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        按中心分组进行数据平衡和增强

        Args:
            X: 特征矩阵
            y: 标签向量
            center_col: 中心（医院）列名或索引
            target_ratio: 目标平衡比例，可以是单个float值或者list。
                         如果是list，则按center从小到大的顺序对应不同中心的比例
            noise_ratio: 噪声增强比例

        Returns:
            tuple: (增强后的特征矩阵, 增强后的标签向量)
        """
        X, y = self._check_input(X, y)

        # 按医院分层时需要有中心列
        if isinstance(X, np.ndarray):
            X = pd.DataFrame(X, columns=selected_features)

        # 获取初始统计信息
        if self.verbose:
            initial_stats = self.get_stratified_stats(X, y, selected_features, center_col)
            print("\nInitial distribution by center:")

            for center, stats in initial_stats['by_center'].items():
                print(f"\nCenter {center}:")
                print(f"Total samples: {stats['total']}")
                print("Class distribution:", stats['class_dist'])
                print(f"Imbalance ratio: {stats['imbalance_ratio']:.2f}")

        # 转换为DataFrame以便处理
        if isinstance(X, np.ndarray):
            X_df = pd.DataFrame(X)
            center_values = X[:, center_col]
        else:
            X_df = X.copy()
            center_values = X[center_col]

        # 获取所有中心并排序
        unique_centers = np.sort(np.unique(center_values))

        # 如果target_ratio是list，检查长度是否匹配
        if isinstance(target_ratio, list):
            if len(target_ratio) != len(unique_centers):
                raise ValueError(f"target_ratio list length ({len(target_ratio)}) "
                                 f"does not match number of centers ({len(unique_centers)})")
            # 创建中心到ratio的映射
            center_ratio_map = dict(zip(unique_centers, target_ratio))
        else:
            # 如果是单个值，创建统一的映射
            center_ratio_map = {center: target_ratio for center in unique_centers}

        # 按中心分别处理
        X_balanced_list = []
        y_balanced_list = []
        center_name = ['上海中山', '上海六院',  '上海十院','北京301', '安医', '广东南海']
        cindex = 0
        for center in unique_centers:
            if self.verbose:
                print(f"\nProcessing center {center_name[cindex]}...")
                print(f"Using target ratio: {center_ratio_map[center]}")

            cindex += 1
            # 获取当前中心的数据
            center_mask = center_values == center
            X_center = X[center_mask]
            y_center = y[center_mask]

            # 获取当前中心的类别分布
            class_counts = Counter(y_center)
            max_count = max(class_counts.values())
            current_target_ratio = center_ratio_map[center]
            target_samples = int(max_count * current_target_ratio)

            # 设置采样策略
            sampling_strategy = {
                label: target_samples
                for label, count in class_counts.items()
                if count < target_samples
            }

            # 获取当前方法的默认参数
            current_params = self.method_params.get(self.balance_method, {}).copy()

            # 只更新通用参数
            current_params['sampling_strategy'] = sampling_strategy
            if 'k_neighbors' in current_params:
                current_params['k_neighbors'] = min(5, min(class_counts.values()) - 1)

            # 更新参数
            self.method_params[self.balance_method] = current_params

            # 应用平衡方法
            X_center_balanced, y_center_balanced = self.available_methods[self.balance_method](
                X_center, y_center
            )

            if self.verbose:
                print(f"Center {center} balanced class distribution:")
                print(pd.Series(Counter(y_center_balanced)))

            X_balanced_list.append(X_center_balanced)
            y_balanced_list.append(y_center_balanced)

        # 合并所有中心的平衡后数据
        X_balanced = np.vstack(X_balanced_list)
        y_balanced = np.concatenate(y_balanced_list)

        if self.verbose:
            print("\nOverall balanced distribution:")
            print(pd.Series(Counter(y_balanced)))

        # 应用噪声增强
        if noise_ratio > 0:
            self.method_params[self.noise_method]['n_samples'] = int(len(X_balanced) * noise_ratio)
            X_augmented, y_augmented = self._random_perturbation(X_balanced, y_balanced)

            if self.verbose:
                print(f"\nAdded {len(X_augmented) - len(X_balanced)} noise-augmented samples")
                print("Final class distribution:")
                print(pd.Series(Counter(y_augmented)))

            return X_augmented, y_augmented

        return X_balanced, y_balanced
    def balance_and_augment(
            self,
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series],
            target_ratio: float = 1.0,
            noise_ratio: float = 0.2
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        全局平衡和增强（不考虑中心）

        Args:
            X: 特征矩阵
            y: 标签向量
            target_ratio: 目标平衡比例
            noise_ratio: 噪声增强比例

        Returns:
            tuple: (增强后的特征矩阵, 增强后的标签向量)
        """
        X, y = self._check_input(X, y)

        # 分析类别分布
        class_counts = Counter(y)
        max_class_count = max(class_counts.values())

        if self.verbose:
            print("Original class distribution:")
            print(pd.Series(class_counts))

        # 设置采样策略
        target_samples = int(max_class_count * target_ratio)
        sampling_strategy = {
            label: target_samples
            for label, count in class_counts.items()
            if count < target_samples
        }

        # 获取当前方法的默认参数
        current_params = self.method_params.get(self.balance_method, {}).copy()

        # 只更新通用参数
        current_params['sampling_strategy'] = sampling_strategy
        if 'k_neighbors' in current_params:
            current_params['k_neighbors'] = min(5, min(class_counts.values()) - 1)

        # 更新参数
        self.method_params[self.balance_method] = current_params

        # 应用平衡方法
        X_balanced, y_balanced = self.available_methods[self.balance_method](X, y)

        if self.verbose:
            print("\nBalanced class distribution:")
            print(pd.Series(Counter(y_balanced)))

        # 应用噪声增强
        if noise_ratio > 0:
            self.method_params[self.noise_method]['n_samples'] = int(len(X_balanced) * noise_ratio)
            X_augmented, y_augmented = self._random_perturbation(X_balanced, y_balanced)

            if self.verbose:
                print(f"\nAdded {len(X_augmented) - len(X_balanced)} noise-augmented samples")
                print("Final class distribution:")
                print(pd.Series(Counter(y_augmented)))

            return X_augmented, y_augmented

        return X_balanced, y_balanced

class AugmentationExperiment:
    """
    数据增强方法的综合实验框架
    """

    def __init__(
            self,
            methods: List[str] = None,
            random_state: int = 42
    ):
        self.methods = methods or ['smote', 'adasyn', 'borderline_smote',
                                   'gaussian_noise', 'random_perturbation']
        self.random_state = random_state
        self.results = {}

        # 初始化模型
        self.models = {
            'RF': RandomForestClassifier(random_state=random_state),
            'XGB': xgb.XGBClassifier(random_state=random_state),
            'LGBM': LGBMClassifier(random_state=random_state)
        }

    def generate_datasets(
            self,
            n_samples: int = 1000,
            imbalance_ratios: List[float] = None,
            noise_levels: List[float] = None
    ) -> Dict:
        """
        生成不同难度和平衡度的数据集

        Args:
            n_samples: 样本数量
            imbalance_ratios: 不平衡比例列表
            noise_levels: 噪声水平列表
        """
        if imbalance_ratios is None:
            imbalance_ratios = [0.5, 0.2, 0.1, 0.05]  # 从平衡到严重不平衡
        if noise_levels is None:
            noise_levels = [0.0, 0.1, 0.2, 0.3]  # 从无噪声到高噪声

        datasets = {}

        for ratio in imbalance_ratios:
            for noise in noise_levels:
                # 生成数据集
                weights = [1 - ratio, ratio]  # 类别权重
                X, y = make_classification(
                    n_samples=n_samples,
                    n_classes=2,
                    weights=weights,
                    n_features=20,
                    n_informative=15,  # 信息特征数量
                    n_redundant=5,  # 冗余特征数量
                    flip_y=noise,  # 标签噪声水平
                    random_state=self.random_state
                )

                dataset_name = f'imbalance_{ratio:.2f}_noise_{noise:.1f}'
                datasets[dataset_name] = (X, y)

        return datasets

    def evaluate_dataset(
            self,
            X: np.ndarray,
            y: np.ndarray,
            dataset_name: str
    ) -> pd.DataFrame:
        """
        评估单个数据集上的所有方法
        确保只在训练集上进行数据增强
        """
        results = []
        cv = StratifiedKFold(5, shuffle=True, random_state=self.random_state)

        # 评估原始数据（不增强）
        print(f"\nEvaluating original data on {dataset_name}")
        for model_name, model in self.models.items():
            scores = cross_validate(
                model, X, y,
                cv=cv,
                scoring={
                    'accuracy': 'accuracy',
                    'f1': 'f1',
                    'precision': 'precision',
                    'recall': 'recall'
                }
            )

            results.append({
                'dataset': dataset_name,
                'method': 'original',
                'model': model_name,
                'accuracy': scores['test_accuracy'].mean(),
                'f1': scores['test_f1'].mean(),
                'precision': scores['test_precision'].mean(),
                'recall': scores['test_recall'].mean()
            })

        # 评估每种增强方法
        augmentor = DataAugmentor(verbose=False)

        for method in self.methods:
            print(f"Evaluating {method} on {dataset_name}")
            try:
                augmentor.active_methods = [method]

                # 存储每个fold的得分
                method_scores = {
                    'accuracy': [], 'f1': [],
                    'precision': [], 'recall': []
                }

                # 对每个fold单独进行数据增强
                for fold_idx, (train_idx, val_idx) in enumerate(cv.split(X, y)):
                    X_train, X_val = X[train_idx], X[val_idx]
                    y_train, y_val = y[train_idx], y[val_idx]

                    # 只对训练集进行增强
                    X_train_aug, y_train_aug = augmentor.preprocess_data(X_train, y_train)

                    # 对每个模型评估当前fold
                    for model_name, model in self.models.items():
                        # 训练模型
                        model.fit(X_train_aug, y_train_aug)

                        # 在验证集上预测
                        y_pred = model.predict(X_val)

                        # 计算各项指标
                        from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
                        fold_scores = {
                            'accuracy': accuracy_score(y_val, y_pred),
                            'f1': f1_score(y_val, y_pred),
                            'precision': precision_score(y_val, y_pred),
                            'recall': recall_score(y_val, y_pred)
                        }

                        # 存储当前fold的得分
                        if model_name not in method_scores:
                            method_scores[model_name] = {
                                'accuracy': [], 'f1': [],
                                'precision': [], 'recall': []
                            }

                        for metric, score in fold_scores.items():
                            method_scores[model_name][metric].append(score)

                # 计算每个模型的平均得分
                for model_name in self.models.keys():
                    results.append({
                        'dataset': dataset_name,
                        'method': method,
                        'model': model_name,
                        'accuracy': np.mean(method_scores[model_name]['accuracy']),
                        'f1': np.mean(method_scores[model_name]['f1']),
                        'precision': np.mean(method_scores[model_name]['precision']),
                        'recall': np.mean(method_scores[model_name]['recall'])
                    })

            except Exception as e:
                print(f"Error with {method} on {dataset_name}: {str(e)}")
                continue

        return pd.DataFrame(results)
    def run_experiment(self) -> pd.DataFrame:
        """
        运行完整实验
        """
        datasets = self.generate_datasets()
        all_results = []

        for dataset_name, (X, y) in datasets.items():
            results_df = self.evaluate_dataset(X, y, dataset_name)
            all_results.append(results_df)

        self.results = pd.concat(all_results, ignore_index=True)
        return self.results

    def visualize_results(self):
        """
        可视化实验结果
        """
        if self.results.empty:
            print("No results to visualize. Run experiment first.")
            return

        metrics = ['accuracy', 'f1', 'precision', 'recall']

        # 整体性能比较
        plt.figure(figsize=(20, 15))
        for i, metric in enumerate(metrics, 1):
            plt.subplot(2, 2, i)
            sns.boxplot(
                data=self.results,
                x='method',
                y=metric,
                hue='model'
            )
            plt.title(f'{metric.upper()} Score by Method and Model')
            plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

        # 不同不平衡度下的性能
        plt.figure(figsize=(15, 10))
        sns.boxplot(
            data=self.results,
            x='dataset',
            y='accuracy',
            hue='method'
        )
        plt.title('F1 Score by Dataset and Method')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

        # 生成统计摘要
        summary = self.results.groupby(['method', 'model'])[metrics].mean()
        print("\nPerformance Summary:")
        print(summary)

        # 最佳方法分析
        best_methods = self.results.groupby('dataset').apply(
            lambda x: x.loc[x['f1'].idxmax()]
        )[['dataset', 'method', 'model', 'f1']]
        print("\nBest Methods by Dataset:")
        print(best_methods)
# 使用示例
if __name__ == "__main__":
    # 创建示例数据
    np.random.seed(42)
    X = np.random.randn(500, 4)
    y = np.random.choice([0, 1], size=500)

    # 初始化增强器
    augmentor = DataAugmentor(
        augmentation_methods=['smote', 'gaussian_noise'],
        verbose=True
    )

    # 设置特定方法的参数
    augmentor.set_params('gaussian_noise', scale=0.2)

    # 应用数据增强
    X_aug, y_aug = augmentor.preprocess_data(X, y, sample_ratio=0.3)

    # 使用多种增强方法
    augmentor = DataAugmentor(
        augmentation_methods=[
            'smote',
            'adasyn',
            'borderline_smote',
            # 'random_oversampling',
            # 'cluster_based',
            # 'rsp',
            # 'semantic_mixing',
            'gaussian_noise',
            'random_perturbation',
            # 'feature_shuffle', # 特征不独立 不予使用
            # 'knn_based',
            # 'cluster_based'
        ]
    )

    # 应用增强
    X_aug, y_aug = augmentor.preprocess_data(X, y, sample_ratio=3)


    # 实验示例
    experiment = AugmentationExperiment(
        methods=['smote', 'adasyn', 'borderline_smote',
                 'gaussian_noise', 'random_perturbation']
    )

    # 运行实验
    results = experiment.run_experiment()

    # 可视化结果
    experiment.visualize_results(experiment)