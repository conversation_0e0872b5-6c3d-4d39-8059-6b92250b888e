'''
@Project ：ML_Pipline 
@File    ：main_feature_generator.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/8/19 下午2:24 
@Discribe：特征生成器，可满足单个文件的所有特征的提取。
'''

import inspect
import pickle
import time
from typing import List

import pandas as pd

from feature_generator.mcg_data_loader import McgDataLoader
from feature_generator.stat_features import StatFeatures
from feature_generator.time_image_features import TimeImageFeatures
from feature_generator.time_seq_features import TimeSeqFeatures
from feature_generator.utils.feature_rigist import FeatureRegistry, \
    feature_regis  # , FeatureRegistry_v2, feature_regis_v2

fg = FeatureRegistry()

# --- Mapping Dictionaries for Chinese Explanation ---

# (PREFIX_MAP_CH, SEGMENT_MAP_CH, SOURCE_MAP_CH remain largely the same, ensure completeness)
PREFIX_MAP_CH = {
    "STATS": "统计特征",
    "TSREGIONAL": "时序区域特征",
    "TSAGGR": "时序区域聚合特征",
    "MFMIMGTS" : "磁场图帧时序特征"
}

SEGMENT_MAP_CH = {
    "P": "P波段", "QRS": "QRS波段", "QR": "QR段", "RS": "RS段", "T": "T波段",
    "T_ONSET": "T波起始点", "T_END": "T波结束点", "Q": "Q波点", "R": "R波点", "S": "S波点",
    "QT": "QT间期", "RT": "RT段", "FULLWAVE": "全波形", "EEG": "脑电图风格区域组",
}

SOURCE_MAP_CH = {
    "CDM": "电流密度图", "MFM": "磁场图", "SIGNAL": "原始信号波形",
    "PEAK": "心电峰值点", "PEAKDURATION": "峰值点间期",
    "INTERPOLATEDSIGNAL": "插值后信号", "CHANNELENVELOPE": "多导联信号包络",
    "MATRIXBLOCK": "矩阵子块", "SIGNAL_WAVELET_DB4": "信号DB4小波分解",
}

MEASUREMENT_MAP_CH = {
    # STATS - CDM
    "MAX_MAGNITUDE": "最大幅值", "MAX_ANGLE": "最大幅值对应角度",
    "MAX_X_POSITION": "最大幅值对应X坐标", "MAX_Y_POSITION": "最大幅值对应Y坐标",
    "RELATIVE_DISPLACEMENT": "相对位移",
    "ABSOLUTE_MAGNITUDE_ORIGIN": "相对原点绝对幅值", # For CDM
    "ABSOLUTE_ANGLE_ORIGIN": "相对原点绝对角度",   # For CDM
    # STATS - MFM
    "MINMAX_RATIO": "最大最小值比率", "MINMAX_ANGLE": "最大最小磁极连线角度",
    "MINMAX_DISTANCE": "最大最小磁极连线距离",
    "MAX_X_POLE": "正磁极X坐标", "MAX_Y_POLE": "正磁极Y坐标",
    "MIN_X_POLE": "负磁极X坐标", "MIN_Y_POLE": "负磁极Y坐标",
    "POSITIVE_POLE_AREA": "正磁极面积", "NEGATIVE_POLE_AREA": "负磁极面积",
    "CENTER_MINMAX_X": "正负磁极中心X坐标", "CENTER_MINMAX_Y": "正负磁极中心Y坐标",
    "MAX_DISTANCE_CENTER_MINMAX_POINTS": "正负磁极中心点轨迹最大位移",
    "POSITIVE_POLE_RELATIVE_DISPLACEMENT": "正磁极相对位移",
    "NEGATIVE_POLE_RELATIVE_DISPLACEMENT": "负磁极相对位移",
    "MEAN_POSITIVE_AREA_TO_MEAN_NEGATIVE_AREA_RATIO": "平均正磁极面积与平均负磁极面积之比",
    "POSITIVE_POLE_ABS_MAGNITUDE_ORIGIN": "正磁极相对原点绝对幅值", # MFM specific
    "POSITIVE_POLE_ABS_ANGLE_ORIGIN": "正磁极相对原点绝对角度",     # MFM specific
    "NEGATIVE_POLE_ABS_MAGNITUDE_ORIGIN": "负磁极相对原点绝对幅值", # MFM specific
    "NEGATIVE_POLE_ABS_ANGLE_ORIGIN": "负磁极相对原点绝对角度",     # MFM specific
    "CENTER_MINMAX_POLE_ABS_MAGNITUDE_ORIGIN": "正负磁极中心点相对原点绝对幅值", # MFM specific
    "CENTER_MINMAX_POLE_ABS_ANGLE_ORIGIN": "正负磁极中心点相对原点绝对角度",     # MFM specific
    # STATS - SIGNAL (from get_advanced_features)
    "MAX_MIN_CHANNEL_RATIO": "导联最大最小值比率",
    "CHANNEL_AREA_TOTAL": "导联信号总面积", "CHANNEL_AREA_POS": "导联信号正面积",
    "CHANNEL_AREA_NEG": "导联信号负面积", "CHANNEL_AREA_POS_NEG_RATIO": "导联信号正负面积比",
    "HAS_POSITIVE_AREA": "是否存在正面积信号", "HAS_NEGATIVE_AREA": "是否存在负面积信号",
    # STATS - PEAK
    "LOCATION_INDEX": "位置索引", "LOCATION_INDEX_ONSET": "起始点位置索引", "LOCATION_INDEX_END": "结束点位置索引",
    "RATIO_VS_QTINTERVAL": "时长与QT间期之比",
    "EUCLIDEAN_DISTANCE_PEAKPOINTS": "峰值点间欧氏距离", "COSINE_SIMILARITY_PEAKPOINTS": "峰值点间余弦相似度",
    # STATS - CHANNELENVELOPE
    "MAX_MIN_COSINE_SIMILARITY": "最大最小包络余弦相似度",
    # STATS - Entropy (SIGNAL source)
    "SHANNON_ENTROPY": "香农熵", "GINI_COEFFICIENT": "基尼系数", "SVD_ENTROPY": "奇异值分解熵",
    # STATS - Frequency (SIGNAL_WAVELET_DB4 source)
    "CA_ENERGY": "cA近似分量能量", "CD1_ENERGY": "cD1细节分量能量", "CD2_ENERGY": "cD2细节分量能量", "CD3_ENERGY": "cD3细节分量能量", "CD4_ENERGY": "cD4细节分量能量",
    "CA_SKEWNESS": "cA近似分量偏度", "CD1_SKEWNESS": "cD1细节分量偏度", "CD2_SKEWNESS": "cD2细节分量偏度", "CD3_SKEWNESS": "cD3细节分量偏度", "CD4_SKEWNESS": "cD4细节分量偏度",
    "CA_KURTOSIS": "cA近似分量峰度", "CD1_KURTOSIS": "cD1细节分量峰度", "CD2_KURTOSIS": "cD2细节分量峰度", "CD3_KURTOSIS": "cD3细节分量峰度", "CD4_KURTOSIS": "cD4细节分量峰度",
    # STATS - EEG (SIGNAL source, HJORTH metric)
    "HJORTH_ACTIVITY": "Hjorth活动度", "HJORTH_MOBILITY": "Hjorth移动度", "HJORTH_COMPLEXITY": "Hjorth复杂度",

    # TSREGIONAL / TSAGGR - Spatial Stats (used as a "measurement" that forms a time series)
    # These are what `spatial_stat_key` or `spatial_stat_type_key` will be.
    "SPATIALMEAN": "空间平均值", "SPATIALSTD": "空间标准差",
    "SPATIALMAX": "空间最大值", "SPATIALMIN": "空间最小值",
    # For TSAGGR where the first part like "MEAN" or "MIN" comes from the spatial stat name.
    # We need to map these simple forms too, if the TSAGGR names are like TSAGGR_MEAN_...
    # The `parts[1]` in TSAGGR will be "MEAN", "STD", etc. These need mapping.
    "MEAN": "空间平均值", # Context: This "MEAN" is the spatial stat.
    "STD": "空间标准差",   # Context: This "STD" is the spatial stat.
    "MAX": "空间最大值",   # Context: This "MAX" is the spatial stat.
    "MIN": "空间最小值",   # Context: This "MIN" is the spatial stat.

    "MAXMCGVALRAW": "原始6x6帧最大MCG值",
    "MINMCGVALRAW": "原始6x6帧最小MCG值",
    "MEANMCGVALRAW": "原始6x6帧平均MCG值",
    "POINTSLARGERAW": "原始6x6帧正值点数",
    "POINTSSMALLRAW": "原始6x6帧负值点数",
    "POLARSTD": "归一化帧极坐标标准差",
    "GRADIENTMAGNITUDEMEAN": "归一化帧梯度幅值均值",  # Corrected typo from MAGNITUTE
    "GRADIENTSTD": "归一化帧梯度幅值标准差",
    "VORTICITYMEAN": "归一化帧涡度均值",
    "CURVATUREMEAN": "归一化帧曲率均值",
    "GRADIENTDIRECTIONENTROPY": "归一化帧梯度方向熵",
    "LOCALVARIANCEMEAN": "归一化帧局部方差均值",
    "LOCALVARIANCESTD": "归一化帧局部方差标准差",
    # Quantile-dependent current features
    "MAXCURRENTMAGNITUDE": "阈下电流最大幅值",
    "MAXCURRENTANGLE": "阈下电流最大角度",
    "MAXCURRENTXPOSITION": "阈下电流X坐标",
    "MAXCURRENTYPOSITION": "阈下电流Y坐标",
    # Pole center coordinates
    "POSCENTERX": "正极中心X坐标", "POSCENTERY": "正极中心Y坐标",
    "NEGCENTERX": "负极中心X坐标", "NEGCENTERY": "负极中心Y坐标",
    # Pole geometry (condensed set)
    "MCGS": "极子面积", "K": "极子伸长度", "ANGLE": "极子主方向角度",
    "VALUE": "极子阈值", "CIRCLERANGE": "极子圆形度",
    # Add other geometric bases if you kept more: MCGMAXLENGTH, MCGMINLENGTH etc.
    "MCGMAXLENGTH": "极子最大长度", "MCGMINLENGTH": "极子最小长度",
    "MCGPERIMETER": "极子周长", "INNERCIRCLEX": "内切圆X坐标",
    # ... and so on for all in _get_default_zero_features_for_pole_static
}

# Statistics / Qualifiers
STATISTIC_MAP_CH = {
    "MEAN": "均值", "STD": "标准差", "MEDIAN": "中位数", "SUM": "总和",
    "MIN": "最小值", "MAX": "最大值", "SKEW": "偏度", "KURT": "峰度",
    "P10": "10百分位数", "P90": "90百分位数", "RANGE": "范围", "VALUE": "值",
    "FLAG": "标记", "MAX_VALUE_CHANNEL_IDX": "最大值所在导联索引", "MIN_VALUE_CHANNEL_IDX": "最小值所在导联索引",
    "AT_RPEAK": "R波峰值点处", "AT_TPEAK": "T波峰值点处", "AT_QPEAK": "Q波峰值点处", "AT_SPEAK": "S波峰值点处",

    # Temporal Stats (for TSREGIONAL/TSAGGR) - These are what `temporal_stat_key` or `temporal_stat_type_key` will be.
    "TREND_SLOPE": "趋势斜率", # Combined "TREND" and "SLOPE"
    "AUTOCORR_LAG1": "一阶自相关系数",
    "SAMPLE_ENTROPY": "样本熵",
    "NUM_PEAKS_N3": "显著峰数量(n=3)",
    "FFT_DOMINANT_FREQ": "主导频率",
    "FFT_DOMINANT_FREQ_MAG": "主导频率幅值",
    # If names like TSAGGR_MEAN_TREND_SLOPE_MEANOFBLOCKS occur, need to map TREND_SLOPE etc.
    # Also, simple stats like MEAN can be a temporal stat:
    # "MEAN" is already defined above as "均值", which is fine for temporal context too.

    # Aggregates across blocks (for TSAGGR)
    "MEANOFBLOCKS": "各子块对应值均值", "STDOFBLOCKS": "各子块对应值标准差",
    "MAXOFBLOCKS": "各子块对应值最大值", "MINOFBLOCKS": "各子块对应值最小值",

    "TRENDSLOPE": "趋势斜率",  # Combined "TREND" and "SLOPE"
    "SHAPEFACTOR": "形状因子",
    "TURNPNTRATIO": "转折点比例",
    "PROPINCR": "增量比例",
    "MEANABSDIFF": "平均绝对差异",
    "SAMPLEENTROPY": "样本熵",
    "LENGTH": "长度",
    "SKEWNESS": "偏度",
    "KURTOSIS": "峰度",
    "MODE": "众数",

}
class FeatureGenerator(McgDataLoader):
    """
    特征生成器,包含基础特征、时序解码特征、综合特征.
    """

    def __init__(self, w_list=None, file_label_tuple=None, remote=True):
        super().__init__(file_label_tuple, remote)
        if w_list is None:
            w_list = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4]
        self.w_list = w_list
        self.feature_cache = pd.DataFrame(index=[0])  # 用于存储已生成的特征
        self.generated_features = set()  # 跟踪已生成的特征名称
        self.all_gend_features = None
        if not fg.is_loaded:
            fg.load_from_file("./files/configs/feature_registry_V2.json")
        else:
            ...
            # print("使用缓存的特征注册器")
            # self.get_all()

    def _update_features(self, func_name, df, feature_columns):
        # 更新注册器、缓存特征
        fg.update_features(func_name, feature_columns)
        df.index = self.feature_cache.index
        self.feature_cache = pd.concat([self.feature_cache, df[feature_columns]], axis=1)
        self.generated_features.update(feature_columns)

    @feature_regis()
    def get_stat_features(self, basic_args_dict=None, label=None) -> pd.DataFrame:
        """
        获取统计特征
        :param basic_args_dict:
        :param label:
        :return:
        """
        basic_args_dict = basic_args_dict or self.basic_args_dict or self.process_file(self.file_label_tuple)[0]
        _, label = self.file_label_tuple
        stat = StatFeatures(basic_args_dict, label)
        return_df = stat.get_stat_features(basic_args_dict, label)

        # 更新特征
        feature_columns = list(set(return_df.columns) - {'MCG_ID', 'mcg_file', 'label'})
        self._update_features(inspect.currentframe().f_code.co_name, return_df, feature_columns)
        return return_df

    @feature_regis()
    def get_ts_features(self, basic_args_dict=None, label=None) -> pd.DataFrame:
        """
        获取时序特征 主要使用ts包
        :param basic_args_dict:
        :param label:
        :return:
        """
        basic_args_dict = basic_args_dict or self.basic_args_dict or self.process_file(self.file_label_tuple)[0]
        _, label = self.file_label_tuple

        # 获取时序特征
        ts = TimeSeqFeatures(basic_args_dict, label)
        df_ts = ts.get_ts_features()

        # 更新特征
        feature_columns = list(set(df_ts.columns) - {'MCG_ID', 'mcg_file', 'label'})
        self._update_features(inspect.currentframe().f_code.co_name, df_ts, feature_columns)
        return df_ts

    @feature_regis()
    def get_ti_features(self, basic_args_dict=None, label=None, w_list=None) -> pd.DataFrame:
        """
        获取时序图特征 time_image_features
        :param basic_args_dict:
        :param label:
        :param w_list:
        :return:
        """
        # w_list = w_list or [0.9, 0.8, 0.7, 0.6, 0.5, 0.4]
        # w_list = w_list or [0.8,0.5,0.2]
        w_list = w_list or [0.5,0.7,0.3]

        print(w_list)

        basic_args_dict = basic_args_dict or self.basic_args_dict or self.process_file(self.file_label_tuple)[0]
        _, label = self.file_label_tuple

        # tif = TimeImageFeatures(basic_args_dict, label, w_list)
        # df_tif = tif.get_complex_features(w_list=w_list)

        # df_tif = tif.get_complex_features(w_list_quantiles_for_frame_features=w_list)

        # --- 更新测试
        from feature_generator import time_image_features_optimized
        tif = time_image_features_optimized.TimeImageFeaturesOptimized(
            basic_args_dict=basic_args_dict, label=label, w_list=w_list,enable_parallel=True)

        df_tif = tif.extract_features()


        # 更新特征
        feature_columns = list(set(df_tif.columns) - {'MCG_ID', 'mcg_file', 'label'})
        self._update_features(inspect.currentframe().f_code.co_name, df_tif, feature_columns)
        return df_tif

    @staticmethod
    def _get_chinese_translation(key_parts, Dictionaries, default_key_str):
        """Helper to find longest match in dictionaries for a list of key_parts."""
        for i in range(len(key_parts), 0, -1):
            current_key = "_".join(key_parts[:i]).upper()
            for D_CH in Dictionaries:
                if current_key in D_CH:
                    return D_CH[current_key], "_".join(key_parts[i:])  # Return translation and remaining parts
        return default_key_str, "_".join(key_parts)  # No match, return original and all parts

    def explain_feature_chinese(self,feature_name: str) -> str:
        parts = feature_name.split('_')
        if not parts:
            return "无法解析特征名称。"

        prefix = parts[0].upper()
        explanation_parts = []

        prefix_ch = PREFIX_MAP_CH.get(prefix)
        if not prefix_ch:
            return f"未知特征类型前缀: {prefix}"
        explanation_parts.append(f"特征属“{prefix_ch}”类别。")

        remaining_parts = parts[1:]

        if prefix == "STATS":
            # STATS_{SEGMENT}_{SOURCE}_{MEASUREMENT}_{STATISTIC_OR_QUALIFIER_MULTI_PART}

            segment_ch, remaining_parts_str = self._get_chinese_translation(remaining_parts, [SEGMENT_MAP_CH],
                                                                       "_".join(remaining_parts))
            remaining_parts = remaining_parts_str.split('_') if remaining_parts_str else []

            source_ch, remaining_parts_str = self._get_chinese_translation(remaining_parts, [SOURCE_MAP_CH],
                                                                      "_".join(remaining_parts))
            remaining_parts = remaining_parts_str.split('_') if remaining_parts_str else []

            # The rest is measurement and then statistic/qualifier
            # Qualifier can be CH{idx}, GROUP{idx}, AT_{PEAK}
            # Statistic is usually the last part if no qualifier.
            # Measurement can be multi-part.

            qualifier_ch = ""
            final_stat_ch = ""
            measurement_candidate_parts = []

            if remaining_parts:
                # Check for CH{idx} or GROUP{idx}
                if remaining_parts[-1].upper().startswith("CH") and remaining_parts[-1][2:].isdigit():
                    qualifier_ch = f"第{remaining_parts[-1][2:]}导联的"
                    measurement_candidate_parts = remaining_parts[:-1]
                elif remaining_parts[-1].upper().startswith("GROUP") and remaining_parts[-1][5:].isdigit():
                    qualifier_ch = f"第{remaining_parts[-1][5:]}组的"
                    measurement_candidate_parts = remaining_parts[:-1]
                # Check for AT_PEAK (e.g., AT_TPEAK)
                elif len(remaining_parts) >= 2 and remaining_parts[-2].upper() == "AT":
                    peak_qualifier_key = f"AT_{remaining_parts[-1].upper()}"
                    qualifier_ch = STATISTIC_MAP_CH.get(peak_qualifier_key, f"{remaining_parts[-1]}处") + "的"
                    measurement_candidate_parts = remaining_parts[:-2]
                else:  # No specific qualifier, last part is statistic
                    stat_key = remaining_parts[-1].upper()
                    final_stat_ch = STATISTIC_MAP_CH.get(stat_key, stat_key)
                    measurement_candidate_parts = remaining_parts[:-1]

            if not measurement_candidate_parts and remaining_parts:  # If only one part left, it's the measurement
                measurement_candidate_parts = remaining_parts

            measurement_ch, _ = self._get_chinese_translation(measurement_candidate_parts, [MEASUREMENT_MAP_CH],
                                                         "_".join(measurement_candidate_parts))

            desc = f"它描述了“{segment_ch}”"
            if source_ch and source_ch != segment_ch:
                desc += f"基于“{source_ch}”数据"

            desc += f"，计算得到的{qualifier_ch}“{measurement_ch}”"

            if final_stat_ch:
                desc += f"的“{final_stat_ch}”。"
            elif qualifier_ch:  # If there was a qualifier, it implies a direct value
                desc += "的“值”。"
            else:  # Should not happen if parsing is correct
                desc += "。"

            explanation_parts.append(desc)

        elif prefix == "TSREGIONAL":
            # TSREGIONAL_BLOCK_R{r}_C{c}_{SPATIALSTAT}_{TEMPORALSTAT_MULTI_PART}
            # e.g. TSREGIONAL_BLOCK_R1_C2_SPATIALSTD_SAMPLE_ENTROPY
            block_id_str = f"{remaining_parts[0]}_{remaining_parts[1]}_{remaining_parts[2]}"  # BLOCK_R0_C0
            block_r = remaining_parts[1][1:]
            block_c = remaining_parts[2][1:]
            block_id_ch = f"空间子块(行{block_r},列{block_c})"

            spatial_stat_key = remaining_parts[3].upper()  # SPATIALSTD
            # Use MEASUREMENT_MAP_CH for spatial stats (e.g. "MEAN" -> "空间平均值")
            spatial_stat_ch = MEASUREMENT_MAP_CH.get(spatial_stat_key, spatial_stat_key)

            temporal_stat_parts = remaining_parts[4:]  # Can be multi-part like SAMPLE_ENTROPY or FFT_DOMINANT_FREQ
            temporal_stat_ch, _ = self._get_chinese_translation(temporal_stat_parts, [STATISTIC_MAP_CH],
                                                           "_".join(temporal_stat_parts))

            explanation_parts.append(
                f"它表示对于“{block_id_ch}”内的“{spatial_stat_ch}”随时间形成的序列，进一步计算得到的时序“{temporal_stat_ch}”。")

        elif prefix == "TSAGGR":
            # TSAGGR_{SPATIAL_STAT_TYPE}_{TEMPORAL_STAT_TYPE_MULTI_PART}_{AGGREGATE_ACROSS_BLOCKS}
            # Example: TSAGGR_MEAN_TREND_SLOPE_MEANOFBLOCKS -> spatial_stat: MEAN, temporal_stat: TREND_SLOPE, agg: MEANOFBLOCKS
            # Example: TSAGGR_MIN_FFT_DOMINANT_FREQ_MAG_MINOFBLOCKS

            # Part 1: Spatial Statistic Type (e.g., MEAN, MIN)
            # This "MEAN" refers to the spatial characteristic whose time series was analyzed
            spatial_stat_type_key = remaining_parts[0].upper()
            # Use MEASUREMENT_MAP_CH for this, as it maps "MEAN" to "空间平均值" etc.
            spatial_stat_type_ch = MEASUREMENT_MAP_CH.get(spatial_stat_type_key, spatial_stat_type_key)

            # Part 3: Aggregate Across Blocks (e.g., MEANOFBLOCKS)
            aggregate_key = remaining_parts[-1].upper()
            aggregate_ch = STATISTIC_MAP_CH.get(aggregate_key, aggregate_key)

            # Part 2: Temporal Statistic Type (everything in between, can be multi-part)
            temporal_stat_parts = remaining_parts[1:-1]
            temporal_stat_type_ch, _ = self._get_chinese_translation(temporal_stat_parts, [STATISTIC_MAP_CH],
                                                                "_".join(temporal_stat_parts))

            explanation_parts.append(
                f"它表示对所有空间子块的“{spatial_stat_type_ch}”时序分别计算“{temporal_stat_type_ch}”后，再将这些得到的“{temporal_stat_type_ch}”值进行“{aggregate_ch}”统计。")
        elif prefix == "MFMIMGTS":
            # MFMIMGTS_{SEGMENT}_{FRAME_METRIC_COMPACT}_{QTAG?}_{POLETYPE?}_{POLEID?}_{TEMP_STAT}_{W_FILTER_TAG}
            # Example: MFMIMGTS_TT_FRAME_MAXMCGVALRAW_MEAN_W0P5
            # Example: MFMIMGTS_QR_FRAME_MCGS_Q0P5_POS_POLE0_TRENDSLOPE_W0P5

            segment_key = remaining_parts[0].upper()
            segment_ch = SEGMENT_MAP_CH.get(segment_key, segment_key)

            w_filter_tag_str = remaining_parts[-1]  # e.g., W0P5
            temporal_stat_key_compact = remaining_parts[-2].upper()  # e.g., MEAN, TRENDSLOPE

            w_filter_val_actual = w_filter_tag_str.replace('W', '').replace('P', '.')
            w_filter_ch_desc = f"(处理基于{w_filter_val_actual}分位数选取的帧特征序列)"
            temporal_stat_ch = STATISTIC_MAP_CH.get(temporal_stat_key_compact, temporal_stat_key_compact)

            # Frame feature base parts: from index 1 ("FRAME") up to before temporal_stat_key and w_filter_tag_str
            # e.g., ["FRAME", "MCGS", "Q0P5", "POS", "POLE0"]
            frame_full_base_name_parts = remaining_parts[1:-2]

            frame_desc = "帧的"
            if frame_full_base_name_parts and frame_full_base_name_parts[0].upper() == "FRAME":
                # Attempt to parse the structured frame feature name
                metric_name_compact = frame_full_base_name_parts[1]  # E.g. "MCGS", "MAXMCGVALRAW"
                metric_ch = MEASUREMENT_MAP_CH.get(metric_name_compact, metric_name_compact)

                qualifiers_for_frame_desc = []
                # Check for Quantile Tag
                q_part = next((p for p in frame_full_base_name_parts[2:] if p.startswith("Q") and "P" in p), None)
                if q_part:
                    qualifiers_for_frame_desc.append(f"{q_part.replace('Q', '').replace('P', '.')}分位数阈值下的")

                # Check for Pole Type
                pole_type_part = next((p for p in frame_full_base_name_parts[2:] if p in ["POS", "NEG"]), None)
                if pole_type_part:
                    qualifiers_for_frame_desc.append(f"{'正' if pole_type_part == 'POS' else '负'}极")

                # Check for Pole ID
                pole_id_part = next((p for p in frame_full_base_name_parts[2:] if p.startswith("POLE")), None)
                if pole_id_part:
                    pole_id_num = pole_id_part.replace('POLE', '')
                    qualifiers_for_frame_desc.append(
                        f"{'主' if pole_id_num == '0' else ('次' + pole_id_num)}极子")  # Assuming POLE0 is primary

                frame_desc += "".join(qualifiers_for_frame_desc) + f"“{metric_ch}”"
            else:  # Fallback if parsing frame name fails
                frame_desc = f"“{' '.join(frame_full_base_name_parts)}”帧级别特征"

            explanation_parts.append(
                f"它表示对于“{segment_ch}”中的“{frame_desc}”随时间形成的序列，计算得到的时序“{temporal_stat_ch}” {w_filter_ch_desc}。")
        else:
            explanation_parts.append(f"未能识别该特征的具体结构: {'_'.join(remaining_parts)}")

        return " ".join(explanation_parts)
    def get_all(self, data=None) -> pd.DataFrame:
        """
        获取所有特征，同时更新特征注册器
        :return: 所有特征dataframe；
        """
        # 获取基础参数
        if data is None:
            basic_args_dict, result_peak = self.process_tmp_file(data,self.file_label_tuple)
            _, label = self.file_label_tuple
        else:
            basic_args_dict, result_peak = self.process_tmp_file(data,self.file_label_tuple)
            _, label = self.file_label_tuple
        # 获取所有特征
        feature_dfs = {}
        for feature_func_name in ['get_stat_features', 'get_ts_features', 'get_ti_features']:
            t1 = time.time()
            feature_func = getattr(self, feature_func_name)
            df = feature_func(basic_args_dict=basic_args_dict, label=label)
            feature_dfs[feature_func_name] = df
            print(f"{feature_func_name}：{time.time() - t1}")

        # 合并数据
        merged_df = pd.merge(feature_dfs['get_stat_features'], feature_dfs['get_ts_features'], on='MCG_ID', how='inner')
        merged_df['mcg_file'] = merged_df['MCG_ID']
        merged_df = merged_df.drop(columns=['MCG_ID'])

        final_merged_df = pd.merge(merged_df, feature_dfs['get_ti_features'], on='mcg_file', how='inner')
        final_merged_df['label'] = label
        print("已完成处理文件:", basic_args_dict['file_name'])
        self.all_gend_features = final_merged_df
        return final_merged_df


        # print all feature names
        # for col in merged_df.columns:
        #     print(col,"解释：",self.explain_feature_chinese(col))

        # final_merged_df = merged_df
        # final_merged_df['label'] = label
        # print("已完成处理文件:", basic_args_dict['file_name'])
        # self.all_gend_features = final_merged_df
        # return final_merged_df



    def get_needed_v1(self, feature_names: List[str], file_name=None):
        """
        从单个文件以及所需特征表，获取相应的特征
        :param feature_names: 要求获取的最少特征列
        :param file_name: 文件名
        :return: 特征DataFrame和不支持的特征列表
        """

        # 替换成单个文件的路径
        if file_name:
            self.file_label_tuple = (file_name, 0)
            # self.base_dir = r'/home/<USER>/data_raw/所有心磁数据2024-3-14/{}.txt'

        # 获取基础参数
        basic_args_dict = self.basic_args_dict or self.process_file(self.file_label_tuple)[0]

        _, label = self.file_label_tuple

        # 确定需要生成的特征 -----------------------------------------------------------
        needed_features = set(feature_names)
        missing_features = needed_features - self.generated_features

        if missing_features:
            groups_to_call = fg.get_groups_for_features(list(missing_features))
            for group_name in groups_to_call:
                feature_func = getattr(self, group_name, None)
                if feature_func:
                    t1 = time.time()
                    group_results = feature_func(basic_args_dict, label)
                    print(f"{group_name}：{time.time() - t1}")
                    # 更新缓存
                    new_features = [col for col in group_results.columns if col not in self.generated_features]
                    # self.feature_cache = pd.concat([self.feature_cache, group_results[new_features]], axis=1)
                    self.generated_features.update(new_features)

        # 从缓存中获取所需特征
        supported_features = list(needed_features.intersection(self.generated_features))
        unsupported_features = list(needed_features - set(supported_features))

        if supported_features:
            final_df = self.feature_cache[supported_features].copy()
            if 'MCG_ID' in final_df.columns:
                final_df['mcg_file'] = final_df['MCG_ID']
                final_df = final_df.drop(columns=['MCG_ID'])
            final_df['mcg_file'] = self.file_label_tuple[0]
            final_df['label'] = label
            print("已完成处理文件:", basic_args_dict['file_name'])
            return final_df, unsupported_features
        else:
            print("提取器不支持所需特征：", feature_names)
            return pd.DataFrame(), feature_names

    def get_embed_features(self):
        all_gend_features = self.all_gend_features or self.get_all()
        # 假设已经获取fit模型； 对所有数据特征进行transform；


if __name__ == '__main__':
    # ---------------------------------------- 选择生成特征测试 ---------------------------------------------
    generator = FeatureGenerator(file_label_tuple=('GDNH_2023_000391', 0), remote=True)
    # df_ti = generator.get_ti_features()
    # df_ts = generator.get_ts_features()
    # df_stat = generator.get_stat_features()

    df = generator.get_all()

    # 选特定特征。
    fg.reload_from_file("./files/configs/feature_registry.json")
    selected_features_ts = fg.filter_features_for_function('get_ts_features')
    print(selected_features_ts)

    # 使用示例
    wanted_features_path = "./files/saved_features/selected_features/integrated_features_stable_F5_s42_fold_all_top150.pkl"
    with open(wanted_features_path, 'rb') as f:
        wanted_features = pickle.load(f)
    selected_features, _ = generator.get_needed_v1(wanted_features.columns.tolist())

    fg.save_to_file("./files/configs/feature_registry_V2.json")
