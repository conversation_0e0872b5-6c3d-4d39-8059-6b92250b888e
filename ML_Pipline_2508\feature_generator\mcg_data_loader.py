'''
@Project ：ML_Pipline 
@File    ：mcg_data_loader.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/8/19 上午11:23 
@Discribe：
'''
import os

import pandas as pd

import feature_generator.utils.time_locs1 as time_locs1
from feature_generator.utils.tool import calculate_interpolation


class McgDataLoader:
    """
    读取单个文件/标签对的基本信息。
    """

    def __init__(self, file_label_tuple=None, remote=True):
        self.file_path = None
        self.data_frame = None
        self.save_path = None
        self.file_name = None
        self.remote = remote
        if self.remote:
            self.base_dir = r'/home/<USER>/data_raw/所有心磁数据2024-3-14/{}.txt'
        else:
            self.base_dir = r'D:/wyh/Data/所有心磁数据2024-3-14/{}.txt'
        self.basic_args_dict = None
        self.result_peak = None
        self.file_label_tuple = file_label_tuple

    def get_basic_args(self, file_path, data=None):
        # save_path = os.path.join(os.path.abspath(os.path.join(file_path, '..', '..')), 'result', )
        # if not os.path.exists(save_path):
        #     os.makedirs(save_path)
        save_path = None
        file_name = os.path.basename(file_path)
        if data is None:
            data_frame = pd.read_csv(file_path, sep='\t', header=None)
        else:
            data_frame = data
        self.data_frame, self.save_path, self.file_name = data_frame, save_path, file_name
        return save_path, file_name, data_frame

    def get_label(self):
        pass

    def process_file(self, file_label_tuple=None):
        """
        获取单个文件/标签对的基本信息。
        :param file_label_tuple:
        :return:
        """
        if not file_label_tuple:
            file_label_tuple = self.file_label_tuple
        file, label = file_label_tuple
        file_path = self.base_dir.replace('{}', file)

        # 获取qrs时刻点
        result_peak = time_locs1.time_point(file_path)

        # 读取Excel文件中的时刻点数据
        try:
            # 假设Excel文件路径保存在self.excel_path中
            df = pd.read_excel("D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx", sheet_name='训练集')

            # 根据心磁号匹配数据
            specific_data = df[df['心磁号'] == file].iloc[0] if not df[df['心磁号'] == file].empty else None

            if specific_data is None:
                print("找不到文件qrs时刻点标记，使用直接计算结果>>")
            else:
                # 创建params_time字典，从specific_data中获取相应的值
                params_time = {
                    'loc_q_peak': specific_data['Qp'],
                    'loc_r_peak': specific_data['Rp'],
                    'loc_s_peak': specific_data['Sp'],
                    'loc_t_onset': specific_data['To'],
                    'loc_t_peak': specific_data['Tp'],
                    'loc_t_end': specific_data['Te'],
                }

                # 替换result_peak中的值，只有当params_time中的值不为空时
                for key, value in params_time.items():
                    if pd.notna(value):
                        result_peak[key.replace('loc_', '').replace('_', '-')] = value
                print("替换qrs时刻点成功，使用文件标记>>")
        except Exception as e:
            print(f"替换qrs时刻点失败，使用直接计算结果>> 错误信息: {e}")

        # 获取数据、插值数据
        save_path, file_name, data_frame = self.get_basic_args(file_path)
        interpolated_data = calculate_interpolation(file_path, data_frame, peak_locs=result_peak)
        basic_args_dict = {
            'file_path': file_path,
            'save_path': save_path,
            'file_name': file_name,
            'data_frame': data_frame,
            'result_peak': result_peak,
            'interpolated_data': interpolated_data
        }

        self.basic_args_dict, self.result_peak = basic_args_dict, result_peak
        return self.basic_args_dict, self.result_peak

    def simulate_styled_data(self, array):
        """
        从不同类型的输入数据模拟生成 DataFrame 和符合制表符分隔格式的列表。

        :param array: ndarray 或 DataFrame, 原始数据
        :return: data_frame, mcgdata
        :raises TypeError: 当输入数据类型不支持时抛出
        """
        import pandas as pd
        import numpy as np

        # 检查输入类型并转换为 DataFrame
        if isinstance(array, pd.DataFrame):
            data_frame = array.copy()
        elif isinstance(array, np.ndarray):
            data_frame = pd.DataFrame(array)
        else:
            raise TypeError(f"Unsupported input type: {type(array)}. Expected numpy.ndarray or pandas.DataFrame")

        # 将 DataFrame 转换为numpy数组以确保正确迭代
        array_data = data_frame.values

        # 创建制表符分隔的字符串列表
        try:
            mcgdata = ["\t".join(map(str, row)) for row in array_data]
        except Exception as e:
            raise TypeError(f"Error converting data to tab-separated format: {str(e)}")

        return data_frame, mcgdata

    def process_tmp_file(self, data=None, file_label_tuple=None):
        """
        获取单个文件/标签对的基本信息。
        :param file_label_tuple:
        :return:
        """
        if not file_label_tuple:
            file_label_tuple = self.file_label_tuple
        file, label = file_label_tuple
        file_path = self.base_dir.replace('{}', file)

        if data is None:  # 从路径读取
            result_peak = time_locs1.time_point(file_path)
            save_path, file_name, data_frame = self.get_basic_args(file_path)
        else:  # 从入参获取
            data_frame, mcgdata = self.simulate_styled_data(data)
            result_peak = time_locs1.time_point(file_path, mcgdata)
            save_path, file_name, data_frame = self.get_basic_args(file_path, data_frame)
        # 从文件获取时刻点----------------------------------------------------
        # 读取Excel文件中的时刻点数据
        # try:
        #     # 假设Excel文件路径保存在self.excel_path中
        #     df = pd.read_excel("D:\std data\\20250227版本\\内测样本分层处理-结果20250227.xlsx", sheet_name='外验集')
        #
        #     # 根据心磁号匹配数据
        #     specific_data = df[df['心磁号'] == file].iloc[0] if not df[df['心磁号'] == file].empty else None
        #
        #     if specific_data is None:
        #         print("找不到文件qrs时刻点标记，使用直接计算结果>>")
        #     else:
        #         # 创建params_time字典，从specific_data中获取相应的值
        #         params_time = {
        #             'loc_q_peak': specific_data['Qp'],
        #             'loc_r_peak': specific_data['Rp'],
        #             'loc_s_peak': specific_data['Sp'],
        #             'loc_t_onset': specific_data['To'],
        #             'loc_t_peak': specific_data['Tp'],
        #             'loc_t_end': specific_data['Te'],
        #         }
        #
        #         # 替换result_peak中的值，只有当params_time中的值不为空时
        #         for key, value in params_time.items():
        #             if pd.notna(value):
        #                 result_peak[key.replace('loc_', '').replace('_', '-')] = value
        #         # 逻辑检查
        #         ajudgement_flag = False
        #         if result_peak['q-head'] > result_peak['q-peak']:
        #             ajudgement_flag = True
        #             result_peak['q-head'] = result_peak['q-peak']- 1
        #         if result_peak['s-r'] < result_peak['s-peak']:
        #             ajudgement_flag = True
        #             result_peak['s-r'] = result_peak['s-peak']+ 1
        #
        #         if result_peak['t-head'] > result_peak['t-onset']:
        #             ajudgement_flag = True
        #             result_peak['t-head'] = result_peak['t-onset']- 1
        #         if result_peak['t-end'] >= result_peak['t-r']:
        #             ajudgement_flag = True
        #             result_peak['t-r'] = min(result_peak['t-end']+ 1,data_frame.shape[0]-1) # 防止越界
        #         if result_peak['s-r'] > result_peak['t-head']:
        #             ajudgement_flag = True
        #             result_peak['s-r'] = result_peak['t-head']- 1
        #         if result_peak['p-r'] > result_peak['q-head']:
        #             ajudgement_flag = True
        #             p1,p2 = result_peak['p-peak']-result_peak['p-head'],result_peak['p-r']-result_peak['p-peak']
        #             result_peak['p-r'] = result_peak['q-head']- 1
        #             result_peak['p-peak'],result_peak['p-head']=result_peak['p-r']-p2,result_peak['p-r']-p1-p2
        #
        #         if ajudgement_flag:
        #             print("替换qrs时刻点成功(经时刻点调整)，使用文件标记>>")
        #         else:
        #             print("替换qrs时刻点成功，使用文件标记>>")
        # except Exception as e:
        #     print(f"替换qrs时刻点失败，使用直接计算结果>> 错误信息: {e}")
        # ------------------------------
        # result_peak['p-head'], result_peak['p-peak'], result_peak['p-r'] =10,20,36
        interpolated_data = calculate_interpolation(file_path, data_frame, peak_locs=result_peak)
        basic_args_dict = {
            'file_path': file_path,
            'save_path': save_path,
            'file_name': file_name,
            'data_frame': data_frame,
            'result_peak': result_peak,
            'interpolated_data': interpolated_data
        }

        self.basic_args_dict, self.result_peak = basic_args_dict, result_peak
        return self.basic_args_dict, self.result_peak
