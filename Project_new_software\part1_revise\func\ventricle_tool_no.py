"""
@ Author: <PERSON>
@ E-mail: <EMAIL>
@ Date: 2024/10/30 16:35

"""
import meshio
import multiprocessing
from .fun_colletion import *
pi = math.pi


class SurfaceRecon:
    def __init__(self, heart_path, lcav_path, Q, R, S, check_time, mcg_data):
        self.location = None
        self.index_R = None
        self.d_z_max = None
        self.dl = np.arange(0, 0.21, 0.04)
        self.mcg_data = mcg_data
        self.heart_path = heart_path
        self.lcav_path = lcav_path
        self.check_time = check_time
        self.tran_x, self.tran_y = None, None
        self.u_lead_lcav = None
        self.Q, self.R, self.S = Q, R, S
        self.left_points, self.right_points = None, None
        self.mcg_data_interpolate = None  # mcg_interpolate(self.mcg_data)
        self.heart_points, self.heart_tri = None, None
        self.lcav_points, self.lcav_tri, self.lcav_treta = None, None, None
        self.lcav_cen_points = None
        self.lf_cav = None
        self.move_x, self.move_y, self.move_z = None, None, None

    def get_mesh(self):
        self.Length, self.width, self.num_layers = 139.80, 176.6, 233
        heart_mesh = meshio.read(self.heart_path, file_format='gmsh')  # 表面及内部网格
        self.heart_points = np.asarray(heart_mesh.points * 100)
        self.heart_tri = np.asarray(heart_mesh.cells_dict['triangle'])

        lcav_mesh = meshio.read(self.lcav_path, file_format='gmsh')  # 表面及内部网格
        self.lcav_points = np.asarray(lcav_mesh.points * 200)
        self.lcav_tri = np.asarray(lcav_mesh.cells_dict['triangle'])
        self.lcav_treta = np.asarray(lcav_mesh.cells_dict['tetra'])


    def model_heart_No_CTA(self, z_A, z_B):
        """
        适用于没有CTA的数据
        :param mesh_path:
        :param save_path:
        :return:
        """
        self.d_z_max = (self.width + 40) / 1000
        self.heart_points[:, 0] = self.heart_points[:, 0] / 512 * self.width
        self.heart_points[:, 1] = self.heart_points[:, 1] / 512 * self.width
        self.heart_points[:, 2] = self.heart_points[:, 2] / self.num_layers * self.Length
        self.heart_points = self.heart_points[:, [1, 2, 0]]

        self.lcav_points[:, 0] = self.lcav_points[:, 0] / 512 * self.width
        self.lcav_points[:, 1] = self.lcav_points[:, 1] / 512 * self.width
        self.lcav_points[:, 2] = self.lcav_points[:, 2] / self.num_layers * self.Length
        self.lcav_points = self.lcav_points[:, [1, 2, 0]]

        # CT真实心脏点坐标转换成MCG下心脏点坐标
        self.tran_x_y_fun()
        self.location = self.heart_tip(self.width, self.heart_points[:, 0], self.heart_points[:, 1],
                                       self.heart_points[:, 2], self.heart_points.shape[0])

        self.move_x = - self.location[0] + self.tran_x + 20
        self.move_y = - self.location[1] + self.tran_y - 20
        self.move_z = z_A - z_B

        self.heart_points[:, 0] = self.heart_points[:, 0] + self.move_x
        self.heart_points[:, 1] = self.heart_points[:, 1] + self.move_y
        self.heart_points[:, 2] = self.heart_points[:, 2] + self.move_z

        self.lcav_points[:, 0] = self.lcav_points[:, 0] + self.move_x
        self.lcav_points[:, 1] = self.lcav_points[:, 1] + self.move_y
        self.lcav_points[:, 2] = self.lcav_points[:, 2] + self.move_z

        self.lcav_points = self.lcav_points / 1000

        self.lcav_cen_points = get_treta_center(self.lcav_points[self.lcav_treta[:, 0], :],
                                                self.lcav_points[self.lcav_treta[:, 1], :],
                                                self.lcav_points[self.lcav_treta[:, 2], :],
                                                self.lcav_points[self.lcav_treta[:, 3], :])

    def heart_tip(self, width, x_2, y_2, z_2, size_point):
        distance = 0
        standard_point = np.array([0, width, 0])
        index = 0
        max_x = np.max(x_2)
        for point_i in range(size_point):
            point = np.array([x_2[point_i], y_2[point_i], z_2[point_i]])
            distance_2 = np.abs(x_2[point_i] - max_x)
            distance_1 = np.linalg.norm(standard_point - point[0:3])
            if distance_1 > distance and distance_2 < 15:
                distance = distance_1
                point_index = index
            index += 1
        inten = np.zeros(size_point)
        inten[point_index] = 1
        location = np.array([x_2[point_index], y_2[point_index], z_2[point_index], point_index])
        return location

    def tran_x_y_fun(self):
        q, r = self.Q, self.R
        qr_range = np.arange(q, r)
        text = np.zeros((len(qr_range), 3))
        x_1 = np.mgrid[0.2:20.1: 0.2]
        y_1 = np.mgrid[0.2:20.1:0.2]
        [xi, yi] = np.meshgrid(x_1, y_1)
        self.mcg_data_interpolate = mcg_interpolate(self.mcg_data[self.Q:self.R, :])
        for k in range(self.R - self.Q):
            b = self.mcg_data_interpolate[k] * 10 ** (-12)
            index_x, index_y = magnetic_gradient(b, xi, yi)
            text[k][0] = k
            text[k][1] = index_x * 10
            text[k][2] = index_y * 10
        text_min_index = np.argmin(text[:, 2])
        while text_min_index <= len(qr_range) - 2:
            if (text[text_min_index][2] == text[text_min_index + 1][2] and
                    text[text_min_index + 1][1] > text[text_min_index][1]):
                text_min_index += 1
            else:
                if text[text_min_index][1] < np.max(text[:, 1]) - 20:
                    self.tran_x, self.tran_y = np.max(text[:, 1]) - 10, text[text_min_index][2]
                    return self.tran_x, self.tran_y
                    break
                else:
                    self.tran_x, self.tran_y = text[text_min_index][1], text[text_min_index][2]
                    return self.tran_x, self.tran_y
                    break
        self.tran_x, self.tran_y = text[text_min_index][1], text[text_min_index][2]

    def get_lf(self):
        """
        :return: 左心室，包括室间隔的分割
        """
        self.lf_cav = np.zeros((36, self.lcav_cen_points.shape[0] * 2))
        self.u_lead_lcav = np.zeros((36, self.lcav_cen_points.shape[0] * 2))
        t_1 = dev_lead_field_1(self.lcav_cen_points, self.dl, self.d_z_max)
        t_2 = dev_lead_field_1(self.lcav_cen_points, self.dl, self.d_z_max + 0.05)
        t_3 = dev_lead_field_1(self.lcav_cen_points, self.dl, self.d_z_max + 0.10)
        t = t_1 - 2 * t_2 + t_3
        for i in range(self.lcav_cen_points.shape[0]):
            lf = t[i, :, :]
            self.lf_cav[:, 2 * i: 2 * i + 2] = lf[:, 0:2]
            u_lead, s_lead, vt_lead = np.linalg.svd(self.lf_cav[:, 2 * i: 2 * i + 2])
            self.u_lead_lcav[:, 2 * i: 2 * i + 2] = u_lead[:, 0:2]

    def rap_music(self, b, ii):
        """
        主要针对整个左心室， 使两个点相关性大于0.75的点去除, 取多个点源
        :param b: 磁场 b
        :param ii: 时刻 ii
        :return: 对于单点的室间隔点源还原
        """
        b_1 = b.reshape((36, 1))
        signal_subspace = sig_subspace(b_1)  # 求出磁场b_1的特征向量
        if self.lf_cav is None:
            self.get_lf()

        corr = signal_subspace.T @ self.u_lead_lcav
        corr_all = np.sqrt(corr[0, 0::2] ** 2 + corr[0, 1::2] ** 2)
        max_index = np.argmax(corr_all)
        point = np.array([[self.lcav_cen_points[max_index][0], self.lcav_cen_points[max_index][1],
                           self.lcav_cen_points[max_index][2]]])
        result_1 = [ii, point[0][0], point[0][1], point[0][2]]
        return result_1

    def source_strength(self):
        result = []
        time_Q_S = list(range(self.check_time[0], self.check_time[1]))
        for ii in time_Q_S:
            b_mat = (-self.mcg_data[ii, :] * 10 ** (-12)).reshape(6, 6)
            b_mat_1 = b_mat[::-1]
            b = b_mat_1.flatten()
            result_1 = self.rap_music(b, ii)
            result.append(result_1)
        return result




