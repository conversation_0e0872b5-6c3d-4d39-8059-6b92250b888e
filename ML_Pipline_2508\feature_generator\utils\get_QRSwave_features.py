'''
@Project ：pythonProject 
@File    ：get_QRSwave_features.py
@IDE     ：PyCharm 
<AUTHOR> Qu
@Date    ：2023/4/20 8:25
Attention: 代码中的loc_q_peak, loc_r_peak, loc_s_peak不一定是对应的  qrs峰，只有在计算QRS段的时候才是及对应的
            可以理解为，q为起始值，s为结束值，r为任意点，这边r主要是为了计算峰值出相关信息。
'''

import pywt

from feature_generator.utils.get_args import *


def time_features_qrs(DATA_TEMP_Spline,signal_channels, loc_q_peak, loc_r_peak, loc_s_peak, gap_loc,band='qrs',park_name ='rpeak',save_path = None,filename = None,imShow = False):
    qrswave_time_dict = {}
    col_magni = 0
    col_angle = 1
    col_xposition = 2
    col_yposition = 3

    loop_set = np.arange(loc_q_peak, loc_s_peak+1, gap_loc)
    rec_all = np.zeros((len(loop_set), 4))

    for i, loc in enumerate(loop_set):
        mfm = DATA_TEMP_Spline[loc]
        max_magni, max_angle, max_x_position, max_y_position = pcd_rec(mfm, model='e', local = loc,save_path = save_path,filename = filename,imShow=imShow,interval='qrs')
        rec_all[i] = [max_magni, max_angle, max_x_position, max_y_position]

    # R 峰最大电流处相关信息
    mfm = DATA_TEMP_Spline[loc_r_peak]
    max_magni, max_angle, max_x_position, max_y_position = pcd_rec(mfm, model='e', imShow=imShow,interval='qrs')
    qrswave_time_dict.update({
        f"{band}_{park_name}_max_magni": max_magni,
        f"{band}_{park_name}_max_angle": max_angle,
        f"{band}_{park_name}_max_x_position": max_x_position,
        f"{band}_{park_name}_max_y_position": max_y_position
    })


    # QRS期间最大电流处幅值

    dict_tempt = statistic_analysis(rec_all[:, col_magni], f'{band}_max_magni')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # QRS期间最大电流处角度
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_angle], f'{band}_max_angle')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # QRS期间最大电流处x
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_xposition], f'{band}_max_x_position')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # QRS期间最大电流处y
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_yposition], f'{band}_max_y_position')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # QRS期间最大电流处相对位移
    rela_disp = np.sqrt(np.sum(np.diff(rec_all[:, [col_xposition, col_yposition]], axis=0) ** 2, axis=1))
    dict_tempt = statistic_analysis_no_normaillize(rela_disp, f'{band}_max_rela_disp')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # QRS间期最大电流处幅值和相位角
    abs_magni = np.sqrt(np.sum(rec_all[:, [col_xposition, col_yposition]] ** 2, axis=1))
    dict_tempt = statistic_analysis(abs_magni, f'{band}_max_abs_magni')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    abs_angle = np.arctan2(rec_all[:, col_yposition], rec_all[:, col_xposition]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle, f'{band}_max_abs_angle')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)
    ###############################################################################
    #    等磁图
    #  下面是磁场相关计算代码
    ##############################################################################

    col_ratio = 0
    col_angle = 1
    col_dist = 2
    col_x_max = 3
    col_y_max = 4
    col_x_min = 5
    col_y_min = 6
    col_area_pos = 7
    col_area_neg = 8

    loop_set = range(loc_q_peak, loc_s_peak+1 , gap_loc)
    rec_all = np.zeros((len(loop_set), 9))

    for i, loc in enumerate(loop_set):
        mfm = DATA_TEMP_Spline[loc]

        ratio_minmax, angle_minmax, dist_minmax, x_max_mfm, y_max_mfm, x_min_mfm, y_min_mfm, area_pos_pole, area_neg_pole = \
            pcd_rec(mfm, model='m', imShow=imShow)
        temp = [ratio_minmax, angle_minmax, dist_minmax, x_max_mfm, y_max_mfm, x_min_mfm, y_min_mfm, area_pos_pole,
                area_neg_pole]
        temp = np.array(temp)
        rec_all[i] = temp

    # R峰正负极磁场角度相关信息
    mfm = DATA_TEMP_Spline[loc_r_peak]
    result = pcd_rec(mfm, model='m', imShow=imShow)

    dict_tempt = {
        f'{band}_{park_name}_ratio_minmax': result[0],
        f'{band}_{park_name}_angle_minmax': result[1],
        f'{band}_{park_name}_dist_minmax': result[2],
        f'{band}_{park_name}_x_max_mfm': result[3],
        f'{band}_{park_name}_y_max_mfm': result[4],
        f'{band}_{park_name}_x_min_mfm': result[5],
        f'{band}_{park_name}_y_min_mfm': result[6],
        f'{band}_{park_name}_area_pos_pole': result[7],
        f'{band}_{park_name}_area_neg_pole': result[8]
    }

    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场连线比率
    dict_tempt = statistic_analysis(rec_all[:, col_ratio], f'{band}_ratio_minmax')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场连线角度
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_angle], f'{band}_angle_minmax')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场连线距离
    dict_tempt = statistic_analysis(rec_all[:, col_dist], f'{band}_dist_minmax')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场最大处x
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_x_max], f'{band}_x_max_mfm')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场最大处y
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_y_max], f'{band}_y_max_mfm')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场最小处x
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_x_min], f'{band}_x_min_mfm')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场最小处y
    dict_tempt = statistic_analysis_no_normaillize(rec_all[:, col_y_min], f'{band}_y_min_mfm')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间正磁场面积
    dict_tempt = statistic_analysis(rec_all[:, col_area_pos], f'{band}_area_pos_pole')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间负磁场面积
    dict_tempt = statistic_analysis(rec_all[:, col_area_neg], f'{band}_area_neg_pole')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # 最大最小的中心点x,y
    center_minmax = 0.5 * (rec_all[:, [col_x_max, col_y_max]] + rec_all[:, [col_x_min, col_y_min]])
    dict_tempt = statistic_analysis_no_normaillize(center_minmax[:, 0], f'{band}_center_minmax_x')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    dict_tempt = statistic_analysis_no_normaillize(center_minmax[:, 1], f'{band}_center_minmax_y')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间磁场最大点的最远距离
    qrswave_time_dict[f'{band}_max_dist_mfm'] = max_distance(center_minmax)[0]

    # qrs期间正磁场的相对移动
    rela_dist_pos = np.sqrt(np.sum(np.diff(rec_all[:, [col_x_max, col_y_max]], 1) ** 2, axis=1))
    dict_tempt = statistic_analysis_no_normaillize(rela_dist_pos, f'{band}_rela_dist_pos')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs期间负磁场的相对移动
    rela_dist_neg = np.sqrt(np.sum(np.diff(rec_all[:, [col_x_min, col_y_min]], 1) ** 2, axis=1))
    dict_tempt = statistic_analysis_no_normaillize(rela_dist_neg, f'{band}_rela_dist_neg')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs间期正磁场处幅值和角度
    abs_magni_pos = np.sqrt(np.sum(rec_all[:, [col_x_max, col_y_max]] ** 2, axis=1))
    dict_tempt = statistic_analysis(abs_magni_pos, f'{band}_abs_magni_pos')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    abs_angle_pos = np.arctan2(rec_all[:, col_y_max], rec_all[:, col_x_max]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle_pos, f'{band}_abs_angle_pos')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs间期负磁场处幅值和角度
    abs_magni_neg = np.sqrt(np.sum(rec_all[:, [col_x_min, col_y_min]] ** 2, axis=1))
    dict_tempt = statistic_analysis(abs_magni_neg, f'{band}_abs_magni_neg')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    abs_angle_neg = np.arctan2(rec_all[:, col_y_min], rec_all[:, col_x_min]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle_neg, f'{band}_abs_angle_neg')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    # qrs间期正负极中心处幅值和角度
    abs_magni_center = np.sqrt(np.sum(center_minmax ** 2, axis=1))
    dict_tempt = statistic_analysis(abs_magni_center, f'{band}_abs_magni_center')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)

    abs_angle_center = np.arctan2(center_minmax[:, 1], center_minmax[:, 0]) * (180 / np.pi)
    dict_tempt = statistic_analysis_no_normaillize(abs_angle_center, f'{band}_abs_angle_center')
    qrswave_time_dict = build_dict(qrswave_time_dict, dict_tempt)
    return qrswave_time_dict

def entropy_features_qrs(signal_channels, loc_q_peak, loc_r_peak, loc_s_peak, window_length,band = 'qrs'):
    qrswave_entropy = {}

    # 提取QRS间期
    qrs_waves = signal_channels[:, loc_q_peak:loc_s_peak+1]
    # 提取香农熵特征
    shannon_tempt_all = 0
    shannon_max = 0
    shannon_min = 9999
    for ii in range(qrs_waves.shape[0]):
        qrs_wave_channel = qrs_waves[ii, :]
        shannon_tempt = shannon_entropy(qrs_wave_channel, 0, 1, 20)
        shannon_tempt_all += shannon_tempt
        if shannon_max < shannon_tempt:
            shannon_max = shannon_tempt
        if shannon_min > shannon_tempt:
            shannon_min = shannon_tempt
    qrswave_entropy[f"{band}_shannon_entropy_all"] = shannon_tempt_all
    qrswave_entropy[f"{band}_shannon_entropy_max"] = shannon_max
    qrswave_entropy[f"{band}_shannon_entropy_min"] = shannon_min
    qrswave_entropy[f"{band}_shannon_entropy_mean"] = shannon_tempt_all / qrs_waves.shape[0]
    # 提取基尼系数
    mean_gini, std_gini, gini_mat = gini_coef(qrs_waves, window_length)
    struct_tempt = statistic_analysis(gini_mat, f'{band}_gini_mat')
    qrswave_entropy = build_dict(qrswave_entropy, struct_tempt)

    # 提取奇异值分解熵
    qrswave_entropy[f'{band}_svd_entropy'] = svd_entropy(qrs_waves)

    return qrswave_entropy

def func_frequency_feature_qrs(signal_channels, loc_q_peak, loc_r_peak, loc_s_peak, band='qrs'):
    qrswave_freq = {}

    # 提取QRS间期
    epsilon = 1e-10
    qrs_waves = signal_channels[:, loc_q_peak:loc_s_peak+1] + epsilon

    # 初始化列表来存储特征
    features_dict = {
        'energy': [],
        'power': [],
        'ratio': [],
        'skewness': [],
        'kurtosis': []
    }

    for i in range(qrs_waves.shape[0]):
        t_wave_channel = qrs_waves[i, :]
        coeffs = pywt.wavedec(t_wave_channel, 'db4')

        # 动态解包小波系数
        cA = coeffs[0]
        details = coeffs[1:]
        cD1, cD2, cD3, cD4 = (details + [None] * 4)[:4]

        # 对于 cA 和每个 cD，我们都提取特征
        for idx, coeff in enumerate([cA, cD1, cD2, cD3, cD4], 1):
            if coeff is not None:
                # 重构信号
                xrec = pywt.waverec([coeff] + [None] * (len(coeffs) - idx), 'db4')
                energy = np.sum(coeff ** 2)
                power = np.sqrt(np.sum(xrec ** 2))
                ratio = power / np.sqrt(np.sum(t_wave_channel ** 2))
                features_dict['energy'].append(energy)
                features_dict['power'].append(power)
                features_dict['ratio'].append(ratio)
                # 新增的统计特征
                features_dict['skewness'].append(skew(coeff))
                features_dict['kurtosis'].append(kurtosis(coeff))

    # 封装特征提取和归一化的步骤
    for feature_name, data in features_dict.items():
        prefix = f'{band}_ch_{feature_name}_a4'
        qrswave_freq.update(extract_normalized_features(data, prefix))
    return qrswave_freq

def extract_normalized_features(data, prefix):
    data_sorted = np.sort(data)
    min_value = np.percentile(data_sorted, 10)-0.000001
    max_value = np.percentile(data_sorted, 90)+0.000001
    normalized_data = (data - min_value) / (max_value - min_value)
    return {
        f'{prefix}_mean': np.mean(normalized_data),
        f'{prefix}_max': np.max(normalized_data),
        f'{prefix}_min': np.min(normalized_data)
    }





