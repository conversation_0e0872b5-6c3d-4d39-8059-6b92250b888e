'''
@Project ：pythonProject 
@File    ：source_recon.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/9/3 14:12
用于TT段在心脏表面进行还原，并对还原的位置进行着色
'''
import meshio
import multiprocessing
from .fun_colletion import *
class SourceRecon:
    def     __init__(self, heart_path, time_loc, mcg_data):
        """
        :param dl:
        :param d_z_max:
        :param bz:
        :param torso: 人体躯体模型
        """
        self.dl = np.arange(0, 0.21, 0.04)
        self.d_z_max, self.lf_cav = (176.6 + 40) / 1000, None
        self.heart_path = heart_path
        self.num_layers, self.Length, self.width = 233, 139.8, 176.6
        self.heart_points, self.heart_tri, self.heart_treta = None, None, None
        self.Q, self.R, self.To, self.Tp, self.Te = time_loc[0], time_loc[1], time_loc[2], time_loc[3], time_loc[4]
        self.mcg_data = mcg_data
        self.u_lead_lcav = None
        self.mcg_data_interpolate = None  # mcg_interpolate(self.mcg_data)  # 心磁的插值数据
        self.tran_x, self.tran_y = None, None
        self.heart_sur_points = None
        self.move_x, self.move_y, self.move_z = None, None, None

    def get_mesh(self):
        """
        :return: 获取左心室的点集及网格
        """
        # 获取心脏的CTA数据的长宽及层数
        heart_mesh = meshio.read(self.heart_path, file_format='gmsh')  # 表面及内部网格
        self.heart_points = np.asarray(heart_mesh.points * 200)
        self.heart_tri = np.asarray(heart_mesh.cells_dict['triangle'])

    def model_heart(self):
        """
        该程序和cta_mcg不同，用的是模拟心脏
        :param mesh_path:
        :param save_path:
        :return: 给出最后平移完的心脏坐标，使得CTA数据的坐标和MCG的坐标对齐
        """
        self.d_z_max = (self.width + 40) / 1000  # 心磁仪到心脏的距离
        # 真实心脏的伸缩与位置校准
        self.heart_points[:, 0] = self.heart_points[:, 0] / 512 * self.width
        self.heart_points[:, 1] = self.heart_points[:, 1] / 512 * self.width
        self.heart_points[:, 2] = self.heart_points[:, 2] / self.num_layers * self.Length
        self.heart_points = self.heart_points[:, [1, 2, 0]]

        # CT真实心脏点坐标转换成MCG下心脏点坐标
        self.tran_x_y_fun()  # 计算磁图对应心尖的位置
        # 为了使得对应的位置可以是心脏的室间隔靠近心尖的位置, s.t.心脏需要再往有平移20mm, 向下平移20mm
        self.tran_x = self.tran_x + 20
        self.tran_y = self.tran_y - 20
        # 计算心脏心尖的位置
        self.location = self.heart_tip()
        self.move_x = - self.location[0] + self.tran_x
        self.move_y = - self.location[1] + self.tran_y
        self.move_z = - 49.52 + 57.40
        self.heart_points[:, 0] = self.heart_points[:, 0] + self.move_x
        self.heart_points[:, 1] = self.heart_points[:, 1] + self.move_y
        self.heart_points[:, 2] = self.heart_points[:, 2] + self.move_z
        self.heart_points = self.heart_points / 1000
        self.heart_sur_points = self.heart_points[0:np.max(self.heart_tri) + 1, :]  # 取表面的点集

    def heart_tip(self):
        """
        :return: 选取心脏的心尖
        """
        index, distance = 0, 0
        standard_point = np.array([0, self.width, 0])
        max_x = np.max(self.heart_points[:, 0])
        for point_i in range(self.heart_points.shape[0]):
            point = self.heart_points[point_i, :]
            distance_2 = np.abs(self.heart_points[point_i][0] - max_x)
            distance_1 = np.linalg.norm(standard_point - point[0:3])
            if distance_1 > distance and distance_2 < 15:  # 距离标准点最远并且里左右边的x距离小于15
                distance = distance_1
                point_index = index
            index += 1
        location = np.hstack((self.heart_points[point_index, :], point_index))
        return location

    def tran_x_y_fun(self):
        """
        :return: 选取磁图上 Q,R时刻最靠近右下的点源，作为与心脏模型对准的标志
        """
        qr_range = np.arange(self.Q, self.R)
        text = np.zeros((len(qr_range), 3))
        x_1 = np.mgrid[0.2:20.1: 0.2]
        y_1 = np.mgrid[0.2:20.1:0.2]
        [xi, yi] = np.meshgrid(x_1, y_1)
        self.mcg_data_interpolate = mcg_interpolate(self.mcg_data[self.Q:self.R, :])
        for k in range(self.R - self.Q):  # 所有时刻点
            b = self.mcg_data_interpolate[k] * 10 ** (-12)
            index_x, index_y = magnetic_gradient(b, xi, yi)
            text[k][0] = k
            text[k][1] = index_x * 10
            text[k][2] = index_y * 10
        text_min_index = np.argmin(text[:, 2])
        while text_min_index <= len(qr_range) - 2:
            if (text[text_min_index][2] == text[text_min_index + 1][2] and
                    text[text_min_index + 1][1] > text[text_min_index][1]):
                text_min_index += 1
            else:
                if text[text_min_index][1] < np.max(text[:, 1]) - 20:
                    self.tran_x, self.tran_y = np.max(text[:, 1]) - 10, text[text_min_index][2]
                    return self.tran_x, self.tran_y
                    break
                else:
                    self.tran_x, self.tran_y = text[text_min_index][1], text[text_min_index][2]
                    return self.tran_x, self.tran_y
                    break
        self.tran_x, self.tran_y = text[text_min_index][1], text[text_min_index][2]

    def get_lf(self):
        """
        :return: 整个左心室的lf函数
        """
        self.lf_cav = np.zeros((36, self.heart_sur_points.shape[0] * 2))
        self.u_lead_lcav = np.zeros((36, self.heart_sur_points.shape[0] * 2))
        t_1 = dev_lead_field_1(self.heart_sur_points, self.dl, self.d_z_max)
        t_2 = dev_lead_field_1(self.heart_sur_points, self.dl, self.d_z_max + 0.05)
        t_3 = dev_lead_field_1(self.heart_sur_points, self.dl, self.d_z_max + 0.10)
        t = t_1 - 2 * t_2 + t_3
        for i in range(self.heart_sur_points.shape[0]):
            lf = t[i, :, :]
            self.lf_cav[:, 2 * i: 2 * i + 2] = lf[:, 0:2]
            u_lead, s_lead, vt_lead = np.linalg.svd(self.lf_cav[:, 2 * i: 2 * i + 2])
            self.u_lead_lcav[:, 2 * i: 2 * i + 2] = u_lead[:, 0:2]

    def rap_music(self, b):
        """
        主要针对整个左心室， 使两个点相关性大于0.75的点去除, 取多个点源
        :param b: 磁场 b
        :param ii: 时刻 ii
        :return: 对于单点的室间隔点源还原
        """
        b_1 = b.reshape((36, 1))
        signal_subspace = sig_subspace(b_1)  # 求出磁场b_1的特征向量
        if self.lf_cav is None:
            self.get_lf()

        corr = signal_subspace.T @ self.u_lead_lcav
        corr_all = np.sqrt(corr[0, 0::2] ** 2 + corr[0, 1::2] ** 2)
        max_index = np.argmax(corr_all)
        pro_matrix = self.lf_cav[:, 2 * max_index: 2 * max_index + 2]
        max_miu, max_x1 = sub_corr_1(pro_matrix, signal_subspace)
        cur_dir = max_x1.reshape(1, 2)
        pro_matrix_1 = pro_matrix @ max_x1.reshape(2, 1)
        intensity = pro_matrix_1.T @ b / norm(pro_matrix_1) ** 2
        point = np.array([[self.heart_sur_points[max_index][0], self.heart_sur_points[max_index][1],
                           self.heart_sur_points[max_index][2]]])
        result_1 = [point[0][0], point[0][1], point[0][2]]
        return result_1


    def source_strength_tt(self):
        """
        :param save_path:
        :return: 直接进行多点源计算，并给出指标, 对于tt而言
        """
        t_start, t_end = round((self.To + self.Tp) / 2), round((self.Tp + self.Te) / 2 + 1)
        time_tt = list(range(t_start, t_end))
        result = []
        for ii in time_tt:
            b_mat = (-self.mcg_data[ii, :] * 10 ** (-12)).reshape(6, 6)
            b_mat_1 = b_mat[::-1]
            b = b_mat_1.flatten()
            result_1 = self.rap_music(b)
            result.append(result_1)

        "计算指标"
        point = np.array(result)
        "简单计算 Tp位置y轴的指标及2D离散度的指标"
        mean_point_2d = np.mean(point[:, 0:2] * 1000, axis=0)  # 计算均值
        dis_2d = np.linalg.norm(point[:, 0:2] * 1000 - mean_point_2d, axis=1)
        all_var_2d = np.mean(dis_2d ** 2)
        all_std_dev_2d = np.sqrt(all_var_2d)
        print(all_std_dev_2d)

        "用于验证"
        # scalar = np.ones(self.heart_sur_points.shape[0])
        # index = 0
        # for i in result:
        #     scalar[(i[1])] = 0  # index / (result[-1][0] - result[0][0] + 1)
        #     index += 1
        #
        # mlab.figure(bgcolor=(24 / 255, 23 / 255, 23 / 255))
        # surf = mlab.triangular_mesh(self.heart_sur_points[:, 0] * 1000, self.heart_sur_points[:, 1] * 1000,
        #                             self.heart_sur_points[:, 2] * 1000,
        #                             self.heart_tri, colormap='jet', scalars=scalar)  # , representation='wireframe')
        # lut = surf.module_manager.scalar_lut_manager.lut.table.to_array()
        # lut[0, 0:4] = np.array([255, 0, 0, 255])  # (red, green, blue, alpha), alpha是透明度
        # lut[-1, 0:4] = np.array([221, 221, 221, 255])
        #
        # surf.module_manager.scalar_lut_manager.lut.table = lut
        # mlab.view(0, 0)
        # mlab.show()
        return result



