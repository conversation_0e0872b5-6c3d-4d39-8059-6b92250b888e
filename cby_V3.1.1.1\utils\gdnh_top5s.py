"""
Author: b<PERSON><PERSON>chen
email: <EMAIL>

file: 
date: 2024/01/24 13:46
desc: 广东南海top5
"""


from utils.utils import *
import os
import datetime
import shutil


def gdnh_top5(ysb, ctrd_qr, ctrd_rs, root_path):
    '''广东南海top5指标计算, 输出结果文档'''
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    unzip_file(ysb, tardir)
    rootdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    for item in os.listdir(rootdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (rootdir, item)
    ids, timelis = get_idstms(biao1, 'Sheet1')
    top5s = 'id, TTarea, QR-rc123, RS-rc123, QRa, Sa'
    top5file = '%s/GDNHtop5_%d_%d%02d%02d%02d%02d%02d.txt' % (root_path, len(ids), now.year, now.month, now.day, now.hour, now.minute, now.second)
    for i11 in range(len(ids)):
        mfm_file = '%s/%s.txt' % (rootdir, ids[i11])
        doc = open(mfm_file, 'r')
        mcglines = doc.readlines()
        doc.close()
        Qp, Te = timelis[i11][0], timelis[i11][5]
        matrixes, mf = cv_interpolate(mfm_file, Qp, min(Te, len(mcglines)-10))
        top5 = get_mfm_mts_top5(timelis[i11], matrixes, mf, Zqr, Zrs, mcglines)
        top5s += '\n%s, %.3f, %.3f, %.3f, %.1f, %.1f' % (ids[i11], top5[0], top5[1], top5[2], top5[3], top5[4])
    write_str(top5s, top5file)
    shutil.rmtree(tardir)
    return top5file


def gdnh_top5_raw(ysb, ctrdfile, root_path):
    '''广东南海top5指标计算(原始代码的流程), 输出结果文档20240123'''
    # now = datetime.datetime.now()
    # tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    tardir = '%s/jys_folder' % root_path
    # unzip_file(ysb, tardir)
    rootdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    for item in os.listdir(rootdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (rootdir, item)
    ids, timelis = get_idstms(biao1, 'Sheet1')
    matrixes_list, mflis, mcglines_list = [], [], []
    i11 = 0
    # for i11 in range(len(ids)):
    mfm_file = '%s/%s.txt' % (rootdir, ids[i11])
    doc = open(mfm_file, 'r')
    lines = doc.readlines()
    doc.close()
    mcglines_list.append(lines)
    Qp, Te = timelis[i11][0], timelis[i11][5]
    matrixes, mf = cv_interpolate(mfm_file, Qp, Te)
    matrixes_list.append(matrixes)
    mflis.append(mf)
    # # # 计算QR指标
    # print('QR...')
    # timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles = get_mfm_mts_QR(timelis, matrixes_list, mflis, ctrdfile)
    # 计算RS指标
    print('RS...')
    timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles = get_mfm_mts_RS(timelis, matrixes_list, mflis, ctrdfile)
    # print(mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles)
    # 计算TT指标
    # print('TT...')
    # mpfiles = get_mfm_mts_TT0(timelis, matrixes_list, mcglines_list)
    print(cdfiles)
    # 之后类似gdnh_top5进行指标计算..
    top5file = '%s_top5metrics.txt' % tardir
    return top5file


def get_mfm_mts_top5(timelis0, matrixes, mf, Zqr, Zrs, mcglines):
    '''广东南海top指标: TTarea, QR-rc123, RS-rc123, QRa, Sa'''
    top5 = []
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    pnptm2 = cal_pnptm(matrixes, mf, range(q_peak-5, r_peak+1))
    q_peak = change_QR(pnptm2, q_peak)  # Q异常修正
    alist, cxylist = [], []
    for j in range(q_peak, r_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        px, py, nx, ny = get_dipole(matrix)
        alist.append(calangle(ny-py, px-nx))  # 角度
        cxylist.append([(px+nx)/2+1, (py+ny)/2+1])  # 中心点坐标和坐标
    lis = statctp(cxylist, 0, 1)
    ns = statnums(Zqr, lis)
    QRrc123 = (ns[1]+2*ns[2]+3*ns[3]) / ns[4]
    QRa, q = cal_qra_q(alist)
    pnptm2 = cal_pnptm(matrixes, mf, range(r_peak, s_peak+6))
    s_peak = change_RS(pnptm2, s_peak)  # S异常修正
    cxylist = []
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        px, py, nx, ny = get_dipole(matrix)
        cxylist.append([(px+nx)/2+1, (py+ny)/2+1])  # 中心点坐标和坐标
        if j == s_peak:
            Sa = calangle(ny-py, px-nx)
    lis = statctp(cxylist, 0, 1)
    ns = statnums(Zrs, lis)
    RSrc123 = (ns[1]+2*ns[2]+3*ns[3]) / ns[4]
    tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end)
    Mf = calmf(matrixes, tt1, tt2)
    flist = []
    for jj in range(tt1, tt2 + 1):
        matrix = matrixes[jj-1]
        Nf = matrix.max() - matrix.min()  # 幅值
        px, py, nx, ny = get_dipole(matrix)
        fx, fy = calptcors(Nf/Mf, ny-py, px-nx)  # 面积-心磁坐标
        flist.append([fx, fy])
    TTarea = calarea(flist, 0, 1)
    TTarea, QRrc123, RSrc123, QRa = get_round([TTarea, QRrc123, RSrc123, QRa], [3, 3, 3, 1])
    top5 = [TTarea, QRrc123, RSrc123, QRa, Sa]
    return top5
