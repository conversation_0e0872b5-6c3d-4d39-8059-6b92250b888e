# ML_Pipeline_2508 重构版本依赖包
# 核心依赖

# 数据处理和科学计算
numpy==1.26.4
pandas>=1.5.0
scipy>=1.9.0

# 机器学习
scikit-learn>=1.1.0
xgboost>=1.6.0
shap>=0.41.0

# 信号处理和特征工程
PyWavelets>=1.4.0
statsmodels>=0.13.0
antropy>=0.1.9

# 图像处理（用于时频图像特征）
opencv-python>=4.6.0
Pillow>=9.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 文件处理
openpyxl>=3.0.0
xlrd>=2.0.0

# 进度条
tqdm>=4.64.0

# 其他工具
cloudpickle>=2.0.0
joblib>=1.1.0

# 深度学习
# torch>=1.12.0
# torchvision>=0.13.0

# 可选依赖（用于特定功能）
# numba>=0.56.0  # 用于加速计算
# dask>=2022.8.0  # 用于大数据处理

# 开发和测试依赖（可选）
# pytest>=7.0.0
# pytest-cov>=3.0.0
# black>=22.0.0
# flake8>=4.0.0

# 注意事项：
# 1. numpy版本固定为1.26.4以解决ci特征生成的兼容性问题
# 2. 其他版本号为最低要求，可以使用更高版本
# 3. 某些包可能已经作为其他包的依赖自动安装
# 4. 根据实际部署环境可能需要调整版本号

# 安装命令：
# pip install -r requirements.txt

# 或者使用conda环境：
# conda activate Algorithm_Union
# pip install -r requirements.txt
