'''
@Project ：002_Code_envs 
@File    ：特征选择.py
@IDE     ：PyCharm 
<AUTHOR> Qu
@Date    ：2023/10/10 9:15 
@Discribe：
目前的特征：
        1.长轴长
        2.短轴长
        3.长轴和短轴的夹角
        4.周长
        5.面积
        6.极值点坐标(x,y)
        7.最大内接圆面积
        8.内接圆圆心坐标(x,y)
        9.磁场强度
        10.每个点到极子的距离
        11.每一帧的最大/小值
        12.每一帧大/小于0的个数
        13.ST段相对高度
        14.PT间隔、PQ间隔、QR、RS、TT、QRS、RT距离、RT峰值时刻磁图相似度
'''

import logging

from scipy.ndimage import uniform_filter

from feature_generator.utils.calculate_params import *
from feature_generator.utils.writemp import statpn

logging.basicConfig(level=logging.INFO)
# 创建一个自定义的颜色映射
colors = [(0, 'blue'), (0.5, 'white'), (1, 'red')]  # R -> G -> B
n_bins = 100  # Discretizes the interpolation into bins
cmap_name = 'custom1'

import numpy as np
from typing import Dict, List, Tuple, Optional


def read_file_plot3(mfm_fine, data, part, w_quantiles=[0.7, 0.5,0.3]):
    """
    改进后的特征提取函数
    :param mfm_fine: 已全局归一化的单帧数据 (100, 100)
    :param data: 原始数据（用于物理量计算）
    :param part: 部位标识符(如tt段)
    :param w_quantiles: 阈值分位数列表（默认[0.7, 0.5,0.3]）
    """
    # ================== 初始化输出结构 ==================
    out = {'pos': {}, 'neg': {}, 'params': {}}

    # ================== 基础特征计算 ==================
    # 物理参数计算（移除经验系数）
    out['params'].update({
        f'{part}_max_mcg_value': np.max(data),
        f'{part}_min_mcg_value': np.min(data),
        f'{part}_mean_mcg_value': np.mean(data),
        f'{part}_points_large': np.sum(data > 0),
        f'{part}_points_small': np.sum(data < 0)
    })

    # ================== 动态阈值计算（正负分离） ==================
    pos_data = mfm_fine[mfm_fine > 0]
    neg_data_abs = np.abs(mfm_fine[mfm_fine < 0])

    pos_w = [np.quantile(pos_data, q) for q in w_quantiles] if len(pos_data) > 0 else [0]*len(w_quantiles)
    neg_w = [np.quantile(neg_data_abs, q) for q in w_quantiles] if len(neg_data_abs) > 0 else [0]*len(w_quantiles)

    # ================== 极坐标特征 ==================
    # 计算中心点（假设磁场中心在图像中心）
    center = (mfm_fine.shape[0]//2, mfm_fine.shape[1]//2)
    polar_img = cv2.warpPolar(mfm_fine, (100, 360), center, 50,
                             cv2.WARP_POLAR_LINEAR)
    out['params'][f'{part}_polar_std'] = np.std(polar_img)

    # ================== 空间梯度特征 ==================
    gx = cv2.Sobel(mfm_fine, cv2.CV_64F, 1, 0, ksize=3)
    gy = cv2.Sobel(mfm_fine, cv2.CV_64F, 0, 1, ksize=3)
    out['params'].update({
        f'{part}_gradient_magnitude': np.sqrt(gx**2 + gy**2).mean(),
        f'{part}_vorticity': np.mean(gx - gy)
    })
    # 在梯度计算后增加曲率
    gyy, gxx = np.gradient(gy), np.gradient(gx)
    gxy = np.gradient(mfm_fine, axis=1)

    curvature = (gxx * gy ** 2 - 2 * gx * gy * gxy + gyy * gx ** 2) / (gx ** 2 + gy ** 2+ 1e-6) ** 1.5
    out[f'{part}_curvature_mean'] = np.nanmean(curvature)  # 忽略除零异常
    # 梯度幅值的标准差 - 反映梯度变化的一致性
    gradient_magnitude = np.sqrt(gx ** 2 + gy ** 2)
    out['params'][f'{part}_gradient_std'] = np.std(gradient_magnitude)

    # 梯度方向的一致性
    gradient_direction = np.arctan2(gy, gx)
    gradient_direction_hist = np.histogram(gradient_direction, bins=8, range=(-np.pi, np.pi))[0]
    gradient_direction_entropy = -np.sum((gradient_direction_hist / gradient_direction_hist.sum() + 1e-6) *
                                         np.log(gradient_direction_hist / gradient_direction_hist.sum() + 1e-6))
    out['params'][f'{part}_gradient_direction_entropy'] = gradient_direction_entropy
    # 使用滑动窗口计算局部方差
    window_size = 5  # 可调整
    local_mean = uniform_filter(mfm_fine, size=window_size)
    local_sqr_mean = uniform_filter(mfm_fine ** 2, size=window_size)
    local_variance = local_sqr_mean - local_mean ** 2

    # 局部方差的统计特征
    out['params'].update({
        f'{part}_local_variance_mean': np.mean(local_variance),
        f'{part}_local_variance_std': np.std(local_variance)  # 反映纹理均匀性
    })
    # ================== 核心特征提取循环 ==================
    default_zero = {
        f"{part}_mcg_max_length": 0,
        f"{part}_mcg_min_length": 0,
        f"{part}_mcg_s": 0,
        f"{part}_mcg_perimeter": 0,
        f"{part}_angle": 0,
        f"{part}_value": 0,
        f"{part}_inner_circle_x": 0,
        f"{part}_inner_circle_y": 0,
        f"{part}_inner_circle_s": 0,
        f"{part}_mcg_sum": 0,
        f"{part}_dist_sum": 0,
        f"{part}_k": 0,
        f"{part}_circle_range": 0,
        f'{part}_l_x': 0,
        f'{part}_l_y': 0,
        f'{part}_r_x': 0,
        f'{part}_r_y': 0,
        f'{part}_t_x': 0,
        f'{part}_t_y': 0,
        f'{part}_d_x': 0,
        f'{part}_d_y': 0,
        f'{part}_circle_radius': 0,
        f'{part}_circle_area': 0,
        f'{part}_circle_center_x': 0,
        f'{part}_circle_center_y': 0,
        f'{part}_rect_area': 0,
        f'{part}_rect_center_x': 0,
        f'{part}_rect_center_y': 0
    }
    try:
        for w_tagi, (w_pos, w_neg) in enumerate(zip(pos_w, neg_w)):
            w_tag = w_quantiles[w_tagi]
            # 生成正负区独立掩码
            mask_pos = (mfm_fine > w_pos)
            mask_neg = (mfm_fine < -w_neg)
            mfm_filtered = np.where(mask_pos | mask_neg, mfm_fine, 0)

            # 电流特征
            max_magni, max_angle, max_x, max_y = get_current(mfm_filtered)
            out['params'].update({
                f'{part}_max_magni_{w_tag}': max_magni,
                f'{part}_max_angle_{w_tag}': max_angle,
                f'{part}_max_x_position_{w_tag}': max_x,
                f'{part}_max_y_position_{w_tag}': max_y
            })

            # 极子提取
            pn = [
                process_poles(statpn(mfm_filtered, vmin=w_pos), mfm_filtered)[0] if w_pos > 0 else [],
                process_poles(statpn(mfm_filtered, vmin=w_neg), mfm_filtered)[1] if w_neg > 0 else []
            ]
            # 区域特征提取
            for region_idx, poles in enumerate(pn):
                # 初始化位置和特征
                region_type  = 'pos' if region_idx == 0 else 'neg'
                region_features = {'primary': default_zero.copy(), 'secondary': default_zero.copy()}
                positions = {'primary': (0,0), 'secondary': (0,0)}
                pole_features = []

                # 处理前两个极子（最多两个）
                for pole_idx, pole in enumerate(poles[:2]):
                    if not pole:
                        pole_features.append(default_zero)
                        continue

                    # 解析极子信息
                    threshold, x, y = pole[0], int(pole[1])-1, int(pole[2])-1

                    # 区域检测
                    region = dfs_optimized(mfm_filtered, x, y, threshold, w=w_tag)

                    # 快速生成特征矩阵
                    region_mask = np.zeros_like(mfm_filtered)
                    dist = 0
                    for rx, ry in region:
                        region_mask[int(rx), int(ry)] = mfm_filtered[int(rx), int(ry)]
                        dist += distance(rx, ry, x, y)
                        # dist += np.hypot(rx-x, ry-y)  # 替换distance函数

                    # 跳过小区域
                    mcg_s = get_area(region_mask)
                    mcg_sum = np.sum(region_mask)
                    if mcg_s < 4:
                        pole_features.append(default_zero)
                        continue

                    # 几何特征
                    mcg_max_length, mcg_min_length, angle = get_longest_shortest_line(region_mask, x, y)# 长轴短轴角度
                    mcg_perimeter, k, l, r, t, d, *shape_params= get_perimeter(
                        region_mask)  # 外接图形参数
                    circle_radius, circle_area, circle_center, rect_area, rect_center = shape_params[:5]
                    inner_circle_x, inner_circle_y, inner_circle_s = find_largest_inscribed_circle(np.abs(region_mask)) #内接图形参数

                    # 纹理特征
                    # 提取region对应的原始强度值，缩放到0-255
                    # region_data = mfm_filtered * region_mask  # region_mask为二值化区域
                    # region_normalized = cv2.normalize(region_data, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

                    # 计算GLCM（距离=1，角度=0度）
                    # glcm = greycomatrix(region_normalized, distances=1, angles=[0, np.pi/4, np.pi/2, 3*np.pi/4], levels=256, symmetric=True, normed=True)
                    # contrast = greycoprops(glcm, 'contrast')[0, 0]
                    # energy = greycoprops(glcm, 'energy')[0, 0]  # 补充能量特征

                    # 构建特征字典
                    feature = {f"{part}_mcg_max_length": mcg_max_length,
                               f"{part}_mcg_min_length": mcg_min_length,
                               f"{part}_mcg_s": mcg_s,
                               f"{part}_mcg_perimeter": mcg_perimeter,
                               f"{part}_angle": angle,
                               f"{part}_value": threshold,
                               f"{part}_inner_circle_x": inner_circle_x,
                               f"{part}_inner_circle_y": inner_circle_y,
                               f"{part}_inner_circle_s": inner_circle_s,
                               f"{part}_mcg_sum": mcg_sum,
                               f"{part}_dist_sum": dist,
                               f"{part}_k": k,
                               f"{part}_circle_range": (4 * np.pi) * mcg_s / (mcg_perimeter ** 2),
                               f'{part}_l_x': l[0],
                               f'{part}_l_y': l[1],
                               f'{part}_r_x': r[0],
                               f'{part}_r_y': r[1],
                               f'{part}_t_x': t[0],
                               f'{part}_t_y': t[1],
                               f'{part}_d_x': d[0],
                               f'{part}_d_y': d[1],
                               f'{part}_circle_radius': circle_radius,
                               f'{part}_circle_area': circle_area,
                               f'{part}_circle_center_x': circle_center[0],
                               f'{part}_circle_center_y': circle_center[1],
                               f'{part}_rect_area': rect_area,
                               f'{part}_rect_center_x': rect_center[0],# if isinstance(rect_center, (tuple, list)) else rect_center,
                               f'{part}_rect_center_y': rect_center[1],# if isinstance(rect_center, (tuple, list)) else rect_center,
                               # f'{part}_rect_center_x': circle_center[0],
                               # f'{part}_rect_center_y': circle_center[1]
                               # f'{part}texture_contrast': contrast, f'{part}texture_energy': energy
                               }

                    # 存储主/次极子特征
                    if pole_idx == 0:
                        region_features['primary'] = feature
                        positions['primary'] = (x, y)
                    else:
                        region_features['secondary'] = feature
                        positions['secondary'] = (x, y)

                # 存储特征（保证始终有两个条目）
                # 写入输出结构
                out[region_type].update({
                    f'{part}_{region_type}_{w_tag}_0': region_features['primary'],
                    f'{part}_{region_type}_{w_tag}_1': region_features['secondary']
                })
                out['params'].update({
                    f'{part}_{region_type}_0_x': positions['primary'][0],
                    f'{part}_{region_type}_0_y': positions['primary'][1],
                    f'{part}_{region_type}_1_x': positions['secondary'][0],
                    f'{part}_{region_type}_1_y': positions['secondary'][0]
                })
    except Exception as e:
        print(f"Error occurred during feature extraction: {e}")
    return out

def replace_key(d, target_substring, new_key):
    """递归地替换包含特定子字符串的所有键。

    参数:
    - d: 字典或列表，要进行键替换的对象。
    - target_substring: 目标子字符串，如果键名包含它，则该键名需要被替换。
    - new_key: 新的键名。
    """
    if isinstance(d, dict):
        # 创建一个新的字典来存储修改后的键值对
        new_dict = {}
        for key, value in d.items():
            # 如果键名包含目标子字符串，则替换该子字符串
            new_key_name = key.replace(target_substring, new_key) if target_substring in key else key
            # 递归处理值
            new_dict[new_key_name] = replace_key(value, target_substring, new_key)
        return new_dict
    elif isinstance(d, list):
        # 对列表中的每个元素递归应用相同的逻辑
        return [replace_key(item, target_substring, new_key) for item in d]
    else:
        # 非字典和列表类型直接返回原值
        return d


def split_result(result, num_chunks):
    """
    将二维数组result按照指定的块数等分，确保每列都被包含。

    参数:
    - result: 要分割的二维数组。
    - num_chunks: 分割成的块数。

    返回:
    - 分割后的块列表。
    """
    total_columns = result.shape[1]  # 获取总列数
    chunk_base_size = total_columns // num_chunks  # 计算每块的基本列数
    extra_columns = total_columns % num_chunks  # 需要额外分配的列数

    chunks = []
    start = 0
    for i in range(num_chunks):
        # 对于前extra_columns个块，每个增加一列
        end = start + chunk_base_size + (1 if i < extra_columns else 0)
        chunks.append(result[:, start:end])
        start = end

    return chunks



def extract_features_from_pic(arr, part, w_list=[0.7,0.5,0.3]):
    """单帧内插值"""
    n_cols = arr.shape[1]
    arr_all = [None] * n_cols
    for i in range(n_cols):
        # 对每一帧的图像提取特征
        tmp_data = arr[:, i].reshape(6, 6)
        tmp_data_resized = cv2.resize(tmp_data, (100, 100), interpolation=cv2.INTER_CUBIC)
        processed_data = read_file_plot3(tmp_data_resized, tmp_data, part, w_list)
        # 将结果保存到 arr_all 中对应的位置
        arr_all[i] = processed_data
    return arr_all


def temporal_interp_data(signal_channels, start_times, end_times, threshold):
    """
    在时间轴上对信号插值，获得等长的数据
    :param signal_channels:
    :param start_times:
    :param end_times:
    :param threshold:
    :return:
    """
    temp = signal_channels[:, start_times:end_times]
    if (end_times - start_times) < threshold:
        return interpolate_array(temp, threshold)
    elif (end_times - start_times) > threshold:
        return largest_triangle_three_buckets(temp, threshold)
    return temp


if __name__ == '__main__':
    pass
