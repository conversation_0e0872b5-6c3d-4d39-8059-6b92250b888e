'''
@Project ：pythonProject 
@File    ：main_squid_opm_method.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/11/12 15:25
主要用于，通过心磁图转换成固定的19个点位上的电流源活动，来还原出心磁向量图
'''
import math
import numpy as np
# import time
from numpy.linalg import *
import matplotlib.pyplot as plt
pi = math.pi


class SquidtransCurrent:
    def __init__(self, point):
        # a+b+c+d, a为杜瓦带探测表面的距离,b为探测表面至躯体的距离, c为躯体至心脏， d为心脏至心脏中心
        self.z_opm = (1.2 + 1 + 4 + 3) / 100
        self.z_squid = (3 + 1 + 4 + 3) / 100
        self.dl = np.arange(0, 0.21, 0.04)
        self.point = point
        self.lf_squid, self.lf_opm = None, None
        self.mcg_data = None

    def get_lf(self):
        if self.lf_squid is None:
            self.lf_squid = np.zeros((36, self.point.shape[0] * 2))
            self.lf_opm = np.zeros((36, self.point.shape[0] * 2))
            t_1 = SquidtransCurrent.dev_lead_field_1(self.point, self.dl, self.z_squid)
            t_2 = SquidtransCurrent.dev_lead_field_1(self.point, self.dl, self.z_squid + 0.05)
            t_3 = SquidtransCurrent.dev_lead_field_1(self.point, self.dl, self.z_squid + 0.10)
            t_squid = t_1 - 2 * t_2 + t_3
            t_opm = SquidtransCurrent.dev_lead_field_1(self.point, self.dl, self.z_opm)
            for i in range(self.point.shape[0]):
                lf_squid, lf_opm = t_squid[i, :, :], t_opm[i, :, :]
                self.lf_squid[:, 2 * i: 2 * i + 2] = lf_squid[:, 0:2]
                self.lf_opm[:, 2 * i: 2 * i + 2] = lf_opm[:, 0:2]

    @staticmethod
    def jac_1(position, jac_x, jac_y, jac_z):  # 参数与上函数一致, 输出分别为均匀场和梯度
        """
        参数与上函数一致, 输出分别为均匀场和梯度
        :param position:
        :param jac_x:
        :param jac_y:
        :param jac_z:
        :param volume:
        :return: 计算磁场Bz在Qx和Qy方向上的梯度, 也可以看出导联场矩阵的一行
        """
        mu0 = 4 * pi * 10 ** (-7)
        r = ((jac_x - position[:, 0]) ** 2 + (jac_y - position[:, 1]) ** 2 + (jac_z - position[:, 2]) ** 2) ** 0.5

        diff_bz_qx = mu0 / (4 * pi * r ** 3) * (jac_y - position[:, 1])
        diff_bz_qy = -mu0 / (4 * pi * r ** 3) * (jac_x - position[:, 0])
        # diff_bz_qz = 0
        return diff_bz_qx, diff_bz_qy  # , diff_bz_qz

    @staticmethod
    def dev_lead_field_1(position, dl, z):  # 生成导联场矩阵, 这里l*l就是像素点个数, 可以作为已知磁场强度点的个数
        """
        生成导联场矩阵，这里应该是36*2
        :param position:
        :param z: MCG探测器所在的 z轴 位置
        :param dl: 将探测器横坐标0-200 分为l个点 ， dl为依次l个点的横坐标
        :return: 导联场矩阵 points.shape[0] * 36 * 3
        第一维度的个数为传入position的个数，为了快速计算一般为points.shape[0], 后面维度代表每个点的引导场矩阵
        磁图的记录与前面一样从左到右，从下到上
        2024/03/29验证无误
        """
        len_1 = len(dl)
        lf = np.empty((position.shape[0], len_1 * len_1, 2))
        for i in range(len_1):
            for j in range(len_1):
                diff_bz_qx, diff_bz_qy = SquidtransCurrent.jac_1(position, dl[j], dl[i], z)
                lf[:, len_1 * i + j, 0] = diff_bz_qx
                lf[:, len_1 * i + j, 1] = diff_bz_qy
        return lf

    def min_norm(self, b, alpha):
        ww = self.lf_squid.T @ self.lf_squid
        identity = np.eye(ww.shape[0])
        alpha = (np.min(np.abs(ww)) + np.max(np.abs(ww)))/10
        x = pinv(ww + alpha * identity) @ self.lf_squid.T @ b
        # x = inv(ww) @ self.lf_squid.T @ b
        return x

    def mnls_moore_Penrose_method(self, bch):
        """
        :param bch:
        :return: 本程序直接应用numpy自带的moore_Penrose_method自带的伪逆
        """
        ww = self.lf_squid.T @ self.lf_squid
        ww_pinv = pinv(ww)
        h = ww_pinv @ self.lf_squid.T
        q = h @ bch
        return q

    def worker(self, ii):
        b_mat = (-self.mcg_data[ii, :] * 10 ** (-12)).reshape(6, 6)  # 将36个变量的磁数据变为6*6的矩阵，矩阵第一行对应磁场最上面一行
        b_mat_1 = b_mat[::-1]  # 进行反转，s.t矩阵第一行对应磁场最底下一行，最后一行对应磁场最上方一行
        b = b_mat_1.reshape(36, 1)  # 再次展开，顺序为从左到右, 从下到上
        cur_dir = self.min_norm(b, 0.1)
        cur_dir_1 = cur_dir.reshape(self.point.shape[0], 2)
        result_1 = [ii, np.sum(cur_dir_1[:, 0]), np.sum(cur_dir_1[:, 1])]
        return result_1

    def squid_tran(self, squid_path, time):
        """
        :param squid_path: squid磁数据路径
        :param save_path: 存储地址
        :return:
        """
        # 读取心磁书记
        with open(squid_path, 'r') as ff:
            file = ff.read()  # 读取的是字符串
        da = []
        for i in file.split():  # 把上面字符串记为数字插入
            da.append(float(i))
        datat = []
        for i in range(int(len(da) / 37)):
            datat.append(da[37 * i + 1:37 * (i + 1)])
        self.mcg_data = np.array(datat)
        time_Q_S = list(range(time[0], time[4]))  # 从Qh ~ Sr
        result_1, result_2 = [], []

        "不使用并行运算"
        for ii in time_Q_S:
            b_mat = (-self.mcg_data[ii, :] * 10 ** (-12)).reshape(6, 6)  # 将36个变量的磁数据变为6*6的矩阵，矩阵第一行对应磁场最上面一行
            b_mat_1 = b_mat[::-1]  # 进行反转，s.t矩阵第一行对应磁场最底下一行，最后一行对应磁场最上方一行
            b = b_mat_1.reshape(36, 1)  # 再次展开，顺序为从左到右, 从下到上
            cur_dir = self.min_norm(b, 0.1)
            cur_dir_1 = cur_dir.reshape(self.point.shape[0], 2)
            result_1.append([ii, np.sum(cur_dir_1[:, 0]), np.sum(cur_dir_1[:, 1])])
        result_1 = np.array(result_1)
        # np.savetxt(save_path + '\\result_1.txt', result_1)
        "QRS波段作图"
        Q, R, S = time[1] - time_Q_S[0], time[2] - time_Q_S[0], time[3] - time_Q_S[0]
        fig, ax = plt.subplots(facecolor=(25 / 255, 25 / 255, 25 / 255))
        ax.patch.set_facecolor((25 / 255, 25 / 255, 25 / 255))
        Q_loc, R_loc, S_loc = result_1[Q, 1:3], result_1[R, 1:3], result_1[S, 1:3]
        ax.plot(result_1[0:R+1, 1], result_1[0:R+1, 2], color=(1, 0, 0), marker='.', linestyle='solid',
                linewidth=0.5, markersize=3)
        ax.plot(result_1[R:S+1, 1], result_1[R:S+1, 2], color=(0, 1, 0), marker='.', linestyle='solid',
                linewidth=0.5, markersize=3)
        ax.plot(result_1[S:, 1], result_1[S:, 2], color=(169/255, 169/255, 169/255), marker='.', linestyle='solid',
                linewidth=0.5, markersize=3)
        width = 3e-8
        grid = round((S - Q)/4)
        t_1, t_2, t_3 = Q + grid, Q + 2 * grid, Q + 3 * grid
        # 计算两个点之间的方向
        dx_1, dy_1 = result_1[t_1 + 1, 1] - result_1[t_1, 1], result_1[t_1 + 1, 2] - result_1[t_1, 2]
        dx_2, dy_2 = result_1[t_2 + 1, 1] - result_1[t_2, 1], result_1[t_2 + 1, 2] - result_1[t_2, 2]
        dx_3, dy_3 = result_1[t_3 + 1, 1] - result_1[t_3, 1], result_1[t_3 + 1, 2] - result_1[t_3, 2]
        # 计算两点的中点
        mx_1, my_1 = (result_1[t_1 + 1, 1] + result_1[t_1, 1]) / 2, (result_1[t_1 + 1, 2] + result_1[t_1, 2]) / 2
        mx_2, my_2 = (result_1[t_2 + 1, 1] + result_1[t_2, 1]) / 2, (result_1[t_2 + 1, 2] + result_1[t_2, 2]) / 2
        mx_3, my_3 = (result_1[t_3 + 1, 1] + result_1[t_3, 1]) / 2, (result_1[t_3 + 1, 2] + result_1[t_3, 2]) / 2
        len_1, len_2, len_3 \
            = np.sqrt(dx_1 ** 2 + dy_1 ** 2), np.sqrt(dx_2 ** 2 + dy_2 ** 2), np.sqrt(dx_3 ** 2 + dy_3 ** 2)
        # 根据向量图中的最大距离及三个时刻点的最小距离来确定箭头的大小
        dx_all_1, dy_all_1 = result_1[1:, 1] - result_1[0:-1, 1], result_1[1:, 2] - result_1[0:-1, 2]
        len_4 = np.sqrt(dx_all_1 ** 2 + dy_all_1 ** 2)
        len_all = (max(len_4) + min(len_1, len_2, len_3)) / 2
        dx_1, dy_1 = dx_1 / len_1 * len_all, dy_1 / len_1 * len_all  #保持两个方向向量的长短一致
        dx_2, dy_2 = dx_2 / len_2 * len_all, dy_2 / len_2 * len_all
        dx_3, dy_3 = dx_3 / len_3 * len_all, dy_3 / len_3 * len_all
        ax.arrow(mx_1 - dx_1 * 0.2, my_1 - dy_1 * 0.2, dx_1 * 0.4, dy_1 * 0.4, width=width, head_width=0.3 * len_all,
                 head_length=0.4 * len_all, linewidth=0.1, fc=(1, 1, 1), ec=(1, 1, 1), length_includes_head=True)
        ax.arrow(mx_2 - dx_2 * 0.2, my_2 - dy_2 * 0.2, dx_2 * 0.4, dy_2 * 0.4, width=width, head_width=0.3 * len_all,
                 head_length=0.4 * len_all, linewidth=0.1, fc=(1, 1, 1), ec=(1, 1, 1), length_includes_head=True)
        ax.arrow(mx_3 - dx_3 * 0.2, my_3 - dy_3 * 0.2, dx_3 * 0.4, dy_3 * 0.4, width=width, head_width=0.3 * len_all,
                 head_length=0.4 * len_all, linewidth=0.1, fc=(1, 1, 1), ec=(1, 1, 1), length_includes_head=True)

        # ax.arrow(result_1[t_2, 1] + dx_2 * 0.3, result_1[t_2, 2] + dy_2 * 0.3, dx_2 * 0.4, dy_2 * 0.4,
        #          width=width, head_length=len_2, linewidth=0.1, fc=(1, 1, 1), ec=(1, 1, 1), length_includes_head=True)


        ax.plot(Q_loc[0], Q_loc[1], 'wo')
        ax.plot(R_loc[0], R_loc[1], 'wo')
        ax.plot(S_loc[0], S_loc[1], 'wo')
        dash_line = np.vstack((result_1[-1, 1:3], np.zeros(2)))
        ax.plot(dash_line[:, 0], dash_line[:, 1], color='#00BFFF', linestyle='--', linewidth=1)

        ax.text(Q_loc[0], Q_loc[1]-1e-07, 'Q', color='#D3D3D3', horizontalalignment='center', verticalalignment='top')
        ax.text(R_loc[0], R_loc[1]-1e-07, 'R', color='#D3D3D3', horizontalalignment='center', verticalalignment='top')
        ax.text(S_loc[0], S_loc[1]-1e-07, 'S', color='#D3D3D3', horizontalalignment='center', verticalalignment='top')

        "把边上的坐标轴移至中间"
        ax.spines[['left', 'bottom']].set_position(('data', 0))
        ax.text(1, 0, 'X', color='#D3D3D3', transform=ax.get_yaxis_transform())
        ax.text(0, 1, "Y", color='#D3D3D3', transform=ax.get_xaxis_transform())
        ax.spines[['left', 'bottom']].set_color('#696969')
        ax.spines[['top', 'right']].set_visible(False)
        # plt.title('X-Y', color=(1, 1, 1))
        plt.xticks([])
        plt.yticks([])
        plt.savefig('part2.jpg', dpi=300)
        plt.close()

def main_part2(mcg_path, time_loc):
    """
    :param mcg_path: 心磁路径
    :param save_path: 保存路径
    :param time: 时间 np.array([Qh, Qp, R, Sp, Sr]) 分别为Q初，Q峰，R峰，S峰，S末
    :return: 保存图片
    """
    # 还原的点的位置
    const = np.sqrt(3)
    point = np.array([[0.06, 0.02 * const, 0], [0.08, 0.02 * const, 0], [0.1, 0.02 * const, 0],
                        [0.12, 0.02 * const, 0], [0.14, 0.02 * const, 0],
                      [0.05, 0.03 * const, 0], [0.07, 0.03 * const, 0], [0.09, 0.03 * const, 0],
                      [0.11, 0.03 * const, 0], [0.13, 0.03 * const, 0], [0.15, 0.03 * const, 0],
                    [0.04, 0.04 * const, 0], [0.06, 0.04 * const, 0], [0.08, 0.04 * const, 0],
                    [0.10, 0.04 * const, 0], [0.12, 0.04 * const, 0], [0.14, 0.04 * const, 0], [0.16, 0.04 * const, 0],
                [0.03, 0.05 * const, 0], [0.05, 0.05 * const, 0], [0.07, 0.05 * const, 0], [0.09, 0.05 * const, 0],
                [0.11, 0.05 * const, 0], [0.13, 0.05 * const, 0], [0.15, 0.05 * const, 0], [0.17, 0.05 * const, 0],
                [0.03, 0.06 * const, 0], [0.05, 0.06 * const, 0], [0.07, 0.06 * const, 0], [0.09, 0.06 * const, 0],
                [0.11, 0.06 * const, 0], [0.13, 0.06 * const, 0], [0.15, 0.06 * const, 0], [0.17, 0.06 * const, 0],
                    [0.04, 0.07 * const, 0], [0.06, 0.07 * const, 0], [0.08, 0.07 * const, 0],
                    [0.10, 0.07 * const, 0], [0.12, 0.07 * const, 0], [0.14, 0.07 * const, 0],[0.16, 0.07 * const, 0],
                        [0.05, 0.08 * const, 0], [0.07, 0.08 * const, 0], [0.09, 0.08 * const, 0],
                        [0.11, 0.08 * const, 0], [0.13, 0.08 * const, 0], [0.15, 0.08 * const, 0],
                         [0.06, 0.09 * const, 0], [0.08, 0.09 * const, 0], [0.1, 0.09 * const, 0],
                         [0.12, 0.09 * const, 0], [0.14, 0.09 * const, 0]
                        ])
    point = np.array(point)
    obj = SquidtransCurrent(point)
    obj.get_lf()
    obj.squid_tran(mcg_path, time_loc)


if __name__ == '__main__':
    # start_time = time.time()
    mcg_path = r'data/GDNH_2023_000432.txt'
    time_loc = np.array([270, 276, 300, 328, 345])
    main_part2(mcg_path, time_loc)
    # end_time = time.time()
    # print('operator time = %f' % (end_time - start_time))
