"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/01/18 16:40
desc: 小工具
    解压缩
    提取id和时刻点
environment: pfafn36
run:
    python utils.py
option:
    结果目录、心磁文档、时刻点文档, plt.show()
"""


import zipfile
import pandas as pd
import math
import numpy as np
import re
from shapely.geometry import Polygon, MultiLineString
from shapely.geometry.multipolygon import MultiPolygon
from shapely.ops import unary_union
import os
# import cv2
import ast
import matplotlib.pyplot as plt
import time
import scipy.special
from scipy import signal
from scipy.ndimage import gaussian_filter1d
import scipy.stats as stats
from scipy.stats import trim_mean
import itertools
import pickle
import random
from sklearn.cluster import KMeans
from joblib import Parallel, delayed
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor

from utils.StatPNs import statpn
from utils.gettimes1 import gettime
from utils.cal_interpolates import cal_interpolate, cal_interpolate0
from utils.space_wave import SpaceWave
from utils.time_wave import TimeWave
from utils.data import Data


def hb_lis0(l0):
    '''合并多维列表的全部元素为一维'''
    if type(l0) == list:
        l1 = []
        for i in range(len(l0)):
            l1 += hb_lis0(l0[i])
        return l1
    else:
        return [l0]


def finetune_tms0(lis, idxs=[]):
    '''时刻点微调'''
    Ph, Pp, Pr, Qh, Q_peak, R_peak, S_peak, Sr, Th, T_onset, T_peak, T_end, Tr, RR = lis
    if not idxs:
        idxs = [Q_peak, R_peak, S_peak, T_onset, T_peak, T_end]
    Qp, Rp, Sp, To, Tp, Te = idxs
    Sr = max(Sp, min([Sr, Sp+60, To]))  # 微调
    Qh = min(Qh, Qp)
    Pr = min(Pr, Qh)
    Pp = min(Pp, Pr)
    Ph = min(Ph, Pp)
    Th = min(To, max([Th, 2*To-Tp, Sr]))
    Tr = max(Te, min(Tr, 2*Te-Tp))
    return [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR]


# def finetune_tms2(lis, idxs=[]):
def finetune_tms2(lis, idxs={}):
    '''时刻点微调'''
    Ph, Pp, Pr, Qh, Q_peak, R_peak, S_peak, Sr, Th, T_onset, T_peak, T_end, Tr = lis
    if not idxs:
        idxs = [Q_peak, R_peak, S_peak, T_onset, T_peak, T_end]
    else:
        idxs = [idxs[x] for x in ['Q', 'R', 'S', 'T_on', 'T', 'T_end']]
    Qp, Rp, Sp, To, Tp, Te = idxs
    Sr = max(Sp, min([Sr, Sp+60, To]))  # 微调
    Qh = min(Qh, Qp)
    Pr = min(Pr, Qh)
    Pp = min(Pp, Pr)
    Ph = min(Ph, Pp)
    return [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr]


def finetune_tms1(lis, idxs=[]):
    '''时刻点微调: 心肌肥厚修改时刻点'''
    Ph, Pp, Pr, Qh, Q_peak, R_peak, S_peak, Sr, Th, T_onset, T_peak, T_end, Tr, RR = lis
    if idxs == []:
        Qp, Rp, Sp, To, Tp, Te = [Q_peak, R_peak, S_peak, T_onset, T_peak, T_end]
    elif len(idxs) == 6:
        Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = finetune_tms0(lis, idxs)
    else:
        Qp, Rp, Sp, To, Tp, Te, Qh, Sr, Th, Tr = idxs
    return Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR


def product2(dx, dy, ik=0):
    if ik == 0:
        return pow(dx*dx+dy*dy, 1/2)
    else:  # 平方差
        return pow(dx*dx-dy*dy, 1/2)


def connect01(brs, pns, i, j, f0, c0, p1, p0, f1, c1, ik):
    for k1 in range(len(p1)):  # 向前搜索
        if len(p0) == 0:
            for k in range(k1, len(p1)):
                c1[ik][k] = [[i]+p1[k].copy()]  # 中间开始
            break
        d1 = [100 for k in range(len(p0))]
        for k0 in range(len(p0)):
            di = product2(p1[k1][1]-p0[k0][1], p1[k1][2]-p0[k0][2])
            if di < 17:
                d1[k0] = di
        if min(d1) == 100:
            c1[ik][k1] = [[i]+p1[k1].copy()]  # 中间开始
        else:
            k0 = d1.index(min(d1))
            k00 = pns[j-1][ik].index(p0[k0])
            c0[ik][k00].append([i]+p1[k1].copy())
            c1[ik][k1] = c0[ik][k00].copy()  # 继承轨迹*——
            p0.pop(k0)
            f1[ik][k1] = f0[ik][k00]
    for k0 in range(len(p0)):
        k00 = pns[j-1][ik].index(p0[k0])  # 获取原索引
        if len(p1) > 0:
            d1 = [100 for k in range(len(p1))]
            for k1 in range(len(p1)):
                di = product2(p1[k1][1]-p0[k0][1], p1[k1][2]-p0[k0][2])
                if di < 17:
                    d1[k1] = di
            if min(d1) < 100:
                k1 = d1.index(min(d1))
                c0[ik][k00].append([i]+p1[k1].copy())
        brs[ik][int(3-2*f0[ik][k00])].append(c0[ik][k00].copy())
    return brs, f1, c1


def writepn(pns, files, j):
    m = len(pns[0])
    n = len(pns[1])
    doc = open(files, 'a')
    doc.write('\n%d %d %d,' % (j, m, n))
    for j1 in range(m):
        doc.write(' %.2f %d %d' %
                  (pns[0][j1][0], pns[0][j1][1], pns[0][j1][2]))
    doc.write(',')  # 分割符
    for j1 in range(n):
        doc.write(' %.2f %d %d' %
                  (pns[1][j1][0], pns[1][j1][1], pns[1][j1][2]))
    doc.close()
    return m, n


def get_traject1(times, t1, t2, M0, files='', pnfile=''):
    '''统计全部分支'''
    pns = []  # 极子list
    times[t2] = min(times[t2], len(M0)-1)  # 过长修正——
    for j in range(times[t1], times[t2]+1):  # 统计极子
        matrix = norms0(M0[j])
        pn = statpn(matrix, vmin=0.3, f2=1)  # 统计多极子
        pns.append(pn)  # 极子列表
        if pnfile:
            writepn(pn, pnfile, j)  # 写入多极子0.3
    brs = [[[], [], [], []], [[], [], [], []]]  # 分支list
    for i in range(times[t1], times[t2]+1):
        j = i - times[t1]
        if j == 0:  # 第1帧-标记从头开始1, 缓存全部极子
            p, n = pns[j].copy()
            f0 = [[1 for k in range(len(p))], [1 for k in range(len(n))]]
            c0 = [[[[i]+p[k]] for k in range(len(p))], [[[i]+n[k]] for k in range(len(n))]]
        else:  # 计算并保存分支
            p1, n1 = pns[j][0].copy(), pns[j][1].copy()
            p0, n0 = pns[j-1][0].copy(), pns[j-1][1].copy()
            f1 = [[0 for k in range(len(p1))], [0 for k in range(len(n1))]]
            c1 = [[[]for k in range(len(p1))], [[] for k in range(len(n1))]]
            brs, f1, c1 = connect01(brs, pns, i, j, f0, c0, p1, p0, f1, c1, 0)
            brs, f1, c1 = connect01(brs, pns, i, j, f0, c0, n1, n0, f1, c1, 1)
            if i == times[t2]:  # 最后1帧
                for k1 in range(len(c1[0])):
                    brs[0][int(2-2*f1[0][k1])].append(c1[0][k1].copy())
                for k1 in range(len(c1[1])):
                    brs[1][int(2-2*f1[1][k1])].append(c1[1][k1].copy())
            f0 = f1.copy()
            c0 = c1.copy()
    brs = brs[0]+brs[1]
    # for i in range(len(brs)):
    #     print(brs[i])
    # strs = [str(brs[0][i]) for i in range(len(brs[0]))]
    # strs += [str(brs[1][i]) for i in range(len(brs[1]))]
    # f00 = write_str('\n'.join(strs), files)
    return brs


def get_pts_lns1(files):
    # 数据统计: 多极子轨迹的点集和线段集
    l0 = txt_to_list(files)
    pts = []  # 散点-x, y, z, c, s
    lns = []  # 线段-x1x2, y1y2, z1z2, c=前一帧散点颜色
    for i in range(4):
        for j in range(len(l0[i])):
            for k in range(len(l0[i][j])):
                m0 = l0[i][j][k].copy()  # 信息
                pts.append([m0[2], m0[3], m0[0], m0[1]/2+0.5, m0[1]*10])
                if k > 0:
                    m1 = l0[i][j][k-1].copy()
                    lns.append([[m1[2],m0[2]], [m1[3],m0[3]], [m1[0],m0[0]], m1[1]/2+0.5])
    for i in range(4, len(l0)):
        for j in range(len(l0[i])):
            for k in range(len(l0[i][j])):
                m0 = l0[i][j][k].copy()  # 信息
                pts.append([m0[2], m0[3], m0[0], m0[1]/2+0.5, -m0[1]*10])
                if k > 0:
                    m1 = l0[i][j][k-1].copy()
                    lns.append([[m1[2],m0[2]], [m1[3],m0[3]], [m1[0],m0[0]], m1[1]/2+0.5])
    return pts, lns


def split_brs(b1, ik):
    '''TT次分支分类: 主分支/次分支/主极子列表'''
    b2 = []
    l1 = [[] for i in range(5000)]  # 定义周期长度-获取主极子
    for i in range(2-2*ik, 6-2*ik):
        for j in range(len(b1[i])):
            b3 = b1[i][j]
            b2.append(b3)  # 分支
            for k in range(len(b3)):
                if l1[b3[k][0]] == []:
                    l1[b3[k][0]] = b3[k]
                elif b3[k][1]*ik > l1[b3[k][0]][1]*ik:
                    l1[b3[k][0]] = b3[k]
    l0 = [b2[i][0][0] for i in range(len(b2))]+[b2[i][-1][0] for i in range(len(b2))]
    b3 = [sum(1 for j in range(len(b2[i])) if b2[i][j][1] == ik) for i in range(len(b2))]  # 主极子长度
    if b3 == []:
        return [], 0, 0
    ik0 = b3.index(max(b3))
    b4 = [[], [], []]
    b4[0] = b2[ik0].copy()
    b2.pop(ik0)
    for b0 in b4[0]:
        l1[b0[0]] = b0
    l1 = l1[min(l0):max(l0)+1]
    b4[1] = b2.copy()
    b4[2] = l1.copy()
    return b4, min(l0), max(l0)+1


def get_pts_lns2(b1, files=''):
    '''数据统计-平面主分支轨迹的点集和线段集
    ts, b4, b5, xs1, ys1, xs2, ys2
    时长, 最大正/负主极子长度, 正/负主极子的横/纵坐标'''
    if files:
        b1 = txt_to_list(files)
    b2 = []
    for i in range(len(b1)):
        for j in range(len(b1[i])):
            b2.append(b1[i][j])  # 分支
    l0 = [b2[i][0][0] for i in range(len(b2))]+[b2[i][-1][0] for i in range(len(b2))]
    ts = max(l0)-min(l0)+1
    b3 = [sum(1 for j in range(len(b2[i])) if b2[i][j][1] == 1) for i in range(len(b2))]  # 主极子长度
    b4 = b2[b3.index(max(b3))]
    b2 = []
    for i in range(4, len(b1)):
        for j in range(len(b1[i])):
            b2.append(b1[i][j])  # 分支
    b3 = [sum(1 for j in range(len(b2[i])) if b2[i][j][1] == -1) for i in range(len(b2))]  # 主极子长度
    b5 = b2[b3.index(max(b3))]  # 主极子
    xs1, ys1, xs2, ys2 = [], [], [], []
    for i in range(len(b4)):
        xs1.append(b4[i][2])
        ys1.append(b4[i][3])
    for i in range(len(b5)):
        xs2.append(b5[i][2])
        ys2.append(b5[i][3])
    return ts, b4, b5, xs1, ys1, xs2, ys2


def draw111(ax, pts, lns):
    ax.set_xlabel('x ', color='k')
    ax.set_ylabel('y ', color='k')
    ax.set_zlabel('t ', color='g')
    for i in range(len(pts)):
        x0, y0, z0, c0, s0 = pts[i] 
        c1 = plt.get_cmap('jet')(c0)
        ax.scatter(x0, y0, z0, c=c1, marker='o', alpha=1, s=s0)
    for i in range(len(lns)):
        x0, y0, z0, c0 = lns[i]
        c1 = plt.get_cmap('jet')(c0)
        ax.plot(x0, y0, z0, c=c1, alpha=1)


def write_xlsx0(f0, iks, f1='', tx='Sheet1'):
    '''xlsx数据提取: xlsx表格, 索引, 文档, 表单名'''
    data = pd.read_excel(f0, sheet_name=tx).values
    vs = []
    if len(iks) == 2:
        lis0 = list(range(iks[0], iks[1]))
    else:
        lis0 = iks
    for i in lis0:
        vs.append(get_xlsx_value(data, i))
    vs = [[vs[i][j] for i in range(len(vs))] for j in range(len(vs[0]))]
    if f1:
        vs = [', '.join(vs[i]) for i in range(len(vs))]
        write_str('\n'.join(vs), f1)
        return f1
    else:
        return vs


def change_per0(p1, p0=50):  # 计算修正概率
    if p1 < p0:  # 阈值
        return 50*p1/p0
    else:
        return 50+50*(p1-p0)/(100-p0)


def change_pers0(l1, p0=50, ik=0):  # 计算修正概率集
    l0 = [round(change_per0(p1, p0), ik) for p1 in l1]
    return l0


def get_metr2(d0, ids, d1, d2, ts=[]):
    l1, l2 = [], []
    for i in range(len(ids)):
        print('start:', i+1, 'left:', len(ids)-1-i)
        mfm_file = '%s/%s.txt' % (d0, ids[i])
        if not os.path.exists(mfm_file):
            # print('no cunzai!!')
            l1.append(ids[i])
            continue
        mcglines = get_lines1(mfm_file)
        l2.append(len(mcglines))
    print(l2)
    print(l1)
    return d2


def get_labels0(d2):
    '''整理标签'''
    l0 = get_lines1('%s/ids.txt' % (d2))
    l1 = get_lines1('%s/ids_consis_tr0.txt' % (d2))
    l2 = get_lines1('%s/ids_consis_tr1.txt' % (d2))
    te0 = get_lines1('%s/ids_consis_te0.txt' % (d2))
    te1 = get_lines1('%s/ids_consis_te1.txt' % (d2))
    random.shuffle(l1)
    random.shuffle(l2)
    tr0 = l1[95:]
    va0 = l1[:95]  # 95/473
    tr1 = l2[155:]
    va1 = l2[:155]  # 155/775
    idx = []
    id1 = []
    for ls in [tr0, tr1]:
        id0 = []
        for item in ls:
            id0.append(l0.index(item))
        id1.append(id0)
    idx.append(id1)
    id1 = []
    for ls in [va0, va1]:
        id0 = []
        for item in ls:
            id0.append(l0.index(item))
        id1.append(id0)
    idx.append(id1)
    id1 = []
    for ls in [te0, te1]:
        id0 = []
        for item in ls:
            id0.append(l0.index(item))
        id1.append(id0)
    idx.append(id1)
    strs = [str(idx[i]) for i in range(len(idx))]
    f00 = '%s/idx.txt' % d2
    write_str('\n'.join(strs), f00)
    return f00


def default_nms1():
    '''综合参数-相关参数'''
    '''综合参数-相关参数: QRS中心偏离度, TT轨迹, T中心偏离度, T磁极角度'''
    nms0 = [['mfm_QR_center', 'mfm_QR_mt2', 'mfm_QR_dicd', 'mfm_RS_center', 'mfm_RS_mt2', 'mfm_RS_dicd'], ['mfm_TTtj_df_melg0', 'mfm_TTtj_n_mtjsp', 'mfm_TTtj_p_atjpa', 'mfm_TTtj_n_saec', 'mfm_TTtj_n_saep', 'mfm_TTtj_n_selg', 'mfm_TTtj_n_atjsp', 'mfm_TTtj_df_didms', 'mfm_TTtj_sg_melg0', 'mfm_TTtj_n_snbd', 'mfm_TTtj_n_atjpa', 'mfm_TTtj_sg_ctjec'], ['mfm_TT_center', 'mfm_TT_mt2', 'mfm_TT_dicd'], ['mfm_TT_tta', 'mfm_TT_ta']]
    rks0 = [[[-0.53, 0.24], [40, 46, 58]], [[-1.73, -1.14, -0.86, -0.66, -0.64, 0.38], [38, 39, 40, 41, 42, 46, 58]], [[35.0, 49.1, 49.8, 50.6, 51.4, 52.5, 53.3, 54.6, 55.2, 55.9, 56.4, 57.1, 57.9, 64.7, 104.6], [64, 52, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 44, 58]], [[-0.73, -0.22, 0.04], [45, 46, 47, 55]], [[-0.53, 0.03], [45, 46, 54]], [[11.7, 14.4, 16.1, 18.0, 19.5, 21.1, 22.3, 23.5, 25.0, 25.4, 25.8, 27.0, 27.4, 28.4, 29.3, 29.9, 31.3, 31.6, 32.1, 33.1, 33.5, 34.1, 34.4, 34.8, 35.6, 36.5, 36.8, 37.2, 37.8, 38.7, 41.6, 48.4, 52.7, 54.6, 74.6, 130.7], [99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71, 70, 64, 63, 62, 61, 45, 40, 56]], [[2.0], [47, 65]], [[5.0, 7.2, 9.2, 11.7, 12.4, 13.0, 14.3, 15.8, 17.8, 19.8, 22.8, 26.4, 34.9, 64.3], [43, 42, 41, 40, 39, 57, 58, 59, 60, 61, 62, 63, 64, 65, 60]], [[-99.9, -99.6, -99.2, -98.6, -97.7, -96.5, -94.5, -92.4, -89.5, -85.5, -82.8, -78.7, -73.7, -68.6, -62.8, -53.1, -26.1], [44, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71, 49]], [[21.8, 26.7, 28.7, 33.7, 39.0, 49.2, 51.1], [41, 43, 53, 54, 55, 56, 57, 65]], [[39.8, 94.6, 100.0], [69, 70, 49, 44]], [[58.1, 66.6, 73.1, 81.6, 87.5, 92.2, 94.9, 98.0, 99.5, 99.9, 100.0], [77, 76, 75, 74, 73, 72, 71, 70, 69, 68, 67, 46]], [[4.0, 5.0, 6.1, 7.9, 9.7, 11.0, 12.9, 13.8, 15.7, 17.0, 18.7, 19.7, 21.0, 23.5, 29.8, 32.3, 36.0, 41.9, 59.7, 107.0], [48, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 49]], [[2.2], [47, 62]], [[43.3, 47.5, 53.7, 55.3, 57.0, 58.2, 60.3, 61.2, 64.0, 66.3, 66.7, 69.2, 71.3, 73.4, 75.5, 79.4, 80.2, 82.1, 84.0, 84.4, 85.3, 86.3, 87.3, 89.6, 91.1, 92.1, 93.1, 93.6, 94.9, 96.2, 97.8, 98.5], [87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71, 70, 69, 68, 67, 66, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 46]], [[2.2, 5.1, 6.7, 8.5, 10.8, 13.0, 15.6], [41, 42, 43, 44, 45, 46, 47, 62]], [[1.0, 3.5], [48, 62, 49]], [[100.0], [77, 40]], [[-0.6, -0.36, -0.2, -0.1, 0.0, 0.13, 0.25, 0.27, 0.73, 0.78, 0.83, 0.88, 0.93, 1.0, 1.05, 1.11, 1.18, 1.23, 1.33, 1.43, 1.6, 1.71, 1.85, 2.12, 2.64, 3.67], [38, 39, 40, 41, 42, 43, 44, 45, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 55]], [[-0.54, 0.44], [35, 42, 58]], [[11.5, 16.2, 20.3, 23.6, 29.8, 52.0, 78.5, 81.1, 82.9, 85.9, 87.2, 91.8, 95.5, 97.8, 101.9, 102.9, 105.6, 110.3, 113.6, 116.0, 119.1, 124.8, 135.0, 139.7, 148.3, 159.4, 166.1, 179.5, 208.0, 230.7, 291.8, 441.6], [31, 32, 33, 34, 40, 60, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 49]], [[3.7, 6.0, 7.2, 8.5, 9.8, 11.0, 12.2, 13.5, 14.7, 16.0, 46.6], [31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 47, 88]], [[2.0, 4.8, 7.6, 9.2, 10.7, 12.1, 14.0, 14.4, 16.6, 18.1, 18.8, 21.8, 23.5, 24.4, 25.0, 26.1, 27.0, 27.6, 28.4, 29.5, 30.8, 32.2, 32.9, 33.5, 34.0, 34.8, 35.2, 36.0, 36.9, 37.0, 37.4, 37.6, 38.0, 47.2, 54.5, 61.2, 74.7, 84.4], [96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71, 70, 69, 68, 67, 66, 65, 64, 58, 34, 30, 29, 28, 94]]]
    combs = [[1, 2, 3, 4, 5, 6], [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], [19, 20, 21], [22, 23]]
    sz = [7, 0, 7, 7]
    return [hb_lis0(nms0), rks0, combs, sz]


def default_nms0():
    '''全部451参数命名-与参数生成序号对应'''
    mfmls = [[['mfm_disp_Q', 'mfm_disp_R', 'mfm_disp_S', 'mfm_disp_T', 'mfm_disp_QRS', 'mfm_disp_TT', '']], [['', '', '', '', '', '', 'mfm_QR_center', 'mfm_QR_mt2'], ['mfm_QR_didma', 'mfm_QR_didav', 'mfm_QR_didmse', 'mfm_QR_dicd', 'mfm_QR_dicar', 'mfm_QR_dicre9'], ['mfm_QR_wg', 'mfm_QR_nums', 'mfm_QR_pm', 'mfm_QR_nm', 'mfm_QR_r', 'mfm_QR_v', 'mfm_QR_mse', '', '', '', '', '', '', '', '', 'mfm_QR_af', 'mfm_QR_ad', 'mfm_QR_v1', 'mfm_QR_mse1', 'mfm_QR_saf', 'mfm_QR_sad'], ['mfm_QR_dppma', 'mfm_QR_dppav', 'mfm_QR_dppmse', 'mfm_QR_dpnma', 'mfm_QR_dpnav', 'mfm_QR_dpnmse', 'mfm_QR_pnpma', 'mfm_QR_pnpflu', 'mfm_QR_pnnma', 'mfm_QR_pnnflu'], ['mfm_QR_qa', 'mfm_QR_ra', '', '', '', '', 'mfm_QR_qra', 'mfm_QR_q', '', '', '', '', 'mfm_QR_areaf', 'mfm_QR_aread'], ['mfm_QR_epma', 'mfm_QR_epav', 'mfm_QR_epmse', 'mfm_QR_enma', 'mfm_QR_enav', 'mfm_QR_enmse']], [['', '', '', '', '', '', 'mfm_RS_center', 'mfm_RS_mt2'], ['mfm_RS_didma', 'mfm_RS_didav', 'mfm_RS_didmse', 'mfm_RS_dicd', 'mfm_RS_dicar', 'mfm_RS_dicre9'], ['mfm_RS_wg', 'mfm_RS_nums', 'mfm_RS_pm', 'mfm_RS_nm', 'mfm_RS_r', 'mfm_RS_v', 'mfm_RS_mse', '', '', '', '', '', '', '', '', 'mfm_RS_af', 'mfm_RS_ad', 'mfm_RS_v1', 'mfm_RS_mse1', 'mfm_RS_saf', 'mfm_RS_sad'], ['mfm_RS_dppma', 'mfm_RS_dppav', 'mfm_RS_dppmse', 'mfm_RS_dpnma', 'mfm_RS_dpnav', 'mfm_RS_dpnmse', 'mfm_RS_pnpma', 'mfm_RS_pnpflu', 'mfm_RS_pnnma', 'mfm_RS_pnnflu'], ['', 'mfm_RS_sa', '', '', '', '', 'mfm_RS_rsa', 'mfm_RS_q', '', '', '', '', 'mfm_RS_areaf', 'mfm_RS_aread'], ['mfm_RS_epma', 'mfm_RS_epav', 'mfm_RS_epmse', 'mfm_RS_enma', 'mfm_RS_enav', 'mfm_RS_enmse']], [['', '', '', '', '', '', 'mfm_TT_center', 'mfm_TT_mt2'], ['mfm_TT_didma', 'mfm_TT_didav', 'mfm_TT_didmse', 'mfm_TT_dicd', 'mfm_TT_dicar', 'mfm_TT_dicre9'], ['mfm_TT_wg', 'mfm_TT_nums', 'mfm_TT_pm', 'mfm_TT_nm', 'mfm_TT_r', 'mfm_TT_v', 'mfm_TT_mse', '', '', '', '', '', '', '', '', 'mfm_TT_af', 'mfm_TT_ad', 'mfm_TT_v1', 'mfm_TT_mse1', 'mfm_TT_saf', 'mfm_TT_sad'], ['mfm_TT_dppma', 'mfm_TT_dppav', 'mfm_TT_dppmse', 'mfm_TT_dpnma', 'mfm_TT_dpnav', 'mfm_TT_dpnmse', 'mfm_TT_pnpma', 'mfm_TT_pnpflu', 'mfm_TT_pnnma', 'mfm_TT_pnnflu'], ['mfm_TT_ta', 'mfm_TT_tt50', 'mfm_TT_tt75', '', '', '', '', 'mfm_TT_tta', 'mfm_TT_q', '', '', '', '', 'mfm_TT_areaf', 'mfm_TT_aread'], ['mfm_TT_epma', 'mfm_TT_epav', 'mfm_TT_epmse', 'mfm_TT_enma', 'mfm_TT_enav', 'mfm_TT_enmse']]]
    pcdmls = [[['', '', '', '', '', '', 'pcdm_QR_center', 'pcdm_QR_mt2'], ['pcdm_QR_mctd', 'pcdm_QR_mcar', 'pcdm_QR_mcre9', 'pcdm_QR_areac', 'pcdm_QR_aread'], ['pcdm_QR_wg', 'pcdm_QR_nums', 'pcdm_QR_pm', 'pcdm_QR_nm', 'pcdm_QR_r', 'pcdm_QR_v', 'pcdm_QR_mse', '', '', '', '', '', '', '', '', 'pcdm_QR_af', 'pcdm_QR_ad', 'pcdm_QR_v1', 'pcdm_QR_mse1', 'pcdm_QR_saf', 'pcdm_QR_sad'], ['pcdm_QR_dpma', 'pcdm_QR_dpav', 'pcdm_QR_dpmse', 'pcdm_QR_dvma', 'pcdm_QR_dvav', 'pcdm_QR_dvmse', 'pcdm_QR_mdama', 'pcdm_QR_mdaav', 'pcdm_QR_mdamse'], ['pcdm_QR_qca', 'pcdm_QR_qia', 'pcdm_QR_rca', 'pcdm_QR_ria', 'pcdm_QR_iama', 'pcdm_QR_iami', 'pcdm_QR_iaav', 'pcdm_QR_iamse', '', '', '', '', 'pcdm_QR_qra', 'pcdm_QR_q', '', '', '', '']], [['', '', '', '', '', '', 'pcdm_RS_center', 'pcdm_RS_mt2'], ['pcdm_RS_mctd', 'pcdm_RS_mcar', 'pcdm_RS_mcre9', 'pcdm_RS_areac', 'pcdm_RS_aread'], ['pcdm_RS_wg', 'pcdm_RS_nums', 'pcdm_RS_pm', 'pcdm_RS_nm', 'pcdm_RS_r', 'pcdm_RS_v', 'pcdm_RS_mse', '', '', '', '', '', '', '', '', 'pcdm_RS_af', 'pcdm_RS_ad', 'pcdm_RS_v1', 'pcdm_RS_mse1', 'pcdm_RS_saf', 'pcdm_RS_sad'], ['pcdm_RS_dpma', 'pcdm_RS_dpav', 'pcdm_RS_dpmse', 'pcdm_RS_dvma', 'pcdm_RS_dvav', 'pcdm_RS_dvmse', 'pcdm_RS_mdama', 'pcdm_RS_mdaav', 'pcdm_RS_mdamse'], ['', '', 'pcdm_RS_rca', 'pcdm_RS_ria', 'pcdm_RS_iama', 'pcdm_RS_iami', 'pcdm_RS_iaav', 'pcdm_RS_iamse', '', '', '', '', 'pcdm_RS_qra', 'pcdm_RS_q', '', '', '', '']], [['', '', '', '', '', '', 'pcdm_TT_center', 'pcdm_TT_mt2'], ['pcdm_TT_mctd', 'pcdm_TT_mcar', 'pcdm_TT_mcre9', 'pcdm_TT_areac', 'pcdm_TT_aread'], ['pcdm_TT_wg', 'pcdm_TT_nums', 'pcdm_TT_pm', 'pcdm_TT_nm', 'pcdm_TT_r', 'pcdm_TT_v', 'pcdm_TT_mse', '', '', '', '', '', '', '', '', 'pcdm_TT_af', 'pcdm_TT_ad', 'pcdm_TT_v1', 'pcdm_TT_mse1', 'pcdm_TT_saf', 'pcdm_TT_sad'], ['pcdm_TT_dpma', 'pcdm_TT_dpav', 'pcdm_TT_dpmse', 'pcdm_TT_dvma', 'pcdm_TT_dvav', 'pcdm_TT_dvmse', 'pcdm_TT_mdama', 'pcdm_TT_mdaav', 'pcdm_TT_mdamse'], ['pcdm_TT_qca', 'pcdm_TT_qia', 'pcdm_TT_rca', 'pcdm_TT_ria', 'pcdm_TT_iama', 'pcdm_TT_iami', 'pcdm_TT_iaav', 'pcdm_TT_iamse', '', '', '', '', 'pcdm_TT_qra', 'pcdm_TT_q', '', '', '', '']]]
    tmls = ['time_areaqj', 'time_qrsttrt', 'time_rtrt', 'time_rtrt3', 'time_stfs', 'time_strs', 'time_stsl', 'time_stsl1', 'time_tmdf', 'time_tmdf1', 'time_tnirt', 'time_tnirt1', 'time_tpnrt1', 'time_tpnrt2', 'time_tsm', 'time_tsm1', 'time_ttflt', 'time_ttflt1', 'time_ttstpn', 'time_ttstpp', 'time_ttstprt', 'time_qsitv', 'time_htrt']
    spls = ['space_rtsgnum', 'space_rtsgnum2', 'space_zeroR', 'space_zeroRTrot', 'space_zeroRTrot1', 'space_zeroT', 'space_zeroqrs']
    disp1 = ['mfm_disp1_Q_p', 'mfm_disp1_Q_n', 'mfm_disp1_Q_pn', 'mfm_disp1_R_p', 'mfm_disp1_R_n', 'mfm_disp1_R_pn', 'mfm_disp1_S_p', 'mfm_disp1_S_n', 'mfm_disp1_S_pn', 'mfm_disp1_T_p', 'mfm_disp1_T_n', 'mfm_disp1_T_pn']
    tttj = ['mfm_TTtj_p_melg', 'mfm_TTtj_p_mmlg', 'mfm_TTtj_p_mnbd', 'mfm_TTtj_p_atjpa', 'mfm_TTtj_p_atjsp', 'mfm_TTtj_p_atjec', 'mfm_TTtj_p_mtjpa', 'mfm_TTtj_p_mtjsp', 'mfm_TTtj_p_mtjec', 'mfm_TTtj_p_htjpa', 'mfm_TTtj_p_htjsp', 'mfm_TTtj_p_htjec', 'mfm_TTtj_p_rtjpa', 'mfm_TTtj_p_rtjsp', 'mfm_TTtj_p_rtjec', 'mfm_TTtj_n_melg', 'mfm_TTtj_n_mmlg', 'mfm_TTtj_n_mnbd', 'mfm_TTtj_n_atjpa', 'mfm_TTtj_n_atjsp', 'mfm_TTtj_n_atjec', 'mfm_TTtj_n_mtjpa', 'mfm_TTtj_n_mtjsp', 'mfm_TTtj_n_mtjec', 'mfm_TTtj_n_htjpa', 'mfm_TTtj_n_htjsp', 'mfm_TTtj_n_htjec', 'mfm_TTtj_n_rtjpa', 'mfm_TTtj_n_rtjsp', 'mfm_TTtj_n_rtjec', 'mfm_TTtj_br_melg0', 'mfm_TTtj_br_mndb0', 'mfm_TTtj_br_qra', 'mfm_TTtj_br_q', 'mfm_TTtj_br_areaf', 'mfm_TTtj_br_aread', 'mfm_TTtj_br_didma', 'mfm_TTtj_br_didmi', 'mfm_TTtj_br_didav', 'mfm_TTtj_br_didms', 'mfm_TTtj_br_ctjpa', 'mfm_TTtj_br_ctjsp', 'mfm_TTtj_br_ctjec', 'mfm_TTtj_sg_melg0', 'mfm_TTtj_sg_mndb0', 'mfm_TTtj_sg_qra', 'mfm_TTtj_sg_q', 'mfm_TTtj_sg_areaf', 'mfm_TTtj_sg_aread', 'mfm_TTtj_sg_didma', 'mfm_TTtj_sg_didmi', 'mfm_TTtj_sg_didav', 'mfm_TTtj_sg_didms', 'mfm_TTtj_sg_ctjpa', 'mfm_TTtj_sg_ctjsp', 'mfm_TTtj_sg_ctjec', 'mfm_TTtj_df_melg0', 'mfm_TTtj_df_mndb0', 'mfm_TTtj_df_qra', 'mfm_TTtj_df_q', 'mfm_TTtj_df_areaf', 'mfm_TTtj_df_aread', 'mfm_TTtj_df_didma', 'mfm_TTtj_df_didmi', 'mfm_TTtj_df_didav', 'mfm_TTtj_df_didms', 'mfm_TTtj_df_ctjpa', 'mfm_TTtj_df_ctjsp', 'mfm_TTtj_df_ctjec', 'mfm_TTtj_p_snbd', 'mfm_TTtj_p_selg', 'mfm_TTtj_p_sapa', 'mfm_TTtj_p_sasp', 'mfm_TTtj_p_saec', 'mfm_TTtj_p_saep', 'mfm_TTtj_n_snbd', 'mfm_TTtj_n_selg', 'mfm_TTtj_n_sapa', 'mfm_TTtj_n_sasp', 'mfm_TTtj_n_saec', 'mfm_TTtj_n_saep']
    return [mfmls, pcdmls, tmls, spls, disp1, tttj]


def get_metr0(d0, ids, d1, d2, ts=[]):
    '''计算mfm_pcdm_time_space指标&保存: d0数据集, d1指标, d2信息, ids样本名'''
    new_folder(d1)
    mfmls, pcdmls, tmls, spls, disp1, tttj = default_nms0()
    Zqr = extractmt('%s/ischemic_mfm_Z/rkdb_qr22.txt' % d2)
    Zrs = extractmt('%s/ischemic_mfm_Z/rkdb_rs22.txt' % d2)
    Ztt = extractmt('%s/ischemic_mfm_Z/rkdb_tt22.txt' % d2)
    dfs = pcdm_default()
    Zqr1 = extractmt('%s/ischemic_pcdm_Z/rkdb_qr31.txt' % d2)
    Zrs1 = extractmt('%s/ischemic_pcdm_Z/rkdb_rs31.txt' % d2)
    Ztt1 = extractmt('%s/ischemic_pcdm_Z/rkdb_tt31.txt' % d2)
    tmrd = [3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
    sprd = [0, 0, 0, 3, 3, 0, 0]
    l0, l1, l2, l3 = [], [], [], []
    l4, l5 = [], []
    l6 = []  # 心肌肥厚参数
    hcms = mts_default3()
    for i in range(len(ids)):
        print('start:', i+1, 'left:', len(ids)-1-i, ids[i])
        mfm_file = '%s/%s.txt' % (d0, ids[i])
        if not os.path.exists(mfm_file):
            print(mfm_file, 'Not Exists!')
            continue
        # mfm&pcdm
        mcglines = get_lines(mfm_file)
        matrixes = cal_interpolate(mfm_file)  # 本地还是服务器0
        if ts:
            tls = list(ts[i])  # 心肌肥厚修改时刻点————
            timelis0 = tls[:6]
        else:
            Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettime(mfm_file)
            timelis0 = [Qp, Rp, Sp, To, Tp, Te]
            mf = get_mfs(get_lines(mfm_file))
            timelis0 = change_QS(timelis0, matrixes, mf)
            tls = [Qp, Rp, Sp, To, Tp, Te, Qh, Sr, Th, Tr]  # 心肌肥厚修改时刻点————
        l0.append(get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt))
        l1.append(get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1))
        # time&space
        data, time_dic, rms = Data(mfm_file).get()
        tmmts = TimeWave(data, rms, time_dic).get_indicators()
        l2.append(get_round([tmmts[nm[5:]] for nm in tmls], tmrd))
        spmts = SpaceWave(data, time_dic).get_indicators()
        l3.append(get_round([spmts[nm[6:]] for nm in spls], sprd))
        # 磁极离散度
        l40 = get_disp1_qrst0(matrixes, timelis0, mfm_file)
        l4.append(hb_lis0(l40))
        # 多极子轨迹
        mf = get_mfs(get_lines(mfm_file), md=2)
        l50 = get_traject_mts0(matrixes, timelis0, mf)
        l5.append(hb_lis0(l50))
        # 心肌肥厚参数
        l6.append(get_hcm_mts0(mfm_file, matrixes, tls))  # 修改时刻点——
    # 写入指标
    d00 = write_lis(len_reshape(l0), mfmls, d1)
    d00 = write_lis(len_reshape(l1), pcdmls, d1)
    d00 = write_lis(len_reshape0(l2), tmls, d1)
    d00 = write_lis(len_reshape0(l3), spls, d1)
    l4 = [[l4[i][j] for i in range(len(l4))] for j in range(len(l4[0]))]
    l5 = [[l5[i][j] for i in range(len(l5))] for j in range(len(l5[0]))]
    for i in range(len(l4)):
        l40 = [str(l4[i][j]) for j in range(len(l4[i]))]
        write_str('\n'.join(l40), '%s/%s.txt' % (d1, disp1[i]))
    for i in range(len(l5)):
        l50 = [str(l5[i][j]) for j in range(len(l5[i]))]
        write_str('\n'.join(l50), '%s/%s.txt' % (d1, tttj[i]))
    l6 = [[l6[i][j] for i in range(len(l6))] for j in range(len(l6[0]))]
    for i in range(len(l6)):
        l60 = [str(l6[i][j]) for j in range(len(l6[i]))]
        write_str('\n'.join(l60), '%s/%s.txt' % (d1, hcms[i]))
    return d2


def mts_default0(md=0):
    '''全部参数名称-默认定义'''
    mfmls = [[['mfm_disp_Q', 'mfm_disp_R', 'mfm_disp_S', 'mfm_disp_T', 'mfm_disp_QRS', 'mfm_disp_TT', '']], [['', '', '', '', '', '', 'mfm_QR_center', 'mfm_QR_mt2'], ['mfm_QR_didma', 'mfm_QR_didav', 'mfm_QR_didmse', 'mfm_QR_dicd', 'mfm_QR_dicar', 'mfm_QR_dicre9'], ['mfm_QR_wg', 'mfm_QR_nums', 'mfm_QR_pm', 'mfm_QR_nm', 'mfm_QR_r', 'mfm_QR_v', 'mfm_QR_mse', '', '', '', '', '', '', '', '', 'mfm_QR_af', 'mfm_QR_ad', 'mfm_QR_v1', 'mfm_QR_mse1', 'mfm_QR_saf', 'mfm_QR_sad'], ['mfm_QR_dppma', 'mfm_QR_dppav', 'mfm_QR_dppmse', 'mfm_QR_dpnma', 'mfm_QR_dpnav', 'mfm_QR_dpnmse', 'mfm_QR_pnpma', 'mfm_QR_pnpflu', 'mfm_QR_pnnma', 'mfm_QR_pnnflu'], ['mfm_QR_qa', 'mfm_QR_ra', '', '', '', '', 'mfm_QR_qra', 'mfm_QR_q', '', '', '', '', 'mfm_QR_areaf', 'mfm_QR_aread'], ['mfm_QR_epma', 'mfm_QR_epav', 'mfm_QR_epmse', 'mfm_QR_enma', 'mfm_QR_enav', 'mfm_QR_enmse']], [['', '', '', '', '', '', 'mfm_RS_center', 'mfm_RS_mt2'], ['mfm_RS_didma', 'mfm_RS_didav', 'mfm_RS_didmse', 'mfm_RS_dicd', 'mfm_RS_dicar', 'mfm_RS_dicre9'], ['mfm_RS_wg', 'mfm_RS_nums', 'mfm_RS_pm', 'mfm_RS_nm', 'mfm_RS_r', 'mfm_RS_v', 'mfm_RS_mse', '', '', '', '', '', '', '', '', 'mfm_RS_af', 'mfm_RS_ad', 'mfm_RS_v1', 'mfm_RS_mse1', 'mfm_RS_saf', 'mfm_RS_sad'], ['mfm_RS_dppma', 'mfm_RS_dppav', 'mfm_RS_dppmse', 'mfm_RS_dpnma', 'mfm_RS_dpnav', 'mfm_RS_dpnmse', 'mfm_RS_pnpma', 'mfm_RS_pnpflu', 'mfm_RS_pnnma', 'mfm_RS_pnnflu'], ['', 'mfm_RS_sa', '', '', '', '', 'mfm_RS_rsa', 'mfm_RS_q', '', '', '', '', 'mfm_RS_areaf', 'mfm_RS_aread'], ['mfm_RS_epma', 'mfm_RS_epav', 'mfm_RS_epmse', 'mfm_RS_enma', 'mfm_RS_enav', 'mfm_RS_enmse']], [['', '', '', '', '', '', 'mfm_TT_center', 'mfm_TT_mt2'], ['mfm_TT_didma', 'mfm_TT_didav', 'mfm_TT_didmse', 'mfm_TT_dicd', 'mfm_TT_dicar', 'mfm_TT_dicre9'], ['mfm_TT_wg', 'mfm_TT_nums', 'mfm_TT_pm', 'mfm_TT_nm', 'mfm_TT_r', 'mfm_TT_v', 'mfm_TT_mse', '', '', '', '', '', '', '', '', 'mfm_TT_af', 'mfm_TT_ad', 'mfm_TT_v1', 'mfm_TT_mse1', 'mfm_TT_saf', 'mfm_TT_sad'], ['mfm_TT_dppma', 'mfm_TT_dppav', 'mfm_TT_dppmse', 'mfm_TT_dpnma', 'mfm_TT_dpnav', 'mfm_TT_dpnmse', 'mfm_TT_pnpma', 'mfm_TT_pnpflu', 'mfm_TT_pnnma', 'mfm_TT_pnnflu'], ['mfm_TT_ta', 'mfm_TT_tt50', 'mfm_TT_tt75', '', '', '', '', 'mfm_TT_tta', 'mfm_TT_q', '', '', '', '', 'mfm_TT_areaf', 'mfm_TT_aread'], ['mfm_TT_epma', 'mfm_TT_epav', 'mfm_TT_epmse', 'mfm_TT_enma', 'mfm_TT_enav', 'mfm_TT_enmse']]]
    pcdmls = [[['', '', '', '', '', '', 'pcdm_QR_center', 'pcdm_QR_mt2'], ['pcdm_QR_mctd', 'pcdm_QR_mcar', 'pcdm_QR_mcre9', 'pcdm_QR_areac', 'pcdm_QR_aread'], ['pcdm_QR_wg', 'pcdm_QR_nums', 'pcdm_QR_pm', 'pcdm_QR_nm', 'pcdm_QR_r', 'pcdm_QR_v', 'pcdm_QR_mse', '', '', '', '', '', '', '', '', 'pcdm_QR_af', 'pcdm_QR_ad', 'pcdm_QR_v1', 'pcdm_QR_mse1', 'pcdm_QR_saf', 'pcdm_QR_sad'], ['pcdm_QR_dpma', 'pcdm_QR_dpav', 'pcdm_QR_dpmse', 'pcdm_QR_dvma', 'pcdm_QR_dvav', 'pcdm_QR_dvmse', 'pcdm_QR_mdama', 'pcdm_QR_mdaav', 'pcdm_QR_mdamse'], ['pcdm_QR_qca', 'pcdm_QR_qia', 'pcdm_QR_rca', 'pcdm_QR_ria', 'pcdm_QR_iama', 'pcdm_QR_iami', 'pcdm_QR_iaav', 'pcdm_QR_iamse', '', '', '', '', 'pcdm_QR_qra', 'pcdm_QR_q', '', '', '', '']], [['', '', '', '', '', '', 'pcdm_RS_center', 'pcdm_RS_mt2'], ['pcdm_RS_mctd', 'pcdm_RS_mcar', 'pcdm_RS_mcre9', 'pcdm_RS_areac', 'pcdm_RS_aread'], ['pcdm_RS_wg', 'pcdm_RS_nums', 'pcdm_RS_pm', 'pcdm_RS_nm', 'pcdm_RS_r', 'pcdm_RS_v', 'pcdm_RS_mse', '', '', '', '', '', '', '', '', 'pcdm_RS_af', 'pcdm_RS_ad', 'pcdm_RS_v1', 'pcdm_RS_mse1', 'pcdm_RS_saf', 'pcdm_RS_sad'], ['pcdm_RS_dpma', 'pcdm_RS_dpav', 'pcdm_RS_dpmse', 'pcdm_RS_dvma', 'pcdm_RS_dvav', 'pcdm_RS_dvmse', 'pcdm_RS_mdama', 'pcdm_RS_mdaav', 'pcdm_RS_mdamse'], ['', '', 'pcdm_RS_rca', 'pcdm_RS_ria', 'pcdm_RS_iama', 'pcdm_RS_iami', 'pcdm_RS_iaav', 'pcdm_RS_iamse', '', '', '', '', 'pcdm_RS_qra', 'pcdm_RS_q', '', '', '', '']], [['', '', '', '', '', '', 'pcdm_TT_center', 'pcdm_TT_mt2'], ['pcdm_TT_mctd', 'pcdm_TT_mcar', 'pcdm_TT_mcre9', 'pcdm_TT_areac', 'pcdm_TT_aread'], ['pcdm_TT_wg', 'pcdm_TT_nums', 'pcdm_TT_pm', 'pcdm_TT_nm', 'pcdm_TT_r', 'pcdm_TT_v', 'pcdm_TT_mse', '', '', '', '', '', '', '', '', 'pcdm_TT_af', 'pcdm_TT_ad', 'pcdm_TT_v1', 'pcdm_TT_mse1', 'pcdm_TT_saf', 'pcdm_TT_sad'], ['pcdm_TT_dpma', 'pcdm_TT_dpav', 'pcdm_TT_dpmse', 'pcdm_TT_dvma', 'pcdm_TT_dvav', 'pcdm_TT_dvmse', 'pcdm_TT_mdama', 'pcdm_TT_mdaav', 'pcdm_TT_mdamse'], ['pcdm_TT_qca', 'pcdm_TT_qia', 'pcdm_TT_rca', 'pcdm_TT_ria', 'pcdm_TT_iama', 'pcdm_TT_iami', 'pcdm_TT_iaav', 'pcdm_TT_iamse', '', '', '', '', 'pcdm_TT_qra', 'pcdm_TT_q', '', '', '', '']]]
    tmls = ['time_areaqj', 'time_qrsttrt', 'time_rtrt', 'time_rtrt3', 'time_stfs', 'time_strs', 'time_stsl', 'time_stsl1', 'time_tmdf', 'time_tmdf1', 'time_tnirt', 'time_tnirt1', 'time_tpnrt1', 'time_tpnrt2', 'time_tsm', 'time_tsm1', 'time_ttflt', 'time_ttflt1', 'time_ttstpn', 'time_ttstpp', 'time_ttstprt']#, 'time_qsitv', 'time_htrt']  # 最后2个无效
    spls = ['space_rtsgnum', 'space_rtsgnum2', 'space_zeroR', 'space_zeroRTrot', 'space_zeroRTrot1', 'space_zeroT', 'space_zeroqrs']
    disp1 = ['mfm_disp1_Q_p', 'mfm_disp1_Q_n', 'mfm_disp1_Q_pn', 'mfm_disp1_R_p', 'mfm_disp1_R_n', 'mfm_disp1_R_pn', 'mfm_disp1_S_p', 'mfm_disp1_S_n', 'mfm_disp1_S_pn', 'mfm_disp1_T_p', 'mfm_disp1_T_n', 'mfm_disp1_T_pn']
    tttj = ['mfm_TTtj_p_melg', 'mfm_TTtj_p_mmlg', 'mfm_TTtj_p_mnbd', 'mfm_TTtj_p_atjpa', 'mfm_TTtj_p_atjsp', 'mfm_TTtj_p_atjec', 'mfm_TTtj_p_mtjpa', 'mfm_TTtj_p_mtjsp', 'mfm_TTtj_p_mtjec', 'mfm_TTtj_p_htjpa', 'mfm_TTtj_p_htjsp', 'mfm_TTtj_p_htjec', 'mfm_TTtj_p_rtjpa', 'mfm_TTtj_p_rtjsp', 'mfm_TTtj_p_rtjec', 'mfm_TTtj_n_melg', 'mfm_TTtj_n_mmlg', 'mfm_TTtj_n_mnbd', 'mfm_TTtj_n_atjpa', 'mfm_TTtj_n_atjsp', 'mfm_TTtj_n_atjec', 'mfm_TTtj_n_mtjpa', 'mfm_TTtj_n_mtjsp', 'mfm_TTtj_n_mtjec', 'mfm_TTtj_n_htjpa', 'mfm_TTtj_n_htjsp', 'mfm_TTtj_n_htjec', 'mfm_TTtj_n_rtjpa', 'mfm_TTtj_n_rtjsp', 'mfm_TTtj_n_rtjec', 'mfm_TTtj_br_melg0', 'mfm_TTtj_br_mndb0', 'mfm_TTtj_br_qra', 'mfm_TTtj_br_q', 'mfm_TTtj_br_areaf', 'mfm_TTtj_br_aread', 'mfm_TTtj_br_didma', 'mfm_TTtj_br_didmi', 'mfm_TTtj_br_didav', 'mfm_TTtj_br_didms', 'mfm_TTtj_br_ctjpa', 'mfm_TTtj_br_ctjsp', 'mfm_TTtj_br_ctjec', 'mfm_TTtj_sg_melg0', 'mfm_TTtj_sg_mndb0', 'mfm_TTtj_sg_qra', 'mfm_TTtj_sg_q', 'mfm_TTtj_sg_areaf', 'mfm_TTtj_sg_aread', 'mfm_TTtj_sg_didma', 'mfm_TTtj_sg_didmi', 'mfm_TTtj_sg_didav', 'mfm_TTtj_sg_didms', 'mfm_TTtj_sg_ctjpa', 'mfm_TTtj_sg_ctjsp', 'mfm_TTtj_sg_ctjec', 'mfm_TTtj_df_melg0', 'mfm_TTtj_df_mndb0', 'mfm_TTtj_df_qra', 'mfm_TTtj_df_q', 'mfm_TTtj_df_areaf', 'mfm_TTtj_df_aread', 'mfm_TTtj_df_didma', 'mfm_TTtj_df_didmi', 'mfm_TTtj_df_didav', 'mfm_TTtj_df_didms', 'mfm_TTtj_df_ctjpa', 'mfm_TTtj_df_ctjsp', 'mfm_TTtj_df_ctjec', 'mfm_TTtj_p_snbd', 'mfm_TTtj_p_selg', 'mfm_TTtj_p_sapa', 'mfm_TTtj_p_sasp', 'mfm_TTtj_p_saec', 'mfm_TTtj_p_saep', 'mfm_TTtj_n_snbd', 'mfm_TTtj_n_selg', 'mfm_TTtj_n_sapa', 'mfm_TTtj_n_sasp', 'mfm_TTtj_n_saec', 'mfm_TTtj_n_saep']
    mtnmls = hb_lis0(mfmls) + hb_lis0(pcdmls) + tmls + spls + disp1 + tttj
    if md:
        return mtnmls
    else:
        return tmls, spls


def mts_default1():
    '''心肌肥厚参数1'''
    mtnms = ['timephase_QR', 'timephase_QRS', 'timephase_ST', 'timephase_STr', 'timephase_TT', 'timephase_QT', 'time_noq']
    mtnms += ['amplitude_qrsmai', 'amplitude_qrsma', 'amplitude_qrs16maavi', 'amplitude_Rmai', 'amplitude_20a10i']
    mtnms += ['timearea_qrsmai', 'timearea_ttmai', 'timearea_qrstt', 'timearea_tmam', 'timearea_16absav', 'timearea_16absma']
    mtnms += ['timeitg_qrsabsn', 'timeitg_ttabsn', 'timeitg_ttsasn', 'timeitg_qrsttabsn', 'timeitg_qrsttsasn', 'timeitg_qrsttdabsn', 'timeitg_qrsttdsasn']
    mtnms += ['time_stph']
    mtnms += ['mfm_ag_qrsa', 'mfm_ag_tta', 'mfm_ag_qrstta', 'mfm_ag_rdz']
    return mtnms


def mts_default2():
    '''心肌肥厚参数2'''
    mtnms = ['pcdm_dpmm', 'time_twivls', 'time_stcgls']
    return mtnms


def mts_default4():
    '''心肌肥厚参数3'''
    mtnms = ['pcdm_dpmm0', 'pcdm_dpmm10', 'pcdm_dpmm20', 'pcdm_dpmm30', 'pcdm_dpmm40', 'pcdm_dpmm50', 'pcdm_dpmm60', 'pcdm_dpmm70', 'pcdm_dpmmth10', 'pcdm_dpmmth', 'mfm_ag_qrsa1', 'mfm_ag_tta1', 'mfm_ag_qrstta1', 'mfm_ag_rdz1', 'mfm_ag_rdz2']
    return mtnms


def mts_default3():
    '''心肌肥厚参数1'''
    mtnms = ['timephase_QR', 'timephase_QRS', 'timephase_ST', 'timephase_STr', 'timephase_TT', 'timephase_QT', 'time_noq']
    mtnms += ['amplitude_qrsmai', 'amplitude_qrsma', 'amplitude_qrs16maavi', 'amplitude_Rmai', 'amplitude_20a10i']
    mtnms += ['timearea_qrsmai', 'timearea_ttmai', 'timearea_qrstt', 'timearea_tmam', 'timearea_16absav', 'timearea_16absma']
    mtnms += ['timeitg_qrsabsn', 'timeitg_ttabsn', 'timeitg_ttsasn', 'timeitg_ttssn', 'timeitg_qrsttabsn', 'timeitg_qrsttdabsn', 'timeitg_qrsttsasn', 'timeitg_qrsttdsasn', 'timeitg_qrsttssn', 'timeitg_qrsttdssn']
    mtnms += ['time_stph']
    mtnms += ['mfm_ag_qrsa', 'mfm_ag_tta', 'mfm_ag_qrstta', 'mfm_ag_rdz']
    mtnms += ['pcdm_dpmm', 'time_twivls_0', 'time_twivls_1', 'time_twivls_2', 'time_twivls_3', 'time_twivls_4', 'time_stcgls_0', 'time_stcgls_1', 'time_stcgls_2', 'time_stcgls_3', 'time_stcgls_4', ]
    mtnms += ['pcdm_dpmm0', 'pcdm_dpmm10', 'pcdm_dpmm20', 'pcdm_dpmm30', 'pcdm_dpmm40', 'pcdm_dpmm50', 'pcdm_dpmm60', 'pcdm_dpmm70', 'pcdm_dpmmth10', 'pcdm_dpmmth', 'mfm_ag_qrsa1', 'mfm_ag_tta1', 'mfm_ag_qrstta1', 'mfm_ag_rdz1', 'mfm_ag_rdz2']
    mtnms += ['timearea_qrsmai1', 'timearea_qrstt1', 'timearea_tmam1', 'timearea_16absav1', 'timearea_16absma1']
    mtnms += ['timeitg_qrsabsn1', 'timeitg_qrsttabsn1', 'timeitg_qrsttdabsn1', 'timeitg_qrsttsasn1', 'timeitg_qrsttdsasn1', 'timeitg_qrsttssn1', 'timeitg_qrsttdssn1']
    mtnms += ['mfm_ag_qrsa2', 'mfm_ag_qrstta2']
    mtnms += ['time_twivls_3_1']
    mtnms += ['mfm_ag_qrsa3', 'mfm_ag_qrstta3', 'mfm_ag_rdz3', 'mfm_ag_rdz4']
    return mtnms


def get_metr_one0(mcg_text_path, d2, idxs):
    '''单人指标: 心磁, 模型地址, 时刻点'''
    Zqr = extractmt('%s/ischemic_mfm_Z/rkdb_qr22.txt' % d2)
    Zrs = extractmt('%s/ischemic_mfm_Z/rkdb_rs22.txt' % d2)
    Ztt = extractmt('%s/ischemic_mfm_Z/rkdb_tt22.txt' % d2)
    dfs = pcdm_default()  # 电流密度图初始参数
    Zqr1 = extractmt('%s/ischemic_pcdm_Z/rkdb_qr31.txt' % d2)
    Zrs1 = extractmt('%s/ischemic_pcdm_Z/rkdb_rs31.txt' % d2)
    Ztt1 = extractmt('%s/ischemic_pcdm_Z/rkdb_tt31.txt' % d2)
    # mtnmls, tmls, spls = mts_default0()  # 全部参数名称
    tmls, spls = mts_default0()  # 获取参数名称
    tmrd = [3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]#, 3, 3]
    sprd = [0, 0, 0, 3, 3, 0, 0]
    mcglines = get_lines(mcg_text_path)
    matrixes = cal_interpolate0(mcg_text_path)
    if idxs[0] != -1:  # 无时刻点
        timelis0 = idxs
    else:
        timelis = gettime(mcg_text_path)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        # Qp, Te = timelis0[0], timelis0[5]
        mf = get_mfs(get_lines(mcg_text_path))
        timelis0 = change_QS(timelis0, matrixes, mf)
    l0 = get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt)
    l1 = get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1)
    data, time_dic, rms = Data(mcg_text_path).get()
    tmmts = TimeWave(data, rms, time_dic).get_indicators()
    l2 = get_round([tmmts[nm[5:]] for nm in tmls], tmrd)
    spmts = SpaceWave(data, time_dic).get_indicators()
    l3 = get_round([spmts[nm[6:]] for nm in spls], sprd)
    l4 = get_disp1_qrst0(matrixes, timelis0, mcg_text_path)
    mf = get_mfs(get_lines(mcg_text_path), md=2)
    l5 = get_traject_mts0(matrixes, timelis0, mf)
    mts = hb_lis0(l0) + hb_lis0(l1) + hb_lis0(l2) + hb_lis0(l3) + hb_lis0(l4) + hb_lis0(l5)
    return mts


def get_metr1(d0, ids, d1, d2):
    '''计算mfm_pcdm_time_space指标&保存: d0数据集, d1指标, d2信息, ids样本名'''
    Zqr = extractmt('%s/ischemic_mfm_Z/rkdb_qr22.txt' % d2)
    Zrs = extractmt('%s/ischemic_mfm_Z/rkdb_rs22.txt' % d2)
    Ztt = extractmt('%s/ischemic_mfm_Z/rkdb_tt22.txt' % d2)
    a1 = [['mfm_disp.txt'], ['mfm_QR1.txt', 'mfm_QR2.txt', 'mfm_QR3.txt', 'mfm_QR4.txt', 'mfm_QR5.txt', 'mfm_QR6.txt'], ['mfm_RS1.txt', 'mfm_RS2.txt', 'mfm_RS3.txt', 'mfm_RS4.txt', 'mfm_RS5.txt', 'mfm_RS6.txt'], ['mfm_TT1.txt', 'mfm_TT2.txt', 'mfm_TT3.txt', 'mfm_TT4.txt', 'mfm_TT5.txt', 'mfm_TT6.txt']]
    mfmls = [[['Q', 'R', 'S', 'T', 'QRS', 'TT', 'noqrs']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['didma', 'didav', 'didmse', 'dicd', 'dicar', 'dicre9'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dppma', 'dppav', 'dppmse', 'dpnma', 'dpnav', 'dpnmse', 'pnpma', 'pnpflu', 'pnnma', 'pnnflu'], ['qa', 'ra', 'nq1', 'nq2', 'nq3', 'nq4', 'qra', 'q', 'm1', 'm2', 'a1', 'a2', 'areaf', 'aread'], ['epma', 'epav', 'epmse', 'enma', 'enav', 'enmse']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['didma', 'didav', 'didmse', 'dicd', 'dicar', 'dicre9'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dppma', 'dppav', 'dppmse', 'dpnma', 'dpnav', 'dpnmse', 'pnpma', 'pnpflu', 'pnnma', 'pnnflu'], ['ra', 'sa', 'nq1', 'nq2', 'nq3', 'nq4', 'rsa', 'q', 'm1', 'm2', 'a1', 'a2', 'areaf', 'aread'], ['epma', 'epav', 'epmse', 'enma', 'enav', 'enmse']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['didma', 'didav', 'didmse', 'dicd', 'dicar', 'dicre9'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dppma', 'dppav', 'dppmse', 'dpnma', 'dpnav', 'dpnmse', 'pnpma', 'pnpflu', 'pnnma', 'pnnflu'], ['ta', 'tt50', 'tt75', 'nq1', 'nq2', 'nq3', 'nq4', 'tta', 'q', 'm1', 'm2', 'a1', 'a2', 'areaf', 'aread'], ['epma', 'epav', 'epmse', 'enma', 'enav', 'enmse']]]
    a2 = [['pcdm_QR1.txt', 'pcdm_QR2.txt', 'pcdm_QR3.txt', 'pcdm_QR4.txt', 'pcdm_QR5.txt'], ['pcdm_RS1.txt', 'pcdm_RS2.txt', 'pcdm_RS3.txt', 'pcdm_RS4.txt', 'pcdm_RS5.txt'], ['pcdm_TT1.txt', 'pcdm_TT2.txt', 'pcdm_TT3.txt', 'pcdm_TT4.txt', 'pcdm_TT5.txt']]
    pcdmls = [[['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['mctd', 'mcar', 'mcre9', 'areac', 'aread'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dpma', 'dpav', 'dpmse', 'dvma', 'dvav', 'dvmse', 'mdama', 'mdaav', 'mdamse'], ['qca', 'qia', 'rca', 'ria', 'iama', 'iami', 'iaav', 'iamse', 'nq1', 'nq2', 'nq3', 'nq4', 'qra', 'q', 'm1', 'm2', 'a1', 'a2']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['mctd', 'mcar', 'mcre9', 'areac', 'aread'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dpma', 'dpav', 'dpmse', 'dvma', 'dvav', 'dvmse', 'mdama', 'mdaav', 'mdamse'], ['qca', 'qia', 'rca', 'ria', 'iama', 'iami', 'iaav', 'iamse', 'nq1', 'nq2', 'nq3', 'nq4', 'RSa', 'q', 'm1', 'm2', 'a1', 'a2']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['mctd', 'mcar', 'mcre9', 'areac', 'aread'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dpma', 'dpav', 'dpmse', 'dvma', 'dvav', 'dvmse', 'mdama', 'mdaav', 'mdamse'], ['qca', 'qia', 'rca', 'ria', 'iama', 'iami', 'iaav', 'iamse', 'nq1', 'nq2', 'nq3', 'nq4', 'TTa', 'q', 'm1', 'm2', 'a1', 'a2']]]
    dfs = pcdm_default()
    Zqr1 = extractmt('%s/ischemic_pcdm_Z/rkdb_qr31.txt' % d2)
    Zrs1 = extractmt('%s/ischemic_pcdm_Z/rkdb_rs31.txt' % d2)
    Ztt1 = extractmt('%s/ischemic_pcdm_Z/rkdb_tt31.txt' % d2)
    a3, a4 = 'time.txt', 'space.txt'
    tmls = ['areaqj', 'qrsttrt', 'rtrt', 'rtrt3', 'stfs', 'strs', 'stsl', 'stsl1', 'tmdf', 'tmdf1', 'tnirt', 'tnirt1', 'tpnrt1', 'tpnrt2', 'tsm', 'tsm1', 'ttflt', 'ttflt1', 'ttstpn', 'ttstpp', 'ttstprt']
    spls = ['rtsgnum', 'rtsgnum2', 'zeroR', 'zeroRTrot', 'zeroRTrot1', 'zeroT', 'zeroqrs']
    tmrd = [3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
    sprd = [0, 0, 0, 3, 3, 0, 0]
    l0, l1, l2, l3 = [], [], [], []
    for i in range(len(ids)):
        mfm_file = '%s/%s.txt' % (d0, ids[i])
        # mfm&pcdm
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file)
        mf = get_mfs(get_lines(mfm_file))
        timelis0 = change_QS(timelis0, matrixes, mf)
        l0.append(get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt))
        l1.append(get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1))
        # time&space
        data, time_dic, rms = Data(mfm_file).get()
        tmmts = TimeWave(data, rms, time_dic).get_indicators()
        l2.append(get_round([tmmts[nm] for nm in tmls], tmrd))
        spmts = SpaceWave(data, time_dic).get_indicators()
        l3.append(get_round([spmts[nm] for nm in spls], sprd))
    d00 = write_lis1(l0, mfmls, d1, a1, ids)
    d00 = write_lis1(l1, pcdmls, d1, a2, ids)
    d00 = write_lis2(l2, tmls, d1, a3, ids)
    d00 = write_lis2(l3, spls, d1, a4, ids)
    return d1


def write_lis1(l0, nms, d0, f1, ids):
    new_folder(d0)
    for i in range(len(f1)):
        for j in range(len(f1[i])):
            f2 = '%s/%s' % (d0, f1[i][j])
            strs = 'id, %s' % ', '.join(nms[i][j])
            for k in range(len(l0)):
                strs += '\n%s, %s' % (ids[k], ', '.join([str(l0[k][i][j][j1]) for j1 in range(len(l0[k][i][j]))]))
            write_str(strs, f2)
    return d0


def write_lis2(l0, nms, d0, f1, ids):
    new_folder(d0)
    f2 = '%s/%s' % (d0, f1)
    strs = 'id, %s' % ', '.join(nms)
    for i in range(len(l0)):
        strs += '\n%s, %s' % (ids[i], ', '.join([str(l0[i][j]) for j in range(len(l0[i]))]))
    write_str(strs, f2)
    return d0


def cal_bfbg0(l2, th1):
    '''计算起止点+波峰波谷点: 数据集l2, 最小间距th1'''
    ik0 = [0, len(l2)-1]
    ik, _ = signal.find_peaks(np.array(l2), distance=th1)
    ik1 = [j for j in ik]
    ik, _ = signal.find_peaks(-np.array(l2), distance=th1)
    ik2 = [j for j in ik]
    return [ik0, ik1, ik2]


def len_reshape0(l0):
    '''按照l0的列表长度, 将每个子list元素叠加在一起'''
    l1, l2 = l0[0], []
    for i in range(len(l1)):
        l3 = []
        for j in range(len(l0)):
            l3.append(l0[j][i])
        l2.append(l3)
    return l2


def len_reshape(l0):
    '''按照l0的列表长度, 将每个子list元素叠加在一起'''
    l1, l2 = l0[0], []
    for i in range(len(l1)):
        l3 = []
        for j in range(len(l1[i])):
            l4 = []
            for k in range(len(l1[i][j])):
                l5 = []
                for n in range(len(l0)):
                    l5.append(l0[n][i][j][k])
                l4.append(l5)
            l3.append(l4)
        l2.append(l3)
    return l2


def write_lis(l0, nms, d0):
    if type(nms) == list:
        for i in range(len(nms)):
            write_lis(l0[i], nms[i], d0)
    else:
        if nms:
            doc = open('%s/%s.txt' % (d0, nms), 'w')
            doc.write('\n'.join([str(l0[i]) for i in range(len(l0))]))
            doc.close()
    return d0


def write_pkl(data, f1):
    '''写入pkl'''
    with open(f1, 'wb') as f:
        pickle.dump(data, f)
    return f1


def load_pkl(f1):
    '''读取pkl'''
    with open(f1, 'rb') as f:  
        loaded_list = pickle.load(f)
    return loaded_list


def addqs0(qs, p1):
    pq1 = [0, 90]
    pq2 = [90, 180]
    pq3 = [180, 270]
    pq4 = [270, 360]
    pqs = [pq1, pq2, pq3, pq4]
    for j1 in range(4):
        if p1[0] <= pqs[j1][1] and pqs[j1][0] < p1[1]:  # 取低不取高
            pq = str(j1 + 1)
            if pq not in qs:
                qs.append(pq)
    return qs


def addqs(qs, p1):
    for i in range(4):
        p0 = p1[0]+(p1[1]-p1[0])*i/3
        pq = calquad(p0)
        if pq not in qs:
            qs.append(pq)
    return qs


def get_xlsx_value(data, ik=0):
    '''获取表格信息, ik仅获取第ik列的结果'''
    l0 = []
    for i in range(data.shape[0]):
        if ik:
            l0.append(str(data[i, ik-1]))
        else:
            if data.shape[1] == 1:  # 1列
                l0.append(str(data[i, 0]))
            else:
                l0.append([str(data[i, j]) for j in range(data.shape[1])])
    return l0


def addtrte(data1, strs, ids, strss=0):
    '''从表格不重复的提取数据strs, ids存放名称-重复跳过'''
    for i3 in range(data1.shape[0]):
        if str(data1[i3, 0]) in ids:  # 去重复
            continue
        else:
            ids.append(str(data1[i3, 0]))
        for j3 in range(data1.shape[1]):
            if strss != 0:
                strss[j3].append(str(data1[i3, j3]))
            if j3 == 0:
                strs += '\n%s' % str(data1[i3, j3])
            else:
                strs += ', %s' % str(data1[i3, j3])
    return strs


def analysis_metr0(d0, d1, lbs, ids0, ids1, fd):
    '''
    指标分布图+效用参数:
        d0指标, d1分析, lbs标签, ids0/1训练测试索引, fd折
    '''
    n0, n1, n2, n3 = len(ids0[0]), len(ids0[1]), len(ids1[0]), len(ids1[1])
    nmax = max([n0, n1, n2, n3])
    nnew = 4 * nmax / (n0+n1+n2+n3)
    f1 = '%s/%s_mess.txt' % (d1, lbs)
    f2 = '%s/%s_para.txt' % (d1, lbs)
    s1 = 'mts, n0, p00, p01, n1, p10, p11, ..., n19, p90, p91'
    s2 = 'mts, error_5, 10, 20'
    # item = 'mfm_QR_af.txt'  # 单例测试
    # 提前选取的可独立分级指标
    itemlis = ['mfm_disp_Q', 'mfm_disp_R', 'mfm_disp_S', 'mfm_disp_T', 'mfm_disp_QRS', 'mfm_disp_TT', 'mfm_disp_noqrs', 'mfm_QR_center', 'mfm_RS_center', 'mfm_TT_center', 'mfm_QR_mt2', 'mfm_RS_mt2', 'mfm_TT_mt2', 'mfm_QR_didma', 'mfm_RS_didma', 'mfm_TT_didma', 'mfm_QR_didav', 'mfm_RS_didav', 'mfm_TT_didav', 'mfm_QR_didmse', 'mfm_RS_didmse', 'mfm_TT_didmse', 'mfm_QR_dicd', 'mfm_RS_dicd', 'mfm_TT_dicd', 'mfm_QR_dicar', 'mfm_RS_dicar', 'mfm_TT_dicar', 'mfm_QR_wg', 'mfm_RS_wg', 'mfm_TT_wg', 'mfm_QR_pm', 'mfm_RS_pm', 'mfm_TT_pm', 'mfm_QR_nm', 'mfm_RS_nm', 'mfm_TT_nm', 'mfm_QR_r', 'mfm_RS_r', 'mfm_TT_r', 'mfm_QR_v', 'mfm_RS_v', 'mfm_TT_v', 'mfm_QR_mse', 'mfm_RS_mse', 'mfm_TT_mse', 'mfm_QR_af', 'mfm_RS_af', 'mfm_TT_af', 'mfm_QR_ad', 'mfm_RS_ad', 'mfm_TT_ad', 'mfm_QR_v1', 'mfm_RS_v1', 'mfm_TT_v1', 'mfm_QR_mse1', 'mfm_RS_mse1', 'mfm_TT_mse1', 'mfm_QR_saf', 'mfm_RS_saf', 'mfm_TT_saf', 'mfm_QR_sad', 'mfm_RS_sad', 'mfm_TT_sad', 'mfm_QR_dppma', 'mfm_RS_dppma', 'mfm_TT_dppma', 'mfm_QR_dppav', 'mfm_RS_dppav', 'mfm_TT_dppav', 'mfm_QR_dppmse', 'mfm_RS_dppmse', 'mfm_TT_dppmse', 'mfm_QR_dpnma', 'mfm_RS_dpnma', 'mfm_TT_dpnma', 'mfm_QR_dpnav', 'mfm_RS_dpnav', 'mfm_TT_dpnav', 'mfm_QR_dpnmse', 'mfm_RS_dpnmse', 'mfm_TT_dpnmse', 'mfm_QR_epma', 'mfm_RS_epma', 'mfm_TT_epma', 'mfm_QR_epav', 'mfm_RS_epav', 'mfm_TT_epav', 'mfm_QR_epmse', 'mfm_RS_epmse', 'mfm_TT_epmse', 'mfm_QR_enma', 'mfm_RS_enma', 'mfm_TT_enma', 'mfm_QR_enav', 'mfm_RS_enav', 'mfm_TT_enav', 'mfm_QR_enmse', 'mfm_RS_enmse', 'mfm_TT_enmse', 'mfm_QR_qa', 'mfm_QR_ra', 'mfm_QR_qra', 'mfm_RS_sa', 'mfm_RS_rsa', 'mfm_TT_ta', 'mfm_TT_tt50', 'mfm_TT_tt75', 'mfm_TT_tta', 'mfm_QR_qs', 'mfm_RS_qs', 'mfm_TT_qs', 'mfm_QR_nums', 'mfm_RS_nums', 'mfm_TT_nums', 'mfm_QR_pnpma', 'mfm_RS_pnpma', 'mfm_TT_pnpma', 'mfm_QR_pnpflu', 'mfm_RS_pnpflu', 'mfm_TT_pnpflu', 'mfm_QR_pnnma', 'mfm_RS_pnnma', 'mfm_TT_pnnma', 'mfm_QR_pnnflu', 'mfm_RS_pnnflu', 'mfm_TT_pnnflu', 'space_zeroR', 'time_tmdf', 'time_htrt', 'time_ttstpp', 'time_rtrt3', 'time_tpnrt2', 'time_ttflt', 'time_tnirt', 'space_rtsgnum2', 'time_pnum', 'time_qrsttrt', 'time_tsm', 'time_qsitv', 'time_strs', 'time_tpnrt1', 'time_rtrt', 'space_zeroqrs', 'time_stsl', 'time_ttstprt', 'space_zeroT', 'space_zeroRTrot', 'time_tmdf1', 'time_tsm1', 'time_tnirt1', 'time_stsl1', 'space_zeroRTrot1', 'time_ttstpn', 'time_areaqj', 'space_rtsgnum', 'time_ttflt1', 'time_stfs']
    for item in os.listdir(d0):
        if item[:-4] not in itemlis:
            continue
        lis0 = txt_to_list('%s/%s' % (d0, item))
        if type(lis0[0]) != tuple:  # 指标数值
            mtlis, er0 = [], 0
            for ids2 in [ids0, ids1]:
                for i2 in range(2):
                    mtlis.append([lis0[j2] for j2 in ids2[i2]])  # tr0/tr1/te0/te1
            lis0 = mtlis[0] + mtlis[1] + mtlis[2] + mtlis[3]
            s1 += '\n%s' % item[:-4]
            s2 += '\n%s' % item[:-4]
            g1 = '%s/%s/%s.png' % (d1, lbs, item[:-4])
            lis01, lis02, lis03, lis04, xtks = [], [], [], [], []
            # for fd in [5, 10, 20]:
            segmt = get_seg0(lis0, fd)
            for i2 in range(len(segmt)-1):
                m0, m1 = segmt[i2], segmt[i2+1]
                nall = cal_list_num(lis0, m0, m1) * nnew
                ntr0 = cal_list_num(mtlis[0], m0, m1) * nmax / n0
                ntr1 = cal_list_num(mtlis[1], m0, m1) * nmax / n1
                nte0 = cal_list_num(mtlis[2], m0, m1) * nmax / n2
                nte1 = cal_list_num(mtlis[3], m0, m1) * nmax / n3
                xtks.append('%.3f' %segmt[i2+1])
                lis01.append(ntr0)
                lis02.append(ntr1)
                lis03.append(nte0)
                lis04.append(nte1)
                try:
                    p00 = get_round(ntr1/(ntr0+ntr1), 3)
                except:
                    p00 = 0
                try:
                    p01 = get_round(nte1/(nte0+nte1), 3)
                except:
                    p01 = 0
                s1 += ', %d, %.3f, %.3f' % (nall, p00, p01)
                er0 += get_round(abs(p00-p01) * nall / len(lis0), 3)
            tit = '%s distrib density: %.3f' % (item[:-4], er0)
            g2 = draw_ddm0(g1, lis01, lis02, lis03, lis04, xtks, tit)
            s2 += ', %.3f' % er0
    write_str(s1, f1)
    write_str(s2, f2)
    return f2


def revi_mode1(ls, comb, pts, iks, tx2, strs, mt):
    l0, l11, l22 = ls
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    j2 = 0
    ids0, ids1 = [], []
    for k2 in range(len(iks[j2])):  # 训练集
        ids0 += pts[iks[j2][k2]][0]
        ids1 += pts[iks[j2][k2]][1]
    l1 = comb_res1([[l0[j][k] for k in comb] for j in ids0], n01, n00)
    l2 = comb_res1([[l0[j][k] for k in comb] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc, sen, spe = roc[-1]
    strs += '\n%s:\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-外部验证-' % (mt, auc0, acc, sen, spe)
    for j2 in range(1, len(iks)):  # 测试集
        ids0, ids1 = [], []
        for k2 in range(len(iks[j2])):
            ids0 += pts[iks[j2][k2]][0]
            ids1 += pts[iks[j2][k2]][1]
        l1 = comb_res1([[l0[j][k] for k in comb] for j in ids0], n01, n00)
        l2 = comb_res1([[l0[j][k] for k in comb] for j in ids1], n01, n00)
        auc0, roc0 = cal_auc0(l1, l2)
        roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
        _, acc, sen, spe = roc[0]
        strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f' % (auc0, acc, sen, spe)
    return strs


def revi_mode4(comb, ls, ids, rks, strs):
    l00, l01, l0, l1 = ls
    ids0, ids1, ids2 = ids
    rkl0, rkl1 = rks
    nr0, nr1, ne1 = len(ids0), len(ids1), len(ids2)
    l02 = l00.copy()
    l11 = l0.copy()
    rkl2 = rkl0.copy()
    for j in comb:
        l02[j] = l01[j]  # 替换新分级下的诊断结果
        l11[j] = l1[j]  # 替换新分级上下阈值
        rkl2[j] = rkl1[j]  # 替换新分级
    l12 = []
    l12.append([l11[j][0] for j in range(len(l11))])
    l12.append([l11[j][1] for j in range(len(l11))])
    n01, n00 = sum(l12[0])-50*len(l12[0]), 50*len(l12[1])-sum(l12[1])
    l02 = [[l02[j][k] for j in range(len(l02))] for k in range(len(l02[0]))]
    l10 = comb_res3([l02[j] for j in ids0], n01, n00)
    l11 = comb_res3([l02[j] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l10, l11)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc, sen, spe = roc[-1]
    strs += '\n\nsearch_%d: %s\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-外部验证-' % (len(comb), str(comb), auc0, acc, sen, spe)
    l12 = comb_res3([l02[j] for j in ids2], n01, n00)
    tp = sum(1 for p0 in l12 if p0 >= p1)
    acc = round(tp / ne1, 3)
    strs += '\nAuc-0.***+Acc-%.3f+Sen-%.3f+Spe-0.000' % (acc, acc)
    return strs


def revi_mode0(ls, comb, pts, iks, tx2, strs):
    l0, l11, l22 = ls
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    j2 = 0
    ids0, ids1 = [], []
    for k2 in range(len(iks[j2])):  # 训练集
        ids0 += pts[iks[j2][k2]][0]
        ids1 += pts[iks[j2][k2]][1]
    l1 = comb_res1([[l0[j][k] for k in comb] for j in ids0], n01, n00)
    l2 = comb_res1([[l0[j][k] for k in comb] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc, sen, spe = roc[-1]
    strs += '\n\nsearch_%d: %s\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-外部验证-' % (len(comb), str(comb), auc0, acc, sen, spe)
    for j2 in range(1, len(iks)):  # 测试集
        ids0, ids1 = [], []
        for k2 in range(len(iks[j2])):
            ids0 += pts[iks[j2][k2]][0]
            ids1 += pts[iks[j2][k2]][1]
        l1 = comb_res1([[l0[j][k] for k in comb] for j in ids0], n01, n00)
        l2 = comb_res1([[l0[j][k] for k in comb] for j in ids1], n01, n00)
        auc0, roc0 = cal_auc0(l1, l2)
        roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
        _, acc, sen, spe = roc[0]
        strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f' % (auc0, acc, sen, spe)
    return strs


def get_lable_data01(pts, iks, j2):
    '''获取pts的索引列表'''
    ids0, ids1 = [], []
    for k2 in range(len(iks[j2])):  # 训练集
        ids0 += pts[iks[j2][k2]][0]
        ids1 += pts[iks[j2][k2]][1]
    return ids0, ids1


def revi_mode5(ls, comb, pts, iks, strs):
    l0, l11, l22 = ls
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
    ids0, ids1 = get_lable_data01(pts, iks, 0)
    l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc, sen, spe = roc[-1]
    print(p1)
    a = get_lable_data01(pts, iks, 0)
    ids0 = a[0]
    ids1 = a[1]
    # l1 = comb_res3([l00[j] for j in range(len(l00))], n01, n00)
    l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc, sen, spe = roc[-1]
    # print(p1)
    strs += '\n\nsearch_%d: %s\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f' % (len(comb), str(comb), auc0, acc, sen, spe)
    # return l1
    return strs


def revi_mode6(ls, comb, pts, iks, strs):
    '''tr阈值-测试tr/va/trva/te'''
    l0, l11, l22 = ls
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
    l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
    ids0, ids1 = get_lable_data01(pts, iks, 0)
    l10 = comb_res3([l00[j] for j in ids0], n01, n00)
    l20 = comb_res3([l00[j] for j in ids1], n01, n00)
    ids0, ids1 = get_lable_data01(pts, iks, 1)
    l11 = comb_res3([l00[j] for j in ids0], n01, n00)
    l21 = comb_res3([l00[j] for j in ids1], n01, n00)
    # ids0, ids1 = get_lable_data01(pts, iks, 2)
    # l13 = comb_res3([l00[j] for j in ids0], n01, n00)
    # l23 = comb_res3([l00[j] for j in ids1], n01, n00)
    auc2, roc0 = cal_auc0(l10+l11, l20+l21)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc2, sen2, spe2 = roc[-1]  # 敏感度
    print(p1)
    # acc
    # acc = [roc0[i][1] for i in range(len(roc0))]
    # p1, acc0, sen0, spe0 = roc0[acc.index(max(acc))]
    auc0, roc0 = cal_auc0(l10, l20)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc0, sen0, spe0 = roc[0]
    auc1, roc0 = cal_auc0(l11, l21)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc1, sen1, spe1 = roc[0]
    # auc3, roc0 = cal_auc0(l13, l23)
    # roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    # _, acc3, sen3, spe3 = roc[0]
    strs += '\n\nsearch_%d: %s, %d\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Tr' % (len(comb), str(comb), p1, auc0, acc0, sen0, spe0)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Va' % (auc1, acc1, sen1, spe1)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-TrVa' % (auc2, acc2, sen2, spe2)
    # strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Te' % (auc3, acc3, sen3, spe3)
    return strs


def test_012(l4, tr0, tr1, tr2):  # 测试阴性阳性偏阳性
    l10, l20, l30 = [l4[i] for i in tr0], [l4[i] for i in tr1], [l4[i] for i in tr2]
    auc1, _ = cal_auc0(l10, l20+l30)
    spe1 = sum(1 for p0 in l10 if p0<50) / len(l10)
    sen11 = sum(1 for p0 in l20 if p0>=50) / len(l20)
    sen12 = sum(1 for p0 in l30 if p0>=50) / len(l30)
    acc1 = (sum(1 for p0 in l10 if p0<50)+sum(1 for p0 in l20 if p0>=50)+sum(1 for p0 in l30 if p0>=50)) / len(l10+l20+l30)
    acc2 = (sum(1 for p0 in l10 if p0<50)+sum(1 for p0 in l20 if p0>=50)) / len(l10+l20)
    return auc1, acc1, acc2, sen11, spe1, sen12


def revi_mode7(l4, tr0, tr1, tr2, te0, te1, te2, f0):
    '''测试2合1模型tr_te: '''
    auc1, acc11, acc12, sen11, spe1, sen12 = test_012(l4, tr0, tr1, tr2)
    auc2, acc21, acc22, sen21, spe2, sen22 = test_012(l4, te0, te1, te2)
    strs = 'Auc-%.3f+Acc1-%.3f+Acc2-%.3f+Sen1-%.3f+Spe-%.3f+Sen2-%.3f-TrVa' % (auc1, acc11, acc12, sen11, spe1, sen12)
    strs += '\nAuc-%.3f+Acc1-%.3f+Acc2-%.3f+Sen1-%.3f+Spe-%.3f+Sen2-%.3f-Te' % (auc2, acc21, acc22, sen21, spe2, sen22)
    write_str(strs, f0)
    return f0


def revi_mode3(ls, comb, pts, iks, strs):
    l0, l11, l22 = ls
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
    # ids0, ids1 = get_lable_data01(pts, iks, 0)
    # l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    # l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    # auc0, roc0 = cal_auc0(l1, l2)
    # roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    # p1, acc, sen, spe = roc[-1]
    # print(p1)
    a = get_lable_data01(pts, iks, 0)
    b = get_lable_data01(pts, iks, 1)
    ids0 = a[0]+b[0]
    ids1 = a[1]+b[1]
    l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    # for i in range(len(l1)):
    #     if l1[i]>0:
    #         # print(comb)
    #         # print(n01, n00)
    #         print(l00[i])
    #         # print(l1[i], ids0[i])
    #         break
    l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc, sen, spe = roc[-1]
    print(p1)
    strs += '\n\nsearch_%d: %s\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-外部验证-' % (len(comb), str(comb), auc0, acc, sen, spe)
    ids0, ids1 = get_lable_data01(pts, iks, 2)
    l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    # l01 = [l00[j] for j in ids0+ids1]  # 保存315测试集结果
    # l12 = l1+l2
    # ids0 = [ids[i] for i in ids0+ids1]
    # l0 = [ids0[i]+', '+str(l01[i])+', '+str(l12[i]) for i in range(len(l12))]
    # write_str('\n'.join(l0), './results00.txt')
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc, sen, spe = roc[0]
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f' % (auc0, acc, sen, spe)
    return strs


def revi_mode2(ls, comb, pts, iks, tx2, strs):
    l0, l11, l22 = ls
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
    j2 = 0
    ids0, ids1 = get_lable_data01(pts, iks, 0)
    l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][2] >= roc0[i][3]]
    p1, acc, sen, spe = roc[-1]
    print(p1)
    a = get_lable_data01(pts, iks, 0)
    b = get_lable_data01(pts, iks, 1)
    ids0 = a[0]+b[0]
    ids1 = a[1]+b[1]
    l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc, sen, spe = roc[0]
    strs += '\n\nsearch_%d: %s\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-外部验证-' % (len(comb), str(comb), auc0, acc, sen, spe)
    ids0, ids1 = get_lable_data01(pts, iks, 2)
    l1 = comb_res3([l00[j] for j in ids0], n01, n00)
    l2 = comb_res3([l00[j] for j in ids1], n01, n00)
    auc0, roc0 = cal_auc0(l1, l2)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc, sen, spe = roc[0]
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f' % (auc0, acc, sen, spe)
    return strs


# def cal_auc1(l1, l2, f1='', auc=1):
#     '''roc曲线和auc值: l1阴性概率, l2阳性概率, f1保存txt'''
#     l12 = list(set(l1+l2))  # roc曲线上的全部概率
#     l12.sort()
#     rocs, aucs = [], []
#     for i3 in range(len(l12)):
#         fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
#         tp = sum(1 for p0 in l2 if p0 >= l12[i3])
#         sen, spe = tp/len(l2), (len(l1)-fp)/len(l1)
#         acc, fpr = (len(l1)-fp+tp)/len(l1+l2), 1-spe
#         rocs.append(get_round([l12[i3], acc, sen, spe], [1, 3, 3, 3]))
#         aucs.append((fpr, sen))
#     aucs += [(0, 0), (1, 0), (1, 1)]  # 补充端点
#     if f1:
#         save_txt(rocs, 'list', f1)
#     if auc:
#         auc0 = getAreaOfPolyGonbyVector(convex_hull(aucs))  # roc曲线下面积
#         return auc0, rocs
#     else:
#         return rocs


def cal_auc0(l1, l2, f1='', auc=1):
    '''roc曲线和auc值: l1阴性概率, l2阳性概率, f1保存txt'''
    l12 = list(set(l1+l2))  # roc曲线上的全部概率
    l12.sort()
    rocs, aucs = [], []
    for i3 in range(len(l12)):
        fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
        tp = sum(1 for p0 in l2 if p0 >= l12[i3])
        sen, spe = tp/len(l2), (len(l1)-fp)/len(l1)
        acc, fpr = (len(l1)-fp+tp)/len(l1+l2), 1-spe
        rocs.append(get_round([l12[i3], acc, sen, spe], [0, 3, 3, 3]))
        aucs.append((fpr, sen))
    aucs += [(0, 0), (1, 0), (1, 1)]  # 补充端点
    if f1:
        save_txt(rocs, 'list', f1)
    if auc:
        auc0 = getAreaOfPolyGonbyVector(convex_hull(aucs))  # roc曲线下面积
        return auc0, rocs
    else:
        return rocs


def cal_cettrj_pts(matrixes_list, times_list, seqs=[1, 2]):
    '''
    中心轨迹点集计算，返回全部人员的点集列表
    matrixes_list插值矩阵list
    times_list时刻点list
    seqs起止时刻点, 如QpRp
    '''
    cttjpts = []
    for i11 in range(len(matrixes_list)):
        matrixes, times = matrixes_list[i11], times_list[i11]
        time1, time2 = int(times[seqs[0]]), int(times[seqs[1]])
        cjpts = cal_cjpts(matrixes, time1, time2)  # 计算中心轨迹点集
        cttjpts.append(cjpts)
    return cttjpts


def cal_cjpt(matrixes, i1):
    '''计算一帧的偶极子中心, 最多4个点'''
    matrix = matrixes[i1-1]
    print(matrix)
    px, py, nx, ny = get_dipole(matrix)
    cx, cy = (px + nx) / 2, (py + ny) / 2
    cors = get_dipole_ct(cx, cy)
    return cors


def cal_cjpts(matrixes, time1, time2=None):
    '''计算QR中心轨迹点集[[x0, y0], [x1, y1], ..., [xN, yN]], 或返回一帧中心轨迹'''
    if time2:
        cjpts = []
        for i1 in range(time1, time2+1):
            cjpt = cal_cjpt(matrixes, i1)
            for cors in cjpt:
                if cors not in cjpts:
                    cjpts.append(cors)
    else:
        cjpts = cal_cjpt(matrixes, time1)
    return cjpts


def cal_fr(matrixes, dfs, r_peak, s_peak):
    '''计算单帧信息PCDM, RS为例'''
    frfile = []
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        v, ca, x, y, dp, dv, mda = plot_pic(matrix, dfs)
        v, ca, x, y, dp, dv, mda = get_round([v, ca, x, y, dp, dv, mda], [6, 1, 0, 0, 1, 1, 1])
        frfile.append([j, v, ca, x, y, dp, dv, mda])
    Mv = max([frfile[j][1] for j in range(len(frfile))])
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        ca = frfile[j-r_peak][2]
        px, py, nx, ny = get_dipole(matrix)
        dx, dy = ny - py, px - nx  # 坐标和角度指向
        da = calangle(dx, dy)  # 指向角度
        q = calquad(ca)  # 象限
        r = abs(da - ca)
        ia = min(r, 360 - r)  # 夹角
        qrd = pow(dx*dx+dy*dy, 1/2)  # 偶极子距离
        fx, fy = calangcors(frfile[j-r_peak][1] / Mv, ca)
        mx, my = calangcors(qrd / pow(50*50+50*50, 1/2), ca)
        da, ia, fx, fy, mx, my = get_round([da, ia, fx, fy, mx, my], [1, 1, 3, 3, 3, 3])
        frfile[j-r_peak].extend([q, da, ia, fx, fy, mx, my])
    return frfile


def cal_frrt(matrixes, r_peak, s_peak):
    '''计算单帧信息和转角信息, RS为例'''
    Mf = calmf(matrixes, r_peak, s_peak)
    frfile = []
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        a, q, qrd, fx, fy, mx, my, cx, cy = statpnpt(matrix, Mf)
        Z = norms0(matrix)  # 正负归一化
        pn = statpn(Z, vmin=0.3)  # 多极子
        dpp, dpn = caldp(pn)  # 离散度
        pnp, pnn = calpn(pn)  # 主极子数量
        ep, en = calplf(Z)  # 离心率
        a, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, ep, en = get_round([a, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, ep, en], [1, 1, 3, 3, 3, 3, 1, 1, 1, 1, 3, 3])
        frfile.append([j, a, q, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, pnp, pnn, ep, en])  # 信息集
    return frfile


def cal_list_num(lis0, m0=0, m1=1):
    '''计算列表中在数值区间的数量'''
    lis1 = [lis0[i5] for i5 in range(len(lis0)) if lis0[i5] > m0 and lis0[i5] < m1]
    return len(lis1)


def cal_noqrs(lis0):
    '''综合QRS波形异常指标, 321分别表示Q/R/S异常'''
    noqrs = ''
    for i4 in range(len(lis0)):
        if lis0[i4] == 1:
            noqrs += str(len(lis0)-i4)
    if noqrs == '':
        return '0'
    else:
        return noqrs


def cal_pnptm(matrixes, mflis0, time12):
    '''获取时刻点修正信息, time12为时刻点区间'''
    pnptm2 = []
    for j1 in time12:
        matrix = matrixes[j1-1]
        mf = mflis0[j1-1]
        pna = cal_zfzxjd0(matrix)  # 指向角度
        matrix = norms0(matrix)
        pn = statpn(matrix, vmin=0.3)
        dpp, dpn = caldp(pn)
        mf, dpp, dpn, pna = get_round([mf, dpp, dpn, pna], [1, 1, 1, 1])
        pnptm2.append([j1, mf, dpp, dpn, pna])
    return pnptm2


def cal_qra_q(alist):
    qs, ps = [], []
    for i1 in range(len(alist) - 1):
        difag = abs(alist[i1] - alist[i1 + 1])
        if difag < 180:
            p1 = (min(alist[i1], alist[i1 + 1]), max(alist[i1], alist[i1 + 1]))
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # 向量指向长度 == min-max
            ps.append(p2)
        else:
            p1 = (0, min(alist[i1], alist[i1 + 1]))
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # 0-min
            ps.append(p2)
            p1 = (max(alist[i1], alist[i1 + 1]), 360)
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # max-360
            ps.append(p2)
    mp = MultiLineString(ps)
    qra = unary_union(mp).length  # 角度跨度
    for i in range(len(alist)):
        qs.append(calquad(alist[i]))
    q = statqs(qs)  # 象限
    return qra, q


def cal_stabs(dpp0, dpps, dpns, pntts, rg12):
    '''计算稳定性列表'''
    stabs = []
    if min(dpp0) >= 10:
        for j1 in rg12:
            if dpps[j1] > 10:  # 稳定阈值——
                dpps[j1] = 1
            else:
                dpps[j1] = 0
    else:
        for j1 in rg12:
            if dpp0[j1] >= 10:  # 稳定判定——
                dpps[j1] = 1
            else:
                dpps[j1] = 0
    for j1 in rg12:
        if dpns[j1] > 10:  # 稳定阈值——
            dpns[j1] = 1
        else:
            dpns[j1] = 0
        if pntts[j1] > 60:  # 稳定阈值——
            pntts[j1] = 1
        else:
            pntts[j1] = 0
        stabs.append(max(dpps[j1], dpns[j1], pntts[j1]))
    return stabs


def cal_utility0(d0, lbs, mtnms, d2, pts):
    '''计算效用因子: d0信息、lbs模型、mtnms指标、d2诊断'''
    ids0, ids1 = pts[0], pts[1]
    f4 = '%s/%s/utility_all.txt' % (d0, lbs)
    f5 = '%s/%s/utility_sort.txt' % (d0, lbs)
    f6 = '%s/%s/utility_idx.txt' % (d0, lbs)
    strs = []
    for i2 in range(len(mtnms)):
        lis0 = txt_to_list('%s/%s/%s.txt' % (d2, lbs, mtnms[i2]))
        l1, flag = [], 1
        for ids2 in [ids0, ids1]:
            for k2 in range(2):
                l1.append([lis0[j2] for j2 in ids2[k2]])
        l1[0] = [50-l1[0][j2] for j2 in range(len(l1[0])) if l1[0][j2] != 50]
        l1[1] = [l1[1][j2]-50 for j2 in range(len(l1[1])) if l1[1][j2] != 50]
        l1[2] = [50-l1[2][j2] for j2 in range(len(l1[2])) if l1[2][j2] != 50]
        l1[3] = [l1[3][j2]-50 for j2 in range(len(l1[3])) if l1[3][j2] != 50]
        uttr = sum(l1[0]+l1[1])
        utte = sum(l1[2]+l1[3])
        if uttr < 0 or utte < 0:
            flag = -1
        strs.append([uttr, utte, uttr+utte, flag])
    f4 = save_txt(strs, 'list', f4)
    lis1 = txt_to_list(f4)
    data = np.array(lis1)
    idex = np.lexsort((-1 * data[:, 1], -1 * data[:, 0], -1 * data[:, 2], -1 * data[:, 3]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    save_txt(lis1, 'list', f5)
    write_str(str(list(idex)), f6)
    return f4


def cal_utility1(l1, l2):
    '''计算效用因子: l1阴性概率, l2阳性概率'''
    tn = sum(1 for j in range(len(l1)) if l1[j] < 50)
    tp = sum(1 for j in range(len(l2)) if l2[j] >= 50)
    fn = len(l1) - tn
    fp = len(l2) - tp
    acc = (tp+tn)/(len(l1+l2))
    perc, reca, f1sc = 0, 0, 0
    if tp+fp:
        perc = tp/(tp+fp)
    if tp+fn:
        reca = tp/(tp+fn)
    if perc+reca:
        f1sc = 2*(perc*reca)/(perc+reca)
    auc, _ = cal_auc0(l1, l2)
    return get_round([auc, acc, f1sc], [3, 3, 3])


def calalis(frfile, ik=1):
    '''正负指向指标'''
    alist = []
    for i1 in range(len(frfile)):
        alist.append(frfile[i1][ik])
    qa, ra = get_ht(frfile, ik=ik)  # Q、R波角度
    qra, q = cal_qra_q(alist)
    m1, m2, a1, a2 = stataf(alist)  # 角度范围
    return qa, ra, qra, q, m1, m2, a1, a2


def calalis_tt(frfile, t_idx):
    '''正负指向指标-TT'''
    alist, rtlist = [], []
    for i1 in range(len(frfile)):
        alist.append(frfile[i1][1])
        if i1 > 0:
            rota = abs(alist[-1]-alist[-2])
            rota = min(360-rota, rota)
            rtlist.append(rota)
    rtlist.sort()  # TT50、TT75
    tt50 = np.median(rtlist)  # 中位数
    tt75 = np.percentile(rtlist, (75))  # 第3四分位数
    ta = frfile[t_idx][1]  # T波角度
    tta, q = cal_qra_q(alist)
    m1, m2, a1, a2 = stataf(alist)  # 角度范围
    return ta, tt50, tt75, tta, q, m1, m2, a1, a2


def calangcors(V1, ca):
    fx = V1 * math.cos(ca / 180 * np.pi)
    fy = V1 * math.sin(ca / 180 * np.pi)
    return fx, fy


def calangle(dx, dy):
    '''[0, 360)'''
    if dx == 0 and dy == 0:
        return 0
    z = math.sqrt(dx * dx + dy * dy)
    if dx >= 0 and dy >= 0:
        angle = math.degrees(math.asin(dy / z))
    elif dx < 0 and dy >= 0:
        angle = 180 - math.degrees(math.asin(dy / z))
    elif dx < 0 and dy < 0:
        angle = 180 + math.degrees(math.asin(-dy / z))
    elif dx >= 0 and dy < 0:
        angle = 360 - math.degrees(math.asin(-dy / z))
    return round(angle, 1)


def calarea(frfile, ix, iy):
    xss, yss, pgs = [], [], []
    for j1 in range(len(frfile)):
        xss.append(frfile[j1][ix])
        yss.append(frfile[j1][iy])
        if j1 > 0:
            pg1 = Polygon([[0, 0], [xss[-2], yss[-2]], [xss[-1], yss[-1]]])
            pgs.append(pg1)
    mpg = MultiPolygon(pgs)
    mpgv = unary_union(mpg).area  # 面积
    return mpgv


def calarea1(xss, yss):
    pgs = []
    for j1 in range(len(xss) - 1):
        pg1 = Polygon([[0, 0], [xss[j1], yss[j1]], [xss[j1 + 1], yss[j1 + 1]]])
        pgs.append(pg1)
    mpg = MultiPolygon(pgs)
    mpgv = unary_union(mpg).area  # 面积
    return mpgv


def calavacors(alis, lis):
    xss, yss = [], []
    for i1 in range(len(alis)):
        dx = np.cos(alis[i1] * np.pi / 180)
        dy = np.sin(alis[i1] * np.pi / 180)
        dz = 0
        for j1 in range(len(lis[i1][0])):
            fx, fy = lis[i1][0][j1], lis[i1][1][j1]
            fz = pow(fx*fx+fy*fy, 1/2)
            dz += fz
        dz = dz / len(lis[i1][0])  # 平均归一化幅值
        xs, ys = calptcors(dz, dx, dy)
        xss.append(xs)
        yss.append(ys)
    return xss, yss


def caldp(pns):
    disps = [0, 0]  # 无极子和单极子都是0
    wk = 2  # 权重
    for j1 in range(2):
        if len(pns[j1]) != 0:
            corx = pns[j1][0][1]
            cory = pns[j1][0][2]
            for j2 in range(1, len(pns[j1])):
                dx = abs(pns[j1][j2][1]-corx)
                dy = abs(pns[j1][j2][2]-cory)
                dk = pow(dx*dx+dy*dy, 1/2) / 10
                vk = pns[j1][j2][0]
                disps[j1] = disps[j1] + wk * dk * vk
    disps[0] = round(disps[0], 1)
    disps[1] = round(-1*disps[1], 1)
    return disps[0], disps[1]


def calfag(ags):
    ps = []
    for i1 in range(len(ags) - 1):
        difag = abs(ags[i1] - ags[i1 + 1])
        if difag < 180:
            p1 = (min(ags[i1], ags[i1 + 1]), max(ags[i1], ags[i1 + 1]))
            p2 = ((0, p1[0]), (0, p1[1]))  # 向量指向长度 == min-max
            ps.append(p2)
        else:
            p1 = (0, min(ags[i1], ags[i1 + 1]))
            p2 = ((0, p1[0]), (0, p1[1]))  # 0-min
            ps.append(p2)
            p1 = (max(ags[i1], ags[i1 + 1]), 360)
            p2 = ((0, p1[0]), (0, p1[1]))  # max-360
            ps.append(p2)
    mp = MultiLineString(ps)
    fag = unary_union(mp).length  # 角度跨度
    return fag


def caljr(frfile, rlis, seq, seqs1, seqs2):
    js, ns, tlis, alis, mse3, ts, ags, rts, fxs, fys, mxs, mys, xss, yss, xss1, yss1 = [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []
    for i1 in range(len(frfile)):
        fline = frfile[i1]
        tlis.append(fline[0])  # 时刻
        alis.append(fline[seq])  # 角度
        fxs.append(fline[seqs1[0]])  # 坐标..
        fys.append(fline[seqs1[1]])
        mxs.append(fline[seqs2[0]])
        mys.append(fline[seqs2[1]])
    tlis.append(fline[0] + 1)
    rlis1 = sorted(list(np.absolute(rlis)), reverse=True)  # 绝对值逆序
    for i1 in range(7):
        rmse = np.std(rlis1[i1:i1 + 3])
        mse3.append(rmse)
    m = 0
    if mse3[4] <= 2.23:
        for j1 in range(4):  # 前4个
            k1 = 3 - j1
            if mse3[k1] > 2.23:
                m = k1 + 1
                break
    else:
        for j1 in range(3):  # 5、6、7
            k1 = 6 - j1
            if mse3[k1] > 2.23:
                m = k1 + 1
                break
    rlis2 = rlis1[:m]  # 小波范围
    ths = [0]  # 起始点——
    m = 0
    rtss = []
    for i1 in range(len(rlis)):
        rtss.append(rlis[i1])
        if abs(rlis[i1]) in rlis2:
            rtss.pop(-1)
            rts.append(rtss)
            rtss = []
            m += 1
            js.append(rlis[i1])
            ths.append(i1 + 1)  # range——
        if i1 == len(rlis) - 1:
            rts.append(rtss)
    wg = rlis1[m]  # 小波范围
    ths.append(len(alis))  # 补充终点——
    for i1 in range(len(ths) - 1):
        ts.append([tlis[ths[i1]], tlis[ths[i1 + 1]]])  # 时刻段
        ags.append(alis[ths[i1]:ths[i1 + 1]])  # 角度
        xss.append(fxs[ths[i1]:ths[i1 + 1]])
        yss.append(fys[ths[i1]:ths[i1 + 1]])
        xss1.append(mxs[ths[i1]:ths[i1 + 1]])
        yss1.append(mys[ths[i1]:ths[i1 + 1]])
    for i1 in range(m + 1):
        if len(rts[i1]) != 0:
            a = ags[i1][0] - np.average(rts[i1])  # 平均角度=a1-av.rn
        else:
            a = ags[i1][0]
        if a >= 360:  # 规范角度
            a = a - 360
        elif a < 0:
            a = a + 360
        q = calquad(a)
        if len(ags[i1]) <= 1:
            f, r, v, mse, af, ad = 1, 0, 0, 0, 0, 0
        else:
            f = len(ags[i1])  # 帧数
            r = calfag(ags[i1])  # 计算角度转角——
            v, vlis = 0, []
            for j1 in range(len(rts[i1])):
                vlis.append(abs(rts[i1][j1]))
            v = np.average(vlis)  # 角速度
            mse = np.std(rts[i1])  # 均方差
            af = calarea1(xss[i1], yss[i1])
            ad = calarea1(xss1[i1], yss1[i1])
        ns.append([f, r, v, mse, a, q, af, ad])
    return wg, m, m+1, js, ts, ns


def calmas(frfile, ik, mode='max'):
    '''列表的最大(最小)、均值、均方差'''
    lis = []
    for i1 in range(len(frfile)):
        lis.append(frfile[i1][ik])
    dppma = max(lis)  # 最大值
    dppmi = min(lis)  # 最大值
    dppav = np.average(lis)  # 均值
    dppmse = np.std(lis)
    if mode == 'min':
        return dppma, dppmi, dppav, dppmse
    else:
        return dppma, dppav, dppmse


def calmf(matrixes, q_peak, r_peak):
    '''间期QR的最大幅值差'''
    flist = []
    for j1 in range(q_peak, r_peak + 1):
        matrix = matrixes[j1 - 1]  # 扩张心磁
        flist.append(matrix.max() - matrix.min())  # 幅值差
    Mf = max(flist)
    return Mf


def calmfl(frfile, ik):
    lis = []
    pnpflu = 0
    for i1 in range(len(frfile)):
        line = frfile[i1]
        lis.append(line[ik])
        if i1 > 1:
            if lis[-1] != lis[-2]:
                pnpflu += 1  # 波动次数
    pnpma = max(lis)  # 最大值
    return pnpma, pnpflu


def calns(ns, nsik=4):
    '''计算等级分布类别, nsik为等级数量'''
    qs = ''
    for i1 in range(nsik):
        if ns[nsik-1-i1] > 0:
            qs += str(nsik-1-i1)
    cdfile = [qs]
    for i1 in range(nsik):
        cdfile.append(ns[i1])
    if nsik == 5:
        mt1 = (2*ns[3]+4*ns[4]-2*ns[1]-4*ns[0]) / sum(ns[:nsik])
        mt2 = (3*ns[3]+5*ns[4]-3*ns[1]-5*ns[0]) / sum(ns[:nsik])
        mt1, mt2 = get_round([mt1, mt2], [2, 2])
        cdfile.extend([mt1, mt2])
    return cdfile


def calplf(Z):
    cors, pap, pan, mcgs = statpf(Z)
    lisc = [[], [], [], []]
    pe, ne = 0, 0  # 初始化——
    if pap != 0:
        for k1 in range(len(cors[0])):
            lisc[0].append(cors[0][k1][0])
            lisc[1].append(cors[0][k1][1])
        cpx = np.average(lisc[0])  # 中心点坐标
        cpy = np.average(lisc[1])
        for k1 in range(len(cors[0])):
            dx = cors[0][k1][0] - cpx
            dy = cors[0][k1][1] - cpy
            vk = mcgs[0][k1]
            dk = pow(dx * dx + dy * dy, 1 / 2)
            pe += dk * vk
        pe = pe / pap  # 离心率归一化=除磁极面积
    if pan != 0:
        for k1 in range(len(cors[1])):
            lisc[2].append(cors[1][k1][0])
            lisc[3].append(cors[1][k1][1])
        cnx = np.average(lisc[2])
        cny = np.average(lisc[3])
        for k1 in range(len(cors[1])):
            dx = cors[1][k1][0] - cnx
            dy = cors[1][k1][1] - cny
            vk = -mcgs[1][k1]
            dk = pow(dx * dx + dy * dy, 1 / 2)
            ne += dk * vk
        ne = ne / pan
    return pe, ne


def calpn(pns):  # 计算主极子数量
    pnp, pnn = 0, 0
    for i1 in range(len(pns[0])):
        if pns[0][i1][0] > 0.8:
            pnp += 1
    for i1 in range(len(pns[1])):
        if pns[1][i1][0] < -0.8:
            pnn += 1
    return pnp, pnn


def calptcors(V1, dx, dy):
    '''角度坐标计算'''
    if dx == 0 and dy == 0:
        return 0, 0
    dz = pow(dx * dx + dy * dy, 1 / 2)
    fx = V1 * dx / dz
    fy = V1 * dy / dz
    return fx, fy


def calqrc(frfile, ix, iy):
    xss, yss, pts = [], [], []
    qrcd, qrcar = 0, 0
    for j1 in range(len(frfile)):
        xss.append(frfile[j1][iy])
        yss.append(frfile[j1][ix])
        if (xss[-1], yss[-1]) not in pts:
            pts.append((xss[-1], yss[-1]))  # 点集
        if j1 > 0:
            dx = xss[-1] - xss[-2]
            dy = yss[-1] - yss[-2]
            qrcd += pow(dx * dx + dy * dy, 1 / 2)
    if len(pts) > 2:
        cvpts = convex_hull(pts)  # 凸包点集, 绘制——
        qrcar = getAreaOfPolyGonbyVector(cvpts)  # 面积
        qrcar = qrcar / 1000  # 归一化
    qrcre9 = calregion(yss, xss)  # 9区域分布
    return qrcd, qrcar, qrcre9


def calquad(p1):  # 计算象限
    pq1234 = [90, 180, 270, 360]
    for j1 in range(4):
        if p1 < pq1234[j1]:
            break
    return str(j1+1)


def calregion(xss, yss):  # 计算9区域分布[0, 33) [33, 67) [67, 100)=33, 34, 33
    rgs = [[0, 33], [33, 67], [67, 100]]  # 坐标取0/100——
    rg9s = []
    for i1 in range(len(xss)):
        for j1 in range(3):
            if xss[i1] >= rgs[j1][0] and xss[i1] < rgs[j1][1]:
                for j2 in range(3):
                    if yss[i1] >= rgs[j2][0] and yss[i1] <= rgs[j2][1]:
                        # rg9 = int(3 * (2 - j1) + j2 + 1)
                        rg9 = int(3 * j1 + j2 + 1)
                        if rg9 not in rg9s:
                            rg9s.append(rg9)
                        break
                break
    rg9s.sort()
    qrcre9 = ''
    for i1 in range(len(rg9s)):
        qrcre9 = qrcre9 + str(rg9s[i1])
    return qrcre9


def calrot(a1, a2):
    r = a2 - a1
    if r < -180:
        r = -360 - r
    elif r > 180:
        r = r - 360
    else:
        r = -r
    return r


def calrt(frfile, ik=2):
    '''计算角度'''
    rlis = []
    for i1 in range(1, len(frfile)):
        ra = calrot(frfile[i1-1][ik], frfile[i1][ik])
        rlis.append(ra)
    return rlis


def change_QR(pnptm2, q_peak):
    '''Q时刻点修正'''
    pnptm = pnptm2[5:]
    tim = 0
    for j1 in range(len(pnptm)):
        if pnptm[j1][1] >= 1:
            tim = j1  # 最后一个<1的序号
            break
    tim1 = 0
    if tim > 0:  # 初始<1.0
        stabs = statdppn_QR(tim, pnptm)
        for j1 in range(tim):
            jend = min(j1+3, tim)
            if max(stabs[j1:jend]) == 0:  # q_peak开始第一次稳定, 更新
                tim1 = j1
                break
    elif tim == 0:  # 初始>=1.0
        stabs = statdppn_QR(8, pnptm2)  # 5+q_peak3=8个值=[1, 9]
        for j2 in range(6):
            j1 = 5 - j2  # 逆序选取=5, 4, 3, 2, 1, 0
            if max(stabs[j1:j1+3]) == 0:  # q_peak逆序第一次稳定, 更新
                tim1 = -j2
                break
    if tim1 == 0:
        tim1 = tim
    return q_peak + tim1


def change_QS(timelis0, matrixes, mf):
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    pnptm2 = cal_pnptm(matrixes, mf, range(q_peak-5, r_peak+1))
    q_peak = change_QR(pnptm2, q_peak)  # Q异常修正
    pnptm2 = cal_pnptm(matrixes, mf, range(r_peak, s_peak+6))
    s_peak = change_RS(pnptm2, s_peak)  # S异常修正
    timelis0[0], timelis0[2] = q_peak, s_peak
    return timelis0


def change_RS(pnptm2, s_peak):
    '''S时刻点修正'''
    pnptm = pnptm2[:-5]
    tim = 1
    for j1 in range(len(pnptm)):
        if pnptm[len(pnptm)-1-j1][1] >= 1:
            tim = j1+1  # 最后一个<1的序号
            break
    tim1 = 0
    if tim > 1:  # 初始<1.0
        stabs = statdppn_RS(tim, pnptm)
        for j1 in range(tim-1):
            jend = min(j1+3, tim-1)
            if max(stabs[j1:jend]) == 0:  # q_peak开始第一次稳定, 更新
                tim1 = j1 + 1
                break
    elif tim == 1:  # 初始>=1.0
        stabs = statdppn_RS(9, pnptm2)  # 5+q_peak3=8个值=[1, 9]
        for j2 in range(6):
            j1 = 5 - j2  # 逆序选取=5, 4, 3, 2, 1, 0
            if max(stabs[j1:j1+3]) == 0:  # q_peak逆序第一次稳定, 更新
                tim1 = -j2 + 1
                break
    if tim1 == 0:
        tim1 = tim
    return s_peak - tim1 + 1


def comb_diag1(perlis):
    '''综合诊断-202310'''
    if type(perlis) == list:  # 最靠近0/100的一个或者多个值中的最大值
        for i4 in range(len(perlis)):
            perlis[i4] = comb_diag1(perlis[i4])
        if len(perlis) == 0:  # 返回空值
            return 50
        else:
            max_p = [max(x, 100-x) for x in perlis]
            max_p_id = [ik for ik, x in enumerate(max_p) if x == max(max_p)]
            perlis_max = [perlis[ik] for ik in max_p_id]
            return max(perlis_max)
    else:
        return perlis


def convex_hull(p):
    p = list(set(p))
    k = 0
    for i1 in range(1, len(p)):
        if p[i1][1] < p[k][1] or (p[i1][1] == p[k][1] and p[i1][0] < p[k][0]):
            k = i1
    pk = p[k]
    p.remove(p[k])
    p_sort = sort_points_tan(p, pk)  # 按与基准点连线和x轴正向的夹角排序后的点坐标
    p_result = [pk, p_sort[0]]
    for i1 in range(1, len(p_sort)):  # 叉乘为正删点;叉乘为负加点
        while (multiply(p_result[-2], p_sort[i1], p_result[-1]) > 0):
            p_result.pop()
        p_result.append(p_sort[i1])
    return p_result


# def cv_interpolate(mfm_file, Qp, Te):
#     '''计算100*100cv插值, 返回矩阵和最大幅值列表'''
#     mlines = get_lines(mfm_file)
#     Te = min(len(mlines), Te)
#     matrixes, mf = [], []
#     for ik in range(len(mlines)):
#         if ik < Qp - 10 or ik >= Te + 10:
#             matrixes.append(np.zeros((100, 100)))
#             mf.append(0)
#         else:
#             mcgdata = mcg_float(mlines)
#             mf.append(max(mcgdata.max(), -1 * mcgdata.min()))
#             matrix = cv2.resize(mcgdata, (100, 100), interpolation=cv2.INTER_CUBIC)
#             matrixes.append(matrix)
#     return matrixes, mf


def diag_res0(mt0, rkme0):
    '''具体诊断: mt0指标, rkmes分级信息'''
    # print(rkme0)
    rg0, rg1 = rkme0[0], rkme0[1].copy()  # 分级范围和概率
    for i in range(len(rg1)):  # 剔除一票否决
        if rg1[i] == 100:
            rg1[i] = 99
    if len(rg1) < 2:
        return 50
    per0 = rg1[-1]  # 默认值: 最后一个概率
    for i in range(len(rg0)):
        if mt0 < rg0[i]:
            per0 = rg1[i]  # 固定诊断!
            break
    return per0


def diag_resu0(d1, mtnms, rkmes, d2, lbs=''):
    '''诊断: d1/mtnms指标, rkmes分级结果, d2/lbs诊断结果'''
    if lbs:
        d3 = '%s/%s' % (d2, lbs)
        new_folder(d3)
    else:
        d3 = d2
    # i2 = 0
    for i2 in range(len(mtnms)):
        # print(i2, mtnms[i2])
        f4 = '%s/%s.txt' % (d1, mtnms[i2])
        f5 = '%s/%s.txt' % (d3, mtnms[i2])
        mtlis = txt_to_list(f4)
        perlis = []
        for i3 in range(len(mtlis)):
            mt0 = mtlis[i3]
            perlis.append(str(diag_res0(mt0, rkmes[i2])))
        write_str('\n'.join(perlis), f5)
    return d3


def draw_bar(xs, ys, saveimg, dpi=256):
    plt.rcParams['figure.figsize'] = (8, 8)
    plt.rcParams['font.size'] = 18
    plt.rcParams['savefig.dpi'] = dpi
    plt.bar(xs, ys)
    # xstx = [str(xs[i]) for i in range(len(xs))]
    # plt.xticks(xs, xstx, rotation=45, fontsize=4)
    plt.savefig(saveimg, bbox_inches='tight', pad_inches=0.1)
    plt.close()


def draw_line(xs, ys, saveimg, dpi=256):
    plt.rcParams['figure.figsize'] = (8, 8)
    plt.rcParams['font.size'] = 18
    plt.rcParams['savefig.dpi'] = dpi
    plt.plot(xs, ys)
    plt.savefig(saveimg, bbox_inches='tight', pad_inches=0.1)
    plt.close()


def draw_ddm0(g1, lis01, lis02, lis03, lis04, xtks, tit):
    '''指标分布密度图: g1地址, lis01/2/3/4数量, xtks标签, tit标题'''
    plt.title(tit)
    lis01 = np.array(lis01)
    lis02 = np.array(lis02)
    lis03 = np.array(lis03)
    lis04 = np.array(lis04)
    width = 0.5
    idx = np.arange(len(xtks))
    plt.bar(idx*2, lis01, width, color='red', label='tr_l0')
    plt.bar(idx*2, lis02, width, bottom=lis01, color='grey', label='tr_l1')
    plt.bar(idx*2+1, lis03, width, color='darkred', label='te_l0')
    plt.bar(idx*2+1, lis04, width, bottom=lis03, color='darkgrey', label='te_l1')
    plt.xlabel('metric values')
    plt.ylabel('distrib nums')
    plt.xticks(idx*2+1.25, xtks, rotation=40)
    for a, b in zip(idx*2, lis01):
        plt.text(a, b+0.05, '%d' % b, ha='center', va='bottom', fontsize=8)
    for a, b, c in zip(idx*2, lis02, lis01):
        plt.text(a, b+c+0.5, '%d' % b, ha='center', va='bottom', fontsize=8)
    for a, b in zip(idx*2+1, lis03):
        plt.text(a, b+0.05, '%d' % b, ha='center', va='bottom', fontsize=8)
    for a, b, c in zip(idx*2+1, lis04, lis03):
        plt.text(a, b+c+0.5, '%d' % b, ha='center', va='bottom', fontsize=8)
    plt.legend()
    plt.savefig(g1)
    plt.close()
    return g1


def extract_metr1(tx1, tx2, tx3, d0, d1, d2, strs):
    '''从原始表格提取ids、time/space集合和单个指标: 去重复'''
    mtsp = '%s/%s_all.txt' % (d1, tx1)
    tr1 = '%s/%s.xlsx' % (d0, tx2)
    te1 = '%s/%s.xlsx' % (d0, tx3)
    ids, strss, mtsps = [], [[]], ['%s/ids.txt' % d1]
    mtstrs = get_split(strs)
    for i2 in range(1, len(mtstrs)):
        strss.append([])
        mtsps.append('%s/%s_%s.txt' % (d2, tx1, mtstrs[i2]))  # 指标
    for stnm in ['spect', 'qfr', 'imr']:
        data1 = pd.read_excel(tr1, sheet_name=stnm).values
        data2 = pd.read_excel(te1, sheet_name=stnm).values
        data1 = data1[:, :len(mtstrs)]
        data2 = data2[:, :len(mtstrs)]
        strs = addtrte(data1, strs, ids, strss)
        strs = addtrte(data2, strs, ids, strss)
    write_str(strs, mtsp)
    for i2 in range(len(mtstrs)-1):
        write_str('\n'.join(strss[i2]), mtsps[i2])
    return mtsp


def def_mts_intp_pre(tardir):
    '''插值前指标的文件列表和标题字符串列表'''
    mflis, mslis = [[], [], [], [], []], [[], [], [], [], []]
    mflis[0].append('%s/dispersion_metrics.txt' % tardir)
    mflis[0].append('%s/dispersion_ranks.txt' % tardir)
    mslis[0].extend(['id, Q, R, S, T, QRS, TT, noq, nor, nos', 'id, Q, R, S, T, QRS, TT'])
    txs1 = ['QR_', 'RS_', 'QRS_', 'TT_']
    txs2 = ['centerdistrib', 'dipole', 'jumprot', 'mulripole', 'pointto', 'poleform']
    titles2 = ['id, qs, n0, n1, n2, n3, na', 'id, didma, didav, didmse, dicd, dicar, dicre9', 'id, wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad', 'id, dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu', '', 'id, epma, epav, epmse, enma, enav, enmse']
    for i2 in range(len(txs1)):
        for j2 in range(len(txs2)):
            mflis[i2+1].append('%s/%s%s.txt' % (tardir, txs1[i2], txs2[j2]))
            if j2 != 4:
                mslis[i2+1].append(titles2[j2])
    mslis[1].insert(-1, 'id, qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread')
    mslis[2].insert(-1, 'id, ra, sa, nq1, nq2, nq3, nq4, rsa, q, m1, m2, a1, a2, areaf, aread')
    mslis[3].insert(-1, 'id, qa, sa, nq1, nq2, nq3, nq4, qsa, q, m1, m2, a1, a2, areaf, aread')
    mslis[4].insert(-1, 'id, ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread')
    return mflis, mslis


def def_mts_mfm(tardir):
    '''插值后指标的文件列表和标题字符串列表'''
    subfolds = ['ischemic_mfm_disp', 'ischemic_mfm_QR', 'ischemic_mfm_RS', 'ischemic_mfm_TT']
    new_folder(['%s/%s' % (tardir, subfolds[i2]) for i2 in range(len(subfolds))])
    mflis, mslis = [[], [], [], []], [[], [], [], []]
    mflis[0].append('%s/%s/metrics.txt' % (tardir, subfolds[0]))
    mslis[0].append('id, Q, R, S, T, QRS, TT, noqrs')
    txs2 = ['metrics_ctdistrib', 'metrics_dipole', 'metrics_jumprot', 'metrics_multipole', 'metrics_pointto', 'metrics_poleform']
    titles2 = ['id, qs, n0, n1, n2, n3, n4, center, mt2', 'id, didma, didav, didmse, dicd, dicar, dicre9', 'id, wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad', 'id, dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu', '', 'id, epma, epav, epmse, enma, enav, enmse']
    for i2 in range(1, len(subfolds)):
        for j2 in range(len(txs2)):
            mflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs2[j2]))
            if j2 != 4:
                mslis[i2].append(titles2[j2])
    mslis[1].insert(-1, 'id, qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread')
    mslis[2].insert(-1, 'id, ra, sa, nq1, nq2, nq3, nq4, rsa, q, m1, m2, a1, a2, areaf, aread')
    mslis[3].insert(-1, 'id, ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread')
    dflis, dslis = [[], [], [], []], [[], [], [], []]
    dflis[0].append('%s/%s/percents.txt' % (tardir, subfolds[0]))
    dflis[0].append('%s/%s/diagnosis_all.txt' % (tardir, subfolds[0]))
    dslis[0].extend(['id, R, T, QRS, TT, noqrs', 'id, percent'])
    txs1 = ['diagnosis_center', 'diagnosis_better', 'diagnosis_available', 'diagnosis_auxiliary', 'diagnosis_subdivision']
    txs2 = [['id, center', 'id, enmse', 'id, qa, ra, qra, qrdmse, qrdma, dpnav, dpnmse, dppav, pnnflu, dppmse, pnnma, enma, nm', 'id, areaf, aread, qrcar, qrcd, qrdav, dpnma, dppma, epmse, epma, epav, wg, pm, saf, af, mse', 'id, subdivision'], ['id, center', 'id, ', 'id, ra, sa, rsa, rsdma, rsdav', 'id, aread, rscar, rscd, dpnma, dppav, epmse, epav, pm, v1, mse', 'id, subdivision'], ['id, center', 'id, ta, tt75, tta, areaf, aread, ttcd, v, ad, af, v1, mse', 'id, tt50, ttdmse, ttdma, dppav, dppmse, dppma, enmse, enma, epmse, epma, wg, nums, pm, nm, saf, sad, r, mse1', 'id, ttcar, dpnav, dpnma, pnpflu, pnnflu, enav, epav', 'id, subdivision']]
    for i2 in range(1, len(subfolds)):
        for j2 in range(len(txs1)):
            dflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs1[j2]))
            dslis[i2].append(txs2[i2-1][j2])
        dflis[i2].append('%s/%s/combinediagnosis.txt' % (tardir, subfolds[i2]))
        dslis[i2].append('id, ct, av, au, al')
    return mflis, mslis, dflis, dslis


def def_mts_pcdm(tardir):
    '''电流密度图指标的文件列表和标题字符串列表'''
    subfolds = ['ischemic_pcdm_QR', 'ischemic_pcdm_RS', 'ischemic_pcdm_TT']
    new_folder(['%s/%s' % (tardir, subfolds[i2]) for i2 in range(len(subfolds))])
    mflis, mslis = [[], [], []], [[], [], []]
    txs2 = ['metrics_ctdistrib', 'metrics_posi', 'metrics_jumprot', 'metrics_red', 'metrics_angle']
    titles2 = ['id, qs, n0, n1, n2, n3, n4, center, mt2', 'id, mctd, mcar, mcre9, areac, aread', 'id, wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad', 'id, dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse', 'id, qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2']
    for i2 in range(len(subfolds)):
        for j2 in range(len(txs2)):
            mflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs2[j2]))
            mslis[i2].append(titles2[j2])
    return mflis, mslis


def get_utility1(list1, ids, rkme0=[]):
    '''计算效用因子-单人: list1指标/诊断, rkmes0分级文档, ids-tr_va索引'''
    if not rkme0:
        return [0, 0, 0]
    else:
        mtlis = []
        ids0, ids1 = ids
        mtlis.append([list1[k] for k in ids0])
        mtlis.append([list1[k] for k in ids1])
        l1, l2 = [], []
        for j in range(len(mtlis[0])):
            mt0 = mtlis[0][j]
            l1.append(diag_res0(mt0, rkme0))
        for j in range(len(mtlis[1])):
            mt0 = mtlis[1][j]
            l2.append(diag_res0(mt0, rkme0))
        return cal_utility1(l1, l2)


def get_utility_raw(list1, ids, rkme0=[]):
    '''计算效用因子-单人: list1指标/诊断, rkmes0分级文档, ids-tr_va索引'''
    if not rkme0:
        return [0, 0, 0]
    else:
        mtlis = []
        ids0, ids1 = ids
        for ids2 in [ids0, ids1]:
            for j in range(2):
                mtlis.append([list1[k] for k in ids2[j]])
        l1, l2 = [], []
        for j in range(len(mtlis[0])):
            mt0 = mtlis[0][j]
            l1.append(diag_res0(mt0, rkme0))
        for j in range(len(mtlis[2])):
            mt0 = mtlis[2][j]
            l1.append(diag_res0(mt0, rkme0))
        for j in range(len(mtlis[1])):
            mt0 = mtlis[1][j]
            l2.append(diag_res0(mt0, rkme0))
        for j in range(len(mtlis[3])):
            mt0 = mtlis[3][j]
            l2.append(diag_res0(mt0, rkme0))
        return cal_utility1(l1, l2)


def get_utility0(list1, ids, rkmes0=[]):
    '''计算效用因子-单人: list1指标/诊断, rkmes0分级文档, ids-tr_va索引'''
    perlis = []
    ids0, ids1 = ids
    for ids2 in [ids0, ids1]:
        for j in range(2):
            perlis.append([list1[k] for k in ids2[j]])
    ut0 = [[], [], [], []]
    for j in range(4):
        for k in range(len(perlis[j])):
            if rkmes0:
                per0 = diag_res0(perlis[j][k], rkmes0)
                ut0[j].append(per0-50)
            else:
                ut0[j].append(perlis[j][k]-50)
    nm1 = round(100*(sum(ut0[1])-sum(ut0[0]))/(len(ut0[0]+ut0[1])))
    nm2 = round(100*(sum(ut0[3])-sum(ut0[2]))/(len(ut0[2]+ut0[3])))
    if nm1 < 0 or nm2 < 0:
        return [nm1, nm2, nm1+nm2, min(nm1, nm2), -1]
    else:
        return [nm1, nm2, nm1+nm2, min(nm1, nm2), 1]


def get_utility_sort0(f0, list0, fd1, ids, d2, mode=None):
    '''
    计算list的效用因子和排序
    输入: 
        f0原始指标集-['mfm_QR_ad', 'mfm_QR_af', 'mfm_QR_aread'...]
        list0指标索引
        f1分级文档-[[-0.67, 0.19, 1.17], [30, 40, 60, 80]]...
        fd1=d1诊断目录; fd1=[f1, d1]分级文档和指标目录
        ids0, ids1样本索引-tr_va、标签0_1
        d2保存目录
    输出: 全部效用文档utility_all、排序效用文档utility_sort、排序指标索引utility_idx
    '''
    if type(fd1) == list:
        rkmes = txt_to_list(fd1[0])  # 分级文档
        fd1, mode = fd1[1], 1
    mtnms = ast.literal_eval(get_lines1(f0)[0])
    strs1 = []
    for i in range(len(list0)):
        item = mtnms[list0[i]]
        print('start:', i+1, item, 'end:', len(list0)-i-1)
        list1 = txt_to_list('%s/%s.txt' % (fd1, item))
        if mode:
            strs1.append(str(get_utility0(list1, ids, rkmes[i])))
        else:
            strs1.append(str(get_utility0(list1, ids, [])))
    write_str('\n'.join(strs1), '%s/utility_all.txt' % d2)
    # 排序
    f3 = '%s/utility_sort.txt' % d2
    f5 = '%s/utility_idx.txt' % d2
    lis1 = txt_to_list('%s/utility_all.txt' % d2)
    data = np.array(lis1)
    idex = np.lexsort((-1 * data[:, 1], -1 * data[:, 0], -1 * data[:, 2], -1 * data[:, 3], -1 * data[:, 4]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    save_txt(lis1, 'list', f3)
    data = np.array(list0)
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    write_str(str(lis1), f5)
    write_resort('%s/rks.txt' % d2, '%s/utility_rks.txt' % d2, list(idex))
    write_resort('%s/mts.txt' % d2, '%s/utility_mts.txt' % d2, list(idex))
    return idex


def comb_res0(n1, n0, ls1, ls2, mode=1):
    '''
    分段线性综合诊断: ls1增量比例, ls2概率范围
    例如:
        增量n1, 理论n0=189, [0, 0.2, 0.5, 1], [50, 60, 80, 100]
        0.5-1 80-100  (n1/n0-0.5)*40+80
        0.2-0.5 60-80 (n1/n0-0.2)*200/3+60
        0-0.2 50-60 (n1/n0-0)*50+50
    '''
    per0 = 100
    for i7 in range(len(ls1)-1):
        if n1/n0 < ls1[i7+1]:
            per0 = (n1/n0-ls1[i7])*(ls2[i7+1]-ls2[i7])/(ls1[i7+1]-ls1[i7])+ls2[i7]
            break
    if mode:
        return round(per0)
    else:
        return round(100-per0)


def comb_res1(l2, n01, n00):
    '''综合诊断: l2全部单指标组合的列表, n01阳性最大值, n00阴性最小值'''
    ls11, ls12 = [0, 0.3, 0.4, 0.6, 1], [50, 55, 75, 90, 100]  # 拉伸尺度
    ls01, ls02 = [0, 0.3, 0.6, 1], [50, 55, 90, 100]
    for i6 in range(len(l2)):
        pers = l2[i6]  # 概率组合
        if np.average(pers) == 50:
            l2[i6] = 50
        elif np.average(pers) > 50:
            pers = [pers[j6] for j6 in range(len(pers)) if pers[j6] > 50]
            l2[i6] = comb_res0(sum(pers)-50*len(pers), n01, ls11, ls12, 1)
        else:
            pers = [pers[j6] for j6 in range(len(pers)) if pers[j6] < 50]
            l2[i6] = comb_res0(50*len(pers)-sum(pers), n00, ls01, ls02, 0)
    return l2


def comb_res3(l3, n01, n00):
    '''综合诊断: l2全部单指标组合的列表, n01阳性最大值, n00阴性最小值'''
    l2 = l3.copy()
    ls11, ls12 = [0, 0.3, 0.4, 0.6, 1], [50, 55, 75, 90, 100]  # 拉伸尺度
    ls01, ls02 = [0, 0.3, 0.6, 1], [50, 55, 90, 100]
    for i6 in range(len(l2)):
        pers = l2[i6]  # 概率组合
        if 100 in pers:  # 一票否决
            l2[i6] = 100
        elif 0 in pers:
            l2[i6] = 0
        elif np.average(pers) == 50:
            l2[i6] = 50
        elif np.average(pers) > 50:
            pers = [pers[j6] for j6 in range(len(pers)) if pers[j6] > 50]
            l2[i6] = comb_res0(sum(pers)-50*len(pers), n01, ls11, ls12, 1)
        else:
            pers = [pers[j6] for j6 in range(len(pers)) if pers[j6] < 50]
            l2[i6] = comb_res0(50*len(pers)-sum(pers), n00, ls01, ls02, 0)
    return l2


def comb_resu1(d0, d1, mtss, nm, ik, d2, lbs, pts):
    '''具体综合诊断: d0信息, mts0指标, iks分类'''
    mts0, ik0 = [], sum(nm[:ik+2])
    for i2 in range(ik+1):
        mts0 += mtss[i2]
    l0 = txt_to_list('%s/%s/rks_sort.txt' % (d0, lbs))
    l1 = [max(l0[i2][1]) for i2 in range(ik0)]
    l2 = [min(l0[i2][1]) for i2 in range(ik0)]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    f1 = '%s/way1_V%s.txt' % (d3, ik+1)
    f2 = '%s/way2_V%s.txt' % (d3, ik+1)
    l0, l1, l2 = [], [], []
    for i2 in range(len(mts0)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mts0[i2])))
    l0 = [[l0[i2][j2] for i2 in range(len(l0))] for j2 in range(len(l0[0]))]
    # ids0, ids1 = pts[0], pts[1]
    # for ids2 in [ids0, ids1]:
    #     for k2 in range(2):
    #         l1.append([l0[j2] for j2 in ids2[k2]])
    ls11, ls12 = [0, 0.3, 0.4, 0.6, 1], [50, 55, 75, 90, 100]
    ls01, ls02 = [0, 0.3, 0.6, 1], [50, 55, 90, 100]
    for i2 in range(len(l0)):
        pers = l0[i2]
        if np.average(pers) > 50:
            pers = [pers[j2] for j2 in range(len(pers)) if pers[j2] > 50]
            if i2 == 12:
                print(pers)
            l1.append(str(max(pers)))
            l2.append(str(comb_res0(sum(pers)-50*len(pers), n01, ls11, ls12, 1)))
        else:
            pers = [pers[j2] for j2 in range(len(pers)) if pers[j2] < 50]
            l1.append(str(min(pers)))
            l2.append(str(comb_res0(50*len(pers)-sum(pers), n00, ls01, ls02, 0)))
    f1 = write_str('\n'.join(l1), f1)
    f2 = write_str('\n'.join(l2), f2)
    # l2 = l1[0]+l1[1]+l1[2]+l1[3]
    # l3.sort()
    # print(l3)
    # for i in range(10):
    #     v0 = i*10
    #     v1 = (1+i)*10
    #     l4.append(round(len([l3[i2] for i2 in range(len(l3)) if l3[i2]>=v0 and l3[i2]<v1])/391, 2))
    # print(l4)
    return f1


def revi_res0(tr_auc, ls1, te_auc, ls2, f3):
    '''审查结果统计: 训练集最好, 训练集和测试集均衡, 以acc/sen/spe顺序'''
    strs = 'tr_better:'
    data = np.array(ls1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 3], -1 * data[:, 2], -1 * data[:, 1]))
    strs += '\n%.3f, %s' % (tr_auc, str(ls1[idex[0]])[1:-1])
    lis1 = [ls2[i3] for i3 in range(len(ls2)) if ls2[i3][0] >= ls1[idex[0]][0]]
    strs += '\n%.3f, %s' % (te_auc, str(lis1[0])[1:-1])
    pers0 = [ls1[i3][0] for i3 in range(len(ls1))]+[ls2[i3][0] for i3 in range(len(ls2))]
    pers0 = list(set(pers0))
    pers0.sort()
    ik1, ik2, flag = 0, 0, 0
    ls3, ls4 = [], []
    for i3 in range(len(pers0)):
        p0 = pers0[i3]
        for j3 in range(ik1, len(ls1)):
            if ls1[j3][0] >= p0:
                ik1 = j3
                break
        for j3 in range(ik2, len(ls2)):
            if ls2[j3][0] >= p0:
                ik2 = j3
                break
        ls3.append([(ls1[ik1][1]+ls2[ik2][1])/2, ls1[ik1][1], ls2[ik2][1]])
        ls4.append([p0, ls1[ik1], ls2[ik2]])
        if p0 >= 50 and flag == 0:
            strs += '\n50:\n%.3f, %s\n%.3f, %s' % (tr_auc, str(ls1[ik1])[1:-1], te_auc, str(ls2[ik2])[1:-1])
            flag = 1
    data = np.array(ls3)
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    strs += '\ntr_te_balance:\n%.3f, %s\n%.3f, %s\n\nauc, thd, acc, sen, spe; tr/te' % (tr_auc, str(ls4[idex[0]][1])[1:-1], te_auc, str(ls4[idex[0]][2])[1:-1])
    write_str(strs, f3)
    return f3


def revi_resu0(l0, d5, lbs0, pts, ik, mode=0):
    '''审查: mode=1全部数据'''
    f1 = '%s/spect_tr_roc.txt' % d5
    if mode:
        ids0, ids1, ik = [[], []], [[], []], 3
        for i2 in [0, 2, 4]:
            ids0[0] += pts[i2][0]
            ids0[1] += pts[i2][1]
            ids1[0] += pts[i2+1][0]
            ids1[1] += pts[i2+1][1]
    else:
        ids0, ids1 = pts[2*ik], pts[2*ik+1]
    l1, l2 = [l0[i2] for i2 in ids0[0]], [l0[i2] for i2 in ids0[1]]
    f1 = '%s/%s_tr_roc.txt' % (d5, lbs0[ik])
    tr_auc, tr_roc = cal_auc0(l1, l2, f1)
    l1, l2 = [l0[i2] for i2 in ids1[0]], [l0[i2] for i2 in ids1[1]]
    f2 = '%s/%s_te_roc.txt' % (d5, lbs0[ik])
    te_auc, te_roc = cal_auc0(l1, l2, f2)
    f3 = '%s/%s_acc.txt' % (d5, lbs0[ik])
    f3 = revi_res0(tr_auc, tr_roc, te_auc, te_roc, f3)
    return f3


def annalysis_resu0(d1, item, tx1):
    '''way2_V1/2/3/4版本的最好结果汇总: d1审查, item版本, tx1测试'''
    strs0 = '%s:' % item
    l0 = txt_to_list('%s/%s/%s_tr_roc.txt' % (d1, item, tx1[0]))
    l0 = [l0[j1] for j1 in range(len(l0)) if l0[j1][2] > l0[j1][3]]
    for i2 in range(len(tx1)):
        for tx2 in ['tr', 'te']:
            l1 = txt_to_list('%s/%s/%s_%s_roc.txt' % (d1, item, tx1[i2], tx2))
            l1 = [l1[j1] for j1 in range(len(l1)) if l1[j1][0] >= l0[-1][0]]
            strs0 += '\n%s_%s, %s' % (tx1[i2], tx2, str(l1[0]))
    return strs0


def comb_dg0(mtnms0, mtidx, nm, d4, pts, d5, d6, l0, iks, tx1):
    '''
    综合诊断和审查
    输入:
    输出: 综合诊断和审查结果
    '''
    new_folder(d4)
    new_folder(d5)
    for i in range(len(nm)):
        n0 = sum(nm[:i+1])
        l1 = [max(l0[j][1]) for j in range(n0)]
        l2 = [min(l0[j][1]) for j in range(n0)]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        l4 = []
        for j in range(n0):
            l4.append(txt_to_list('%s/%s.txt' % (d4, mtnms0[mtidx[j]])))
        l4 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
        l5 = comb_res1(l4, n01, n00)
        write_str('\n'.join([str(p0) for p0 in l5]), '%s/V%d.txt' % (d5, i+1))
    new_folder(d6)
    f0, strs = '%s/comb_reviews.txt' % d6, ''
    for i in range(len(nm)):
        l0 = txt_to_list('%s/V%d.txt' % (d5, i+1))
        strs += '\nV%d:' % (i+1)
        d7 = '%s/V%d' % (d6, i+1)
        new_folder(d7)
        for j in range(len(iks)):
            f1 = '%s/%s_roc.txt' % (d7, tx1[j])
            ids0, ids1 = [], []
            for k in range(len(iks[j])):
                ids0 += pts[iks[j][k]][0]
                ids1 += pts[iks[j][k]][1]
            l1 = [l0[k] for k in ids0]
            l2 = [l0[k] for k in ids1]
            auc, roc = cal_auc0(l1, l2, f1)
            if j == 0:
                roc = [roc0 for roc0 in roc if roc0[2] >= roc0[3]]
                p0 = roc[-1][0]
                roc = roc[-1]
            else:
                roc = [roc0 for roc0 in roc if roc0[0] >= p0]
                roc = [p0]+roc[0][1:]
            roc.insert(1, round(auc, 3))
            strs += '\n%s, %s' % (tx1[j], str(roc))
    write_str(strs, f0)
    return d6


def comb_resu2(comb, lisss0):
    rk0, ls0, p1 = lisss0
    l1 = [max(rk0[i2][1]) for i2 in comb]
    l2 = [min(rk0[i2][1]) for i2 in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
    l2 = [[ls0[1][i3][j3] for j3 in comb] for i3 in range(len(ls0[1]))]
    l2 = comb_res1(l2, n01, n00)
    tp = sum(1 for p0 in l2 if p0 >= p1)
    if tp/len(l2) <= 0.77:
        return []
    else:
        l1 = [[ls0[0][i3][j3] for j3 in comb] for i3 in range(len(ls0[0]))]
        l1 = comb_res1(l1, n01, n00)
        fp = sum(1 for p0 in l1 if p0 >= p1)
        sen, spe = tp/len(l2), (len(l1)-fp)/len(l1)
        acc = (len(l1)-fp+tp)/len(l1+l2)
        if acc <= 0.76:
            return []
        else:
            rt = get_round([p1, acc, sen, spe], [0, 3, 3, 3])
            l3 = [[ls0[2][i3][j3] for j3 in comb] for i3 in range(len(ls0[2]))]
            l4 = [[ls0[3][i3][j3] for j3 in comb] for i3 in range(len(ls0[3]))]
            l3 = comb_res1(l3, n01, n00)
            l4 = comb_res1(l4, n01, n00)
            rt1 = rt  # 保存
            fp = sum(1 for p0 in l3 if p0 >= rt[0])
            tp = sum(1 for p0 in l4 if p0 >= rt[0])
            sen, spe = tp/len(l4), (len(l3)-fp)/len(l3)
            acc = (len(l3)-fp+tp)/len(l3+l4)
            if acc <= 0.86:
                return []
            else:
                print('get one')
                rt2 = get_round([rt[0], acc, sen, spe], [0, 3, 3, 3])
                cmrts = [comb, [rt1, rt2]]
                return cmrts


def comb_res2(l0):
    # 均值>0
    l1 = l0.copy()
    print('len', len(l1))
    c0, c1 = 0, 0
    for i6 in range(len(l1)):
        l1[i6] = [l1[i6][j6] for j6 in range(len(l1[i6])) if l1[i6][j6] != 50]
        a=int(sum(l1[i6]) - 50 * len(l1[i6]))
        b=sum([1 for i in range(len(l1[i6])) if l1[i6][i] > 50])
        if a < 0:
                c0 += 1
        if a < 0 or b <= len(l1[i6])/2:
                c1 += 1
    print(c0, c1, len(l1)-c0, len(l1)-c1)
    # 等级计算求和
    # p0s = [0, 30, 40, 50, 50.1, 60, 70, 100]
    # s0s = [-2, -1, -0.5, 0, 0.5, 1, 2]
    # for i6 in range(len(l1)):
    #     p0, k7 = 0, 7
    #     for j6 in range(len(l1[i6])):
    #         for k6 in range(1, len(p0s)-1):
    #             if l1[i6][j6] < p0s[k6]:
    #                 k7 = k6
    #                 break
    #         p0 += s0s[k7-1]
    #     l1[i6] = p0
    return l1


def rm_element(l1, v1):
    '''去除列表中的元素'''
    l0 = l1.copy()
    if v1 in l0:
        l0.remove(v1)
        return rm_element(l0, v1)
    else:
        return l0


def search_dg2(nm, ik, comb0, ls, ids, rks, f1, i1=0):
    '''i1起始搜索位置'''
    cn0, dp = 0, 1000
    l00, l01, l0, l1 = ls
    ids0, ids1, ids2 = ids
    rkl0, rkl1 = rks
    acc0, acc1, rslis = 0.889, 0, []
    nr0, nr1, ne1 = len(ids0), len(ids1), len(ids2)
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    t1 = time.time()
    i = 0
    for i in range(n1+1):  # 最多n1个结果
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            cnt += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t2 = time.time()
                sec = int((t2-t1) * (cn-cnt) / dp)
                dspy0 = 'start: %d, left: %d, end_time: ' % (cnt, cn-cnt)
                hr, mn, sc = sec//3600, (sec%3600)//60, sec%60
                if hr:
                    dspy0 += '%dh' % hr
                if mn:
                    dspy0 += '%dmin' % mn
                if sc:
                    dspy0 += '%ds' % sc
                print(dspy0)
                t1 = t2
            l02 = l00.copy()
            l11 = l0.copy()
            rkl2 = rkl0.copy()
            for j in comb:
                l02[j] = l01[j]  # 替换新分级下的诊断结果
                l11[j] = l1[j]  # 替换新分级上下阈值
                rkl2[j] = rkl1[j]  # 替换新分级
            l12 = []
            l12.append([l11[j][0] for j in range(len(l11))])
            l12.append([l11[j][1] for j in range(len(l11))])
            n01, n00 = sum(l12[0])-50*len(l12[0]), 50*len(l12[1])-sum(l12[1])
            l02 = [[l02[j][k] for j in range(len(l02))] for k in range(len(l02[0]))]
            l10 = comb_res3([l02[j] for j in ids0], n01, n00)
            l11 = comb_res3([l02[j] for j in ids1], n01, n00)
            l22 = list(set(l10+l11))  # roc曲线上的全部概率
            l22.sort(reverse=True)
            for i3 in range(len(l22)):
                fp = sum(1 for p0 in l10 if p0 >= l22[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l11 if p0 >= l22[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                if sen >= spe:
                    p1 = l22[i3]
                    break
            acc = round((nr0-fp+tp)/(nr0+nr1), 3)
            if acc > acc0:
                l12 = comb_res3([l02[j] for j in ids2], n01, n00)
                tp = sum(1 for p0 in l12 if p0 >= p1)
                acc = round(tp / ne1, 3)
                if acc > acc1:
                    j = 0
                    for j in range(min(len(rslis), 3)):
                        if acc > rslis[j][0]:
                            break
                    rslis.insert(j, [acc, comb, p1])
                    print('get one!\n', rslis[:min(len(rslis), 3)])
                    acc1 = rslis[min(len(rslis), 3)-1][0]
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    if len(rslis) > 0:
        comb0 = rslis[0][1]
    return comb0


def sort_001(f1, f2):
    ls0 = ['000', '100', '010', '101', '001', '110', '011', '111']
    l1 = get_lines1(f1)
    idx = [0]
    for i in range(len(ls0)):
        for j in range(1, len(l1)):
            l2 = get_split(l1[j])
            if l2[1]+l2[2]+l2[3] == ls0[i]:
                idx.append(j)
    l2 = [l1[i] for i in idx]
    write_str('\n'.join(l2), f2)
    return f2


def search_dg3(nm, ik, comb0, ls, ts, f1, i1=0):
    '''i1起始搜索位置'''
    cn0, dp = 0, 1000
    l0, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    acc0, rslis = 0, []
    nr0, nr1 = len(tr0), len(tr1)
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    if len(comb0) == 0:
        rgls = list(range(max(1, i1), n1+1))  # 每组最多5个
    else:
        rgls = list(range(max(0, i1), n1+1))
    t1 = time.time()
    for i in rgls:  # 最多搜索8个结果
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            cnt += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t2 = time.time()
                sec = int((t2-t1) * (cn-cnt) / dp)
                dspy0 = 'start: %d, left: %d, end_time: ' % (cnt, cn-cnt)
                hr, mn, sc = sec//3600, (sec%3600)//60, sec%60
                if hr:
                    dspy0 += '%dh' % hr
                if mn:
                    dspy0 += '%dmin' % mn
                if sc:
                    dspy0 += '%ds' % sc
                print(dspy0)
                t1 = t2
            l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
            l1 = comb_res3([l00[j] for j in tr0], n01, n00)
            l2 = comb_res3([l00[j] for j in tr1], n01, n00)
            l12 = list(set(l1+l2))  # roc曲线上的全部概率
            l12.sort(reverse=True)
            for i3 in range(len(l12)):
                fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l2 if p0 >= l12[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                # print(sen, spe)
                if sen >= spe:
                    p1 = l12[i3]
                    break
            acc = round((nr0-fp+tp)/(nr0+nr1), 3)
            if acc > acc0:
                j = 0
                for j in range(min(len(rslis), 3)):
                    if acc > rslis[j][0]:
                        break
                rslis.insert(j, [acc, comb, p1])
                print('get one!\n', rslis[:min(len(rslis), 3)])
                acc0 = rslis[min(len(rslis), 3)-1][0]
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    return comb0


def zongh_diag(ls, comb, lis):
    '''综合诊断: 诊断信息ls, comb组合, lis索引集'''
    l0, l11, l22 = ls
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
    l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
    rs = []
    for i in range(len(lis)):
        rs.append(comb_res3([l00[j] for j in lis[i]], n01, n00))
    return rs


def search_dg1_0(i1, i2, i12, n00, comb0, ls, ts, f1):
    '''初始个数，搜索总数，当前搜索个数，初始组合，当前最大数量'''
    '''0, 20, [0, 3], comb0, 10'''
    dp = 1000
    loss, rslis = 0, []
    combs0 = list(range(i1, i1+i2))
    combs = [comb for comb in combs0 if comb not in comb0]  # 剩余combs
    t1 = time.time()
    if len(i12) == 1:
        n1 = len(combs)
        n0 = i12[0]
        combs1 = np.array(list(itertools.combinations(list(np.arange(n1)), n0)))
        cn = len(combs1)
        print('---------start(%d, %d) cnt=%d' % (n1, n0, cn))
        cnt = 0
        for comb1 in combs1:
            comb = comb0+[combs[j1] for j1 in comb1]  # 组合
            comb.sort()
            cnt += 1
            if cnt % dp == 0:  # 展示频率
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t1 = display_time0(t1, cn, cnt, dp)
            l1234 = zongh_diag(ls, comb, ts)  # 综合诊断
            rslis, loss, fg = loss_mgd0(l1234, rslis, loss, comb, f1)
    else:
        n01 = n00 - len(combs0) + len(combs)
        for _ in range(n01):
            flag = 0  # 循环标识
            combs = [comb for comb in combs0 if comb not in comb0]
            n02 = n00 - len(combs0) + len(combs)
            if n02 <= 0:
                continue
            n1 = len(combs)
            for i in range(i12[0], min(n02+1, i12[1]+1)):
                combs1 = np.array(list(itertools.combinations(list(np.arange(n1)), i)))
                cn = len(combs1)
                print('---------start(%d, %d) cnt=%d' % (n1, i, cn))
                cnt = 0
                for comb1 in combs1:
                    comb = comb0+[combs[j1] for j1 in comb1]  # 组合
                    comb.sort()
                    cnt += 1
                    if cnt % dp == 0:  # 展示频率
                        write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                        t1 = display_time0(t1, cn, cnt, dp)
                    l1234 = zongh_diag(ls, comb, ts)  # 综合诊断
                    rslis, loss, fg = loss_mgd0(l1234, rslis, loss, comb, f1)
                    flag = max(flag, fg)
                if flag:  # 存在优化时
                    comb0 = rslis[0][1]
                    break
            if not flag:
                break
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    if rslis == []:
        return comb0
    else:
        return rslis[0][1]


# def w_bce(yt, yp, wt=[1, 1.1]):
def w_bce(l1, l2):
    '''加权二元交叉熵wt: 负样本权重和正样本权重, 1:n1/n2, 正负比n2/n1'''
    yt = [0 for i in l1] + [1 for i in l2]
    yt = np.array(yt)
    yp = np.array(l1+l2)
    wt=[1, len(l1)/len(l2)]
    # if not wt:
    #     n2 = np.count_nonzero(yt)  # 正样本数量
    #     n1 = yt.size - n2
    #     wt = [1, n1/n2]
    wts = np.array([wt[1], wt[0]])
    yp = np.clip(yp, 1e-15, 1 - 1e-15)
    return -np.mean(wts[yt] * np.log(yp) + wts[1 - yt] * np.log(1 - yp))


def loss_wbce0(l1234, rslis, loss, comb, f1):
    '''加权二元交叉熵最高'''
    l1, l2, l3, l4 = l1234
    # yt = [0 for i in l1] + [1 for i in l2]
    # wbce0 = w_bce(yt, l1+l2, wt=[1, len(l1)/len(l2)])
    acc0 = w_bce(l1, l2)
    fg = 0
    if rslis != []:
        if rslis[0][0] <= acc0:  # 损失
            return rslis, loss, fg
    acc1 = w_bce(l3, l4)
    loss = acc0  # 更新
    acc0, acc1 = get_round([acc0, acc1], [3, 3])
    if [acc0, comb, acc1] not in rslis:
        fg = 1
        rslis = [[acc0, comb, acc1]] +rslis
        print('get one!\n', rslis[0])
        write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    return rslis, loss, fg


def loss_wacc0(l1234, rslis, loss, comb, f1):
    '''加权acc最高'''
    l1, l2, l3, l4 = l1234
    l12 = list(set(l1+l2))  # roc曲线上的全部概率
    l12.sort(reverse=True)
    l = [860*sum(1 for p0 in l1 if p0<p1) + 742*sum(1 for p0 in l2 if p0>=p1) for p1 in l12]
    acc0 = max(l)/len(l1+l2)/1602
    p1 = l12[l.index(max(l))]  # 最大acc
    fg = 0
    if rslis != []:
        if rslis[0][0] >= acc0:
            return rslis, loss, fg
    acc1 = 215*sum(1 for p0 in l3 if p0 < p1) + 186*sum(1 for p0 in l4 if p0 >= p1)
    acc1 = acc1 / len(l3+l4) / 401
    loss = acc0  # 更新
    acc0, acc1 = get_round([acc0, acc1], [3, 3])
    if [acc0, comb, acc1] not in rslis:
        fg = 1
        rslis = [[acc0, comb, acc1]] +rslis
        print('get one!\n', rslis[0])
        write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    return rslis, loss, fg


def loss_acc0(l1234, rslis, loss, comb, f1):
    '''acc最高'''
    l1, l2, l3, l4 = l1234
    l12 = list(set(l1+l2))  # roc曲线上的全部概率
    l12.sort(reverse=True)
    fg = 0
    l = [sum(1 for p0 in l1 if p0<p1) + sum(1 for p0 in l2 if p0>=p1) for p1 in l12]
    acc0 = max(l)/len(l1+l2)
    p1 = l12[l.index(max(l))]  # 最大acc
    if rslis != []:
        if rslis[0][0] >= acc0:
            return rslis, loss, fg
    acc1 = sum(1 for p0 in l3 if p0 < p1) + sum(1 for p0 in l4 if p0 >= p1)
    acc1 = acc1 / len(l3+l4)
    loss = acc0  # 更新
    acc0, acc1 = get_round([acc0, acc1], [3, 3])
    if [acc0, comb, acc1] not in rslis:
        fg = 1
        rslis = [[acc0, comb, acc1]] +rslis
        print('get one!\n', rslis[0])
        write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    return rslis, loss, fg


def loss_mgd0(l1234, rslis, loss, comb, f1):
    '''敏感度>特异度的acc损失'''
    l1, l2, l3, l4 = l1234
    l12 = list(set(l1+l2))  # roc曲线上的全部概率
    l12.sort(reverse=True)
    p1 = l12[-1]
    acc0, fg = 0, 0
    for i3 in range(len(l12)):
        sen = sum(1 for p0 in l2 if p0 >= l12[i3]) / len(l2)
        spe = sum(1 for p0 in l1 if p0 < l12[i3]) / len(l1)
        if sen >= spe:
            p1 = l12[i3]
            acc0 = (sen*len(l2)+spe*len(l1)) / len(l1+l2)
            break
    acc1 = sum(1 for p0 in l3 if p0 < p1) + sum(1 for p0 in l4 if p0 >= p1)
    acc1 = acc1 / len(l3+l4)
    if rslis != []:  # 验证集最优
        if rslis[min(len(rslis), 2)][0] >= acc1:  # 第3个
            return rslis, loss, fg
    loss = acc1  # 更新
    if [acc1, comb, acc0] not in rslis:
        fg, i = 1, 0
        for i in range(len(rslis)):
            if rslis[i][0] < acc1:
                break
        rslis.insert(i, [acc1, comb, acc0])
        print('get one!\n', rslis[0])
        write_str('\n'.join(['%.3f, %s, %.3f' % (r[0], str(r[1]), r[2]) for r in rslis]), f1)
    return rslis, loss, fg


def display_time0(t1, cn, cnt, dp):
    '''打印剩余时间'''
    t2 = time.time()
    sec = int((t2-t1) * (cn-cnt) / dp)
    dspy0 = 'start: %d, left: %d, end_time: ' % (cnt, cn-cnt)
    hr, mn, sc = sec//3600, (sec%3600)//60, sec%60
    if hr:
        dspy0 += '%dh' % hr
    if mn:
        dspy0 += '%dmin' % mn
    if sc:
        dspy0 += '%ds' % sc
    print(dspy0)
    return t2


def get_trva_idx0(pts, iks, i1, i2):
    '''搜索策略中的数据来源: 获取tr/tr_va的id索引'''
    ts = [[], [], [], []]
    for ik in i1:
        for i in range(len(iks[ik])):
            ts[0] += pts[iks[ik][i]][0]
            ts[1] += pts[iks[ik][i]][1]
    for ik in i2:
        for i in range(len(iks[ik])):
            ts[2] += pts[iks[ik][i]][0]
            ts[3] += pts[iks[ik][i]][1]
    return ts


def search_dg1(nm, ik, comb0, ls, ts, f1, i1=0):
    '''i1起始搜索位置: 可选的'''
    cn0, dp = 0, 1000
    l0, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    acc0, rslis = 0, []
    nr0, nr1, ne0, ne1 = len(tr0), len(tr1), len(te0), len(te1)
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    if len(comb0) == 0:
        rgls = list(range(max(1, i1), n1+1))  # 每组最多5个
    else:
        rgls = list(range(max(0, i1), n1+1))
    t1 = time.time()
    nm00 = [10, 7, 8]  # 设定个数天花板
    for i in rgls:  # 最多搜索8个结果
        if i >= nm00[ik]:
            break
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        fg = 1
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            cnt += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t1 = display_time0(t1, cn, cnt, dp)
            l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
            l1 = comb_res3([l00[j] for j in tr0], n01, n00)
            l2 = comb_res3([l00[j] for j in tr1], n01, n00)
            l12 = list(set(l1+l2))  # roc曲线上的全部概率
            l12.sort(reverse=True)
            p1 = l12[-1]
            for i3 in range(len(l12)):
                fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l2 if p0 >= l12[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                if sen >= spe:
                    p1 = l12[i3]
                    break
            l1 = comb_res3([l00[j] for j in te0], n01, n00)
            l2 = comb_res3([l00[j] for j in te1], n01, n00)
            fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= p1)
            acc = round((ne0-fp+tp)/(ne0+ne1), 3)
            if acc > acc0:
                j = 0
                for j in range(min(len(rslis), 3)):
                    if acc > rslis[j][0]:
                        break
                rslis.insert(j, [acc, comb, p1])
                print('get one!\n', rslis[:min(len(rslis), 3)])
                fg = 0
                acc0 = rslis[min(len(rslis), 3)-1][0]
        # if fg and i > 2:
        # if i >= nm00[ik]:
        #     break
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    lis1 = []
    for i in range(min(len(rslis), 3)):
        comb = rslis[i][1]
        l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        l1 = comb_res3([[l0[j][k] for k in comb] for j in tr0], n01, n00)
        l2 = comb_res3([[l0[j][k] for k in comb] for j in tr1], n01, n00)
        l12 = list(set(l1+l2))  # roc曲线上的全部概率
        l12.sort(reverse=True)
        for i3 in range(len(l12)):
            fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= l12[i3])
            sen, spe = tp/nr1, (nr0-fp)/nr0
            if sen >= spe:
                p1 = l12[i3]
                break
        l1 = comb_res3([[l0[j][k] for k in comb] for j in te0], n01, n00)
        l2 = comb_res3([[l0[j][k] for k in comb] for j in te1], n01, n00)
        fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
        tp = sum(1 for p0 in l2 if p0 >= p1)
        lis1.append([i, (ne0-fp+tp)/(ne0+ne1), tp/ne1])
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    comb0 = rslis[int(lis1[0][0])][1]
    return comb0


def search_dg0(nm, ik, comb0, ls, ts, f1, i1=0):
    '''i1起始搜索位置'''
    cn0, dp = 0, 1000
    l0, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    acc0, rslis = 0, []
    nr0, nr1, ne0, ne1 = len(tr0), len(tr1), len(te0), len(te1)
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    if len(comb0) == 0:
        rgls = list(range(max(1, i1), n1+1))  # 每组最多5个
    else:
        rgls = list(range(max(0, i1), n1+1))
    t1 = time.time()
    # i=1
    for i in rgls:  # 最多搜索8个结果
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        fg = 1
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t2 = time.time()
                sec = int((t2-t1) * (cn-cn0) / dp)
                dspy0 = 'start: %d, left: %d, end_time: ' % (cn0, cn-cn0)
                hr, mn, sc = sec//3600, (sec%3600)//60, sec%60
                if hr:
                    dspy0 += '%dh' % hr
                if mn:
                    dspy0 += '%dmin' % mn
                if sc:
                    dspy0 += '%ds' % sc
                print(dspy0)
                t1 = t2
            l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l1 = comb_res1([[l0[j][k] for k in comb] for j in tr0], n01, n00)
            l2 = comb_res1([[l0[j][k] for k in comb] for j in tr1], n01, n00)
            # if i == 6:
            #     auc1, roc1 = cal_auc0(l1, l2)
            #     print(roc1)
            l12 = list(set(l1+l2))  # roc曲线上的全部概率
            l12.sort(reverse=True)
            for i3 in range(len(l12)):
                fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l2 if p0 >= l12[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                # print(sen, spe)
                if sen >= spe:
                    p1 = l12[i3]
                    break
            l1 = comb_res1([[l0[j][k] for k in comb] for j in te0], n01, n00)
            l2 = comb_res1([[l0[j][k] for k in comb] for j in te1], n01, n00)
            fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= p1)
            acc = round((ne0-fp+tp)/(ne0+ne1), 3)
            # if ik == 0:
            #     print(p1, acc)
            if acc > acc0:
                j = 0
                for j in range(min(len(rslis), 3)):
                    if acc > rslis[j][0]:
                        break
                rslis.insert(j, [acc, comb, p1])
                print('get one!\n', rslis[:min(len(rslis), 3)])
                fg = 0
                acc0 = rslis[min(len(rslis), 3)-1][0]
        if fg:
            break
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    lis1 = []
    for i in range(min(len(rslis), 3)):
        comb = rslis[i][1]
        l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        l1 = comb_res1([[l0[j][k] for k in comb] for j in tr0], n01, n00)
        l2 = comb_res1([[l0[j][k] for k in comb] for j in tr1], n01, n00)
        l12 = list(set(l1+l2))  # roc曲线上的全部概率
        l12.sort(reverse=True)
        for i3 in range(len(l12)):
            fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= l12[i3])
            sen, spe = tp/nr1, (nr0-fp)/nr0
            if sen >= spe:
                p1 = l12[i3]
                break
        l1 = comb_res1([[l0[j][k] for k in comb] for j in te0], n01, n00)
        l2 = comb_res1([[l0[j][k] for k in comb] for j in te1], n01, n00)
        fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
        tp = sum(1 for p0 in l2 if p0 >= p1)
        lis1.append([i, (ne0-fp+tp)/(ne0+ne1), tp/ne1])
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    comb0 = rslis[int(lis1[0][0])][1]
    return comb0


def cal_acc(l00, p1, idsp1):
    l1, l2 = [l00[i2] for i2 in idsp1[0]], [l00[i2] for i2 in idsp1[1]]
    n11 = sum(1 for i2 in range(len(l1)) if l1[i2] < p1)
    n12 = sum(1 for i2 in range(len(l2)) if l2[i2] >= p1)
    return n11+n12


def cal_per(l00, n01, n00):
    nl1, nl2 = len(l00[0]), len(l00)
    if nl1 == 1:
        return [l00[i3][0] for i3 in range(nl2)]
    else:
        for i3 in range(nl2):
            permean = np.average(l00[i3])
            if permean == 50:
                l00[i3] = 50
            elif permean > 50:
                l00[i3] = [l00[i3][j3] for j3 in range(nl1) if l00[i3][j3] > 50]
                l00[i3] = int(((sum(l00[i3])-50*len(l00[i3]))/n01+1)*50)
            else:
                l00[i3] = [l00[i3][j3] for j3 in range(nl1) if l00[i3][j3] < 50]
                l00[i3] = int((1-(50*len(l00[i3])-sum(l00[i3]))/n00)*50)
        return l00


def get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt):
    '''
    插值后指标: disp, QR, RS, TT
    timelis: 时刻点
    mf: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    # print(timelis0)
    mlfile = writedisp(matrixes, timelis0, mcglines)[0]
    mlfile = mlfile[:-3] + [cal_noqrs(mlfile[-3:])]
    mtsall = [[mlfile]]
    frfile = cal_frrt(matrixes, q_peak, r_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zqr, nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, r_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zrs, nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, t_onset, t_end)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Ztt, t_idx=t_peak-t_onset, mode='tt', nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    return mtsall


def get_mfm_mts_intp_pre(timelis0, matrixes, mcglines, Zqr, Zrs, Zqrs, Ztt):
    '''
    插值前指标: disp, QR, RS, QRS, TT
    timelis: 时刻点
    mflis: 最大幅值
    ctrdfile: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/QRS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    mlfile, rksfile = writedisp(matrixes, timelis0, mcglines)
    mtsall = [[mlfile, rksfile]]
    frfile = cal_frrt(matrixes, q_peak, r_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zqr)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, r_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zrs)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, q_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zqrs)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, t_onset, t_end)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Ztt, t_idx=t_peak-t_onset, mode='tt')
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    return mtsall


def cal_zb_qt0(ls, Z):
    '''连通嵌套: ls形态集, Z矩阵'''
    from PIL import Image
    from scipy.ndimage import binary_fill_holes
    from skimage import measure
    zbs, qts = [], []
    for i1 in range(len(ls)):
        v0 = ls[i1]  # 形态
        # print(v0)
        matrix = np.where(Z>v0, 1, 0).astype(np.uint8)  # 正极
        if v0 < 0:  # 负极
            matrix = np.where(Z<v0, 1, 0).astype(np.uint8)
        image = Image.fromarray(matrix*255)  # 转换为图像
        filled_image = binary_fill_holes(np.array(image))  # 填充小孔
        binary_image = filled_image > 0  # 转换二值图像
        label_image = measure.label(binary_image)  # 测量连通组件
        zb0, qt0 = [], []
        for region in measure.regionprops(label_image):  # 遍历每个连通组件
            l1 = region.coords
            zb1 = [list(l1[i]) for i in range(len(l1))]
            zb0.append(zb1)  # 添加坐标集
            qt1 = []
            if i1 > 0:
                for i2 in range(len(zbs[i1-1])):  # 源形态连通数
                    if zbs[i1-1][i2][0] in zb1:  # 坐标集
                        qt1.append(i2)
            qt0.append(qt1)  # 添加嵌套索引-源连通
        zbs.append(zb0)
        qts.append(qt0)
    return zbs, qts


def cal_jh_idx0(qts):
    '''计算聚合索引集: 逆嵌套qts'''
    idx = []
    idx.append([list(range(len(qts[0])))])  # 0.3的全部形态
    for i in range(len(qts)-1):
        idx.append([qts[i][j] for j in range(len(qts[i])) if len(qts[i][j])>1])  # 嵌套数量>1的那些嵌套索引
    return idx


def cal_szh_zx1(zbs, iks, Z):
    '''计算数值和/重心: 连通坐标集zbs/聚合索引idx/矩阵Z'''
    szhs, zxs = [], []
    for i in iks:
        s, wx, wy = 0, 0, 0
        for x, y in zbs[i]:  # 单连通坐标集
            s += Z[x, y]  # 数值和
            wx += x*Z[x, y]  # 加权坐标x
            wy += y*Z[x, y]  # 加权坐标y
        cx, cy = wx/s, wy/s
        szhs.append(abs(s))
        zxs.append([cx, cy])
    return szhs, zxs


def cal_szh_zx0(zbs, Z):
    '''统计多极多连通数值和集/重心集: 多极坐标集zbs/矩阵Z'''
    szhs, zxs = [], []
    for i in range(len(zbs)):
        szh0, zx0 = cal_szh_zx1(zbs[i], range(len(zbs[i])), Z)
        szhs.append(szh0)
        zxs.append(zx0)
    return szhs, zxs


def cal_bianchang1(Z, md=0):
    '''矩阵的1区域边坐标: 弧线坐标nm1, 非边界坐标nm2'''
    nm1, nm2 = [], []
    for i in range(Z.shape[0]):
        for j in range(Z.shape[1]):
            if Z[i, j] == 1:
                if i in [0, 99] or j in [0, 99]:
                    nm2.append([i, j])
                else:
                    for i1,j1 in [[-1,0],[1,0],[0,1],[0,-1]]:
                        if Z[i-i1,j-j1] == 0:
                            nm1.append([i, j])
                            break
    # return nm1, nm2
    if md:  # 全输出
        return nm1, nm2
    else:
        return nm1


def sort_pts0(a):
    '''弧线坐标排序和分段'''
    c = []  # 存储已连接的弧线点
    e, e1 = [], []  # 弧线条数汇总, 单条
    for i in range(len(a)):  # 全部数量索引
        if i == 0:
            c.append(a[i])  # 已连接
            e1.append(a[i])  # 初始起点
        else:
            b = [b1 for b1 in a if b1 not in c]  # 剩余未连接
            d = [product2(b[j][0]-c[-1][0], b[j][1]-c[-1][1]) for j in range(len(b))]  # 全部剩余点的距离列表
            c.append(b[d.index(min(d))])  # 取最小距离点
            if min(d) > 2:  # 最小距离>2重新开启新弧线段
                e.append(e1)  # 添加段
                e1 = [c[-1]]  # 新段起点
            else:
                e1.append(c[-1])  # 连接
    e.append(e1)  # 最后一段
    e = rm_element(e, [])  # 移除空白段
    if len(e)>2:  # 第一个和最后一个弧线段的距离很近，融合为一段——
        d = e[0][0]
        f = e[-1][-1]
        if product2(d[0]-f[0], d[1]-f[1]) <= 2:
            e = [e[-1]+e[0]] + e[1:-1]
    return e


def cal_bj0(zbs):
    '''统计多级多连通的边界集/边界率集: 坐标集zbs'''
    bjs, bjls = [], []
    for i in range(len(zbs)):  # 逐级
        bj0, bjl0 = [], []
        for j in range(len(zbs[i])):  # 逐连通
            Z0 = np.zeros((100, 100), dtype=int)
            for x, y in zbs[i][j]:
                Z0[x, y] = 1
            nm1, nm2 = cal_bianchang1(Z0, 1)  # 边界坐标
            bjl0.append(len(nm2)/len(nm1+nm2))  # 边界率
            bj0.append(sort_pts0(nm1))  # 弧线段
        bjs.append(bj0)
        bjls.append(bjl0)
    return bjs, bjls


def cal_score0(v0, it0, cd0):
    '''计算弥散度得分: 阈值it0/等级cd0'''
    jd = 4  # 精度——
    if v0 < it0[0]:
        sc0 = cd0[0]*v0/it0[0]
    else:
        sc0 = 10
        for i in range(1, len(it0)):
            if v0 < it0[i]:
                sc0 = cd0[i-1]+(cd0[i]-cd0[i-1])*(v0-it0[i-1])/(it0[i]-it0[i-1])
                break
    return round(sc0, jd)


def gb_score0(v0, md=0):
    '''改变弥散度得分: 调整到0-10'''
    cd0 = [1, 3, 4, 8, 10]
    if md == 0:  # 聚合离散度阈值
        it0 = [10, 25, 130, 250, 500]
    elif md == 1:  # 距离弥散度阈值
        it0 = [0.7, 1, 2.5, 4.3, 15]
    elif md == 2:
        it0 = [0.4, 1.5, 6, 10, 15]
    else:  # 形态弥散度阈值
        # it0 = [0.15, 1.7, 8, 16, 25]
        it0 = [0.15, 2.33, 8, 16, 25]
    return cal_score0(v0, it0, cd0)


def cal_jh1(szhs, zxs):
    '''
    计算聚合离散度:
        数值和集szhs/重心集zxs
        双离散公式jh1=k*数值和1*数值和2/中心距离^2
        多离散公式jh1=jh1*2/n——两两作用/全部平均
    '''
    if len(szhs) < 2:
        return gb_score0(0, 0)
    jh1 = 0
    k = 1  # 库仑参数——
    for i in range(len(szhs)-1):  # 前n-1个逐级找匹配
        for j in range(i+1, len(szhs)):  # 遍历i之后-匹配
            zxjl = product2(zxs[i][0]-zxs[j][0], zxs[i][1]-zxs[j][1])
            # print('szh, zx:', szhs[i], szhs[j], zxjl)
            jh2 = k*szhs[i]*szhs[j]/(zxjl*zxjl)
            jh1 += jh2
    jh1 = jh1 * 2 / len(szhs)
    jh1 = gb_score0(jh1, 0)
    return jh1


def cal_jh0(qts, szhs, zxs, i1):
    '''计算聚合离散度集: 嵌套集qts/数值和集szhs/重心集zxs/形态极i1'''
    jh0 = []
    if i1 == 0:  # 0.3多连通的整体离散度
        idx = range(len(qts[i1]))
        szhs0 = [szhs[j] for j in idx]
        zxs0 = [zxs[j] for j in idx]
        jh1 = cal_jh1(szhs0, zxs0)
        jh0.append(jh1)
    else:
        for i in range(len(qts[i1-1])):  # 0.3-0.8各级下的多连通
            jh1 = 0
            if len(qts[i1-1][i]) > 1:  # 多连通的聚合离散度
                idx = qts[i1-1][i]
                szhs0 = [szhs[j] for j in idx]
                zxs0 = [zxs[j] for j in idx]
                jh1 = cal_jh1(szhs0, zxs0)
            jh0.append(jh1)
    return jh0


def get_range0(n0, i1=-9, i2=10):
    '''获取列表范围: 长度n0, 索引起点i1, 终点i2'''
    if i1>=0 and i2<=n0:
        return list(range(i1,i2))
    if i1 < 0:
        if i2-i1<n0:
            l1 = list(range(i2))+list(range(n0-i1,n0))
        else:
            l1 = list(range(n0))
    else:
        if i2-i1<n0:
            l1 = list(range(i2-n0))+list(range(i1,n0))
        else:
            l1 = list(range(n0))
    return l1


def get_tuchu0(ds):
    '''
    形态突出位置统计: 
        距离列表ds, 计算最大距离和次大突出位置
        阈值: 突出间距>10; 突出程度>1.5mean; 突出边(-4,4)
    '''
    ik = ds.index(max(ds))
    d0 = 1.5*sum(ds)/len(ds)  # 程度阈值
    if max(ds) < d0:  # 小于突出阈值1.5
        return []
    ls = [ik]
    rg0 = get_range0(len(ds), ik-9, ik+10)
    ds0 = []
    for i in range(len(ds)):
        if i in rg0:  # 最大间距
            continue
        if ds[i] < d0:
            continue
        flag = 1
        rg1 = get_range0(len(ds), i-4, i+5)
        for j in rg1:
            k = max(0, min(j, len(ds)-1))
            if ds[i] < ds[k]:  # 4近邻最大值>=
                flag = 0
                break
        if flag:
            ds0.append(ds[i])
    if len(ds0) > 0:
        ls.append(ds.index(max(ds0)))
    return ls


def add_index0(flat_segments, st, i, T2, ik):
    if ik == 0:
        if len(st) == 0:
            st.append(i)
        else:
            st = [st[0], i+1]
    else:
        if len(st) > 0:  # 非空
            st = [st[0], i]
            if st[1]-st[0]+1 > T2:
                flat_segments.append(st)
        st = []
    return flat_segments, st


def get_rg_lis0(f1, n0):
    '''统计区间分段的列表: 段fs, 长度n0'''
    fs = f1.copy()
    f0 = []
    if len(fs) == 1:
        return [list(range(fs[0][0],fs[0][1]))]
    if fs[0][0]==0 and fs[-1][1]==n0-1:
        f0.append(list(range(fs[-1][0],n0))+list(range(fs[0][1])))
        fs.pop(-1)
        fs.pop(0)
    for t1, t2 in fs:
        f0.append(list(range(t1,t2+1)))
    return f0


def cal_tudu0(ik, ds, p0s, f0):
    '''计算弥散度/突出程度: ik突出位置, ds距离集, p0s源形态点集, f0稳定段集'''
    f1 = []
    l1 = [len(f0[i]) for i in range(len(f0))]
    f2 = f0[l1.index(max(l1))]
    if ik in f2:
        return 0
    # print(f0)
    for i in range(len(f0)):
        if ik not in f0[i]:
            f1.append(f0[i])
    # for t1, t2 in f0:  # 备选稳定段
    #     if ik < t1 or ik > t2:
    #         f1.append([t1, t2])
    # if len(f1) > 0:
    #     if ik == 0 and f1[-1][1] == len(ds)-1:
    #         f1.pop(-1)
    #     if ik == len(ds)-1 and f1[0][0] == 0:
    #         f1.pop(0)
    if f1 == []:  # 不突出
        return 0
    a, b = [], []
    # for t1, t2 in f1:  # 最近稳定段位置索引——左右各1
    for i in range(len(f1)):
        t1, t2 = f1[i][0], f1[i][-1]
        if ik < t1:
            a.append(t1-ik)
        else:
            a.append(len(ds)-ik+t1)
        if ik > t2:
            b.append(ik-t2)
        else:
            b.append(len(ds)-t2+ik)
    t1 = f1[a.index(min(a))][0]
    t2 = f1[b.index(min(b))][-1]  # 1
    d0 = ds[ik]  # 最大距离
    if ds[t1] >= ds[t2]:  # 最大稳定位置
        d1 = ds[t1]  # 稳定距离
        # if ik < t1:
        #     ls = p0s[ik:t1+1]
        # else:
        #     ls = p0s[t1:]+p0s[:ik+1]
        # l1 = cal_elm_nums0(ls)  # 底边长-源弧线点数量
    else:
        d1 = ds[t2]
        # if ik > t2:
        #     ls = p0s[t2:ik+1]
        # else:
        #     ls = p0s[ik:]+p0s[:t2+1]
        # l1 = cal_elm_nums0(ls)
    if d0 < d1*1.2:
        return 0
    # tv = (d0-d1)/(d1*l1)
    tv = (d0-d1)/d1
    # print('index', ds.index(d1))
    # print('tv', ik, d0, d1, l1, tv)
    # print('tv', ik, d0, d1, tv)
    return tv


def tucq_test0(group1, group2, md='u'):
    '''t检验u检验卡方检验的P值和相互独立性'''
    alpha = 0.05
    group1 = np.array(group1)
    group2 = np.array(group2)
    fg = 0
    if md == 't':
        t_stat, p_value = stats.ttest_ind(group1, group2)
        if p_value < alpha:  # 相互独立
            fg = 1
    elif md == 'u':
        u, p_value = stats.mannwhitneyu(group1, group2, alternative='two-sided')
        if p_value < alpha:  # 相互独立
            fg = 1
    elif md == 'cq':
        data = np.array([group1, group2])
        chi2, p_value, dof, expected = stats.chi2_contingency(data)
        if p_value >= alpha:  # 相互独立
            fg = 1
    return p_value, fg


def cal_pktg0(ys, vv, th):
# def cal_pktg0(ys, vv, th, g1, xs):
    '''高斯拟合曲线/波峰波谷位置'''
    ysm = gaussian_filter1d(ys, sigma=vv)
    x12 = signal.find_peaks(ysm, distance=th)[0]
    x11 = signal.find_peaks(-ysm, distance=th)[0]
    idx = list(x12)+list(x11)  # 初始波峰波谷0
    # id0 = sorted(set(idx))
    # xss = [xs[i] for i in id0]
    # yss = [ysm[i] for i in id0]
    # draw0([[xs, ys], [xs, ysm]], [[xss, yss]], '%s_gausssian2.png' % g1)
    return x11, idx


def cal_pks0(idx, xs, ys, x11, th):
    '''合并波峰波谷: xs=n1+n2, ys=n2-n1, th范围数量阈值'''
    idx = sorted(set(idx))
    if idx == []:
        return idx
    pks0 = [0]  # 前向选择过半数量
    for i in range(1, len(idx)-1):  # 不取最后一个
        if pks0[-1] >= len(idx)/2-1:  # 过半
            break
        if xs[idx[i]]-xs[idx[pks0[-1]]]>th:  # 数量大于阈值
            if xs[idx[i+1]]-xs[idx[i]]>th:
                pks0.append(i)  # 后者相距大于阈值
            elif ys[idx[i]]>ys[idx[pks0[-1]]] and ys[idx[i]]>ys[idx[i+1]]:
                pks0.append(i)  # 大于前后ys
            elif ys[idx[i]]<ys[idx[pks0[-1]]] and ys[idx[i]]<ys[idx[i+1]]:
                pks0.append(i)  # 小于前后ys
    pks1 = [len(idx)-1]  # 后向选择过半数量
    for j in range(1, len(idx)-1):  # 不取最后一个
        i = len(idx) - 1 - j
        if pks1[-1] <= len(idx)/2-1:  # 过半
            break
        if xs[idx[pks1[-1]]]-xs[idx[i]]>th:
            if xs[idx[i]]-xs[idx[i-1]]>th:
                pks1.append(i)
            elif ys[idx[i]]>ys[idx[pks1[-1]]] and ys[idx[i]]>ys[idx[i-1]]:
                pks1.append(i)
            elif ys[idx[i]]<ys[idx[pks1[-1]]] and ys[idx[i]]<ys[idx[i-1]]:
                pks1.append(i)
    pks1.reverse()
    for i in range(len(pks1)-1):  # 筛选只保留1个过半数量
        if pks1[i+1]>pks0[-1]:
            pks1 = pks1[i:]
            break
    pks = pks0[:-1]+pks1  # 保留后向结果
    if pks0[-1] > pks1[0]:  # 重叠分段点
        if pks0[-1]-(len(idx)/2-1) < len(idx)/2-1-pks1[0]:
            pks = pks0+pks1[1:]  # 前向距离中点更近
        elif pks0[-1]-(len(idx)/2-1) == len(idx)/2-1-pks1[0]:
            if xs[idx[pks0[-1]]]-xs[idx[pks0[-2]]] > xs[idx[pks1[1]]]-xs[idx[pks1[0]]]:
                pks = pks0+pks1[1:]
    idx0 = [idx[i] for i in pks]
    idx = []  # 实际分段点
    l1, l2 = find_extrema(ys)  # 极大极小值点集
    for i in idx0:
        i0 = i
        if i in x11:  # 波谷
            l3 = find_closest_points(i, l2)  # 最近极小值点
            l3 = [j for j in l3 if abs(xs[i]-xs[j]) < th]
            l4 = [ys[j] for j in l3]
            if l4 != []:  # 最近且最小
                i0 = l3[l4.index(min(l4))]
        else:
            l3 = find_closest_points(i, l1)
            l3 = [j for j in l3 if abs(xs[i]-xs[j]) < th]
            l4 = [ys[j] for j in l3]
            if l4 != []:
                i0 = l3[l4.index(max(l4))]
        idx.append(i0)
    tc = []
    for i in range(len(idx)-1):
        if xs[idx[i+1]]-xs[idx[i]]<th:
            if i == 0:
                tc.append(idx[i+1])
            elif i == len(idx)-2:
                tc.append(idx[i])
            else:
                if xs[idx[i]]-xs[idx[i-1]]>xs[idx[i+2]]-xs[idx[i+1]]:
                    tc.append(idx[i+1])
                else:
                    tc.append(idx[i])
    idx = [i for i in idx if i not in tc]
    return idx


def find_closest_points(v0, l0):
    '''寻找最近的点集'''
    l1 = [abs(v0-x) for x in l0]
    v1 = min(l1)
    l2 = [l0[i] for i in range(len(l0)) if l1[i] == v1]
    return l2


def find_extrema(lis):
    '''寻找极大极小值点: l1极大, l2极小'''
    l1, l2 = [], []
    for i in range(1, len(lis) - 1):
        if lis[i] > lis[i - 1] and lis[i] > lis[i + 1]:
            l1.append(i)
        elif lis[i] < lis[i - 1] and lis[i] < lis[i + 1]:
            l2.append(i)
    if lis[0] > lis[1]:
        l1.append(0)
    elif lis[0] < lis[1]:
        l2.append(0)
    if lis[-1] > lis[-2]:
        l1.append(len(lis) - 1)
    elif lis[-1] < lis[-2]:
        l2.append(len(lis) - 1)
    l1 = sorted(set(l1))
    l2 = sorted(set(l2))
    return l1, l2


def if_exva0(ys, j1, j2, fg):
    '''两个位置的极值索引: ys列表, j1/j2索引, fg=1极大值'''
    r = ''
    try:
        if fg:
            if ys[j1] > ys[j2]:  # 更大
                if ys[j1]>=ys[j1+1] and ys[j1]>=ys[j1-1]:
                    r = j1
            else:
                if ys[j2]>=ys[j2+1] and ys[j2]>=ys[j2-1]:
                    r = j2
        else:
            if ys[j1] > ys[j2]:  # 更小
                if ys[j2]<=ys[j2+1] and ys[j2]<=ys[j2-1]:
                    r = j2
            else:
                if ys[j1]<=ys[j1+1] and ys[j1]<=ys[j1-1]:
                    r = j1
    except:
        pass
    return r


def cal_rlsgp0(i1, x11, xs, ys):
    '''计算实际分段点'''
    fg = 1
    if i1 in x11:
        fg = 0  # 极小值
    for i in range(5):
        i2 = if_exva0(ys, i1-i, i1+i, fg)
        if i2:
            i1 = i2
            break
    return i1


def cal_seg0(apx, pks1, xs, ys):
    '''筛选单调分段'''
    pks2 = cal_pks2(apx, pks1, ys)
    n1, n2 = len(apx)-1, len(pks2)-1
    if n1 != n2:  # 与锚点不匹配
        l0 = cal_pks3(apx, pks2, xs, ys)  # 斜率集
        pks2 = cal_pks4(apx, l0, pks2, xs, ys, 5)
    return pks2


def cal_pks1(pks0, xs, ys, th):
    '''合并锚点: 剔除候选点中的锚点近邻, 添加非近邻的最值点, 合并排序'''
    apx = [ys.index(max(ys)), ys.index(min(ys)), 0, len(xs)-1]  # 锚点
    pks2 = []
    for i in apx:
        pks2 += [i0 for i0 in pks0 if abs(xs[i0]-xs[i])<th]
    apx = [0, len(xs)-1]
    for i in [ys.index(max(ys)), ys.index(min(ys))]:
        if [1 for j in apx if abs(xs[i]-xs[j])<th] == []:  # 非近邻, 添加
            apx.append(i)
    apx.sort()
    pks2.sort()
    # print([xs[i] for i in pks0])
    # print([xs[i] for i in pks2])
    pks1 = sorted(set([p0 for p0 in pks0 if p0 not in pks2]+apx))  # 去重复
    return apx, pks1


def ifzj(lis):
    '''判断数列是否单调: True 单调'''
    array = np.array(lis)
    dfa = np.ediff1d(array)
    return np.all(dfa>=0) or np.all(dfa<=0)


def cal_pks2(apx, pks1, ys):
    '''候选单调分段点'''
    pks2 = [pks1[0]]
    for i in range(len(apx)-1):
        i1, i2 = pks1.index(apx[i]), pks1.index(apx[i+1])
        # l0 = [ys[pks1[j]] for j in range(i1, i2)]
        pks2.append(pks1[i2])
        l1 = [pks1[i1], pks1[i2]]
        for j in range(i1+1, i2):
            if not ifzj([ys[pks1[k]] for k in range(j-1, j+2)]):  # 3点论初筛
                l1.insert(-1, pks1[j])
        for j in range(1, len(l1)-1):
            if not ifzj([ys[k] for k in l1[j-1:j+2]]):  # 3点论再筛
                pks2.insert(-1, l1[j])
    pks2.sort()
    return pks2


def cal_pks3(apx, pks2, xs, ys):
    '''固定分段数'''
    l0 = []
    for i in range(len(apx)-1):
        i1, i2 = pks2.index(apx[i]), pks2.index(apx[i+1])
        if i2-i1>1:
            ks = cal_ks1(pks2, i1, i2, xs, ys)  # 整体和分段斜率集
            l0.append([i1]+get_round(ks, [2 for j in range(len(ks))]))
    return l0


def fit_pks0(pks2, pks1, xs, ys):
    '''筛选单调分段: 异常不单调'''
    l0, l1, l2 = [], [], pks1
    for i in range(len(pks2)-1):
        i1, i2 = pks1.index(pks2[i]), pks1.index(pks2[i+1])
        if i2-i1>1:  # 单调多分段
            ks = cal_ks1(pks1, i1, i2, xs, ys)  # 整体和分段斜率集
            l0.append(ks)
            l11 = pks1[i1:i2+1]
            l1.append(l11)
            l2 = [pt for pt in l2 if pt not in l11]
    return l0, l1, l2


def cal_pks4(apx, l0, pks2, xs, ys, n0):
    '''单调分段点: 固定和排序'''
    n1, n2 = len(apx)-1, len(pks2)-1
    l6 = [i for i in range(len(pks2)) if pks2[i] in apx]
    if n2 > n1:
        l1 = []
        for l2 in l0:
            i1, k0, l3 = l2[0], l2[1], l2[2:]
            for i in range(len(l3)):  # 统计备选斜率区间
                if abs(k0+l3[i]) < max(abs(k0), abs(l3[i])) and abs(l3[i]) > 0.01:
                    d = round(xs[pks2[i1+i+1]]-xs[pks2[i1+i]], 2)
                    l1.append([abs(l3[i]), d, [i1+i, i1+i+1]])  # 数值/距离/索引ij
        if len(l1) > 0:  # 数值越大越靠前, 数值相差<=0.02时距离越大越靠前
            l1.sort(key=lambda x: x[0], reverse=True)  # 按0排序
            l2, l4, l5 = [], [], []
            for i in range(len(l1)):
                if l5 == []:
                    l5, l2 = l1[i][:1], [l1[i]]
                    if i == len(l1)-1:
                        l2.sort(key=lambda x: x[1], reverse=True)
                        l4 += l2
                else:
                    if l5[0]-l1[i][0]<0.03:
                        l2.append(l1[i])
                    else:
                        l2.sort(key=lambda x: x[1], reverse=True)
                        l4 += l2
                        l5, l2 = l1[i][:1], [l1[i]]
                    if i == len(l1)-1:
                        l2.sort(key=lambda x: x[1], reverse=True)
                        l4 += l2
            for _, _, ik in l4:  # 逐步添加不多于阈值的段数
                if len(sorted(set(l6+ik)))-1>n0:
                    break
                else:
                    l6 = sorted(set(l6+ik))
    pks3 = [pks2[i] for i in l6]
    return pks3


def cal_ddhb0(l2, iks, k0, xs, ys, a):
    '''端点合并1'''
    if a>0:
        for i in range(len(l2)):
            if i==len(l2)-1:
                iks = [iks[0]]+iks[i+1: len(iks)]
                l2 = l2[i:]
            elif l2[i]*k0<=0 or l2[i]>k0:  # 反向/趋势矛盾
                i1, i2 = iks[0], iks[i+2]+1
                l2[i+1] = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]  # 更新合并斜率
                # print(i1, i2-1, l2[i+1])
            else:  # 剩余保存
                iks = [iks[0]]+iks[i+1: len(iks)]
                l2 = l2[i:]
                break
        for j in range(len(l2)):
            i = -1-j
            if j==len(l2)-1:
                iks = iks[:len(iks)+i]+[iks[-1]]
                l2 = l2[:len(l2)+i+1]
            elif l2[i]*k0<=0 or l2[i]<k0:
                i1, i2 = iks[i-2], iks[-1]+1
                l2[i-1] = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                # print(i1, i2-1, l2[i-1])
            else:
                iks = iks[:len(iks)+i]+[iks[-1]]
                l2 = l2[:len(l2)+i+1]
                break
    else:
        for i in range(len(l2)):
            if i==len(l2)-1:
                iks = [iks[0]]+iks[i+1: len(iks)]
                l2 = l2[i:]
            elif l2[i]*k0<=0 or l2[i]<k0:
                i1, i2 = iks[0], iks[i+2]+1
                l2[i+1] = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                # print(i1, i2-1, l2[i+1])
            else:
                iks = [iks[0]]+iks[i+1: len(iks)]
                l2 = l2[i:]
                break
        for j in range(len(l2)):
            i = -1-j
            if j==len(l2)-1:
                iks = iks[:len(iks)+i]+[iks[-1]]
                l2 = l2[:len(l2)+i+1]
            elif l2[i]*k0<=0 or l2[i]>k0:
                i1, i2 = iks[i-2], iks[-1]+1
                l2[i-1] = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                # print(i1, i2-1, l2[i-1])
            else:
                iks = iks[:len(iks)+i]+[iks[-1]]
                l2 = l2[:len(l2)+i+1]
                break
    return l2, iks


def cal_jshb0(l2, iks, xs, ys):
    '''近似合并2'''
    l21, iks1 = [], [iks[0]]
    for i in range(len(l2)-1):
        if abs(l2[i+1]-l2[i]) < 0.03:
            i1, i2 = iks[i], iks[i+2]+1
            l2[i+1] = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
            # print(i1, i2-1, l2[i+1])
            iks[i+1] = iks[i]
        else:
            iks1.append(iks[i+1])
            l21.append(l2[i])
    iks1.append(iks[-1])
    l21.append(l2[-1])
    return l21, iks1


def cal_jjhb0(l2, iks, xs, ys, a):
    '''就近合并3'''
    l21, iks1 = [], []
    if a > 0:
        for i in range(int((len(l2)+1)/2)):
            j = len(l2)-1-i
            if l2[i+1]<=l2[i] or l2[j]<=l2[j-1]:  # 趋势异常
                if l2[i+1]<=l2[i] and l2[j]<=l2[j-1] and j-i==1:  # 并排异常对
                    ds = [abs(l2[i]-l2[i-1]), abs(l2[i]-l2[i+1]), abs(l2[i+2]-l2[i+1])]
                    k = ds.index(min(ds)) + i - 1
                    i1, i2 = iks[k], iks[k+2]+1
                    k1 = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                    # print(i1, i2-1, k1)
                    l21 += l2[:k]+[k1]+l2[k+2:]
                    iks1 += iks[:k+1]+iks[k+2:]
                else:
                    if l2[i+1]<=l2[i]:
                        if i == 0:
                            k0 = i
                        else:
                            ds = [abs(l2[i]-l2[i-1]), abs(l2[i+1]-l2[i])]
                            k0 = ds.index(min(ds)) + i - 1
                        i1, i2 = iks[k0], iks[k0+2]+1
                        k1 = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                        l21 += l2[:k0]+[k1]+l2[k0+2:j-2]
                        iks1 += iks[:k0+1]+iks[k0+2:j-1]
                        k0 = max(k0+2, j-2)
                        # print(i1, i2-1, k1)
                    else:
                        l21 += l2[:j-2]
                        iks1 += iks[:j-1]
                        k0 = j - 2
                    if l2[j]<=l2[j-1] and j-i>1:
                        if j == len(l2)-1:
                            k = j-1
                        else:
                            ds = [abs(l2[j]-l2[j-1]), abs(l2[j-1]-l2[j-2])]
                            k = j - ds.index(min(ds)) - 1
                        i1, i2 = iks[k], iks[k+2]+1
                        k1 = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                        l21 += l2[max(k0,j-2):k]+[k1]+l2[k+2:]
                        iks1 += iks[max(k0,j-1):k+1]+iks[k+2:]
                        # print(i1, i2-1, k1)
                    else:
                        l21 += l2[max(j-2,k0):]
                        iks1 += iks[max(j-1,k0):]
                break
    else:
        for i in range(int((len(l2)+1)/2)):
            j = len(l2)-1-i
            if l2[i+1]>=l2[i] or l2[j]>=l2[j-1]:
                if l2[i+1]>=l2[i] and l2[j]>=l2[j-1] and j-i==1:
                    ds = [abs(l2[i]-l2[i-1]), abs(l2[i]-l2[i+1]), abs(l2[i+2]-l2[i+1])]
                    k = ds.index(min(ds)) + i - 1
                    i1, i2 = iks[k], iks[k+2]+1
                    k1 = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                    # print(i1, i2-1, k1)
                    l21 += l2[:k]+[k1]+l2[k+2:]
                    iks1 += iks[:k+1]+iks[k+2:]
                else:
                    # print('ij', k0, i, j)
                    if l2[i+1]>=l2[i]:
                        if i == 0:
                            k0 = i
                        else:
                            ds = [abs(l2[i]-l2[i-1]), abs(l2[i+1]-l2[i])]
                            k0 = ds.index(min(ds)) + i - 1
                        i1, i2 = iks[k0], iks[k0+2]+1
                        k1 = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                        # print(i1, i2-1, k1)
                        l21 += l2[:k0]+[k1]+l2[k0+2:j-2]
                        iks1 += iks[:k0+1]+iks[k0+2:j-1]
                        k0 = max(k0+2, j-2)
                    else:
                        l21 += l2[:j-2]
                        iks1 += iks[:j-1]
                        k0 = j - 2
                    if l2[j]>=l2[j-1] and j-i>1:
                        if j == len(l2)-1:
                            k = j-1
                        else:
                            ds = [abs(l2[j]-l2[j-1]), abs(l2[j-1]-l2[j-2])]
                            k = j - ds.index(min(ds)) - 1
                        i1, i2 = iks[k], iks[k+2]+1
                        k1 = cal_ks0(xs[i1:i2], ys[i1:i2], 0)[0]
                        # print(i1, i2-1, k1)
                        l21 += l2[max(k0,j-2):k]+[k1]+l2[k+2:]
                        iks1 += iks[max(k0,j-1):k+1]+iks[k+2:]
                    else:
                        l21 += l2[max(j-2,k0):]
                        iks1 += iks[max(j-1,k0):]
                    # print('now', l21, iks1)
                break
    return l21, iks1


def cal_dnhb0(l1, iks, xs, ys, a=0):
    '''
    单调段内合并: 端点合并/近似合并/就近合并-循环
    '''
    k0, l2 = l1[0], l1[1:]
    if abs(k0) < 0.03:  # 整体合并
        if k0 > 0:  # 最后修正！——————————
            k0 = max(0.02, k0)
        else:
            k0 = min(-0.02, k0)
        i, l2, iks = -1, [k0], [iks[0],iks[-1]]
    else:
        # print('raw', l1, '\n', iks)
        for i in range(10):  # 合并循环次数<10
            # print(i)
            l2, iks = cal_ddhb0(l2, iks, k0, xs, ys, a)  # 1.端点合并
            # print('d1', l2, iks)
            l2, iks = cal_jshb0(l2, iks, xs, ys)  # 2.近似合并
            # print('d2', l2, iks)
            if ifzj(l2):
                break
            else:
                l2, iks = cal_jjhb0(l2, iks, xs, ys, a)  # 3.就近合并
            # print('d3', l2, iks)
        if len(l2) == 1:
            l2 = [k0]
    if a > 0:
        return i, l1+['-+'], l2, iks
    else:
        return i, l1+['+-'], l2, iks


def fit_pks1(l0, pts, xs, ys):
    '''单调段内合并'''
    l2, l3, pt = [], [], []
    for i in range(len(l0)):
        l1, pt1 = l0[i], pts[i]
        i1, i2 = pt1[0], pt1[-1]+1
        a = cal_ks0(xs[i1:i2], ys[i1:i2], 1)[0]  # 凹凸性
        j, l21, l31, pt1 = cal_dnhb0(l1, pt1, xs, ys, a)  # 单调段内合并-循环次数/源ks/后ks/索引s
        l2.append(l21)
        l3.append(l31)
        pt += pt1
    return pt


def cal_ks2(pks, pks2, xs, ys):
    '''单调斜率统计: 整体/段内'''
    l2 = []
    for i in range(len(pks2)-1):
        i1, i2 = pks.index(pks2[i]), pks.index(pks2[i+1])
        # print(i1, i2)
        ks = cal_ks1(pks, i1, i2, xs, ys)  # 整体斜率s
        if len(ks) == 2:
            l2.append(ks[:1])
        else:
            l2.append(ks)
    return l2


def fit_ks3_raw(pks, pks2, xs, ys):
    '''拟合斜率'''
    for i in range(len(pks2)-1):
        i1, i2 = pks.index(pks2[i]), pks.index(pks2[i+1])
        xss = [xs[k] for k in range(pks[i1], pks[i2]+1)]
        yss = [ys[k] for k in range(pks[i1], pks[i2]+1)]
        print(i, cal_ks0(xss, yss, 0)[0])
        a0=0
        if i2-i1>1:
            a0 = cal_ks0(xss, yss, 1)[0]
        for j in range(i1, i2):
            xss = [xs[k] for k in range(pks[j], pks[j+1]+1)]
            yss = [ys[k] for k in range(pks[j], pks[j+1]+1)]
            print(cal_ks0(xss, yss, 0)[0])
            if len(xss) == 2:
                print(j-i1, 'fit error!')
            else:
                a, b = cal_ks0(xss, yss, 1)[:2]
                if a0*a<0:  # 同向
                    print(j-i1, 'fit error!')
                else:
                    print(j-i1, '\n', [round(2*a*x0+b, 2) for x0 in xss])


def ifks0(ks, k0):
    '''抛物拟合ks优化判定0: 自身差<0.03, 逆向>0.1'''
    if abs(max(ks)-min(ks))<0.03:
        return 0
    elif ks[0]*k0<0 and abs(ks[0])>0.1:
        return 0
    elif ks[-1]*k0<0 and abs(ks[-1])>0.1:
        return 0
    else:
        return 1  # 需要拟合


def fit_ks3(pks, pks2, xs, ys, th):
    '''拟合单调段内斜率'''
    iks, rks = [0], []  # 索引序号/概率值——初始化
    for i in range(len(pks2)-1):  # 单调段
        i1, i2 = pks.index(pks2[i]), pks.index(pks2[i+1])
        xss = [xs[k] for k in range(pks[i1], pks[i2]+1)]
        yss = [ys[k] for k in range(pks[i1], pks[i2]+1)]
        if i2-i1>1:  # 多分段拟合
            l2, l3, nss, kss, fg = [], [], [], [], 0
            a0 = cal_ks0(xss, yss, 1)[0]
            for j in range(i1, i2):
                xss = [xs[k] for k in range(pks[j], pks[j+1]+1)]
                yss = [ys[k] for k in range(pks[j], pks[j+1]+1)]
                k0 = cal_ks0(xss, yss, 0)[0]
                l4, ns0, ks0 = [], xss, []
                if len(xss)>2 and abs(k0)>=0.03:
                    a, b = cal_ks0(xss, yss, 1)[:2]
                    if a0*a>0:  # 凹凸性
                        ks = [round(2*a*x0+b, 3) for x0 in xss]
                        if ifks0(ks, k0):
                            l4, ks0, fg = [ks[0], ks[-1]], ks, 1
                else:
                    if k0>0:
                        k0 = max(0.02, k0)
                    else:
                        k0 = min(-0.02, k0)
                l3.append([k0, k0])
                l2.append(l4)
                nss.append(ns0)
                kss.append(ks0)
            if fg:  # 段内拟合优化
                # print(i, 'now\n', kss)
                for j in range(10):
                    l3, l2 = fit_ks4(l3, l2, nss, kss, th)
                    # print(j, kss)
                    if sum([len(l20) for l20 in l2])==0:
                        break
                # print(kss)
            for j in range(len(l3)):  # 统计
                if l3[j][0] == l3[j][1]:
                    iks.append(pks[j+i1+1])
                    if l3[j][0]>0:
                        # print('now')
                        rks.append(max(0.02, l3[j][0]))
                    else:
                        rks.append(min(-0.02, l3[j][0]))
                else:
                    iks += list(range(pks[j+i1]+1, pks[j+i1+1]+1))
                    rks += kss[j][1:]
            # print(i, rks)
        else:  # 单段拟合
            k00 = yss[-1]-yss[0]  # 阴阳性
            k0 = min(1, max(-1, cal_ks0(xss, yss, 0)[0]))  # 整体斜率
            if k00*k0<=0:  # 阴阳相反-固定概率
                if k00>0:
                    k0 = 0.02
                else:
                    k0 = -0.02
                ks = []
            elif len(xss)<3:  # 2点直线
                ks = []
            else:
                a, b = cal_ks0(xss, yss, 1)[:2]
                ks = [round(2*a*x0+b, 3) for x0 in xss]
                if ifks0(ks, k0):
                    ks = fit_ks5(k0, xss, ks, th)
                else:
                    ks = []
            if ks:
                iks += list(range(pks[i1]+1, pks[i2]+1))
                rks += ks[1:]
            else:
                iks.append(pks[i2])
                if k0>0:
                    rks.append(max(0.02, k0))
                else:
                    rks.append(min(-0.02, k0))
        # print(i, k0, ks)
    # 综合最终索引和概率
    pss = [round(100*(1+k0)/2) for k0 in rks]
    # print(pss)
    # for i in range(len(pss)):
    #     if pss[i] == 50:
    #         pss[i] = 51
    idx, yss = [iks[0]], []
    for i in range(len(pss)-1):  # 最终概率合并
        if pss[i+1] != pss[i]:
            idx.append(iks[i+1])
            yss.append(pss[i])
        if i == len(pss)-2:
            idx.append(iks[i+2])
            yss.append(pss[i+1])
    # print(yss)
    idx = sorted(set(idx))
    # print(idx)
    # print(yss)
    # iks1 = iks.copy()  # 历史版本
    # xss1 = [xs[i] for i in iks1]
    # yss1 = [pss[0]]+pss  # <mt, =p0——补起点
    # xss1.reverse()
    # yss1.reverse()
    # iks1.reverse
    # xss, yss, iks = [], [], []
    # for i in range(len(yss1)):
    #     if i == 0 or i == len(yss1)-1:
    #         yss.append(yss1[i])
    #         xss.append(xss1[i])
    #         iks.append(iks1[i])
    #     elif yss1[i] != yss1[i-1]:
    #         yss.append(yss1[i])
    #         xss.append(xss1[i])
    #         iks.append(iks1[i])
    # xss.reverse()
    # yss.reverse()
    # iks.reverse()
    # plt.scatter(xss, yss, c='k', s=5)
    # plt.plot(xss, yss, c='k', linewidth=0.5)
    # plt.plot([xss[0], xss[-1]], [50, 50], c='k', alpha=0.1, linewidth=1)
    # # plt.show()
    # plt.savefig('./tst2.png')  # 保存结果趋势
    # plt.close()
    return idx, yss


def cg_ks0(l30, l20, i, nss, kss, th, md=1):
    '''更改ks0: md1从大到小
    超出<0.03时保留;
    不超出<0.03时, 前一k0确定并替换其ks'''
    if l20[i] == []:  # 跳过
        return l30, l20
    l3, l2 = l30.copy(), l20.copy()
    k1, k2 = l2[i]
    if md:  # 递减
        if i == 0:
            if l3[i][1]<0:
                if k2>l3[i+1][0]+0.03 and k1<0.03:  # 不超出
                    l3[i], l2[i] = [min(-0.02, k1), k2], []
                    kss[i] = [min(-0.02, k0) for k0 in kss[i]]
                elif k2>l3[i+1][0]-0.03 and k1<0.03:  # 0.03以内
                    l3[i], l2[i] = [min(-0.02, k1), max(k2, l3[i+1][0])], []
                    kss[i] = [min(-0.02, max(k0, l3[i+1][0])) for k0 in kss[i]]
                    l2[i+1], kss[i+1] = [], []  # 确定前一k0
                else:  # 0.03以外
                    if l3[i][1]-l3[i+1][0]<0.1:  # k0差距<0.1
                        l2[i], kss[i] = [], []
                    elif max(0,k1)+max(0,l3[i+1][0]-k2)>0.15:  # ks与两端k0差和>0.15
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]>0])
                        n1 = nss[i][ik]-nss[i][0]
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]<l3[i+1][0]])
                        n1 += nss[i][-1]-nss[i][len(nss[i])-1-ik]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:  # 超出数量满足两种比例条件
                            l3[i], l2[i] = [min(-0.02, k1), max(k2, l3[i+1][0])], []
                            kss[i] = [min(-0.02, max(k0, l3[i+1][0])) for k0 in kss[i]]
                            l2[i+1], kss[i+1] = [], []
            else:
                if k2>l3[i+1][0]+0.03:
                    l3[i], l2[i] = [min(1, k1), k2], []
                    kss[i] = [min(1, k0) for k0 in kss[i]]
                elif k2>l3[i+1][0]-0.03:
                    l3[i], l2[i] = [min(1, k1), max(k2, l3[i+1][0])], []
                    kss[i] = [min(1, max(k0, l3[i+1][0])) for k0 in kss[i]]
                    l2[i+1], kss[i+1] = [], []
                else:
                    if l3[i][1]-l3[i+1][0]<0.1:
                        l2[i], kss[i] = [], []
                    elif l3[i+1][0]-k2>0.15:
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]<l3[i+1][0]])
                        n1 = nss[i][-1]-nss[i][len(nss[i])-1-ik]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:
                            l3[i], l2[i] = [min(1, k1), max(k2, l3[i+1][0])], []
                            kss[i] = [min(1, max(k0, l3[i+1][0])) for k0 in kss[i]]
                            l2[i+1], kss[i+1] = [], []
        elif i == len(l2)-1:
            if l3[i][1]<0:
                if k1<l3[i-1][1]-0.03:
                    l3[i], l2[i] = [k1, max(-1, k2)], []
                    kss[i] = [max(-1, k0) for k0 in kss[i]]
                elif k1<l3[i-1][1]+0.03:
                    l3[i], l2[i] = [min(l3[i-1][1], k1), max(k2, -1)], []
                    kss[i] = [max(-1, min(k0, l3[i-1][1])) for k0 in kss[i]]
                    l2[i-1], kss[i-1] = [], []
                else:
                    if l3[i-1][1]-l3[i][0]<0.1:
                        l2[i], kss[i] = [], []
                    elif k1-l3[i-1][1]>0.15:
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]>l3[i-1][1]])
                        n1 = nss[i][ik]-nss[i][0]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:
                            l3[i], l2[i] = [min(l3[i-1][1], k1), max(k2, -1)], []
                            kss[i] = [max(-1, min(k0, l3[i-1][1])) for k0 in kss[i]]
                            l2[i-1], kss[i-1] = [], []
            else:
                if k1<l3[i-1][1]-0.03 and k2>-0.03:
                    l3[i], l2[i] = [k1, max(0.02, k2)], []
                    kss[i] = [max(0.02, k0) for k0 in kss[i]]
                elif k1<l3[i-1][1]+0.03 and k2>-0.03:
                    l3[i], l2[i] = [min(l3[i-1][1], k1), max(k2, 0.02)], []
                    kss[i] = [max(0.02, min(k0, l3[i-1][1])) for k0 in kss[i]]
                    l2[i-1], kss[i-1] = [], []
                else:
                    if min(l3[i-1][1]-l3[i][0],l3[i][0])<0.1:
                        l2[i], kss[i] = [], []
                    elif max(0,-k2)+max(0,k1-l3[i-1][1])>0.15:
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]<0])
                        n1 = nss[i][-1]-nss[i][len(nss[i])-1-ik]
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]>l3[i-1][1]])
                        n1 += nss[i][ik]-nss[i][0]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:
                            l3[i], l2[i] = [min(l3[i-1][1], k1), max(k2, -1)], []
                            kss[i] = [max(-1, min(k0, l3[i-1][1])) for k0 in kss[i]]
                            l2[i-1], kss[i-1] = [], []
        else:
            # if l3[i][1]<0:
            if k1<l3[i-1][1]-0.03 and k2>l3[i+1][0]+0.03:
                l3[i], l2[i] = [k1, k2], []
            elif k1<l3[i-1][1]+0.03 and k2>l3[i+1][0]-0.03:
                l3[i], l2[i] = [min(l3[i-1][1], k1), max(l3[i+1][0], k2)], []
                kss[i] = [min(l3[i-1][1], max(l3[i+1][0], k0)) for k0 in kss[i]]
                if k1>=l3[i-1][1]-0.03:
                    l2[i-1], kss[i-1] = [], []
                if k2<=l3[i+1][0]+0.03:
                    l2[i+1], kss[i+1] = [], []
            else:
                if l3[i][1]-l3[i+1][0]<0.1 or l3[i-1][1]-l3[i][0]<0.1:
                    l2[i], kss[i] = [], []
                elif max(0, l3[i+1][0]-k2)+max(0, k1-l3[i-1][1])>0.15:
                    l2[i], kss[i] = [], []
                else:
                    ik = len([j for j in range(len(kss[i])) if kss[i][j]>l3[i-1][1]])
                    n1 = nss[i][ik]-nss[i][0]
                    ik = len([j for j in range(len(kss[i])) if kss[i][j]<l3[i+1][0]])
                    n1 += nss[i][-1]-nss[i][len(nss[i])-1-ik]
                    if n1>=len(nss[i])/5 or n1>=th:
                        l2[i], kss[i] = [], []
                    else:
                        l3[i], l2[i] = [min(l3[i-1][1], k1), max(k2, l3[i+1][0])], []
                        kss[i] = [min(l3[i-1][1], max(l3[i+1][0], k0)) for k0 in kss[i]]
                        if k1>=l3[i-1][1]-0.03:
                            l2[i-1], kss[i-1] = [], []
                        if k2<=l3[i+1][0]+0.03:
                            l2[i+1], kss[i+1] = [], []
    else:  # 递增
        if i == 0:
            # print('now1')
            if l3[i][1]>0:
                if k2<l3[i+1][0]-0.03 and k1>-0.03:  # 不超出
                    l3[i], l2[i] = [max(0.02, k1), k2], []
                    kss[i] = [max(0.02, k0) for k0 in kss[i]]
                elif k2<l3[i+1][0]+0.03 and k1>-0.03:  # 0.03以内
                    l3[i], l2[i] = [max(0.02, k1), min(k2, l3[i+1][0])], []
                    kss[i] = [max(0.02, min(k0, l3[i+1][0])) for k0 in kss[i]]
                    l2[i+1], kss[i+1] = [], []  # 确定前一k0
                else:  # 0.03以外
                    if l3[i+1][0]-l3[i][1]<0.1 or l3[i][0]-l3[i-1][1]<0.1:  # k0差距<0.1
                        l2[i], kss[i] = [], []
                    elif max(0,-k1)+max(0, k2-l3[i+1][0])>0.15:  # ks与两端k0差和>0.15
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]<0])
                        n1 = nss[i][ik]-nss[i][0]
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]>l3[i+1][0]])
                        n1 += nss[i][-1]-nss[i][len(nss[i])-1-ik]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:  # 超出数量满足两种比例条件
                            l3[i], l2[i] = [max(0.02, k1), min(k2, l3[i+1][0])], []
                            kss[i] = [max(0.02, min(k0, l3[i+1][0])) for k0 in kss[i]]
                            l2[i+1], kss[i+1] = [], []
            else:
                if k2<l3[i+1][0]-0.03:
                    l3[i], l2[i] = [max(-1, k1), k2], []
                    kss[i] = [max(-1, k0) for k0 in kss[i]]
                elif k2<l3[i+1][0]+0.03:
                    l3[i], l2[i] = [max(-1, k1), min(k2, l3[i+1][0])], []
                    kss[i] = [max(-1, min(k0, l3[i+1][0])) for k0 in kss[i]]
                    l2[i+1], kss[i+1] = [], []
                else:
                    if l3[i+1][0]-l3[i][1]<0.1:
                        l2[i], kss[i] = [], []
                    elif k2-l3[i+1][0]>0.15:
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]>l3[i+1][0]])
                        n1 = nss[i][-1]-nss[i][len(nss[i])-1-ik]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:
                            l3[i], l2[i] = [max(-1, k1), min(k2, l3[i+1][0])], []
                            kss[i] = [max(-1, min(k0, l3[i+1][0])) for k0 in kss[i]]
                            l2[i+1], kss[i+1] = [], []
        elif i == len(l2)-1:
            if l3[i][1]<0:
                if k1>l3[i-1][1]+0.03 and k2<0.03:
                    l3[i], l2[i] = [k1, min(-0.02, k2)], []
                    kss[i] = [min(-0.02, k0) for k0 in kss[i]]
                elif k1>l3[i-1][1]-0.03 and k2<0.03:
                    l3[i], l2[i] = [max(l3[i-1][1], k1), min(k2, -0.02)], []
                    kss[i] = [min(-0.02, max(k0, l3[i-1][1])) for k0 in kss[i]]
                    l2[i-1], kss[i-1] = [], []
                else:
                    if l3[i][0]-l3[i-1][1]<0.1 or -l3[i][1]<0.1:
                        l2[i], kss[i] = [], []
                    elif max(0, l3[i-1][1]-k1)+max(0, k2)>0.15:
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]<l3[i-1][1]])
                        n1 = nss[i][ik]-nss[i][0]
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]>0])
                        n1 += nss[i][-1]-nss[i][len(nss[i])-1-ik]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:
                            l3[i], l2[i] = [max(l3[i-1][1], k1), min(k2, -0.02)], []
                            kss[i] = [min(-0.02, max(k0, l3[i-1][1])) for k0 in kss[i]]
                            l2[i-1], kss[i-1] = [], []
            else:
                if k1>l3[i-1][1]+0.03:
                    # print('now2')
                    l3[i], l2[i] = [k1, min(1, k2)], []
                    kss[i] = [max(0.02, min(1, k0)) for k0 in kss[i]]
                elif k1>l3[i-1][1]-0.03:
                    l3[i], l2[i] = [max(l3[i-1][1], k1), min(k2, 1)], []
                    kss[i] = [min(1, max(k0, l3[i-1][1])) for k0 in kss[i]]
                    l2[i-1], kss[i-1] = [], []
                else:
                    if l3[i][0]-l3[i-1][1]<0.1:
                        l2[i], kss[i] = [], []
                    elif l3[i-1][1]-k1>0.15:
                        l2[i], kss[i] = [], []
                    else:
                        ik = len([j for j in range(len(kss[i])) if kss[i][j]<l3[i-1][1]])
                        n1 = nss[i][-1]-nss[i][len(nss[i])-1-ik]
                        if n1>=len(nss[i])/5 or n1>=th:
                            l2[i], kss[i] = [], []
                        else:
                            l3[i], l2[i] = [max(l3[i-1][1], k1), min(k2, 1)], []
                            kss[i] = [min(1, max(k0, l3[i-1][1])) for k0 in kss[i]]
                            l2[i-1], kss[i-1] = [], []
        else:
            # if l3[i][1]<0:
            if k1>l3[i-1][1]+0.03 and k2<l3[i+1][0]-0.03:
                l3[i], l2[i] = [k1, k2], []
            elif k1>l3[i-1][1]-0.03 and k2<l3[i+1][0]+0.03:
                # print('now3')
                l3[i], l2[i] = [max(l3[i-1][1], k1), min(l3[i+1][0], k2)], []
                kss[i] = [max(l3[i-1][1], min(l3[i+1][0], k0)) for k0 in kss[i]]
                if k1<=l3[i-1][1]+0.03:
                    l2[i-1], kss[i-1] = [], []
                if k2>=l3[i+1][0]-0.03:
                    l2[i+1], kss[i+1] = [], []
            else:
                if l3[i+1][0]-l3[i][1]<0.1 or l3[i][0]-l3[i-1][1]<0.1:
                    l2[i], kss[i] = [], []
                elif max(0, k2-l3[i+1][0])+max(0, l3[i-1][1]-k1)>0.15:
                    l2[i], kss[i] = [], []
                else:
                    ik = len([j for j in range(len(kss[i])) if kss[i][j]<l3[i-1][1]])
                    n1 = nss[i][ik]-nss[i][0]
                    ik = len([j for j in range(len(kss[i])) if kss[i][j]>l3[i+1][0]])
                    n1 += nss[i][-1]-nss[i][len(nss[i])-1-ik]
                    if n1>=len(nss[i])/5 or n1>=th:
                        l2[i], kss[i] = [], []
                    else:
                        l3[i], l2[i] = [max(l3[i-1][1], k1), min(k2, l3[i+1][0])], []
                        kss[i] = [max(l3[i-1][1], min(l3[i+1][0], k0)) for k0 in kss[i]]
                        if k1<=l3[i-1][1]+0.03:
                            l2[i-1], kss[i-1] = [], []
                        if k2>=l3[i+1][0]-0.03:
                            l2[i+1], kss[i+1] = [], []
    return l3, l2


def fit_ks4(l3, l2, nss, kss, th):
    '''段内拟合匹配'''
    if l3[0][0] > l3[-1][1]:
        if l3[0][0]>0:
            for i in range(len(l2)):
                # print('i', i, l2[i])
                l30, l20 = cg_ks0(l3, l2, i, nss, kss, th, 1)
                if l2 != l20:
                    l3, l2 = l30, l20
                    break
        else:
            for j in range(len(l2)):
                # print('i', j, l2[len(l2)-1-j])
                l30, l20 = cg_ks0(l3, l2, len(l2)-1-j, nss, kss, th, 1)
                if l2 != l20:
                    l3, l2 = l30, l20
                    break
    else:
        if l3[0][0]>0:
            for j in range(len(l2)):
                
                # if j ==1:
                #     print(kss[len(l2)-1-j])
                l30, l20 = cg_ks0(l3, l2, len(l2)-1-j, nss, kss, th, 0)
                # if j==1:
                #     print(kss[len(l2)-1-j])
                if l2 != l20:
                    l3, l2 = l30, l20
                    break
        else:
            for i in range(len(l2)):
                # print('i', i, l2[i])
                l30, l20 = cg_ks0(l3, l2, i, nss, kss, th, 0)
                if l2 != l20:
                    l3, l2 = l30, l20
                    break
    return l3, l2


def fit_ks5(k0, xss, ks, th):
    '''单调单段拟合:'''
    if ks[0]*ks[-1]>0:
        if ks[0]>0:
            ks = [min(1, max(0.02, k1)) for k1 in ks]
        else:
            ks = [min(-0.02, max(-1, k1)) for k1 in ks]
    elif k0*ks[0]<=0:
        if abs(ks[0])<0.03:
            if k0>0:
                ks = [max(0.02, min(1, k1)) for k1 in ks]
            else:
                ks = [min(-0.02, max(-1, k1)) for k1 in ks]
        else:
            if k0>0:
                ik = len([k1 for k1 in ks if k1<0])
                n1 = xss[ik]-xss[0]
                if n1>=len(xss) or n1>=th:
                    ks = []
                else:
                    ks = [max(0.02, min(1, k1)) for k1 in ks]
            else:
                ik = len([k1 for k1 in ks if k1>0])
                n1 = xss[ik]-xss[0]
                if n1>=len(xss) or n1>=th:
                    ks = []
                else:
                    ks = [min(-0.02, max(-1, k1)) for k1 in ks]
    else:
        if abs(ks[-1])<0.03:
            if k0>0:
                ks = [max(0.02, min(1, k1)) for k1 in ks]
            else:
                ks = [min(-0.02, max(-1, k1)) for k1 in ks]
        else:
            if k0>0:
                ik = len([k1 for k1 in ks if k1<0])
                n1 = xss[-1]-xss[len(xss)-1-ik]
                if n1>=len(xss) or n1>=th:
                    ks = []
                else:
                    ks = [max(0.02, min(1, k1)) for k1 in ks]
            else:
                ik = len([k1 for k1 in ks if k1>0])
                n1 = xss[-1]-xss[len(xss)-1-ik]
                if n1>=len(xss) or n1>=th:
                    ks = []
                else:
                    ks = [min(-0.02, max(-1, k1)) for k1 in ks]
    # print(ks)
    return ks


def kmeans0(ys):
    '''K-means聚类可视化: 几簇, 肘部法则, WCSS下降速率骤减的K值'''
    data1 = np.array(ys)  # 替换成你的数据集
    # 将数据转换为二维数组
    data = data1.reshape(-1, 1)
    # 尝试不同的K值，并计算每个K值的WCSS kmeans.cluster_centers_
    kmeans = KMeans(n_clusters=1)
    kmeans.fit(data)
    print(max(ys), min(ys), kmeans.cluster_centers_)
    print(np.mean(ys))
    a = trim_mean(data1, 0.02, axis=0)
    print(a)
    # wcss = []
    # K_range = range(1, 11)  # 假设我们尝试从1到10个簇
    # for k in K_range:
    #     kmeans = KMeans(n_clusters=k)
    #     kmeans.fit(data)
    #     wcss.append(kmeans.inertia_)  # WCSS
    # # 绘制WCSS与K值的关系图
    # plt.plot(K_range, wcss, 'bo-')
    # plt.show()
    # plt.close()


def sta_list_el0(mts1, rt):
    '''统计列表的元素: 按rt比例去除两端值'''
    l1 = sorted(mts1)
    n = round(len(l1)*rt)
    return l1[n:len(l1)-n]


def sqop_dsta0(mts1, mts2):
    '''描述性统计量: 截断均值、中位数、标准差'''
    l1, l2 = sta_list_el0(mts1, 0.02), sta_list_el0(mts2, 0.02)
    lc = max(l1)-min(l1)
    ms1, ms2 = np.mean(l1), np.mean(l2)  # 均值
    zws1, zws2 = l1[round(len(l1)/2-1)], l2[round(len(l2)/2-1)]  # 中位数
    bzc1 = math.sqrt(sum([(x-ms1)**2 for x in l1])/len(l1))
    bzc2 = math.sqrt(sum([(x-ms2)**2 for x in l2])/len(l2))
    return round(abs(ms1-ms2)/lc,3), round(abs(zws1-zws2)/lc,3), round(abs(bzc1-bzc2)/lc,3)


def sqop_getls0(l00, i):
    '''构建差异指标的排序数列: uti/dis/dat/all/spearman'''
    ls = []
    ls.append(round((l00[1][i]+l00[2][i])/2,3))
    ls.append(round((l00[3][i]+l00[4][i])/2,3))
    ls.append(round((l00[5][i]+l00[6][i])/2,3))
    ls.append(round(sum(ls[-3:])/3,3))
    ls.append(l00[7][i])
    return ls


def sqop_sx0(l):
    '''多重再筛选: 3均值<0.04, spearman<0.3, 均值s<0.07'''
    fg = 0
    if l[1]<=0.07 and l[2]<=0.07 and l[3]<=0.07 and l[4]<0.04 and l[5]<0.3:
        fg = 1
    return fg


def sqop_draw0(mts1, mts2, mts3, d1, item):
    '''squid_opm指标的数值分布曲线'''
    d2, d3 = '%s/sqsq/distrib_mts' % d1, '%s/sqop/distrib_mts' % d1
    new_folder([d2, d3])
    l1 = mts1[0]+mts1[1]
    l2 = mts2[0]+mts2[1]
    l3 = mts3[0]+mts3[1]
    xs = list(range(len(l1)))
    # lis1 = [[i, l1[i], l2[i]] for i in range(len(l1))]
    # data = np.array(lis1)
    # idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
    # ys1 = [data[i, 1] for i in idex]
    # ys2 = [data[i, 2] for i in idex]
    # plt.rcParams['savefig.dpi'] = 1024
    # plt.plot(xs, ys1, c='k', linewidth=0.5)
    # plt.plot(xs, ys2, c='r', linewidth=0.5)
    # plt.savefig('%s/%s.png' % (d2, item))
    # plt.close()
    # lis1 = [[i, l2[i], l3[i]] for i in range(len(l2))]
    # data = np.array(lis1)
    # idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
    # ys1 = [data[i, 1] for i in idex]
    # ys2 = [data[i, 2] for i in idex]
    # plt.rcParams['savefig.dpi'] = 1024
    # plt.plot(xs, ys1, c='k', linewidth=0.5)
    # plt.plot(xs, ys2, c='r', linewidth=0.5)
    # plt.savefig('%s/%s.png' % (d3, item))
    # plt.close()
    # 计算误差
    lis1 = [abs(l2[i]-l3[i]) for i in range(len(l2))]
    kmeans0(lis1)


def sqop_draw1(xyss1, xyss2, xyss3, na, d1, item):
    '''squid_opm指标的数量变化分布曲线'''
    d2, d3 = '%s/sqsq/distrib_nms' % d1, '%s/sqop/distrib_nms' % d1
    # d2, d3 = '%s/sqsq/distrib_mtnm' % d1, '%s/sqop/distrib_mtnm' % d1
    new_folder([d2, d3])
    xs1 = [0] + [x0[3] for x0 in xyss1] + [2*na]
    xs2 = [0] + [x0[3] for x0 in xyss2] + [2*na]
    xs3 = [0] + [x0[3] for x0 in xyss3] + [2*na]
    # lc1 = (xyss1[-1][0]-xyss1[0][0])/50
    # lc2 = (xyss2[-1][0]-xyss2[0][0])/50
    # lc3 = (xyss3[-1][0]-xyss3[0][0])/50
    # xs1 = [xyss1[0][0]-lc1] + [x0[0] for x0 in xyss1] + [xyss1[-1][0]+lc1]
    # xs2 = [xyss2[0][0]-lc2] + [x0[0] for x0 in xyss2] + [xyss2[-1][0]+lc2]
    # xs3 = [xyss3[0][0]-lc3] + [x0[0] for x0 in xyss3] + [xyss3[-1][0]+lc3]
    ys1 = [0] + [x0[4] for x0 in xyss1] + [0]
    ys2 = [0] + [x0[4] for x0 in xyss2] + [0]
    ys3 = [0] + [x0[4] for x0 in xyss3] + [0]
    fig, ax = plt.subplots()
    ax.set_aspect('equal')
    plt.rcParams['savefig.dpi'] = 1024
    plt.plot(xs1, ys1, c='k', linewidth=0.5)
    plt.plot(xs2, ys2, c='r', linewidth=0.5)
    plt.savefig('%s/%s.png' % (d2, item))
    plt.close()
    fig, ax = plt.subplots()
    ax.set_aspect('equal')
    plt.rcParams['savefig.dpi'] = 1024
    plt.plot(xs2, ys2, c='k', linewidth=0.5)
    plt.plot(xs3, ys3, c='r', linewidth=0.5)
    plt.savefig('%s/%s.png' % (d3, item))
    plt.close()


def sqop_rks0(r1, r2):
    '''squid_opm指标的分级节点合并: 合并指标集+-, 重构分级结果1/2'''
    rk1, rk2 = [], []
    mt1 = sorted(set(r1[0]+r2[0]))
    if len(mt1) == 1:
        mt1 = [mt1[0]-abs(mt1[0])/50, mt1[0], mt1[0]+abs(mt1[0])/50]
    else:
        lc = (max(mt1)-min(mt1))/50
        mt1.insert(0, mt1[0]-lc)
        mt1.append(mt1[-1]+lc)
    j1, j2 = 0, 0
    for i in range(1, len(mt1)-1):
        for j in range(j1, len(r1[0])):
            if r1[0][j]>=mt1[i]:
                j1 = j
                break
            else:
                j1 = len(r1[0])
        rk1.append(r1[1][j1])
        for j in range(j2, len(r2[0])):
            if r2[0][j]>=mt1[i]:
                j2 = j
                break
            else:
                j2 = len(r2[0])
        rk2.append(r2[1][j2])
    rk1.append(r1[1][-1])
    rk2.append(r2[1][-1])
    return mt1, rk1, rk2


def sqop_draw2(l1, l2, l3, d1, mtnms):
    '''squid_opm指标的分级变化曲线'''
    d2, d3 = '%s/sqsq/distrib_rks' % d1, '%s/sqop/distrib_rks' % d1
    new_folder([d2, d3])
    ls1, ls2 = [], []
    for i in range(len(mtnms)):
        item = mtnms[i]
        print(i+1, item)
        mt1, rk1, rk2 = sqop_rks0(l1[i], l2[i])
        # plt.rcParams['savefig.dpi'] = 1024
        # plt.plot([mt1[0], mt1[-1]], [50, 50], c='c', linewidth=0.5)
        # for j in range(len(mt1)-1):
        #     xs = [mt1[j], mt1[j+1], mt1[j+1]]
        #     ys1 = [rk1[j], rk1[j], rk1[min(j+1, len(rk1)-1)]]
        #     ys2 = [rk2[j], rk2[j], rk2[min(j+1, len(rk1)-1)]]
        #     plt.plot(xs, ys1, c='k', linewidth=0.5)
        #     plt.plot(xs, ys2, c='r', linewidth=0.5)
        # plt.savefig('%s/%s.png' % (d2, item))
        # plt.close()
        mt2, rk3, rk4 = sqop_rks0(l2[i], l3[i])
        # plt.rcParams['savefig.dpi'] = 1024
        # plt.plot([mt2[0], mt2[-1]], [50, 50], c='c', linewidth=0.5)
        # for j in range(len(mt2)-1):
        #     xs = [mt2[j], mt2[j+1], mt2[j+1]]
        #     ys1 = [rk3[j], rk3[j], rk3[min(j+1, len(rk3)-1)]]
        #     ys2 = [rk4[j], rk4[j], rk4[min(j+1, len(rk3)-1)]]
        #     plt.plot(xs, ys1, c='k', linewidth=0.5)
        #     plt.plot(xs, ys2, c='r', linewidth=0.5)
        # plt.savefig('%s/%s.png' % (d3, item))
        # plt.close()
        # 分级误差均值
        eab1 = sum([abs(rk1[j]-rk2[j])*(mt1[j+1]-mt1[j]) for j in range(len(rk1))])/(mt1[-1]-mt1[0])
        eab2 = sum([abs(rk3[j]-rk4[j])*(mt2[j+1]-mt2[j]) for j in range(len(rk3))])/(mt2[-1]-mt2[0])
        ls1.append([i, round(eab1/100,3), round(eab2/100,3)])
        lis1, lis2 = [], []
        for j in range(len(rk1)):
            lc = abs(rk1[j]-rk2[j])
            if (rk1[j]-50)*(rk2[j]-50)<0:
                lc = 50
            lis1.append(lc*(mt1[j+1]-mt1[j])/(mt1[-1]-mt1[0]))
        for j in range(len(rk3)):
            lc = abs(rk3[j]-rk4[j])
            if (rk3[j]-50)*(rk4[j]-50)<0:
                lc = 50
            lis2.append(lc*(mt2[j+1]-mt2[j])/(mt2[-1]-mt2[0]))
        ls2.append([i, round(sum(lis1)/100,3), round(sum(lis2)/100,3)])
    return ls1, ls2


def cal_trim_er0(l20, l30):
    '''计算截断误差均值'''
    l2, l3 = l20.copy(), l30.copy()
    if type(l20) != list:
        l2, l3 = l2.tolist(), l3.tolist()
    lis1 = [abs(l2[i]-l3[i]) for i in range(len(l2))]
    luocha2 = max(l2)-min(l2)
    data1 = np.array(lis1)
    fem2 = trim_mean(data1, 0.02, axis=0) / luocha2
    return fem2


def sqop_draw3(data1, data2, d1, item):
    '''多项式拟合'''
    x = np.array(data1)  # 替换成你的x数据
    y = np.array(data2)  # 替换成你的y数据
    # 进行线性拟合搜索-最优拟合次数
    l1 = []
    for i in range(1, 4):
        z = np.polyfit(x, y, i)  # 1代表线性
        p = np.poly1d(z)
        l1.append(cal_trim_er0(x, p(x)))
    j0 = l1.index(min(l1))+1
    z = np.polyfit(x, y, j0)  # 1代表线性
    p = np.poly1d(z)
    # xs = list(range(len(data1)))
    # plt.rcParams['savefig.dpi'] = 1024
    # plt.plot(xs, x, c='c', linewidth=0.5)
    # plt.plot(xs, y, c='k', linewidth=0.5)
    # plt.plot(xs, p(x), c='r', linewidth=0.5)
    # plt.savefig('%s/%s.png' % (d1, item))
    # plt.close()
    y1 = p(x).tolist()
    return y1


def cal_err0(l1, l2, p0=50):
    '''计算诊断误差百分比: l1/l2为0-100的诊断概率集, 返回对应位置阴阳不匹配的百分比'''
    n1 = 0
    for i in range(len(l1)):
        p1, p2 = l1[i], l2[i]
        if p1>=p0 and p2>=p0:
            n1 += 1
        elif p1<p0 and p2<p0:
            n1 += 1
    return round(n1/len(l1),3)


def sqop_scdg0(nm, ik, comb0, ls, ts, f1, i1=0):
    '''i1起始搜索位置: 可选的'''
    cn0, dp = 0, 1000
    l0, l01, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    acc0, rslis = 0, []
    nr0, nr1, ne0, ne1 = len(tr0), len(tr1), len(te0), len(te1)
    if ik == 0:
        ne0, ne1 = nr0+ne0, nr1+ne1
    elif ik == 2:
        nr0, nr1, ne0, ne1 = nr0+ne0, nr1+ne1, nr0+ne0, nr1+ne1
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    if comb0 == []:
        rgls = list(range(1, n1+1))  # 搜索组合数
    else:
        rgls = list(range(n1+1))  # 搜索组合数
    t1 = time.time()
    for i in rgls:  # 最多搜索8个结果
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        fg = 1
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            cnt += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t1 = display_time0(t1, cn, cnt, dp)
            l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
            l02 = [[l01[j][k] for k in comb] for j in range(len(l01))]
            l1 = comb_res3([l00[j] for j in tr0], n01, n00)
            l2 = comb_res3([l00[j] for j in tr1], n01, n00)
            if ik == 2:  # trva_trva
                l1 += comb_res3([l02[j] for j in te0], n01, n00)
                l2 += comb_res3([l02[j] for j in te1], n01, n00)
            l12 = list(set(l1+l2))  # roc曲线上的全部概率
            l12.sort(reverse=True)
            p1 = l12[-1]
            for i3 in range(len(l12)):
                fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l2 if p0 >= l12[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                if sen >= spe:
                    p1 = l12[i3]
                    break
            if ik == 0:  # tr_trva
                l1 += comb_res3([l02[j] for j in te0], n01, n00)
                l2 += comb_res3([l02[j] for j in te1], n01, n00)
            elif ik == 1:  # tr_va
                l1 = comb_res3([l02[j] for j in te0], n01, n00)
                l2 = comb_res3([l02[j] for j in te1], n01, n00)
            fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= p1)
            acc = round((ne0-fp+tp)/(ne0+ne1), 3)
            if acc > acc0:
                j = 0
                for j in range(min(len(rslis), 3)):
                    if acc > rslis[j][0]:
                        break
                rslis.insert(j, [acc, comb, p1])
                print('get one!\n', rslis[:min(len(rslis), 3)])
                fg = 0
                acc0 = rslis[min(len(rslis), 3)-1][0]
        if fg and i > i1:
            break
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    lis1 = []
    for i in range(min(len(rslis), 3)):
        comb = rslis[i][1]
        l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        l1 = comb_res3([[l0[j][k] for k in comb] for j in tr0], n01, n00)
        l2 = comb_res3([[l0[j][k] for k in comb] for j in tr1], n01, n00)
        if ik == 2:  # trva_trva
            l1 += comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
            l2 += comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        l12 = list(set(l1+l2))  # roc曲线上的全部概率
        l12.sort(reverse=True)
        for i3 in range(len(l12)):
            fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= l12[i3])
            sen, spe = tp/nr1, (nr0-fp)/nr0
            if sen >= spe:
                p1 = l12[i3]
                break
        if ik == 0:  # tr_trva
            l1 += comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
            l2 += comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        elif ik == 1:  # tr-VA
            l1 = comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
            l2 = comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
        tp = sum(1 for p0 in l2 if p0 >= p1)
        lis1.append([i, (ne0-fp+tp)/(ne0+ne1), tp/ne1])
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    comb0 = rslis[int(lis1[0][0])][1]
    return comb0


def sqop_scdg1(nm, ik, comb0, ls, ts, f1, i1=0):
    '''i1起始搜索位置: 可选的'''
    cn0, dp = 0, 1000
    l0, l01, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    acc0, rslis = 0, []
    nr0, nr1, ne0, ne1 = len(tr0), len(tr1), len(te0), len(te1)
    nr0, nr1, ne0, ne1 = nr0+ne0, nr1+ne1, nr0+ne0, nr1+ne1
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    if comb0 == []:
        rgls = list(range(1, n1+1))  # 搜索组合数
    else:
        rgls = list(range(n1+1))  # 搜索组合数
    t1 = time.time()
    for i in rgls:  # 最多搜索8个结果
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        fg = 1
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            cnt += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t1 = display_time0(t1, cn, cnt, dp)
            l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
            l02 = [[l01[j][k] for k in comb] for j in range(len(l01))]
            l1 = comb_res3([l00[j] for j in tr0], n01, n00)
            l2 = comb_res3([l00[j] for j in tr1], n01, n00)
            l1 += comb_res3([l02[j] for j in te0], n01, n00)
            l2 += comb_res3([l02[j] for j in te1], n01, n00)
            l12 = list(set(l1+l2))  # roc曲线上的全部概率
            l12.sort(reverse=True)
            p1 = l12[-1]
            for i3 in range(len(l12)):
                fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l2 if p0 >= l12[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                if sen >= spe:
                    p1 = l12[i3]
                    break
            fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= p1)
            acc = round((ne0-fp+tp)/(ne0+ne1), 3)
            if acc > acc0:
                j = 0
                for j in range(min(len(rslis), 3)):
                    if acc > rslis[j][0]:
                        break
                rslis.insert(j, [acc, comb, p1])
                print('get one!\n', rslis[:min(len(rslis), 3)])
                fg = 0
                acc0 = rslis[min(len(rslis), 3)-1][0]
        if fg and i > i1:
            break
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    lis1 = []
    for i in range(min(len(rslis), 3)):
        comb = rslis[i][1]
        l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        l1 = comb_res3([[l0[j][k] for k in comb] for j in tr0], n01, n00)
        l2 = comb_res3([[l0[j][k] for k in comb] for j in tr1], n01, n00)
        l1 += comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
        l2 += comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        l12 = list(set(l1+l2))  # roc曲线上的全部概率
        l12.sort(reverse=True)
        for i3 in range(len(l12)):
            fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= l12[i3])
            sen, spe = tp/nr1, (nr0-fp)/nr0
            if sen >= spe:
                p1 = l12[i3]
                break
        fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
        tp = sum(1 for p0 in l2 if p0 >= p1)
        lis1.append([i, (ne0-fp+tp)/(ne0+ne1), tp/ne1])
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    comb0 = rslis[int(lis1[0][0])][1]
    return comb0


def sqop_scdg2(nm, ik, comb0, ls, ts, f1, i1=0):
    '''i1起始搜索位置: 可选的'''
    cn0, dp = 0, 1000
    l0, l01, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    acc0, rslis = 0, []
    nr0, nr1, ne0, ne1 = len(tr0), len(tr1), len(te0), len(te1)
    if ik == 0:
        ne0, ne1 = nr0+ne0, nr1+ne1
    elif ik == 2:
        nr0, nr1, ne0, ne1 = nr0+ne0, nr1+ne1, nr0+ne0, nr1+ne1
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    if comb0 == []:
        rgls = list(range(max(1, i1), n1+1))  # 每组最多5个
    else:
        rgls = list(range(max(0, i1), n1+1))
    t1 = time.time()
    nm00 = [10, 7, 8]  # 设定个数天花板
    for i in rgls:  # 最多搜索8个结果
        if i >= nm00[ik]:
            break
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        fg = 1
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            cnt += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t1 = display_time0(t1, cn, cnt, dp)
            l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
            l02 = [[l01[j][k] for k in comb] for j in range(len(l01))]
            l1 = comb_res3([l00[j] for j in tr0], n01, n00)
            l2 = comb_res3([l00[j] for j in tr1], n01, n00)
            if ik == 2:  # trva_trva
                l1 += comb_res3([l02[j] for j in te0], n01, n00)
                l2 += comb_res3([l02[j] for j in te1], n01, n00)
            l12 = list(set(l1+l2))  # roc曲线上的全部概率
            l12.sort(reverse=True)
            p1 = l12[-1]
            for i3 in range(len(l12)):
                fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l2 if p0 >= l12[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                if sen >= spe:
                    p1 = l12[i3]
                    break
            if ik == 0:  # tr_trva
                l1 += comb_res3([l02[j] for j in te0], n01, n00)
                l2 += comb_res3([l02[j] for j in te1], n01, n00)
            elif ik == 1:  # tr_va
                l1 = comb_res3([l02[j] for j in te0], n01, n00)
                l2 = comb_res3([l02[j] for j in te1], n01, n00)
            fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= p1)
            acc = round((ne0-fp+tp)/(ne0+ne1), 3)
            if acc > acc0:
                j = 0
                for j in range(min(len(rslis), 3)):
                    if acc > rslis[j][0]:
                        break
                rslis.insert(j, [acc, comb, p1])
                print('get one!\n', rslis[:min(len(rslis), 3)])
                fg = 0
                acc0 = rslis[min(len(rslis), 3)-1][0]
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    lis1 = []
    for i in range(min(len(rslis), 3)):
        comb = rslis[i][1]
        l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        l1 = comb_res3([[l0[j][k] for k in comb] for j in tr0], n01, n00)
        l2 = comb_res3([[l0[j][k] for k in comb] for j in tr1], n01, n00)
        if ik == 2:  # trva_trva
            l1 += comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
            l2 += comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        l12 = list(set(l1+l2))  # roc曲线上的全部概率
        l12.sort(reverse=True)
        for i3 in range(len(l12)):
            fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= l12[i3])
            sen, spe = tp/nr1, (nr0-fp)/nr0
            if sen >= spe:
                p1 = l12[i3]
                break
        if ik == 0:  # tr_trva
            l1 += comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
            l2 += comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        elif ik == 1:  # tr-VA
            l1 = comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
            l2 = comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
        tp = sum(1 for p0 in l2 if p0 >= p1)
        lis1.append([i, (ne0-fp+tp)/(ne0+ne1), tp/ne1])
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    comb0 = rslis[int(lis1[0][0])][1]
    return comb0


def sqop_scdg3(nm, ik, comb0, ls, ts, f1, i1=0):
    '''i1起始搜索位置: 可选的'''
    cn0, dp = 0, 1000
    l0, l01, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    acc0, rslis = 0, []
    nr0, nr1, ne0, ne1 = len(tr0), len(tr1), len(te0), len(te1)
    nr0, nr1, ne0, ne1 = nr0+ne0, nr1+ne1, nr0+ne0, nr1+ne1
    n0, n1, cn = sum(nm[:ik]), nm[ik], 0
    if comb0 == []:
        rgls = list(range(max(1, i1), n1+1))  # 每组最多5个
    else:
        rgls = list(range(max(0, i1), n1+1))
    t1 = time.time()
    nm00 = [10, 7, 8]  # 设定个数天花板
    for i in rgls:  # 最多搜索8个结果
        if i >= nm00[ik]:
            break
        print('---------start combinations(%d, %d)' % (n1, i))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i))).tolist()
        cn = len(combs)
        cnt = 0
        fg = 1
        for comb in combs:
            comb = comb0+[comb1+n0 for comb1 in comb]
            cn0 += 1
            cnt += 1
            if cn0 % dp == 0:
                write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
                t1 = display_time0(t1, cn, cnt, dp)
            l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[j][k] for k in comb] for j in range(len(l0))]
            l02 = [[l01[j][k] for k in comb] for j in range(len(l01))]
            l1 = comb_res3([l00[j] for j in tr0], n01, n00)
            l2 = comb_res3([l00[j] for j in tr1], n01, n00)
            l1 += comb_res3([l02[j] for j in te0], n01, n00)
            l2 += comb_res3([l02[j] for j in te1], n01, n00)
            l12 = list(set(l1+l2))  # roc曲线上的全部概率
            l12.sort(reverse=True)
            p1 = l12[-1]
            for i3 in range(len(l12)):
                fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
                tp = sum(1 for p0 in l2 if p0 >= l12[i3])
                sen, spe = tp/nr1, (nr0-fp)/nr0
                if sen >= spe:
                    p1 = l12[i3]
                    break
            fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= p1)
            acc = round((ne0-fp+tp)/(ne0+ne1), 3)
            if acc > acc0:
                j = 0
                for j in range(min(len(rslis), 3)):
                    if acc > rslis[j][0]:
                        break
                rslis.insert(j, [acc, comb, p1])
                print('get one!\n', rslis[:min(len(rslis), 3)])
                fg = 0
                acc0 = rslis[min(len(rslis), 3)-1][0]
    write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    lis1 = []
    for i in range(min(len(rslis), 3)):
        comb = rslis[i][1]
        l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        l1 = comb_res3([[l0[j][k] for k in comb] for j in tr0], n01, n00)
        l2 = comb_res3([[l0[j][k] for k in comb] for j in tr1], n01, n00)
        l1 += comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
        l2 += comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
        l12 = list(set(l1+l2))  # roc曲线上的全部概率
        l12.sort(reverse=True)
        for i3 in range(len(l12)):
            fp = sum(1 for p0 in l1 if p0 >= l12[i3])  # >=阈值的数量
            tp = sum(1 for p0 in l2 if p0 >= l12[i3])
            sen, spe = tp/nr1, (nr0-fp)/nr0
            if sen >= spe:
                p1 = l12[i3]
                break
        fp = sum(1 for p0 in l1 if p0 >= p1)  # >=阈值的数量
        tp = sum(1 for p0 in l2 if p0 >= p1)
        lis1.append([i, (ne0-fp+tp)/(ne0+ne1), tp/ne1])
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    comb0 = rslis[int(lis1[0][0])][1]
    return comb0


def sqop_revi0(ls, combs, ts, strs):
    '''tr阈值-测试tr/va/trva/te'''
    comb, p1 = combs[1:]
    print(p1)
    l0, l01, l11, l22 = ls
    tr0, tr1, te0, te1 = ts
    l1, l2 = [l11[j] for j in comb], [l22[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
    l10 = comb_res3([[l0[j][k] for k in comb] for j in tr0], n01, n00)
    l11 = comb_res3([[l0[j][k] for k in comb] for j in tr1], n01, n00)
    l20 = comb_res3([[l01[j][k] for k in comb] for j in tr0], n01, n00)
    l21 = comb_res3([[l01[j][k] for k in comb] for j in tr1], n01, n00)
    l30 = comb_res3([[l0[j][k] for k in comb] for j in te0], n01, n00)
    l31 = comb_res3([[l0[j][k] for k in comb] for j in te1], n01, n00)
    l40 = comb_res3([[l01[j][k] for k in comb] for j in te0], n01, n00)
    l41 = comb_res3([[l01[j][k] for k in comb] for j in te1], n01, n00)
    auc1, roc1 = cal_auc0(l10, l11)
    auc2, roc2 = cal_auc0(l20, l21)
    auc3, roc3 = cal_auc0(l30, l31)
    auc4, roc4 = cal_auc0(l40, l41)
    auc12, roc12 = cal_auc0(l10+l20, l11+l21)
    err12 = cal_err0(l10+l11, l20+l21, p0=p1)
    auc34, roc34 = cal_auc0(l30+l40, l31+l41)
    err34 = cal_err0(l30+l31, l40+l41, p0=p1)
    roc1 = [roc1[i] for i in range(len(roc1)) if roc1[i][0] >= p1]
    _, acc1, sen1, spe1 = roc1[0]
    roc2 = [roc2[i] for i in range(len(roc2)) if roc2[i][0] >= p1]
    _, acc2, sen2, spe2 = roc2[0]
    roc3 = [roc3[i] for i in range(len(roc3)) if roc3[i][0] >= p1]
    _, acc3, sen3, spe3 = roc3[0]
    roc4 = [roc4[i] for i in range(len(roc4)) if roc4[i][0] >= p1]
    _, acc4, sen4, spe4 = roc4[0]
    roc12 = [roc12[i] for i in range(len(roc12)) if roc12[i][0] >= p1]
    _, acc12, sen12, spe12 = roc12[0]
    roc34 = [roc34[i] for i in range(len(roc34)) if roc34[i][0] >= p1]
    _, acc34, sen34, spe34 = roc34[0]
    strs += '\n\nsearch_%d: %s, %d, err12-%.3f, err34-%.3f' % (len(comb), str(comb), p1, err12, err34)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Tr' % (auc1, acc1, sen1, spe1)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Tr' % (auc2, acc2, sen2, spe2)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Tr12' % (auc12, acc12, sen12, spe12)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Te' % (auc3, acc3, sen3, spe3)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Te' % (auc4, acc4, sen4, spe4)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Te12' % (auc34, acc34, sen34, spe34)
    return strs


def stat_nums0(l1, l2, ln=[]):
    '''统计阴阳数据的>=数量'''
    l1 = sorted(l1)
    l2 = sorted(l2)
    if not ln:
        ln = sorted(set(l1+l2), reverse=1)
    l11 = [0 for i in range(len(ln))]  # 数量统计>=
    l12 = [0 for i in range(len(ln))]
    n1, n2 = 0, 0
    for i1 in range(len(ln)):  # 逆序
        v0 = ln[i1]
        if v0 in l1:
            n1 = len(l1)-l1.index(v0)
        if v0 in l2:
            n2 = len(l2)-l2.index(v0)
        l11[-1-i1] = n1
        l12[-1-i1] = n2
    return l1, l2, ln, l11, l12


def ranks_spemt0(rs1, rs2, l1, l2, m0, rg=1):
    '''筛选特异性指标: 两端'''
    l1, l2, ln, l11, l12 = stat_nums0(l1, l2)
    rs10, rs20 = [[], [], []], [[], [], []]
    nm1, nm2 = 0, 0
    for i1 in range(len(ln)):  # 逆序
        n1, n2 = len(l1)-l11[-1-i1], len(l2)-l12[-1-i1]  # 第一个<的v0
        if n1 + n2 > 0:
            n12 = n2/(n1+n2)
            if n12 >= rg and rs10[0] == []:
                rs10[0] = [ln[i1]]
                nm1 += n1+n2
            elif n12 <= 1-rg and rs20[0] == []:
                rs20[0] = [ln[i1]]
                nm2 += n1+n2
    for i1 in range(len(ln)):
        n1, n2 = l11[i1], l12[i1]  # 第一个>=的v0
        if n1 + n2 > 0:
            n12 = n2/(n1+n2)
            if n12 >= rg and rs10[2] == []:
                rs10[2] = [ln[-1-i1]]
                nm1 += n1+n2
            elif n12 <= 1-rg and rs20[2] == []:
                rs20[2] = [ln[-1-i1]]
                nm2 += n1+n2
    rs1.append([rs10, nm1/len(l1+l2), m0])
    rs2.append([rs20, nm2/len(l1+l2), m0])
    return rs1, rs2, l11, l12


def add_qj0(l11, ln, l1, l2, m0):
    '''计算分段诊断区间: 前、中间、后'''
    rs1 = [[], [], []]  # 小于), 若干[), 大于等于[
    nm = 0
    i1 = l11.index(0)
    if i1:
        rs1[0].append(ln[i1])
        nm += sum(1 for v in l1+l2 if v < ln[i1])
    l11.reverse()
    i2 = l11.index(0)
    if i2:
        rs1[2].append(ln[len(l11)-i2])
        nm += sum(1 for v in l1+l2 if v >= ln[len(l11)-i2])
    qj = []
    l11.reverse()
    for i in range(i1, len(l11)-i2):
        if len(qj) == 0 and l11[i] == 1:
            qj.append(ln[i])
        elif len(qj) == 1 and l11[i] == 0:
            rs1[1].append(qj+[ln[i]])
            nm += sum(1 for v in l1+l2 if v>=qj[0] and v<ln[i])
            qj = []
    nm = nm/len(l1+l2)
    rs = [rs1, nm, m0]
    return rs


def add_qj2(r1, r2, ln, l1, l2, l3, l4, rg):
    '''计算分段诊断区间tr_trte: 中间'''
    l1, l2, ln, l11, l12 = stat_nums0(l1, l2)
    l3, l4, ln1, l13, l14 = stat_nums0(l1+l3, l2+l4, ln)
    i1, i2, r01 = 0, len(ln), 0  # 索引位置
    if r1[0][2]:
        i2 = ln.index(r1[0][2][0])
    if r1[0][0]:
        i1 = ln.index(r1[0][0][0])
    for i in range(i1, i2-1):
        for j in range(i+1, i2):
            n1 = l11[i]-l11[j]
            n2 = l12[i]-l12[j]
            if n1 + n2 > 0:
                n12 = n2/(n1+n2)
                if n12 >= rg and n1+n2 > r01:
                    n1 = l13[i]-l13[j]
                    n2 = l14[i]-l14[j]
                    n12 = n2/(n1+n2)
                    if n12 >= rg:
                        r01 = n1+n2
                        r1[0][1] = [[ln[-1-i], ln[-1-j]]]
    r1[1] = (r1[1]*(l13[0]+l13[0])+r01) / (l13[0]+l14[0])
    i1, i2, r02 = 0, len(ln), 0
    if r2[0][2]:
        i2 = ln.index(r2[0][2][0])
    if r2[0][0]:
        i1 = ln.index(r2[0][0][0])
    for i in range(i1, i2-1):
        for j in range(i+1, i2):
            n1 = l11[i]-l11[j]
            n2 = l12[i]-l12[j]
            if n1 + n2 > 0:
                n12 = n2/(n1+n2)
                if n12 <= 1-rg and n1+n2 > r02:
                    n1 = l13[i]-l13[j]
                    n2 = l14[i]-l14[j]
                    n12 = n2/(n1+n2)
                    if n12 <= 1-rg:
                        r02 = n1+n2
                        r2[0][1] = [[ln[-1-i], ln[-1-j]]]
    r2[1] = (r2[1]*(l13[0]+l14[0])+r02) / (l13[0]+l14[0])
    return r1, r2


def add_qj1(r1, r2, ln, l11, l12, rg):
    '''计算分段诊断区间: 中间'''
    i1, i2, r01 = 0, len(ln), 0  # 索引位置
    if r1[0][2]:
        i2 = ln.index(r1[0][2][0])
    if r1[0][0]:
        i1 = ln.index(r1[0][0][0])
    for i in range(i1, i2-1):
        for j in range(i+1, i2):
            n1 = l11[i]-l11[j]
            n2 = l12[i]-l12[j]
            if n1 + n2 > 0:
                n12 = n2/(n1+n2)
                if n12 >= rg and n1+n2 > r01:
                    r01 = n1+n2
                    r1[0][1] = [[ln[-1-i], ln[-1-j]]]
    r1[1] = (r1[1]*(l11[0]+l12[0])+r01) / (l11[0]+l12[0])
    i1, i2, r02 = 0, len(ln), 0
    if r2[0][2]:
        i2 = ln.index(r2[0][2][0])
    if r2[0][0]:
        i1 = ln.index(r2[0][0][0])
    for i in range(i1, i2-1):
        for j in range(i+1, i2):
            n1 = l11[i]-l11[j]
            n2 = l12[i]-l12[j]
            if n1 + n2 > 0:
                n12 = n2/(n1+n2)
                if n12 <= 1-rg and n1+n2 > r02:
                    r02 = n1+n2
                    r2[0][1] = [[ln[-1-i], ln[-1-j]]]
    r2[1] = (r2[1]*(l11[0]+l12[0])+r02) / (l11[0]+l12[0])
    return r1, r2


def ranks_spemt3(rs1, rs2, l1, l2, l3, l4, m0, rg=1):
    '''筛选特异性指标tr_trte: 区间'''
    l1 = sorted(l1)
    l2 = sorted(l2)
    ln = sorted(set(l1+l2), reverse=1)
    l3 = sorted(l1+l3)
    l4 = sorted(l2+l4)
    if m0[-2:] == '_q':
        ln.reverse()
        l11 = [0 for i in range(len(ln))]
        l21 = [0 for i in range(len(ln))]
        for i1 in range(len(ln)):  # 逆序
            v0 = ln[i1]
            n1 = sum(1 for v in l1 if v==v0)
            n2 = sum(1 for v in l2 if v==v0)
            if n1 + n2 > 0:
                n12 = n2/(n1+n2)
                if n12 >= rg:
                    n3 = sum(1 for v in l3 if v==v0)
                    n4 = sum(1 for v in l4 if v==v0)
                    n34 = n4/(n3+n4)
                    if n34 >= rg:
                        l11[i1] = 1
                elif n12 <= 1-rg:
                    n3 = sum(1 for v in l3 if v==v0)
                    n4 = sum(1 for v in l4 if v==v0)
                    n34 = n4/(n3+n4)
                    if n34 <= 1-rg:
                        l21[i1] = 1
        rs10 = add_qj0(l11, ln, l1+l3, l2+l4, m0)
        rs20 = add_qj0(l21, ln, l1+l3, l2+l4, m0)
    else:
        rs10, rs20 = ranks_spemt2([], [], l1, l2, l3, l4, m0, rg=rg)
        rs10, rs20 = add_qj2(rs10[0], rs20[0], ln, l1, l2, l3, l4, rg)
    rs1.append(rs10)
    rs2.append(rs20)
    return rs1, rs2


def ranks_spemt1(rs1, rs2, l1, l2, m0, rg=1):
    '''筛选特异性指标: 区间'''
    l1 = sorted(l1)
    l2 = sorted(l2)
    ln = sorted(set(l1+l2), reverse=1)
    if m0[-2:] == '_q':
        ln.reverse()
        l11 = [0 for i in range(len(ln))]
        l21 = [0 for i in range(len(ln))]
        for i1 in range(len(ln)):  # 逆序
            v0 = ln[i1]
            n1 = sum(1 for v in l1 if v==v0)
            n2 = sum(1 for v in l2 if v==v0)
            if n1 + n2 > 0:
                n12 = n2/(n1+n2)
                if n12 >= rg:
                    l11[i1] = 1
                elif n12 <= 1-rg:
                    l21[i1] = 1
        rs10 = add_qj0(l11, ln, l1, l2, m0)
        rs20 = add_qj0(l21, ln, l1, l2, m0)
    else:
        rs10, rs20, l11, l12 = ranks_spemt0([], [], l1, l2, m0, rg=rg)
        rs10, rs20 = add_qj1(rs10[0], rs20[0], ln, l11, l12, rg)
    rs1.append(rs10)
    rs2.append(rs20)
    return rs1, rs2


def ranks_spemt2(rs1, rs2, l1, l2, l3, l4, m0, rg=1):
    '''筛选特异性指标tr_trte: 两端'''
    l1, l2, ln, l11, l12 = stat_nums0(l1, l2)
    l3, l4, ln1, l13, l14 = stat_nums0(l1+l3, l2+l4)
    rs10, rs20 = [[], [], []], [[], [], []]
    nm1, nm2 = 0, 0
    for i1 in range(len(ln)):  # 逆序
        n1, n2 = len(l1)-l11[-1-i1], len(l2)-l12[-1-i1]  # 第一个<的v0
        if n1 + n2 > 0:
            n12 = n2/(n1+n2)
            if n12 >= rg and rs10[0] == []:
                i2 = ln1.index(ln[i1])
                n3, n4 = len(l3)-l13[-1-i2], len(l4)-l14[-1-i2]
                n34 = n4/(n3+n4)
                if n34 >= rg:
                    rs10[0] = [ln[i1]]
                    nm1 += n3+n4
            elif n12 <= 1-rg and rs20[0] == []:
                i2 = ln1.index(ln[i1])
                n3, n4 = len(l3)-l13[-1-i2], len(l4)-l14[-1-i2]
                n34 = n4/(n3+n4)
                if n34 <= 1-rg:
                    rs20[0] = [ln[i1]]
                    nm2 += n3+n4
    for i1 in range(len(ln)):
        n1, n2 = l11[i1], l12[i1]  # 第一个>=的v0
        if n1 + n2 > 0:
            n12 = n2/(n1+n2)
            if n12 >= rg and rs10[2] == []:
                i2 = ln1.index(ln[-1-i1])
                n3, n4 = l13[i2], l14[i2]
                n34 = n4/(n3+n4)
                if n34 >= rg:
                    rs10[2] = [ln[-1-i1]]
                    nm1 += n3+n4
            elif n12 <= 1-rg and rs20[2] == []:
                i2 = ln1.index(ln[-1-i1])
                n3, n4 = l13[i2], l14[i2]
                n34 = n4/(n3+n4)
                if n34 <= 1-rg:
                    rs20[2] = [ln[-1-i1]]
                    nm2 += n3+n4
    rs1.append([rs10, nm1/len(l3+l4), m0])
    rs2.append([rs20, nm2/(len(l3+l4)), m0])
    return rs1, rs2


def cmpr_draw0(ls36, g1, ik=[]):
    '''绘制空间波组图'''
    # 1.创建数据
    M, N = max(get_mai0(ls36)), len(ls36[0])
    x = np.arange(N)
    y0 = x*0  # 0基线
    # 2.绘制
    fig, axs = plt.subplots(6, 6, figsize=(15, 15), facecolor='black', subplot_kw={'facecolor': 'black'})
    for ax in axs.flat:
        ax.set_xlim(0, N-1)
        ax.set_ylim(-M, M)
        ax.set_xticks([])  # 隐藏x轴刻度
        ax.set_yticks([])  # 隐藏y轴刻度
        ax.spines['top'].set_visible(False)    # 隐藏顶部轴线
        ax.spines['right'].set_visible(False)  # 隐藏右侧轴线
        ax.spines['bottom'].set_visible(False)  # 隐藏底部轴线
        ax.spines['left'].set_visible(False)   # 隐藏左侧轴线
    for i in range(6):
        for j in range(6):
            y = np.array(ls36[i*6+j])
            # axs[i, j].plot(x, y0, color='blue', linewidth=2, alpha=0.6)
            for i0 in ik:
                # axs[i, j].scatter(ik, [0, 0], c='r', s=50)
                axs[i, j].scatter(i0, 0, c='r', s=50)
            axs[i, j].plot(x, y0, color='blue', linewidth=2)
            axs[i, j].plot(x, y, color='white', linewidth=2)  # 设置曲线颜色为白色
    plt.tight_layout()
    # plt.show()
    plt.savefig(g1)
    plt.close()


def cmpr_draw0_0(ls36, g1, iks=[]):
    '''绘制空间波组图'''
    # 1.创建数据
    M, N = max(get_mai0(ls36)), len(ls36[0])
    x = np.arange(N)
    y0 = x*0  # 0基线
    # 2.绘制
    plt.rcParams['savefig.dpi'] = 512
    fig, axs = plt.subplots(6, 6, figsize=(15, 15), facecolor='black', subplot_kw={'facecolor': 'black'})
    for ax in axs.flat:
        ax.set_xlim(0, N-1)
        ax.set_ylim(-M, M)
        ax.set_xticks([])  # 隐藏x轴刻度
        ax.set_yticks([])  # 隐藏y轴刻度
        ax.spines['top'].set_visible(False)    # 隐藏顶部轴线
        ax.spines['right'].set_visible(False)  # 隐藏右侧轴线
        ax.spines['bottom'].set_visible(False)  # 隐藏底部轴线
        ax.spines['left'].set_visible(False)   # 隐藏左侧轴线
    for i in range(6):
        for j in range(6):
            y = np.array(ls36[i*6+j])
            # axs[i, j].plot(x, y0, color='blue', linewidth=2, alpha=0.6)
            axs[i, j].plot(x, y0, color='blue', linewidth=2, alpha=0.6)
            axs[i, j].plot(x, y, color='white', linewidth=2, alpha=0.6)  # 设置曲线颜色为白色
            for i0 in iks[i*6+j]:
                # axs[i, j].scatter(ik, [0, 0], c='r', s=50)
                axs[i, j].scatter(i0, 0, c='r', s=10)
    plt.tight_layout()
    # plt.show()
    plt.savefig(g1)
    plt.close()


def cmpr_draw0_1(ls36, g1, l12=[]):
    '''绘制空间波组图'''
    # 1.创建数据
    M, N = max(get_mai0(ls36)), len(ls36[0])
    x = np.arange(N)
    y0 = x*0  # 0基线
    # 2.绘制
    plt.rcParams['savefig.dpi'] = 512
    fig, axs = plt.subplots(6, 6, figsize=(15, 15), facecolor='black', subplot_kw={'facecolor': 'black'})
    for ax in axs.flat:
        ax.set_xlim(0, N-1)
        ax.set_ylim(-M, M)
        ax.set_xticks([])  # 隐藏x轴刻度
        ax.set_yticks([])  # 隐藏y轴刻度
        ax.spines['top'].set_visible(False)    # 隐藏顶部轴线
        ax.spines['right'].set_visible(False)  # 隐藏右侧轴线
        ax.spines['bottom'].set_visible(False)  # 隐藏底部轴线
        ax.spines['left'].set_visible(False)   # 隐藏左侧轴线
    for i in range(6):
        for j in range(6):
            ij = i*6+j
            y = np.array(ls36[ij])
            zh, alp, qs = l12[ij]
            v0, v1 = np.min(y), np.max(y)
            axs[i, j].plot(x, y0, color='blue', linewidth=2, alpha=0.6)
            axs[i, j].plot(x, y, color='white', linewidth=2, alpha=0.6)  # 设置曲线颜色为白色
            for i0 in zh:
                axs[i, j].plot([i0, i0], [v0, v1], color='peru', linewidth=2, alpha=0.5)
            for i0 in alp:
                axs[i, j].scatter(i0, y[i0], c='r', s=10)
            for i0 in qs:
                axs[i, j].scatter(i0, y[i0], c='g', s=10)
    plt.tight_layout()
    # plt.show()
    plt.savefig(g1)
    plt.close()


def cmpr_draw1(ls36, tms, g1):
    '''绘制时间波组图'''
    # 1.创建数据
    M0, M1 = get_mai0(ls36)
    N = len(ls36[0])
    x = np.arange(N)
    # Ny = math.floor(M/4)
    # ytks = np.arange(-Ny, Ny+1)*5
    ytks = np.arange(-math.floor(M1/5), math.floor(M0/5)+1)*5
    # 2.绘制
    clis = np.linspace(0, 1, 38)  # 36 去除首尾
    plt.figure()
    plt.style.use('bmh')
    plt.rcParams['savefig.dpi'] = 512  # 保存像素640*480*dpi/100
    # plt.xticks(tms[:-1], tms[:-1], fontsize=4)  # x刻度
    plt.xticks(tms, tms, fontsize=4)  # x刻度
    plt.yticks(ytks, ytks, fontsize=4)
    for i1 in range(len(ls36)):  # 时间波组图
        c = plt.get_cmap('gist_rainbow')(clis[i1])  # 36
        plt.plot(x, np.array(ls36[i1]), color=c, linewidth=0.5)  # 36
    plt.plot([0, N-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    # plt.show()
    plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    plt.close()


def cmpr_draw2(ls36, l0, g1):
    '''绘制TP段基线: ls36和rms; mi+ma*5/4+rms'''
    # 1.创建数据
    M0, M1 = get_mai0(ls36)
    N = len(ls36[0])
    x = np.arange(N)
    M2 = max(l0)
    ytks = np.arange(-math.floor(M1*5), math.floor(M0*25/4)+1)*0.2
    ys = np.arange(math.floor(M2*5)+1)*0.2
    ytks1 = list(ytks) + list(ys)
    ytks = list(ytks) + list(ys+M0*5/4)
    ytks1 = [round(ytks1[i],1) for i in range(len(ytks1))]
    ytks = [round(ytks[i],1) for i in range(len(ytks))]
    l1 = [l0[i]+M0*5/4 for i in range(len(l0))]
    # 2.绘制
    clis = np.linspace(0, 1, 38)  # 36 去除首尾
    plt.figure()
    plt.style.use('bmh')
    plt.rcParams['savefig.dpi'] = 512  # 保存像素640*480*dpi/100
    plt.yticks(ytks, ytks1, fontsize=4)
    for i1 in range(len(ls36)):  # 时间波组图
        c = plt.get_cmap('gist_rainbow')(clis[i1])  # 36
        plt.plot(x, np.array(ls36[i1]), color=c, linewidth=0.5)  # 36
    plt.plot(x, l1, color='r', linewidth=0.5)
    plt.plot([0, N-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    plt.plot([0, N-1], [M0*5/4, M0*5/4], linewidth=1, alpha=0.5, c='k')
    plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    plt.close()


def cmpr_draw3(l0, tms, g1):
    '''绘制rms'''
    l1 = np.array(l0)
    h0 = np.max(l1)/10
    th = 20
    rpeaks, _ = signal.find_peaks(l1, height=h0, distance=th)
    # 1.创建数据
    ytks = np.arange(math.floor(max(l0))+1)
    # 2.绘制
    plt.figure()
    plt.style.use('bmh')
    plt.rcParams['savefig.dpi'] = 512
    plt.xticks(tms, tms, fontsize=4)  # x刻度
    plt.yticks(ytks, ytks, fontsize=4)
    plt.plot(range(len(l0)), l0, color='r', linewidth=0.5)
    plt.plot([0, len(l0)-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    plt.plot(rpeaks, l1[rpeaks], 'r*')
    plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    plt.close()


def cal_nums01(data):
    '''计算数列的每一段的长度'''
    # 初始化变量
    lengths = []
    current_value = None
    current_length = 0
    # 遍历数组
    for value in data:
        if value == current_value:
            current_length += 1
        else:
            if current_value is not None:
                lengths.append(current_length)
            current_value = value
            current_length = 1
    # 添加最后一段的长度
    if current_value is not None:
        lengths.append(current_length)
    return lengths


def get_hxd0(l2):
    '''统计候选点: 最低点，极小点, 鞍点'''
    l3 = [l2.index(min(l2))]  # 最低点
    l1 = np.array(l2)
    th = 20
    iks, _ = signal.find_peaks(-l1, distance=th)
    for i in iks:  # 极小值-波谷
        if abs(l3[0]-i)>th:
            l3.append(i)
    data1 = np.diff(l1, n=1)
    data1 = np.insert(data1, 0, l1[0])
    iks, _ = signal.find_peaks(-np.abs(data1), distance=th)  # 斜率极小值-平缓
    N1 = len(l3)
    for i in iks:
        sy = [1 for j in range(N1) if abs(l3[j]-i)<=th]
        if sy == []:  # 不在近邻中
            l3.append(i)
    l3.sort()
    return l3


def cmpr_draw31(l0, tms, g1):
    '''绘制rms: ThTr的候选位置'''
    Sr, Tp, N = tms[7], tms[10], len(l0)
    M1 = l0[Tp]  # Tp最大值
    # l2 = l0[Sr: Tp]  # Th————
    l2 = l0[Tp:]  # Tr————
    l3 = [l2.index(min(l2))]  # 最低点
    M0 = l2[l3[0]]  # 最小值
    # t0 = l3[0] + Sr  # 最小时刻————
    t0 = l3[0] + Tp  # 最小时刻
    l1 = np.array(l2)
    th = 20
    # n0 = 0.092
    n0 = 0.076
    iks, _ = signal.find_peaks(-l1, distance=th)
    for i in iks:  # 极小值-波谷
        if abs(l3[0]-i)>th:
            l3.append(i)
    data1 = np.diff(l1, n=1)
    data1 = np.insert(data1, 0, l1[0])
    # iks, _ = signal.find_peaks(-data1, distance=th)  # 斜率极小值-平缓
    iks, _ = signal.find_peaks(data1, distance=th)  # 斜率极大值-平缓————
    N1 = len(l3)
    for i in iks:
        sy = [1 for j in range(N1) if abs(l3[j]-i)<=th]
        if sy == []:  # 不在近邻中
            l3.append(i)
    l3.sort()
    # l3 = [l3[i]+Sr for i in range(len(l3))]  # 全部候选时刻————
    l3 = [l3[i]+Tp for i in range(len(l3))]  # 全部候选时刻
    l1 = np.array(l0)
    l2 = []
    for i in l3:
        if i < t0:
            # l2.append((M0-l1[i])/(M1-M0))
            l2.append((l1[i]-M0)/(M1-M0))
        else:
            # l2.append((l1[i]-M0)/(M1-M0))
            l2.append((M0-l1[i])/(M1-M0))
    for i in range(len(l3)):  # 修改时刻点-step2展示——————
        # if l2[len(l3)-1-i] < n0:
        #     tms[8] = l3[len(l3)-1-i]
        #     break
        if l2[i] < n0:
            tms[12] = l3[i]
            break
    tx = ', '.join(['%.3f' % l2[i] for i in range(len(l2))])
    # 1.创建数据
    ytks = np.arange(math.floor(max(l0))+1)
    # 2.绘制
    plt.figure()
    plt.style.use('bmh')
    plt.rcParams['savefig.dpi'] = 512
    plt.xticks(tms, tms, fontsize=4)  # x刻度
    plt.yticks(ytks, ytks, fontsize=4)
    plt.plot(range(len(l0)), l0, color='r', linewidth=0.5)
    plt.plot([0, len(l0)-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    plt.plot(l3, l1[l3], 'r*')
    plt.title(tx)
    plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    plt.close()


def cmpr_draw4(ls36, m1, m2, g1):
    '''绘制TP段基线: ls36和rms基线值'''
    # 1.创建数据
    M0, M1 = get_mai0(ls36)
    N = len(ls36[0])
    x = np.arange(N)
    ytks = np.arange(-math.floor(M1*5), math.floor(M0*25/4)+1)*0.2
    ytks = [round(ytks[i],1) for i in range(len(ytks))]
    # 2.绘制
    clis = np.linspace(0, 1, 38)  # 36 去除首尾
    plt.figure()
    plt.style.use('bmh')
    plt.rcParams['savefig.dpi'] = 512  # 保存像素640*480*dpi/100
    plt.yticks(ytks, ytks, fontsize=4)
    for i1 in range(len(ls36)):  # 时间波组图
        c = plt.get_cmap('gist_rainbow')(clis[i1])  # 36
        plt.plot(x, np.array(ls36[i1]), color=c, linewidth=0.5)  # 36
    # plt.plot(x, l1, color='r', linewidth=0.5)
    plt.plot([0, N-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    plt.plot([0, N-1], [m1, m1], linewidth=1, alpha=0.5, c='k')
    plt.plot([0, N-1], [m2, m2], linewidth=1, alpha=0.5, c='r')
    plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    plt.close()


def cal_iqr0(l0, md=1):
    '''IQR去除奇异值后均值'''
    data = np.array(l0)
    Q1 = np.percentile(data, 25)
    Q3 = np.percentile(data, 75)
    IQR = Q3 - Q1
    v1 = Q1 - 1.5 * IQR
    v2 = Q3 + 1.5 * IQR
    # m1 = round(np.mean(data), 2)
    data1 = data[(data >= v1) & (data <= v2)]
    # m2 = round(np.mean(data1), 2)
    m2 = np.mean(data1)
    if md:
        return m2
    else:
        m1 = np.mean(data)
        return get_round([m1, m2], [2, 2])+[v1, v2]


def cmpr_iqr1(l0, g1):
    '''IQR方法来识别并去除奇异值'''
    m1, m2, v1, v2 = cal_iqr0(l0, 0)
    if m1 != m2:
        N = len(l0)
        x = np.arange(N)
        plt.rcParams['savefig.dpi'] = 512
        cnt = 0
        for i in range(len(l0)):
            if l0[i]>=v1 and l0[i]<=v2:
                plt.scatter(i, l0[i], c='k', s=2)
            else:
                cnt += 1
                plt.scatter(i, l0[i], c='r', s=2)
        plt.title('%d, %.2f, %.2f' % (cnt, m1, m2))
        plt.plot([0,N-1], [m1, m1], linewidth=1, alpha=0.5, c='k')
        plt.plot([0,N-1], [m2, m2], linewidth=1, alpha=0.5, c='r')
        plt.savefig(g1, bbox_inches='tight', pad_inches=0)
        plt.close()


def cmpr_cgmcg0(f0, f1, v0):
    ls36 = get_mcg36(f0)  # N*36
    # ls36 = [[i]+[round(ls36[i][j]-v0,6) for j in range(len(ls36[i]))] for i in range(len(ls36))]
    ls36 = [[i]+[ls36[i][j]-v0 for j in range(len(ls36[i]))] for i in range(len(ls36))]
    l0 = ['\t'.join(format(ls36[i][j],'.6f') for j in range(len(ls36[i]))) for i in range(len(ls36))]
    write_str('\n'.join(l0), f1)


def get_mai0(ls36):
    '''周期最大幅值'''
    M0 = max([max(ls36[i]) for i in range(len(ls36))])
    M1 = -min([min(ls36[i]) for i in range(len(ls36))])
    return [M0, M1]


def cmpr_draw5(ls36, l0, ik, g1):
    '''修改基线后: 空间波组图'''
    M, N = max(get_mai0(ls36)), len(ls36[0])
    x = np.arange(N)
    y0 = x*0  # 0基线
    # 2.绘制
    fig, axs = plt.subplots(6, 6, figsize=(15, 15), facecolor='black', subplot_kw={'facecolor': 'black'})
    for ax in axs.flat:
        ax.set_xlim(0, N-1)
        ax.set_ylim(-M, M)
        ax.set_xticks([])  # 隐藏x轴刻度
        ax.set_yticks([])  # 隐藏y轴刻度
        ax.spines['top'].set_visible(False)    # 隐藏顶部轴线
        ax.spines['right'].set_visible(False)  # 隐藏右侧轴线
        ax.spines['bottom'].set_visible(False)  # 隐藏底部轴线
        ax.spines['left'].set_visible(False)   # 隐藏左侧轴线
    for i in range(6):
        for j in range(6):
            tx = []
            for v in [max(l0[i*6+j])/M, min(l0[i*6+j])/M]:
                if abs(round(v,3))>0.08:
                    tx.append('%.3f' % v)
            y = np.array(ls36[i*6+j])
            axs[i, j].plot(x, y0, color='blue', linewidth=2, alpha=0.6)
            axs[i, j].plot(x, y, color='white', linewidth=2)  # 设置曲线颜色为白色
            axs[i, j].scatter(ik, [0, 0], c='r', s=50)
            axs[i, j].text(0.7, 0.9, ', '.join(tx), horizontalalignment='center', verticalalignment='center', transform=axs[i, j].transAxes, color='white')
    plt.tight_layout()
    # plt.show()
    plt.savefig(g1)
    plt.close()


def cmpr_gettx0(l1, a, c):
    tx, fg = '', 0
    if l1 != []:
        tx0 = ', '.join(['%.3f' % f for f in l1])
        f0, f1 = min(l1), max(l1)
        if f0<=-a and f1>=a:
            tx1, fg = 'bidirectional', 1
        elif f0<=-a and f1<a:
            tx1, fg = 'inversion', 2
        elif f0>-a and f1>=c:
            tx1, fg = 'upright', 3
        elif f0>-a and f1<c:
            tx1, fg = 'low', 4
        tx = '%s\n%s' % (tx1, tx0)
    return [tx, fg]


def cmpr_gettx1(fs, ths):
    f00, f01, f10, f11 = fs
    a1, b1, c1, a2, b2, c2 = ths
    fg = [0, 0]
    if f01>=b2:
        fg[0] = 1
    elif f01>=a2 and f11>= c2:
        fg[0] = 1
    if f00<=b1:
        fg[1] = 1
    elif f00<=a1 and f10>=c1:
        fg[1] = 1
    if fg == [1,1]:
        tx, fg = 'bidirectional', 1
    elif fg == [0,1]:
        tx, fg = 'inversion', 2
    elif fg == [1,0]:
        tx, fg = 'upright', 3
    elif fg == [0,0]:
        tx, fg = 'low', 4
    tx += '\n%.3f, %.3f\n%.3f, %.3f' % (f00, f01, f10, f11)
    return [tx, fg]


def cmpr_gettx2(fs, ths, md=0):
    f00, f01, f10, f11, j0, j1 = fs
    a1, b1, c1, a2, b2, c2 = ths
    fg = [0, 0]
    if f01>=b2:
        fg[0] = 1
    elif f01>=a2 and f11>=c2:
        fg[0] = 1
    if f00<=b1:
        fg[1] = 1
    elif f00<=a1 and f10>=c1:
        fg[1] = 1
    if fg == [1,1]:
        if j0 < j1:
            tx, fg = 'negpos_bidirect', 1
        else:
            tx, fg = 'posneg_bidirect', 1
    elif fg == [0,1]:
        tx, fg = 'inversion', 2
    elif fg == [1,0]:
        tx, fg = 'upright', 3
    elif fg == [0,0]:
        tx, fg = 'low', 4
    if md:
        tx += '\n%.3f, %.3f\n%.3f, %.3f' % (f00, f01, f10, f11)
    return [tx, fg]


def cmpr_gettx3(fs, ths):
    '''T波单参数'''
    f00, f01, f10, f11, j0, j1 = fs
    a1, b1, c1, a2, b2, c2 = ths
    fg = [0, 0]
    if f01>=b2:
        fg[0] = 1
    elif f01>=a2 and f11>=c2:
        fg[0] = 1
    if f00<=b1:
        fg[1] = 1
    elif f00<=a1 and f10>=c1:
        fg[1] = 1
    if fg == [1,1]:
        if j0 < j1:
            return 4
        else:
            return 3
    elif fg == [0,1]:
        return 5
    elif fg == [1,0]:
        return 1
    else:
        return 2


def cmpr_t_mt0(ls36, g1, ts0, msh, ths, th=20):
    '''计算T波单通道参数'''
    Qh, Sr, Th, Tr = ts0
    l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    l3, ms0 = [], []
    l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    M = max(get_mai0(ls36))
    for i in range(len(l1)):  # 基于双比例
        M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
        l2 = l1[i]
        v0, v1, j0, j1 = 0, 0, 0, 0
        ik, _ = signal.find_peaks(-np.array(l2), distance=th)
        l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
        ik, _ = signal.find_peaks(np.array(l2), distance=th)
        l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
        if l30:
            l0 = [l2[j] for j in l30]
            v0, j0 = min(0, min(l0)), l30[l0.index(min(l0))]
        if l31:
            l0 = [l2[j] for j in l31]
            v1, j1 = max(0, max(l0)), l31[l0.index(max(l0))]
        f00, f01, f10, f11 = v0/M, v1/M, abs(v0/M0), abs(v1/M0)
        l3.append(cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 1))  # 
        tx, _ = cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 0)
        ms0.append(msh.index(tx)+1)
    if g1:
        cmpr_draw6(ls36, l3, [Th, Tr], g1)  # 空间波组图
    return ms0


def cmpr_t_mt1(ls36, ts0, ths, th=20):
    '''计算T波单通道参数: 1/2/3/4/5波的类型'''
    Qh, Sr, Th, Tr = ts0
    l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    ms0 = []
    l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    M = max(get_mai0(ls36))
    for i in range(len(l1)):  # 基于双比例
        M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
        l2 = l1[i]
        v0, v1, j0, j1 = 0, 0, 0, 0
        ik, _ = signal.find_peaks(-np.array(l2), distance=th)
        l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
        ik, _ = signal.find_peaks(np.array(l2), distance=th)
        l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
        if l30:
            l0 = [l2[j] for j in l30]
            v0, j0 = min(0, min(l0)), l30[l0.index(min(l0))]
        if l31:
            l0 = [l2[j] for j in l31]
            v1, j1 = max(0, max(l0)), l31[l0.index(max(l0))]
        f00, f01, f10, f11 = v0/M, v1/M, abs(v0/M0), abs(v1/M0)
        m0 = cmpr_gettx3([f00,f01,f10,f11,j0,j1], ths)
        ms0.append(m0)
    return ms0


def cmpr_qrs_mt1(ls36, ts0, ths, dhs, th1):
    '''计算QRS波单通道参数: 1/2/.../12波的类型'''
    Qh, Sr = ts0
    ms0, qhs0, qhs, srs, rss = [], [], [], [], []
    l1 = [ls36[i][Qh:Sr+1] for i in range(len(ls36))]
    for i in range(len(l1)):  # 基于双比例
        l2 = l1[i]
        M0 = max(max(l2), -min(l2))
        ik0, ik1, ik2 = cal_bfbg0(l2, th1)
        pt0 = sorted(set(ik0+ik1+ik2))
        l3 = [round(l2[i1]/M0,3) for i1 in range(len(l2))]
        Qh0, QRSs, Sr0, Rs = cmpr_qrspt1(l3, pt0, ths, dhs, ik1, ik2)
        mg = [[l3[ii] for ii in jj] for jj in QRSs]
        fg = cmpr_tpqrs0(mg)
        ms0.append(fg+1)
        qhs.append(Qh0)
        srs.append(Sr0)
        rss.append(Rs)
        if 1.0 in mg[0] or -1.0 in mg[0]:
            qhs0.append(Qh0)
    rss = hb_lis0(rss)
    if len(qhs0)>2:
        qh0 = cmpr_jzd0(qhs0, max(min(rss)-1,0), 0)[-1]  # 综合起止点
    else:
        qh0 = cmpr_jzd0(qhs, max(min(rss)-1,0), 0)[-1]
    t1, t2 = 62+qh0, 109+qh0
    sr0s = cmpr_jzd0(srs, min(max(rss)+1,Sr+1-Qh), 1)
    sr1s = [t00 for t00 in sr0s if t00>=t1 and t00<=t2]
    if sr1s:
        sr0 = sr1s[0]
    else:
        srs1 = [t00 for t00 in srs if t00>=t1 and t00<=t2]
        if srs1:
            sr0 = cmpr_jzd0(srs1, min(max(rss)+1,Sr+1-Qh), 1)[0]
        else:
            sr0 = sr0s[0]
    qhs = [t0+Qh for t0 in qhs]
    srs = [t0+Qh for t0 in srs]
    sr0 = sr0 + Qh
    return [ms0, qhs, srs, sr0]


class STSegmentAnalyzer:
    def __init__(self, rows=6, cols=6):
        self.name = None
        self.rows = rows
        self.cols = cols
        self.thresholds = {
            'J_elevation_1': 0.2,      # 幅值<3时：J点抬高阈值
            'J_depression_1': -0.2,    # 幅值<3时：J点压低阈值

            'J_elevation_2': 0.4,      # 3≤幅值<8时：J点抬高阈值
            'J_depression_2': -0.4,    # 3≤幅值<8时：J点压低阈值

            'J_elevation_3': 1.0,      # 幅值≥8时：J点抬高阈值
            'J_depression_3': -1.0,    # 幅值≥8时：J点压低阈值

            'J_elevation_4': 0.6,      # 极值比正常时：抬高阈值
            'J_depression_4': -0.6,    # 极值比正常时：压低阈值

            'st_measure_point': 80,    # J点后测量窗口(ms)
            'min_leads': 3,            # 最少相邻异常通道数
            'Amp_threshold_1': 3,      # 幅值第一个临界值
            'Amp_threshold_2': 8,      # 幅值第二个临界值
            'sustained_ratio': 0.9,    # ST段持续时间阈值（90%）
            'ratio_low': 0.75,         # 极值比下限
            'ratio_high': 1.33         # 极值比上限
        }

        self._initialize_regions()
        self._initialize_adjacent_leads()

    def _initialize_regions(self):
        """基于网格尺寸动态初始化空间区域"""
        # 计算区域边界
        top_rows = max(1, round(self.rows / 3))  # 上方区域：前1/3的行
        bottom_rows = max(1, round(self.rows / 3))  # 下方区域：后1/3的行
        left_cols = max(1, round(self.cols / 3))  # 左边区域：前1/3的列
        right_cols = max(1, round(self.cols / 3))  # 右边区域：后1/3的列

        # 计算中间区域边界（从1/6到5/6）
        middle_start_row = max(1, round(self.rows / 6))
        middle_end_row = self.rows - middle_start_row
        middle_start_col = max(1, round(self.cols / 6))
        middle_end_col = self.cols - middle_start_col

        # 初始化各个区域的集合
        self.spatial_regions = {
            1: set(),  # 上方区域
            2: set(),  # 下方区域
            3: set(),  # 左边区域
            4: set(),  # 右边区域
            5: set()  # 中间区域
        }

        # 辅助函数：将行列坐标转换为通道编号
        def get_channel(row, col):
            return row * self.cols + col + 1

        # 填充区域集合
        for row in range(self.rows):
            for col in range(self.cols):
                channel = get_channel(row, col)

                # 上方区域
                if row < top_rows:
                    self.spatial_regions[1].add(channel)

                # 下方区域
                if row >= self.rows - bottom_rows:
                    self.spatial_regions[2].add(channel)

                # 左边区域
                if col < left_cols:
                    self.spatial_regions[3].add(channel)

                # 右边区域
                if col >= self.cols - right_cols:
                    self.spatial_regions[4].add(channel)

                # 中间区域
                if (middle_start_row <= row < middle_end_row and
                        middle_start_col <= col < middle_end_col):
                    self.spatial_regions[5].add(channel)

        # 初始化每个通道的主要归属区域
        self.channel_primary_region = {}
        for channel in range(1, self.rows * self.cols + 1):
            self.channel_primary_region[channel] = {
                region_num for region_num, region in self.spatial_regions.items()
                if channel in region
            }

    def _initialize_adjacent_leads(self):
        """动态初始化相邻导联关系"""
        self.adjacent_leads = {}

        for row in range(self.rows):
            for col in range(self.cols):
                current = row * self.cols + col + 1
                adjacent = set()

                # 检查8个可能的相邻位置
                for dr in [-1, 0, 1]:
                    for dc in [-1, 0, 1]:
                        if dr == 0 and dc == 0:
                            continue

                        new_row = row + dr
                        new_col = col + dc

                        # 检查新位置是否在网格范围内
                        if (0 <= new_row < self.rows and
                                0 <= new_col < self.cols):
                            adjacent.add(new_row * self.cols + new_col + 1)

                self.adjacent_leads[current] = list(adjacent)

    def analyze_lead(self, lead_data, Ph, Sr, Th, Tr):
        """分析单个通道的ST段形态"""
        # 1. 计算基线
        l1, l2 = lead_data[Tr:], lead_data[:Ph]
        baseline = round(cal_iqr0(l1 + l2), 6)

        # 2. 获取ST段数据
        st_start = Sr
        st_end = min(Sr + self.thresholds['st_measure_point'], Th)
        st_segment = lead_data[st_start:st_end]
        st_data = [x - baseline for x in st_segment]

        # 3. 计算ST段的特征
        signal_amplitude = max(abs(max(lead_data)), abs(min(lead_data)))
        j_point_deviation = st_data[0]
        st_max = max(st_data)
        st_min = min(st_data)

        return {
            'st_data': st_data,                           # 添加ST段数据
            'signal_amplitude': signal_amplitude,         # 添加信号幅值
            'J_deviation': j_point_deviation,             # J点偏移量
            'st_max': st_max,                             # ST段最大值
            'st_min': st_min                              # ST段最小值
        }

    def _find_connected_leads(self, leads_dict):
        """深度优先搜索(DFS)算法找出相邻的异常通道群"""
        def dfs(lead, visited, current_group):
            visited.add(lead)
            current_group.add(lead)
            for neighbor in self.adjacent_leads[lead]:
                if neighbor not in visited and neighbor in leads_dict:
                    dfs(neighbor, visited, current_group)

            return current_group

        visited = set()
        connected_groups = []
        for lead in leads_dict:
            if lead not in visited:
                current_group = set()
                connected_group = dfs(lead, visited, current_group)
                if len(connected_group) >= self.thresholds['min_leads']:
                    connected_groups.append(connected_group)

        return connected_groups

    def _get_dominant_position(self, channels):
        """获取通道的主要空间位置"""
        if not channels:
            return None

        # 统计每个区域的权重
        region_weights = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}

        # 对每个通道，将其所属的所有区域权重都加1
        for channel in channels:
            for region in self.channel_primary_region[channel]:
                region_weights[region] += 1

        # 返回权重最高的区域编号
        return max(region_weights.items(), key=lambda x: x[1])[0] if any(region_weights.values()) else None

    def analyze_case(self, mcg_data, time_loc):
        """分析单个case的ST段形态
        1. 首先计算所有导联的ST段数据，获取极值信息
        2. 计算极值比，判断是否在正常范围内（0.75-1.33）
        3. 根据极值比的结果选择不同的分析策略：
           - 极值比异常：给三个阈值，按照不同幅值范围使用不同的阈值判断抬高或者压低
           - 极值比正常：给一个阈值，进一步分析是否抬高或者压低
        """
        Ph, Sr, Th, Tr = time_loc

        # 1. 分析所有导联，收集ST段数据
        lead_results = []
        st_max_values, st_min_values = [], []

        for lead_idx in range(36):
            result = self.analyze_lead(mcg_data[lead_idx], Ph, Sr, Th, Tr)
            result['lead_number'] = lead_idx + 1
            lead_results.append(result)
            if result['st_max'] > 0:
                st_max_values.append(result['st_max'])
            if result['st_min'] < 0:
                st_min_values.append(abs(result['st_min']))

        # 2. 计算极值比
        max_elevation = max(st_max_values) if st_max_values else 0
        max_depression = max(st_min_values) if st_min_values else 0
        ratio = max_elevation / max_depression if max_depression != 0 else float('inf')

        # 3. 根据极值比选择分析策略
        if not (self.thresholds['ratio_low'] <= ratio <= self.thresholds['ratio_high']) or max_depression == 0:
            # 极值比异常，使用三个阈值的分档策略
            return self._analyze_with_three_thresholds(lead_results)
        else:
            # 极值比正常，使用单一阈值策略
            return self._analyze_with_single_threshold(lead_results)

    def _analyze_with_three_thresholds(self, lead_results):
        """使用三档阈值策略判断异常"""
        elevation_leads = {}
        depression_leads = {}

        for result in lead_results:
            lead_num = result['lead_number']
            if result['signal_amplitude'] < self.thresholds['Amp_threshold_1']:
                elevation_threshold = self.thresholds['J_elevation_1']
                depression_threshold = self.thresholds['J_depression_1']
            elif self.thresholds['Amp_threshold_1'] <= result['signal_amplitude'] < self.thresholds['Amp_threshold_2']:
                elevation_threshold = self.thresholds['J_elevation_2']
                depression_threshold = self.thresholds['J_depression_2']
            else:
                elevation_threshold = self.thresholds['J_elevation_3']
                depression_threshold = self.thresholds['J_depression_3']

            # 计算超过阈值的点的比例
            total_points = len(result['st_data'])
            elevated_ratio = sum(1 for x in result['st_data'] if x >= elevation_threshold) / total_points
            depressed_ratio = sum(1 for x in result['st_data'] if x <= depression_threshold) / total_points

            # 判断是否有90%的点保持同向变化
            if elevated_ratio >= self.thresholds['sustained_ratio']:
                elevation_leads[lead_num] = result['J_deviation']
            elif depressed_ratio >= self.thresholds['sustained_ratio']:
                depression_leads[lead_num] = result['J_deviation']

        return self._process_connected_leads(elevation_leads, depression_leads)

    def _analyze_with_single_threshold(self, lead_results):
        """使用单一阈值策略判断异常"""
        elevation_leads = {}
        depression_leads = {}
        elevation_threshold = self.thresholds['J_elevation_4']
        depression_threshold = self.thresholds['J_depression_4']

        for result in lead_results:
            lead_num = result['lead_number']

            # 计算超过阈值的点的比例
            total_points = len(result['st_data'])
            elevated_ratio = sum(1 for x in result['st_data'] if x >= elevation_threshold) / total_points
            depressed_ratio = sum(1 for x in result['st_data'] if x <= depression_threshold) / total_points

            # 判断是否有90%的点保持同向变化
            if elevated_ratio >= self.thresholds['sustained_ratio']:
                elevation_leads[lead_num] = result['J_deviation']
            elif depressed_ratio >= self.thresholds['sustained_ratio']:
                depression_leads[lead_num] = result['J_deviation']

        return self._process_connected_leads(elevation_leads, depression_leads)

    def _process_connected_leads(self, elevation_leads, depression_leads):
        """处理相邻通道的公共逻辑"""
        elevation_groups = self._find_connected_leads(elevation_leads)
        depression_groups = self._find_connected_leads(depression_leads)

        elevation_channels = sorted([lead for group in elevation_groups for lead in group])
        depression_channels = sorted([lead for group in depression_groups for lead in group])

        if elevation_channels or depression_channels:
            elevation_region = self._get_dominant_position(elevation_channels)
            depression_region = self._get_dominant_position(depression_channels)
            return elevation_channels, depression_channels, elevation_region, depression_region

        return [], [], None, None


def cmpr_draw6(ls36, l0, ik, g1):
    '''修改基线后: 空间波组图'''
    M, N = max(get_mai0(ls36)), len(ls36[0])
    x = np.arange(N)
    y0 = x*0  # 0基线
    # 2.绘制
    fig, axs = plt.subplots(6, 6, figsize=(15, 15), facecolor='black', subplot_kw={'facecolor': 'black'})
    for ax in axs.flat:
        ax.set_xlim(0, N-1)
        ax.set_ylim(-M, M)
        ax.set_xticks([])  # 隐藏x轴刻度
        ax.set_yticks([])  # 隐藏y轴刻度
        ax.spines['top'].set_visible(False)    # 隐藏顶部轴线
        ax.spines['right'].set_visible(False)  # 隐藏右侧轴线
        ax.spines['bottom'].set_visible(False)  # 隐藏底部轴线
        ax.spines['left'].set_visible(False)   # 隐藏左侧轴线
    cs = ['gold', 'red', 'green', 'orange']
    for i in range(6):
        for j in range(6):
            tx, fg = l0[i*6+j]
            y = np.array(ls36[i*6+j])
            axs[i, j].plot(x, y0, color='blue', linewidth=2)
            axs[i, j].plot(x, y, color='white', linewidth=2)  # 设置曲线颜色为白色
            axs[i, j].scatter(ik, [0, 0], c='r', s=50)
            if fg:
                axs[i, j].text(0.7, 0.9, tx, horizontalalignment='center', verticalalignment='center', transform=axs[i, j].transAxes, color=cs[fg-1])
    plt.tight_layout()
    # plt.show()
    plt.savefig(g1)
    plt.close()


def cmpr_draw7(l2, g1):
    '''Qh候选位置'''
    th, r0, r1 = 20, 0.5, 0.1
    if max(l2)<-min(l2):
        l2 = [-v for v in l2]
    l20 = l2.copy()
    k0 = l2.index(max(l2))
    l2 = l2[:k0+1]
    l3 = [l2.index(min(l2))]  # 最低点
    M1 = max(l2)
    M0 = l2[l3[0]]  # 最小值
    # t0 = l3[0] + Sr  # 最小时刻————
    l1 = np.array(l2)
    iks, _ = signal.find_peaks(-l1, distance=th)
    for i in iks:  # 极小值-波谷
        if abs(l3[0]-i)>th:
            l3.append(i)
    data1 = np.diff(l1, n=1)
    data1 = np.insert(data1, 0, l1[0])
    iks, _ = signal.find_peaks(-data1, distance=th)  # 斜率极小值-平缓
    N1 = len(l3)
    for i in iks:
        sy = [1 for j in range(N1) if abs(l3[j]-i)<=th]
        if sy == []:  # 不在近邻中
            l3.append(i)
    l3.sort()
    l30 = []
    for i in range(len(l3)):
        v0 = l2[l3[len(l3)-1-i]]/M1
        if v0<r1 and abs(v0)<r0:
            l30 = [l3[j] for j in range(len(l3)-i) if abs(l2[l3[j]]/M1)<r0]
            break
    l3 = l30
    l3.reverse()
    l4 = [round(l2[i]/M1,3) for i in l3]
    # print(l3, l4)
    # l3.reverse()
    # strs = ', '.join(['%.3f' % (l2[i]/M1) for i in l3])
    # strs += '\n'+str(l3)
    # xs = list(range(len(l2)))
    # plt.plot([xs[0],xs[-1]], [0,0], linewidth=0.5)
    # plt.plot(xs, l2, c='k', linewidth=0.5)
    # for i in l3:
    #     plt.scatter(i, l2[i], c='r', s=2)
    # plt.title(strs)
    # plt.savefig(g1)
    # # plt.show()
    # plt.close()
    return l3


def cmpr_draw8(ls36, l0s, g1):
    '''QRS复合波类型: 空间波组图'''
    M, N = max(get_mai0(ls36)), len(ls36[0])
    x = np.arange(N)
    y0 = x*0  # 0基线
    # 2.绘制
    fig, axs = plt.subplots(6, 6, figsize=(15, 15), facecolor='black', subplot_kw={'facecolor': 'black'})
    plt.rcParams['font.size'] = 8
    plt.rcParams['savefig.dpi'] = 1024
    for ax in axs.flat:
        ax.set_xlim(0, N-1)
        ax.set_ylim(-M, M)
        ax.set_xticks([])  # 隐藏x轴刻度
        ax.set_yticks([])  # 隐藏y轴刻度
        ax.spines['top'].set_visible(False)    # 隐藏顶部轴线
        ax.spines['right'].set_visible(False)  # 隐藏右侧轴线
        ax.spines['bottom'].set_visible(False)  # 隐藏底部轴线
        ax.spines['left'].set_visible(False)   # 隐藏左侧轴线
    cmap = plt.get_cmap('rainbow', 12)
    cs = cmap(np.linspace(0, 1, 12))
    for i in range(6):
        for j in range(6):
            tx, fg, ik1, ik2 = l0s[i*6+j]
            y = np.array(ls36[i*6+j])
            axs[i, j].plot(x, y0, color='blue', linewidth=1)
            axs[i, j].plot(x, y, color='white', linewidth=1)  # 白色
            for i1 in ik1:
                axs[i, j].scatter(i1, y[i1], c='r', s=20)
            for i1 in ik2:
                axs[i, j].scatter(i1, y[i1], c='g', s=20)
            # if fg:
            axs[i, j].text(0.5, 0.9, tx, horizontalalignment='center', verticalalignment='center', transform=axs[i, j].transAxes, color=cs[fg])
    plt.tight_layout()
    # plt.show()
    plt.savefig(g1)
    plt.close()


def cmpr_R_q0(R0, pt0, l0, th1, th2):
    '''计算R0前的Q'''
    Rs, Qs = [R0], []
    jzd0 = R0-1  # <=开始点
    Qmi = pt0[max(pt0.index(R0)-3,0)]
    Qs0 = [vt for vt in l0 if vt[1]<R0 and vt[1]>=Qmi]  # 备选Q
    if Qs0:
        v0, Q0 = Qs0[-1]  # 预选Q
        l2 = [vt for vt in l0 if vt[1]<Q0 and vt[0]*v0>0]
        if l2:
            v1 = abs(l2[-1][0])
            if v1>th1*abs(v0) and v1<=th2:  # 噪声水平-排除Q
                Q0 = []
        if Q0:
            Qs.append(Q0)
            jzd0 = min(Qs)
    return [Rs, Qs, jzd0]


def cmpr_R_s0(t0, R0, l3, pt0, l0, th1, th2):
    '''计算R0后的S'''
    Rs, Ss = [], []
    jzd1 = R0+1  # >=结束点
    Sma = pt0[min(pt0.index(R0)+3,len(pt0)-1)]
    Ss0 = [vt for vt in l0 if vt[1]>R0 and vt[1]<=Sma]  # 备选S
    if Ss0:
        v0, S0 = Ss0[0]  # 预选S
        l2 = [vt for vt in l0 if vt[1]>S0 and vt[0]*v0>0]
        if l2:
            v1, t1 = abs(l2[0][0]), l2[0][1]
            if v1>th1*abs(v0) and v1<=th2:  # 噪声水平
                Ss = []
            elif v1>th2:
                Ss.append(S0)
                lis = [l3[i1] for i1 in range(S0, t1)]
                if min(lis)*v0>0 and max(lis)*v0>0:
                    Ss = [S0, t1]
            else:
                Ss = [S0]
        else:
            Ss = [S0]
    if not Ss:
        # Ss.append(R0)
        R0 = t0
    else:
        jzd1 = max(Ss)
    Rs.append(R0)
    return [Rs, Ss, jzd1]


def cmpr_jzd0(ts, v0=0, md=0):
    '''数值列表的集中点'''
    from collections import Counter
    a = ts.copy()
    a.sort()
    A = sorted(set(a))
    if md:
        A = [t0 for t0 in A if t0>=v0]
        if A == []:
            return [min(a)]
    else:
        A = [t0 for t0 in A if t0<=v0]
        if A == []:
            return [max(a)]
    if len(A) < 3:
        if md:
            return [A[-1]]
        else:
            return [A[0]]
    l0 = []
    for N0 in range(2, 11):
        N = min(max(a)-min(a), N0)
        l1 = [[] for _ in range(N)]
        for i in range(len(A)):
            a0 = A[i]
            ns = []
            for i0 in range(N):
                n0 = sum(1 for a1 in a if abs(a1-a0)<=i0)
                ns.append(n0)
                l1[i0].append(n0)
        l2 = []
        for i in range(N):
            M = max(l1[i])
            l2 += [j for j in range(len(l1[i])) if l1[i][j]==M]
        counts = Counter(l2)
        max_count = max(counts.values())
        modes = [num for num, count in counts.items() if count == max_count]
        l0 += modes
    counts = Counter(l0)
    max_count = max(counts.values())
    modes = [num for num, count in counts.items() if count == max_count]
    lns = [A[i1] for i1 in modes]
    return lns


def cmpr_qhsr0(pt0, jzd0, md=0):
    '''根据断点计算Qh和Sr-单通道'''
    l1 = []
    if md:
        for i0 in pt0:
            l1.append(i0)
            if i0>jzd0:
                break
    else:
        for i0 in range(len(pt0)):
            j0 = len(pt0)-1-i0
            l1.append(pt0[j0])
            if pt0[j0]<jzd0:
                break
    return l1[-1]


def cmpr_qrspt0(l3, pt0, ik1, ik2):
    '''QRS复合波识别和断点计算: 归一化值l3, 候选点pt0, 波峰ik1波谷ik2'''
    th0, th1, th2 = 0.042, 0.7, 0.3
    pt1 = [i0 for i0 in pt0 if i0 in ik1 and l3[i0]>th0]
    pt2 = [i0 for i0 in pt0 if i0 in ik2 and l3[i0]<-th0]
    pts = sorted(set(pt1+pt2))  # 备选波
    if len(pts) > 1:
        l0 = [[l3[i0], i0] for i0 in pts]
        l1 = [[abs(l3[i0]), l3[i0], i0] for i0 in pts]
        data = np.array(l1)
        idex = np.lexsort((-1 * data[:, 1], data[:, 2], -1 * data[:, 0]))
        sorted_data = data[idex]
        l1 = sorted_data.tolist()  # 绝对值从大到小, 时刻点从小到大, 幅值从大到小
        t0, t1 = int(l1[0][2]), int(l1[1][2])
        if t0<t1 or abs(l3[t1])<th2:  # 最大波在前-R波, 次大波<0.3-最大值R波
            R0 = t0
            Rs, Qs, jzd0 = cmpr_R_q0(R0, pt0, l0, th1, th2)
            _, Ss, jzd1 = cmpr_R_s0(t1, R0, l3, pt0, l0, th1, th2)
        else:
            t0, t1 = t1, t0  # 时间顺序
            R0 = t1
            Rs, Ss, jzd1 = cmpr_R_s0(t0, R0, l3, pt0, l0, th1, th2)
            if Ss == []:
                Ss, jzd1 = [t1], t1
            Rs, Qs, jzd0 = cmpr_R_q0(min(Rs), pt0, l0, th1, th2)
    else:  # 只有一个值
        Qs, Ss = [], []
        jzd0, jzd1, Rs = pts[0]-1, pts[0]+1, pts.copy()
    # print('Qs', [[i1, l3[i1]]for i1 in Qs])
    # print('Rs', [[i1, l3[i1]]for i1 in Rs])
    # print('Ss', [[i1, l3[i1]]for i1 in Ss])
    Qh0 = cmpr_qhsr0(pt0, jzd0, 0)
    Qs = [t0 for t0 in Qs if t0 != Qh0]
    Sr0 = cmpr_qhsr0(pt0, jzd1, 1)
    Ss = [t0 for t0 in Ss if t0 != Sr0]
    return [Qh0, Qs, Rs, Ss, Sr0]


def cmpr_non_noise0(l3, i1, alp, res, th3, dh3):
    '''是否非噪声: 当前点i1, 已有点alp, 剩余点res, 比例阈值th3, 距离阈值dh3'''
    if i1 < alp[0]:  # 当前点在左
        l0 = [i for i in res if i<i1 and l3[i]*l3[i1]>=0]  # 同号较小
        if l0:
            i0 = max(l0)
            if i1-i0<dh3 and l3[i0]/l3[i1]>th3:  # 噪声
                return 0
        i2 = min(alp)
        l0 = [i for i in res if i>i1 and i<i2 and l3[i]*l3[i1]>=0]  # 同号较大
        if l0:
            i0 = min(l0)
            if i0-i1<dh3 and l3[i0]/l3[i1]>th3:  # 噪声
                return 0
    else:  # 当前点在右
        l0 = [i for i in res if i>i1 and l3[i]*l3[i1]>=0]  # 同号较大
        if l0:
            i0 = min(l0)
            if i0-i1<dh3 and l3[i0]/l3[i1]>th3:  # 噪声
                return 0
        i2 = max(alp)
        l0 = [i for i in res if i<i1 and i>i2 and l3[i]*l3[i1]>=0]  # 同号较小
        if l0:
            i0 = max(l0)
            if i1-i0<dh3 and l3[i0]/l3[i1]>th3:  # 噪声
                return 0
    return 1  # 非噪声


def cmpr_addp0(l3, i1, res, alp, qjp, bgp, th3, dh2):
    '''添加波: 当前点i1, 剩余点res, 已有点alp, 合并qjp, 合并后最大bgp, 比例阈值th3, 距离阈值dh2'''
    if i1 < alp[0]:  # 当前点在左
        i2 = min(alp)
        l0 = sorted([i for i in res if i>=i1 and i<i2])  # 待整合点
        res = [i for i in res if i<i1 or i>i2]  # 剩余波-更新
        alp = l0 + alp
        l1 = []  # 待添加合并波
        if len(l0) == 1:
            l1.append(l0)
        else:
            for j1 in range(len(l0)-1):
                if j1 == 0:
                    l2 = [l0[j1]]
                if cmpr_notch0(l3, l0[j1], l0[j1+1], th3, dh2):
                    l2.append(l0[j1+1])  # 切迹合并
                else:
                    l1.append(l2)
                    l2 = [l0[j1+1]]
                if j1 == len(l0)-2:
                    l1.append(l2)
        j = [j0 for j0 in range(len(qjp)) if i2 in qjp[j0]][0]
        t1 = 0
        if cmpr_notch0(l3, l1[-1][-1], qjp[j][0], th3, dh2):
            l21 = l1[-1]+qjp[j]
            qjp[j] = l21.copy()
            l22 = [abs(l3[t0]) for t0 in l21]
            j1 = l22.index(max(l22))
            bgp[j] = l21[j1]
            t1 = 1  # 第一个不算
        qjp = l1[:len(l1)-t1]+qjp  # 合并波-更新
        l11 = []
        for k in range(len(l1)-t1):  # 添加新合并波
            l22 = [abs(l3[t0]) for t0 in l1[k]]
            j1 = l22.index(max(l22))
            l11.append(l1[k][j1])
        bgp = l11+bgp  # 合并后最大-更新
    else:  # 当前点在右
        i2 = max(alp)
        l0 = sorted([i for i in res if i<=i1 and i>i2])  # 待整合点
        res = [i for i in res if i>i1 or i<i2]  # 剩余波-更新
        alp = alp + l0
        l1 = []  # 待添加合并波
        if len(l0) == 1:
            l1.append(l0)
        else:
            for j1 in range(len(l0)-1):
                if j1 == 0:
                    l2 = [l0[j1]]
                if cmpr_notch0(l3, l0[j1], l0[j1+1], th3, dh2):
                    l2.append(l0[j1+1])  # 切迹合并
                else:
                    l1.append(l2)
                    l2 = [l0[j1+1]]
                if j1 == len(l0)-2:
                    l1.append(l2)
        j = [j0 for j0 in range(len(qjp)) if i2 in qjp[j0]][0]
        t1 = 0
        if cmpr_notch0(l3, qjp[j][-1], l1[0][0], th3, dh2):
            l21 = qjp[j]+l1[0]
            qjp[j] = l21.copy()
            l22 = [abs(l3[t0]) for t0 in l21]
            j1 = l22.index(max(l22))
            bgp[j] = l21[j1]
            t1 = 1  # 第一个不算
        qjp = qjp+l1[t1:]  # 合并波-更新
        l11 = []
        for k in range(t1, len(l1)):  # 添加新合并波
            l22 = [abs(l3[t0]) for t0 in l1[k]]
            j1 = l22.index(max(l22))
            l11.append(l1[k][j1])
        bgp = bgp+l11  # 合并后最大-更新
    return res, alp, qjp, bgp


def cmpr_notch0(l3, i1, i2, th3, dh2):
    '''是否切迹: i1/i2两个波, l3列表, 不例阈值th3, 距离阈值dh2'''
    if l3[i1]*l3[i2]<=0:
        return 0
    l0 = l3[i1:i2+1]
    if max(l0)*min(l0)<=0:
        return 0
    l0 = [abs(v) for v in l0]
    v1 = min(l0[0], l0[-1])
    v2 = min(l0)
    j1 = l0.index(v2)
    if l0[0]<=l0[-1]:
        if j1<dh2 and v2/l0[0]>th3:  # 切迹
            return 1
    else:
        if i2-i1-j1<dh2 and v2/l0[-1]>th3:  # 切迹
            return 1
    return 0


def cmpr_tpqrs0(QRSs):
    '''QRS复合的类型计算: ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']'''
    if len(QRSs)>3:
        ik = 11
    elif len(QRSs)==3:
        ik = 10
    elif len(QRSs)==1:
        if QRSs[0][0]>0:
            ik = 0
        else:
            ik = 8
    elif QRSs[0][0]>0 and QRSs[1][0]>0:
        ik = 1
    elif QRSs[0][0]<0 and QRSs[1][0]<0:
        ik = 9
    elif QRSs[0][0]<0 and QRSs[1][0]>0:
        a = max([abs(i1) for i1 in QRSs[0]])
        b = max([abs(i1) for i1 in QRSs[1]])
        ik = 4
        if a<=0.7 and b>0.7:
            ik = 2
        elif a>0.7 and b>0.7:
            ik = 3
    else:
        a = max([abs(i1) for i1 in QRSs[0]])
        b = max([abs(i1) for i1 in QRSs[1]])
        ik = 7
        if a>0.7 and b<=0.7:
            ik = 5
        elif a>0.7 and b>0.7:
            ik = 6
    return ik


def cmpr_qrspt1(l3, pt00, ths, dhs, ik1, ik2):
    '''QRS复合波识别和断点计算: 归一化值l3, 候选点pt0, 波峰ik1波谷ik2'''
    Qh0, QRSs, Sr0, Rs = pt00[0], [], pt00[-1], []
    th0, th1, th2, th3, th4 = ths
    dh0, dh1, dh2, dh3 = dhs
    if len(pt00) < 3:
        return [Qh0, QRSs, Sr0]
    pt01 = [pt00[0], pt00[-1]]
    pt01 += [i0 for i0 in pt00 if i0 in ik1 and l3[i0]>th4]
    pt01 += [i0 for i0 in pt00 if i0 in ik2 and l3[i0]<-th4]
    pt01 = sorted(set(pt01))  # 候选点-噪声判定
    pt0 = pt00[1:-1]
    pt1 = [i0 for i0 in pt0 if i0 in ik1 and l3[i0]>th0]
    pt1 += [i0 for i0 in pt0 if i0 in ik2 and l3[i0]<-th0]
    pt1 = sorted(set(pt1))  # 备选波
    if len(pt1) == 0:
        print('Error! no peaks!')
    elif len(pt1) == 1:  # 一个R波
        Qh0, Sr0 = pt1[0], pt1[0]
        Qh0 = cmpr_qhsr0(pt00, Qh0, 0)
        Sr0 = cmpr_qhsr0(pt00, Sr0, 1)
        QRSs, Rs = [pt1], pt1
    else:  # 不止1个波
        l1 = [[abs(l3[i0]), l3[i0], i0] for i0 in pt1]
        data = np.array(l1)
        idex = np.lexsort((-1 * data[:, 1], data[:, 2], -1 * data[:, 0]))
        sorted_data = data[idex]
        l1 = sorted_data.tolist()
        pt2 = [int(l1[i1][2]) for i1 in range(len(l1))]  # 备选点
        res = pt2[1:]  # 剩余点-控制
        # 全部波、切迹合并、合并后最大波
        alp, qjp, bgp, Rs = [pt2[0]], [[pt2[0]]], [pt2[0]], [pt2[0]]  # 1.最大波
        for i1 in pt2[1:]:  # 依次取次大波
            if res == []:  # 无剩余波
                break
            elif i1 not in res:  # 不属于剩余波
                continue
            p0 = []
            if len(bgp) < 3:  # 2.第2/3个
                if min([abs(i1-i2) for i2 in bgp])<dh0:  # 距离<55
                    if abs(l3[i1]) > th1 and len(bgp)==1:  # >0.3添加
                        p0.append(i1)
                    else:
                        if cmpr_non_noise0(l3, i1, alp, pt01, th3, dh3):
                            p0.append(i1)  # 非噪声
            else:  # 第4/5/..个
                if abs(l3[i1]) >= th2:  # >0.15
                    if min([abs(i1-i2) for i2 in bgp])<dh1:  # 距离<40
                        if cmpr_non_noise0(l3, i1, alp, pt01, th3, dh3):
                            p0.append(i1)  # 非噪声
            if p0:  # 逐个添加波
                res, alp, qjp, bgp = cmpr_addp0(l3, i1, res, alp, qjp, bgp, th3, dh2)
            else:  # 单点截断
                if i1 < alp[0]:
                    i0 = max(alp)
                    res = [i2 for i2 in res if i2>i1]  # 只取大
                else:
                    i0 = min(alp)
                    res = [i2 for i2 in res if i2<i1]  # 只取小
        Qh0, Sr0 = alp[0], alp[-1]
        QRSs1 = qjp.copy()  # 全部合并波
        Qh0 = cmpr_qhsr0(pt00, Qh0, 0)
        Sr0 = cmpr_qhsr0(pt00, Sr0, 1)
        QRSs = []
        for j in range(len(QRSs1)):
            l2 = QRSs1[j]
            if Qh0 in l2:
                l2.remove(Qh0)
            if Sr0 in l2:
                l2.remove(Sr0)
            if l2:
                QRSs.append(l2)
    return [Qh0, QRSs, Sr0, Rs]


def cmpr_diag0(l1):
    '''参数综合0: 最大/最小概率为诊断分数'''
    l3 = []
    for l2 in l1:
        v0, v1 = min(l2), max(l2)
        if v1 > 100-v0:
            l3.append(v1)
        else:
            l3.append(v0)
    return l3


def cmpr_diag1(l1):
    '''参数综合1: 60-100对应0-4'''
    l3 = []
    for l2 in l1:
        # p1 = round(sum([max(p0/10-6,0) for p0 in l2]))  # 离散
        # p1 = round(sum([p0/10-5 for p0 in l2]))
        # p1 = round(sum([p0/10-5 for p0 in l2]),1)
        p1 = round(sum([max(p0/10-6,0) for p0 in l2]),1)  # 连续
        l3.append(p1)
    return l3


def draw0(xys, xyss, g1='', tx=''):
    '''绘图'''
    fig, ax = plt.subplots()
    ax.set_aspect('equal')
    plt.rcParams['savefig.dpi'] = 1024
    cnt = 1
    for xs, ys in xys:
        if cnt==1:
            plt.plot(xs, ys, c='c', linewidth=0.5)
        else:
            plt.plot(xs, ys, c='k', linewidth=0.5)
        cnt+=1
    cnt = 1
    for xss, yss in xyss:
        if cnt==1:
            plt.scatter(xss, yss, c='k', s=2)
        else:
            plt.scatter(xss, yss, c='r', s=2)
        cnt+=1
    # plt.scatter(xss1, yss1, c='r', s=2)
    # plt.plot(xs, ysm, c='k', linewidth=0.5)
    # plt.title('\n'.join([str(l00) for l00 in l0]))
    plt.title(tx)
    plt.savefig(g1)
    # plt.show()
    plt.close()

# def extract_seg0(xs, ys, g1):
def extract_seg0(xs, ys):
    '''提取变化曲线的分段点和单调分段'''
    # draw0([[xs, ys]], [], '%s_qushixian1.png' % g1)
    vv, th = 3, max(xs)/20  # 阈值-高斯拟合/数量范围
    x11, idx = cal_pktg0(ys, vv, th)  # 波峰波谷
    a=idx.copy()
    pks0 = cal_pks0(idx, xs, ys, x11, th)  # 近邻合并1
    b=pks0.copy()
    # xss = [xs[i] for i in pks0]
    # yss = [ys[i] for i in pks0]
    # draw0([[xs, ys]], [[xss, yss]], '%s_hebing3.png' % g1)
    apx, pks1 = cal_pks1(pks0, xs, ys, th)  # 锚点/段点
    c=pks1.copy()
    # xss = [xs[i] for i in pks1]
    # yss = [ys[i] for i in pks1]
    # draw0([[xs, ys]], [[xss, yss]], '%s_maodian4.png' % g1)
    pks2 = cal_seg0(apx, pks1, xs, ys)  # 单调分段点——1
    d=pks2.copy()
    # xss = [xs[i] for i in pks1 if i not in pks2]
    # yss = [ys[i] for i in pks1 if i not in pks2]
    # xss1 = [xs[i] for i in pks2]
    # yss1 = [ys[i] for i in pks2]
    # draw0([[xs, ys]], [[xss, yss], [xss1, yss1]], '%s_dandiaodian5.png' % g1)
    l0, pts, pks3 = fit_pks0(pks2, pks1, xs, ys)  # 单调多分段-斜率s/索引s/剩余索引s
    pt = fit_pks1(l0, pts, xs, ys)  # 单调段内合并-合并后索引集
    pks1 = sorted(set(pks3+pt))  # 整合全部分段点——0
    e=pks1.copy()
    # xss = [xs[i] for i in pks1 if i not in pks2]
    # yss = [ys[i] for i in pks1 if i not in pks2]
    # xss1 = [xs[i] for i in pks2]
    # yss1 = [ys[i] for i in pks2]
    # draw0([[xs, ys]], [[xss, yss], [xss1, yss1]], '%s_zuizhongdian6.png' % g1)
    pt = cal_ks2(pks1, pks2, xs, ys)  # 统计单调斜率集
    # print(pt)
    iks, rks = fit_ks3(pks1, pks2, xs, ys, th/2)  # 单调分段拟合结果-索引集n+1/概率集n
    # print(iks, rks)
    # r0 = []
    # c1 = [c0 for c0 in b if c0 not in c]
    # if len(a)>len(b) and len(c1)>0 and len(c)>len(e) and len(pt)>3:
    #     r0 = [1]
    # rks.append(r0)
    # if l2:
    # print(l2)
    # print(l3)
    # xss = [xs[i] for i in pks1 if i not in pks2]
    # yss = [ys[i] for i in pks1 if i not in pks2]
    # xss1 = [xs[i] for i in pks2]
    # yss1 = [ys[i] for i in pks2]
    # fig, ax = plt.subplots()
    # ax.set_aspect('equal')
    # plt.rcParams['savefig.dpi'] = 1024
    # plt.plot(xs, ys, c='c', linewidth=0.5)
    # plt.scatter(xss, yss, c='k', s=2)
    # plt.scatter(xss1, yss1, c='r', s=2)
    # # plt.plot(xs, ysm, c='k', linewidth=0.5)
    # # plt.title('\n'.join([str(l00) for l00 in l0]))
    # plt.title(str(pt))
    # plt.savefig(g1)
    # # plt.show()
    # plt.close()
    return [iks, rks]


def cal_hb_ps0(p1, p2):
    '''计算合并概率'''
    if min(p1, p2)>=50:
        p3 = max(51, min(p1, p2))
    elif max(p1, p2)<=50:
        p3 = min(49, max(p1, p2))
    elif p1+p2>=100:
        p3 = 51
    else:
        p3 = 49
    return p3


def hb_ranks0(l1, l2):
    '''合并两个自动化分级结果: l1=trva, l2=tr, l3=tr-va'''
    l0 = sorted(set(l1[0]+l2[0]))  # 指标排序
    l3 = [l0, []]
    i1, i2 = 0, 0
    for v0 in l0:
        n1 = len([v1 for v1 in l1[0] if v1>=v0])
        n2 = len([v1 for v1 in l2[0] if v1>=v0])
        p3 = cal_hb_ps0(l1[1][len(l1[1])-1-n1], l2[1][len(l2[1])-1-n2])
        l3[1].append(p3)
    l3[1].append(cal_hb_ps0(l1[1][-1], l2[1][-1]))
    return l3


def cal_ks0(x_data, y_data, g=0):
    '''抛物型/直线型: 拟合参数'''
    x_data = np.array(x_data)
    y_data = np.array(y_data)
    if g:
        coefficients = np.polyfit(x_data, y_data, 2)  # 抛物线型
        a, b, c = coefficients
        return [a, b, c]
        # return get_round([a, b, c], [2, 2, 2])
    else:
        A = np.vstack([x_data, np.ones(len(x_data))]).T  # 直线型
        m, c = np.linalg.lstsq(A, y_data, rcond=None)[0]
        return get_round([m, c], [2, 2])


def cal_ks1(pks2, i1, i2, xs, ys):
    '''统计单调段内的整体拟合斜率和分段拟合斜率'''
    k0 = cal_ks0(xs[pks2[i1]:pks2[i2]+1], ys[pks2[i1]:pks2[i2]+1], 0)[0]
    if ys[pks2[i2]] > ys[pks2[i1]]:
        ks = [max(0.02, k0)]
    else:
        ks = [min(-0.02, k0)]
    for j in range(i1, i2):
        k0 = cal_ks0(xs[pks2[j]:pks2[j+1]+1], ys[pks2[j]:pks2[j+1]+1], 0)[0]
        ks.append(k0)
    return ks


def cal_ols(x_data, y_data, g=0):
    '''抛物型/直线型: 拟合结果'''
    x_data = np.array(x_data)
    y_data = np.array(y_data)
    A = np.vstack([x_data, np.ones(len(x_data))]).T  # 直线型
    m, c = np.linalg.lstsq(A, y_data, rcond=None)[0]
    y_fit = m * x_data + c
    if g:
        coefficients = np.polyfit(x_data, y_data, 2)  # 抛物线型
        a, b, c = coefficients
        y_fit = a * x_data**2 + b * x_data + c
    return y_fit


def get_fit0(x_data, y_data, fg):
    '''抛物型/直线型: 拟合类型选择; 阈值*'''
    x_data = np.array(x_data)
    y_data = np.array(y_data)
    dk = 0  # 不能抛物型时——n=2
    if x_data.shape[0] > 2:
        coefficients = np.polyfit(x_data, y_data, 2)  # 抛物型
        a, b, c = coefficients
        # y_fit = a * x_data**2 + b * x_data + c
        ks = [max(0, min(1, 2*a*x_data[i1]+b)) for i1 in range(x_data.shape[0])]
        if fg:
            ks = [max(0.51, k0) for k0 in ks]
        else:
            ks = [min(0.49, k0) for k0 in ks]
        k1, k2 = ks[0], ks[-1]
        dk = abs(min(k1, 0.5) - min(k2, 0.5))
        if fg:
            dk = abs(max(k1, 0.5) - max(k2, 0.5))
    g = 1
    if dk < 0.1:
        A = np.vstack([x_data, np.ones(len(x_data))]).T  # 直线型
        m1, c = np.linalg.lstsq(A, y_data, rcond=None)[0]
        if fg:
            m = min(1, max(0.51, m1))
        else:
            m = max(0, min(0.49, m1))  # 概率截断
        # y_fit = m * x_data + c
        ks, g = [m for i in range(x_data.shape[0])], 0
    return ks, g


def xiuzh_ks0(kss):
    '''修正kss: 线性概率前后有固定概率, 且趋势矛盾时, 匹配固定概率'''
    kss0 = []
    for i in range(len(kss)):
        ks1 = kss[i]
        if len(set(ks1)) > 1:
            if i > 0:
                ks0 = kss[i-1]
                if len(set(ks0))==1 and ks0[0] < max(ks1) and ks0[0] > min(ks1):
                    if ks0[0] > ks1[0]:
                        for j in range(len(ks1)):
                            if ks1[j] < ks0[0]:
                                ks1[j] = ks0[0]
                            else:
                                break
                    else:
                        for j in range(len(ks1)):
                            if ks1[j] > ks0[0]:
                                ks1[j] = ks0[0]
                            else:
                                break
            if i < len(kss)-1:
                ks0 = kss[i+1]
                if len(set(ks0))==1 and ks0[0] < max(ks1) and ks0[0] > min(ks1):
                    if ks0[0] > ks1[-1]:
                        for j1 in range(len(ks1)):
                            j = -1-j1
                            if ks1[j] < ks0[0]:
                                ks1[j] = ks0[0]
                            else:
                                break
                    else:
                        for j in range(len(ks1)):
                            if ks1[j] > ks0[0]:
                                ks1[j] = ks0[0]
                            else:
                                break
            kss[i] = ks1
        kss[i] = get_round(kss[i], [2 for i in range(len(kss[i]))])
        kss0 += kss[i][:-1]
        if i == len(kss)-1:
            kss0 += kss[i][-1:]
    return kss0


def get_fitting0(xs, ys, qj, fg):
    '''单调性拟合: X轴、Y轴、区间分段点、阴阳'''
    kss, gs = [], []  # 类型
    for i in range(len(qj)-1):
        i1, i2 = qj[i], qj[i+1]
        xs2 = [x0-xs[i1] for x0 in xs]
        ys2 = [y0-ys[i1] for y0 in ys]
        ks, g = get_fit0(xs2[i1:i2+1], ys2[i1:i2+1], fg)
        kss.append(ks)
        gs.append(g)
    qj0, gs0 = [qj[0]], []  # 区间合并
    for i in range(len(qj)-1):
        qj0.append(qj[i+1])
        gs0.append(gs[i])
        if i > 0:  # 连续直线型&概率<5
            if gs[i]==0 and gs[i-1]==0 and abs(kss[i][0]-kss[i-1][0])<0.05:
                qj0.pop(-2)
                gs0.pop(-2)
    kss, gs = [], []  # 遍历
    for i in range(len(qj0)-1):
        i1, i2 = qj0[i], qj0[i+1]
        xs2 = [x0-xs[i1] for x0 in xs]
        ys2 = [y0-ys[i1] for y0 in ys]
        ks, g = get_fit0(xs2[i1:i2+1], ys2[i1:i2+1], fg)
        kss.append(ks)
        gs.append(g)
    kss = xiuzh_ks0(kss)  # 修正抛物型概率
    mts, pss = [], []  # 写入分级信息
    i0 = qj0[0]
    for i in range(len(kss)):
        if i>0 and i<len(kss)-1:
            if kss[i] == kss[i-1]:
                continue
        mts.append(i+i0)
        pss.append(int(100*kss[i]))
    return mts, pss, qj0, gs


def draw2(xy, g1):
    '''绘制概率拟合曲线: (n2-n1)/(n1+n2)'''
    fig, ax = plt.subplots()
    ax.set_aspect('equal')
    plt.rcParams['savefig.dpi'] = 1024
    for l0 in xy:
        x_data, y_data, y_fit = l0
        plt.plot(x_data, y_data, c='c', linewidth=0.5)
        for i in [0, -1]:
            plt.plot([x_data[i], x_data[i]], [0, y_data[i]], c='lightgrey', linewidth=1)
        plt.plot(x_data, y_fit, c='k', linewidth=0.5)
    plt.savefig(g1)
    # plt.show()
    plt.close()


def ana_lsrk0(ls):
    '''分析查看-自动分级结果rks.txt'''
    l1 = [l[1] for l in ls]
    l2 = []
    for l3 in l1:
        cn = 1
        for i in range(len(l3)-1):
            if l3[i] > 50 and l3[i-1] < 50:
                cn += 1
            elif l3[i] < 50 and l3[i-1] > 50:
                cn += 1
        l2.append(cn)
    print(l2)
    print(sorted(set(l2)))


def draw3(xss, yss, g1=''):
    '''展示曲线'''
    fig, ax = plt.subplots()
    ax.set_aspect('equal')
    plt.rcParams['savefig.dpi'] = 1024
    plt.scatter(xss, yss, c='r', s=1)
    plt.plot(xss, yss, c='k', linewidth=0.5)
    ma = max(xss)
    for i in range(20):
        plt.plot([i/20*ma, i/20*ma], [min(yss), max(yss)], c='k', alpha=0.1, linewidth=1)
    # plt.savefig(g1)
    plt.show()
    plt.close()


def draw4(yss, g1=''):
    '''概率趋势曲线'''
    if len(yss) > 2:
        xss = list(range(len(yss)))
        # plt.rcParams['savefig.dpi'] = 256
        plt.scatter(xss, yss, c='r', s=1)
        plt.plot(xss, yss, c='k', linewidth=0.5)
        plt.plot([xss[0], xss[-1]], [50, 50], c='k', alpha=0.1, linewidth=1)
        # ma = max(xss)
        # for i in range(20):
        #     plt.plot([i/20*ma, i/20*ma], [min(yss), max(yss)], c='k', alpha=0.1, linewidth=1)
        plt.savefig(g1)
        # plt.show()
        plt.close()


def cal_rtsq1(ks1, iks1, k0, cnt1, n2s, n12s, a):
    '''象限类合并100-50连续概率'''
    if a > 0:
        ks, iks, cnt = ks1, iks1, cnt1
        l1 = [k0[0]]
        for i in range(len(k0)-1):
            # print(i, cnt)
            if len(l1) == 1:
                if abs(k0[i+1]-k0[i])<20:
                    l1.append(k0[i+1])
                    k0[i+1] = round(100*sum(n2s[cnt:cnt+2])/sum(n12s[cnt:cnt+2]))
                    # k0[i+1] = sum([k0[i], k0[i+1]])/2
                else:
                    if k0[0]>=50:
                        ks.append(max(51, k0[i]))
                    else:
                        ks.append(min(49, k0[i]))
                    iks.append(cnt)
                    l1 = [k0[i+1]]
            else:
                if abs(k0[i+1]-k0[i])<15:
                    l1.append(k0[i+1])
                    # print(cnt+2-len(l1), cnt+2)
                    k0[i+1] = round(100*sum(n2s[cnt+2-len(l1):cnt+2])/sum(n12s[cnt+2-len(l1):cnt+2]))
                else:
                    if k0[0]>=50:
                        ks.append(max(51, k0[i]))
                    else:
                        ks.append(min(49, k0[i]))
                    iks.append(cnt)
                    l1 = [k0[i+1]]
            cnt += 1
            if i == len(k0)-2:
                if k0[0]>=50:
                    ks.append(max(51, k0[i+1]))
                else:
                    ks.append(min(49, k0[i+1]))
                iks.append(cnt)
            # print(i, cnt, l1, k0, ks, iks)
        ks1, iks1, cnt1 = ks, iks, cnt+1
    else:
        ks, iks, cnt = [], [], cnt1+len(k0)-1
        iks.append(cnt)
        l1 = [k0[-1]]
        for j in range(len(k0)-1):
            cnt -= 1
            i = len(k0)-1-j
            if len(l1) == 1:
                if abs(k0[i-1]-k0[i])<20:
                    l1.append(k0[i-1])
                    k0[i-1] = round(100*sum(n2s[cnt:cnt+2])/sum(n12s[cnt:cnt+2]))
                else:
                    if k0[0]>=50:
                        ks.append(max(51, k0[i]))
                    else:
                        ks.append(min(49, k0[i]))
                    iks.append(cnt)
                    l1 = [k0[i-1]]
            else:
                if abs(k0[i-1]-k0[i])<15:
                    l1.append(k0[i-1])
                    k0[i-1] = round(100*sum(n2s[cnt:cnt+len(l1)])/sum(n12s[cnt:cnt+len(l1)]))
                else:
                    if k0[0]>=50:
                        ks.append(max(51, k0[i]))
                    else:
                        ks.append(min(49, k0[i]))
                    iks.append(cnt)
                    l1 = [k0[i-1]]
            if i == 1:
                if k0[0]>=50:
                    ks.append(max(51, k0[i-1]))
                else:
                    ks.append(min(49, k0[i-1]))
            # print(j, cnt, l1, ks, k0, iks)
        ks.reverse()
        # print(ks)
        iks.reverse()
        ks1, iks1, cnt1 = ks1+ks, iks1+iks, cnt1+len(k0)
    return ks1, iks1, cnt1


def cal_rtsnm0(xss, na):
    '''数量输出指标和结果/自动化分级'''
    mts = [x[0] for x in xss]+[xss[-1][0]]
    k0s = [round(100*xss[0][2]/xss[0][3])]
    k0s += [round(100*(xss[i+1][2]-xss[i][2])/(xss[i+1][3]-xss[i][3])) for i in range(len(xss)-1)]
    k0s += [round(100*(na-xss[-1][2])/(2*na-xss[-1][3]))]
    return [mts, k0s]


def cal_rtsq0(xss, na):
    '''象限自动化分级'''
    mts = [x[0] for x in xss]+[xss[-1][0]]  # 指标值
    n2s = [xss[0][2]]+[xss[i+1][2]-xss[i][2] for i in range(len(xss)-1)]+[na-xss[-1][2]]  # n2
    n12s = [xss[0][3]]+[xss[i+1][3]-xss[i][3] for i in range(len(xss)-1)]+[2*na-xss[-1][3]]  # n1+n2
    k0s = [round(100*n2s[i]/n12s[i]) for i in range(len(n2s))]  # 离散概率k0
    kss, k0 = [], [k0s[0]]
    for i in range(len(k0s)-1):
        if k0s[i+1]>=50 and k0s[i]<50:
            kss.append(k0)
            k0 = [k0s[i+1]]
        elif k0s[i+1]<50 and k0s[i]>=50:
            kss.append(k0)
            k0 = [k0s[i+1]]
        else:
            k0.append(k0s[i+1])
        if i == len(k0s)-2:
            kss.append(k0)
    # print(mts)
    # print(k0s)
    # print(kss)
    cnt = 0
    ks = []
    iks = []
    for k0 in kss:
        # print(k0)
        if len(k0) == 1:
            if k0[0] == 50:
                k0[0] = 51
            ks.append(k0[0])
            iks.append(cnt)
            cnt += 1
        else:
            if k0[0]>=k0[-1] and k0[0]>=50:
                ks, iks, cnt = cal_rtsq1(ks, iks, k0, cnt, n2s, n12s, 1)
            elif k0[0]<=k0[-1] and k0[0]<50:
                ks, iks, cnt = cal_rtsq1(ks, iks, k0, cnt, n2s, n12s, 1)
            else:
                ks, iks, cnt = cal_rtsq1(ks, iks, k0, cnt, n2s, n12s, 0)
    # for i in range(len(ks)):
    #     if ks[i] == 50:
    #         ks[i] = 51
    # if len(iks)>1:
    #     iks[-1] = iks[-2]
    # print([mts[i] for i in iks])
    # print(iks)
    # print(ks)
    return [[mts[i] for i in iks[:-1]], ks]


# def auto_rks0(xss, d5, mt):
def auto_rks0(xss, mt, na=671):
    '''cache自动化分级'''
    # na = max([x0[1] for x0 in xss]+[x0[2] for x0 in xss])  # 同最大值时-错误
    # na = 671  # 最大数量671/883————
    # rts = cal_rtsnm0(xss, na)  # 
    # print(mt, rts)
    if mt in ['mfm_QR_nums', 'mfm_QR_pnnflu', 'mfm_QR_pnnma', 'mfm_QR_pnpflu', 'mfm_QR_pnpma', 'mfm_RS_nums', 'mfm_RS_pnnflu', 'mfm_RS_pnnma', 'mfm_RS_pnpflu', 'mfm_RS_pnpma', 'mfm_TT_nums', 'mfm_TT_pnnflu', 'mfm_TT_pnnma', 'mfm_TT_pnpflu', 'mfm_TT_pnpma', 'pcdm_QR_nums', 'pcdm_RS_nums', 'pcdm_TT_nums', 'mfm_QR_q', 'mfm_RS_q', 'mfm_TT_q', 'mfm_TTtj_br_q', 'mfm_TTtj_sg_q', 'mfm_TTtj_df_q', 'pcdm_QR_q', 'pcdm_RS_q', 'pcdm_TT_q']:  # 数量类/象限类-自动化分级
        rts = cal_rtsq0(xss, na)
        if len(rts[0])<1:
            rts = [[], [50]]
    else:
        xs = [0] + [x0[3] for x0 in xss] + [2*na]  # n1+n2
        ys1 = [0] + [x0[4] for x0 in xss] + [0]  # n2-n1
        # iks, rts = extract_seg0(xs, ys1, '%s/%s.png' % (d5, mt))
        # rts = extract_seg0(xs, ys1, '%s/%s' % (d5, mt))
        iks, rks = extract_seg0(xs, ys1)
        if len(iks)<3:
            rts = [[], [50]]
        else:
            rts = [[xss[i-1][0] for i in iks[1:-1]], rks]
        # xs = [xss[0]]+rts[0]+[xss[-1]]
        # ys = [rks[1][0]]+rks[1]
        # draw0([[xs, ys]], [], '%s/%s.png' % (d5, mt))
    # ys = [0] + [x0[2] for x0 in xss] + [na]  # n2
    # rks = [[], []]
    # mt0 = [x0[0] for x0 in xss]
    # xy = []
    # flag = 0
    # # if len(qjs) == 1:
    # #     flag = 1
    # for i in range(len(qjs)):
    #     mts, pss, qj0, gs0 = get_fitting0(xs, ys, qjs[i], fgs[i])
    #     for j in range(len(qj0)-1):  # 趋势变化曲线
    #         xss = [xs[k] for k in range(qj0[j], qj0[j+1]+1)]
    #         yss = [ys1[k] for k in range(qj0[j], qj0[j+1]+1)]
    #         y_fit = cal_ols(xss, yss, gs0[j])
    #         xy.append([xss, yss, y_fit])
    #         # xy1.append([xss])
    #     # qj0 = qjs[i]
    #     # for j in range(len(qj0)-1):  # 趋势变化曲线
    #     #     xss = [xs[k] for k in range(qj0[j], qj0[j+1]+1)]
    #     #     yss = [ys1[k] for k in range(qj0[j], qj0[j+1]+1)]
    #     #     xy.append([xss, yss, yss])
    #     rks[1] += pss[:-1]
    #     rks[0] += [mt0[j-1] for j in mts[1:-1]]
    #     if i < len(qjs)-1:
    #         rks[0] += [mt0[mts[-1]-1]]
    # if flag:
    #     print(pss)
    # if rks[0] != []:
    #     draw2(xy, '%s/%s.png' % (d5, mt))
    # else:
    #     draw3(xs, ys1, '%s/%s.png' % (d5, mt))
    # draw3(xs, ys1, '%s/%s.png' % (d5, mt))
    return rts
    # print(len(mt0))
    # print(mt0)
    # # # return qjs
    # x00 = qjs[0]
    # ys1 = [x0[2] for x0 in xss]  # n2
    # # for i in range(len(x00)-1):
    # #     ik, ik2 = x00[i], x00[i+1]
    # #     # print(ik, ik2, len(xs))
    # #     xs2 = [x0-xs[ik] for x0 in xs]
    # #     ys2 = [y0-ys1[ik] for y0 in ys1]
    # #     xs2 = xs[ik:ik2+1]
    # #     ys2 = ys1[ik:ik2+1]
    # #     # print(xs, ys1)
    # #     cal_ols(xs2, ys2)
    # ik, ik2 = x00[0], x00[1]
    # xs2 = [x0-xs[ik] for x0 in xs]
    # ys2 = [y0-ys1[ik] for y0 in ys1]
    # xs2 = xs[ik:ik2+1]
    # ys2 = ys1[ik:ik2+1]
    # cal_ols(xs2, ys2)
    # ik, ik2 = x00[1], x00[3]
    # xs2 = [x0-xs[ik] for x0 in xs]
    # ys2 = [y0-ys1[ik] for y0 in ys1]
    # xs2 = xs[ik:ik2+1]
    # ys2 = ys1[ik:ik2+1]
    # cal_ols(xs2, ys2)
    # ik, ik2 = x00[2], x00[3]
    # xs2 = [x0-xs[ik] for x0 in xs]
    # ys2 = [y0-ys1[ik] for y0 in ys1]
    # xs2 = xs[ik:ik2+1]
    # ys2 = ys1[ik:ik2+1]
    # cal_ols(xs2, ys2)
    # ys = [x0[i1] for x0 in xss]
    # # 绘制曲线
    # fig, ax = plt.subplots()
    # ax.set_aspect('equal')
    # plt.rcParams['savefig.dpi'] = 1024
    # xs0 = [xs[i] for i in pks[1:-1]]
    # ys0 = [ys[i] for i in pks[1:-1]]
    # plt.scatter(xs0, ys0, c='k', s=1)
    # plt.plot(xs, ys, c='c', linewidth=0.5)
    # plt.plot([min(xs), max(xs)], [0, 0], c='k', alpha=0.1, linewidth=1)
    # ma = max(xs)
    # for i in range(20):
    #     plt.plot([i/20*ma, i/20*ma], [min(ys), max(ys)], c='k', alpha=0.1, linewidth=1)
    # # plt.savefig('%s/%s.png' % (d5, mt))
    # # plt.pause(5)
    # # plt.show()
    # plt.close()


def draw_fdnh0(x_data, y_data):
    from scipy.interpolate import PchipInterpolator
    # 使用PchipInterpolator进行分段线性拟合
    interp = PchipInterpolator(x_data, y_data)
    # 生成用于绘制的x和y值
    x_fit = np.linspace(min(x_data), max(x_data), 100)
    y_fit = interp(x_fit)
    # 绘制原始数据点
    plt.scatter(x_data, y_data, marker='^', label='Original Data', color='red')
    # # 绘制分段线性拟合结果
    # plt.plot(x_fit, y_fit, marker='.', label='Piecewise Linear Fit', color='blue')
    # 绘制分段点
    plt.scatter(x_data, interp(x_data), label='Breakpoints')
    # plt.scatter(x_data, interp(x_data), color='green', label='Breakpoints')
    plt.legend()
    plt.title('Piecewise Linear Fitting')
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.pause(5)
    # plt.show()
    # time.sleep(5)
    plt.close()
    ##
    # from scipy.interpolate import splmake, splev
    # x_new = np.linspace(min(x_data), max(x_data), 4 + 1)  # 包括两端点
    # k = 1  # 分段线性，k=3为三次样条
    # t, c, u = splmake(x_data, y_data, k=k, estep=(max(x_data) - min(x_data)) / 4)
    # # 使用插值对象
    # xi = np.linspace(min(x_data), max(x_data), 100)
    # yi = splev(xi, (t, c, u), der=0)
    # # 绘制原始数据点
    # plt.scatter(x_data, y_data, label='Original Data', color='red')
    # # 绘制分段线性拟合结果
    # plt.plot(xi, yi, label='Piecewise Linear Fit', color='blue')
    # plt.pause(5)
    # # plt.show()
    # # time.sleep(5)
    # plt.close()


def draw(ds, p0s, T2=10):
    x = np.array(list(range(len(ds))))
    y = np.array(ds)
    vv=2
    nm = max(int(len(ds)/4), 1)
    ds0 = ds[-nm:]+ds+ds[:nm]  # 弧线距离段前后补1/4保证拟合的连续性
    y0 = np.array(ds0)
    y_smooth = gaussian_filter1d(y0, sigma=vv)  # 高斯滤波
    ys = list(y_smooth)  # 可选地ds
    ys = ys[nm:-nm]
    y_smooth = np.array(ys)
    jz = sum(ys)/len(ys)
    ts = get_tuchu0(ys)  # 突出索引列表
    # print('tuchu nums', len(ts))
    if len(ts) == 0:
        return [0]
    y_diff = np.abs(np.diff(y_smooth))
    # T1 = 0.26  # 平缓段斜率阈值
    T1 = jz/20  # 平缓段斜率阈值
    # print('T1', T1)
    flat_segments = []
    st = []
    for i in range(len(y_diff)):
        if y_diff[i] <= T1:  # 添加点
            flat_segments, st = add_index0(flat_segments, st, i, T2, 0)
        else:  # 添加段
            flat_segments, st = add_index0(flat_segments, st, i, T2, 1)
        if i == len(y_diff)-1:
            flat_segments, st = add_index0(flat_segments, st, i+1, T2, 1)
    if flat_segments == []:
        return [0]
    flat_averages = [np.mean(y_smooth[s[0]:s[1]+1]) for s in flat_segments]
    # print(flat_segments, flat_averages)
    f0 = get_rg_lis0(flat_segments, len(ds))
    tvs = []
    for i in range(len(ts)):
        tv = cal_tudu0(ts[i], ys, p0s, f0)
        tvs.append(tv)
    # print('tvs', tvs)
    # ly = list(y_smooth)
    # print(sum(ly)/len(ly), max(y), ly.index(max(ly)))
    # ly = list(y_diff)
    # print(np.mean(y_diff), max(y_diff), ly.index(max(ly)))
    # l1 = y.copy()
    # l1 = [round(l1[i], 2) for i in range(len(l1))]
    # print(l1)
    # l1 = y_smooth.copy()
    # l1 = [round(l1[i], 2) for i in range(len(l1))]
    # print(l1)
    # l1 = y_diff.copy()
    # l1 = [round(l1[i], 2) for i in range(len(l1))]
    # print(l1)
    # print(max(y_smooth), max(y_diff))
    # plt.plot(x[:-1], y_diff, 'k')
    # x_diff = np.diff(x)
    # 找到平缓段的起始和结束索引
    # # 计算每个平缓段的平均值
    # print(flat_segments)
    # print(y_smooth)
    # if sum(tvs) == 0:
    #     return tvs
    # plt.scatter(0, 30, s=1)
    # plt.plot(x, y)
    # plt.plot(x, y_smooth, label='Smoothed Data', linestyle='--')
    # for s in flat_segments:
    #     # print(y_smooth[s[0]:s[1]+2])
    #     xss = np.array(list(range(s[0], s[1]+1)))
    #     plt.plot(xss, 0*xss, 'b')
    #     # print(s[0], s[1]+2)
    # # # 打印平缓段的平均数值
    # # print("Average values of flat segments:", flat_averages)
    # # if flat_segments == []:
    # #     plt.show()
    # plt.show()
    # # plt.savefig('./fig%f.png' % vv)
    # plt.close()
    return tvs


def drawmcg(Z, savefig, ct=0):
    '''绘制等磁图'''
    plt.rcParams['figure.figsize'] = (8, 8)
    plt.rcParams['savefig.dpi'] = 256
    plt.matshow(Z, cmap='jet')
    plt.xticks([])
    plt.yticks([])
    plt.axis('off')
    plt.margins(0, 0)
    plt.draw()
    if ct:
        plt.contour(Z, levels=[0])
    plt.savefig(savefig, bbox_inches='tight', pad_inches=0)
    plt.close()


def cal_pts_dis0(p1, p2, T2):
    '''计算弧线距离: 形态弧线p1<p2, T2最小稳定距离'''
    ds, p0s = [], []
    a, b = [], []
    for p0 in p1:
        a += p0
    for p0 in p2:
        b += p0
    if len(b) == 0:
        return 0
    for i in range(len(a)):
        d = [product2(a[i][0]-b[j][0],a[i][1]-b[j][1]) for j in range(len(b))]
        ds.append(round(min(d), 2))
        p0s.append(b[int(d.index(min(d)))])
    # print(ds)
    # print('ds', p1)
    # print('ds', ds, p0s, T2)
    tvs = draw(ds, p0s, T2)  # 距离离散度，2个突出时/1.5
    return sum(tvs)/(1+0.5*(len(tvs)-1))


def cal_jl0(qts, bjs, i1):
    '''计算距离弥散度集: 嵌套集qts/多级多连通边界集bjs/逆序索引i1'''
    jl0 = []
    for i in range(len(qts)):  # 每个连通
        if qts[i] == []:
            jl0.append(0)
        else:
            pt1 = bjs[i1][i]  # 低形态边界集
            pt2 = []  # 高形态边界集
            for j in qts[i]:
                pt2 += bjs[i1+1][j]
            ds = 0
            if min(len(pt1), len(pt2))>0:
                ds = cal_pts_dis0(pt1, pt2, 10-i)
            jl1 = (i1+1)*0.1*max(0, min(3, ds-0.5))  # 距离弥散度
            jl1 = gb_score0(jl1, 1)
            jl0.append(jl1)
    return jl0


def cal_xt0(qts, bjls, zbs, zxs, i1):
    '''计算形态弥散度集: 嵌套集qts/边界率集bjls/多连通坐标集/重心集zxs/形态极i1'''
    xt0 = []
    T0 = 0.25  # 边界率阈值-大于则不计算
    T1 = 1.7  # 形态弥散度阈值-大于则计算
    for i in range(len(qts)):  # 每个连通
        xt1 = 0
        if qts[i] == []:
            if bjls[i] <= T0:
                cx, cy = zxs[i]
                l1 = zbs[i]
                e = 0
                if len(l1) > 0:
                    l2 = [product2(cx-l1[j][0], cy-l1[j][1]) for j in range(len(l1))]
                    ma = max(l2)
                    mi = sum(l2)/len(l2)
                    if mi != 0:
                        e = product2(ma, mi, 1)/mi
                # print(e)
                if e > 1.7:
                    xt1 = (e-1.7)*(i1+1)*1.6  # 基于形态极的弥散度
        xt1 = gb_score0(xt1, 1)
        xt0.append(xt1)
    return xt0


def add_jl_szh0(jl3, qts, k, iks, jls):
    '''逐级添加距离弥散度: 弥散度jl3/嵌套qts/形态极i/索引集iks/弥散度集jls'''
    for i in iks:
        jl3 += jls[k][i]
        jl3 = add_jl_szh0(jl3, qts, k+1, qts[k][i], jls)
    return jl3


def cal_all_jl0(qts, jls):
    '''整合距离弥散度: 嵌套qts/距离弥散度jls'''
    jd = 4  # 精度——
    jl2 = []
    for i in range(len(qts[0])):  # 0.3极
        jl3 = jls[0][i]
        jl3 = add_jl_szh0(jl3, qts, 1, qts[0][i], jls)  # 逐级添加距离弥散度
        jl2.append(round(jl3, jd))
    return jl2


def cal_lsds0(qts, jhs, jls0, xts):
    '''整合连通弥散度和整体离散度: 0.3各连通形态的全部弥散度和整体离散度'''
    jls = jls0.copy()
    for i in range(len(jls)):
        for j in range(len(jls[i])):
            jls[i][j] += xts[i][j]  # 添加形态弥散度
            if i < len(jls)-1:
                jls[i][j] += jhs[i+1][j]  # 添加0.3-0.8级聚合离散度
    jl2 = cal_all_jl0(qts, jls)  # 整合0.3各连通的3种弥散/离散
    return [jl2, jhs[0][0]]  # 添加整体聚合离散度


def top_n_with_same_tens(numbers, n=3):
    # 2位数列表, 取前几个, 十位数相同且不多于3个，记录原始索引
    sorted_numbers = sorted(enumerate(numbers), key=lambda x: x[1], reverse=True)
    result = []
    current_tens = max(numbers)//10
    for index, num in sorted_numbers:
        tens = num // 10
        if current_tens != tens or len(result) >= n:
            break
        result.append(index)
    return result


def cal_jh_jl_xt0(zbs, qts, Z):
    '''计算聚合离散度/距离弥散度/形态弥散度: 坐标集zbs/嵌套集qts/矩阵Z'''
    jhs, jls, xts = [], [], []
    zbs.reverse()  # 逆向计算0.3-0.9
    qts.reverse()
    szhs, zxs = cal_szh_zx0(zbs, Z)  # 多极多连通数值和集/重心集
    bjs, bjls = cal_bj0(zbs)  # 多级多连通的边界集
    for i1 in range(len(zbs)):  # 逐级0.3-0.9
        jh0 = cal_jh0(qts, szhs[i1], zxs[i1], i1)  # 聚合离散度集
        jl0 = cal_jl0(qts[i1], bjs, i1)  # 距离弥散度集
        xt0 = cal_xt0(qts[i1], bjls[i1], zbs[i1], zxs[i1], i1)  # 形态弥散度
        jhs.append(jh0)
        jls.append(jl0)
        xts.append(xt0)
    # print('聚合离散/距离弥散/形态弥散', jhs, jls, xts)
    a,b,c = [sum(hb_lis0(lis)) for lis in [jhs, jls, xts]]
    lsds = cal_lsds0(qts, jhs, jls, xts)
    lsd0 = round(a+b+c,3)
    lsd1 = gb_score0(lsd0, 3)
    lis0 = [qts, jhs, jls, xts, [a,b,c], [lsds, lsd0, lsd1]]
    return lis0


def get_disp1_qrst0(matrixes, timelis0, mfm_file):
    '''计算磁极离散度(形态渐进): QRST、正磁极、负磁极、正负磁极'''
    l40 = []
    l1 = get_lines1(mfm_file)
    for i1 in [0, 1, 2, 4]:  # QRST时刻点
        t1 = timelis0[i1]
        Z = norms0(matrixes[t1])
        ls = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3]#, 0.2, 0.1]
        zbs, qts = cal_zb_qt0(ls, Z)
        lis0 = cal_jh_jl_xt0(zbs, qts, Z)
        dp = lis0[-1][-1]
        ls = [-0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3]#, -0.2, -0.1]
        zbs, qts = cal_zb_qt0(ls, Z)
        lis0 = cal_jh_jl_xt0(zbs, qts, Z)
        dn = lis0[-1][-1]
        mcgdata = mcg_float(l1, t1)
        dpn = dp*mcgdata.max()-dn*mcgdata.min()
        dpn = dpn / max(mcgdata.max(), -mcgdata.min())  # 正负幅值归一化和
        l40.append(get_round([dp, dn, dpn], [3, 3, 3]))
    return l40


def cal_num0(p0, ik=0, v0=1, v1=1):
    '''多维列表-元素匹配计数: 第ik列, 系数v0, 匹配数值v1'''
    return sum(1 for j in range(len(p0)) if p0[j][ik] == v1)*v0


def cal_nonbd0(p0, ix, iy, mi=5, ma=96):
    '''非边界率: 多维列表p0, ixy坐标列, mi最小边, ma最大边'''
    if len(p0) == 0:
        return 0
    return sum(1 for j in range(len(p0)) if p0[j][ix]>mi and p0[j][ix]<ma and p0[j][iy]>mi and p0[j][iy]<ma)/len(p0)*100


def rm_small_brs1(br, ts, ik):
    '''
    小分支筛除:
        br分支, ts帧数, ik正负;
        主极子比例>0.5时，有效长度>2.9保留;
        无主极子，非边界/半边界，有效长度>4.2保留;
        无主极子，边界，有效长度>5.2保留;
        主极子比例<0.5时，有效长度>3.6保留;
    '''
    flag = 1  # 剔除
    if len(br) > 2:
        b4 = (1-2*ik)*round(sum(br[j][1] for j in range(len(br)))/ts*100, 1)  # 有效长度
        b5 = round(cal_num0(br, 1, 100/ts, 1-2*ik), 1)  # 主极子长度
        b6 = round(cal_nonbd0(br, 2, 3, 5, 96), 1)  # 非边界率
        if b5/b4 >= 0.5 and b4 > 2.9:
            flag = 0
        elif b5 == 0:  # 无主极子
            if b6 > 0 and b4 > 4.2:
                flag = 0
            if b6 == 0 and b4 > 5.2:
                flag = 0
        elif b5/b4 < 0.5 and b4 > 3.6:
            flag = 0
    return flag

def tichu_traject0(b1, md=0):
    '''剔除小分支'''
    b3 = []
    for i in range(len(b1)):
        for j in range(len(b1[i])):
            for k in range(len(b1[i][j])):
                b3.append(b1[i][j][k][0])  # 帧
                # b3.append(b1[i][j][k][0][0])  # 帧
    # b3 = [b3[i][0] for ]
    # print(b3)
    ts = max(b3)-min(b3)+1  # 统计帧数
    b2 = []
    b01 = []
    for i in range(4):
        b3 = []
        for j in range(len(b1[i])):
            br = b1[i][j].copy()
            if not rm_small_brs1(br, ts, 0):
                b3.append(b1[i][j])  # 分支
        b2.append(b3)
        b01.append(len(b1[i])-len(b3))
    for i in range(4, len(b1)):
        b3 = []
        for j in range(len(b1[i])):
            br = b1[i][j].copy()
            if not rm_small_brs1(br, ts, 1):
                b3.append(b1[i][j])  # 分支
        b2.append(b3)
        b01.append(len(b1[i])-len(b3))
    if md == 0:
        return b2
    else:
        return [b2, b01]


def cal_sum0(p0, ik=0, v0=1):
    '''多维列表-元素求和: 第ik列, 系数v0'''
    return sum(p0[j][ik] for j in range(len(p0)))*v0


def cal_dis1(xs, ys):
    '''计算点集前后距离列表'''
    ds = []
    for i in range(len(xs)-1):
        ds.append(round(product2(xs[i+1]-xs[i], ys[i+1]-ys[i]), 1))
    return ds


def get_subseg0(dn):
    '''获取全部子分段: dn主分支距离列表'''
    a = []  # 主分支标记0/1
    for i1 in range(len(dn)):
        if dn[i1] >= 2:
            a.append(1)
        else:
            a.append(0)
    c = []  # 全部子分段
    c1 = []
    for i in range(len(a)):
        if a[i] == 0 and c1 == []:
            c1.append(i)
        if a[i] == 1 and c1:
            c.append(c1+[i])
            c1 = []
        elif i == len(a)-1 and c1:
            c.append(c1+[i+1])
    d = []# 合并后的有效分段
    d1 = c[0]
    if len(c) == 1:
        d = c
    for i in range(len(c)-1):
        if c[i+1][0]-d1[1]==1 and min(d1[1]-d1[0],c[i+1][1]-c[i+1][0])>4:
            d1 = [d1[0], c[i+1][1]]
            if i == len(c)-2:
                d.append(d1.copy())
        else:
            d.append(d1.copy())
            d1 = c[i+1]
            if i == len(c)-2:
                d.append(d1.copy())
    e = [d[i][1]-d[i][0] for i in range(len(d))]  # 筛选主段/头/尾
    e1 = d[e.index(max(e))]
    n1 = min(e1[0]+1, len(dn))
    n2 = max(e1[1], 0)
    e2 = [e1.copy(), [0, n1], [n2, len(dn)]]
    return e2


def cal_max_dist0(xs, ys):
    '''散点中的最远距离'''
    if len(xs) < 2:
        return 0
    ds=0
    for i in range(len(xs)):
        for j in range(i+1, len(xs)):
            d0 = product2(xs[i]-xs[j], ys[i]-ys[j])
            ds = max(ds, d0)
    return ds


def cal_tt_mtj_stabs0(xs0, ys0, iks):
    i0, i1 = iks
    xs = xs0[i0:i1]
    ys = ys0[i0:i1]
    atjpa = sum([product2(xs[i+1]-xs[i], ys[i+1]-ys[i]) for i in range(len(xs)-1)])
    atjsp = cal_max_dist0(xs, ys)
    atjec = 0
    if atjpa > 0:
        atjec = atjpa / atjsp
    atjpa, atjsp, atjec = get_round([atjpa, atjsp, atjec], [1, 1, 1])
    return atjpa, atjsp, atjec


def get_tt_mtj_mts0(ts, nmtj, xs2, ys2, v0):
    '''
    计算TT主分支的单极子指标:
        有效长度、主极子长度、非边界率
        整段a、主段m、头部h、尾部r
        轨迹路径tjpa——动态稳定性
        轨迹跨度tjsp——形态稳定性
        弹性曲率
    '''
    melg = round(cal_sum0(nmtj, 1, 100/ts), 1)  # 有效长度
    mmlg = round(cal_num0(nmtj, 1, 100/ts, v0), 1)  # 主极子长度
    mnbd = round(cal_nonbd0(nmtj, 2, 3, 5, 96), 1)  # 非边界率
    e = get_subseg0(cal_dis1(xs2, ys2))  # 极子距离->主段
    # dn = cal_dis1(xs2, ys2)  # 极子距离->主段
    # print(dn)
    # e = get_subseg0(dn)  # 极子距离->主段
    atjpa, atjsp, atjec = cal_tt_mtj_stabs0(xs2, ys2, [0, len(nmtj)])
    mtjpa, mtjsp, mtjec = cal_tt_mtj_stabs0(xs2, ys2, e[0])  # 主段
    htjpa, htjsp, htjec = cal_tt_mtj_stabs0(xs2, ys2, e[1])  # 头部
    rtjpa, rtjsp, rtjec = cal_tt_mtj_stabs0(xs2, ys2, e[2])  # 尾部
    return e[0], [melg, mmlg, mnbd, atjpa, atjsp, atjec, mtjpa, mtjsp, mtjec, htjpa, htjsp, htjec, rtjpa, rtjsp, rtjec]


def get_mf_dxyndy0(pmtj, nmtj, mf):
    '''获取幅值和坐标[[mf1, pxy1, nxy1], [mf2, pxy2, nxy2], ...]'''
    # 获取匹配段
    p1, p2 = pmtj[0][0], pmtj[-1][0]
    n1, n2 = nmtj[0][0], nmtj[-1][0]
    t1, t2 = max(p1, n1), min(p2, n2)+1
    ps = pmtj[max(0, t1-p1):max(0, t2-p1)]
    ns = nmtj[max(0, t1-n1):max(0, t2-n1)]
    fs = mf[t1:t2]
    mg0 = []  # 统计信息
    # print(ps)
    # print(ns)
    for i in range(len(ps)):
        f0 = ps[i][1]*fs[i][0] + ns[i][1]*fs[i][1]  # 幅值差
        px, py = ps[i][2], ps[i][3]  # 正极子坐标
        nx, ny = ns[i][2], ns[i][3]  # 负极子坐标
        mg0.append([f0, px, py, nx, ny])
    return ps, ns, mg0


def cal_di_avlg0(p0, n0, ts):
    '''偶极子有效长度: 正负极子有效长度的平均'''
    me0 = round(cal_sum0(p0, 1, 100/ts), 1)
    me1 = round(cal_sum0(n0, 1, 100/ts), 1)  # 负
    melg0 = round((me0-me1)/2, 1)
    return melg0


def cal_di_nonbd0(p0, n0, ix, iy, mi, ma):
    mn0 = round(cal_nonbd0(p0, ix, iy, mi, ma), 1)  # 非边界率
    mn1 = round(cal_nonbd0(n0, ix, iy, mi, ma), 1)  # 非边界率
    mndb0 = round((mn0+mn1)/2, 1)  # 修正均值————
    return mndb0


def cal_tt_mtj_dimts0(mg0, p0, n0, ts, ix, iy, mi, ma):
    '''计算11个TT主分支/主段偶极子参数'''
    if len(mg0) == 0:
        return [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    melg0 = cal_di_avlg0(p0, n0, ts)  # 主分支有效长度
    mndb0 = cal_di_nonbd0(p0, n0, ix, iy, mi, ma)  # 主分支非边界率
    # 细节参数11个
    Mf = max([mg0[i][0] for i in range(len(mg0))])
    a, qrd, cx, cy, mxy, fxy = [], [], [], [], [], []
    for i in range(len(mg0)):
        Nf, px, py, nx, ny = mg0[i]  # 幅值差、坐标
        dx, dy = ny-py, px-nx  # 指向
        a.append(calangle(dx, dy))  # 角度
        d0 = product2(dx, dy)
        qrd.append([d0])  # 偶极距
        cx.append((px+nx)/2)  # 中心坐标
        cy.append((py+ny)/2)
        mxy.append(list(calptcors(d0/product2(50,50), dx, dy)))  # 面积距离坐标
        fxy.append(list(calptcors(Nf/Mf, dx, dy)))  # 面积幅值坐标
    qra, q = cal_qra_q(a)  # 整体转角、所在象限
    q = int(q)
    areaf = calarea(fxy, 0, 1)  # 面积幅值
    aread = calarea(mxy, 0, 1)  # 面积距离
    didma, didmi, didav, didms = calmas(qrd, 0, 'min')  # 偶极矩最大、最小、均值、均方差
    ctjpa, ctjsp, ctjec = cal_tt_mtj_stabs0(cx, cy, [0, len(cx)])  # 路径、跨度、曲率
    qra, areaf, aread, didma, didmi, didav, didms = get_round([qra, areaf, aread, didma, didmi, didav, didms], [1, 3, 3, 1, 1, 1, 3])
    return [melg0, mndb0, qra, q, areaf, aread, didma, didmi, didav, didms, ctjpa, ctjsp, ctjec]


def diff(l0, l1, iks=0):
    '''两个值/列表作差'''
    if type(l0) == list:
        if type(iks) == list:
            l2 = []
            for i in range(len(iks)):
                l2.append(diff(l0, l1, iks[i]))
            return l2
        else:
            return l0[iks]-l1[iks]
    else:
        return l0-l1


def cal_tt_mtj_dimts1(mts0, mts1):
    '''TT主分支和主段的偶极子参数差'''
    df0 = get_round(diff(mts0, mts1, [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12]), [1, 1, 1, 3, 3, 1, 1, 1, 3, 1, 1, 1])
    dmelg, dmndb, dqra, dareaf, daread, ddidma, ddidmi, ddidav, ddidms, dctjpa, dctjsp, dctjec = df0
    dq0 = ''.join([i for i in str(mts0[3]) if i not in str(mts1[3])])
    dq = 0  # 象限重叠0——
    if dq0:
        dq = int(dq0)
    return [dmelg, dmndb, dqra, dq, dareaf, daread, ddidma, -ddidmi, ddidav, ddidms, dctjpa, dctjsp, dctjec]


def get_tt_mtj_dimts0(pmtj, nmtj, e0, e1, ts, mf):
    '''
    计算TT主分支的偶极子指标:
        主分支匹配有效长度、主段匹配有效长度、非边界率
        主分支mtj、主段msg
        整体转角、所在象限、正负指向面积、偶极子距离的最大值、均值、均方差
        偶极子中心轨迹路径、跨度、弹性曲率
    '''
    p0, n0, mg0 = get_mf_dxyndy0(pmtj, nmtj, mf)  # 主分支
    p1, n1, mg1 = get_mf_dxyndy0(pmtj[e0[0]:e0[1]], nmtj[e1[0]:e1[1]], mf)  # 主段
    mts0 = cal_tt_mtj_dimts0(mg0, p0, n0, ts, 2, 3, 5, 96)  # 主分支13个参数
    mts1 = cal_tt_mtj_dimts0(mg1, p1, n1, ts, 2, 3, 5, 96)  # 主段13个参数
    mts2 = cal_tt_mtj_dimts1(mts0, mts1)  # 主分支-主段的13个参数差
    return mts0, mts1, mts2


def cal_disfact0(b5, b3):
    '''计算距离因子'''
    t1, t2 =  b5[0][0], b5[-1][0]
    b4 = []
    for i in range(len(b3)):
        if b3[i] != []:
            if b3[i][0]>=t1 and b3[i][0]<=t2:
                b4.append(b3[i])
    # b4 = [b3[j] for j in range(len(b3)) if b3[j][0]>=t1 and b3[j][0]<=t2]
    dsf = sum(product2(b4[i][2]-b5[i][2], b4[i][3]-b5[i][3]) for i in range(len(b5))) / len(b5)
    return dsf


def cal_lengfact0(b5, mf):
    '''计算长度因子: 极子*幅值, 求和'''
    lgf = 0
    for b6 in b5:
        i, a = b6[0], b6[1]
        lgf += a*mf[i]
    return lgf


def get_tt_stj_mts1(b20, mf, amf):
    '''次分支参数6个'''
    snbd, selg, sapa, sasp, saec, saep = 0, 0, 0, 0, 0, 0
    if len(b20) < 3:
        return [snbd, selg, sapa, sasp, saec, saep]
    else:
        b2, b3 = b20[1], b20[2]
    for i in range(len(b2)):
        nbd = max(0.1, round(cal_nonbd0(b2[i], 2, 3, 5, 96), 1))  # 边界因子
        dsf = max(0.1, min(1, round(cal_disfact0(b2[i], b3), 1)))  # 距离因子
        lgf = round(cal_lengfact0(b2[i], mf)*20/amf, 1)  # 长度因子
        xs = [b2[i][j][2] for j in range(len(b2[i]))]  # 横坐标
        ys = [b2[i][j][3] for j in range(len(b2[i]))]  # 纵坐标
        pa = sum([product2(xs[j+1]-xs[j], ys[j+1]-ys[j]) for j in range(len(xs)-1)])  # 路径
        sp = cal_max_dist0(xs, ys)  # 跨度
        snbd += nbd  # 有效分支数量/总非边界率
        selg += lgf*nbd  # 分支大小/总有效长度
        sapa += pa  # 总路径
        sasp += sp  # 总跨度
        if sp > 0:
            saec += pa/sp  # 总弹性曲率
            saep += pa*pa/sp/sp  # 总弹性强度
    snbd, selg, sapa, sasp, saec, saep = get_round([snbd, selg, sapa, sasp, saec, saep], [1, 1, 1, 1, 1, 1])
    return [snbd, selg, sapa, sasp, saec, saep]


def get_pts_lns3(b1, mf, files=''):
    '''次分支信息统计'''
    if files:
        b1 = txt_to_list(files)
    b2, t1, t2 = split_brs(b1, -1)  # 负极子
    amf = sum([mf[i][1] for i in range(len(mf)) if i>=t1 and i<t2])
    mf1 = [mf[i][1] for i in range(len(mf))]
    mt2 = get_tt_stj_mts1(b2, mf1, amf)
    b2, t1, t2 = split_brs(b1, 1)  # 正极子
    amf = sum([mf[i][0] for i in range(len(mf)) if i>=t1 and i<t2])
    mf0 = [mf[i][0] for i in range(len(mf))]
    mt1 = get_tt_stj_mts1(b2, mf0, amf)
    return mt1, mt2


def get_traject_mts0(matrixes, timelis0, mf):
    '''TT多极子轨迹参数'''
    brs = get_traject1(timelis0, 3, 5, matrixes)  # 统计全部分支
    brs2 = tichu_traject0(brs)  # 剔除小分支
    ts, pmtj, nmtj, xs1, ys1, xs2, ys2 = get_pts_lns2(brs2)  # 统计点集和线段集
    e0, mt0 = get_tt_mtj_mts0(ts, pmtj, xs1, ys1, 1)
    e1, mt1 = get_tt_mtj_mts0(ts, nmtj, xs2, ys2, -1)
    mt2, mt3, mt4 = get_tt_mtj_dimts0(pmtj, nmtj, e0, e1, ts, mf)
    mt5, mt6 = get_pts_lns3(brs2, mf)
    return [mt0, mt1, mt2, mt3, mt4, mt5, mt6]


def cal_tmphase0(lis, idx):
    '''计算时相参数6+1: QR, QRS, ST, ST1, TT, QT, noq, tm0'''
    noq, nos = lis[-2:]  # 1=noq
    # Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = finetune_tms0(lis[:-2], idx)
    Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = finetune_tms1(lis[:-2], idx)  # 心肌肥厚修改时刻点——
    tm0 = [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR]
    QR = Rp - Qh
    QRS = Sr - Qh
    ST = Tp - Sr
    ST1 = Tr - Sr
    TT = Tr - Th
    QT = Tr - Qh
    return [QR, QRS, ST, ST1, TT, QT, noq, tm0]


def get_mcg36(f0, md=1):
    '''获取36条心磁信息: N*36, 精度%.6f'''
    l1 = [get_item(get_lines1(f0), i+1, [], jd=6) for i in range(36)]
    if md:  # N*36
        l1 = [[l1[i][j] for i in range(len(l1))] for j in range(len(l1[0]))]
    return l1


def get_rms0(f0):
    mcgdata, lrms = get_lines1(f0), []
    for i in range(len(mcgdata)):
        asimg = np.array(mcgdata[i].split()[1:])
        asimg = asimg.astype(float)  # str转float
        vrms = np.sqrt(np.sum(asimg**2, axis=0) / asimg.shape[0])
        lrms.append(vrms)
    return lrms


def cal_er0(l3):
    '''斜率列表是否正常: 1正确, 0错误'''
    fg=1
    k0, l0 = l3[0], l3[1:]
    if not ifzj(l0):
        return 0
    # print(1)
    for i in range(len(l0)):
        if l0[i]*k0<0:
            fg=0
            break
    if fg==0:
        return fg
    # print(2)
    if k0<min(l0):
        return 0
    # print(3)
    if k0>max(l0):
        return 0
    # print(4)
    return fg


def cal_zf0(l1, i1, i2):
    '''时间波组图l1在区间iks上的最大正/负振幅: 输出36列表'''
    l11 = [max([l1[i][j] for i in range(i1, i2)]) for j in range(len(l1[0]))]
    l12 = [min([l1[i][j] for i in range(i1, i2)]) for j in range(len(l1[0]))]
    l13 = [0 for i in range(len(l11))]
    for i in range(len(l11)):
        if l11[i] >= -l12[i]:
            l13[i] = l11[i]
        else:
            l13[i] = l12[i]
    return l13


def cal_zhfu0(l1, tm0):
    '''计算QRS振幅参数5: ma-mi、ma、16maav-miav、Rma-mi、20ma-10mi'''
    Qp, Rp, Sp = tm0
    l2 = cal_zf0(l1, Qp, Sp+1)
    qrs = max(l2)-min(l2)  # 最大正-最大负
    qrs1 = max(l2)  # 仅最大正
    l21, l22 = [], []
    for i in range(36):
        if i > 5 and i < 30 and i%6 != 0 and i%6 != 5:
            if l2[i]>0:
                l21.append(l2[i])
            else:
                l22.append(l2[i])
    qrs2 = 0
    if l21:
        qrs2 = sum(l21)/len(l21)  # 16通道的正负分别平均, 求和
    if l22:
        qrs2 -= sum(l22)/len(l22)
    l2 = cal_zf0(l1, Rp, Rp+1)
    r = max(l2)-min(l2)  # R波最大正-最大负
    qrs3 = max([l1[i][20] for i in range(Qp, Sp+1)]) - min([l1[i][10] for i in range(Qp, Sp+1)])  # 两通道20/26-10
    return get_round([qrs, qrs1, qrs2, r, qrs3], [3, 3, 3, 3, 3])


def cal_qxarea0(l1, tm0):
    '''
    计算曲线下面积参数6: QRSma-mi、TTma-mi、qrs-tt、时长*振幅、中间16通道abs均值、最大值
    '''
    Qp, Sp, Th, Tr = tm0
    # Qp, Rp, Sp, To, Tp, Te = tm0
    qrs = sum([max(l1[i])-min(l1[i]) for i in range(Qp, Sp+1)])  # 面积
    tt = sum([max(l1[i])-min(l1[i]) for i in range(Th, Tr+1)])
    qrstt = qrs-tt
    zf = max([max(l1[i]) for i in range(Qp, Sp+1)])
    zf = max([zf]+[-min(l1[i]) for i in range(Qp, Sp+1)])
    qrs1 = (Sp-Qp+1) * zf
    l10 = []
    for j in range(len(l1)):
        l11 = []
        for i in range(len(l1[0])):
            if i > 5 and i < 30 and i%6 != 0 and i%6 != 5:
                l11.append(l1[j][i])
        l10.append(l11)
    l11 = [sum([abs(l10[j][i]) for j in range(Qp, Sp+1)]) for i in range(len(l10[0]))]
    qrs2 = sum(l11)/len(l11)
    qrs3 = max(l11)
    return get_round([qrs, tt, qrstt, qrs1, qrs2, qrs3], [3, 3, 3, 3, 3, 3])


def cal_integral0(l1, tm0):
    '''计算QRS积分参数7: QRSabs/N、TT、TTsum+abs+sum/N、qrs-tt、qrs/tt'''
    Qp, Sp, Th, Tr = tm0
    # Qp, Rp, Sp, To, Tp, Te = tm0
    qrs = sum([sum([abs(l1j) for l1j in l1[i]]) for i in range(Qp, Sp+1)])
    qrs = qrs/len(l1[0])
    tt1 = sum([sum([abs(l1j) for l1j in l1[i]]) for i in range(Th, Tr+1)])
    tt1 = tt1/len(l1[0])
    qrstt11 = qrs-tt1
    if tt1:
        qrstt12 = qrs/tt1
    else:
        qrstt12 = 0
    tt2 = sum([abs(sum([l1[i][j] for i in range(Th, Tr+1)])) for j in range(len(l1[0]))])
    tt2 = tt2/len(l1[0])
    qrstt21 = qrs-tt2
    if tt2:
        qrstt22 = qrs/tt2
    else:
        qrstt22 = 0
    tt3 = sum([sum([l1[i][j] for i in range(Th, Tr+1)]) for j in range(len(l1[0]))])
    tt3 = tt3/len(l1[0])
    # print(tt3)
    qrstt31 = qrs-tt3
    if tt3:
        qrstt32 = qrs/tt3
    else:
        qrstt32 = 0
    jds = [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
    return get_round([qrs, tt1, tt2, tt3, qrstt11, qrstt12, qrstt21, qrstt22, qrstt31, qrstt32], jds)


def cal_qrs_mami0(d0, d1):
    '''计算qrs最大最小: mess_path, data_path'''
    ids = get_lines1('%s/ids.txt' % d0)
    tms = txt_to_list('%s/times.txt' % d0)
    print(tms)
    print(len(tms))
    l01, l02 = [0 for i in range(36)], [0 for i in range(36)]
    for i in range(len(ids)):
        f0 = '%s/%s.txt' % (d1, ids[i])
        print(i+1, len(ids)-i-1)
        l01, l02 = cal_zhfu0(f0, tms[i], l01, l02)
    print(l01)
    print(l02)
    print(l01.index(max(l01)))
    print(l02.index(min(l02)))
    return d1


def cal_xgxs0(x, y):
    '''计算两个场图的相关系数: 等磁图xy拉成1维'''
    return np.corrcoef(x, y)[0, 1]


def calculate_average_angle(angles):
    '''计算平均角度'''
    adjusted_angles = [(angle % 360) if angle >= 0 else (angle % 360 + 360) for angle in angles]  # 调整0-360
    lis1 = [angle for angle in adjusted_angles if angle < 180]  # 分类0-180
    lis2 = [angle for angle in adjusted_angles if angle >= 180]  # 180-360
    n1 = len(lis1)
    n2 = len(lis2)
    a1 = sum(lis1) / n1 if n1 > 0 else 0
    a2 = sum(lis2) / n2 if n2 > 0 else 0
    if (a2 - a1 > 180) or (a1 - a2 > 180):  # >180类更偏向0线
        average_angle = (n1 * a1 + n2 * (a2-360)) / (n1 + n2)  # >180类取负
    else:
        average_angle = (n1 * a1 + n2 * a2) / (n1 + n2)  # 否则取正
    average_angle %= 360
    if average_angle < 0:  # 修正
        average_angle += 360
    return average_angle


def cal_meana0(Ms, i1, i2):
    '''统计和计算平均角度'''
    ags = [cal_zfzxjd0(Ms[i]) for i in range(i1, i2)]
    return calculate_average_angle(ags)


def cal_meana1(Ms, i1, i2):
    '''计算波段的平均角度'''
    M = np.mean(np.stack([Ms[i] for i in range(i1, i2+1)]), axis=0)
    px, py, nx, ny = get_dipole(M)
    return calangle(ny-py, px-nx)


def cal_zhxl0(ags, vs):
    '''计算综合向量: 角度集, 强度集'''
    agrd = np.deg2rad(ags)
    xs, ys = vs*np.cos(agrd), vs*np.sin(agrd)
    zhxl = np.sum(np.stack([xs, ys], axis=1), axis=0)
    return np.degrees(np.arctan2(zhxl[1], zhxl[0]))


def cal_meandz0(f0, Ms, i1, i2):
    '''计算QRS平均心电轴: 瞬时心电轴+强度幅值差, 向量综合, x轴正向夹角'''
    ags = [cal_zfzxjd0(Ms[i])+90 for i in range(i1, i2+1)]  # 电轴方向s
    mfs = get_mfs(get_lines1(f0), 1)
    vs = [mfs[i][0]-mfs[i][1] for i in range(i1, i2+1)]  # 电轴强度s
    return round(cal_zhxl0(ags, vs), 1)


def cal_ctmts0(matrixes, tm0):
    '''计算等磁图相关参数4: qrs平均角度、tt、qrs-tt、电轴dz'''
    Qp, Rp, Sp, Th, Tr = tm0
    qrs = cal_meana0(matrixes, Qp, Sp+1)
    tt = cal_meana0(matrixes, Th, Tr+1)
    qrstt = qrs - tt
    r = cal_meana0(matrixes, Rp, Rp+1)
    dz = (360-(r+90)) % 360
    return get_round([qrs, tt, qrstt, dz], [0, 0, 0, 0])


def cal_stph0(l1, tm0):
    '''计算复极段相对平滑指数:ST段每20ms取一张等磁图5-8, 连续两图的相关系数/N-1'''
    Sr, Th = tm0
    iks = [i for i in range(Sr, Th+1) if (i-Sr)%20==0]
    if len(iks) < 2:
        return 1
    else:
        stph = sum([cal_xgxs0(l1[iks[i]], l1[iks[i+1]]) for i in range(len(iks)-1)])
        stph = stph/len(iks[1:])
        return round(stph, 3)


def cal_dpmm0(l0, t0):
    '''计算偶极矩参数: Qh+50时刻'''
    l1 = l0[t0]
    d = max(l1) - min(l1)
    ma = max(l1)
    return round((ma*d/2) / 0.385, 3)


def if_rgt_dw0(i):
    '''T波负磁极是否位于下半区'''
    negl = 0
    if int((i+1)/6) > 3:
        # if (i+1)%6 > 3:
        negl = 1
    return negl


def cal_rgt_dw0(l1):
    '''T波右下角负向的比例'''
    n0 = 0
    for i in range(len(l1)):
        if int((i+1)/6) > 3 and (i+1)%6 > 3:
            if l1[i] < 0:
                n0 += 1
    return round(n0 / 9, 3)


def cal_qrsttzfiv(l1, l0):
    '''计算QRS段最大振幅方向与T波相反的通道比例'''
    n0 = 0
    for i in range(len(l1)):
        l2 = l0[i]  # 每个通道波
        if l1[i]>0 and max(l2)+min(l2)<0:
            n0 += 1
        elif l1[i]<0 and max(l2)+min(l2)>0:
            n0 += 1
    if n0 == 0 or n0 == 36:
        return 0
    else:
        return round(n0 / (36-n0), 3)


def cal_zfzxjd0(M):
    '''正负指向角度'''
    px, py, nx, ny = get_dipole(M)
    return calangle(ny - py, px - nx)


def cal_twivs0(l0, ms, tm0):
    '''计算T波倒置参数5: 负正振幅比、负磁极右下、负向波右下占比、QRS&T异号比、T波角度'''
    Qp, Sp, Tp = tm0
    l1 = l0[Tp]
    nprt = round(abs(min(l1) / max(l1)), 3)
    negl = if_rgt_dw0(l1.index(min(l1)))
    negn = cal_rgt_dw0(l1)
    l2 = [[l0[i][j] for i in range(Qp, Sp+1)] for j in range(len(l0[0]))]
    qrsttn = cal_qrsttzfiv(l1, l2)
    ta = cal_zfzxjd0(ms[Tp])
    return [nprt, negl, negn, qrsttn, ta]


def cal_stcg0(l0, tm0):
    '''计算ST改变参数5: T波振幅、Sr振幅比例nf、压低比例nr、pf、pr'''
    Sr, Th, Tp = tm0
    l1, l2, l3 = l0[Tp], l0[Sr], l0[Th]
    Tzf = max(max(l1), -min(l1))
    nf = -min(l2) / Tzf
    nr = min(l3) / min(l2)
    pf = max(l2) / Tzf
    pr = max(l3) / max(l2)
    return get_round([Tzf, nf, nr, pf, pr], [3, 3, 3, 3, 3])


def cal_ojjadz0(f0, Ms, tm0, l11):
    '''计算偶极矩/平均角度/电轴'''
    Qh, Qp, Sp, Th, Tr = tm0
    ys = [cal_dpmm0(l11, Qh+j) for j in range(71)]
    j2 = ys.index(max(ys))  # 最大偶极矩位置完整
    ik = [math.ceil(j2/10)*10, math.floor(j2/10)*10]
    ls = [ys[ik[0]], ys[ik[1]]]
    j1 = ik[ls.index(max(ls))]  # 最大偶极矩位置10
    ojjs = [ys[j*10] for j in range(8)]
    qrs = cal_meana1(Ms, Qp, Sp)  # 平均角度
    tt = cal_meana1(Ms, Th, Tr)  # 平均角度
    qrstt = qrs-tt
    dz1 = (360-(qrs+90)) % 360  # 电轴
    dz20 = cal_meandz0(f0, Ms, Qp, Sp)
    dz2 = (360-dz20) % 360
    jds = [3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 1, 1, 1, 1, 1]
    return get_round(ojjs+[j1, j2, qrs, tt, qrstt, dz1, dz2], jds)


def get_hcm_mts0(f0, Ms, timelis0):
    '''计算心肌肥厚参数: 30+11'''
    lis = gettime(f0, noqs=1)
    l1 = cal_tmphase0(lis, timelis0)
    Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = l1[-1]
    l11 = get_mcg36(f0)  # 提取36波数值n*36
    l2 = cal_zhfu0(l11, [Qp, Rp, Sp])  # QRS振幅
    l3 = cal_qxarea0(l11, [Qp, Sp, Th, Tr])  # 曲线下面积
    l4 = cal_integral0(l11, [Qp, Sp, Th, Tr])  # 积分
    stph = cal_stph0(l11, [Sr, Th])  # ST相对平滑指数
    l5 = cal_ctmts0(Ms, [Qp, Rp, Sp, Th, Tr])  # 平均角度/电轴
    dpmm = cal_dpmm0(l11, Qh+50)  # 偶极矩Qh+50
    l6 = cal_twivs0(l11, Ms, [Qp, Sp, Tp])  # T波倒置参数
    l7 = cal_stcg0(l11, [Sr, Th, Tp])  # ST改变参数
    l8 = cal_ojjadz0(f0, Ms, [Qh, Qp, Sp, Th, Tr], l11)  # 偶极矩/平均角度/电轴
    ls = l1[:-1]+l2+l3+l4+[stph]+l5+[dpmm]+l6+l7+l8
    # QRS段修正为Qh-Sr
    l3 = cal_qxarea0(l11, [Qh, Sr, Th, Tr])  # 曲线下面积
    ls += [l3[0]]+l3[2:]
    l4 = cal_integral0(l11, [Qh, Sr, Th, Tr])  # 积分
    ls += [l4[0]]+l4[4:]
    l5 = cal_ctmts0(Ms, [Qh, Rp, Sr, Th, Tr])  # 平均角度/电轴
    ls += [l5[0], l5[2]]
    l6 = cal_twivs0(l11, Ms, [Qh, Sr, Tp])  # T波倒置参数
    ls += [l6[3]]
    l8 = cal_ojjadz0(f0, Ms, [Qh, Qh, Sr, Th, Tr], l11)  # 偶极矩/平均角度/电轴
    ls += [l8[10]]+l8[12:]
    return ls


def draw_dpmm0(ys, g1):
    '''绘制偶极矩变化曲线Qh+70'''
    xs = [i+1 for i in range(len(ys))]
    iks = [i for i in range(1, len(xs)-1) if xs[i]>xs[i-1] and xs[i]>xs[i+1]]
    xss1 = [xs[i] for i in iks]
    yss1 = [ys[i] for i in iks]
    xss = [xs[i] for i in range(len(xs)) if i not in iks]
    yss = [ys[i] for i in range(len(ys)) if i not in iks]
    plt.rcParams['savefig.dpi'] = 1024
    plt.plot(xs, ys, c='k', linewidth=0.5)
    plt.scatter(xss, yss, c='k', s=2)
    plt.scatter(xss1, yss1, c='r', s=2)
    plt.scatter(xs[ys.index(max(ys))], max(ys), c='r', s=5)
    plt.savefig(g1)
    plt.close()


def get_hcm_mts1(f0, Ms, timelis0):
    '''计算心肌肥厚参数1-补充: 30+11'''
    lis = gettime(f0, noqs=1)
    # l1 = cal_tmphase0(lis, timelis0)
    l11 = get_mcg36(f0)
    Qh = lis[3]
    Qp, Rp, Sp, To, Tp, Te = timelis0
    ys = [cal_dpmm0(l11, Qh+j) for j in range(71)]
    j2 = ys.index(max(ys))  # 最大偶极矩位置完整
    ik = [math.ceil(j2/10)*10, math.floor(j2/10)*10]
    ls = [ys[ik[0]], ys[ik[1]]]
    j1 = ik[ls.index(max(ls))]  # 最大偶极矩位置10
    ojjs = [ys[j*10] for j in range(8)]
    qrs = cal_meana1(Ms, Qp, Sp)  # 平均角度
    tt = cal_meana1(Ms, To, Te)  # 平均角度
    qrstt = qrs-tt
    dz1 = (360-(qrs+90)) % 360  # 电轴
    dz20 = cal_meandz0(f0, Ms, Qp, Sp)
    dz2 = (360-dz20) % 360
    jds = [3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 1, 1, 1, 1, 1]
    return get_round(ojjs+[j1, j2, qrs, tt, qrstt, dz1, dz2], jds)


def get_pcdm_mts(timelis0, matrixes, dfs, Zqr, Zrs, Ztt):
    '''
    等磁图和电流密度图指标: QR, RS, TT
    timelis: 时刻点
    mf: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    frfile = cal_fr(matrixes, dfs, q_peak, r_peak)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Zqr, nsik=5)
    mtsall = [[cdfile, mpfile, mjfile, mrfile, mafile]]
    frfile = cal_fr(matrixes, dfs, r_peak, s_peak)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Zrs, nsik=5)
    mtsall.append([cdfile, mpfile, mjfile, mrfile, mafile])
    frfile = cal_fr(matrixes, dfs, t_onset, t_end)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Ztt, nsik=5)
    mtsall.append([cdfile, mpfile, mjfile, mrfile, mafile])
    return mtsall


def change_mode0(lis0, p11, mode=0):
    if mode == 1:
        p01 = 0.405
        if lis0[-1] < p01:
            if sum(1 for i7 in range(len(lis0)) if lis0[i7] < p01)>len(lis0)*0.5:
                p11, mode = p01, 0
    elif mode == 2:
        p01 = 0.596
        if lis0[-1] > p01:
            if sum(1 for i7 in range(len(lis0)) if lis0[i7] > p01)>len(lis0)*0.5:
                p11, mode = p01, 3
    return p11, mode


def make_ranks1(lis1, lis2, d2=0, item=0):
    n1 = max(len(lis1), len(lis2))
    n0 = 0.14 * n1  # 分级的最小数量-先验设置
    v1 = min(lis1+lis2)-1
    if d2:
        f2, f3 = '%s/%s_tr0.txt' % (d2, item), '%s/%s_tr1.txt' % (d2, item)
    else:
        f2, f3 = '', ''
    xys0 = make_ranks(lis1, lis2, f2=f2, mode=0, v1=v1)
    rlis = make_rank1(xys0, n0, n1, [])
    v1 = max(lis1+lis2)+1
    xys1 = make_ranks(lis1, lis2, f2=f3, mode=1, v1=v1)
    xys1.reverse()
    rlis = make_rank1(xys1, n0, n1, rlis)  # xys1逆序搜索分级区间
    xys2 = [50]  # 默认全50
    if rlis:  # 为空
        xys2 = make_ranks(lis1, lis2, lis0=rlis)
        xys2 = [round(xys2[i3][4]*100) for i3 in range(len(xys2))]
        try:
            if rlis[0] >= rlis[1]:
                if abs(xys2[0]-50) > abs(xys2[1]-50):
                    rlis = [rlis[0]]
                else:
                    rlis = [rlis[1]]
                xys2 = make_ranks(lis1, lis2, lis0=rlis)
                xys2 = [round(xys2[i3][4]*100) for i3 in range(len(xys2))]
        except:
            pass
    return rlis, xys2


def make_rank1(xys0, n0, n1, rlis):
    '''指标初步分级(指标筛选): xys0累加列表, n0n1最小最大数量, rlis分级列表'''
    st0 = -1  # 初始设定
    i3 = sum(1 for ik in range(len(xys0)) if xys0[ik][3] < n0)  # 起始位置
    if i3 == 0:  # 搜索是否可分
        if xys0[i3][4] <= 0.49 or xys0[i3][4] >= 0.51:
            st0 = i3
    else:  # 阴阳比>=1.5, 可分
        lis0 = [xys0[j3][4] for j3 in range(i3+1)]
        if cal_posnegrt(lis0, xys0[i3][4]):
            st0 = i3
        elif i3+4 < len(xys0):
            for k3 in range(1, 4):
                if xys0[i3+k3][3] >= n1:
                    break
                lis0 = [xys0[j3][4] for j3 in range(i3+k3+1)]
                l = min(max(i3+k3-2, 4), i3+k3+1)  # 去掉3个&长度>=4
                if cal_posnegrt(lis0[-l:], lis0[-1]):
                    st0 = i3+k3
                    break
    if st0 != -1:  # 可分级
        st1, p0 = -1, xys0[st0][4]
        if p0 <= 0.4:
            p01, mode = 0.405, 0
        elif p0 < 0.5:
            p01, mode = 0.49, 1
        elif p0 >= 0.6:
            p01, mode = 0.596, 3
        else:
            p01, mode = 0.51, 2
        for i3 in range(st0+1, len(xys0)):  # 滑动搜索, 概率稳定区间
            if i3 >= n1:
                break
            lis0 = [xys0[j3][4] for j3 in range(i3+1)]
            l = min(10, i3+1)
            p01, mode = change_mode0(lis0[-l:], p01, mode=mode)
            if cal_exceptrt(lis0[-l:], p01, mode=mode):  # 截止位置
                st1 = get_loc_unexcept(lis0, l, st0, i3, p01, mode=mode)
                break
        if st1 == -1:  # 没有截止
            lis0 = [xys0[j3][4] for j3 in range(i3)]
            l = min(10, i3)
            st1 = get_loc_unexcept(lis0, l, st0, i3-1, p01, mode=mode)
        rlis.append(round(xys0[max(st0, st1)][0], 3))
    return rlis


def cal_exceptrt(lis0, p01, mode=0):
    '''例外比例, p01端>一半'''
    if mode < 2:
        n1 = sum(1 for i7 in range(len(lis0)) if lis0[i7] > p01)
    else:
        n1 = sum(1 for i7 in range(len(lis0)) if lis0[i7] < p01)
    return n1>len(lis0)*0.5


def cal_posnegrt(lis0, p0):
    '''阴阳比例, p0端>=1.5'''
    n1 = sum(1 for i7 in range(len(lis0)) if lis0[i7] > 0.5)
    n2 = sum(1 for i7 in range(len(lis0)) if lis0[i7] < 0.5)
    if p0 >= 0.5:
        return n1>=n2*1.5
    else:
        return n2>=n1*1.5


def get_loc_unexcept(lis1, l, st0, ik0, p01, mode=0):
    '''获取列表稳定位置索引: ik0末尾源索引, p01概率值, mode模式'''
    lis0 = lis1[-l:]
    st1 = ik0-len(lis0)+1
    if mode == 0:  # 40
        for i7 in range(len(lis0)):
            if lis0[-1-i7] <= p01:
                st1 = ik0-i7
                break
    elif mode == 3:  # 60
        for i7 in range(len(lis0)):
            if lis0[-1-i7] >= p01:
                st1 = ik0-i7
                break
    else:
        lis0 = []
        if mode == 1:  # 截取一段有效概率
            for i7 in range(max([st0-2, 0, ik0-9]), ik0+1):
                if lis1[i7] >= p01:
                    break
                lis0.append(round(lis1[i7], 2))
        else:
            for i7 in range(max([st0-2, 0, ik0-9]), ik0+1):
                if lis1[i7] <= p01:
                    break
                lis0.append(round(lis1[i7], 2))
        st1, ik0 = i7, i7-1
        for i7 in range(len(lis0)-2):  # 连续稳定3个
            if abs(lis0[-1-i7] - sum(lis0[-3-i7:][:3])/3) < 0.01:
                st1 = ik0-i7
                break
        if st1 == ik0+1:
            if len(lis0) > 2:  # 前2个稳定
                if abs(lis0[0] - lis0[1]) < 0.02:
                    st1 = ik0-len(lis0)+2
        if st1 == ik0+1:
            if mode == 1:  # 49
                for i7 in range(1, len(lis0)-1):
                    if lis0[-1-i7] < lis0[-i7] and lis0[-1-i7] < lis0[-2-i7]:
                        st1 = ik0-i7
                        break
            else:  # 51
                for i7 in range(1, len(lis0)-1):
                    if lis0[-1-i7] > lis0[-i7] and lis0[-1-i7] > lis0[-2-i7]:
                        st1 = ik0-i7
                        break
    return min(st1, ik0)


def count_decimal_places(rlis):
    '''计算小数点位置'''
    jqws = 0
    for i in range(len(rlis)):
        stnm = str(rlis[i])
        dcidx = stnm.find('.')
        if dcidx == -1:
            pass
        else:
            jqws = max(jqws, len(stnm)-dcidx-1)
    return jqws


def stat_mtstrs_intp_pre(mtsall, mtstrs, item):
    '''统计指标-字符串'''
    for i2 in range(len(mtsall)):  # 4
        for j2 in range(len(mtsall[i2])):  # 6
            mtstrs[i2][j2] += '\n%s, %s' % (item, str(mtsall[i2][j2])[1:-1])
    return mtstrs


def extract_metr2(tx1, d0, d1):
    '''从原始指标文档(3标签)中整合mfm_pcdm指标: 有重复'''
    d2 = '%s/%s' % (d1, tx1)
    new_folder(d2)
    rtdir = '%s/spect' % d0
    im1 = os.listdir(rtdir)
    fileliss, strliss = [], []
    for i1 in range(len(im1)):
        dir1 = '%s/%s' % (rtdir, im1[i1])
        new_folder('%s/%s' % (d2, im1[i1]))
        files, strs = [], []
        for item in os.listdir(dir1):
            files.append('%s/%s' % (im1[i1], item))
            str0 = ''.join(get_lines('%s/%s' % (dir1, item)))
            strs.append(str0)
        fileliss.append(files)
        strliss.append(strs)
    for i1 in range(len(fileliss)):
        for j1 in range(len(fileliss[i1])):
            for ths in ['qfr', 'imr']:
                files = '%s/%s/%s' % (d0, ths, fileliss[i1][j1])
                str0 = ''.join(get_lines(files)[1:])
                strliss[i1][j1] += '\n' + str0
            files = '%s/%s' % (d2, fileliss[i1][j1])
            write_str(strliss[i1][j1], files)
    return d2


def extract_metr3(tx1, tx2, d1):
    '''按照space指标中的id顺序, 提取和排列mfm_pcdm指标: 去重复'''
    f2 = '%s/%s' % (d1, tx2)
    d2 = '%s/%s_raw' % (d1, tx1)
    d3 = '%s/%s' % (d1, tx1)
    new_folder(d3)
    im1 = os.listdir(d2)
    fileliss, strliss = [], []
    for i1 in range(len(im1)):
        dir1 = '%s/%s' % (d2, im1[i1])
        new_folder('%s/%s' % (d3, im1[i1]))
        files, strs = [], []
        for item in os.listdir(dir1):
            files.append('%s/%s' % (im1[i1], item))
            str0 = get_lines1('%s/%s' % (dir1, item))
            strs.append(str0)
        fileliss.append(files)
        strliss.append(strs)
    f2ls = get_lines(f2)  # 提取全部行
    f2ls = get_item(f2ls[1:], 0, [])  # 提取全部id
    idxs = get_idxs(strliss[0][0], f2ls, [0])  # 匹配序号列表
    for i1 in range(len(fileliss)):
        for j1 in range(len(fileliss[i1])):
            str0 = '\n'.join(strliss[i1][j1][k1] for k1 in idxs)
            files = '%s/%s' % (d3, fileliss[i1][j1])
            write_str(str0, files)
    return d3


def extract_metr4(f1, tx3, d2):
    '''提取表格中的全部信息'''
    new_folder(d2)
    f2 = '%s/%s.txt' % (d2, tx3)
    data1 = pd.read_excel(f1, sheet_name=tx3)
    strs = ', '.join(data1.columns)
    strs = addtrte(data1.values, strs, [])
    f3 = write_str(strs, f2)
    return f3


def extract_metr5(d0, tx3, tx1, f2ls):
    '''提取spect/qfr/imr标签下训练和测试集的id索引: 用于指标操作'''
    mslis = [[[], []], [[], []], [[], []]]  # id分类-spect/qfr/imr, id+0/1
    idths = [3, 4, 1]  # spect/qfr/imr.txt对应标签位置
    for i1 in range(len(tx3)):
        f3ls = get_lines('%s/%s/%s.txt' % (d0, tx1[0], tx3[i1]))
        f3ids = get_item(f3ls[1:], 0, [])
        ids0 = get_item(f3ls[1:], idths[i1], [])
        mslis[i1][0] = [f3ids[j1] for j1 in range(len(f3ids)) if ids0[j1] == 0]
        mslis[i1][1] = f3ids[len(mslis[i1][0]):]  # 后面l1
    idxlis = [[[], []], [[], []], [[], []], [[], []], [[], []], [[], []]]
    for i1 in range(len(tx3)):  # 提取id
        for j1 in range(2):
            f4ls = get_lines('%s/%s/%s.txt' % (d0, tx1[j1+1], tx3[i1]))
            idss = get_item(f4ls[1:], 0, [])
            idl0 = [idss[k1] for k1 in range(len(idss)) if idss[k1] in mslis[i1][0]]
            idxlis[2*i1+j1][0] = get_idxs(f2ls, idl0, [])
            idl1 = [idss[k1] for k1 in range(len(idss)) if idss[k1] in mslis[i1][1]]
            idxlis[2*i1+j1][1] = get_idxs(f2ls, idl1, [])
    return idxlis


def get_seg0(l1, fd=10):
    '''分割线: 返回n+1个指标列表, n折; s步长'''
    s = int(len(l1)/fd) + 1  # fd折步长
    l2 = sorted(l1.copy())
    segmt = [l2[0]-0.1]
    for i3 in range(fd+1):
        m0 = l2[s-1]
        if m0 == l2[0]:
            l2 = [l2[j3] for j3 in range(len(l2)) if l2[j3] != m0]
            segmt.append((m0+l2[0])/2)  # 添加分割线
        else:
            l3 = [l2[j3] for j3 in range(len(l2)) if l2[j3] == m0]
            if len(l3) < s and m0 != l2[-1]:
                l2 = [l2[j3] for j3 in range(len(l2)) if l2[j3] > m0]
                segmt.append((m0+l2[0])/2)  # 添加分割线
            elif len(l3) >= s:
                l3 = sorted(set(l2[:s]))
                segmt.append((l3[-1]+l3[-2])/2)
                l2 = [l2[j3] for j3 in range(len(l2)) if l2[j3] > m0]
                if len(l2) > 0:
                    segmt.append((m0+l2[0])/2)  # 添加分割线
        if len(l2) <= s or len(set(l2)) < 2:  # 剩余不可划分
            break
    segmt.append(max(l1)+0.1)
    return segmt


def get_split(strs0):
    '''获取分割后的字符串列表'''
    strlis00 = re.split(' |,|\t', strs0.strip())
    strlis00 = rm_null(strlis00)
    return strlis00


def extractmt(files):
    '''提取矩阵'''
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    Z = np.zeros((100, 100))
    for i1 in range(len(lines)):
        line = get_split(lines[i1])
        for j1 in range(1, len(line)):
            Z[i1][j1 - 1] = int(line[j1])
    return Z


def find_index(lists, tx1):
    '''寻找列表中字符tx1所在的字符串的位置索引'''
    try:
        p = next(i for i, item in enumerate(lists) if tx1 in item)
    except StopIteration:
        pass
    return p


def getAreaOfPolyGonbyVector(points):  # 基于向量叉乘计算多边形面积
    area = 0
    if (len(points) < 3):
        return 0
        # raise Exception("error")
    for i1 in range(0, len(points) - 1):
        p1 = points[i1]
        p2 = points[i1 + 1]
        triArea = (p1[0] * p2[1] - p2[0] * p1[1]) / 2
        area += triArea
    fn = (points[-1][0] * points[0][1] - points[0][0] * points[-1][1]) / 2
    return abs(area + fn)


def get_arc(p1, p0):  # 反正切极角
    if (p1[0] - p0[0]) == 0:
        if ((p1[1] - p0[1])) == 0:
            return -1
        else:
            return math.pi / 2
    tan = float((p1[1] - p0[1])) / float((p1[0] - p0[0]))
    arc = math.atan(tan)
    if arc >= 0:
        return arc
    else:
        return math.pi + arc


def get_dipole(matrix):
    '''计算偶极子坐标'''
    px, py = np.unravel_index(np.argmax(matrix), matrix.shape)
    nx, ny = np.unravel_index(np.argmin(matrix), matrix.shape)
    return px, py, nx, ny


def get_dipole_ct(cx, cy):
    '''偶极子中心统计'''
    cors = []
    for x1 in [int(cx), round(cx)]:
        for y1 in [int(cy), round(cy)]:
            x1 = max(0, min(99, x1))  # 端点控制
            y1 = max(0, min(99, y1))
            if [x1, y1] not in cors:
                cors.append([x1, y1])
    return cors


def get_ht(frfile, ik=1):
    '''获取首尾值'''
    return frfile[0][ik], frfile[-1][ik]


def get_idstms(biao1, biaodan, mode='idtm'):
    '''提取ids和时刻点(广东南海), 1+6(有标题); mode=id时只取ids'''
    data = pd.read_excel(biao1, sheet_name=biaodan).values
    data_raw = data[:, :7]
    ids, timelis = [], []
    for i2 in range(data_raw.shape[0]):
        ids.append(str(data_raw[i2, 0]))
        if mode == 'idtm':
            tms = []
            for j2 in range(1, 7):
                tms.append(int(str(data_raw[i2, j2])))
            timelis.append(tms)
    if mode == 'id':
        return ids
    else:
        return ids, timelis


def get_idxs(ls1, ls2, lis=[]):
    '''获取ls2在ls1中的匹配索引: 以ls2的顺序, lis初始化索引'''
    for i4 in range(len(ls2)):
        lis.append(find_index(ls1, ls2[i4]))
    return lis


def gen_isdir_list(dir_name):
    '''生成文件夹列表'''
    files = os.listdir(dir_name)
    isdir_list = []
    for f in files:
        if os.path.isdir(dir_name + '/' + f):
            isdir_list.append(True)
        else:
            isdir_list.append(False)
    return isdir_list


def get_item(f2ls, ik=0, lis0=[], jd=3):
    '''提取文档列表的第ik个元素'''
    lis0 = []
    for i4 in range(len(f2ls)):
        row0 = get_split(f2ls[i4])
        lis0.append(get_item_real(row0[ik], jd=jd))
    return lis0


def get_item_real(item, jd=3):
    try:
        return int(item)
    except:
        try:
            item = float(item)
            return get_round(item, jd)
        except:
            if '\'' in item:  # 去除所有字符串符号''
                return get_item_real(item[1:-1])
            else:
                return str(item)


def get_lines(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    return lines


def get_lines1(files):
    '''获取文档内容, 去除换行府'''
    lines = get_lines(files)
    lines0 = []
    for i3 in range(len(lines)):
        lines0.append(lines[i3].replace('\n', ''))
    return lines0


def get_mfm_mts_QR(timelis, matrixes_list, mflis, Zqr):
    '''
    等磁图指标QR
    timelis: 时刻点
    mflis: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics QR...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        q_peak, r_peak = timelis[i1][0], timelis[i1][1]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        pnptm2 = cal_pnptm(matrixes, mflis0, range(q_peak-5, r_peak+1))
        q_peak = change_QR(pnptm2, q_peak)  # Q异常修正
        timelis[i1][0] = q_peak
        frfile, rtfile = cal_frrt(matrixes, q_peak, r_peak)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zqr)
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_RS(timelis, matrixes_list, mflis, Zrs):
    '''
    等磁图指标RS
    timelis: 时刻点
    mflis: 最大幅值
    Zrs: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics RS...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        r_peak, s_peak = timelis[i1][1], timelis[i1][2]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        pnptm2 = cal_pnptm(matrixes, mflis0, range(r_peak, s_peak+6))
        s_peak = change_RS(pnptm2, s_peak)  # S异常修正
        timelis[i1][2] = s_peak
        frfile, rtfile = cal_frrt(matrixes, r_peak, s_peak)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zrs)
        print(cdfile)
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_TT(timelis, matrixes_list, mflis, Ztt):
    '''
    等磁图指标TT
    timelis: 时刻点
    mflis: 最大幅值
    Ztt: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics TT...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        t_onset, t_peak, t_end = timelis[i1][3], timelis[i1][4], timelis[i1][5]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        frfile, rtfile = cal_frrt(matrixes, t_onset, t_end)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt, t_idx=t_peak-t_onset, mode='tt')
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_TT0(timelis, matrixes_list, mcglines_list):
    '''
    等磁图指标TT-初版
    timelis: 时刻点
    mflis: 最大幅值
    输出指标列表:
        area, rotation, quadrant, m1 m2 a1 a2, TT50 TT75 Tangle
    '''
    mpfiles = []
    for i1 in range(len(matrixes_list)):
        t_onset, t_peak, t_end = timelis[i1][3], timelis[i1][4], timelis[i1][5]
        matrixes, mcglines = matrixes_list[i1], mcglines_list[i1]
        mpfile = writemetric_tt0(matrixes, mcglines, t_onset, t_peak, t_end)
        mpfiles.append(mpfile)
    return mpfiles


def get_mfs(mlines, md=0):
    mf = []
    # if not Qp:
    #     Qp, Te = 10, len(mlines) - 10
    mf1 = []
    for ik in range(len(mlines)):
        mcgdata = mcg_float(mlines, ik)
        mf.append(max(mcgdata.max(), -1 * mcgdata.min()))
        mf1.append([mcgdata.max(), mcgdata.min()])
        # if ik < Qp - 10 or ik >= Te + 10:
        #     mf.append(0)
        # else:
        #     mcgdata = mcg_float(mlines, ik)
        #     mf.append(max(mcgdata.max(), -1 * mcgdata.min()))
    if md == 0:
        return mf
    else:
        return mf1


def get_offxy_mat(mfm_fine, dfs):
    '''获取初始信息: 最大强度、方向、坐标、矩阵'''
    diff_xx, diff_yy = dfs
    diff_mfm_x = np.diff(mfm_fine, axis=1)
    diff_mfm_x = np.hstack((diff_mfm_x, diff_mfm_x[:, -1].reshape(-1, 1)))
    diff_mfm_y = np.diff(mfm_fine, axis=0)
    diff_mfm_y = np.vstack((diff_mfm_y, diff_mfm_y[-1, :]))
    current_x = diff_mfm_y / diff_yy
    current_y = -diff_mfm_x / diff_xx
    mat_magni = np.sqrt(current_x**2 + current_y**2)
    v = np.max(mat_magni) * 0.232515 / 1.162577  # 等比例匹配NI的强度值
    px, py = np.unravel_index(np.argmax(mat_magni), mat_magni.shape)
    # print('shape', mat_magni)
    # print(px, py)
    mat_angle = np.arctan2(current_y, current_x) * (180 / np.pi)
    max_magni = np.max(mat_magni)
    mat_magni1 = mat_magni / max_magni
    offset_x = mat_magni1 * np.cos(np.deg2rad(mat_angle))
    offset_y = mat_magni1 * np.sin(np.deg2rad(mat_angle))
    dx = offset_x[px, py]
    dy = offset_y[px, py]
    ca = calangle(dx, -dy)  # 电流角度
    offset_x = offset_x[::8, ::8]
    offset_y = offset_y[::8, ::8]
    mat_magni = mat_magni[::8, ::8]
    return v, ca, px, py, offset_x, offset_y, mat_magni


def get_pts(matrixes, q_peak, r_peak):
    '''从矩阵计算点集'''
    cjpts = []
    for j in range(q_peak, r_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        px, py, nx, ny = get_dipole(matrix)
        cx = (px + nx) / 2  # 中心点坐标和坐标
        cy = (py + ny) / 2
        cors = get_dipole_ct(cx, cy)
        for cor in cors:
            if cor not in cjpts:
                cjpts.append(cor)
    return cjpts


def get_pts1(matrixes, dfs, q_peak, r_peak):
    '''从矩阵计算点集-主电流位置'''
    cjpts = []
    for j in range(q_peak, r_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        v, ca, cx, cy, offset_x, offset_y, mat_magni = get_offxy_mat(matrix, dfs)
        if [cx, cy] not in cjpts:
            cjpts.append([cx, cy])
    return cjpts


def get_qrs_t12(mcglines, locp, nl, vf):
    '''Q/R/S有效波段获取'''
    mcgdata = mcg_float(mcglines, locp)
    max_t = max(mcgdata.ravel()) * vf  # TT的1/3数值，有效波段——
    max_t2 = -1 * min(mcgdata.ravel()) * vf
    if max_t >= max_t2:
        t1, t2 = get_qrs_valid_p(mcglines, locp, nl, max_t)
    else:
        t1, t2 = get_qrs_valid_n(mcglines, locp, nl, -1*max_t2)
    return t1, t2


def get_qrs_valid_n(mcglines, locp, nl, max_t):
    '''QRS有效波段, 以负向波判断'''
    t1, t2 = locp, locp + 1
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp-jj)
        if min(mcgdata.ravel()) > max_t:
            t1 = locp - jj  # 有效波段起点
            break
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp+jj+1)
        if min(mcgdata.ravel()) > max_t:
            t2 = locp+jj+1  # 有效波段终点
            break
    return t1, t2


def get_qrs_valid_p(mcglines, locp, nl, max_t):
    '''QRS有效波段, 以正向波判断'''
    t1, t2 = locp, locp + 1
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp-jj)
        if max(mcgdata.ravel()) < max_t:
            t1 = locp - jj  # 有效波段起点
            break
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp+jj+1)
        if max(mcgdata.ravel()) < max_t:
            t2 = locp+jj+1  # 有效波段终点
            break
    return t1, t2


def get_round(lis0, ik=3):
    '''精确到ik位小数, ik为list时按照列表'''
    if type(lis0) == list:
        return [get_round(lis0[i3], ik[i3]) for i3 in range(len(ik))]
    else:  # 单个
        if ik == 0:
            return round(lis0)
        else:
            return round(lis0, ik)


def get_stabmgs(tim, lis0, mode='QR'):
    '''获取稳定性信息: 幅值, 正负离散度, 转角'''
    dpp0, dpps, dpns, pntts = [], [], [], []
    if mode == 'QR':
        for j1 in range(tim):
            dpp0.append(lis0[j1][2])
            dpps.append(abs(lis0[j1][2] - lis0[j1+1][2]))
            dpns.append(abs(lis0[j1][3] - lis0[j1+1][3]))
            pntts.append(abs(calrot(lis0[j1][4], lis0[j1+1][4])))
    else:  # 'RS'
        for j2 in range(1, tim):
            j1 = tim - j2
            dpp0.append(lis0[j1][2])
            dpps.append(abs(lis0[j1][2] - lis0[j1-1][2]))
            dpns.append(abs(lis0[j1][3] - lis0[j1-1][3]))
            pntts.append(abs(calrot(lis0[j1][4], lis0[j1-1][4])))
    return dpp0, dpps, dpns, pntts


def get_tt12(mcglines, t_onset, t_peak, t_end, mode='p'):
    '''TT有效波段获取, mode=pn时以正负向波较大值判断'''
    mcgdata = mcg_float(mcglines, t_peak)
    max_t = max(mcgdata.ravel()) / 3  # TT的1/3数值，有效波段——
    if mode == 'p':
        tt1, tt2 = get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t)
    else:  # mode='pn'
        max_t2 = -1 * min(mcgdata.ravel()) / 3
        if max_t >= max_t2:
            tt1, tt2 = get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t)
        else:
            tt1, tt2 = get_tt_valid_n(mcglines, t_onset, t_peak, t_end, -1*max_t2)
    return tt1, tt2


def get_tt_valid_n(mcglines, t_onset, t_peak, t_end, max_t):
    '''TT有效波段, 以负向波判断'''
    tt1, tt2 = t_onset, t_end
    for jj in range(t_onset, t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if min(mcgdata.ravel()) < max_t:
            tt1 = jj  # 有效波段起点
            break
    for jj in range(0, t_end-t_peak):
        mcgdata = mcg_float(mcglines, t_end-jj)
        if min(mcgdata.ravel()) < max_t:
            tt2 = t_end - jj  # 有效波段终点
            break
    return tt1, tt2


def get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t):
    '''TT有效波段, 以正向波判断'''
    tt1, tt2 = t_onset, t_end
    for jj in range(t_onset, t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if max(mcgdata.ravel()) > max_t:
            tt1 = jj  # 有效波段起点
            break
    for jj in range(0, t_end - t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if max(mcgdata.ravel()) > max_t:
            tt2 = t_end - jj  # 有效波段终点
            break
    return tt1, tt2


def getava(jline):
    alis = []
    for i1 in range(jline[2]):
        alis.append(jline[7 + jline[1] + 8 * i1])
    return alis


def getts(tline):
    ts = []
    for i1 in range(tline[0]):
        if i1 < tline[0] - 1:
            ts.append([tline[1+2*i1], tline[2+2*i1]])
        else:  # 最后一个取t+1=r_peak+1
            ts.append([tline[1+2*i1], tline[2+2*i1]+1])
    return ts


def getxys(frfile, seq, ts):
    lis, xss, yss = [], [], []
    for i1 in range(len(ts)):
        lis.append([[], []])
    for i1 in range(len(frfile)):
        line = frfile[i1]
        xss.append(line[seq[0]])
        yss.append(line[seq[1]])
        for j1 in range(len(ts)):
            if line[0] in range(ts[j1][0], ts[j1][1]):
                lis[j1][0].append(line[seq[0]])
                lis[j1][1].append(line[seq[1]])
                break
    return lis, xss, yss


def make_ranks(lis1, lis2, f2=None, mode=0, v1=0, lis0=False):
    '''
    单个指标分级, 阴阳比均衡拉伸, v0<=x<v1分级;
    lis0为空: mode0, v0=v1截断; mode1, v1截断;
    lis0=列表: 按列表分级;
    '''
    n12 = max(len(lis1), len(lis2))
    n1rt, n2rt = n12 / len(lis1), n12 / len(lis2)
    lis12 = list(set((lis1 + lis2)))
    lis12.sort()
    xyss = []
    if lis0:
        lis0 = [min(lis1+lis2)-1]+lis0+[max(lis1+lis2)+1]
        for ik in range(len(lis0)-1):
            v0, v1 = lis0[ik], lis0[ik+1]
            n1, n2 = cal_smaller(lis1, v0, v1), cal_smaller(lis2, v0, v1)
            if ik == 0:
                v0 = v1
            if n1 > 0 or n2 > 0:
                n1, n2 = n1 * n1rt, n2 * n2rt
                xyss.append(get_round([v0, n1, n2, n1+n2, n2/(n1+n2+0.00001)], [3, 2, 2, 2, 3]))
    elif mode:
        for v0 in lis12:
            if v1 < v0:
                continue
            n1, n2 = cal_smaller(lis1, v0, v1), cal_smaller(lis2, v0, v1)
            if n1 > 0 or n2 > 0:
                n1, n2 = n1 * n1rt, n2 * n2rt
                xyss.append(get_round([v0, n1, n2, n1+n2, n2/(n1+n2+0.00001)], [3, 2, 2, 2, 3]))
    else:
        v0 = v1
        for v1 in lis12:
            if v1 < v0:
                continue
            n1, n2 = cal_smaller(lis1, v0, v1), cal_smaller(lis2, v0, v1)
            if n1 > 0 or n2 > 0:
                n1, n2 = n1 * n1rt, n2 * n2rt
                xyss.append(get_round([v1, n1, n2, n1+n2, n2/(n1+n2+0.00001)], [3, 2, 2, 2, 3]))
    if f2:
        strs = 'mt, n1, n2, n1+n2, p\n'
        xyss1 = ['%.3f, %.2f, %.2f, %.2f, %.3f' % (xyss[i][0], xyss[i][1], xyss[i][2], xyss[i][3], xyss[i][4]) for i in range(len(xyss))]
        strs += '\n'.join(xyss1)
        f2 = write_str(strs, f2)
    return xyss


def make_ranks2(lis1, lis2, f2=None, mode=0, v1=0, lis0=False):
    '''
    单个指标分级, 阴阳比均衡拉伸, v0<=x<v1分级;
    lis0为空: mode0, v0=v1截断; mode1, v1截断;
    lis0=列表: 按列表分级;
    '''
    n12 = max(len(lis1), len(lis2))
    n1rt, n2rt = n12 / len(lis1), n12 / len(lis2)
    lis12 = list(set((lis1 + lis2)))
    lis12.sort()
    xyss = []
    if lis0:
        lis0 = [min(lis1+lis2)-1]+lis0+[max(lis1+lis2)+1]
        for ik in range(len(lis0)-1):
            v0, v1 = lis0[ik], lis0[ik+1]
            n1, n2 = cal_smaller(lis1, v0, v1), cal_smaller(lis2, v0, v1)
            if ik == 0:
                v0 = v1
            if n1 > 0 or n2 > 0:
                n1, n2 = n1 * n1rt, n2 * n2rt
                xyss.append(get_round([v0, n1, n2, n1+n2, n2-n1, n2/(n1+n2+0.00001)], [3, 2, 2, 2, 2, 3]))
    elif mode:
        for v0 in lis12:
            if v1 < v0:
                continue
            n1, n2 = cal_smaller(lis1, v0, v1), cal_smaller(lis2, v0, v1)
            if n1 > 0 or n2 > 0:
                n1, n2 = n1 * n1rt, n2 * n2rt
                xyss.append(get_round([v0, n1, n2, n1+n2, n2-n1, n2/(n1+n2+0.00001)], [3, 2, 2, 2, 2, 3]))
    else:
        v0 = v1
        for v1 in lis12:
            if v1 < v0:
                continue
            n1, n2 = cal_smaller(lis1, v0, v1), cal_smaller(lis2, v0, v1)
            if n1 > 0 or n2 > 0:
                n1, n2 = n1 * n1rt, n2 * n2rt
                xyss.append(get_round([v1, n1, n2, n1+n2, n2-n1, n2/(n1+n2+0.00001)], [3, 2, 2, 2, 2, 3]))
    if f2:
        strs = 'mt, n1, n2, n1+n2, n2-n1, p\n'
        xyss1 = ['%.3f, %.2f, %.2f, %.2f, %.2f, %.3f' % (xyss[i][0], xyss[i][1], xyss[i][2], xyss[i][3], xyss[i][4], xyss[i][5]) for i in range(len(xyss))]
        strs += '\n'.join(xyss1)
        f2 = write_str(strs, f2)
    return xyss


def get_0_100_nms(xyss):
    '''获取一票否决数量: xyss分级信息, 输出0/100+数量'''
    ls = []
    for j in range(len(xyss)):
        per0 = xyss[j][-1]
        if per0 == 0 or per0 == 1:
            if j == 0:
                v0 = per0
                ls = [v0, xyss[j][-2]]
            elif per0 == v0:
                ls = [v0, xyss[j][-2]]
            else:
                break
        else:
            break
    return ls


def cal_smaller(lis1, v0, v1):
    '''列表中小于数量'''
    return sum(1 for x in lis1 if x >= v0 and x < v1)


def cal_bigger(lis1, v0, v1):
    '''列表中大于数量'''
    return sum(1 for x in lis1 if x > v0 and x <= v1)


def make_ranks0(lis1, lis2, f2=None, mode=0, n12m=False, lis0=False, mode0=0):
    '''
    单个指标分级
    lis0为空:
        按lis12分级;
        mode: 0按<数量, 1按按>=数量;
        n12m: [n1m, n2m]时, n1和n2分别减去数量; 为空时不减去数量;
    lis0=列表:
        按列表分级;
        mode0: 0左闭右开, 1左开右闭;
        注意: mode0=0时, lis0[0]为lis12最小, lis0[-1]大于lis12最大;
    '''
    n12 = max(len(lis1), len(lis2))
    n1rt, n2rt = n12 / len(lis1), n12 / len(lis2)
    lis12 = list(set((lis1 + lis2)))
    lis12.sort()
    strs = 'mt, n1, n2, n1+n2, p'
    xyss = []
    if lis0:
        for ik in range(len(lis0)-1):
            if mode0:
                v0 = lis0[ik+1]
                n1 = sum(1 for x in lis1 if x > lis0[ik] and x <= v0)
                n2 = sum(1 for x in lis2 if x > lis0[ik] and x <= v0)
            else:
                v0 = lis0[ik]
                n1 = sum(1 for x in lis1 if x >= v0 and x < lis0[ik+1])
                n2 = sum(1 for x in lis2 if x >= v0 and x < lis0[ik+1])
            n1, n2 = n1 * n1rt, n2 * n2rt
            strs += '\n%.3f, %.3f, %.3f, %.3f, %.3f' % (v0, n1, n2, n1+n2, n2/(n1+n2+0.00001))
            xyss.append([v0, n2/(n1+n2+0.00001)])
    else:
        for v0 in lis12:
            n1 = sum(1 for x in lis1 if x < v0)  # <数量
            n2 = sum(1 for x in lis2 if x < v0)
            if mode:
                n1, n2 = len(lis1)-n1, len(lis2)-n2  # >=数量
            n1, n2 = n1 * n1rt, n2 * n2rt
            if n12m:
                n1 -= n12m[0]
                n2 -= n12m[1]
            strs += '\n%.3f, %.3f, %.3f, %.3f, %.3f' % (v0, n1, n2, n1+n2, n2/(n1+n2+0.00001))
            xyss.append([v0, n2/(n1+n2+0.00001)])
    f2 = write_str(strs, f2)
    xyss = [[xyss[i1][0] for i1 in range(len(xyss))], [xyss[i1][1] for i1 in range(len(xyss))]]
    return xyss


def metrics_cls0(d0, lbs, nm):
    '''指标分类: nm分类; 18较好, 25有效, 31辅助, 24可用, 较差27'''
    f1 = '%s/%s/mts.txt' % (d0, lbs)
    f2 = '%s/%s/utility_idx.txt' % (d0, lbs)
    mtnms = get_item(get_lines1(f1))
    idx = txt_to_list(f2)
    mtss = []
    for j1 in range(len(nm)-1):
        mtss.append([mtnms[i1] for i1 in idx[0][sum(nm[:j1+1]):sum(nm[:j1+2])]])
    return mtss


def mcg_float(mcglines, loct):
    '''36心磁数值'''
    mcgdata = np.array(mcglines[loct-1].split()[1:]).reshape((6, 6))
    mcgdata = mcgdata.astype(float)
    return mcgdata


def multiply(p1, p2, p0):  # 叉乘计算
    return (p1[0] - p0[0]) * (p2[1] - p0[1]) - (p2[0] - p0[0]) * (p1[1] - p0[1])


def new_folder(root_dir):
    if type(root_dir) == list:
        for item in root_dir:
            new_folder(item)
    else:  # 单个
        try:
            os.mkdir(root_dir)
        except:
            pass
    return root_dir


def noqrs(itv):
    if itv == 1:
        return 1
    else:
        return 0


def get_mf0(M0):
    l0 = []
    for i in range(len(M0)):
        Z = M0[i].copy()
        max_n = float('%4.6f' % np.max(Z))
        min_n = float('%4.6f' % np.min(Z))
        l0.append([max_n, min_n])
    return l0


def norms0(Z0):
    '''正负分别归一化'''
    Z = Z0.copy()
    max_n = float('%4.6f' % np.max(Z))
    min_n = float('%4.6f' % np.min(Z))
    for i1 in range(Z.shape[0]):
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] < 0:
                Z[i1, j1] = -1 * Z[i1, j1] / min_n
            else:
                Z[i1, j1] = Z[i1, j1] / max_n
    return Z


def pcdm_default():
    '''电流密度图指标的初始化参数, 不输入参数'''
    xx, yy = np.meshgrid(np.arange(0, 20, 0.2), np.arange(0, 20, 0.2))
    diff_xx = np.diff(xx, axis=1)
    diff_xx = np.hstack((diff_xx, diff_xx[:, -1].reshape(-1, 1)))
    diff_yy = np.diff(yy, axis=0)
    diff_yy = np.vstack((diff_yy, diff_yy[-1, :]))
    return [diff_xx, diff_yy]


def plot_pic(mfm_fine, dfs):
    '''统计电流信息'''
    v, ca, x, y, offset_x, offset_y, mat_magni = get_offxy_mat(mfm_fine, dfs)
    dp, dv, mda = statred(offset_x, offset_y, mat_magni)
    return v, ca, x, y, dp, dv, mda


def ranks_sort0(d0, lbs):
    '''分级结果排序: 根据效用因子'''
    f1 = '%s/%s/rks.txt' % (d0, lbs)
    f2 = '%s/%s/utility_idx.txt' % (d0, lbs)
    f3 = '%s/%s/rks_sort.txt' % (d0, lbs)
    rkss = txt_to_list(f1)
    idx = txt_to_list(f2)
    rkss = [rkss[i1] for i1 in idx[0]]
    save_txt(rkss, 'list', f3)
    return f3


def rkdp(disp):
    '''波离散度等级'''
    if disp > 24.0:
        rdp = 3
    elif disp > 10.0:
        rdp = 2
    elif disp > 2.0:
        rdp = 1
    else:
        rdp = 0
    return rdp


def rkdpqrs(degree):
    '''qrs离散度等级'''
    if degree < 4.1:
        return 0
    elif degree < 20.0:
        return 1
    elif degree < 30.0:
        return 2
    else:
        return 3


def rkdptt(degree):
    '''tt离散度等级'''
    if degree < 4.3:
        return 0
    elif degree < 13.1:
        return 1
    elif degree < 24.6:
        return 2
    else:
        return 3


def rm_null(liebiao):
    '''去除列表中的空字符'''
    while "" in liebiao:
        liebiao.remove("")
    return liebiao


def save_txt(messages, mode='list', files=None):
    '''保存为txt'''
    doc = open(files, 'w')
    if mode == 'list':  # tuple
        for ii1 in range(len(messages)):
            doc.write(str(messages[ii1]))
            if ii1 < len(messages) - 1:
                doc.write('\n')
    elif mode == 'lists':  # tuple
        for ii1 in range(len(messages[0])):
            for jj1 in range(len(messages)):
                doc.write(str(messages[jj1][ii1]))
                if jj1 < len(messages) - 1:
                    doc.write(', ')
            if ii1 < len(messages[0]) - 1:
                doc.write('\n')
    elif mode == 'array':
        for ii1 in range(messages.shape[0]):
            doc.write(str(messages[ii1, :]))
            if ii1 < messages.shape[0] - 1:
                doc.write('\n')
    elif mode == 'dict':
        cnt = 0
        for key, value in messages.items():
            cnt += 1
            doc.write('%s %s' % (str(key), str(value)))
            if cnt < len(messages):
                doc.write('\n')
    else:  # int/str
        doc.write(str(messages))
    doc.close()
    return files


def sort_points_tan(p, pk):  # 极角排序, 不包含基准点
    p2 = []
    for i1 in range(0, len(p)):
        p2.append({"index": i1, "arc": get_arc(p[i1], pk)})
    p2.sort(key=lambda k: (k.get('arc')))
    p_out = []
    for i1 in range(0, len(p2)):
        p_out.append(p[p2[i1]["index"]])
    return p_out


def sort_text(f2, f3, ik=1, mode=0, f4=0, f5=0):
    '''根据f2的第ik列的数值排序f2和f4: 从小到大'''
    lines = get_lines1(f2)
    lis0 = get_item(lines[1:], ik, [])
    lis1 = [[i4+1, lis0[i4]] for i4 in range(len(lis0))]
    data = np.array(lis1)
    if mode:
        idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    else:
        idex = np.lexsort((data[:, 0], data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    strs = lines[0] + '\n'
    strs += '\n'.join([lines[int(lis1[i4][0])] for i4 in range(len(lis1))])
    write_str(strs, f3)
    if f4 != 0:
        lines = get_lines1(f4)
        strs = lines[0] + '\n'
        strs += '\n'.join([lines[int(lis1[i4][0])] for i4 in range(len(lis1))])
        write_str(strs, f5)
    return idex


def split_mt_rk(f1, f2, f3):
    '''提取分级结果: f1原始分级, f2指标, f3分级结果'''
    f1ls = get_lines1(f1)
    mtnms = get_item(f1ls)
    rkmes = ['[%s]' % f1ls[i3][len(mtnms[i3])+2:] for i3 in range(len(mtnms))]
    write_str('\n'.join(mtnms), f2)
    write_str('\n'.join(rkmes), f3)
    return mtnms


def stat_Z(Z, clis):  # 中心点集label叠加
    Z0 = Z.copy()
    for c1 in clis:  # 单人中心点集
        if c1[0] in range(Z.shape[0]) and c1[1] in range(Z.shape[1]):
            Z0[c1[0], c1[1]] += 1  # 数值——
    return Z0


def stataf(ags):
    m1, m2, a1, a2 = 0, 0, 0, 360  # 角度范围m1, m2, a1, a2
    for j1 in range(len(ags) - 1):
        difag = abs(ags[j1] - ags[j1 + 1])
        p1 = min(ags[j1], ags[j1 + 1])
        p2 = max(ags[j1], ags[j1 + 1])
        if difag <= 180:  # 更新角度范围
            if j1 == 0:
                m1, m2 = p1, p2
            elif a1 == 0 and a2 == 360:
                m1, m2 = min(m1, p1), max(m2, p2)
            elif p1 > a1:
                a2 = min(a2, p1)
            elif p2 < a2:
                a1 = max(a1, p2)
            else:
                m1, m2, a1, a2 = 0, 0, 360, 0
                break
        else:
            if m1 == 0 and m2 == 0:
                a1, a2 = max(a1, p1), min(a2, p2)
                if a1 > a2:
                    m1, m2, a1, a2 = 0, 0, 360, 0
                    break
                else:
                    continue
            elif m1 > p2 or m2 < p1:
                m1, m2, a1, a2 = 0, 0, p1, p2
                continue
            elif m1 > p1:
                a1, a2 = p1, min(m1, p2)
            elif m2 < p2:
                a1, a2 = max(m2, p1), p2
            else:
                m1, m2, a1, a2 = 0, 0, 360, 0
                break
            m1 = 0
            m2 = 0
    return m1, m2, a1, a2


def statcor(x0, y0):
    lis, liss = [], []
    lis.append([int(x0), int(y0)])
    lis.append([int(x0), round(y0)])
    lis.append([round(x0), int(y0)])
    lis.append([round(x0), round(y0)])
    for a in lis:
        if a not in liss:
            liss.append(a)
    return liss


def statctp(corf, ij, ik):
    '''点集生成20240204'''
    cjpts = []
    for i1 in range(len(corf)):
        cors = get_dipole_ct(corf[i1][ij], corf[i1][ik])
        for cor in cors:
            if cor not in cjpts:
                cjpts.append(cor)
    return cjpts


def statctp_raw(corf, ij, ik):
    '''点集生成-原始2023'''
    Z = np.zeros((100, 100))
    clis = []
    for i1 in range(len(corf)):
        liss = statcor(corf[i1][ij], corf[i1][ik])  # 当前帧中心点集
        for a in liss:
            if a not in clis:
                clis.append(a)
    for c1 in clis:  # 个人全部中心点集
        if c1[0] in range(Z.shape[0]) and c1[1] in range(Z.shape[1]):
            Z[c1[0], c1[1]] = 1  # 数值——
    return clis


def statdppn_QR(tim, lis0):
    '''统计QR稳定性列表stabs=[0/1, ...]'''
    dpp0, dpps, dpns, pntts = get_stabmgs(tim, lis0, 'QR')
    return cal_stabs(dpp0, dpps, dpns, pntts, range(tim))


def statdppn_RS(tim, lis0):
    '''统计RS稳定性列表stabs=[0/1, ...]'''
    dpp0, dpps, dpns, pntts = get_stabmgs(tim, lis0, 'RS')
    return cal_stabs(dpp0, dpps, dpns, pntts, range(tim-1))


def statnums(Z, lis, nsik=4):
    '''统计中心轨迹等级分布数量, nsik为等级数量'''
    ns = [0, 0, 0, 0]
    for ik in range(4, nsik):
        ns.append(0)
    for a in lis:
        k1 = int(Z[a[0]][a[1]])
        ns[k1] += 1
    ns.append(sum(ns))
    return ns


def statpf(Z):
    cors = [[], []]
    mcgs = [[], []]
    pap, pan = 0, 0
    for i1 in range(Z.shape[0]):
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] > 0.8:
                cors[0].append([i1, j1])
                mcgs[0].append(Z[i1, j1])
                pap += 1
            elif Z[i1, j1] < -0.8:
                cors[1].append([i1, j1])
                mcgs[1].append(Z[i1, j1])
                pan += 1
    return cors, pap, pan, mcgs


def statpnpt(matrix, Mf):
    '''计算初始信息'''
    Nf = matrix.max() - matrix.min()  # 幅值
    px, py, nx, ny = get_dipole(matrix)
    dx = ny - py  # 坐标和角度指向
    dy = px - nx
    a = calangle(dx, dy)  # 角度
    q = calquad(a)  # 象限
    qrd = pow(dx * dx + dy * dy, 1 / 2)  # 偶极子距离
    V1 = Nf / Mf  # 幅值归一化
    fx, fy = calptcors(V1, dx, dy)  # 面积-心磁坐标
    # V1 = 1 / pow(50 * 50 + 50 * 50, 1 / 2)  # 距离归一化
    V1 = qrd / pow(50 * 50 + 50 * 50, 1 / 2)  # 距离归一化
    mx, my = calptcors(V1, dx, dy)  # 面积-距离坐标
    cx = (px + nx) / 2  # 中心点坐标和坐标
    cy = (py + ny) / 2
    return a, q, qrd, fx, fy, mx, my, cx, cy


def statqs(qs):
    '''统计全部象限的并集'''
    alist = []
    for j1 in range(len(qs)):
        alist.append(int(qs[j1]))
    alist = sorted(list(set(alist)))
    slist = ''
    for j1 in range(len(alist)):
        slist = slist + str(alist[j1])
    return slist


def statquad(frfile, ik=2):
    '''q象限统计'''
    nq1, nq2, nq3, nq4 = 0, 0, 0, 0
    for i1 in range(len(frfile)):
        line = frfile[i1]
        if line[ik] == '1':
            nq1 += 1
        elif line[ik] == '2':
            nq2 += 1
        elif line[ik] == '3':
            nq3 += 1
        elif line[ik] == '4':
            nq4 += 1
    return nq1, nq2, nq3, nq4


def statred(offset_x, offset_y, mat_magni):
    '''红色电流信息'''
    redlis, dalis, dp = [], [], 0
    vmax = np.max(mat_magni)
    for i1 in range(mat_magni.shape[0]):
        for j1 in range(mat_magni.shape[1]):
            if mat_magni[i1, j1] >= vmax * 0.8:
                v1 = mat_magni[i1, j1] * 0.232515 / 1.162577
                a1 = calangle(offset_x[i1, j1], -offset_y[i1, j1])
                redlis.append([v1, a1, i1, j1])
                if mat_magni[i1, j1] == vmax:
                    mv, ma, mx, my = v1, a1, i1, j1
    for i1 in range(len(redlis)):
        dk = pow((redlis[i1][2]-mx)**2+(redlis[i1][3]-my)**2, 1/2) / 1.3
        dp += 2 * dk * redlis[i1][0] / mv
        v1 = abs(redlis[i1][1] - ma)
        v1 = min(v1, 360-v1)
        dalis.append(v1)
    dv = sum(dalis) / len(redlis)
    mda = max(dalis)
    return dp, dv, mda


def txt_to_array(mfm_file, time0=None):
    '''心磁txt文件转array格式[N, 36], 可选地返回一帧[6, 6]'''
    data0 = pd.read_csv(mfm_file, header=None, sep='\t')
    data0 = data0.iloc[:, 1:]
    data0 = np.array(data0)
    if time0:
        data0 = data0[time0-1, :].reshape(6, 6)
    return data0


def txt_to_list(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    lists = [ast.literal_eval(line) for line in lines]  # 读取为list
    return lists


def unzip_file(zip_filepath, dest_path):
    '''解压缩zip'''
    with zipfile.ZipFile(zip_filepath, 'r') as zip_ref:
        zip_ref.extractall(dest_path)
    return dest_path


def write_mt(idlis, mtlis, files):
    '''写入指标'''
    strs = ''
    for i1 in range(len(idlis)):
        strs += '\n%s' % idlis[i1]
        for j1 in range(len(mtlis)):
            strs += ', %s' % str(mtlis[j1][i1])
    write_str(strs, files)
    return idlis


def write_resort(f1, f2, list0):
    '''重新排序并写入'''
    lines = get_lines1(f1)
    write_str('\n'.join([lines[i] for i in list0]), f2)
    return f2


def write_str(strs, files, mode='w'):
    '''写入字符串, mode=a时添加字符串'''
    if mode == 'a':
        doc = open(files, 'a')
    else:
        doc = open(files, 'w')
    doc.write(strs)
    doc.close()
    return files


def write_strlis(mslis, mflis):
    if type(mslis) != str:
        for i2 in range(len(mflis)):
            write_strlis(mslis[i2], mflis[i2])
    else:
        write_str(mslis, mflis)
    return mflis


def writecvpts(cvpts):
    cvfile = [len(cvpts)]
    for i1 in range(len(cvpts)):
        ri = cvpts[i1]
        cvfile.extend([ri[0], ri[1]])
    return cvfile


def bingxing(hanshu, shuru, mode=1):
    '''并行计算'''
    cpu_count = os.cpu_count()
    if mode==1:  # CPU 密集型任务（如计算密集型操作）
        with ProcessPoolExecutor(max_workers=cpu_count) as executor:
            futures = [executor.submit(hanshu, Z) for Z in shuru]
            pns = [future.result() for future in futures]
    elif mode==2:  # 简化并行化
        pns = Parallel(n_jobs=-1)(delayed(hanshu)(Z) for Z in shuru)
    else:  # I/O 密集型任务（如网络请求、文件读写等）
        with ThreadPoolExecutor(max_workers=cpu_count*2) as executor:
            futures = [executor.submit(hanshu, Z) for Z in shuru]
            pns = [future.result() for future in futures]
    return pns


def writedisp(matrixes, timelis0, mcglines, mode='qrs'):
    '''离散度指标和等级计算'''
    mlfile, rksfile = [], []
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    rkslis = []
    for j2 in range(q_peak-5):
        rkslis.append(0)
    args = [norms0(matrixes[j2-1]) for j2 in range(q_peak-5, t_end+1)]
    pns = bingxing(statpn, args)  # 并行计算——
    for j2 in range(q_peak-5, t_end+1):
        # matrix = matrixes[j2-1]
        # Z = norms0(matrix)  # 正负归一化
        # pn = statpn(Z, vmin=0.3)  # 多极子
        pn = pns[j2-q_peak+5]
        dpp, dpn = caldp(pn)  # 离散度
        disp = max(dpp, dpn)
        rdp = rkdp(disp)
        rkslis.append(rdp)
        if j2 in [q_peak, r_peak, s_peak, t_peak]:
            mlfile.append(disp)
            rksfile.append(rdp)
    tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end, mode='pn')
    t1, t2 = get_qrs_t12(mcglines, q_peak, 5, 0.975)  # 截断、阈值
    t3, t4 = get_qrs_t12(mcglines, r_peak, 10, 0.95)
    t5, t6 = get_qrs_t12(mcglines, s_peak, 5, 0.975)
    dpq = sum(rkslis[j2] for j2 in range(t1, t2) if rkslis[j2] >= 2)
    dpr = sum(rkslis[j2] for j2 in range(t3, t4) if rkslis[j2] >= 2)
    dps = sum(rkslis[j2] for j2 in range(t5, t6) if rkslis[j2] >= 2)
    dpt = sum(rkslis[j2] for j2 in range(tt1, tt2) if rkslis[j2] >= 2)
    if mode == 'newqrs':  # 修正后
        dpqrs = round(10 * (dpq / (t2-t1) + dpr / (t4-t3) + dps / (t6-t5)), 1)
    else:  # 'qrs'->代码错误
        dpqrs = round(10 * (dpq / (t2-t1) + dpq / (t4-t3) + dpq / (t6-t5)), 1)
    dptt = round(20 * dpt / (tt2-tt1), 1)
    rkqrs, rktt = rkdpqrs(dpqrs), rkdptt(dptt)
    noq, nor, nos = noqrs(t2-t1), noqrs(t4-t3), noqrs(t6-t5)
    mlfile.extend([dpqrs, dptt, noq, nor, nos])
    rksfile.extend([rkqrs, rktt])
    # print(mlfile, rksfile)
    return mlfile, rksfile


def writejr(wg, m, n, js, ts, ns):
    mjfile0 = [get_round(wg, 1), m, n]
    for i1 in range(len(js)):
        mjfile0.append(js[i1])
    for i1 in range(len(ns)):
        ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][6], ns[i1][7] = get_round([ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][6], ns[i1][7]], [1, 2, 2, 1, 3, 3])
        mjfile0.extend([ns[i1][0], ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][5], ns[i1][6], ns[i1][7]])
    tjfile = [len(ts)]
    for i1 in range(len(ts)):
        tjfile.extend([ts[i1][0], ts[i1][1]])
    return mjfile0, tjfile


def writemetric(frfile, Z0, t_idx=150, mode='qr', nsik=4):
    '''
    计算等磁图指标
    单帧&转角信息->指标mpfile, mjfile, mdfile, mefile, mffile, cdfile
    '''
    lis = statctp(frfile, 8, 9)
    ns = statnums(Z0, lis, nsik)
    cdfile = calns(ns, nsik)
    nq1, nq2, nq3, nq4 = statquad(frfile)
    areaf = calarea(frfile, 4, 5)  # 幅值面积
    aread = calarea(frfile, 6, 7)  # 距离面积
    if mode == 'tt':
        ta, tt50, tt75, tta, q, m1, m2, a1, a2 = calalis_tt(frfile, t_idx)
        ta, tt50, tt75, tta, m1, m2, a1, a2, areaf, aread = get_round([ta, tt50, tt75, tta, m1, m2, a1, a2, areaf, aread], [1, 1, 1, 1, 1, 1, 1, 1, 3, 3])
        mpfile = [ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread]
    else:
        qa, ra, qra, q, m1, m2, a1, a2 = calalis(frfile)
        qa, ra, qra, m1, m2, a1, a2, areaf, aread = get_round([qa, ra, qra, m1, m2, a1, a2, areaf, aread], [1, 1, 1, 1, 1, 1, 1, 3, 3])
        mpfile = [qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread]
    rlis = calrt(frfile, 1)
    wg, m, n, js, ts, ns = caljr(frfile, rlis, 1, [4, 5], [6, 7])
    mjfile0, tjfile = writejr(wg, m, n, js, ts, ns)
    mjfile1 = writemj1(mjfile0)
    saf, sad = writesarea(tjfile, mjfile0, frfile, [4, 5], [6, 7])
    mjfile = mjfile1 + [saf, sad]
    qrdma, qrdav, qrdmse = calmas(frfile, 3)  # 偶极子间距最大/平均/均方差
    qrcd, qrcar, qrcre9 = calqrc(frfile, 8, 9)  # QR中心距离、面积、区域9
    dppma, dppav, dppmse = calmas(frfile, 10)  # 正离散度
    dpnma, dpnav, dpnmse = calmas(frfile, 11)  # 负离散度
    pnpma, pnpflu = calmfl(frfile, 12)  # 正主极子数量最大、波动次数
    pnnma, pnnflu = calmfl(frfile, 13)  # 负主极子数量
    epma, epav, epmse = calmas(frfile, 14)  # 正离心率
    enma, enav, enmse = calmas(frfile, 15)  # 负离心率
    qrdma, qrdav, qrdmse, qrcd, qrcar = get_round([qrdma, qrdav, qrdmse, qrcd, qrcar], [1, 1, 1, 1, 3])
    mdfile = [qrdma, qrdav, qrdmse, qrcd, qrcar, qrcre9]
    dppma, dppav, dppmse, dpnma, dpnav, dpnmse = get_round([dppma, dppav, dppmse, dpnma, dpnav, dpnmse], [1, 1, 1, 1, 1, 1])
    mefile = [dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu]
    epma, epav, epmse, enma, enav, enmse = get_round([epma, epav, epmse, enma, enav, enmse], [2, 2, 1, 2, 2, 1])
    mffile = [epma, epav, epmse, enma, enav, enmse]
    return mpfile, mjfile, mdfile, mefile, mffile, cdfile


def writemetric1(frfile, Z0, nsik=4):
    '''
    计算电流密度图指标
    单帧信息->指标mpfile, mjfile, mdfile, mefile, cdfile
    '''
    lis = statctp(frfile, 3, 4)
    ns = statnums(Z0, lis, nsik)
    cdfile = calns(ns, nsik)
    qca, rca, qra, q, m1, m2, a1, a2 = calalis(frfile, ik=2)
    qia, ria = get_ht(frfile, ik=10)
    nq1, nq2, nq3, nq4 = statquad(frfile, ik=8)
    iama, iami, iaav, iamse = calmas(frfile, 10, mode='min')
    qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, m1, m2, a1, a2 = get_round([qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, m1, m2, a1, a2], [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1])
    mafile = [qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2]
    mctd, mcar, mcre9 = calqrc(frfile, 3, 4)
    areac = calarea(frfile, 11, 12)
    aread = calarea(frfile, 13, 14)
    mctd, mcar, areac, aread = get_round([mctd, mcar, areac, aread], [1, 3, 3, 3])
    mpfile = [mctd, mcar, mcre9, areac, aread]
    dpma, dpav, dpmse = calmas(frfile, 5)
    dvma, dvav, dvmse = calmas(frfile, 6)
    mdama, mdaav, mdamse = calmas(frfile, 7)
    dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse = get_round([dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse], [1, 1, 1, 1, 1, 1, 1, 1, 1])
    mrfile = [dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse]
    rlis = calrt(frfile, 2)
    wg, m, n, js, ts, ns = caljr(frfile, rlis, 2, [11, 12], [13, 14])
    mjfile0, tjfile = writejr(wg, m, n, js, ts, ns)
    mjfile1 = writemj1(mjfile0)
    saf, sad = writesarea(tjfile, mjfile0, frfile, [11, 12], [13, 14])
    mjfile = mjfile1 + [saf, sad]
    return mafile, mjfile, mpfile, mrfile, cdfile


def writemetric_tt0(matrixes, mcglines, t_onset, t_peak, t_end):
    '''生成等磁图指标TT-初版top5指标'''
    tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end)
    Mf = calmf(matrixes, tt1, tt2)
    flist = []
    for jj in range(tt1, tt2 + 1):
        matrix = matrixes[jj-1]
        Nf = matrix.max() - matrix.min()  # 幅值
        px, py, nx, ny = get_dipole(matrix)
        fx, fy = calptcors(Nf/Mf, ny-py, px-nx)  # 面积-心磁坐标
        flist.append([fx, fy])
    area = calarea(flist, 0, 1)
    mpfile = [get_round(area, 3)]
    return mpfile


def writemj1(line):
    wg, m, n = line[0], line[1], line[2]
    ms, fs, rs, vs, mses, qs, afs, ads = [], [], [], [], [], [], [], []
    for j1 in range(m):
        ms.append(line[3 + j1])
    pm, nm = 0, 0
    if len(ms) > 0:
        pm = max(0, max(ms))  # 顺时针跳转最大值
        nm = min(0, min(ms))  # 逆时针跳转最大值
    for j1 in range(int(n)):
        fs.append(line[3 + m + 8 * j1])  # 帧
        rs.append(line[4 + m + 8 * j1])  # 转角
        vs.append(line[5 + m + 8 * j1])  # 角速度
        mses.append(line[6 + m + 8 * j1])  # 均方差
        qs.append(line[8 + m + 8 * j1])  # 象限
        afs.append(line[9 + m + 8 * j1])  # 面积-幅值
        ads.append(line[10 + m + 8 * j1])  # 面积-距离
    r = sum(rs)  # 转角和
    v, mse = 0, 0
    for j1 in range(n):
        v += fs[j1] * vs[j1]
        mse += fs[j1] * mses[j1]
    v = v / sum(fs)  # 平均角速度
    mse = mse / sum(fs)  # 平均均方差（扰动性）
    v1 = np.average(vs)
    mse1 = np.average(mses)
    nqs, vqs = [0, 0, 0, 0], [0, 0, 0, 0]
    for j1 in range(int(n)):
        nqs[int(qs[j1]) - 1] += 1  # 平均跳转象限
        vqs[int(qs[j1]) - 1] += fs[j1]  # 累计跳转象限
    af = sum(afs)  # 面积-幅值和
    ad = sum(ads)  # 面积-距离和
    pm, nm, r, v, mse, af, ad, v1, mse1 = get_round([pm, nm, r, v, mse, af, ad, v1, mse1], [3, 3, 2, 2, 2, 3, 3, 2, 2])
    mjfile1 = [wg, m, pm, nm, r, v, mse, nqs[0], nqs[1], nqs[2], nqs[3], vqs[0], vqs[1], vqs[2], vqs[3], af, ad, v1, mse1]
    return mjfile1


def writesarea(tjfile, jline, frfile, seqs1, seqs2):
    '''2种平均角度正负指向图'''
    ts = getts(tjfile)  # 获取时间段
    alis = getava(jline)  # 平均角度
    lis, xss, yss = getxys(frfile, [seqs1[0], seqs1[1]], ts)
    xss, yss = calavacors(alis, lis)  # 平均角度坐标
    af = calarea1(xss, yss)
    lis, xss, yss = getxys(frfile, [seqs2[0], seqs2[1]], ts)
    xss, yss = calavacors(alis, lis)
    ad = calarea1(xss, yss)
    af, ad = get_round([af, ad], [3, 3])
    return af, ad


def zipfolder(zip_path, folder_path):
    if type(zip_path) == list:
        for i4 in range(len(zip_path)):
            zipfolder(zip_path[i4], folder_path[i4])
    else:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:  
            for root, dirs, files in os.walk(folder_path):  
                for file in files:  
                    file_path = os.path.join(root, file)  
                    zipf.write(file_path, os.path.relpath(file_path, folder_path))
    return folder_path

