# -*- coding: utf-8 -*-
"""
平衡特征选择示例
展示如何使用改进的FeatureSelector处理类别不平衡问题
"""

import numpy as np
import pandas as pd
from collections import Counter
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.ensemble import RandomForestClassifier

from feature_selector import FeatureSelector


def create_imbalanced_dataset(n_samples=1000, n_features=100, imbalance_ratio=0.1):
    """
    创建不平衡数据集用于测试
    
    Parameters:
    -----------
    n_samples : int
        样本总数
    n_features : int
        特征总数
    imbalance_ratio : float
        少数类占比
        
    Returns:
    --------
    tuple: (X, y) 特征矩阵和标签
    """
    # 计算各类别样本数
    minority_samples = int(n_samples * imbalance_ratio)
    majority_samples = n_samples - minority_samples
    
    # 生成不平衡数据集
    X, y = make_classification(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=int(n_features * 0.3),  # 30%的特征是有信息的
        n_redundant=int(n_features * 0.1),    # 10%的特征是冗余的
        n_clusters_per_class=1,
        weights=[1-imbalance_ratio, imbalance_ratio],
        flip_y=0.01,  # 1%的标签噪声
        random_state=42
    )
    
    return pd.DataFrame(X, columns=[f'feature_{i}' for i in range(n_features)]), pd.Series(y)


def compare_feature_selection_methods(X, y, n_features=50):
    """
    比较不同特征选择方法的效果
    
    Parameters:
    -----------
    X : pd.DataFrame
        特征矩阵
    y : pd.Series
        标签
    n_features : int
        目标特征数量
        
    Returns:
    --------
    dict: 比较结果
    """
    print("=" * 60)
    print("特征选择方法比较")
    print("=" * 60)
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"训练集类别分布: {dict(Counter(y_train))}")
    print(f"测试集类别分布: {dict(Counter(y_test))}")
    
    results = {}
    
    # 1. 传统特征选择（不处理不平衡）
    print("\n1. 传统特征选择（不处理类别不平衡）")
    print("-" * 40)
    
    fs_traditional = FeatureSelector("", "")
    try:
        _, selected_features_traditional = fs_traditional.select_features(
            X_train, y_train, n_features=n_features, balance_before_selection=False
        )
        
        # 评估效果
        rf = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        rf.fit(X_train[selected_features_traditional], y_train)
        y_pred = rf.predict(X_test[selected_features_traditional])
        
        results['traditional'] = {
            'selected_features': selected_features_traditional,
            'n_features': len(selected_features_traditional),
            'classification_report': classification_report(y_test, y_pred, output_dict=True),
            'confusion_matrix': confusion_matrix(y_test, y_pred)
        }
        
        print(f"选择特征数: {len(selected_features_traditional)}")
        print("分类报告:")
        print(classification_report(y_test, y_pred))
        
    except Exception as e:
        print(f"传统方法失败: {str(e)}")
        results['traditional'] = None
    
    # 2. 平衡特征选择（使用SMOTE）
    print("\n2. 平衡特征选择（使用SMOTE处理不平衡）")
    print("-" * 40)
    
    fs_balanced = FeatureSelector("", "")
    try:
        _, selected_features_balanced = fs_balanced.select_features(
            X_train, y_train, n_features=n_features, balance_before_selection=True
        )
        
        # 评估效果
        rf = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        rf.fit(X_train[selected_features_balanced], y_train)
        y_pred = rf.predict(X_test[selected_features_balanced])
        
        results['balanced'] = {
            'selected_features': selected_features_balanced,
            'n_features': len(selected_features_balanced),
            'classification_report': classification_report(y_test, y_pred, output_dict=True),
            'confusion_matrix': confusion_matrix(y_test, y_pred)
        }
        
        print(f"选择特征数: {len(selected_features_balanced)}")
        print("分类报告:")
        print(classification_report(y_test, y_pred))
        
    except Exception as e:
        print(f"平衡方法失败: {str(e)}")
        results['balanced'] = None
    
    return results


def analyze_feature_overlap(results):
    """
    分析不同方法选择的特征重叠情况
    """
    if results['traditional'] is None or results['balanced'] is None:
        print("无法进行特征重叠分析，某些方法失败")
        return
    
    traditional_features = set(results['traditional']['selected_features'])
    balanced_features = set(results['balanced']['selected_features'])
    
    overlap = traditional_features.intersection(balanced_features)
    traditional_only = traditional_features - balanced_features
    balanced_only = balanced_features - traditional_features
    
    print("\n" + "=" * 60)
    print("特征选择重叠分析")
    print("=" * 60)
    print(f"传统方法选择特征数: {len(traditional_features)}")
    print(f"平衡方法选择特征数: {len(balanced_features)}")
    print(f"重叠特征数: {len(overlap)}")
    print(f"重叠比例: {len(overlap) / len(traditional_features.union(balanced_features)):.2%}")
    print(f"仅传统方法选择: {len(traditional_only)}")
    print(f"仅平衡方法选择: {len(balanced_only)}")
    
    if len(overlap) > 0:
        print(f"\n重叠特征示例: {list(overlap)[:10]}")
    if len(traditional_only) > 0:
        print(f"仅传统方法特征示例: {list(traditional_only)[:10]}")
    if len(balanced_only) > 0:
        print(f"仅平衡方法特征示例: {list(balanced_only)[:10]}")


def main():
    """
    主函数：运行完整的特征选择比较实验
    """
    print("开始平衡特征选择实验...")
    
    # 创建不平衡数据集
    print("\n创建不平衡数据集...")
    X, y = create_imbalanced_dataset(
        n_samples=1000, 
        n_features=200, 
        imbalance_ratio=0.15  # 15%的少数类
    )
    
    print(f"数据集形状: {X.shape}")
    print(f"类别分布: {dict(Counter(y))}")
    
    # 比较特征选择方法
    results = compare_feature_selection_methods(X, y, n_features=50)
    
    # 分析特征重叠
    analyze_feature_overlap(results)
    
    # 性能比较
    print("\n" + "=" * 60)
    print("性能比较总结")
    print("=" * 60)
    
    for method_name, result in results.items():
        if result is not None:
            report = result['classification_report']
            print(f"\n{method_name.upper()}方法:")
            print(f"  宏平均F1: {report['macro avg']['f1-score']:.3f}")
            print(f"  加权平均F1: {report['weighted avg']['f1-score']:.3f}")
            print(f"  少数类F1: {report['1']['f1-score']:.3f}")
            print(f"  多数类F1: {report['0']['f1-score']:.3f}")


if __name__ == "__main__":
    main()
