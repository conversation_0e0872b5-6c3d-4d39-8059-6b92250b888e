"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/01/18 16:40
desc: 小工具
    解压缩
    提取id和时刻点
environment: pfafn36
run:
    python utils.py
option:
    结果目录、心磁文档、时刻点文档, plt.show()
"""


import zipfile
import pandas as pd
from utils.StatPNs import *
import math
import numpy as np
import re
from shapely.geometry import Polygon, MultiLineString
from shapely.geometry.multipolygon import MultiPolygon
from shapely.ops import unary_union
# import matplotlib.pyplot as plt
import os
import cv2
import ast


def rm_null(liebiao):
    '''去除列表中的空字符'''
    while "" in liebiao:
        liebiao.remove("")
    return liebiao


def get_lines(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    return lines


def txt_to_list(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    lists = [ast.literal_eval(line) for line in lines]  # 读取为list
    return lists


def txt_to_array(mfm_file, time0=None):
    '''心磁txt文件转array格式[N, 36], 可选地返回一帧[6, 6]'''
    data0 = pd.read_csv(mfm_file, header=None, sep='\t')
    data0 = data0.iloc[:, 1:]
    data0 = np.array(data0)
    if time0:
        data0 = data0[time0-1, :].reshape(6, 6)
    return data0


def gen_isdir_list(dir_name):
    files = os.listdir(dir_name)
    isdir_list = []
    for f in files:
        if os.path.isdir(dir_name + '/' + f):
            isdir_list.append(True)
        else:
            isdir_list.append(False)
    return isdir_list


def new_folder(root_dir):
    if type(root_dir) == list:
        for item in root_dir:
            new_folder(item)
    else:  # 单个
        try:
            os.mkdir(root_dir)
        except:
            pass
    return root_dir


def zipfolder(zip_path, folder_path):
    if type(zip_path) == list:
        for i4 in range(len(zip_path)):
            zipfolder(zip_path[i4], folder_path[i4])
    else:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:  
            for root, dirs, files in os.walk(folder_path):  
                for file in files:  
                    file_path = os.path.join(root, file)  
                    zipf.write(file_path, os.path.relpath(file_path, folder_path))
    return folder_path


def write_str(strs, files, mode='w'):
    '''写入字符串, mode=a时添加字符串'''
    if mode == 'a':
        doc = open(files, 'a')
    else:
        doc = open(files, 'w')
    doc.write(strs)
    doc.close()
    return files


def write_strlis(mslis, mflis):
    if type(mslis) != str:
        for i2 in range(len(mflis)):
            write_strlis(mslis[i2], mflis[i2])
    else:
        write_str(mslis, mflis)
    return mflis


def write_mt(idlis, mtlis, files):
    '''写入指标'''
    strs = ''
    for i1 in range(len(idlis)):
        strs += '\n%s' % idlis[i1]
        for j1 in range(len(mtlis)):
            strs += ', %s' % str(mtlis[j1][i1])
    write_str(strs, files)
    return idlis


def save_txt(messages, mode='list', files=None):
    '''保存为txt'''
    doc = open(files, 'w')
    if mode == 'list':  # tuple
        for ii1 in range(len(messages)):
            doc.write(str(messages[ii1]))
            if ii1 < len(messages) - 1:
                doc.write('\n')
    elif mode == 'lists':  # tuple
        for ii1 in range(len(messages[0])):
            for jj1 in range(len(messages)):
                doc.write(str(messages[jj1][ii1]))
                if jj1 < len(messages) - 1:
                    doc.write(', ')
            if ii1 < len(messages[0]) - 1:
                doc.write('\n')
    elif mode == 'array':
        for ii1 in range(messages.shape[0]):
            doc.write(str(messages[ii1, :]))
            if ii1 < messages.shape[0] - 1:
                doc.write('\n')
    elif mode == 'dict':
        cnt = 0
        for key, value in messages.items():
            cnt += 1
            doc.write('%s %s' % (str(key), str(value)))
            if cnt < len(messages):
                doc.write('\n')
    else:  # int/str
        doc.write(str(messages))
    doc.close()
    return files


def get_round(lis0, ik=3):
    '''精确到ik位小数, ik为list时按照列表'''
    if type(lis0) == list:
        return [get_round(lis0[i3], ik[i3]) for i3 in range(len(ik))]
    else:  # 单个
        if ik == 0:
            return round(lis0)
        else:
            return round(lis0, ik)


def unzip_file(zip_filepath, dest_path):
    '''解压缩zip'''
    with zipfile.ZipFile(zip_filepath, 'r') as zip_ref:
        zip_ref.extractall(dest_path)
    return dest_path


def get_idstms(biao1, biaodan, mode='idtm'):
    '''
    提取ids和时刻点(广东南海), 1+6(有标题)
    mode=id时只取ids
    '''
    data = pd.read_excel(biao1, sheet_name=biaodan).values
    data_raw = data[:, :7]
    ids, timelis = [], []
    for i in range(data_raw.shape[0]):
        ids.append(str(data_raw[i, 0]))
        if mode == 'idtm':
            tms = []
            for j in range(1, 7):
                tms.append(int(str(data_raw[i, j])))
            timelis.append(tms)
    if mode == 'id':
        return ids
    else:
        return ids, timelis


def get_dipole(matrix):
    '''计算偶极子坐标'''
    px, py = np.unravel_index(np.argmax(matrix), matrix.shape)
    nx, ny = np.unravel_index(np.argmin(matrix), matrix.shape)
    return px, py, nx, ny


def calangle(dx, dy):
    z = math.sqrt(dx * dx + dy * dy)
    if dx >= 0 and dy >= 0:
        angle = math.degrees(math.asin(dy / z))
    elif dx < 0 and dy >= 0:
        angle = 180 - math.degrees(math.asin(dy / z))
    elif dx < 0 and dy < 0:
        angle = 180 + math.degrees(math.asin(-dy / z))
    elif dx >= 0 and dy < 0:
        angle = 360 - math.degrees(math.asin(-dy / z))
    return round(angle, 1)


def norms0(Z0):
    '''正负分别归一化'''
    Z = Z0.copy()
    max_n = float('%4.6f' % np.max(Z))
    min_n = float('%4.6f' % np.min(Z))
    for i1 in range(Z.shape[0]):
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] < 0:
                Z[i1, j1] = -1 * Z[i1, j1] / min_n
            else:
                Z[i1, j1] = Z[i1, j1] / max_n
    return Z


def caldp(pns):
    disps = [0, 0]  # 无极子和单极子都是0
    wk = 2  # 权重
    for j1 in range(2):
        if len(pns[j1]) != 0:
            corx = pns[j1][0][1]
            cory = pns[j1][0][2]
            for j2 in range(1, len(pns[j1])):
                dx = abs(pns[j1][j2][1]-corx)
                dy = abs(pns[j1][j2][2]-cory)
                dk = pow(dx*dx+dy*dy, 1/2) / 10
                vk = pns[j1][j2][0]
                disps[j1] = disps[j1] + wk * dk * vk
    disps[0] = round(disps[0], 1)
    disps[1] = round(-1*disps[1], 1)
    return disps[0], disps[1]


def noqrs(itv):
    if itv == 1:
        return 1
    else:
        return 0


def cal_noqrs(lis0):
    '''综合QRS波形异常指标, 321分别表示Q/R/S异常'''
    noqrs = ''
    for i4 in range(len(lis0)):
        if lis0[i4] == 1:
            noqrs += str(len(lis0)-i4)
    if noqrs == '':
        return '0'
    else:
        return noqrs


def comb_diag1(perlis):
    '''综合诊断-202310'''
    if type(perlis) == list:  # 最靠近0/100的一个或者多个值中的最大值
        for i4 in range(len(perlis)):
            perlis[i4] = comb_diag1(perlis[i4])
        if len(perlis) == 0:  # 返回空值
            return 50
        else:
            max_p = [max(x, 100-x) for x in perlis]
            max_p_id = [ik for ik, x in enumerate(max_p) if x == max(max_p)]
            perlis_max = [perlis[ik] for ik in max_p_id]
            return max(perlis_max)
    else:
        return perlis


def cal_cjpt(matrixes, i1):
    '''计算一帧的偶极子中心, 最多4个点'''
    matrix = matrixes[i1-1]
    px, py, nx, ny = get_dipole(matrix)
    cx, cy = (px + nx) / 2, (py + ny) / 2
    cors = get_dipole_ct(cx, cy)
    return cors


def cal_cjpts(matrixes, time1, time2=None):
    '''计算QR中心轨迹点集[[x0, y0], [x1, y1], ..., [xN, yN]], 或返回一帧中心轨迹'''
    if time2:
        cjpts = []
        for i1 in range(time1, time2+1):
            cjpt = cal_cjpt(matrixes, i1)
            for cors in cjpt:
                if cors not in cjpts:
                    cjpts.append(cors)
    else:
        cjpts = cal_cjpt(matrixes, time1)
    return cjpts


def cal_cettrj_pts(matrixes_list, times_list, seqs=[1, 2]):
    '''
    中心轨迹点集计算，返回全部人员的点集列表
    matrixes_list插值矩阵list
    times_list时刻点list
    seqs起止时刻点, 如QpRp
    '''
    cttjpts = []
    for i11 in range(len(matrixes_list)):
        matrixes, times = matrixes_list[i11], times_list[i11]
        time1, time2 = int(times[seqs[0]]), int(times[seqs[1]])
        cjpts = cal_cjpts(matrixes, time1, time2)  # 计算中心轨迹点集
        cttjpts.append(cjpts)
    return cttjpts


def rkdp(disp):
    '''波离散度等级'''
    if disp > 24.0:
        rdp = 3
    elif disp > 10.0:
        rdp = 2
    elif disp > 2.0:
        rdp = 1
    else:
        rdp = 0
    return rdp


def rkdpqrs(degree):
    '''qrs离散度等级'''
    if degree < 4.1:
        return 0
    elif degree < 20.0:
        return 1
    elif degree < 30.0:
        return 2
    else:
        return 3


def rkdptt(degree):
    '''tt离散度等级'''
    if degree < 4.3:
        return 0
    elif degree < 13.1:
        return 1
    elif degree < 24.6:
        return 2
    else:
        return 3


def calrot(a1, a2):
    r = a2 - a1
    if r < -180:
        r = -360 - r
    elif r > 180:
        r = r - 360
    else:
        r = -r
    return r


def get_stabmgs(tim, lis0, mode='QR'):
    '''获取稳定性信息: 幅值, 正负离散度, 转角'''
    dpp0, dpps, dpns, pntts = [], [], [], []
    if mode == 'QR':
        for j1 in range(tim):
            dpp0.append(lis0[j1][2])
            dpps.append(abs(lis0[j1][2] - lis0[j1+1][2]))
            dpns.append(abs(lis0[j1][3] - lis0[j1+1][3]))
            pntts.append(abs(calrot(lis0[j1][4], lis0[j1+1][4])))
    else:  # 'RS'
        for j2 in range(1, tim):
            j1 = tim - j2
            dpp0.append(lis0[j1][2])
            dpps.append(abs(lis0[j1][2] - lis0[j1-1][2]))
            dpns.append(abs(lis0[j1][3] - lis0[j1-1][3]))
            pntts.append(abs(calrot(lis0[j1][4], lis0[j1-1][4])))
    return dpp0, dpps, dpns, pntts


def cal_stabs(dpp0, dpps, dpns, pntts, rg12):
    '''计算稳定性列表'''
    stabs = []
    if min(dpp0) >= 10:
        for j1 in rg12:
            if dpps[j1] > 10:  # 稳定阈值——
                dpps[j1] = 1
            else:
                dpps[j1] = 0
    else:
        for j1 in rg12:
            if dpp0[j1] >= 10:  # 稳定判定——
                dpps[j1] = 1
            else:
                dpps[j1] = 0
    for j1 in rg12:
        if dpns[j1] > 10:  # 稳定阈值——
            dpns[j1] = 1
        else:
            dpns[j1] = 0
        if pntts[j1] > 60:  # 稳定阈值——
            pntts[j1] = 1
        else:
            pntts[j1] = 0
        stabs.append(max(dpps[j1], dpns[j1], pntts[j1]))
    return stabs


def statdppn_QR(tim, lis0):
    '''统计QR稳定性列表stabs=[0/1, ...]'''
    dpp0, dpps, dpns, pntts = get_stabmgs(tim, lis0, 'QR')
    return cal_stabs(dpp0, dpps, dpns, pntts, range(tim))


def statdppn_RS(tim, lis0):
    '''统计RS稳定性列表stabs=[0/1, ...]'''
    dpp0, dpps, dpns, pntts = get_stabmgs(tim, lis0, 'RS')
    return cal_stabs(dpp0, dpps, dpns, pntts, range(tim-1))


def cal_pnptm(matrixes, mflis0, time12):
    '''获取时刻点修正信息, time12为时刻点区间'''
    pnptm2 = []
    for j1 in time12:
        matrix = matrixes[j1-1]
        mf = mflis0[j1-1]
        px, py, nx, ny = get_dipole(matrix)
        pna = calangle(ny-py, px-nx)  # 指向角度
        matrix = norms0(matrix)
        pn = statpn(matrix, vmin=0.3)
        dpp, dpn = caldp(pn)
        mf, dpp, dpn, pna = get_round([mf, dpp, dpn, pna], [1, 1, 1, 1])
        pnptm2.append([j1, mf, dpp, dpn, pna])
    return pnptm2


def change_QR(pnptm2, q_peak):
    '''Q时刻点修正'''
    pnptm = pnptm2[5:]
    tim = 0
    for j1 in range(len(pnptm)):
        if pnptm[j1][1] >= 1:
            tim = j1  # 最后一个<1的序号
            break
    tim1 = 0
    if tim > 0:  # 初始<1.0
        stabs = statdppn_QR(tim, pnptm)
        for j1 in range(tim):
            jend = min(j1+3, tim)
            if max(stabs[j1:jend]) == 0:  # q_peak开始第一次稳定, 更新
                tim1 = j1
                break
    elif tim == 0:  # 初始>=1.0
        stabs = statdppn_QR(8, pnptm2)  # 5+q_peak3=8个值=[1, 9]
        for j2 in range(6):
            j1 = 5 - j2  # 逆序选取=5, 4, 3, 2, 1, 0
            if max(stabs[j1:j1+3]) == 0:  # q_peak逆序第一次稳定, 更新
                tim1 = -j2
                break
    if tim1 == 0:
        tim1 = tim
    return q_peak + tim1


def change_RS(pnptm2, s_peak):
    '''S时刻点修正'''
    pnptm = pnptm2[:-5]
    tim = 1
    for j1 in range(len(pnptm)):
        if pnptm[len(pnptm)-1-j1][1] >= 1:
            tim = j1+1  # 最后一个<1的序号
            break
    tim1 = 0
    if tim > 1:  # 初始<1.0
        stabs = statdppn_RS(tim, pnptm)
        for j1 in range(tim-1):
            jend = min(j1+3, tim-1)
            if max(stabs[j1:jend]) == 0:  # q_peak开始第一次稳定, 更新
                tim1 = j1 + 1
                break
    elif tim == 1:  # 初始>=1.0
        stabs = statdppn_RS(9, pnptm2)  # 5+q_peak3=8个值=[1, 9]
        for j2 in range(6):
            j1 = 5 - j2  # 逆序选取=5, 4, 3, 2, 1, 0
            if max(stabs[j1:j1+3]) == 0:  # q_peak逆序第一次稳定, 更新
                tim1 = -j2 + 1
                break
    if tim1 == 0:
        tim1 = tim
    return s_peak - tim1 + 1


def change_QS(timelis0, matrixes, mf):
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    pnptm2 = cal_pnptm(matrixes, mf, range(q_peak-5, r_peak+1))
    q_peak = change_QR(pnptm2, q_peak)  # Q异常修正
    pnptm2 = cal_pnptm(matrixes, mf, range(r_peak, s_peak+6))
    s_peak = change_RS(pnptm2, s_peak)  # S异常修正
    timelis0[0], timelis0[2] = q_peak, s_peak
    return timelis0


def calmf(matrixes, q_peak, r_peak):
    '''间期QR的最大幅值差'''
    flist = []
    for j1 in range(q_peak, r_peak + 1):
        matrix = matrixes[j1 - 1]  # 扩张心磁
        flist.append(matrix.max() - matrix.min())  # 幅值差
    Mf = max(flist)
    return Mf


def calquad(p1):  # 计算象限
    pq1234 = [90, 180, 270, 360]
    for j1 in range(4):
        if p1 < pq1234[j1]:
            break
    return str(j1+1)
    # pq1 = [0, 90]  # raw
    # pq2 = [90, 180]
    # pq3 = [180, 270]
    # pq4 = [270, 360]
    # pqs = [pq1, pq2, pq3, pq4]
    # pq = ''
    # for j1 in range(4):
    #     if p1 < pqs[j1][1] and pqs[j1][0] <= p1:  # 左闭右开
    #         pq = str(j1 + 1)
    # return pq


def calptcors(V1, dx, dy):
    '''角度坐标计算'''
    dz = pow(dx * dx + dy * dy, 1 / 2)
    fx = V1 * dx / dz
    fy = V1 * dy / dz
    return fx, fy


def statpnpt(matrix, Mf):
    '''计算初始信息'''
    Nf = matrix.max() - matrix.min()  # 幅值
    px, py, nx, ny = get_dipole(matrix)
    # px += 1  # 避免cxcy过界
    # py += 1
    # nx += 1
    # ny += 1
    dx = ny - py  # 坐标和角度指向
    dy = px - nx
    a = calangle(dx, dy)  # 角度
    q = calquad(a)  # 象限
    qrd = pow(dx * dx + dy * dy, 1 / 2)  # 偶极子距离
    V1 = Nf / Mf  # 幅值归一化
    fx, fy = calptcors(V1, dx, dy)  # 面积-心磁坐标
    # V1 = 1 / pow(50 * 50 + 50 * 50, 1 / 2)  # 距离归一化
    V1 = qrd / pow(50 * 50 + 50 * 50, 1 / 2)  # 距离归一化
    mx, my = calptcors(V1, dx, dy)  # 面积-距离坐标
    cx = (px + nx) / 2  # 中心点坐标和坐标
    cy = (py + ny) / 2
    return a, q, qrd, fx, fy, mx, my, cx, cy


def pcdm_default():
    '''电流密度图指标的初始化参数, 不输入参数'''
    xx, yy = np.meshgrid(np.arange(0, 20, 0.2), np.arange(0, 20, 0.2))
    diff_xx = np.diff(xx, axis=1)
    diff_xx = np.hstack((diff_xx, diff_xx[:, -1].reshape(-1, 1)))
    diff_yy = np.diff(yy, axis=0)
    diff_yy = np.vstack((diff_yy, diff_yy[-1, :]))
    return [diff_xx, diff_yy]


def get_offxy_mat(mfm_fine, dfs):
    '''获取初始信息: 最大强度、方向、坐标、矩阵'''
    diff_xx, diff_yy = dfs
    diff_mfm_x = np.diff(mfm_fine, axis=1)
    diff_mfm_x = np.hstack((diff_mfm_x, diff_mfm_x[:, -1].reshape(-1, 1)))
    diff_mfm_y = np.diff(mfm_fine, axis=0)
    diff_mfm_y = np.vstack((diff_mfm_y, diff_mfm_y[-1, :]))
    current_x = diff_mfm_y / diff_yy
    current_y = -diff_mfm_x / diff_xx
    mat_magni = np.sqrt(current_x**2 + current_y**2)
    v = np.max(mat_magni) * 0.232515 / 1.162577  # 等比例匹配NI的强度值
    px, py = np.unravel_index(np.argmax(mat_magni), mat_magni.shape)
    mat_angle = np.arctan2(current_y, current_x) * (180 / np.pi)
    max_magni = np.max(mat_magni)
    mat_magni1 = mat_magni / max_magni
    offset_x = mat_magni1 * np.cos(np.deg2rad(mat_angle))
    offset_y = mat_magni1 * np.sin(np.deg2rad(mat_angle))
    dx = offset_x[px, py]
    dy = offset_y[px, py]
    ca = calangle(dx, -dy)  # 电流角度
    # px += 1  # 超出边界
    # py += 1
    offset_x = offset_x[::8, ::8]
    offset_y = offset_y[::8, ::8]
    mat_magni = mat_magni[::8, ::8]
    return v, ca, px, py, offset_x, offset_y, mat_magni


def statred(offset_x, offset_y, mat_magni):
    '''红色电流信息'''
    redlis, dalis, dp = [], [], 0
    vmax = np.max(mat_magni)
    for i1 in range(mat_magni.shape[0]):
        for j1 in range(mat_magni.shape[1]):
            if mat_magni[i1, j1] >= vmax * 0.8:
                v1 = mat_magni[i1, j1] * 0.232515 / 1.162577
                a1 = calangle(offset_x[i1, j1], -offset_y[i1, j1])
                redlis.append([v1, a1, i1, j1])
                if mat_magni[i1, j1] == vmax:
                    mv, ma, mx, my = v1, a1, i1, j1
    for i1 in range(len(redlis)):
        dk = pow((redlis[i1][2]-mx)**2+(redlis[i1][3]-my)**2, 1/2) / 1.3  # 归一化10
        dp += 2 * dk * redlis[i1][0] / mv
        v1 = abs(redlis[i1][1] - ma)
        v1 = min(v1, 360-v1)
        dalis.append(v1)
    dv = sum(dalis) / len(redlis)
    mda = max(dalis)
    return dp, dv, mda


def plot_pic(mfm_fine, dfs):
    '''统计电流信息'''
    v, ca, x, y, offset_x, offset_y, mat_magni = get_offxy_mat(mfm_fine, dfs)
    dp, dv, mda = statred(offset_x, offset_y, mat_magni)
    return v, ca, x, y, dp, dv, mda


def calpn(pns):  # 计算主极子数量
    pnp, pnn = 0, 0
    for i1 in range(len(pns[0])):
        if pns[0][i1][0] > 0.8:
            pnp += 1
    for i1 in range(len(pns[1])):
        if pns[1][i1][0] < -0.8:
            pnn += 1
    return pnp, pnn


def statpf(Z):
    cors = [[], []]
    mcgs = [[], []]
    pap, pan = 0, 0
    for i1 in range(Z.shape[0]):
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] > 0.8:
                cors[0].append([i1, j1])
                mcgs[0].append(Z[i1, j1])
                pap += 1
            elif Z[i1, j1] < -0.8:
                cors[1].append([i1, j1])
                mcgs[1].append(Z[i1, j1])
                pan += 1
    return cors, pap, pan, mcgs


def calplf(Z):
    cors, pap, pan, mcgs = statpf(Z)
    lisc = [[], [], [], []]
    pe, ne = 0, 0  # 初始化——
    if pap != 0:
        for k1 in range(len(cors[0])):
            lisc[0].append(cors[0][k1][0])
            lisc[1].append(cors[0][k1][1])
        cpx = np.average(lisc[0])  # 中心点坐标
        cpy = np.average(lisc[1])
        for k1 in range(len(cors[0])):
            dx = cors[0][k1][0] - cpx
            dy = cors[0][k1][1] - cpy
            vk = mcgs[0][k1]
            dk = pow(dx * dx + dy * dy, 1 / 2)
            pe += dk * vk
        pe = pe / pap  # 离心率归一化=除磁极面积
    if pan != 0:
        for k1 in range(len(cors[1])):
            lisc[2].append(cors[1][k1][0])
            lisc[3].append(cors[1][k1][1])
        cnx = np.average(lisc[2])
        cny = np.average(lisc[3])
        for k1 in range(len(cors[1])):
            dx = cors[1][k1][0] - cnx
            dy = cors[1][k1][1] - cny
            vk = -mcgs[1][k1]
            dk = pow(dx * dx + dy * dy, 1 / 2)
            ne += dk * vk
        ne = ne / pan
    return pe, ne


def calangcors(V1, ca):
    fx = V1 * math.cos(ca / 180 * np.pi)
    fy = V1 * math.sin(ca / 180 * np.pi)
    return fx, fy


def cal_fr(matrixes, dfs, r_peak, s_peak):
    '''计算单帧信息PCDM, RS为例'''
    frfile = []
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        v, ca, x, y, dp, dv, mda = plot_pic(matrix, dfs)
        v, ca, x, y, dp, dv, mda = get_round([v, ca, x, y, dp, dv, mda], [6, 1, 0, 0, 1, 1, 1])
        frfile.append([j, v, ca, x, y, dp, dv, mda])
    Mv = max([frfile[j][1] for j in range(len(frfile))])
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        ca = frfile[j-r_peak][2]
        px, py, nx, ny = get_dipole(matrix)
        dx, dy = ny - py, px - nx  # 坐标和角度指向
        da = calangle(dx, dy)  # 指向角度
        q = calquad(ca)  # 象限
        r = abs(da - ca)
        ia = min(r, 360 - r)  # 夹角
        qrd = pow(dx*dx+dy*dy, 1/2)  # 偶极子距离
        fx, fy = calangcors(frfile[j-r_peak][1] / Mv, ca)
        mx, my = calangcors(qrd / pow(50*50+50*50, 1/2), ca)
        da, ia, fx, fy, mx, my = get_round([da, ia, fx, fy, mx, my], [1, 1, 3, 3, 3, 3])
        frfile[j-r_peak].extend([q, da, ia, fx, fy, mx, my])
    return frfile


def cal_frrt(matrixes, r_peak, s_peak):
    '''计算单帧信息和转角信息, RS为例'''
    alist = []
    Mf = calmf(matrixes, r_peak, s_peak)
    frfile, rtfile = [], []
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        a, q, qrd, fx, fy, mx, my, cx, cy = statpnpt(matrix, Mf)
        alist.append(a)
        Z = norms0(matrix)  # 正负归一化
        pn = statpn(Z, vmin=0.3)  # 多极子
        dpp, dpn = caldp(pn)  # 离散度
        pnp, pnn = calpn(pn)  # 主极子数量
        ep, en = calplf(Z)  # 离心率
        a, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, ep, en = get_round([a, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, ep, en], [1, 1, 3, 3, 3, 3, 1, 1, 1, 1, 3, 3])
        frfile.append([j, a, q, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, pnp, pnn, ep, en])  # 信息集
        if j > r_peak:
            ra = calrot(alist[-2], alist[-1])
            rtfile.append([j-1, get_round(ra, 2)])
    return frfile, rtfile


def extractmt(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    Z = np.zeros((100, 100))
    for i1 in range(len(lines)):
        line = re.split(' |,', lines[i1].strip())
        line = rm_null(line)
        for j1 in range(1, len(line)):
            Z[i1][j1 - 1] = int(line[j1])
    return Z


def statcor(x0, y0):
    lis, liss = [], []
    lis.append([int(x0), int(y0)])
    lis.append([int(x0), round(y0)])
    lis.append([round(x0), int(y0)])
    lis.append([round(x0), round(y0)])
    for a in lis:
        if a not in liss:
            liss.append(a)
    return liss


def statctp_raw(corf, ij, ik):
    '''点集生成-原始2023'''
    Z = np.zeros((100, 100))
    clis = []
    for i1 in range(len(corf)):
        liss = statcor(corf[i1][ij], corf[i1][ik])  # 当前帧中心点集
        for a in liss:
            if a not in clis:
                clis.append(a)
    for c1 in clis:  # 个人全部中心点集
        if c1[0] in range(Z.shape[0]) and c1[1] in range(Z.shape[1]):
            Z[c1[0], c1[1]] = 1  # 数值——
    return clis


def get_dipole_ct(cx, cy):
    '''偶极子中心统计'''
    cors = []
    for x1 in [int(cx), round(cx)]:
        for y1 in [int(cy), round(cy)]:
            # x1 = max(0, min(99, x1))  # 端点控制
            # y1 = max(0, min(99, y1))
            if [x1, y1] not in cors:
                cors.append([x1, y1])
    return cors


def get_pts(matrixes, q_peak, r_peak):
    '''从矩阵计算点集'''
    cjpts = []
    for j in range(q_peak, r_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        px, py, nx, ny = get_dipole(matrix)
        cx = (px + nx) / 2  # 中心点坐标和坐标
        cy = (py + ny) / 2
        cors = get_dipole_ct(cx, cy)
        for cor in cors:
            if cor not in cjpts:
                cjpts.append(cor)
    return cjpts


def get_pts1(matrixes, dfs, q_peak, r_peak):
    '''从矩阵计算点集-主电流位置'''
    cjpts = []
    for j in range(q_peak, r_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        v, ca, cx, cy, offset_x, offset_y, mat_magni = get_offxy_mat(matrix, dfs)
        if [cx, cy] not in cjpts:
            cjpts.append([cx, cy])
    return cjpts


def stat_Z(Z, clis):  # 中心点集label叠加
    Z0 = Z.copy()
    for c1 in clis:  # 单人中心点集
        if c1[0] in range(Z.shape[0]) and c1[1] in range(Z.shape[1]):
            Z0[c1[0], c1[1]] += 1  # 数值——
    return Z0


def statctp(corf, ij, ik):
    '''点集生成20240204'''
    cjpts = []
    for i1 in range(len(corf)):
        cors = get_dipole_ct(corf[i1][ij], corf[i1][ik])
        for cor in cors:
            if cor not in cjpts:
                cjpts.append(cor)
    return cjpts


def statnums(Z, lis, nsik=4):
    '''统计中心轨迹等级分布数量, nsik为等级数量'''
    ns = [0, 0, 0, 0]
    for ik in range(4, nsik):
        ns.append(0)
    for a in lis:
        k1 = int(Z[a[0]][a[1]])
        ns[k1] += 1
    ns.append(sum(ns))
    return ns


def calns(ns, nsik=4):
    '''计算等级分布类别, nsik为等级数量'''
    qs = ''
    for i1 in range(nsik):
        if ns[nsik-1-i1] > 0:
            qs += str(nsik-1-i1)
    cdfile = [qs]
    for i1 in range(nsik):
        cdfile.append(ns[i1])
    if nsik == 5:
        mt1 = (2*ns[3]+4*ns[4]-2*ns[1]-4*ns[0]) / sum(ns[:nsik])
        mt2 = (3*ns[3]+5*ns[4]-3*ns[1]-5*ns[0]) / sum(ns[:nsik])
        mt1, mt2 = get_round([mt1, mt2], [2, 2])
        cdfile.extend([mt1, mt2])
    return cdfile


def addqs(qs, p1):
    pq1 = [0, 90]
    pq2 = [90, 180]
    pq3 = [180, 270]
    pq4 = [270, 360]
    pqs = [pq1, pq2, pq3, pq4]
    for j1 in range(4):
        if p1[0] <= pqs[j1][1] and pqs[j1][0] < p1[1]:  # 取低不取高
            pq = str(j1 + 1)
            if pq not in qs:
                qs.append(pq)
    return qs


def statqs(qs):
    '''统计全部象限的并集'''
    alist = []
    for j1 in range(len(qs)):
        alist.append(int(qs[j1]))
    alist = sorted(list(set(alist)))
    slist = ''
    for j1 in range(len(alist)):
        slist = slist + str(alist[j1])
    return slist


def stataf(ags):
    m1, m2, a1, a2 = 0, 0, 0, 360  # 角度范围m1, m2, a1, a2
    for j1 in range(len(ags) - 1):
        difag = abs(ags[j1] - ags[j1 + 1])
        p1 = min(ags[j1], ags[j1 + 1])
        p2 = max(ags[j1], ags[j1 + 1])
        if difag <= 180:  # 更新角度范围
            if j1 == 0:
                m1, m2 = p1, p2
            elif a1 == 0 and a2 == 360:
                m1, m2 = min(m1, p1), max(m2, p2)
            elif p1 > a1:
                a2 = min(a2, p1)
            elif p2 < a2:
                a1 = max(a1, p2)
            else:
                m1, m2, a1, a2 = 0, 0, 360, 0
                break
        else:
            if m1 == 0 and m2 == 0:
                a1, a2 = max(a1, p1), min(a2, p2)
                if a1 > a2:
                    m1, m2, a1, a2 = 0, 0, 360, 0
                    break
                else:
                    continue
            elif m1 > p2 or m2 < p1:
                m1, m2, a1, a2 = 0, 0, p1, p2
                continue
            elif m1 > p1:
                a1, a2 = p1, min(m1, p2)
            elif m2 < p2:
                a1, a2 = max(m2, p1), p2
            else:
                m1, m2, a1, a2 = 0, 0, 360, 0
                break
            m1 = 0
            m2 = 0
    return m1, m2, a1, a2


def cal_qra_q(alist):
    qs, ps = [], []
    for i1 in range(len(alist) - 1):
        difag = abs(alist[i1] - alist[i1 + 1])
        if difag < 180:
            p1 = (min(alist[i1], alist[i1 + 1]), max(alist[i1], alist[i1 + 1]))
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # 向量指向长度 == min-max
            ps.append(p2)
        else:
            p1 = (0, min(alist[i1], alist[i1 + 1]))
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # 0-min
            ps.append(p2)
            p1 = (max(alist[i1], alist[i1 + 1]), 360)
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # max-360
            ps.append(p2)
    mp = MultiLineString(ps)
    qra = unary_union(mp).length  # 角度跨度
    for i in range(len(alist)):
        qs.append(calquad(alist[i]))
    q = statqs(qs)  # 象限
    return qra, q


def get_ht(frfile, ik=1):
    '''获取首尾值'''
    return frfile[0][ik], frfile[-1][ik]


def calalis(frfile, ik=1):
    '''正负指向指标'''
    alist = []
    for i1 in range(len(frfile)):
        alist.append(frfile[i1][ik])
    qa, ra = get_ht(frfile, ik=ik)  # Q、R波角度
    qra, q = cal_qra_q(alist)
    m1, m2, a1, a2 = stataf(alist)  # 角度范围
    return qa, ra, qra, q, m1, m2, a1, a2


def calalis_tt(frfile, t_idx):
    '''正负指向指标-TT'''
    alist, rtlist = [], []
    for i1 in range(len(frfile)):
        alist.append(frfile[i1][1])
        if i1 > 0:
            rota = abs(alist[-1]-alist[-2])
            rota = min(360-rota, rota)
            rtlist.append(rota)
    rtlist.sort()  # TT50、TT75
    tt50 = np.median(rtlist)  # 中位数
    tt75 = np.percentile(rtlist, (75))  # 第3四分位数
    ta = frfile[t_idx][1]  # T波角度
    tta, q = cal_qra_q(alist)
    m1, m2, a1, a2 = stataf(alist)  # 角度范围
    return ta, tt50, tt75, tta, q, m1, m2, a1, a2


def statquad(frfile, ik=2):
    '''q象限统计'''
    nq1, nq2, nq3, nq4 = 0, 0, 0, 0
    for i1 in range(len(frfile)):
        line = frfile[i1]
        if line[ik] == '1':
            nq1 += 1
        elif line[ik] == '2':
            nq2 += 1
        elif line[ik] == '3':
            nq3 += 1
        elif line[ik] == '4':
            nq4 += 1
    return nq1, nq2, nq3, nq4


def calarea(frfile, ix, iy):
    xss, yss, pgs = [], [], []
    for j1 in range(len(frfile)):
        xss.append(frfile[j1][ix])
        yss.append(frfile[j1][iy])
        if j1 > 0:
            pg1 = Polygon([[0, 0], [xss[-2], yss[-2]], [xss[-1], yss[-1]]])
            pgs.append(pg1)
    mpg = MultiPolygon(pgs)
    mpgv = unary_union(mpg).area  # 面积
    return mpgv


def calarea1(xss, yss):
    pgs = []
    for j1 in range(len(xss) - 1):
        pg1 = Polygon([[0, 0], [xss[j1], yss[j1]], [xss[j1 + 1], yss[j1 + 1]]])
        pgs.append(pg1)
    mpg = MultiPolygon(pgs)
    mpgv = unary_union(mpg).area  # 面积
    return mpgv


def calfag(ags):
    ps = []
    for i1 in range(len(ags) - 1):
        difag = abs(ags[i1] - ags[i1 + 1])
        if difag < 180:
            p1 = (min(ags[i1], ags[i1 + 1]), max(ags[i1], ags[i1 + 1]))
            p2 = ((0, p1[0]), (0, p1[1]))  # 向量指向长度 == min-max
            ps.append(p2)
        else:
            p1 = (0, min(ags[i1], ags[i1 + 1]))
            p2 = ((0, p1[0]), (0, p1[1]))  # 0-min
            ps.append(p2)
            p1 = (max(ags[i1], ags[i1 + 1]), 360)
            p2 = ((0, p1[0]), (0, p1[1]))  # max-360
            ps.append(p2)
    mp = MultiLineString(ps)
    fag = unary_union(mp).length  # 角度跨度
    return fag


def caljr(frfile, rlis, seq, seqs1, seqs2):
    js, ns, tlis, alis, mse3, ts, ags, rts, fxs, fys, mxs, mys, xss, yss, xss1, yss1 = [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []
    for i1 in range(len(frfile)):
        fline = frfile[i1]
        tlis.append(fline[0])  # 时刻
        alis.append(fline[seq])  # 角度
        fxs.append(fline[seqs1[0]])  # 坐标..
        fys.append(fline[seqs1[1]])
        mxs.append(fline[seqs2[0]])
        mys.append(fline[seqs2[1]])
    tlis.append(fline[0] + 1)
    rlis1 = sorted(list(np.absolute(rlis)), reverse=True)  # 绝对值逆序
    for i1 in range(7):
        rmse = np.std(rlis1[i1:i1 + 3])
        mse3.append(rmse)
    m = 0
    if mse3[4] <= 2.23:
        for j1 in range(4):  # 前4个
            k1 = 3 - j1
            if mse3[k1] > 2.23:
                m = k1 + 1
                break
    else:
        for j1 in range(3):  # 5、6、7
            k1 = 6 - j1
            if mse3[k1] > 2.23:
                m = k1 + 1
                break
    rlis2 = rlis1[:m]  # 小波范围
    ths = [0]  # 起始点——
    m = 0
    rtss = []
    for i1 in range(len(rlis)):
        rtss.append(rlis[i1])
        if abs(rlis[i1]) in rlis2:
            rtss.pop(-1)
            rts.append(rtss)
            rtss = []
            m += 1
            js.append(rlis[i1])
            ths.append(i1 + 1)  # range——
        if i1 == len(rlis) - 1:
            rts.append(rtss)
    wg = rlis1[m]  # 小波范围
    ths.append(len(alis))  # 补充终点——
    for i1 in range(len(ths) - 1):
        ts.append([tlis[ths[i1]], tlis[ths[i1 + 1]]])  # 时刻段
        ags.append(alis[ths[i1]:ths[i1 + 1]])  # 角度
        xss.append(fxs[ths[i1]:ths[i1 + 1]])
        yss.append(fys[ths[i1]:ths[i1 + 1]])
        xss1.append(mxs[ths[i1]:ths[i1 + 1]])
        yss1.append(mys[ths[i1]:ths[i1 + 1]])
    for i1 in range(m + 1):
        if len(rts[i1]) != 0:
            a = ags[i1][0] - np.average(rts[i1])  # 平均角度=a1-av.rn
        else:
            a = ags[i1][0]
        if a >= 360:  # 规范角度
            a = a - 360
        elif a < 0:
            a = a + 360
        q = calquad(a)
        if len(ags[i1]) <= 1:
            f, r, v, mse, af, ad = 1, 0, 0, 0, 0, 0
        else:
            f = len(ags[i1])  # 帧数
            r = calfag(ags[i1])  # 计算角度转角——
            v, vlis = 0, []
            for j1 in range(len(rts[i1])):
                vlis.append(abs(rts[i1][j1]))
            v = np.average(vlis)  # 角速度
            mse = np.std(rts[i1])  # 均方差
            af = calarea1(xss[i1], yss[i1])
            ad = calarea1(xss1[i1], yss1[i1])
        ns.append([f, r, v, mse, a, q, af, ad])
    return wg, m, m+1, js, ts, ns


def writejr(wg, m, n, js, ts, ns):
    mjfile0 = [get_round(wg, 1), m, n]
    for i1 in range(len(js)):
        mjfile0.append(js[i1])
    for i1 in range(len(ns)):
        ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][6], ns[i1][7] = get_round([ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][6], ns[i1][7]], [1, 2, 2, 1, 3, 3])
        mjfile0.extend([ns[i1][0], ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][5], ns[i1][6], ns[i1][7]])
    tjfile = [len(ts)]
    for i1 in range(len(ts)):
        tjfile.extend([ts[i1][0], ts[i1][1]])
    return mjfile0, tjfile


def calmas(frfile, ik, mode='max'):
    '''列表的最大(最小)、均值、均方差'''
    lis = []
    for i1 in range(len(frfile)):
        lis.append(frfile[i1][ik])
    dppma = max(lis)  # 最大值
    dppmi = min(lis)  # 最大值
    dppav = np.average(lis)  # 均值
    dppmse = np.std(lis)
    if mode == 'min':
        return dppma, dppmi, dppav, dppmse
    else:
        return dppma, dppav, dppmse


def get_leftbottompoint(p):  # 左下角基准点
    k = 0
    for i1 in range(1, len(p)):
        if p[i1][1] < p[k][1] or (p[i1][1] == p[k][1] and p[i1][0] < p[k][0]):
            k = i1
    return k


def multiply(p1, p2, p0):  # 叉乘计算
    return (p1[0] - p0[0]) * (p2[1] - p0[1]) - (p2[0] - p0[0]) * (p1[1] - p0[1])


def get_arc(p1, p0):  # 反正切极角
    if (p1[0] - p0[0]) == 0:
        if ((p1[1] - p0[1])) == 0:
            return -1
        else:
            return math.pi / 2
    tan = float((p1[1] - p0[1])) / float((p1[0] - p0[0]))
    arc = math.atan(tan)
    if arc >= 0:
        return arc
    else:
        return math.pi + arc


def sort_points_tan(p, pk):  # 极角排序, 不包含基准点
    p2 = []
    for i1 in range(0, len(p)):
        p2.append({"index": i1, "arc": get_arc(p[i1], pk)})
    p2.sort(key=lambda k: (k.get('arc')))
    p_out = []
    for i1 in range(0, len(p2)):
        p_out.append(p[p2[i1]["index"]])
    return p_out


def convex_hull(p):
    p = list(set(p))
    k = 0
    for i1 in range(1, len(p)):
        if p[i1][1] < p[k][1] or (p[i1][1] == p[k][1] and p[i1][0] < p[k][0]):
            k = i1
    pk = p[k]
    p.remove(p[k])
    p_sort = sort_points_tan(p, pk)  # 按与基准点连线和x轴正向的夹角排序后的点坐标
    p_result = [pk, p_sort[0]]
    for i1 in range(1, len(p_sort)):  # 叉乘为正删点;叉乘为负加点
        while (multiply(p_result[-2], p_sort[i1], p_result[-1]) > 0):
            p_result.pop()
        p_result.append(p_sort[i1])
    return p_result


def getAreaOfPolyGonbyVector(points):  # 基于向量叉乘计算多边形面积
    area = 0
    if (len(points) < 3):
        return 0
        # raise Exception("error")
    for i1 in range(0, len(points) - 1):
        p1 = points[i1]
        p2 = points[i1 + 1]
        triArea = (p1[0] * p2[1] - p2[0] * p1[1]) / 2
        area += triArea
    fn = (points[-1][0] * points[0][1] - points[0][0] * points[-1][1]) / 2
    return abs(area + fn)


def calregion(xss, yss):  # 计算9区域分布[0, 33) [33, 67) [67, 100)=33, 34, 33
    rgs = [[0, 33], [33, 67], [67, 100]]  # 坐标取0/100——
    rg9s = []
    for i1 in range(len(xss)):
        for j1 in range(3):
            if xss[i1] >= rgs[j1][0] and xss[i1] < rgs[j1][1]:
                for j2 in range(3):
                    if yss[i1] > rgs[j2][0] and yss[i1] <= rgs[j2][1]:
                        # rg9 = int(3 * (2 - j1) + j2 + 1)
                        rg9 = int(3 * j1 + j2 + 1)
                        if rg9 not in rg9s:
                            rg9s.append(rg9)
                        break
                break
    rg9s.sort()
    qrcre9 = ''
    for i1 in range(len(rg9s)):
        qrcre9 = qrcre9 + str(rg9s[i1])
    return qrcre9


def calqrc(frfile, ix, iy):
    xss, yss, pts = [], [], []
    qrcd, qrcar = 0, 0
    for j1 in range(len(frfile)):
        xss.append(frfile[j1][iy])
        yss.append(frfile[j1][ix])
        if (xss[-1], yss[-1]) not in pts:
            pts.append((xss[-1], yss[-1]))  # 点集
        if j1 > 0:
            dx = xss[-1] - xss[-2]
            dy = yss[-1] - yss[-2]
            qrcd += pow(dx * dx + dy * dy, 1 / 2)
    if len(pts) > 2:
        cvpts = convex_hull(pts)  # 凸包点集, 绘制——
        qrcar = getAreaOfPolyGonbyVector(cvpts)  # 面积
        qrcar = qrcar / 1000  # 归一化
    qrcre9 = calregion(yss, xss)  # 9区域分布
    return qrcd, qrcar, qrcre9


def writecvpts(cvpts):
    cvfile = [len(cvpts)]
    for i1 in range(len(cvpts)):
        ri = cvpts[i1]
        cvfile.extend([ri[0], ri[1]])
    return cvfile


def calmfl(frfile, ik):
    lis = []
    pnpflu = 0
    for i1 in range(len(frfile)):
        line = frfile[i1]
        lis.append(line[ik])
        if i1 > 1:
            if lis[-1] != lis[-2]:
                pnpflu += 1  # 波动次数
    pnpma = max(lis)  # 最大值
    return pnpma, pnpflu


def calrt(frfile, ik=2):
    '''计算角度'''
    rlis = []
    for i1 in range(1, len(frfile)):
        ra = calrot(frfile[i1-1][ik], frfile[i1][ik])
        rlis.append(ra)
    return rlis


def writemetric1(frfile, Z0, t_idx=150, mode='qr', nsik=4):
    '''
    计算电流密度图指标
    单帧信息->指标mpfile, mjfile, mdfile, mefile, cdfile
    '''
    lis = statctp(frfile, 3, 4)
    ns = statnums(Z0, lis, nsik)
    cdfile = calns(ns, nsik)
    qca, rca, qra, q, m1, m2, a1, a2 = calalis(frfile, ik=2)
    qia, ria = get_ht(frfile, ik=10)
    nq1, nq2, nq3, nq4 = statquad(frfile, ik=8)
    iama, iami, iaav, iamse = calmas(frfile, 10, mode='min')
    qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, m1, m2, a1, a2 = get_round([qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, m1, m2, a1, a2], [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1])
    mafile = [qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2]
    mctd, mcar, mcre9 = calqrc(frfile, 3, 4)
    areac = calarea(frfile, 11, 12)
    aread = calarea(frfile, 13, 14)
    mctd, mcar, areac, aread = get_round([mctd, mcar, areac, aread], [1, 3, 3, 3])
    mpfile = [mctd, mcar, mcre9, areac, aread]
    dpma, dpav, dpmse = calmas(frfile, 5)
    dvma, dvav, dvmse = calmas(frfile, 6)
    mdama, mdaav, mdamse = calmas(frfile, 7)
    dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse = get_round([dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse], [1, 1, 1, 1, 1, 1, 1, 1, 1])
    mrfile = [dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse]
    rlis = calrt(frfile, 2)
    wg, m, n, js, ts, ns = caljr(frfile, rlis, 2, [11, 12], [13, 14])
    mjfile0, tjfile = writejr(wg, m, n, js, ts, ns)
    mjfile1 = writemj1(mjfile0)
    saf, sad = writesarea(tjfile, mjfile0, frfile, [11, 12], [13, 14])
    mjfile = mjfile1 + [saf, sad]
    return mafile, mjfile, mpfile, mrfile, cdfile


def writemetric(frfile, rtfile, Z0, t_idx=150, mode='qr', nsik=4):
    '''
    计算等磁图指标
    单帧&转角信息->指标mpfile, mjfile, mdfile, mefile, mffile, cdfile
    '''
    lis = statctp(frfile, 8, 9)
    ns = statnums(Z0, lis, nsik)
    cdfile = calns(ns, nsik)
    nq1, nq2, nq3, nq4 = statquad(frfile)
    areaf = calarea(frfile, 4, 5)  # 幅值面积
    aread = calarea(frfile, 6, 7)  # 距离面积
    if mode == 'tt':
        ta, tt50, tt75, tta, q, m1, m2, a1, a2 = calalis_tt(frfile, t_idx)
        ta, tt50, tt75, tta, m1, m2, a1, a2, areaf, aread = get_round([ta, tt50, tt75, tta, m1, m2, a1, a2, areaf, aread], [1, 1, 1, 1, 1, 1, 1, 1, 3, 3])
        mpfile = [ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread]
    else:
        qa, ra, qra, q, m1, m2, a1, a2 = calalis(frfile)
        qa, ra, qra, m1, m2, a1, a2, areaf, aread = get_round([qa, ra, qra, m1, m2, a1, a2, areaf, aread], [1, 1, 1, 1, 1, 1, 1, 3, 3])
        mpfile = [qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread]
    rlis = calrt(frfile, 1)
    wg, m, n, js, ts, ns = caljr(frfile, rlis, 1, [4, 5], [6, 7])
    mjfile0, tjfile = writejr(wg, m, n, js, ts, ns)
    mjfile1 = writemj1(mjfile0)
    saf, sad = writesarea(tjfile, mjfile0, frfile, [4, 5], [6, 7])
    mjfile = mjfile1 + [saf, sad]
    qrdma, qrdav, qrdmse = calmas(frfile, 3)  # 偶极子间距最大/平均/均方差
    qrcd, qrcar, qrcre9 = calqrc(frfile, 8, 9)  # QR中心距离、面积、区域9
    dppma, dppav, dppmse = calmas(frfile, 10)  # 正离散度
    dpnma, dpnav, dpnmse = calmas(frfile, 11)  # 负离散度
    pnpma, pnpflu = calmfl(frfile, 12)  # 正主极子数量最大、波动次数
    pnnma, pnnflu = calmfl(frfile, 13)  # 负主极子数量
    epma, epav, epmse = calmas(frfile, 14)  # 正离心率
    enma, enav, enmse = calmas(frfile, 15)  # 负离心率
    qrdma, qrdav, qrdmse, qrcd, qrcar = get_round([qrdma, qrdav, qrdmse, qrcd, qrcar], [1, 1, 1, 1, 3])
    mdfile = [qrdma, qrdav, qrdmse, qrcd, qrcar, qrcre9]
    dppma, dppav, dppmse, dpnma, dpnav, dpnmse = get_round([dppma, dppav, dppmse, dpnma, dpnav, dpnmse], [1, 1, 1, 1, 1, 1])
    mefile = [dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu]
    epma, epav, epmse, enma, enav, enmse = get_round([epma, epav, epmse, enma, enav, enmse], [2, 2, 1, 2, 2, 1])
    mffile = [epma, epav, epmse, enma, enav, enmse]
    return mpfile, mjfile, mdfile, mefile, mffile, cdfile


def writedisp(matrixes, timelis0, mcglines, mode='qrs'):
    '''离散度指标和等级计算'''
    mlfile, rksfile = [], []
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    rkslis = []
    for j2 in range(q_peak-5):
        rkslis.append(0)
    for j2 in range(q_peak-5, t_end+1):
        matrix = matrixes[j2-1]
        Z = norms0(matrix)  # 正负归一化
        pn = statpn(Z, vmin=0.3)  # 多极子
        dpp, dpn = caldp(pn)  # 离散度
        disp = max(dpp, dpn)
        rdp = rkdp(disp)
        rkslis.append(rdp)
        if j2 in [q_peak, r_peak, s_peak, t_peak]:
            mlfile.append(disp)
            rksfile.append(rdp)
    tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end, mode='pn')
    t1, t2 = get_qrs_t12(mcglines, q_peak, 5, 0.975)  # 截断、阈值
    t3, t4 = get_qrs_t12(mcglines, r_peak, 10, 0.95)
    t5, t6 = get_qrs_t12(mcglines, s_peak, 5, 0.975)
    dpq = sum(rkslis[j2] for j2 in range(t1, t2) if rkslis[j2] >= 2)
    dpr = sum(rkslis[j2] for j2 in range(t3, t4) if rkslis[j2] >= 2)
    dps = sum(rkslis[j2] for j2 in range(t5, t6) if rkslis[j2] >= 2)
    dpt = sum(rkslis[j2] for j2 in range(tt1, tt2) if rkslis[j2] >= 2)
    if mode == 'newqrs':  # 修正后
        dpqrs = round(10 * (dpq / (t2-t1) + dpr / (t4-t3) + dps / (t6-t5)), 1)
    else:  # 'qrs'->代码错误
        dpqrs = round(10 * (dpq / (t2-t1) + dpq / (t4-t3) + dpq / (t6-t5)), 1)
    dptt = round(20 * dpt / (tt2-tt1), 1)
    rkqrs, rktt = rkdpqrs(dpqrs), rkdptt(dptt)
    noq, nor, nos = noqrs(t2-t1), noqrs(t4-t3), noqrs(t6-t5)
    mlfile.extend([dpqrs, dptt, noq, nor, nos])
    rksfile.extend([rkqrs, rktt])
    return mlfile, rksfile


def writemetric_tt0(matrixes, mcglines, t_onset, t_peak, t_end):
    '''生成等磁图指标TT-初版'''
    # top5指标
    tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end)
    Mf = calmf(matrixes, tt1, tt2)
    flist = []
    for jj in range(tt1, tt2 + 1):
        matrix = matrixes[jj-1]
        Nf = matrix.max() - matrix.min()  # 幅值
        px, py, nx, ny = get_dipole(matrix)
        fx, fy = calptcors(Nf/Mf, ny-py, px-nx)  # 面积-心磁坐标
        flist.append([fx, fy])
    area = calarea(flist, 0, 1)
    mpfile = [get_round(area, 3)]
    # # 全部指标
    # mpfile = []
    # matrix = matrixes[t_peak-1]
    # px, py, nx, ny = get_dipole(matrix)
    # Tangle = calangle(ny-py, px-nx)  # 角度
    # tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end)
    # Mf = calmf(matrixes, tt1, tt2)
    # rtfile, flist, alist = [], [], []  # 收集位置和幅值
    # for jj in range(tt1, tt2 + 1):
    #     matrix = matrixes[jj-1]
    #     Nf = matrix.max() - matrix.min()  # 幅值
    #     px, py, nx, ny = get_dipole(matrix)
    #     a = calangle(ny-py, px-nx)  # 角度
    #     q = calquad(a)  # 象限
    #     fx, fy = calptcors(Nf/Mf, ny-py, px-nx)  # 面积-心磁坐标
    #     flist.append([fx, fy])
    #     alist.append(a)
    #     if jj > tt1:
    #         rota = abs(alist[-1]-alist[-2])
    #         rota = min(360-rota, rota)
    #         rtfile.append(rota)
    # area = calarea(flist, 0, 1)
    # qra, q = cal_qra_q(alist)
    # m1, m2, a1, a2 = stataf(alist)
    # rtfile.sort()  # TT50、TT75
    # tt50 = np.median(rtfile)  # 中位数
    # tt75 = np.percentile(rtfile, (75))  # 第3四分位数
    # area, qra, m1, m2, a1, a2, tt50, tt75, Tangle = get_round([area, qra, m1, m2, a1, a2, tt50, tt75, Tangle], [3, 1, 1, 1, 1, 1, 1, 1, 1])
    # mpfile = [area, qra, q, m1, m2, a1, a2, tt50, tt75, Tangle]
    return mpfile


def writemj1(line):
    wg, m, n = line[0], line[1], line[2]
    ms, fs, rs, vs, mses, qs, afs, ads = [], [], [], [], [], [], [], []
    for j1 in range(m):
        ms.append(line[3 + j1])
    pm, nm = 0, 0
    if len(ms) > 0:
        pm = max(0, max(ms))  # 顺时针跳转最大值
        nm = min(0, min(ms))  # 逆时针跳转最大值
    for j1 in range(int(n)):
        fs.append(line[3 + m + 8 * j1])  # 帧
        rs.append(line[4 + m + 8 * j1])  # 转角
        vs.append(line[5 + m + 8 * j1])  # 角速度
        mses.append(line[6 + m + 8 * j1])  # 均方差
        qs.append(line[8 + m + 8 * j1])  # 象限
        afs.append(line[9 + m + 8 * j1])  # 面积-幅值
        ads.append(line[10 + m + 8 * j1])  # 面积-距离
    r = sum(rs)  # 转角和
    v, mse = 0, 0
    for j1 in range(n):
        v += fs[j1] * vs[j1]
        mse += fs[j1] * mses[j1]
    v = v / sum(fs)  # 平均角速度
    mse = mse / sum(fs)  # 平均均方差（扰动性）
    v1 = np.average(vs)
    mse1 = np.average(mses)
    nqs, vqs = [0, 0, 0, 0], [0, 0, 0, 0]
    for j1 in range(int(n)):
        nqs[int(qs[j1]) - 1] += 1  # 平均跳转象限
        vqs[int(qs[j1]) - 1] += fs[j1]  # 累计跳转象限
    af = sum(afs)  # 面积-幅值和
    ad = sum(ads)  # 面积-距离和
    pm, nm, r, v, mse, af, ad, v1, mse1 = get_round([pm, nm, r, v, mse, af, ad, v1, mse1], [3, 3, 2, 2, 2, 3, 3, 2, 2])
    mjfile1 = [wg, m, pm, nm, r, v, mse, nqs[0], nqs[1], nqs[2], nqs[3], vqs[0], vqs[1], vqs[2], vqs[3], af, ad, v1, mse1]
    return mjfile1


def getts(tline):
    ts = []
    for i1 in range(tline[0]):
        if i1 < tline[0] - 1:
            ts.append([tline[1+2*i1], tline[2+2*i1]])
        else:  # 最后一个取t+1=r_peak+1
            ts.append([tline[1+2*i1], tline[2+2*i1]+1])
    return ts


def getava(jline):
    alis = []
    for i1 in range(jline[2]):
        alis.append(jline[7 + jline[1] + 8 * i1])
    return alis


def getxys(frfile, seq, ts):
    lis, xss, yss = [], [], []
    for i1 in range(len(ts)):
        lis.append([[], []])
    for i1 in range(len(frfile)):
        line = frfile[i1]
        xss.append(line[seq[0]])
        yss.append(line[seq[1]])
        for j1 in range(len(ts)):
            if line[0] in range(ts[j1][0], ts[j1][1]):
                lis[j1][0].append(line[seq[0]])
                lis[j1][1].append(line[seq[1]])
                break
    return lis, xss, yss


def calavacors(alis, lis):
    xss, yss = [], []
    for i1 in range(len(alis)):
        dx = np.cos(alis[i1] * np.pi / 180)
        dy = np.sin(alis[i1] * np.pi / 180)
        dz = 0
        for j1 in range(len(lis[i1][0])):
            fx, fy = lis[i1][0][j1], lis[i1][1][j1]
            fz = pow(fx*fx+fy*fy, 1/2)
            dz += fz
        dz = dz / len(lis[i1][0])  # 平均归一化幅值
        xs, ys = calptcors(dz, dx, dy)
        xss.append(xs)
        yss.append(ys)
    return xss, yss


def writesarea(tjfile, jline, frfile, seqs1, seqs2):
    '''2种平均角度正负指向图'''
    ts = getts(tjfile)  # 获取时间段
    alis = getava(jline)  # 平均角度
    lis, xss, yss = getxys(frfile, [seqs1[0], seqs1[1]], ts)
    xss, yss = calavacors(alis, lis)  # 平均角度坐标
    af = calarea1(xss, yss)
    lis, xss, yss = getxys(frfile, [seqs2[0], seqs2[1]], ts)
    xss, yss = calavacors(alis, lis)
    ad = calarea1(xss, yss)
    af, ad = get_round([af, ad], [3, 3])
    return af, ad


def mcg_float(mcglines, loct):
    '''36心磁数值'''
    mcgdata = np.array(mcglines[loct-1].split()[1:]).reshape((6, 6))
    mcgdata = mcgdata.astype(float)
    return mcgdata


def get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t):
    '''TT有效波段, 以正向波判断'''
    tt1, tt2 = t_onset, t_end
    for jj in range(t_onset, t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if max(mcgdata.ravel()) > max_t:
            tt1 = jj  # 有效波段起点
            break
    for jj in range(0, t_end - t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if max(mcgdata.ravel()) > max_t:
            tt2 = t_end - jj  # 有效波段终点
            break
    return tt1, tt2


def get_tt_valid_n(mcglines, t_onset, t_peak, t_end, max_t):
    '''TT有效波段, 以负向波判断'''
    tt1, tt2 = t_onset, t_end
    for jj in range(t_onset, t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if min(mcgdata.ravel()) < max_t:
            tt1 = jj  # 有效波段起点
            break
    for jj in range(0, t_end-t_peak):
        mcgdata = mcg_float(mcglines, t_end-jj)
        if min(mcgdata.ravel()) < max_t:
            tt2 = t_end - jj  # 有效波段终点
            break
    return tt1, tt2


def get_tt12(mcglines, t_onset, t_peak, t_end, mode='p'):
    '''TT有效波段获取, mode=pn时以正负向波较大值判断'''
    mcgdata = mcg_float(mcglines, t_peak)
    max_t = max(mcgdata.ravel()) / 3  # TT的1/3数值，有效波段——
    if mode == 'p':
        tt1, tt2 = get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t)
    else:  # mode='pn'
        max_t2 = -1 * min(mcgdata.ravel()) / 3
        if max_t >= max_t2:
            tt1, tt2 = get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t)
        else:
            tt1, tt2 = get_tt_valid_n(mcglines, t_onset, t_peak, t_end, -1*max_t2)
    return tt1, tt2


def get_qrs_valid_p(mcglines, locp, nl, max_t):
    '''QRS有效波段, 以正向波判断'''
    t1, t2 = locp, locp + 1
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp-jj)
        if max(mcgdata.ravel()) < max_t:
            t1 = locp - jj  # 有效波段起点
            break
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp+jj+1)
        if max(mcgdata.ravel()) < max_t:
            t2 = locp+jj+1  # 有效波段终点
            break
    return t1, t2


def get_qrs_valid_n(mcglines, locp, nl, max_t):
    '''QRS有效波段, 以负向波判断'''
    t1, t2 = locp, locp + 1
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp-jj)
        if min(mcgdata.ravel()) > max_t:
            t1 = locp - jj  # 有效波段起点
            break
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp+jj+1)
        if min(mcgdata.ravel()) > max_t:
            t2 = locp+jj+1  # 有效波段终点
            break
    return t1, t2


def get_qrs_t12(mcglines, locp, nl, vf):
    '''Q/R/S有效波段获取'''
    mcgdata = mcg_float(mcglines, locp)
    max_t = max(mcgdata.ravel()) * vf  # TT的1/3数值，有效波段——
    max_t2 = -1 * min(mcgdata.ravel()) * vf
    if max_t >= max_t2:
        t1, t2 = get_qrs_valid_p(mcglines, locp, nl, max_t)
    else:
        t1, t2 = get_qrs_valid_n(mcglines, locp, nl, -1*max_t2)
    return t1, t2


def get_mfm_mts_QR(timelis, matrixes_list, mflis, Zqr):
    '''
    等磁图指标QR
    timelis: 时刻点
    mflis: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics QR...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        q_peak, r_peak = timelis[i1][0], timelis[i1][1]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        pnptm2 = cal_pnptm(matrixes, mflis0, range(q_peak-5, r_peak+1))
        q_peak = change_QR(pnptm2, q_peak)  # Q异常修正
        timelis[i1][0] = q_peak
        frfile, rtfile = cal_frrt(matrixes, q_peak, r_peak)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zqr)
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_RS(timelis, matrixes_list, mflis, Zrs):
    '''
    等磁图指标RS
    timelis: 时刻点
    mflis: 最大幅值
    Zrs: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics RS...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        r_peak, s_peak = timelis[i1][1], timelis[i1][2]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        pnptm2 = cal_pnptm(matrixes, mflis0, range(r_peak, s_peak+6))
        s_peak = change_RS(pnptm2, s_peak)  # S异常修正
        timelis[i1][2] = s_peak
        frfile, rtfile = cal_frrt(matrixes, r_peak, s_peak)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zrs)
        print(cdfile)
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_TT(timelis, matrixes_list, mflis, Ztt):
    '''
    等磁图指标TT
    timelis: 时刻点
    mflis: 最大幅值
    Ztt: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics TT...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        t_onset, t_peak, t_end = timelis[i1][3], timelis[i1][4], timelis[i1][5]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        frfile, rtfile = cal_frrt(matrixes, t_onset, t_end)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt, t_idx=t_peak-t_onset, mode='tt')
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_TT0(timelis, matrixes_list, mcglines_list):
    '''
    等磁图指标TT-初版
    timelis: 时刻点
    mflis: 最大幅值
    输出指标列表:
        area, rotation, quadrant, m1 m2 a1 a2, TT50 TT75 Tangle
    '''
    mpfiles = []
    for i1 in range(len(matrixes_list)):
        t_onset, t_peak, t_end = timelis[i1][3], timelis[i1][4], timelis[i1][5]
        matrixes, mcglines = matrixes_list[i1], mcglines_list[i1]
        mpfile = writemetric_tt0(matrixes, mcglines, t_onset, t_peak, t_end)
        mpfiles.append(mpfile)
    return mpfiles


def get_mfs(mlines, Qp=None, Te=None):
    mf = []
    if not Qp:
        Qp, Te = 10, len(mlines) - 10
    for ik in range(len(mlines)):
        if ik < Qp - 10 or ik >= Te + 10:
            mf.append(0)
        else:
            mcgdata = mcg_float(mlines, ik)
            mf.append(max(mcgdata.max(), -1 * mcgdata.min()))
    return mf


def cv_interpolate(mfm_file, Qp, Te):
    '''计算100*100cv插值, 返回矩阵和最大幅值列表'''
    mlines = get_lines(mfm_file)
    Te = min(len(mlines), Te)
    matrixes, mf = [], []
    for ik in range(len(mlines)):
        if ik < Qp - 10 or ik >= Te + 10:
            matrixes.append(np.zeros((100, 100)))
            mf.append(0)
        else:
            mcgdata = mcg_float(mlines)
            mf.append(max(mcgdata.max(), -1 * mcgdata.min()))
            matrix = cv2.resize(mcgdata, (100, 100), interpolation=cv2.INTER_CUBIC)
            matrixes.append(matrix)
    return matrixes, mf

