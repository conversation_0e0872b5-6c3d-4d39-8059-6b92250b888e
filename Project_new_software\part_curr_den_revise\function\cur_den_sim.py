'''
@Project ：pythonProject 
@File    ：cur_den_sim.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/10/8 16:15
该程序用于先给定电流源生成磁图，然后进行插值，最后进行电流密度图
'''
import numpy as np

class CurrentDensity:
    def __init__(self, bz, x, y, z):
        """
        :param bz: 磁场，这里矩阵一般是 101*101， 代表20cm * 20cm中的磁场数据
        :param x: x轴的矩阵也是 101 * 101
        :param y: y轴的矩阵也是 101 * 101
        :param z: z轴为0
        """
        self.bz = bz
        self.x, self.y = x / 100, y / 100
        self.z = z


    def cur_density(self):
        """
        :return: 通过一阶导数的中心差分方法来计算电流密度图, 其中边界上的导数由于没有足够的点做中心差分，
        因此对于边界上限的点用
        u'(x_N) = [3u(x_(N)) - 4u(x_(N-1)) + u(x_(N-2))] / (2 * delta_x)
        对于边界下限的点用
        u'(x_0) = [4u(x_1) - u(x_2) + 3u(x_0)] / (2 * delta_x)
        """
        size = self.x.shape[0]
        q_x, q_y = np.zeros((size, size)), np.zeros((size, size))
        d_3 = 1 / 0.04 ** 3 - 2 / 0.09 ** 3 + 1 / 0.14 ** 3
        delta_x, delta_y = self.x[0][1] - self.x[0][0], self.y[1][0] - self.y[0][0]  # 记录差分格式的delta_x和delta_y
        for i in range(size):  # 行
            for j in range(size):  # 列
                if i == 100:  # 如果是最后一行也就是y最上边
                    q_x[100][j] = \
                        (3 * self.bz[100][j] - 4 * self.bz[99][j] + self.bz[98][j]) / (2 * delta_y) / d_3 * 1e7
                elif i == 0:
                    q_x[0][j] = (4 * self.bz[1][j] - self.bz[2][j] - 3 * self.bz[0][j]) / (2 * delta_y) / d_3 * 1e7
                else:
                    q_x[i][j] = (self.bz[i + 1][j] - self.bz[i - 1][j]) / (2 * delta_y) / d_3 * 1e7
                if j == 100:  # 如果是最后一列也就是y最上边
                    q_y[i][100] = \
                        - (3 * self.bz[i][100] - 4 * self.bz[i][99] + self.bz[i][98]) / (2 * delta_x) / d_3 * 1e7
                elif j == 0:
                    q_y[i][0] = -(4 * self.bz[i][1] - self.bz[i][2] - 3 * self.bz[i][0]) / (2 * delta_x) / d_3 * 1e7
                else:
                    q_y[i][j] = -(self.bz[i][j + 1] - self.bz[i][j - 1]) / (2 * delta_x) / d_3 * 1e7
        return q_x, q_y

