'''
@Project ：pythonProject2025 
@File    ：indicator_xu.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/1/2 14:33 
'''
import numpy as np
import math


def TT_feature(cur_dir_1, inten, points, To, Tp, Te):
    """
    :param cur_dir_1: TT段加上Rp的电流方向
    :param inten: TT段加上Rp的电流强度
    :param points: TT段加上Rp的电流源位置
    :param To: T_onset
    :param Tp: T_peak
    :return: [ratio_strength, max_angle, max_angle_1, I, I_var, flatness_factor, ratio_str_RT, theta,
              overall_std_dev, overall_std_dev_1, TT_quad, TT_quad_1, TT_rotat]
    """
    pi = math.pi
    size = inten.shape[0]
    intensity = inten.reshape((size, 1))[:-1]
    cur_dir = cur_dir_1[:-1] * intensity  # TT时刻的电流方向，含强度
    n = cur_dir.shape[0]
    T_start = round((Tp + To)/2) - To  # 取To和Tp中点为起点
    T_end = round((Tp + Te) / 2) - To   # 取Tp 和 Te中点为中点
    cur_dir_diff_range = cur_dir[T_start:T_end + 1, :]
    points_diff_range = points[T_start:T_end + 1, :]

    # 计算最大点源强度和最小点源强度的比值
    max_strength = max(abs(inten)[:-1])  # 在计算T的指标是都只取到最后一列, 因为最后一列的R时刻
    min_strength = min(abs(inten)[:-1])
    ratio_strength = max_strength / min_strength

    # 计算TT段向量扫过的最大夹角

    max_angle = 0
    for i in range(len(cur_dir)):
        for j in range(i + 1, len(cur_dir)):
            dot_product = np.dot(cur_dir[i], cur_dir[j])
            norm_A = np.linalg.norm(cur_dir[i])
            norm_B = np.linalg.norm(cur_dir[j])
            cos_theta = dot_product / (norm_A * norm_B)
            theta = np.arccos(cos_theta) * 180 / pi
            if theta > max_angle:
                max_angle = theta

    max_angle_1 = 0  # 取不同起点的最大夹角
    for i in range(len(cur_dir_diff_range)):
        for j in range(i + 1, len(cur_dir_diff_range)):
            dot_product = np.dot(cur_dir_diff_range[i], cur_dir_diff_range[j])
            norm_A = np.linalg.norm(cur_dir_diff_range[i])
            norm_B = np.linalg.norm(cur_dir_diff_range[j])
            cos_theta = dot_product / (norm_A * norm_B)
            theta = np.arccos(cos_theta) * 180 / pi
            if theta > max_angle_1:
                max_angle_1 = theta


    # 计算总电流向量, 方差电流向量, 平坦度因子
    T1 = Tp - To
    T2 = n
    I_X, I_Y = np.sum(cur_dir[:, 0]), np.sum(cur_dir[:, 1])
    I = np.sqrt(I_X ** 2 + I_Y ** 2)
    I1_X = np.sum(cur_dir[:T1, 0])
    I1_Y = np.sum(cur_dir[:T1, 1])
    I2_X = np.sum(cur_dir[:T2, 0])
    I2_Y = np.sum(cur_dir[:T2, 1])
    I_varX = I2_X - I1_X
    I_varY = I2_Y - I1_Y
    I_var = np.sqrt(I_varX ** 2 + I_varY ** 2)

    if I != 0:
        flatness_factor = I_var / I
    else:
        flatness_factor = 0

    # R和T波峰最大点源强度比值, 点源向量夹角
    pi = math.pi
    str_R = np.array(inten)[-1]
    str_T = np.array(inten)[Tp - To]
    ratio_str_RT = str_R / str_T
    dir_R, stren_R = np.array(cur_dir_1)[-1], np.array(inten)[-1]
    dir_T, stren_T = np.array(cur_dir_1)[Tp - To], np.array(inten)[Tp - To]
    cur_dir_R, cur_dir_T = dir_R * stren_R, dir_T * stren_T
    dot_product = np.dot(cur_dir_R, cur_dir_T)
    norm_R, norm_T = np.linalg.norm(cur_dir_R), np.linalg.norm(cur_dir_T)
    cos_theta = dot_product / (norm_R * norm_T)
    theta = np.arccos(cos_theta) * 180 / pi

    # 计算TT段点源散度
    mean_point_3d = np.mean(points, axis=0)  # 计算均值
    distances_3d = np.linalg.norm(points - mean_point_3d, axis=1)
    overall_variance_3d = np.mean(distances_3d ** 2)
    overall_std_dev_3d = np.sqrt(overall_variance_3d)

    mean_point_2d = np.mean(points[:, 0:2], axis=0)  # 计算均值
    distances_2d = np.linalg.norm(points[:, 0:2] - mean_point_2d, axis=1)
    overall_variance_2d = np.mean(distances_2d ** 2)
    overall_std_dev_2d = np.sqrt(overall_variance_2d)

    mean_point_3d_1 = np.mean(points_diff_range, axis=0)  # 计算均值
    distances_3d_1 = np.linalg.norm(points_diff_range - mean_point_3d_1, axis=1)
    overall_variance_3d_1 = np.mean(distances_3d_1 ** 2)
    overall_std_dev_3d_1 = np.sqrt(overall_variance_3d_1)

    mean_point_2d_1 = np.mean(points_diff_range[:, 0:2], axis=0)  # 计算均值
    distances_2d_1 = np.linalg.norm(points_diff_range[:, 0:2] - mean_point_2d_1, axis=1)
    overall_variance_2d_1 = np.mean(distances_2d_1 ** 2)
    overall_std_dev_2d_1 = np.sqrt(overall_variance_2d_1)

    # 计算TT段所处象限 注原来的算法好像是TT和R都计算了，这里只取TT, 从To~~Te
    for vector in cur_dir:
        x, y = vector
        if x < 0 or y > 0:
            TT_quad = 1
            break
        else:
            TT_quad = 0

    for vector in cur_dir_diff_range:
        x, y = vector
        if x < 0 or y > 0:
            TT_quad_1 = 1
            break
        else:
            TT_quad_1 = 0

    # 计算TT段向量旋转方向
    n = len(cur_dir)
    quarter_n = math.floor(n // 4)
    sum_first_quarter = np.sum(cur_dir[:quarter_n], axis=0)
    sum_middle_half = np.sum(cur_dir[quarter_n:quarter_n * 3], axis=0)
    sum_last_quarter = np.sum(cur_dir[quarter_n * 3:], axis=0)
    x1, y1 = sum_first_quarter
    x2, y2 = sum_middle_half
    x3, y3 = sum_last_quarter
    det_1 = x1 * y2 - y1 * x2
    det_2 = x2 * y3 - y2 * x3
    if max_angle > 10:
        if det_1 <= 0 and det_2 <= 0:
            TT_rotat = 0
        else:
            TT_rotat = 1
    else:
        TT_rotat = 0

    result = [ratio_strength, max_angle, max_angle_1, I, I_var, flatness_factor, ratio_str_RT, theta,
              overall_std_dev_3d, overall_std_dev_3d_1, overall_std_dev_2d, overall_std_dev_2d_1,
              TT_quad, TT_quad_1, TT_rotat]
    return result

def plot(heart_point, indices):
    '''
    提取不旋转的心脏的QRS坐标
    '''
    x_4, y_4, z_4 = heart_point[:, 0] * 1000, heart_point[:, 1] * 1000, heart_point[:, 2] * 1000

    # QRS 时刻的坐标
    x_S_0, x_S_5, x_S_10 = x_4[indices[2]], x_4[indices[3]], x_4[indices[4]]
    y_S_0, y_S_5, y_S_10 = y_4[indices[2]], y_4[indices[3]], y_4[indices[4]]
    z_S_0, z_S_5, z_S_10 = z_4[indices[2]], z_4[indices[3]], z_4[indices[4]]
    x_S, y_S, z_S = (x_S_0 + x_S_5 + x_S_10) / 3, (y_S_0 + y_S_5 + y_S_10) / 3, (z_S_0 + z_S_5 + z_S_10) / 3
    x_Q, y_Q, z_Q = x_4[indices[0]], y_4[indices[0]], z_4[indices[0]]
    x_R, y_R, z_R = x_4[indices[1]], y_4[indices[1]], z_4[indices[1]]

    # 坐标相对位置计算
    x_Q_x_R, x_R_x_S, x_Q_x_S = x_Q - x_R, x_R - x_S, x_Q - x_S
    y_Q_y_R, y_R_y_S, y_Q_y_S = y_Q - y_R, y_R - y_S, y_Q - y_S
    z_Q_z_R, z_R_z_S, z_Q_z_S = z_Q - z_R, z_R - z_S, z_Q - z_S

    result = [x_Q_x_R, x_R_x_S, x_Q_x_S, y_Q_y_R, y_R_y_S, y_Q_y_S, z_Q_z_R, z_R_z_S, z_Q_z_S, x_S, y_S, z_S]
    return result


def plot_rotation(heart_point, indices, strength, qx, qy, points_qr):
    '''
    旋转心脏并提取坐标
    '''
    merged_array_front = np.column_stack((heart_point[:, 0], heart_point[:, 1], heart_point[:, 2])) * 1000
    rotation_angle1 = np.deg2rad(-50)
    rotation_matrix_x = np.array([[1, 0, 0],
                                  [0, np.cos(rotation_angle1), -np.sin(rotation_angle1)],
                                  [0, np.sin(rotation_angle1), np.cos(rotation_angle1)]])
    transformed_points_x = np.dot(merged_array_front, rotation_matrix_x)
    rotation_angle2 = np.deg2rad(-45)
    rotation_matrix_z = np.array([[np.cos(rotation_angle2), np.sin(rotation_angle2), 0],
                                  [-np.sin(rotation_angle2), np.cos(rotation_angle2), 0],
                                  [0, 0, 1]])
    transformed_points_z = np.dot(transformed_points_x, rotation_matrix_z)
    rotation_angle3 = np.deg2rad(-15)
    rotation_matrix_y = np.array([[np.cos(rotation_angle3), 0, -np.sin(rotation_angle3)],
                                  [0, 1, 0],
                                  [np.sin(rotation_angle3), 0, np.cos(rotation_angle3)]])
    transformed_points_y = np.dot(transformed_points_z, rotation_matrix_y)
    x_4 = transformed_points_y[:, 0]
    y_4 = transformed_points_y[:, 1]
    z_4 = transformed_points_y[:, 2]

    # QRS 时刻的坐标
    x_S_0, x_S_5, x_S_10 = x_4[indices[2]], x_4[indices[3]], x_4[indices[4]]
    y_S_0, y_S_5, y_S_10 = y_4[indices[2]], y_4[indices[3]], y_4[indices[4]]
    z_S_0, z_S_5, z_S_10 = z_4[indices[2]], z_4[indices[3]], z_4[indices[4]]
    x_S_rot, y_S_rot, z_S_rot = (x_S_0 + x_S_5 + x_S_10) / 3, (y_S_0 + y_S_5 + y_S_10) / 3, (z_S_0 + z_S_5 + z_S_10) / 3
    x_Q, y_Q, z_Q = x_4[indices[0]], y_4[indices[0]], z_4[indices[0]]
    x_R, y_R, z_R = x_4[indices[1]], y_4[indices[1]], z_4[indices[1]]
    Q_strength, R_strength, S_strength = strength[0], strength[1], strength[2]
    Q_x_direct, R_x_direct, S_x_direct = qx[0], qx[1], qx[2]
    Q_y_direct, R_y_direct, S_y_direct = qy[0], qy[1], qy[2]

    # 坐标相对位置计算
    x_Q_x_R_rot, x_R_x_S_rot, x_Q_x_S_rot = x_Q - x_R, x_R - x_S_rot, x_Q - x_S_rot
    y_Q_y_R_rot, y_R_y_S_rot, y_Q_y_S_rot = y_Q - y_R, y_R - y_S_rot, y_Q - y_S_rot
    z_Q_z_R_rot, z_R_z_S_rot, z_Q_z_S_rot = z_Q - z_R, z_R - z_S_rot, z_Q - z_S_rot

    # 指标:QR非常聚集->QR段点源的离散度
    mean_point = np.mean(points_qr, axis=0)  # 计算均值
    distances_QR = np.linalg.norm(points_qr - mean_point, axis=1)
    overall_variance_QR = np.mean(distances_QR ** 2)
    overall_std_dev_QR = np.sqrt(overall_variance_QR)

    result = [x_Q_x_R_rot, x_R_x_S_rot, x_Q_x_S_rot, y_Q_y_R_rot, y_R_y_S_rot, y_Q_y_S_rot, z_Q_z_R_rot, z_R_z_S_rot,\
        z_Q_z_S_rot, x_S_rot, y_S_rot, z_S_rot, Q_strength, R_strength, S_strength, Q_x_direct, Q_y_direct, R_x_direct, \
        R_y_direct, S_x_direct, S_y_direct, overall_std_dev_QR]
    return result
