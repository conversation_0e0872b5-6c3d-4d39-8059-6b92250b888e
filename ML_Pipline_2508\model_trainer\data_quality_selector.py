"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: data_quality_selector
date: 2024/11/13 下午2:23
desc: 
"""
import logging
from typing import Union, Tuple, Dict, Optional

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import tqdm
import xgboost
import xgboost as xgb
from sklearn.datasets import make_classification
from sklearn.ensemble import IsolationForest
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    log_loss
)
from sklearn.metrics.pairwise import pairwise_distances
from sklearn.model_selection import KFold
from sklearn.model_selection import train_test_split
from sklearn.neighbors import LocalOutlierFactor
from sklearn.preprocessing import StandardScaler
from tqdm import tqdm
from xgboost import XGBClassifier, XGBRegressor


class ImprovedHessianOptimizer:
    def __init__(self, n_splits=5, test_size=0.2, random_state=42):
        self.n_splits = n_splits
        self.test_size = test_size
        self.random_state = random_state
        self.model = XGBClassifier(random_state=random_state)
        self.xgb_params = {
            'max_depth': 4,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'objective': 'binary:logistic',
            'eval_metric': 'logloss',
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'seed': random_state
        }

    def _get_tree_predictions(self, model, X, y):
        """
        获取模型在每个阶段的预测结果
        """
        dmatrix = xgb.DMatrix(X, label=y)
        n_trees = len(model.get_booster().get_dump())

        # 存储每个阶段的预测和梯度信息
        stage_preds = []
        stage_grads = []
        stage_hessians = []

        # 收集每个阶段的预测
        for i in range(n_trees):
            # 获取到当前树的预测
            pred = model.get_booster().predict(dmatrix, iteration_range=(0, i+1), output_margin=True)
            stage_preds.append(pred)

            # 计算梯度
            pred_prob = 1.0 / (1.0 + np.exp(-pred))
            grad = pred_prob - y
            hess = pred_prob * (1.0 - pred_prob)

            stage_grads.append(grad)
            stage_hessians.append(hess)

        return np.array(stage_preds), np.array(stage_grads), np.array(stage_hessians)

    def _compute_influence(self, stage_grads, stage_hessians):
        """
        基于梯度和Hessian计算样本影响力
        """
        n_stages, n_samples = stage_grads.shape
        influence = np.zeros((n_samples, n_samples))

        # 累积每个阶段的影响力
        for stage in range(n_stages):
            grads = stage_grads[stage]
            hessians = stage_hessians[stage]

            # 使用近似的Hessian逆矩阵
            h_inv = 1.0 / (hessians + 1e-8)

            # 更新影响力矩阵
            for i in range(n_samples):
                influence[i] += grads[i] * h_inv * grads

        return influence / n_stages  # 归一化

    def get_suggested_weights(self, X, y, scale=0.05, conflict_percentile=5, low_weight=0.5):
        """
        获取建议的样本权重，基于百分位数识别最强烈的对抗样本

        Args:
            X: 输入特征
            y: 标签
            scale: 影响力缩放因子
            conflict_percentile: 判定为强对抗样本的百分位数阈值
            low_weight: 对抗样本的权重值
        """
        base_weights = np.ones(len(X))
        weight_adjustments = np.zeros((self.n_splits, len(X)))
        conflict_scores = np.zeros(len(X))  # 累积每个样本的对抗程度

        for split in tqdm(range(self.n_splits), desc="Processing splits"):
            # 划分数据和对应的索引
            indices = np.arange(len(X))
            X_train, X_test, y_train, y_test, train_indices, test_indices = train_test_split(
                X, y, indices, test_size=self.test_size,
                random_state=self.random_state + split
            )

            # 训练模型
            model = xgb.XGBClassifier(**self.xgb_params)
            model.fit(X_train, y_train, eval_set=[(X_test, y_test)], verbose=False)

            # 获取预测和梯度信息
            _, stage_grads, stage_hessians = self._get_tree_predictions(
                model, X_train, y_train)

            # 计算训练集样本的影响力
            influence = self._compute_influence(stage_grads, stage_hessians)

            # 计算每个训练样本的影响力
            sample_influence = np.mean(influence, axis=1)

            # 标准化影响力
            norm_influence = (sample_influence - np.mean(sample_influence)) / (np.std(sample_influence) + 1e-8)

            # 计算对抗程度（使用影响力的绝对值）
            conflict_degree = np.abs(norm_influence)

            # 找出最强烈的对抗样本（基于百分位数）
            conflict_threshold = np.percentile(conflict_degree, 100 - conflict_percentile)
            strongest_conflicts = conflict_degree > conflict_threshold

            # 计算权重调整
            train_adjustment = -scale * norm_influence

            # 对最强烈的对抗样本设置较大的负调整
            train_adjustment[strongest_conflicts] = -1.0

            # 扩展到完整数据集
            full_adjustment = np.zeros(len(X))
            full_adjustment[train_indices] = train_adjustment

            # 更新权重调整
            weight_adjustments[split] = full_adjustment

            # 累积对抗程度分数
            conflict_score = np.zeros(len(X))
            conflict_score[train_indices] = conflict_degree
            conflict_scores += conflict_score

            logging.info(f"Split {split + 1}/{self.n_splits} - "
                         f"Identified {np.sum(strongest_conflicts)} strongest conflicts, "
                         f"Threshold: {conflict_threshold:.4f}")

        # 计算平均对抗程度
        avg_conflict_scores = conflict_scores / self.n_splits

        # 基于所有split的累积对抗程度确定最终的强对抗样本
        final_conflict_threshold = np.percentile(avg_conflict_scores[avg_conflict_scores > 0],
                                                 100 - conflict_percentile)
        strongest_conflicts_final = avg_conflict_scores > final_conflict_threshold

        # 计算基础权重调整
        final_adjustment = np.mean(weight_adjustments, axis=0)
        final_weights = base_weights + final_adjustment

        # 对最强烈的对抗样本设置低权重
        final_weights[strongest_conflicts_final] = low_weight

        # 确保权重非负且合理
        final_weights = np.clip(final_weights, 0.1, 2.0)

        logging.info(f"\nFinal statistics:"
                     f"\n- Total strongest conflicts: {np.sum(strongest_conflicts_final)}"
                     f"\n- Final conflict threshold: {final_conflict_threshold:.4f}"
                     f"\n- Weight mean: {np.mean(final_weights):.4f}"
                     f"\n- Weight std: {np.std(final_weights):.4f}")

        return final_weights
    def evaluate_weights(self, X, y, weights, X_valid, y_valid):
        """
        评估权重的效果
        """
        # 训练基准模型
        base_model = xgb.XGBClassifier(**self.xgb_params)
        base_model.fit(X, y)
        base_score = log_loss(y_valid, base_model.predict_proba(X_valid))

        # 训练加权模型
        weighted_model = xgb.XGBClassifier(**self.xgb_params)
        weighted_model.fit(X, y, sample_weight=weights)
        weighted_score = log_loss(y_valid, weighted_model.predict_proba(X_valid))

        results = {
            'base_score': base_score,
            'weighted_score': weighted_score,
            'improvement': base_score - weighted_score
        }

        logging.info(f"Evaluation results:")
        logging.info(f"Base score: {base_score:.4f}")
        logging.info(f"Weighted score: {weighted_score:.4f}")
        logging.info(f"Improvement: {base_score - weighted_score:.4f}")

        return results
    def _evaluate_model(self, X: np.ndarray, y: np.ndarray) -> float:
        """评估模型性能"""
        y_pred = self.model.predict(X)
        return np.mean(y_pred == y)

    def apply_weighted_model(self, X_train, y_train, weights, X_test, y_test):
        """
        应用加权训练并评估效果
        """
        # 未加权模型
        base_model = xgb.XGBClassifier(**self.xgb_params)
        base_model.fit(X_train, y_train)
        base_pred = base_model.predict(X_test)
        base_score = np.mean(y_test==base_pred)

        # 加权模型
        weighted_model = xgb.XGBClassifier(**self.xgb_params)
        weighted_model.fit(X_train, y_train, sample_weight=weights)
        weighted_pred = weighted_model.predict(X_test)
        weighted_score = np.mean(y_test==weighted_pred)

        return base_score, weighted_score


class MetaWeightOptimizer:
    """
    Meta-learning based weight optimizer that adjusts training sample weights
    based on validation set performance feedback.
    """

    def __init__(
            self,
            task_type: str = 'classification',
            meta_iterations: int = 5,
            meta_lr: float = 0.1,
            meta_test_size: float = 0.2,
            base_weight: float = 1.0,
            n_weight_groups: int = 10,
            random_state: int = 42,
            beta: float = 0.9,  # momentum factor
            weight_decay: float = 0.01,  # weight decay factor
            lr_decay: float = 0.95  # learning rate decay
    ):
        """
        Args:
            task_type: 'classification' or 'regression'
            meta_iterations: number of meta-learning iterations
            meta_lr: learning rate for weight updates
            meta_test_size: proportion of data to use as meta-test set
            base_weight: initial weight for all samples
            n_weight_groups: number of groups for weight perturbation
            random_state: random seed
        """
        self.task_type = task_type
        self.meta_iterations = meta_iterations
        self.meta_lr = meta_lr
        self.meta_test_size = meta_test_size
        self.base_weight = base_weight
        self.n_weight_groups = n_weight_groups
        self.random_state = random_state

        self.beta = beta
        self.weight_decay = 0.01
        self.lr_decay = 0.95
        if task_type == 'classification':
            self.model = XGBClassifier(random_state=random_state)
        else:
            self.model = XGBRegressor(random_state=random_state)

        self.scaler = StandardScaler()

    def _evaluate_model(self, X: np.ndarray, y: np.ndarray) -> float:
        """Evaluate model performance."""
        y_pred = self.model.predict(X)
        if self.task_type == 'classification':
            return np.mean(y_pred == y)
        else:
            return -np.mean((y_pred - y) ** 2)  # Negative MSE

    def _perturb_weights(
            self,
            base_weights: np.ndarray,
            group_indices: list,
            perturbation: float
    ) -> np.ndarray:
        """Apply perturbation to a group of weights."""
        weights = base_weights.copy()
        weights[group_indices] *= (1 + perturbation)
        return weights / np.mean(weights) * self.base_weight  # Normalize

    def _update_weights(self, weights: np.ndarray, gradient: np.ndarray) -> np.ndarray:
        """SGD-like weight update with momentum"""
        if not hasattr(self, 'momentum'):
            self.momentum = np.zeros_like(weights)

        # Update momentum
        self.momentum = self.beta * self.momentum + (1 - self.beta) * gradient

        # Apply update with momentum
        weights *= (1 + self.meta_lr * self.momentum)

        # Add weight decay
        weights *= (1 - self.meta_lr * self.weight_decay)

        # Normalize
        return weights / np.mean(weights) * self.base_weight

    def get_suggested_weights(
            self,
            # X: np.ndarray,
            # y: np.ndarray,
            X_train,
            y_train,
            X_test,
            y_test,
            sample_weights: Optional[np.ndarray] = None,
            verbose: bool = True,
            patience: int = 3,
            min_delta: float = 1e-4
    ) -> np.ndarray:
        """
        Optimize sample weights using meta-learning approach with SGD-like updates.
        """
        np.random.seed(self.random_state)
        # X_scaled = self.scaler.fit_transform(X)

        # # Split data and initialize weights
        # X_train, X_test, y_train, y_test = train_test_split(
        #     X_scaled, y,
        #     test_size=self.meta_test_size,
        #     random_state=self.random_state
        # )

        current_weights = (np.full(len(X_train), self.base_weight) if sample_weights is None
                           else sample_weights[:len(X_train)].copy())

        # Create random groups
        n_samples = len(X_train)
        samples_per_group = n_samples // self.n_weight_groups
        all_indices = np.arange(n_samples)
        np.random.shuffle(all_indices)
        groups = [all_indices[i:i + samples_per_group]
                  for i in range(0, n_samples, samples_per_group)]

        # Initialize tracking variables
        best_weights = current_weights.copy()
        self.model.fit(X_train, y_train, sample_weight=current_weights)
        best_score = self._evaluate_model(X_test, y_test)
        current_meta_lr = self.meta_lr

        if verbose:
            print(f"Initial score: {best_score:.4f}")

        patience_counter = 0
        previous_best = best_score

        # Meta-learning iterations
        for iteration in range(self.meta_iterations):
            weights_gradient = np.zeros_like(current_weights)
            iteration_improvements = []

            for group_idx, group_indices in enumerate(groups):
                group_best_improvement = 0
                group_gradients = []

                # Multiple perturbation levels for better gradient estimation
                perturbations = np.linspace(-current_meta_lr, current_meta_lr, 5)
                for perturbation in perturbations:
                    perturbed_weights = self._perturb_weights(
                        current_weights, group_indices, perturbation
                    )

                    self.model.fit(X_train, y_train, sample_weight=perturbed_weights)
                    score = self._evaluate_model(X_test, y_test)

                    # Calculate gradient using score difference
                    score_diff = score - best_score
                    if abs(perturbation) > 1e-10:  # Avoid division by zero
                        gradient = score_diff / perturbation
                        group_gradients.append(gradient)

                    if score > best_score:
                        best_score = score
                        best_weights = perturbed_weights.copy()
                        group_best_improvement = max(group_best_improvement, score_diff)

                # Update weights for this group if we have valid gradients
                if group_gradients:
                    avg_gradient = np.mean(group_gradients)
                    weights_gradient[group_indices] = avg_gradient

                iteration_improvements.append(group_best_improvement)

            # Update weights using SGD-like update
            if np.any(weights_gradient != 0):
                current_weights = self._update_weights(current_weights, weights_gradient)

            # Learning rate decay
            current_meta_lr *= self.lr_decay

            # Print iteration progress
            if verbose:
                avg_improvement = np.mean(iteration_improvements)
                max_improvement = np.max(iteration_improvements)
                print(f"Iteration {iteration + 1}/{self.meta_iterations}:")
                print(f"  Current best score: {best_score:.4f}")
                print(f"  Average improvement: {avg_improvement:.4f}")
                print(f"  Max improvement: {max_improvement:.4f}")
                print(f"  Groups improved: {np.sum(np.array(iteration_improvements) > 0)}/{len(groups)}")
                print(f"  Current learning rate: {current_meta_lr:.6f}")

            # Early stopping check
            improvement = best_score - previous_best
            if improvement < min_delta:
                patience_counter += 1
                if verbose:
                    print(f"  No significant improvement. Patience: {patience_counter}/{patience}")
                if patience_counter >= patience:
                    if verbose:
                        print("Early stopping triggered")
                    break
            else:
                patience_counter = 0
                previous_best = best_score

            if np.array_equal(best_weights, current_weights):
                if verbose:
                    print("Weights converged, stopping optimization")
                break

        if verbose:
            print(f"\nOptimization completed:")
            print(f"Initial score: {self._evaluate_model(X_test, y_test):.4f}")
            print(f"Final best score: {best_score:.4f}")
            print(f"Improvement: {(best_score - self._evaluate_model(X_test, y_test)):.4f}")

        final_weights = np.full(len(X_train), self.base_weight)
        final_weights[:len(X_train)] = best_weights
        return final_weights

    def apply_weighted_model(
            self,
            X_train: np.ndarray,
            y_train: np.ndarray,
            weights: np.ndarray,
            X_test: np.ndarray,
            y_test: np.ndarray
    ) -> Tuple[float, float]:
        """
        Train models with full training data and evaluate on external test set.

        Returns:
            Tuple[float, float]: (unweighted_score, weighted_score)
        """
        X_train_scaled = X_train  # self.scaler.fit_transform(X_train)
        X_test_scaled = X_test  # self.scaler.transform(X_test)

        # Train and evaluate unweighted model
        self.model.fit(X_train_scaled, y_train,sample_weight=None)
        unweighted_score = self._evaluate_model(X_test_scaled, y_test)

        # Train and evaluate weighted model
        self.model.fit(X_train_scaled, y_train, sample_weight=weights)
        weighted_score = self._evaluate_model(X_test_scaled, y_test)

        return unweighted_score, weighted_score

class EnhancedMetaWeightOptimizer:
    """Meta-learning based weight optimizer with multiple validation splits"""

    def __init__(
            self,
            task_type: str = 'classification',
            meta_iterations: int = 5,
            meta_lr: float = 0.1,
            n_splits: int = 5,  # 交叉验证折数
            n_weight_groups: int = 10,
            base_weight: float = 1.0,
            lr_decay: float = 0.95,
            random_state: int = 42
    ):
        self.task_type = task_type
        self.meta_iterations = meta_iterations
        self.meta_lr = meta_lr
        self.n_splits = n_splits
        self.n_weight_groups = n_weight_groups
        self.base_weight = base_weight
        self.lr_decay = lr_decay
        self.random_state = random_state

        # 初始化模型
        if task_type == 'classification':
            self.model = XGBClassifier(random_state=random_state)
        else:
            self.model = XGBRegressor(random_state=random_state)

    def _evaluate_model(self, X: np.ndarray, y: np.ndarray) -> float:
        """评估模型性能"""
        y_pred = self.model.predict(X)
        if self.task_type == 'classification':
            return np.mean(y_pred == y)
        else:
            return -np.mean((y_pred - y) ** 2)  # 负MSE

    def _perturb_weights(self, base_weights: np.ndarray, group_indices: list, perturbation: float) -> np.ndarray:
        """对一组权重进行扰动"""
        weights = base_weights.copy()
        weights[group_indices] *= (1 + perturbation)
        return weights / np.mean(weights) * self.base_weight  # 归一化

    def _optimize_split_weights(self, X_train: np.ndarray, y_train: np.ndarray,
                                X_val: np.ndarray, y_val: np.ndarray, train_indices: np.ndarray,
                                n_total:int,
                                verbose: bool = False) -> np.ndarray:
        """优化单次划分的权重"""
        n_samples = len(X_train)
        current_weights = np.full(n_samples, self.base_weight)
        best_weights = current_weights.copy()

        # 初始化完整长度的权重数组
        full_weights = np.full(n_total, self.base_weight)

        # 训练基准模型
        self.model.fit(X_train, y_train)
        best_score = self._evaluate_model(X_val, y_val)
        current_meta_lr = self.meta_lr

        # 创建随机分组
        samples_per_group = n_samples // self.n_weight_groups
        all_indices = np.arange(n_samples)
        np.random.shuffle(all_indices)
        groups = [all_indices[i:i + samples_per_group]
                  for i in range(0, n_samples, samples_per_group)]

        # Meta-learning iterations
        for iteration in range(self.meta_iterations):
            weights_gradient = np.zeros_like(current_weights)

            for group_indices in groups:
                group_gradients = []
                # 对每个组尝试不同程度的扰动
                perturbations = np.linspace(-current_meta_lr, current_meta_lr, 5)

                for perturbation in perturbations:
                    perturbed_weights = self._perturb_weights(
                        current_weights, group_indices, perturbation
                    )

                    # 用扰动后的权重训练和评估
                    self.model.fit(X_train, y_train, sample_weight=perturbed_weights)
                    score = self._evaluate_model(X_val, y_val)

                    # 计算梯度
                    score_diff = score - best_score
                    if abs(perturbation) > 1e-10:
                        gradient = score_diff / perturbation
                        group_gradients.append(gradient)

                    # 更新最佳分数和权重
                    if score > best_score:
                        best_score = score
                        best_weights = perturbed_weights.copy()

                # 更新该组的梯度
                if group_gradients:
                    avg_gradient = np.mean(group_gradients)
                    weights_gradient[group_indices] = avg_gradient

            # 更新权重
            if np.any(weights_gradient != 0):
                update = self.meta_lr * weights_gradient
                current_weights *= (1 + update)
                current_weights = current_weights / np.mean(current_weights) * self.base_weight

            # 学习率衰减
            current_meta_lr *= self.lr_decay

            if verbose:
                print(f"Iteration {iteration + 1}, Score: {best_score:.4f}")
            full_weights = np.full(n_total, self.base_weight)  # 使用传入的总长度
            full_weights[train_indices] = best_weights
        return full_weights

    def get_suggested_weights(self, X: np.ndarray, y: np.ndarray, verbose: bool = True) -> np.ndarray:
        """获取建议的样本权重"""
        np.random.seed(self.random_state)
        n_total = len(X)  # 原始数据集长度
        all_weights = np.zeros((self.n_splits, n_total))  # 完整长度的权重数组
        weight_counts = np.zeros(n_total)  # 记录更新次数

        kf = KFold(n_splits=self.n_splits, shuffle=True, random_state=self.random_state)

        for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
            if verbose:
                print(f"\nOptimizing fold {fold + 1}/{self.n_splits}")

            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]

            # 传入原始数据集长度
            fold_weights = self._optimize_split_weights(
                X_train, y_train, X_val, y_val,
                train_idx, n_total, verbose
            )

            all_weights[fold] = fold_weights
            weight_counts[train_idx] += 1

        # 加权平均
        final_weights = np.sum(all_weights, axis=0) / np.maximum(weight_counts, 1)
        final_weights = final_weights / np.mean(final_weights) * self.base_weight

        return final_weights
    def apply_weighted_model(self, X_train: np.ndarray, y_train: np.ndarray,
                             weights: np.ndarray, X_test: np.ndarray, y_test: np.ndarray) -> Tuple[float, float]:
        """应用权重并评估模型"""
        # 训练和评估无权重模型
        self.model.fit(X_train, y_train)
        unweighted_score = self._evaluate_model(X_test, y_test)

        # 训练和评估有权重模型
        self.model.fit(X_train, y_train, sample_weight=weights)
        weighted_score = self._evaluate_model(X_test, y_test)

        return unweighted_score, weighted_score
class DataQualitySelector:
    def __init__(
            self,
            task_type: str = 'classification',
            n_splits: int = 5,
            weight_threshold: float = 2.0,
            prediction_std_threshold: float = 0.2,
            depth_threshold: float = 0.8,
            final_keep_ratio: float = 0.8
    ):
        self.task_type = task_type
        self.n_splits = n_splits
        self.weight_threshold = weight_threshold
        self.prediction_std_threshold = prediction_std_threshold
        self.depth_threshold = depth_threshold
        self.final_keep_ratio = final_keep_ratio

        self.model_class = XGBClassifier if task_type == 'classification' else XGBRegressor

    def _get_leaf_depths(self, model, X) -> np.ndarray:
        """Get average leaf depths for each sample across all trees."""
        # 确保模型已经训练完成
        if not hasattr(model, 'get_booster'):
            raise ValueError("Model not trained yet")

        # 获取实际的树数量
        n_trees = len(model.get_booster().get_dump())

        if n_trees == 0:
            raise ValueError("No trees in model")

        leaves = model.apply(X)

        # 记录日志
        self.last_apply_shape = leaves.shape
        self.last_tree_count = n_trees

        # 统一处理逻辑
        if len(leaves.shape) == 1:
            depths = np.floor(np.log2(leaves + 1))
        else:
            depths = np.zeros(len(X))
            for tree_idx in range(leaves.shape[1]):
                tree_depths = np.floor(np.log2(leaves[:, tree_idx] + 1))
                depths += tree_depths
            depths /= leaves.shape[1]

        return depths

    def _get_prediction_stats(self, model, X) -> Tuple[np.ndarray, np.ndarray]:
        """Get prediction mean and std for each sample using predict_proba or predict."""
        predictions = []

        # Collect predictions from each tree
        for tree_idx in range(model.n_estimators):
            if self.task_type == 'classification':
                # 使用iteration_range替代ntree_limit
                model.n_estimators = tree_idx + 1
                pred = model.predict_proba(X)[:, 1]
                model.n_estimators = 100  # 重置回原始值
            else:
                model.n_estimators = tree_idx + 1
                pred = model.predict(X)
                model.n_estimators = 100  # 重置回原始值
            predictions.append(pred)

        predictions = np.array(predictions).T
        return predictions.mean(axis=1), predictions.std(axis=1)

    def select_quality_data(
            self,
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series]
    ) -> Tuple[Union[np.ndarray, pd.DataFrame], Union[np.ndarray, pd.Series]]:
        """
        Select high-quality samples based on model feedback.
        """
        is_df = isinstance(X, pd.DataFrame)
        is_series = isinstance(y, pd.Series)

        # 转换为numpy进行计算，但保留索引信息
        X_values = X.values if is_df else X
        y_values = y.values if is_series else y

        n_samples = len(X_values)
        kf = KFold(n_splits=self.n_splits, shuffle=True, random_state=42)

        # Initialize arrays to store metrics
        weight_ratios = np.zeros(n_samples)
        prediction_stds = np.zeros(n_samples)
        avg_depths = np.zeros(n_samples)

        # 使用enumerate获取原始索引
        original_indices = np.arange(n_samples)

        # Collect metrics using cross-validation
        for fold_idx, (train_idx, val_idx) in enumerate(kf.split(original_indices)):
            X_train, X_val = X_values[train_idx], X_values[val_idx]
            y_train, y_val = y_values[train_idx], y_values[val_idx]

            # Train model
            model = self.model_class(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42,
                callbacks=[xgboost.callback.EarlyStopping(rounds=10)]
            )

            eval_set = [(X_val, y_val)]
            model.fit(
                X_train, y_train,
                eval_set=eval_set,
                verbose=False
            )

            # Collect metrics for validation set
            _, pred_stds = self._get_prediction_stats(model, X_val)
            prediction_stds[val_idx] = pred_stds

            depths = self._get_leaf_depths(model, X_val)
            max_depth = depths.max()
            avg_depths[val_idx] = depths / max_depth

            if hasattr(model, 'get_booster'):
                weights = model.get_booster().get_score(importance_type='weight')
                weight_ratios[val_idx] = np.array([weights.get(f'f{i}', 1.0) for i in range(X_values.shape[1])]).mean()

        # Normalize metrics
        weight_ratios = (weight_ratios - weight_ratios.min()) / (weight_ratios.max() - weight_ratios.min() + 1e-8)
        prediction_stds = (prediction_stds - prediction_stds.min()) / (
                prediction_stds.max() - prediction_stds.min() + 1e-8)

        # Calculate quality scores
        quality_scores = (
            # (1 - weight_ratios) * 0.3 +
                (1 - prediction_stds) * 0.8  # +
            # (1 - avg_depths) * 0.2
        )

        # 获取需要保留的样本数
        n_select = int(n_samples * self.final_keep_ratio)

        # 创建布尔掩码，标记要保留的样本
        mask = np.zeros(n_samples, dtype=bool)
        top_indices = np.argsort(quality_scores)[-n_select:]
        mask[top_indices] = True

        # 根据输入类型返回相应格式的数据
        if is_df:
            X_selected = X[mask]
        else:
            X_selected = X_values[mask]

        if is_series:
            y_selected = y[mask]
        else:
            y_selected = y_values[mask]

        return X_selected, y_selected

    def get_sample_scores(
            self,
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series]
    ) -> pd.DataFrame:
        if isinstance(X, pd.DataFrame):
            X = X.values
        if isinstance(y, pd.Series):
            y = y.values

        n_samples = len(X)

        model = self.model_class(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
        model.fit(X, y, verbose=False)

        # 获取预测统计
        pred_mean, pred_std = self._get_prediction_stats(model, X)
        pred_std = (pred_std - pred_std.min()) / (
                pred_std.max() - pred_std.min() + 1e-8)
        # 获取深度信息
        depths = self._get_leaf_depths(model, X)

        # 为每个样本分配相同的权重比率
        if hasattr(model, 'get_booster'):
            weights = model.get_booster().get_score(importance_type='weight')
            avg_weight = np.mean([weights.get(f'f{i}', 1.0) for i in range(X.shape[1])])
            weight_ratios = np.full(n_samples, avg_weight)
        else:
            weight_ratios = np.ones(n_samples)

        # 确保所有数组长度一致
        assert all(len(arr) == n_samples for arr in [weight_ratios, pred_std, depths, pred_mean]), \
            f"Array lengths: {len(weight_ratios)}, {len(pred_std)}, {len(depths)}, {len(pred_mean)}, should all be {n_samples}"

        return pd.DataFrame({
            'weight_ratio': weight_ratios,
            'prediction_std': pred_std,
            'avg_depth': depths,
            'prediction_mean': pred_mean
        })


class EnhancedDataQualitySelector(DataQualitySelector):
    def __init__(
            self,
            task_type: str = 'classification',
            n_splits: int = 5,
            n_models: int = 3,  # 使用多个模型进行交叉验证
            final_keep_ratio: float = 0.8,
            noise_threshold: float = 0.7,  # 噪声样本的阈值
            margin_threshold: float = 0.9  # 边缘样本的阈值
    ):
        super().__init__()
        self.task_type = task_type
        self.n_splits = n_splits
        self.n_models = n_models
        self.final_keep_ratio = final_keep_ratio
        self.noise_threshold = noise_threshold
        self.margin_threshold = margin_threshold

        # 继承原有的XGB模型
        self.xgb_class = XGBClassifier if task_type == 'classification' else XGBRegressor

    def _normalize_scores(self, scores: np.ndarray) -> np.ndarray:
        """将分数归一化到[0,1]区间"""
        return (scores - scores.min()) / (scores.max() - scores.min() + 1e-8)

    def _get_cross_model_consistency(
            self,
            X: np.ndarray,
            y: np.ndarray,
            val_idx: np.ndarray
    ) -> np.ndarray:
        """评估样本在不同模型间的预测一致性"""
        predictions = []

        # 使用多个不同参数的XGB模型
        model_params = [
            {'max_depth': 4, 'subsample': 0.8},
            {'max_depth': 6, 'subsample': 0.9},
            {'max_depth': 8, 'subsample': 0.7}
        ]

        for params in model_params[:self.n_models]:
            model = self.xgb_class(
                n_estimators=100,
                learning_rate=0.1,
                random_state=42,
                **params
            )
            model.fit(X, y, verbose=False)

            if self.task_type == 'classification':
                pred = model.predict_proba(X[val_idx])[:, 1]
            else:
                pred = model.predict(X[val_idx])
            predictions.append(pred)

        predictions = np.array(predictions)
        consistency_scores = 1 - np.std(predictions, axis=0)
        return self._normalize_scores(consistency_scores)

    def _get_stability_score(self, predictions: np.ndarray) -> np.ndarray:
        """
        计算预测稳定性分数
        返回: 越高表示预测越稳定
        """
        pred_std = np.std(predictions, axis=0)
        pred_std = (pred_std - pred_std.min()) / (
                pred_std.max() - pred_std.min() + 1e-8)
        return 1 - pred_std

    def _get_density_score(self, X: np.ndarray) -> np.ndarray:
        """
        计算样本的局部密度分数
        返回: 越高表示样本密度越大(越不可能是异常点)
        """
        lof = LocalOutlierFactor(n_neighbors=20, contamination='auto', novelty=True)
        lof.fit(X)
        # LOF分数越小表示越正常,需要转换
        density_scores = -lof.score_samples(X)
        return 1 - self._normalize_scores(density_scores)

    def _get_novelty_score(self, X: np.ndarray) -> np.ndarray:
        """
        使用隔离森林评估样本的新颖性
        返回: 越高表示越不可能是异常点
        """
        iso = IsolationForest(contamination='auto', random_state=42)
        # 转换隔离森林的预测(-1为异常,1为正常)为分数
        scores = (iso.fit_predict(X) + 1) / 2  # 转换到[0,1]区间
        return self._normalize_scores(scores)

    def _get_cohesion_score(self, X: np.ndarray) -> np.ndarray:
        """
        计算样本的局部凝聚度(与邻近点的紧密程度)
        返回: 越高表示样本与周围点越紧密
        """
        distances = pairwise_distances(X)
        k = min(20, len(X) - 1)  # 取最近的k个邻居
        # 对每个样本,计算其与最近k个邻居的平均距离
        knn_distances = np.partition(distances, k, axis=1)[:, :k].mean(axis=1)
        # 距离越小表示凝聚度越高,需要转换
        return 1 - self._normalize_scores(knn_distances)

    def _get_learning_easiness_score(self, depths: np.ndarray) -> np.ndarray:
        """
        基于树深度计算学习难易度
        返回: 越高表示越容易学习(树深度越浅)
        """
        max_depth = depths.max() + 1e-8  # 避免除零
        return 1 - (depths / max_depth)

    def get_sample_weights(
            self,
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series]
    ) -> pd.DataFrame:
        """计算每个样本的详细评分和建议权重"""
        is_df = isinstance(X, pd.DataFrame)
        original_index = X.index if is_df else None

        X_values = X.values if is_df else X
        y_values = y.values if isinstance(y, pd.Series) else y
        n_samples = len(X_values)

        # 收集所有评分
        stability_scores = np.zeros(n_samples)  # 纵深稳定性
        model_consistency = np.zeros(n_samples)  # 横向稳定性
        learning_difficulties = np.zeros(n_samples)  # 学习难度

        # 计算基础特征空间指标
        density_scores = self._get_density_score(X_values)  # 局部密度
        novelty_scores = self._get_novelty_score(X_values)  # 非异常性
        cohesion_scores = self._get_cohesion_score(X_values)  # 局部凝聚度

        # 使用交叉验证收集模型相关指标
        kf = KFold(n_splits=self.n_splits, shuffle=True, random_state=42)

        for _, val_idx in kf.split(X_values):
            # 获取横向稳定性
            fold_consistency = self._get_cross_model_consistency(
                X_values, y_values, val_idx
            )
            model_consistency[val_idx] = fold_consistency

            # 获取纵深稳定性
            model = self.xgb_class(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
            model.fit(X_values, y_values, verbose=False)

            predictions = []
            for tree_idx in range(model.n_estimators):
                if self.task_type == 'classification':
                    model.n_estimators = tree_idx + 1
                    pred = model.predict_proba(X_values[val_idx])[:, 1]
                else:
                    pred = model.predict(X_values[val_idx])
                predictions.append(pred)
            predictions = np.array(predictions)
            stability_scores[val_idx] = self._get_stability_score(predictions)

            # 获取学习难度
            depths = self._get_leaf_depths(model, X_values[val_idx])
            learning_difficulties[val_idx] = 1 - self._get_learning_easiness_score(depths)  # 转换为难度分数

        # 1. 计算预测稳定性评分
        stability_score = (
                stability_scores * 0.6 +  # 纵向预测稳定性(树数量维度)
                model_consistency * 0.4  # 横向预测稳定性(模型参数维度)
        )

        # 2. 计算样本代表性评分
        representativeness = (
                density_scores * 0.4 +  # 局部密度
                novelty_scores * 0.3 +  # 非异常性
                cohesion_scores * 0.3  # 局部凝聚度
        )

        # 3. 基于三个维度的组合评分
        # 3.1 正常样本评分（稳定且有代表性）
        normal_score = (stability_score + representativeness) / 2  # 改用平均而不是相乘
        normal_weight_adj = np.clip(normal_score, 0.95, 1.05)

        # 3.2 边缘案例评分（不稳定但高代表性，难度适中）
        margin_score = (
            ((1 - stability_score) * 0.4 +  # 不稳定性
             representativeness * 0.4 +  # 高代表性
             (1 - learning_difficulties) * 0.2)  # 适中难度
        )
        # 使用sigmoid函数使分数分布更均匀
        margin_score = 1 / (1 + np.exp(-5 * (margin_score - 0.5)))
        margin_weight_boost = np.clip(margin_score, 0, 1)

        # 3.3 噪声样本评分（不稳定且低代表性，难度大）
        noise_score = (
            ((1 - stability_score) * 0.4 +  # 不稳定性
             (1 - representativeness) * 0.4 +  # 低代表性
             learning_difficulties * 0.2)  # 高难度
        )
        # 同样使用sigmoid函数
        noise_score = 1 / (1 + np.exp(-5 * (noise_score - 0.5)))
        noise_weight_penalty = np.clip(noise_score, 0, 0.5)

        # 4. 样本类型判定（用于分析）
        sample_types = np.full(n_samples, 'normal', dtype=object)
        # 调整判定阈值
        margin_mask = (margin_score > 0.5) & (noise_score < 0.3)
        noise_mask = (noise_score > 0.5) & (margin_score < 0.3)
        sample_types[margin_mask] = 'margin'
        sample_types[noise_mask] = 'noise'

        # 5. 计算最终权重
        base_weights = np.ones(n_samples)

        # # 将所有调整因子集中到0为中心的空间
        # margin_adj = margin_weight_boost # - 0.5  # [-0.5, 0.5]范围
        # noise_adj = noise_weight_penalty # - 0.25  # [-0.25, 0.25]范围
        # # normal_adj = normal_weight_adj - 1.0  # [-0.2, 0.2]范围

        # 综合调整
        # weight_adj = margin_adj - noise_adj
        # sample_weights = base_weights * (1 + weight_adj)
        # 将boost和penalty统一到倍数变化
        margin_factor = 1 + margin_weight_boost  # [1, 2]范围
        noise_factor = 1 / (1 + noise_weight_penalty)  # [0.67, 1]范围

        sample_weights = (
                base_weights *
                normal_weight_adj *
                margin_factor *
                noise_factor
        )

        # 5. 权重范围限制
        sample_weights = np.clip(sample_weights, 0.5, 2.0)

        # 6. 确定样本类型（用于分析）
        sample_types = np.full(n_samples, 'normal', dtype=object)
        margin_mask = (margin_score > 0.6) & (noise_score < 0.3)
        noise_mask = (noise_score > 0.6) & (margin_score < 0.3)
        sample_types[margin_mask] = 'margin'
        sample_types[noise_mask] = 'noise'

        # 7. 创建结果DataFrame
        results = pd.DataFrame({
            'stability_score': stability_score,  # 综合稳定性
            'representativeness': representativeness,  # 样本代表性
            'learning_difficulty': learning_difficulties,  # 学习难度
            'normal_score': normal_score,  # 正常样本评分
            'margin_score': margin_score,  # 边缘案例评分
            'noise_score': noise_score,  # 噪声样本评分
            'sample_type': sample_types,  # 样本类型
            'suggested_weight': sample_weights,  # 建议权重
            # 原始评分
            'stability_vertical': stability_scores,  # 纵深稳定性
            'stability_horizontal': model_consistency,  # 横向稳定性
            'density': density_scores,  # 局部密度
            'novelty': novelty_scores,  # 非异常性
            'cohesion': cohesion_scores  # 局部凝聚度
        })

        if original_index is not None:
            results.index = original_index

        return results

    def select_quality_data(
            self,
            X: Union[np.ndarray, pd.DataFrame],
            y: Union[np.ndarray, pd.Series],
            selection_config: Dict[str, float] = None
    ) -> Tuple[Union[np.ndarray, pd.DataFrame], Union[np.ndarray, pd.Series]]:
        """
        根据配置选择样本

        Parameters
        ----------
        selection_config : Dict[str, float]
            配置不同指标的权重，默认配置为：
            {
                'stability_weight': 0.4,         # 稳定性权重(纵向+横向)
                'representativeness_weight': 0.4, # 代表性权重(密度+新颖性+凝聚度)
                'difficulty_weight': 0.2,        # 学习难度权重
                'prefer_margin': False,          # 是否偏好边缘案例
                'prefer_stability': True,        # 是否偏好稳定样本
                'margin_boost': 0.3              # 边缘案例提升权重
            }
        """
        # 获取样本评分
        results = self.get_sample_weights(X, y)

        # 默认配置
        default_config = {
            'stability_weight': 0.4,
            'representativeness_weight': 0.4,
            'difficulty_weight': 0.2,
            'prefer_margin': False,
            'prefer_stability': True,
            'margin_boost': 0.3
        }

        # 更新配置
        config = default_config.copy()
        if selection_config:
            config.update(selection_config)

        # 计算选择分数
        if config['prefer_stability']:
            # 偏好稳定样本
            selection_scores = (
                    results['stability_score'] * config['stability_weight'] +
                    results['representativeness'] * config['representativeness_weight'] +
                    (1 - results['learning_difficulty']) * config['difficulty_weight']
            )
        else:
            # 偏好有代表性的不稳定样本
            selection_scores = (
                    (1 - results['stability_score']) * config['stability_weight'] +
                    results['representativeness'] * config['representativeness_weight'] +
                    (1 - results['learning_difficulty']) * config['difficulty_weight']
            )

        if config['prefer_margin']:
            # 如果偏好边缘案例，根据margin_score提升权重
            selection_scores = (
                    selection_scores * (1 - config['margin_boost']) +
                    results['margin_score'] * config['margin_boost']
            )

        # 选择样本
        n_samples = len(X)
        n_select = int(n_samples * self.final_keep_ratio)
        selected_indices = selection_scores.nlargest(n_select).index

        # 返回选中的样本
        if isinstance(X, pd.DataFrame):
            X_selected = X.loc[selected_indices]
        else:
            X_selected = X[selected_indices]

        if isinstance(y, pd.Series):
            y_selected = y.loc[selected_indices]
        else:
            y_selected = y[selected_indices]

        return X_selected, y_selected


def generate_test_data(n_samples=1000, noise_ratio=0.1, random_state=42):
    """
    生成测试数据，包含:
    - 正常样本
    - 噪声样本
    - 边界样本
    - 异常样本

    Returns:
        X: 特征矩阵
        y: 标签
    """
    # 生成基础数据集
    X_base, y_base = make_classification(
        n_samples=int(n_samples * (1 - noise_ratio)),
        n_features=10,
        n_informative=6,  # 真正有信息量的特征
        n_redundant=2,  # 冗余特征
        n_repeated=0,  # 重复特征
        n_clusters_per_class=3,
        class_sep=0.8,  # 类别间分离度
        random_state=random_state,
        weights=[0.4, 0.6]
    )

    # 生成噪声数据
    n_noise = int(n_samples * noise_ratio)
    X_noise = np.random.randn(n_noise, 10) * 1.5  # 较大的方差
    y_noise = np.random.randint(0, 2, n_noise)

    # 合并数据
    X = np.vstack([X_base, X_noise])
    y = np.hstack([y_base, y_noise])

    # 添加一些特征变换
    # 添加一个二次项特征
    X = np.column_stack([
        X,
        X[:, 0] ** 2,
        X[:, 1] ** 2,
        X[:, 0] * X[:, 1]
    ])

    return X, y


def visualize_data(X, y):
    """可视化数据分布"""
    plt.figure(figsize=(10, 5))

    # 选择前两个特征可视化
    plt.subplot(121)
    plt.scatter(X[:, 0], X[:, 1], c=y, cmap='bwr', alpha=0.6)
    plt.title('Feature 1 vs Feature 2')
    plt.xlabel('Feature 1')
    plt.ylabel('Feature 2')

    # 可视化特征分布
    plt.subplot(122)
    plt.boxplot([X[:, i] for i in range(5)])
    plt.title('Feature Distributions')
    plt.xlabel('Feature Index')
    plt.ylabel('Value')

    plt.tight_layout()
    plt.show()


def train_and_evaluate(X_train, y_train, X_val, y_val, model_name="", sample_weight=None):
    """训练模型并在验证集上评估"""
    model = xgboost.XGBClassifier(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=4,
        random_state=42
    )

    # 训练模型
    if sample_weight is not None:
        model.fit(X_train, y_train, sample_weight=sample_weight)
    else:
        model.fit(X_train, y_train)
    # 在验证集上评估
    y_pred = model.predict(X_val)
    y_pred_proba = model.predict_proba(X_val)[:, 1]

    # 计算评估指标
    metrics = {
        'accuracy': accuracy_score(y_val, y_pred),
        'precision': precision_score(y_val, y_pred),
        'recall': recall_score(y_val, y_pred),
        'f1': f1_score(y_val, y_pred),
        'auc': roc_auc_score(y_val, y_pred_proba)
    }

    print(f"\n{model_name} Performance on Validation Set:")
    for metric, value in metrics.items():
        print(f"{metric}: {value:.4f}")

    return model, metrics


def run_comparison_experiments(
        X, y, X_val, y_val,
        keep_ratios=[0.6, 0.7, 0.8, 0.9],
        noise_levels=[0.05, 0.1, 0.15, 0.2],
        n_trials=5
):
    """
    运行完整的对比实验

    Parameters:
    - keep_ratios: 数据保留比例列表
    - noise_levels: 生成数据时的噪声水平列表
    - n_trials: 每组参数重复实验次数
    """
    results = []

    # 存储最佳结果
    best_metrics = {
        'accuracy': 0,
        'params': None,
        'improvement': 0
    }

    for noise in noise_levels:
        for keep_ratio in keep_ratios:
            trial_metrics = []

            for trial in range(n_trials):
                # 重新生成带噪声的训练数据
                X_train, y_train = generate_test_data(n_samples=1000, noise_ratio=noise)

                # 使用当前参数的selector
                selector = DataQualitySelector(
                    task_type='classification',
                    final_keep_ratio=keep_ratio
                )

                # 获取筛选后的数据
                X_selected, y_selected = selector.select_quality_data(X_train, y_train)

                # 评估原始数据
                _, original_metrics = train_and_evaluate(
                    X_train, y_train, X_val, y_val,
                    model_name=f"Original (Noise={noise}, Keep={keep_ratio}, Trial={trial})"
                )

                # 评估筛选后的数据
                _, selected_metrics = train_and_evaluate(
                    X_selected, y_selected, X_val, y_val,
                    model_name=f"Selected (Noise={noise}, Keep={keep_ratio}, Trial={trial})"
                )

                # 计算改进幅度
                improvements = {
                    metric: selected_metrics[metric] - original_metrics[metric]
                    for metric in original_metrics.keys()
                }

                trial_metrics.append({
                    'noise': noise,
                    'keep_ratio': keep_ratio,
                    'trial': trial,
                    'original': original_metrics,
                    'selected': selected_metrics,
                    'improvements': improvements
                })

                # 更新最佳结果
                if selected_metrics['accuracy'] > best_metrics['accuracy']:
                    best_metrics['accuracy'] = selected_metrics['accuracy']
                    best_metrics['params'] = {
                        'noise': noise,
                        'keep_ratio': keep_ratio,
                        'trial': trial
                    }
                    best_metrics['improvement'] = improvements['accuracy']

            # 计算当前参数组合的平均指标
            avg_metrics = {
                'noise': noise,
                'keep_ratio': keep_ratio,
                'avg_improvement': np.mean([m['improvements']['accuracy'] for m in trial_metrics]),
                'std_improvement': np.std([m['improvements']['accuracy'] for m in trial_metrics]),
                'avg_accuracy': np.mean([m['selected']['accuracy'] for m in trial_metrics]),
                'std_accuracy': np.std([m['selected']['accuracy'] for m in trial_metrics])
            }
            results.append(avg_metrics)

    return results, best_metrics


def visualize_experiment_results(results):
    """可视化实验结果"""
    plt.figure(figsize=(15, 5))

    # 1. 不同噪声水平下的性能
    plt.subplot(131)
    noise_levels = sorted(set(r['noise'] for r in results))
    keep_ratios = sorted(set(r['keep_ratio'] for r in results))

    for keep_ratio in keep_ratios:
        noise_results = [r for r in results if r['keep_ratio'] == keep_ratio]
        noise_results = sorted(noise_results, key=lambda x: x['noise'])

        plt.errorbar(
            [r['noise'] for r in noise_results],
            [r['avg_accuracy'] for r in noise_results],
            yerr=[r['std_accuracy'] for r in noise_results],
            label=f'Keep={keep_ratio}'
        )

    plt.xlabel('Noise Level')
    plt.ylabel('Accuracy')
    plt.title('Performance vs Noise Level')
    plt.legend()

    # 2. 不同保留比例下的性能
    plt.subplot(132)
    for noise in noise_levels:
        keep_results = [r for r in results if r['noise'] == noise]
        keep_results = sorted(keep_results, key=lambda x: x['keep_ratio'])

        plt.errorbar(
            [r['keep_ratio'] for r in keep_results],
            [r['avg_accuracy'] for r in keep_results],
            yerr=[r['std_accuracy'] for r in keep_results],
            label=f'Noise={noise}'
        )

    plt.xlabel('Keep Ratio')
    plt.ylabel('Accuracy')
    plt.title('Performance vs Keep Ratio')
    plt.legend()

    # 3. 性能提升热力图
    plt.subplot(133)
    improvements = np.zeros((len(noise_levels), len(keep_ratios)))
    for i, noise in enumerate(noise_levels):
        for j, keep_ratio in enumerate(keep_ratios):
            result = [r for r in results if r['noise'] == noise and r['keep_ratio'] == keep_ratio][0]
            improvements[i, j] = result['avg_improvement']

    plt.imshow(improvements, cmap='RdYlGn', aspect='auto')
    plt.colorbar(label='Accuracy Improvement')
    plt.xticks(range(len(keep_ratios)), keep_ratios)
    plt.yticks(range(len(noise_levels)), noise_levels)
    plt.xlabel('Keep Ratio')
    plt.ylabel('Noise Level')
    plt.title('Performance Improvement Heatmap')

    plt.tight_layout()
    plt.show()


if __name__ == '__main__':
    ...
    # # <editor-fold desc="基于数据特质的数据加权/选择">
    #
    # # # 1. 生成训练数据和独立的验证数据
    # # noise_ratio = 0.1
    # # X, y = generate_test_data(n_samples=1000, noise_ratio=noise_ratio)
    # # # 生成验证集，使用不同的随机种子确保独立性
    # # X_val, y_val = generate_test_data(n_samples=300, noise_ratio=noise_ratio)
    # #
    # # # 2. 查看数据基本统计信息
    # # print("训练数据形状:", X.shape)
    # # print("验证数据形状:", X_val.shape)
    # # print("\n训练集类别分布:", np.bincount(y))
    # # print("验证集类别分布:", np.bincount(y_val))
    # # print("\n特征统计:")
    # # print("训练集均值:", X.mean(axis=0)[:5])
    # # print("训练集标准差:", X.std(axis=0)[:5])
    # # print("验证集均值:", X_val.mean(axis=0)[:5])
    # # print("验证集标准差:", X_val.std(axis=0)[:5])
    # # # 3. 可视化数据
    # # visualize_data(X, y)
    # # visualize_data(X_val, y_val)
    # #
    # # # 4. 初始化选择器并筛选数据
    # #
    # # selector = EnhancedDataQualitySelector(
    # #     task_type='classification',
    # #     final_keep_ratio=0.75
    # # )
    # #
    # # # X_selected, y_selected = selector.select_quality_data(X, y, config_margin)
    # # results = selector.get_sample_weights(X, y)
    # # suggested_weight = results['suggested_weight']
    # #
    # # # 5. 模型训练与评估
    # # ...
    # #
    # # # 6. 对比原始数据和筛选后数据的模型效果
    # # print("\n=== 模型性能对比 ===")
    # # original_model, original_metrics = train_and_evaluate(X, y, X_val, y_val, "Original Data")
    # # # selected_model, selected_metrics = train_and_evaluate(X_selected, y_selected, X_val, y_val, "Selected Data")
    # # weighted_model, weighted_metrics = train_and_evaluate(X, y, X_val, y_val, "Weighted Data",
    # #                                                       sample_weight=suggested_weight.values)
    # #
    # # # 7. 绘制性能对比图
    # # plt.figure(figsize=(10, 5))
    # # metrics_to_plot = ['accuracy', 'precision', 'recall', 'f1', 'auc']
    # # x = np.arange(len(metrics_to_plot))
    # # width = 0.35
    # #
    # # plt.bar(x - width / 2, [original_metrics[m] for m in metrics_to_plot], width, label='Original')
    # # plt.bar(x + width / 2, [weighted_metrics[m] for m in metrics_to_plot], width, label='Selected')
    # # plt.xticks(x, metrics_to_plot, rotation=45)
    # # plt.title('Model Performance Comparison on Validation Set')
    # # plt.legend()
    # # plt.tight_layout()
    # # plt.show()
    # #
    # # # 8. 输出改进情况
    # # print("\nImprovement after selection:")
    # # for metric in metrics_to_plot:
    # #     change = weighted_metrics[metric] - original_metrics[metric]
    # #     print(f"{metric}: {change:+.4f}")
    # #
    # # # 9. 运行完整的对比实验 -----------------------------------------------------------------------
    # # # 运行实验
    # # results, best_params = run_comparison_experiments(X, y, X_val, y_val,
    # #                                                   keep_ratios=[i / 100 for i in range(50, 100, 5)],
    # #                                                   noise_levels=[0.05, 0.1, 0.15, 0.2, 0.25, 0.3])
    # #
    # # # 打印最佳结果
    # # print("\nBest Performance:")
    # # print(f"Accuracy: {best_params['accuracy']:.4f}")
    # # print(f"Parameters: {best_params['params']}")
    # # print(f"Improvement: {best_params['improvement']:.4f}")
    # #
    # # # 可视化结果
    # # visualize_experiment_results(results)
    # # </editor-fold>
    #
    # # <editor-fold desc="基于元学习的样本加权">
    # # 10 基于元学习的样本加权 ----------------------------------------------------------------------------
    # # 10.1 生成原始数据/外部测试数据
    # res = []
    # for i in tqdm(range(5)):
    #     X_orig, y_orig = generate_test_data(n_samples=1000, noise_ratio=0.1, random_state=i * 2)
    #     X, X_valid, y, y_valid = train_test_split(
    #         X_orig, y_orig,
    #         test_size=0.25,
    #         random_state=42
    #     )
    #     # 10.2 训练集再划分，在内部测试集追求提升
    #     X_train, X_test, y_train, y_test = train_test_split(
    #         X, y,
    #         test_size=0.2,
    #         random_state=42
    #     )
    #     # 10.3 优化训练
    #     optimizer = MetaWeightOptimizer(
    #         task_type='classification',
    #         meta_iterations=15,
    #         meta_lr=0.2,
    #         n_weight_groups=10
    #     )
    #     weights = optimizer.get_suggested_weights(X_train, y_train, X_test, y_test, patience=3)
    #     # 10.4 在外部测试集验证效果
    #     unweighted_scores, weighted_scores = optimizer.apply_weighted_model(X_train, y_train, weights, X_valid, y_valid)
    #     print(weighted_scores - unweighted_scores)
    #     res.append(weighted_scores - unweighted_scores)
    # print(np.mean(res))
    # # </editor-fold>
    #
    # # <editor-fold desc="改良版元学习数据加权策略">
    # # 11 改良版元学习数据加权策略 ------------------------------------------------------------------------
    # res = []
    # for i in tqdm(range(5)):
    #     X_orig, y_orig = generate_test_data(n_samples=1000, noise_ratio=0.1, random_state=i * 2)
    #     X, X_valid, y, y_valid = train_test_split(
    #         X_orig, y_orig,
    #         test_size=0.25,
    #         random_state=42
    #     )
    #     # 10.3 优化训练
    #     optimizer = EnhancedMetaWeightOptimizer(
    #         task_type='classification',
    #         meta_iterations=2,
    #         meta_lr=0.5,
    #         n_weight_groups=2,
    #         n_splits=30
    #     )
    #     weights = optimizer.get_suggested_weights(X, y)
    #     # 10.4 在外部测试集验证效果
    #     unweighted_scores, weighted_scores = optimizer.apply_weighted_model(X, y, weights, X_valid, y_valid)
    #     print(weighted_scores - unweighted_scores)
    #     res.append(weighted_scores - unweighted_scores)
    # print(np.mean(res))
    # # </editor-fold>

    # <editor-fold desc="Hessian优化数据加权策略">
    # 12 Hessian优化数据加权策略 ------------------------------------------------------------------------
    res = []
    for i in tqdm(range(10)):
        X_orig, y_orig = generate_test_data(n_samples=1000, noise_ratio=0.1, random_state=i * 2)
        X, X_valid, y, y_valid = train_test_split(
            X_orig, y_orig,
            test_size=0.25,
            random_state=42
        )
        # 10.3 优化训练
        optimizer = ImprovedHessianOptimizer(n_splits=30)
        weights = optimizer.get_suggested_weights(X, y,scale = 0.02)
        # 10.4 在外部测试集验证效果
        unweighted_scores, weighted_scores = optimizer.apply_weighted_model(X, y, weights, X_valid, y_valid)
        print(f"improve:{weighted_scores - unweighted_scores:.4f}")
        res.append(weighted_scores - unweighted_scores)
    print(np.mean(res))

    # </editor-fold>

