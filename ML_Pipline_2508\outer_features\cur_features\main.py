"""
@ Author: <PERSON>
@ E-mail: <EMAIL>
@ Date: 2025/8/6 9:31
"""

from typing import Dict, Optional, List
from function.new_time_locs import *
from function.source_recon import *
import time


class MCGAnalyzer:
    """心磁图数据分析器"""
    def __init__(self):
        # 设置心脏模型路径
        self.lcav_path = os.path.join('model', 'heart_point_lcav_mesh.msh')
        self.heart_path = os.path.join('model', 'heart_point.msh')

        # 检查模型文件是否存在
        if not os.path.exists(self.lcav_path):
            raise FileNotFoundError(f"心脏模型文件不存在: {self.lcav_path}")
        if not os.path.exists(self.heart_path):
            raise FileNotFoundError(f"心脏模型文件不存在: {self.heart_path}")

    def read_mcg(self, file_path: str) -> np.ndarray:
        """读取心磁图数据"""
        with open(file_path, 'r') as ff:
            file = ff.read()
        da = []
        for i in file.split():
            da.append(float(i))
        datat = []
        for i in range(int(len(da) / 37)):
            datat.append(da[37 * i + 1:37 * (i + 1)])
        return np.array(datat)

    def analyze_file(self, mcg_data, Qp=None, Rp=None, Sp=None, To=None, Tp=None, Te=None) -> Dict:
        """
        生成电流源参数
        Args:
            mcg_data: 心磁图数据（可以是文件路径字符串或numpy数组）
            Qp, Rp, Sp, To, Tp, Te: 时刻点参数
        Returns:
            Dict: 包含80个电流源参数的结果字典
        """
        if isinstance(mcg_data, str):
            data = self.read_mcg(mcg_data)
            base_name = os.path.splitext(os.path.basename(mcg_data))[0]
        else:
            # 如果是numpy数组
            data = mcg_data
            base_name = "mcg_data"

        # 时刻点
        if all(tp is not None for tp in [Qp, Rp, Sp, To, Tp, Te]):
            time_loc = np.array([Qp, Rp, Sp, To, Tp, Te])
            print("使用输入的时刻点")
        else:
            if isinstance(mcg_data, str):
                Ph, Pp, Qp_calc, Rp_calc, Sp_calc, Tp_calc, Sr, To_calc, Te_calc = main_calculationPQRS(mcg_data)
                print("使用计算的时刻点")
            else:
                raise ValueError("对于numpy数组输入，必须提供所有时刻点参数")
            time_loc = np.array([Qp_calc, Rp_calc, Sp_calc, To_calc, Tp_calc, Te_calc])

        # 源重建计算
        reconstruction = SourceRecon(self.heart_path, self.lcav_path, time_loc, data, base_name)
        reconstruction.get_mesh()
        reconstruction.cta_mcg()
        reconstruction.get_lf()
        result_1 = reconstruction.source_music_qr()
        result_2 = reconstruction.source_music_tt()
        result_1 = [x[0] if isinstance(x, (list, np.ndarray)) and len(x) == 1 else x for x in result_1]
        result_2 = [x[0] if isinstance(x, (list, np.ndarray)) and len(x) == 1 else x for x in result_2]
        result = [base_name] + result_1 + result_2

        columns = [
            "Name", "J_q_rotate_ang", 'q_angle', "J_q_move", "J_r_rotate_ang",
            "J_r_max_angle", "J_s_angle", "J_s_movement", 'J_q_r_loc', 'J_r_move',
            'J_r_index', "J_r_angle", "J_front_q_rate", "J_front_r_rate", "J_front_s_rate",
            "J_long_q_rate", "J_long_r_rate", "J_long_s_rate",
            "J_up_q_rate", "J_up_r_rate", "J_up_s_rate",
            "J_front_q_r", "J_front_r_s", "J_front_q_s",
            "J_long_q_r", "J_long_r_s", "J_long_q_s",
            "J_up_q_r", "J_up_r_s", "J_up_q_s",
            "x_Q_x_R", "x_R_x_S", "x_Q_x_S", "y_Q_y_R", "y_R_y_S", "y_Q_y_S",
            "z_Q_z_R", "z_R_z_S", "z_Q_z_S", "x_S", "y_S", "z_S",
            "x_Q_x_R_rotation", "x_R_x_S_rotation", "x_Q_x_S_rotation",
            "y_Q_y_R_rotation", "y_R_y_S_rotation", "y_Q_y_S_rotation",
            "z_Q_z_R_rotation", "z_R_z_S_rotation", "z_Q_z_S_rotation",
            "x_S_rotation", "y_S_rotation", "z_S_rotation",
            "Q_strength", "R_strength", "S_strength",
            "Q_x_direct", "Q_y_direct", "R_x_direct", "R_y_direct", "S_x_direct",
            "S_y_direct", "overall_std_dev_QR", "TT最大和最小点源强度比值",
            "TT段最大夹角", 'TT段最大夹角_1', "TT总电流向量", "TT方差电流向量", "平坦度因子",
            "RT最大点源强度比", "RT夹角",
            "TT段离散度_3d", 'TT段离散度_3d_1', "TT段离散度_2d", 'TT段离散度_2d_1',
            "所处象限", '所处象限_1', "旋转方向", '单偶极子误差', 'Tp段位置'
        ]

        result_dict = {}
        for i, col in enumerate(columns):
            if i < len(result):
                val = result[i]
                if isinstance(val, (list, np.ndarray)):
                    if len(val) == 1:
                        result_dict[col] = val[0]
                    else:
                        result_dict[col] = val
                else:
                    result_dict[col] = val
            else:
                result_dict[col] = None

        return result_dict

    def save_results(self, result_dict: Dict, output_path: str):
        """保存结果到Excel"""
        df = pd.DataFrame([result_dict])
        df.to_excel(output_path, index=False)


if __name__ == "__main__":
    # 1: 如果读txt文件
    start_time = time.time()
    Qp, Rp, Sp, To, Tp, Te = 336, 359, 375, 553, 613, 662  # 假设时刻点，非该数据真实时刻点
    analyzer = MCGAnalyzer()
    result_dict = analyzer.analyze_file('data/AHYK_2023_000037.txt', Qp=Qp, Rp=Rp, Sp=Sp, To=To, Tp=Tp, Te=Te)
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"代码运行时间：{execution_time:.3f} 秒")
    analyzer.save_results(result_dict, 'mcg_results.xlsx')

    # # 2. 如果读numpy数组
    # mcg_data_array = np.load('AHYK_2023_000037.npy')
    # start_time = time.time()
    # Qp, Rp, Sp, To, Te, Tp = 212, 234, 256, 279, 303, 355  # 假设时刻点，非该数据真实时刻点
    # analyzer = MCGAnalyzer()
    # result_dict = analyzer.analyze_file(mcg_data_array, Qp=Qp, Rp=Rp, Sp=Sp, To=To, Te=Te, Tp=Tp)
    # end_time = time.time()
    # execution_time = end_time - start_time
    # print(f"代码运行时间：{execution_time:.3f} 秒")
    # analyzer.save_results(result_dict, 'mcg_results.xlsx')