
### 概述
1. 文件目录说明：
   1. main.py：主功能函数，包含1个全部算法接口的类：QRS_rotate
   2. utils文件夹：算法背后的全部功能函数，是一个package，实现具体功能的所有代码都存放在里面；
   3. mcg文件夹：存放心磁文件

2. 运行环境:
   conda install -y numpy=1.26.4 scipy=1.10.0 pandas=2.2.3 scikit-learn=1.6.0 scikit-image=0.19.3 matplotlib=3.7.0 shapely=2.0.7 pillow=9.4.0 openpyxl=3.1.5 joblib=1.1.1

3. 输入：100*100*N 的插值后心磁数据
      36通道原始心磁数据（用于计算波形形态/包络线）
      13个时刻点
输出：QRS_WaveStatus, QR_Rotation, QR_Reason, RS_Rotation, RS_Reason
        例：输入： AHYK_2023_000046.pkl   和  AHYK_2023_000046.txt
                  时刻点：ts = [148, 178, 208, 314, 326, 346, 366, 400, 475, 475, 558, 620, 665]
           输出：  三峰分明 正常 QR段转动正常 正常 RS段转动正常

4. 描述：1. QR转动正异常判断：当由“QR折返转动、前后帧跳转、R时刻方向异常、临近R时刻才转到位”这几种情况组合时，QR转动是正常的  

5. 代码运行时间：0.8544 秒





