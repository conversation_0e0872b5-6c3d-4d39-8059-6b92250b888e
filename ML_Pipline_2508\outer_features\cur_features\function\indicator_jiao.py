'''
@Project ：pythonProject2025 
@File    ：indicator_jiao.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/1/2 13:26 
'''
import math
import numpy as np
from numpy.linalg import *
from mayavi import mlab
from sklearn.decomposition import PCA

def data_process(result):
    """
    :param result: 单点源计算的结果
    :return: 根据单点源计算的结果理清顺序，便于及指标计算
    """
    time = []
    for i in range(len(result)):
        time.append(result[i][0])
        max_index, q_dir, q_intensity, q_locate = result[i][1], result[i][2], result[i][3], result[i][4]
        if i == 0:
            if q_dir.shape[0] == 1:
                max_index_1 = max_index
                cur_dir_1 = q_dir
                inten_1 = q_intensity[0]
                point_1 = q_locate
        else:
            if q_dir.shape[0] == 1:
                max_index_1 = np.vstack((max_index_1, max_index))
                cur_dir_1 = np.vstack((cur_dir_1, q_dir))
                inten_1 = np.vstack((inten_1, q_intensity[0]))
                point_1 = np.vstack((point_1, q_locate))
    # inten_1 = inten_1 / np.max(np.abs(inten_1))
    return time, max_index_1, cur_dir_1, inten_1, point_1


def get_ind_q_locate(time, cur_dir_1, inten_1, point_1, Q, file_name, lcav_points):
    """
    :param save_path:
    :param result_path:
    :return: 读取excel表格并根据最新的Q时刻下降算法进行计算，取到Q + 15
    """
    q_locate, index_name = [], ['name']
    cur_dir_1 = cur_dir_1 * inten_1
    l_cav_high, l_cav_low = np.max(lcav_points[:, 1]), np.min(lcav_points[:, 1])
    start_index = [index for index, item in enumerate(time) if item == Q]  # 初始时间为Q
    end_index = [start_index[0] + 15]  # 终止时刻为Q + 15

    x, y = cur_dir_1[start_index[0]][0], cur_dir_1[start_index[0]][1]
    angle_1 = math.degrees(np.arctan2(y, x))  # 角度从-180 —— +180
    if angle_1 < 0:
        q_angle = angle_1 + 360  # 角度从 0--360
    else:
        q_angle = angle_1
    for i in np.arange(start_index[0], end_index[0] + 1, 1):  # 实则为(Q, Q+15), 点源位置相对于心脏的比例
        x_dir = cur_dir_1[i][0]
        if x_dir > 0:  # x_dir为了确定是否是Q时刻, 当电流源指向左边则说明是除极的开始
            continue
        y = point_1[i][1]
        y_rate = (y - l_cav_low) / (l_cav_high - l_cav_low)
        q_locate.append(y_rate)  # 记录每一秒点源所占的心脏位置比例
        index_name.append(time[0] + i)
    if len(q_locate) == 0 or len(q_locate) == 1:  # 没有时刻或只有一个时刻电流源方向是朝着心脏右下，因此几乎没有Q时刻
        q_locate = [file_name, q_angle, 0, 0, 0]
        index_name = index_name + ['q_angle', 'high_point', 'low_point', 'distance']
        result = [index_name, q_locate]
        return q_angle, 0
    q_locate = np.array(q_locate)
    # 计算点源下降的最大比例位置
    q_loc_diff = np.diff(q_locate)  # 该组元素个数相比q_locate少1, 因为时刻取了16个时刻，所以diff为15个
    flag, val, index = 0, 0, np.argmax(q_locate)  # index为(Q, Q+15)电流源处在位置最高的点, 以此来确保接下来是向下的运动
    while flag == 0:
        if index == 0:  # 最高点出现在第一个位置
            break
        if index == q_locate.shape[0] - 1:  # or np.isnan(q_locate[index + 1]):
            # 如果是最后一个元素最大, 则说明是Q时刻后期在单调向上因此舍弃, 不是我们要的初始时刻的最高点再向下运动
            q_locate[index] = np.nan
            index = np.nanargmax(q_locate)
            continue
        if q_loc_diff[index - 1] >= 0 > q_loc_diff[index]:  # 证明该点为最高点，且之后是下降的
            flag = 1
        else:
            q_locate[index] = np.nan
            index = np.nanargmax(q_locate)

    val = np.zeros(q_loc_diff.shape[0] - index)
    for j in np.arange(index, q_loc_diff.shape[0], 1):  # 取每个时间的累计值，为了取到下降最多的值
        if j == index:
            val[0] = q_loc_diff[j]
        else:
            val[j - index] = val[j - index - 1] + q_loc_diff[j]
    q_loc = np.min(val)
    # start_index = index
    # ter_index = np.argmin(val) + index + 1
    # q_locate = q_locate.tolist()
    # q_locate = [file_name] + [q_angle] + q_locate + [start_index, ter_index, loc] #将q时刻的角度也包含进去
    # index_name = index_name + ['q_angle', 'high_point', 'low_point', 'distance']
    # result = [index_name, q_locate]
    return q_angle, q_loc


def get_ind_q_angle(time, cur_dir_1, inten_1, Q, file_name):
    """
    :param save_path:
    :param result_path:
    :return: 主要给出在Q-Q+10时刻的转动角度，若为负的则为顺时针, 且可能是心尖存在问题
    """
    angle = []
    index_name = ['name']
    cur_dir_1 = cur_dir_1 * inten_1
    start_index = [index for index, item in enumerate(time) if item == Q]  # 初始时间为Q
    end_index = [start_index[0] + 10]  # 终止时刻为Q+10
    for i in np.arange(start_index[0], end_index[0], 1):  # 实则为Q到Q+9， 10秒
        x, y = cur_dir_1[i][0], cur_dir_1[i][1]
        angle_1 = math.degrees(np.arctan2(y, x))  # 角度从-180 —— +180
        angle.append(angle_1)
        index_name.append(time[0] + i)
    angle_1 = np.array(angle)
    angle_1 = np.diff(angle_1)  # 后一角度和前一角度的转角

    for i in range(angle_1.shape[0]):
        if angle_1[i] > 180:   # 后一角度减去前一角度大于180
            angle_1[i] = angle_1[i] - 360
        if angle_1[i] < -180:  # 后一角度减去前一角度小于-180
            angle_1[i] = angle_1[i] + 360
    sum_angle = np.sum(angle_1)
    # angle_1 = angle_1.tolist()
    # angle_1 = [file_name] + angle_1
    # angle_1.append(sum_angle)
    # result = [index_name, angle_1]
    return sum_angle


def get_ind_r_angle(time, cur_dir_1, inten_1, R, file_name):
    """
    :param save_path:
    :param result_path:
    :return: 读取excel表格并进行作图 ,主要在于给出R时刻电流源的转角，从(R-10~R+10)，转角不够可能是侧壁的问题
    [[index_name, 'lar_time', 'rotate_angle', 'lar_angle']
    [file_name, r_angle, start_index[0] + ter_index, loc, r_angle[0] + loc]]
    """
    cur_dir_1 = cur_dir_1 * inten_1
    r_angle, index_name = [], ['name']
    start_index = [index for index, item in enumerate(time) if item == R - 10]  # 初始时刻为R - 10
    end_index = [start_index[0] + 20]  # 终止时刻为R + 10
    for i in np.arange(start_index[0], end_index[0], 1):
        x, y = cur_dir_1[i][0], cur_dir_1[i][1]
        angle_r = math.degrees(np.arctan2(y, x))  # 角度统一从-180-180度
        r_angle.append(angle_r)
        index_name.append(time[0] + i)
    r_angle = np.array(r_angle)
    r_angle_1 = np.diff(r_angle)
    val = np.zeros(r_angle_1.shape[0])
    for i_1 in range(r_angle_1.shape[0]):
        if i_1 == 0:
            val[0] = r_angle_1[0]
        elif r_angle_1[i_1] < -180:  # 如果后面一个角跨过一个周期, 则需要加上360度
            val[i_1] = val[i_1 - 1] + r_angle_1[i_1] + 360
        elif r_angle_1[i_1] > 180:  # 如果后面一个角跨过一个周期, 则需要减去360度, 但是该种情况在R时刻很少发生或基本不发生
            val[i_1] = val[i_1 - 1] + r_angle_1[i_1] - 360
        else:
            val[i_1] = val[i_1 - 1] + r_angle_1[i_1]
    loc = np.max(val)
    ter_index = np.argmax(val) + 1  # 因为是差值的最大值，是后项减前项，所有需要加1
    r_angle = (r_angle + 360).tolist()  # s.t.原本在三四象限的副角度变为正的，便于查看
    # r_angle = [file_name] + r_angle + [start_index[0] + ter_index, loc, r_angle[0] + loc]
    # index_name = index_name + ['lar_time', 'rotate_angle', 'lar_angle']
    # result = [index_name, r_angle]
    r_rotate_angle, r_max_angle = loc, r_angle[0] + loc
    return r_rotate_angle, r_max_angle


def get_ind_s_angle(time, cur_dir_1, inten_1, point_1, S, file_name, lcav_points):
    """
    :param save_path:
    :param result_path:
    :return: 读取excel表格并进行作图 ,主要在于给出R时刻电流源的转角，转角不够可能是侧壁的问题
    [[index_name, 's_angle', 'y_rate'],
    [file_name, s_angle, y_rate]]
    """
    cur_dir_1 = cur_dir_1 * inten_1
    s_indicat, index_name = [file_name], ['name']
    time_index = [index for index, item in enumerate(time) if item == S]  # 初始时刻为R - 10
    x, y = cur_dir_1[time_index[0]][0], cur_dir_1[time_index[0]][1]
    s_angle = math.degrees(np.arctan2(y, x))  # 角度统一从-180-180度
    if s_angle < 0:
        s_angle = s_angle + 360  # 统一到0-360度
    l_cav_high, l_cav_low = np.max(lcav_points[:, 1]), np.min(lcav_points[:, 1])
    y = point_1[time_index[0]][1]
    y_rate = (y - l_cav_low) / (l_cav_high - l_cav_low)
    index_name = index_name + ['s_angle', 'y_rate']
    s_indicat = s_indicat + [s_angle, y_rate]
    result = [index_name, s_indicat]
    return s_angle, y_rate


def get_distance(normal, ori_point, point):
    """
    :param normal: 平面的法向量
    :param ori_point: 平面的原点，用于构建平面
    :param point: 需要求到平面距离的点集，可以是一个点，可以是点集
    :return: 点集分别到平面距离
    """
    distance = np.dot((point - ori_point), normal)
    return distance

def get_ind_r_locate(time, point_1, Q, R, lcav_points):
    time_index_q = [index for index, item in enumerate(time) if item == Q]  # Q
    time_index_r = [index for index, item in enumerate(time) if item == R]  # R
    l_cav_high, l_cav_low = np.max(lcav_points[:, 1]), np.min(lcav_points[:, 1])
    y_q, y_r = point_1[time_index_q[0]][1], point_1[time_index_r[0]][1]
    q_r_index = (y_q - y_r) / (l_cav_high - l_cav_low)
    y_r_rate = (y_r - l_cav_low) / (l_cav_high - l_cav_low)
    return q_r_index, y_r_rate

def get_q_r_s_loc(time, cur_dir_1, inten_1, point_1, Q, R, S, file_name, lcav_points):
    """

    :param time:
    :param cur_dir_1:
    :param inten_1:
    :param point_1:
    :param Q:
    :param R:
    :param S:
    :param file_name:
    :param lcav_points:
    :return:
    index_name + ['angle_r', 'front_q', 'front_r', 'front_s', 'long_q', 'long_r', 'long_s',
                               'up_q', 'up_r', 'up_s']
    """

    cur_dir_1 = cur_dir_1 * inten_1
    indicator, index_name = [file_name], ['name']
    time_ind_q = [index for index, item in enumerate(time) if item == Q]  # Q时刻
    time_ind_r = [index for index, item in enumerate(time) if item == R]  # R时刻
    time_ind_s = [index for index, item in enumerate(time) if item == S]  # S时刻
    point_q, point_r, point_s = point_1[time_ind_q[0]], point_1[time_ind_r[0]], point_1[time_ind_s[0]]

    x, y = cur_dir_1[time_ind_r[0]][0], cur_dir_1[time_ind_r[0]][1]
    angle_r = math.degrees(np.arctan2(y, x))  # 角度统一从-180-180度
    if angle_r < 0:
        angle_r = angle_r + 360  # 统一到0-360度

    data = lcav_points
    pca = PCA(n_components=3)
    pca.fit(data)
    left_long_axis, left_mid_axis, left_short_axis \
        = pca.components_[0], pca.components_[1], pca.components_[2]  # 左心室的长轴
    # print(pca.components_)
    left_mid_point = np.array([(np.max(lcav_points[:, 0]) + np.min(lcav_points[:, 0])) / 2,
                            (np.max(lcav_points[:, 1]) + np.min(lcav_points[:, 1])) / 2,
                            (np.max(lcav_points[:, 2]) + np.min(lcav_points[:, 2])) / 2])  # 计算左心室中心用来构建切平面
    # 心脏前后轴指标分析
    front_index = np.argmin(np.abs(pca.components_[1:3, 1]))  # 去除长轴完以后的y变动最小的轴为前后轴
    left_front_axis = pca.components_[1 + front_index]  # 左心室的前后轴
    if left_front_axis[2] > 0:  # 左心室前后轴的方向指向侧壁
        left_front_axis = -left_front_axis
    front_dis = get_distance(left_front_axis, left_mid_point, lcav_points)
    front_index = np.argmax(front_dis)
    front_dis_1 = np.max(front_dis) - np.min(front_dis)  # 前后轴方向左心室最大距离差
    point_front_dis_q = get_distance(left_front_axis, left_mid_point, point_q)
    point_front_dis_r = get_distance(left_front_axis, left_mid_point, point_r)
    point_front_dis_s = get_distance(left_front_axis, left_mid_point, point_s)
    front_dis_rate_q = (np.max(front_dis) - point_front_dis_q) / front_dis_1
    front_dis_rate_r = (np.max(front_dis) - point_front_dis_r) / front_dis_1
    front_dis_rate_s = (np.max(front_dis) - point_front_dis_s) / front_dis_1
    # draw_relative_pic(lcav_points, left_mid_point, left_front_axis, point_q, point_r, point_s, lcav_tri, front_index)
    # print('rate q is %.2f, rate r is %.2f, rate s is %.2f' % (front_dis_rate_q, front_dis_rate_r, front_dis_rate_s))

    # 心脏长轴作为法向量作图
    if left_long_axis[0] < 0:  # 左心室长轴的方向指向心尖
        left_long_axis = - left_long_axis
    long_dis = get_distance(left_long_axis, left_mid_point, lcav_points)
    long_index = np.argmax(long_dis)
    long_dis_1 = np.max(long_dis) - np.min(long_dis)
    point_long_dis_q = get_distance(left_long_axis, left_mid_point, point_q)
    point_long_dis_r = get_distance(left_long_axis, left_mid_point, point_r)
    point_long_dis_s = get_distance(left_long_axis, left_mid_point, point_s)
    long_dis_rate_q = (np.max(long_dis) - point_long_dis_q) / long_dis_1
    long_dis_rate_r = (np.max(long_dis) - point_long_dis_r) / long_dis_1
    long_dis_rate_s = (np.max(long_dis) - point_long_dis_s) / long_dis_1
    # draw_relative_pic(lcav_points, left_mid_point, left_long_axis, point_q, point_r, point_s, lcav_tri, long_index)
    # print('rate q is %.2f, rate r is %.2f, rate s is %.2f' % (long_dis_rate_q, long_dis_rate_r, long_dis_rate_s))

    # 心脏上下作为法向量作图
    list_1 = [0, 1, 2]  # 主成分分析的三个维度的指标
    list_2 = [0, front_index]  # 长轴和前后轴的指标
    up_index = list(set(list_1) - set(list_2))[0]
    left_up_axis = pca.components_[up_index]  # 左心室的前后轴
    if left_up_axis[1] < 0:  # 左心室长轴的方向指向前壁也就是向上的方向
        left_up_axis = -left_up_axis
    up_dis = get_distance(left_up_axis, left_mid_point, lcav_points)
    up_index = np.argmax(up_dis)
    up_dis_1 = np.max(up_dis) - np.min(up_dis)
    point_up_dis_q = get_distance(left_up_axis, left_mid_point, point_q)
    point_up_dis_r = get_distance(left_up_axis, left_mid_point, point_r)
    point_up_dis_s = get_distance(left_up_axis, left_mid_point, point_s)
    up_dis_rate_q = (np.max(up_dis) - point_up_dis_q) / up_dis_1
    up_dis_rate_r = (np.max(up_dis) - point_up_dis_r) / up_dis_1
    up_dis_rate_s = (np.max(up_dis) - point_up_dis_s) / up_dis_1
    # draw_relative_pic(lcav_points, left_mid_point, left_up_axis, point_q, point_r, point_s, lcav_tri, up_index)
    # print('rate q is %.2f, rate r is %.2f, rate s is %.2f' % (up_dis_rate_q, up_dis_rate_r, up_dis_rate_s))

    index_name = index_name + ['angle_r', 'front_q', 'front_r', 'front_s', 'long_q', 'long_r', 'long_s',
                               'up_q', 'up_r', 'up_s']
    indicator = indicator + [angle_r,  front_dis_rate_q,  front_dis_rate_r,  front_dis_rate_s,
                             long_dis_rate_q, long_dis_rate_r, long_dis_rate_s,
                             up_dis_rate_q, up_dis_rate_r, up_dis_rate_s]
    result = [index_name, indicator]
    return result