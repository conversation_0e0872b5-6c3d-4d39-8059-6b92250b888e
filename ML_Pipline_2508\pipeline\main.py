"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: main_v2
date: 2024/12/9 上午9:41
desc:
    文档到文档结果的全流程处理， 也可以用作生成新特征的工具。(跑一次即可获得文档中心磁文件的特征，然后用于后续的模型训练)
"""
import argparse
import logging
import os
import pickle
import sys
from pathlib import Path

import numpy as np
import pandas as pd
from tqdm import tqdm

from pipeline.pipline import suppress_print, predict_integrated


def configure_logging():
    """禁用所有日志输出"""
    pass
    os.environ['NUMBA_WARNINGS'] = '0'
    os.environ['NUMBA_DEBUG'] = '0'
    # 方法1：使用 logging.disable 完全禁用所有日志
    logging.disable(logging.CRITICAL)

    # 方法2：将所有已知的logger设置为最高级别
    for name in logging.root.manager.loggerDict:
        if "numba" in name:  # 特别针对numba的日志
            logging.getLogger(name).setLevel(logging.CRITICAL)


configure_logging()


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='预测模型参数化调用脚本')

    # 必需参数
    parser.add_argument('--selected_features_path', required=True, help='特征选择文件路径')
    parser.add_argument('--model_path', required=True, help='模型文件路径')

    # 可选的临床参数
    parser.add_argument('--clinic_gender',
                        type=int,
                        choices=[0, 1],
                        default=None,
                        help='性别 (男:1, 女:0)')

    parser.add_argument('--clinic_age',
                        type=int,
                        default=None,
                        help='年龄')

    parser.add_argument('--clinic_hospital',
                        type=int,
                        choices=[0, 1, 2, 3, 4, 5, 7],
                        default=None,
                        help='医院编码 (上海中山:0, 上海六院:1, 上海十院:2, 北京301:3, 安医:4, 广东南海:5, 其它:7)')

    parser.add_argument('--clinic_diabetes',
                        type=int,
                        choices=[0, 1],
                        default=None,
                        help='糖尿病情况 (0或1)')

    parser.add_argument('--clinic_symp',
                        type=int,
                        choices=[0, 1],
                        default=None,
                        help='临床症状情况 (0或1)')

    parser.add_argument('--txt_path',
                        help='txt数据文件路径（可选）')

    return parser.parse_args()


def load_txt_data(txt_path):
    """从txt文件加载数据
    如果需要特殊的数据加载逻辑，可以在这里实现
    """
    try:
        # 这里需要根据实际的txt文件格式来实现具体的加载逻辑
        # 这里假设txt文件可以直接用numpy加载，实际使用时请根据实际情况修改
        # data = np.loadtxt(txt_path)[:, 1:]
        data_frame = pd.read_csv(txt_path, sep='\t', header=None).iloc[:, :] # 对于某些数据  似乎不需要去掉第一列。。。？
        # data = pd.DataFrame(data)
        return data_frame
    except Exception as e:
        print(f"Error loading txt file: {str(e)}")
        sys.exit(1)


def main():
    # 配置日志级别
    configure_logging()

    # 解析命令行参数
    args = parse_arguments()

    # 获取脚本的绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 构建完整的文件路径
    selected_features_path = os.path.join(script_dir, args.selected_features_path)
    model_path = os.path.join(script_dir, args.model_path)

    # 构建临床数据字典，只包含非None值
    clinic_data = {k: v for k, v in {
        'clinic_gender': args.clinic_gender,
        'clinic_age': args.clinic_age,
        'clinic_hospital': args.clinic_hospital,
        'clinic_diabetes': args.clinic_diabetes,
        'clinic_symp': args.clinic_symp
    }.items() if v is not None}

    # 准备数据
    if args.txt_path:
        # 如果提供了txt路径，从文件加载数据
        txt_path = os.path.join(script_dir, args.txt_path)
        raw_data = load_txt_data(txt_path)

        file_label_tuple = (txt_path, 0)
    else:
        # 否则使用随机生成的示例数据
        raw_data = np.random.randint(0, 100, size=(1009, 36))
        file_label_tuple = ('to_data', 0)

    # 执行预测
    with suppress_print(enable_suppress=True, capture_output=True) as captured_output:
        results = predict_integrated(
            data=raw_data,
            clinic_data=clinic_data,
            model_path=model_path,
            selected_features_path=selected_features_path,
            file_label_tuple=file_label_tuple,
            fold=0
        )

    # 输出结果
    print(results.to_numpy()[0][1:])
    # print("log:", captured_output.getvalue() if captured_output else None)
    # captured_output.close()


def process_files(txt_folder, excel_path, model_path, selected_features_path, fold=4):
    """批量处理文件并生成结果"""
    # 读取Excel文件
    df_info = pd.read_excel(excel_path,sheet_name='测试用')

    # 确保'心磁号'列存在
    if '心磁号' not in df_info.columns:
        raise ValueError("Excel文件中必须包含'心磁号'列")

    # 获取所有需要处理的心磁号
    mcg_ids = df_info['心磁号'].astype(str).tolist()

    # 准备结果列表
    results = []

    # 遍历Excel中的心磁号
    for mcg_id in tqdm(mcg_ids[:], desc="处理文件"):
        # 构建对应的txt文件路径
        txt_file = Path(txt_folder) / f"{mcg_id}.txt"

        # 检查文件是否存在
        if not txt_file.exists():
            logging.warning(f"未找到对应的txt文件：{txt_file}")
            continue
        # debug 定位测试-----------------------
        # if mcg_id!='SHLY_2024_000960':
        #     continue
        # 获取对应的临床信息
        info_row = df_info[df_info['心磁号'].astype(str) == mcg_id]

        # 构建临床数据字典
        # clinic_data_old_version = {
        #     'clinic_gender': info_row['性别'].iloc[0] if '性别' in df_info.columns else None,
        #     'clinic_age': info_row['年龄'].iloc[0] if '年龄' in df_info.columns else None,
        #     'clinic_hospital': info_row['医院'].iloc[0] if '医院' in df_info.columns else None,
        #     'clinic_diabetes': info_row['糖尿病'].iloc[0] if '糖尿病' in df_info.columns else None,
        #     'clinic_symp': info_row['典型症状'].iloc[0] if '典型症状' in df_info.columns else None
        # }
        clinic_data = {
            'clinic_height': info_row['临床特征-身高'].iloc[0] if '临床特征-身高' in df_info.columns else None,
            'clinic_weight': info_row['临床特征-体重'].iloc[0] if '临床特征-体重' in df_info.columns else None,
            'clinic_gender': info_row['临床特征-性别'].iloc[0] if '临床特征-性别' in df_info.columns else None,
            'clinic_age': info_row['临床特征-年龄'].iloc[0] if '临床特征-年龄' in df_info.columns else None,
            'clinic_smoking': info_row['临床特征-吸烟'].iloc[0] if '临床特征-吸烟' in df_info.columns else None,
            'clinic_drinking': info_row['临床特征-饮酒'].iloc[0] if '临床特征-饮酒' in df_info.columns else None,
            'clinic_hypertension': info_row['临床特征-高血压'].iloc[0] if '临床特征-高血压' in df_info.columns else None,
            'clinic_hyperlipidemia': info_row['临床特征-高脂血症'].iloc[0] if '临床特征-高脂血症' in df_info.columns else None,
            'clinic_intervention': info_row['临床特征-既往介入'].iloc[0] if '临床特征-既往介入' in df_info.columns else None,
            'clinic_hospital': info_row['临床特征-所在医院'].iloc[0] if '临床特征-所在医院' in df_info.columns else None,
            'clinic_diabetes': info_row['临床特征-糖尿病'].iloc[0] if '临床特征-糖尿病' in df_info.columns else None,
            'clinic_symp': info_row['临床特征-典型症状'].iloc[0] if '临床特征-典型症状' in df_info.columns else None
        }
        # 清理临床数据字典，移除None值
        clinic_data = {k: v for k, v in clinic_data.items() if v is not None}

        # 加载txt数据
        raw_data = load_txt_data(txt_file)
        if raw_data is None:
            continue
        try:
            # 执行预测
            file_label_tuple = (Path(txt_file).stem, 0)
            with suppress_print(enable_suppress=False, capture_output=False) as captured_output:
                pred_results = predict_integrated(
                    data=raw_data,
                    clinic_data=clinic_data,
                    model_path=model_path,
                    selected_features_path=selected_features_path,
                    file_label_tuple=file_label_tuple,
                    fold=fold,
                    save_path='./files/saved_features/file_features_V250623/' + Path(txt_file).stem + '.pkl'
                )

            # 提取预测结果
            pred_values = pred_results.to_numpy()[0][1:]

            # 创建结果字典
            result_dict = {
                'mcg_file': mcg_id,
                'pred': pred_values[0],  # 第一个值是预测标签
                'prob': pred_values[1],  # 第二个值是概率
            }

            # 添加原始Excel中的其他字段
            for col in df_info.columns:
                if col != '心磁号':
                    result_dict[col] = info_row[col].iloc[0]

            results.append(result_dict)
        except Exception as e:
            print(f"处理{txt_file}文件时发生错误：{e}")
            print(f"处理{txt_file}文件出错")
            continue

    # 创建结果DataFrame
    df_results = pd.DataFrame(results)

    return df_results


def main_processfiles():
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 设置路径
    txt_folder = os.path.join(script_dir, 'data', 'txt_files')  # txt文件夹路径
    excel_path = os.path.join(script_dir, 'data', 'clinical_info.xlsx')  # 临床信息Excel路径
    model_path = os.path.join(script_dir, 'model.pkl')  # 模型路径
    selected_features_path = os.path.join(script_dir, 'selected_features.json')  # 特征选择文件路径
    output_path = os.path.join(script_dir, 'results.xlsx')  # 输出结果路径

    try:
        # 处理文件
        results_df = process_files(txt_folder, excel_path, model_path, selected_features_path)

        # 保存结果
        results_df.to_excel(output_path, index=False)
        logging.info(f"结果已保存到: {output_path}")

    except Exception as e:
        logging.error(f"处理过程中出错: {str(e)}")
        raise


if __name__ == '__main__':

    # main() # 单个处理
    # main_processfiles() # 批量化处理
    txt_folder = 'E:\数据\LABEL_DATA\临床任务所需数据2023-12-04\任务一\\20250521算法诊断建模\总集'
    excel_path = 'D:\std data\\20250520版本\新增数据临床信息.xlsx'
    model_path = "../files/saved_models/V1211_xgb_S42F5_boruta875_cons_bj0_nly_partial_smote_f0.pkl"  # 模型路径
    selected_features_path = '../files/saved_features/selected_features/features_V1211F5S42_boruta_cons_top875_clinic5.pkl'  # 特征选择文件路径
    selected_features_path = None  # 未指定则全部生成；
    model_path = None  # 未指定则不做预测；
    # todo：由于版本更新，该脚本目前不再完全适用，尤其是加载模型预测的环节，需要重构。
    # 目前，给定txt_folder和excel_path后，或给定一个txt_file以及相应的临床特征(这里main的临床特征要对照process_files函数中更新)，可以生成所需的基础机器学习特征，
    # 然而，具体的模型/推理 则需要参考ML_Pipline_2508\script_train_analysis_0627全量.py中的新版本实现了，(所以目前我置为none的话相当于仅生成特征)
    # 如果你深入了解，就会发现新脚本里通过加载已生成的机器学习特征，以及cur电流源特征/ci参数特征，汇合后再进行预测。
    # 所以 我们提供了outer_features文件夹下的脚本来提供cur/ci特征的生成服务。
    # 检查验证临床特征，然后加载对应版本的模型进行预测。我们的部署重构需要根据传入的txt文件和临床信息，先生成一批基础特征，再生成一批cur/ci特征，
    # 然后加载模型进行预测。 最后 在process_files 中 临床特征也有更新，你可以适当性修改Main脚本下的配置。

    output_path = './files/results/results.xlsx'  # 输出结果路径

    try:
        # 处理文件
        results_df = process_files(txt_folder, excel_path, model_path, selected_features_path, fold=0)
        print(results_df)
        # 保存结果
        results_df.to_excel(output_path, index=False)
        logging.info(f"结果已保存到: {output_path}")

    except Exception as e:
        logging.error(f"处理过程中出错: {str(e)}")
        raise

len(results_df['mcg_file'].unique())

