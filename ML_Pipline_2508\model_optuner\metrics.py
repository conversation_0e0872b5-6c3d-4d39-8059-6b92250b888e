'''
@Project ：002_Code_envs 
@File    ：metrics.py
@IDE     ：PyCharm 
<AUTHOR> Qu
@Date    ：2024/2/4 14:50 
@Discribe：
'''

from sklearn.metrics import roc_auc_score, accuracy_score, recall_score, confusion_matrix, matthews_corrcoef
import numpy as np


def get_metrics(y, pred, prob):
    if len(prob.shape) == 2:
        prob = prob[:, 1]

    auc = roc_auc_score(y, prob)
    acc = accuracy_score(y, pred)
    rec = recall_score(y, pred)
    tn, fp, fn, tp = confusion_matrix(y, pred).ravel()
    spec = tn / (tn + fp)
    mcc = matthews_corrcoef(y, pred)
    gmean = np.sqrt(rec * spec)

    auc_caculate = np.round(auc, 3)
    acc_caculate = np.round(acc, 3)
    rec_caculate = np.round(rec, 3)
    spec_caculate = np.round(spec, 3)
    gmean_caculate = np.round(gmean, 3)
    mcc_caculate = np.round(mcc, 3)

    return {'Auc': auc_caculate,
            'Acc': acc_caculate,
            'Sen': rec_caculate,
            'Spe': spec_caculate,
            'GMean': gmean_caculate,
            'Mcc': mcc_caculate,
            }
