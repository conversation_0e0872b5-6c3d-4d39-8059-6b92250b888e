from sklearn.preprocessing import PowerTransformer
import math
import warnings

from sklearn.decomposition import PCA
from sklearn.feature_selection import mutual_info_classif, f_classif
from sklearn.preprocessing import PowerTransformer

warnings.filterwarnings('ignore')


def explore_and_normalize_features(df, y=None, batch_size=20, sample_size=100, random_state=42,
                                   visualization_scaler=None):
    """
    对大量特征的数据进行探索性分析，并比较不同归一化方法的效果，
    支持基于标签(y)的分析

    参数:
    df: pandas DataFrame, 输入数据
    y: array-like 或 pandas Series, 标签数据 (可选)
    batch_size: int, 每批可视化的特征数量
    sample_size: int, 用于可视化的样本数量
    random_state: int, 随机数种子
    visualization_scaler: str 或 None, 用于可视化的标准化方法 ('MinMax', 'Standard', 'Robust', 'Yeo-Johnson', None=原始数据)

    返回:
    dict: 包含不同归一化方法处理后的DataFrame
    """
    # 判断是否有标签数据
    has_labels = y is not None
    if has_labels:
        if isinstance(y, pd.Series):
            y_series = y
        else:
            y_series = pd.Series(y)

        # 检查标签类型
        if pd.api.types.is_numeric_dtype(y_series):
            if len(y_series.unique()) < 10:  # 少量唯一值视为分类
                y_type = 'categorical'
                y_series = y_series.astype('category')
            else:
                y_type = 'continuous'
        else:
            y_type = 'categorical'
            y_series = y_series.astype('category')

        print(f"标签类型: {'分类型' if y_type == 'categorical' else '连续型'}")
        if y_type == 'categorical':
            print(f"类别数量: {len(y_series.unique())}")
            value_counts = y_series.value_counts()
            print(f"类别分布:\n{value_counts}")
            print(f"类别占比:\n{(value_counts / len(y_series) * 100).round(2)}")

            # 可视化标签分布
            plt.figure(figsize=(10, 6))
            sns.countplot(x=y_series)
            plt.title("标签分布")
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.show()

    # 1. 基本数据探索
    print(f"数据集形状: {df.shape}")
    print("\n前5行数据预览:")
    print(df.head())

    # 2. 缺失值处理和统计
    missing_stats = df.isnull().sum()
    missing_percent = (missing_stats / len(df)) * 100
    missing_info = pd.DataFrame({
        '缺失值数量': missing_stats,
        '缺失百分比': missing_percent
    })
    missing_info = missing_info[missing_info['缺失值数量'] > 0].sort_values('缺失百分比', ascending=False)

    print("\n缺失值统计 (只显示有缺失的特征):")
    if len(missing_info) > 0:
        print(missing_info)
    else:
        print("数据集中没有缺失值")

    # 3. 用0填充NaN值
    df_filled = df.fillna(0)
    print("\n已用0填充所有NaN值")

    # 4. 数据统计描述
    print("\n数据统计描述 (显示部分统计量):")
    desc_stats = df_filled.describe().T[['count', 'mean', 'std', 'min', 'max']]
    print(desc_stats.head(10))  # 只显示前10个特征的统计

    # 5. 特征分布可视化 (分批次)
    feature_names = df_filled.columns
    n_features = len(feature_names)
    n_batches = math.ceil(n_features / batch_size)

    # 抽样以加快可视化速度
    df_sample = df_filled.sample(min(sample_size, len(df_filled)), random_state=random_state)

    # 如果有标签，确保样本中包含标签
    if has_labels:
        sample_indices = df_sample.index
        y_sample = y_series.loc[sample_indices] if isinstance(y_series, pd.Series) and y_series.index.equals(
            df.index) else y_series.iloc[sample_indices.tolist()]

    print(f"\n特征分布可视化 (共{n_features}个特征，分{n_batches}批展示，每批{batch_size}个)")

    for batch in range(n_batches):
        start_idx = batch * batch_size
        end_idx = min((batch + 1) * batch_size, n_features)
        batch_features = feature_names[start_idx:end_idx]

        if len(batch_features) == 0:
            break

        print(f"\n可视化第{batch + 1}批特征 ({start_idx + 1}-{end_idx})")

        # 每批特征的分布直方图
        n_cols = min(5, len(batch_features))
        n_rows = math.ceil(len(batch_features) / n_cols)

        plt.figure(figsize=(16, n_rows * 3))

        for i, feature in enumerate(batch_features):
            plt.subplot(n_rows, n_cols, i + 1)

            # 如果有标签且是分类型，使用色彩区分不同类别
            if has_labels and y_type == 'categorical' and len(y_series.unique()) <= 10:
                sns.histplot(data=df_sample, x=feature, hue=y_sample,
                             element="step", common_norm=False, kde=True)
            else:
                sns.histplot(df_sample[feature], kde=True)

            plt.title(f"{feature}")
            plt.tight_layout()

        plt.show()

        # 是否继续显示下一批
        if batch < n_batches - 1:
            response = input("按Enter键继续查看下一批特征，输入'exit'停止可视化: ")
            if response.lower() == 'exit':
                break

    # 6. 归一化方法比较
    print("\n比较不同归一化方法")

    # 准备不同的归一化方法
    scalers = {
        'MinMax': MinMaxScaler(),
        'Standard': StandardScaler(),
        'Robust': RobustScaler(),
        'Yeo-Johnson': PowerTransformer(method='yeo-johnson')
    }

    # 存储归一化后的数据
    normalized_dfs = {}

    # 应用不同的归一化方法
    for scaler_name, scaler in scalers.items():
        print(f"\n应用 {scaler_name} 标准化...")
        try:
            normalized_data = scaler.fit_transform(df_filled)
            normalized_df = pd.DataFrame(normalized_data, columns=df_filled.columns)
            normalized_dfs[scaler_name] = normalized_df

            # 显示标准化后的统计信息
            print(f"{scaler_name} 标准化后统计信息:")
            print(normalized_df.describe().T[['mean', 'std', 'min', 'max']].head(5))
        except Exception as e:
            print(f"{scaler_name} 标准化失败: {str(e)}")

    # 如果指定了可视化的标准化方法，重新运行特征分布可视化
    if visualization_scaler is not None and visualization_scaler in normalized_dfs:
        print(f"\n使用 {visualization_scaler} 标准化数据进行特征分布可视化")

        # 获取标准化后的数据框
        viz_df = normalized_dfs[visualization_scaler]

        # 重新运行分批次可视化
        feature_names = viz_df.columns
        n_features = len(feature_names)
        n_batches = math.ceil(n_features / batch_size)

        # 抽样以加快可视化速度（使用相同的索引以保持与标签的一致性）
        viz_df_sample = viz_df.loc[df_sample.index]

        print(
            f"\n{visualization_scaler}标准化后的特征分布可视化 (共{n_features}个特征，分{n_batches}批展示，每批{batch_size}个)")

        for batch in range(n_batches):
            start_idx = batch * batch_size
            end_idx = min((batch + 1) * batch_size, n_features)
            batch_features = feature_names[start_idx:end_idx]

            if len(batch_features) == 0:
                break

            print(f"\n可视化第{batch + 1}批特征 ({start_idx + 1}-{end_idx}) - {visualization_scaler}标准化后")

            # 每批特征的分布直方图
            n_cols = min(5, len(batch_features))
            n_rows = math.ceil(len(batch_features) / n_cols)

            plt.figure(figsize=(16, n_rows * 3))

            for i, feature in enumerate(batch_features):
                plt.subplot(n_rows, n_cols, i + 1)

                # 如果有标签且是分类型，使用色彩区分不同类别
                if has_labels and y_type == 'categorical' and len(y_series.unique()) <= 10:
                    sns.histplot(data=viz_df_sample, x=feature, hue=y_sample,
                                 element="step", common_norm=False, kde=True)
                else:
                    sns.histplot(viz_df_sample[feature], kde=True)

                plt.title(f"{feature} ({visualization_scaler}标准化)")
                plt.tight_layout()

            plt.show()

            # 是否继续显示下一批
            if batch < n_batches - 1:
                response = input(f"按Enter键继续查看下一批特征({visualization_scaler}标准化)，输入'exit'停止可视化: ")
                if response.lower() == 'exit':
                    break

    # 7. 可视化不同归一化方法的比较
    # 从每种方法中选择几个有代表性的特征进行可视化比较
    print("\n可视化不同标准化方法的比较")

    # 选择几个代表性特征（方差较大的前5个特征）
    variances = df_filled.var().sort_values(ascending=False)
    representative_features = variances.index[:5].tolist()

    for feature in representative_features:
        plt.figure(figsize=(15, 10))

        # 原始数据分布
        plt.subplot(3, 2, 1)
        sns.histplot(df_sample[feature], kde=True)
        plt.title(f"原始数据: {feature}")

        # 不同标准化方法的分布
        i = 2
        for scaler_name, normalized_df in normalized_dfs.items():
            plt.subplot(3, 2, i)
            sns.histplot(normalized_df.loc[df_sample.index, feature], kde=True)
            plt.title(f"{scaler_name} 标准化: {feature}")
            i += 1

        plt.tight_layout()
        plt.show()

    # 8. PCA可视化所有特征的分布情况
    print("\n使用PCA可视化特征分布")

    # 对不同标准化的数据进行PCA
    for scaler_name, normalized_df in normalized_dfs.items():
        try:
            pca = PCA(n_components=2)
            pca_result = pca.fit_transform(normalized_df)

            plt.figure(figsize=(10, 8))

            # 如果有标签且是分类型，使用不同颜色表示不同类别
            if has_labels and y_type == 'categorical':
                # 创建颜色映射
                unique_classes = y_series.unique()

                # 绘制散点图，不同类别用不同颜色
                for cls in unique_classes:
                    mask = y_series == cls
                    plt.scatter(pca_result[mask, 0], pca_result[mask, 1],
                                alpha=0.6, label=f'类别 {cls}')
                plt.legend()
            else:
                plt.scatter(pca_result[:, 0], pca_result[:, 1], alpha=0.5)

            plt.title(f"PCA投影 ({scaler_name} 标准化)")
            plt.xlabel(f"主成分1 ({pca.explained_variance_ratio_[0]:.2%})")
            plt.ylabel(f"主成分2 ({pca.explained_variance_ratio_[1]:.2%})")
            plt.grid(True)
            plt.show()

            print(f"{scaler_name} 标准化后，前两个主成分解释的方差比例: {sum(pca.explained_variance_ratio_):.2%}")
        except Exception as e:
            print(f"{scaler_name} PCA失败: {str(e)}")

    # 9. 相关性分析（选择部分特征）
    print("\n相关性分析 (选择前30个特征)")

    # 只展示前30个特征的相关性
    subset_features = feature_names[:30]
    correlation_matrix = df_filled[subset_features].corr()

    plt.figure(figsize=(16, 14))
    sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0)
    plt.title("特征相关性热图 (前30个特征)")
    plt.tight_layout()
    plt.show()

    # 10. 归一化后的相关性变化
    print("\n比较标准化前后的相关性变化 (使用Standard标准化，前15个特征)")

    # 只取前15个特征进行对比
    small_subset = feature_names[:15]

    # 获取原始和标准化后的相关矩阵
    original_corr = df_filled[small_subset].corr()

    if 'Standard' in normalized_dfs:
        standard_corr = normalized_dfs['Standard'][small_subset].corr()

        plt.figure(figsize=(20, 8))

        plt.subplot(1, 2, 1)
        sns.heatmap(original_corr, annot=True, cmap='coolwarm', center=0, fmt='.2f')
        plt.title("标准化前的相关性 (前15个特征)")

        plt.subplot(1, 2, 2)
        sns.heatmap(standard_corr, annot=True, cmap='coolwarm', center=0, fmt='.2f')
        plt.title("Standard标准化后的相关性 (前15个特征)")

        plt.tight_layout()
        plt.show()

    # 11. 如果有标签数据，分析特征与标签的关系
    if has_labels:
        print("\n特征与标签的关系分析")

        # 选择顶部特征进行分析
        top_n_features = 20
        print(f"分析前{top_n_features}个特征与标签的关系")

        # 特征重要性评分
        if y_type == 'categorical':
            # 使用互信息和ANOVA F值
            try:
                mi_scores = mutual_info_classif(df_filled[feature_names[:50]], y_series, random_state=random_state)
                f_scores, _ = f_classif(df_filled[feature_names[:50]], y_series)

                # 创建特征重要性DataFrame
                importance_df = pd.DataFrame({
                    '特征': feature_names[:50],
                    '互信息': mi_scores,
                    'F统计量': f_scores
                })

                # 按互信息排序并显示前N个特征
                mi_top = importance_df.sort_values('互信息', ascending=False).head(top_n_features)
                f_top = importance_df.sort_values('F统计量', ascending=False).head(top_n_features)

                print("\n基于互信息的前{}个重要特征:".format(top_n_features))
                print(mi_top)

                print("\n基于F统计量的前{}个重要特征:".format(top_n_features))
                print(f_top)

                # 可视化重要特征
                plt.figure(figsize=(12, 8))
                sns.barplot(x='互信息', y='特征', data=mi_top.head(10))
                plt.title("基于互信息的前10个重要特征")
                plt.tight_layout()
                plt.show()

                # 对每个归一化方法，比较前几个重要特征的分布情况
                top_features = mi_top['特征'].head(5).tolist()

                for feature in top_features:
                    plt.figure(figsize=(15, 10))
                    plt.suptitle(f"不同标准化方法下特征'{feature}'与标签的关系", fontsize=16)

                    for i, (scaler_name, normalized_df) in enumerate(normalized_dfs.items(), 1):
                        plt.subplot(2, 2, i)

                        # 如果类别少，使用箱线图
                        if len(y_series.unique()) <= 7:
                            sns.boxplot(x=y_sample, y=normalized_df.loc[df_sample.index, feature])
                            plt.title(f"{scaler_name} 标准化")
                            plt.xlabel("标签")
                            plt.ylabel(feature)
                        else:
                            # 类别多时使用散点图
                            plt.scatter(normalized_df.loc[df_sample.index, feature], y_sample, alpha=0.5)
                            plt.title(f"{scaler_name} 标准化")
                            plt.xlabel(feature)
                            plt.ylabel("标签")

                    plt.tight_layout(rect=[0, 0, 1, 0.95])
                    plt.show()
            except Exception as e:
                print(f"计算特征重要性时出错: {str(e)}")

        elif y_type == 'continuous':
            # 连续型标签，计算相关性
            correlations = df_filled.corrwith(y_series).sort_values(ascending=False)

            # 显示相关性最强的特征
            top_corr = correlations.abs().sort_values(ascending=False).head(top_n_features)
            print("\n与标签相关性最强的{}个特征:".format(top_n_features))
            for feature, corr in top_corr.items():
                print(f"{feature}: {correlations[feature]:.4f}")

            # 可视化相关性
            plt.figure(figsize=(12, 8))
            sns.barplot(x=correlations[top_corr.index], y=top_corr.index)
            plt.title("与标签相关性最强的特征")
            plt.tight_layout()
            plt.show()

            # 对每个归一化方法，比较前几个重要特征的分布情况
            top_features = top_corr.index[:5].tolist()

            for feature in top_features:
                plt.figure(figsize=(15, 10))
                plt.suptitle(f"不同标准化方法下特征'{feature}'与标签的关系", fontsize=16)

                for i, (scaler_name, normalized_df) in enumerate(normalized_dfs.items(), 1):
                    plt.subplot(2, 2, i)

                    # 散点图显示与标签的关系
                    plt.scatter(normalized_df.loc[df_sample.index, feature], y_sample, alpha=0.5)
                    plt.title(f"{scaler_name} 标准化")
                    plt.xlabel(feature)
                    plt.ylabel("标签值")

                    # 添加趋势线
                    try:
                        z = np.polyfit(normalized_df.loc[df_sample.index, feature], y_sample, 1)
                        p = np.poly1d(z)
                        plt.plot(sorted(normalized_df.loc[df_sample.index, feature]),
                                 p(sorted(normalized_df.loc[df_sample.index, feature])),
                                 "r--", alpha=0.8)
                    except:
                        pass

                plt.tight_layout(rect=[0, 0, 1, 0.95])
                plt.show()

    return normalized_dfs


# 使用示例:
# 假设df是您的数据DataFrame，y是标签数组或Series
# normalized_data = explore_and_normalize_features(df, y=None, batch_size=20)


def compare_normalization_methods(df, y=None, feature_names=None, n_samples=200, n_features=10):
    """
    直观比较不同归一化方法对特定特征的影响

    参数:
    df: pandas DataFrame, 输入数据
    y: array-like 或 pandas Series, 标签数据 (可选)
    feature_names: list, 要分析的特征名称列表(如果为None则自动选择)
    n_samples: int, 可视化的样本数
    n_features: int, 要分析的特征数(当feature_names为None时有效)

    返回:
    None
    """
    # 检查是否有标签
    has_labels = y is not None
    # 用0填充NaN值
    df_filled = df.fillna(0)

    # 如果未提供特征名称，则自动选择
    if feature_names is None:
        # 选择方差最大的n个特征
        variances = df_filled.var().sort_values(ascending=False)
        feature_names = variances.index[:n_features].tolist()

    # 抽取样本
    df_sample = df_filled.sample(min(n_samples, len(df_filled)), random_state=42)

    # 如果有标签，获取对应样本的标签
    if has_labels:
        if isinstance(y, pd.Series):
            y_series = y
        else:
            y_series = pd.Series(y)

        # 获取样本对应的标签
        if y_series.index.equals(df.index):
            y_sample = y_series.loc[df_sample.index]
        else:
            y_sample = y_series.iloc[df_sample.index.tolist()]

        # 确定标签类型
        if pd.api.types.is_numeric_dtype(y_series):
            if len(y_series.unique()) < 10:  # 少量唯一值视为分类
                y_type = 'categorical'
                y_sample = y_sample.astype('category')
            else:
                y_type = 'continuous'
        else:
            y_type = 'categorical'
            y_sample = y_sample.astype('category')

    # 定义归一化方法
    scalers = {
        '原始数据': None,
        'Min-Max缩放': MinMaxScaler(),
        'Z-score标准化': StandardScaler(),
        '鲁棒缩放': RobustScaler(),
        'Yeo-Johnson变换': PowerTransformer(method='yeo-johnson')
    }

    # 为每个特征创建一个图
    for feature in feature_names:
        plt.figure(figsize=(15, 10))
        plt.suptitle(f"特征 '{feature}' 的不同归一化方法比较", fontsize=16)

        # 获取适当的箱数
        orig_data = df_sample[feature]
        bins = min(50, max(10, int(np.sqrt(len(orig_data)))))

        # 绘制每种归一化方法的结果
        for i, (name, scaler) in enumerate(scalers.items(), 1):
            plt.subplot(3, 2, i)

            if scaler is None:
                # 原始数据
                data = orig_data
            else:
                # 应用归一化
                try:
                    # 仅对当前特征应用归一化
                    data_array = df_sample[feature].values.reshape(-1, 1)
                    scaled_data = scaler.fit_transform(data_array).flatten()
                    data = pd.Series(scaled_data)
                except Exception as e:
                    plt.text(0.5, 0.5, f"错误: {str(e)}",
                             horizontalalignment='center',
                             verticalalignment='center',
                             transform=plt.gca().transAxes)
                    continue

            # 绘制直方图和KDE（带标签信息）
            if has_labels and y_type == 'categorical' and len(y_sample.unique()) <= 8:
                # 使用标签颜色区分直方图
                temp_df = pd.DataFrame({feature: data, 'label': y_sample})
                sns.histplot(data=temp_df, x=feature, hue='label', element="step",
                            common_norm=False, kde=True, bins=bins)
            else:
                sns.histplot(data, kde=True, bins=bins)

            # 添加统计数据
            stats_text = f"均值: {data.mean():.2f}\n标准差: {data.std():.2f}\n"
            stats_text += f"最小值: {data.min():.2f}\n最大值: {data.max():.2f}\n"
            stats_text += f"偏度: {data.skew():.2f}\n峰度: {data.kurtosis():.2f}"

            plt.title(f"{name}")
            plt.xlim(data.min() - 0.1 * data.std(), data.max() + 0.1 * data.std())
            plt.figtext(0.02 + 0.33 * ((i-1) % 2),
                       0.95 - 0.33 * ((i-1) // 2),
                       stats_text,
                       fontsize=9,
                       bbox=dict(facecolor='white', alpha=0.8))

        plt.tight_layout(rect=[0, 0, 1, 0.95])
        plt.show()

        # 是否继续到下一个特征
        if feature != feature_names[-1]:
            response = input("按Enter键继续查看下一个特征，输入'exit'停止可视化: ")
            if response.lower() == 'exit':
                break


# 使用示例：
# 展示特定特征的归一化效果（带标签）
# compare_normalization_methods(df, y=labels, n_features=5)


def analyze_features_by_class(df, y, top_n=10, n_samples=500, random_state=42):
    """
    按类别分析特征，找出能够区分不同类别的重要特征

    参数:
    df: pandas DataFrame, 输入数据
    y: array-like 或 pandas Series, 标签数据
    top_n: int, 展示的顶部特征数量
    n_samples: int, 可视化的样本数量
    random_state: int, 随机数种子

    返回:
    pd.DataFrame: 特征重要性排序结果
    """
    # 转换标签为Series
    if not isinstance(y, pd.Series):
        y = pd.Series(y)

    # 填充缺失值
    df_filled = df.fillna(0)

    # 随机抽样用于可视化
    df_sample = df_filled.sample(min(n_samples, len(df_filled)), random_state=random_state)

    # 获取类别标签
    if y.index.equals(df.index):
        y_sample = y.loc[df_sample.index]
    else:
        y_sample = y.iloc[df_sample.index.tolist()]

    # 确保标签是分类型
    unique_labels = y.unique()
    if len(unique_labels) > 10:
        print(f"警告：标签类别过多({len(unique_labels)}个)，只显示前10个类别")
        unique_labels = unique_labels[:10]

    # 计算每个特征的类别间区分度
    print(f"计算每个特征的类别间区分度...")

    feature_importance = {}

    # 使用互信息
    try:
        feature_importance['互信息'] = mutual_info_classif(df_filled, y, random_state=random_state)
    except Exception as e:
        print(f"计算互信息时出错: {str(e)}")

    # 使用F统计量
    try:
        f_values, _ = f_classif(df_filled, y)
        feature_importance['F统计量'] = f_values
    except Exception as e:
        print(f"计算F统计量时出错: {str(e)}")

    # 按每个类别的特征均值差异
    feature_means = {}
    for label in unique_labels:
        mask = y == label
        feature_means[label] = df_filled[mask].mean()

    # 计算类别间特征均值的标准差
    feature_means_df = pd.DataFrame(feature_means).T
    feature_importance['类别间变异度'] = feature_means_df.std().values

    # 合并特征重要性评分
    importance_df = pd.DataFrame({'特征': df_filled.columns})

    for method, scores in feature_importance.items():
        importance_df[method] = scores
        # 归一化分数
        if method in ['互信息', 'F统计量', '类别间变异度']:
            importance_df[f'{method}_norm'] = (scores - scores.min()) / (scores.max() - scores.min() + 1e-10)

    # 计算综合得分
    available_methods = [m for m in ['互信息_norm', 'F统计量_norm', '类别间变异度_norm'] if m in importance_df.columns]
    if available_methods:
        importance_df['综合得分'] = importance_df[available_methods].mean(axis=1)

    # 按综合得分排序
    if '综合得分' in importance_df.columns:
        importance_df = importance_df.sort_values('综合得分', ascending=False)
    elif '互信息' in importance_df.columns:
        importance_df = importance_df.sort_values('互信息', ascending=False)
    elif 'F统计量' in importance_df.columns:
        importance_df = importance_df.sort_values('F统计量', ascending=False)

    # 显示顶部特征
    print(f"\n顶部{top_n}个区分性特征:")
    display_cols = ['特征'] + [col for col in importance_df.columns if not col.endswith('_norm')]
    print(importance_df[display_cols].head(top_n))

    # 可视化顶部特征
    top_features = importance_df['特征'].head(top_n).tolist()

    # 绘制不同类别的箱线图
    for feature in top_features[:min(5, len(top_features))]:
        plt.figure(figsize=(12, 6))
        sns.boxplot(x=y_sample, y=df_sample[feature])
        plt.title(f"特征 '{feature}' 在不同类别下的分布")
        plt.xlabel("类别")
        plt.ylabel(feature)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

    # 绘制前两个重要特征的散点图
    if len(top_features) >= 2:
        plt.figure(figsize=(10, 8))
        feature1, feature2 = top_features[0], top_features[1]

        sns.scatterplot(data=df_sample, x=feature1, y=feature2, hue=y_sample)
        plt.title(f"顶部两个特征的散点图")
        plt.xlabel(feature1)
        plt.ylabel(feature2)
        plt.legend(title="类别")
        plt.tight_layout()
        plt.show()

    # 应用标准化并可视化效果
    print("\n应用标准化后的特征区分效果:")

    # 使用StandardScaler
    try:
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        df_scaled = pd.DataFrame(
            scaler.fit_transform(df_sample),
            columns=df_sample.columns,
            index=df_sample.index
        )

        # 绘制标准化后的顶部特征箱线图
        for feature in top_features[:min(3, len(top_features))]:
            plt.figure(figsize=(12, 10))

            # 原始数据
            plt.subplot(2, 1, 1)
            sns.boxplot(x=y_sample, y=df_sample[feature])
            plt.title(f"标准化前: '{feature}'")
            plt.xlabel("类别")
            plt.ylabel(feature)

            # 标准化后数据
            plt.subplot(2, 1, 2)
            sns.boxplot(x=y_sample, y=df_scaled[feature])
            plt.title(f"标准化后: '{feature}'")
            plt.xlabel("类别")
            plt.ylabel(f"{feature} (标准化)")

            plt.tight_layout()
            plt.show()
    except Exception as e:
        print(f"标准化可视化失败: {str(e)}")

    return importance_df


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler, MaxAbsScaler
import warnings

warnings.filterwarnings('ignore')  # 忽略警告信息


def explore_and_normalize_analysis(data, batch_size=50):
    """
    对数据集进行探索性分析、可视化和归一化方法对比。

    参数:
    - data: 输入数据集（Pandas DataFrame）
    - batch_size: 每批次可视化的特征数量，默认50
    """

    # 第一步：数据预处理 - 将NaN填充为0
    print("正在填充缺失值（NaN -> 0）...")
    data_filled = data.fillna(0)

    # 基本统计信息
    print("\n=== 数据基本信息 ===")
    print(f"数据形状: {data_filled.shape}")
    print(f"缺失值总数（填充前）: {data.isna().sum().sum()}")
    print("\n前5行数据预览:")
    print(data_filled.head())

    # 特征数量和批次划分
    n_features = data_filled.shape[1]
    n_batches = (n_features + batch_size - 1) // batch_size
    print(f"\n共有 {n_features} 个特征，将分 {n_batches} 批次可视化，每批最多 {batch_size} 个特征。")

    # 第二步：分批次可视化特征分布
    for batch in range(n_batches):
        start_idx = batch * batch_size
        end_idx = min((batch + 1) * batch_size, n_features)
        batch_features = data_filled.columns[start_idx:end_idx]

        print(f"\n=== 可视化第 {batch + 1}/{n_batches} 批次特征分布 ===")
        plt.figure(figsize=(15, 10))
        for i, col in enumerate(batch_features, 1):
            plt.subplot(5, 10, i)  # 每页5行10列
            sns.histplot(data_filled[col], bins=20, kde=True)
            plt.title(col, fontsize=8)
            plt.xticks(fontsize=6)
            plt.yticks(fontsize=6)
        plt.tight_layout()
        plt.show()

        # 异常值检测（基于IQR）
        for col in batch_features:
            Q1 = data_filled[col].quantile(0.25)
            Q3 = data_filled[col].quantile(0.75)
            IQR = Q3 - Q1
            outliers = ((data_filled[col] < (Q1 - 1.5 * IQR)) | (data_filled[col] > (Q3 + 1.5 * IQR))).sum()
            print(f"{col}: 异常值数量 = {outliers}")

    # 第三步：比较不同归一化方法
    print("\n=== 比较不同归一化方法 ===")
    sample_features = data_filled.columns[:5]  # 取前5个特征作为示例
    data_sample = data_filled[sample_features]

    # 定义归一化方法
    scalers = {
        "MinMaxScaler": MinMaxScaler(),
        "StandardScaler": StandardScaler(),
        "RobustScaler": RobustScaler(),
        "MaxAbsScaler": MaxAbsScaler()
    }

    # 应用归一化并可视化
    plt.figure(figsize=(15, 10))
    for i, (name, scaler) in enumerate(scalers.items(), 1):
        scaled_data = scaler.fit_transform(data_sample)
        scaled_df = pd.DataFrame(scaled_data, columns=sample_features)

        # 绘制箱线图对比
        plt.subplot(2, 2, i)
        sns.boxplot(data=scaled_df)
        plt.title(f"{name}", fontsize=10)
        plt.xticks(rotation=45, fontsize=8)
    plt.tight_layout()
    plt.show()

    # 统计归一化后的分布特性
    for name, scaler in scalers.items():
        scaled_data = scaler.fit_transform(data_sample)
        scaled_df = pd.DataFrame(scaled_data, columns=sample_features)
        print(f"\n{name} 归一化后统计信息:")
        print(scaled_df.describe())
# 使用示例:
# feature_importance = analyze_features_by_class(df, y, top_n=15)