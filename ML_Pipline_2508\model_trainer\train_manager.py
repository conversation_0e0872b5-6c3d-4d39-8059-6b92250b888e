import pandas as pd
import numpy as np
from pathlib import Path
from tqdm.auto import tqdm
from model_trainer.data_augmentor import StratifiedBalancedDataAugmentor
import os
import time
from joblib import Parallel, delayed

from sklearn.model_selection import train_test_split

import json
import pickle
from datetime import datetime
import xgboost as xgb
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import classification_report, roc_auc_score
import shap
from model_trainer.clinical_feature_augment import enrich_clinical_features


# 全局变量用于存储合并后的特征
_COMBINED_FEATURES_DF = None
_COMBINED_FEATURES_PATH = None
_MCG_ID_INDEX = None  # 用于快速查找


class Condition_config:
    DELETE_SY = True  # 是否删除上海十院数据
    ONLY_SY = True  # 是否删除上海十院数据
    DELETE_NH = True  # 是否删除广东南海CT备注为1的数据
    RECUR_301 = True  # 是否对因北京慢血流为1而原造影结论为0的数据，综合标签改回0
    META_LABLE = True  # 是否对'综合的标签'为1的数据，修改其为'造影结论'为1  ,notice: 0520后的数据去掉了'的'
    USE_EXTRA_FEATURE = True  # 是否使用额外特征
    REFRESH_CLINIC_FEATURE = True # 是否刷新临床特征
    GEN_CLINIC_EXTRA_FEATURE = True # 是否生成医院独热编码/BMI
    DELETE_HT = True  # 是否删除高血压数据
    DELETE_NHT = False  # 是否删除非高血压数据
    DELETE_SY3HT = False  # 是否删除十院3类高血压数据
    DELETE_SY3R = False  # 是否控制十院3类数据比例
    DELETE_PIANCHA = True
    # 医院名称映射
    HOSPITAL_MAPPING = {
        '上海中山': 0,
        '上海六院': 1,
        '北京301': 3,
        '上海十院': 2,
        '安医': 4,
        '广东南海': 5,
    }

    # 反向映射，用于显示
    REVERSE_HOSPITAL_MAPPING = {v: k for k, v in HOSPITAL_MAPPING.items()}

    @classmethod
    def reload(cls):
        """重新加载配置"""
        import importlib
        import sys
        if "Condition_config" in sys.modules:
            importlib.reload(sys.modules["Condition_config"])

Condition_config.DELETE_SY = False
Condition_config.ONLY_SY = False  # 是否仅保留十院数据
Condition_config.DELETE_NH = False
Condition_config.RECUR_301 = False
Condition_config.META_LABLE = True
Condition_config.USE_EXTRA_FEATURE = True
Condition_config.REFRESH_CLINIC_FEATURE=True
Condition_config.GEN_CLINIC_EXTRA_FEATURE=True
Condition_config.DELETE_SY3HT = False  # 是否删除十院3类高血压数据
Condition_config.DELETE_SY3R = False  # 是否控制十院3类数据比例
Condition_config.DELETE_NHT = False  # 是否仅保留非高血压数据
Condition_config.DELETE_HT = False  # 是否仅保留高血压数据
Condition_config.ONLY_NHT = False  # 是否仅保留非高血压数据
Condition_config.ONLY_HT = False  # 是否仅保留高血压数据
Condition_config.DELETE_PIANCHA = True  # 是否删除偏差数据

Condition_config.reload()
print("Condition_config reloaded\n")


def ensure_combined_features(feature_dir, force_rebuild=False):
    """确保合并特征文件存在且已加载到内存中"""
    global _COMBINED_FEATURES_DF, _COMBINED_FEATURES_PATH, _MCG_ID_INDEX

    feature_dir = Path(feature_dir)
    combined_path = feature_dir / "combined_features.pkl"
    _COMBINED_FEATURES_PATH = combined_path

    # 检查是否需要重建合并文件
    rebuild = force_rebuild or not combined_path.exists()

    if not rebuild and _COMBINED_FEATURES_DF is None:
        # 尝试加载现有合并文件
        try:
            print("加载合并特征文件...")
            with open(combined_path, 'rb') as f:
                data = pickle.load(f)
                _COMBINED_FEATURES_DF = data['df']
                _MCG_ID_INDEX = data['index']
            print(f"已加载 {len(_MCG_ID_INDEX)} 个心磁号的特征")
            return
        except Exception as e:
            print(f"加载合并特征失败: {e}")
            rebuild = True

    if rebuild:
        # 构建合并特征文件
        print("构建合并特征文件...")
        # 获取目录中所有特征文件
        feature_files = list(feature_dir.glob("*.pkl"))
        feature_files = [f for f in feature_files if f.name != "combined_features.pkl"]

        # 从文件名提取心磁号
        mcg_ids = [f.stem for f in feature_files]

        # 加载所有特征
        features_list = Parallel(n_jobs=20)(
            delayed(load_single_feature)(feature_dir, mcg_id)
            for mcg_id in tqdm(mcg_ids, desc="加载特征以合并")
        )

        # 给每个特征添加心磁号
        for i, df in enumerate(features_list):
            df['mcg_id'] = mcg_ids[i]

        # 合并为一个大的DataFrame
        _COMBINED_FEATURES_DF = pd.concat(features_list, ignore_index=True)

        # 创建索引字典，用于快速定位特定心磁号的行
        _MCG_ID_INDEX = {}
        for mcg_id in mcg_ids:
            indices = _COMBINED_FEATURES_DF.index[_COMBINED_FEATURES_DF['mcg_id'] == mcg_id].tolist()
            if indices:
                _MCG_ID_INDEX[mcg_id] = indices

        # 保存合并特征
        with open(combined_path, 'wb') as f:
            pickle.dump({'df': _COMBINED_FEATURES_DF, 'index': _MCG_ID_INDEX}, f)

        print(f"已构建合并特征，包含 {len(_MCG_ID_INDEX)} 个心磁号")


def get_features(feature_dir, mcg_ids, rebuild_if_missing=True):
    """获取指定心磁号的特征，从合并DataFrame中加载"""
    global _COMBINED_FEATURES_DF, _MCG_ID_INDEX

    # 确保合并特征已加载
    ensure_combined_features(feature_dir)

    # 检查哪些心磁号需要单独加载
    existing_mcg_ids = set(_MCG_ID_INDEX.keys())
    missing_mcg_ids = [mcg_id for mcg_id in mcg_ids if mcg_id not in existing_mcg_ids]

    if missing_mcg_ids and rebuild_if_missing:
        print(f"单独加载 {len(missing_mcg_ids)} 个缺失的心磁号...")

        # 单独加载缺失特征
        missing_features_list = Parallel(n_jobs=20)(
            delayed(load_single_feature)(feature_dir, mcg_id)
            for mcg_id in tqdm(missing_mcg_ids, desc="加载缺失特征")
        )

        # 添加心磁号列
        for i, df in enumerate(missing_features_list):
            df['mcg_id'] = missing_mcg_ids[i]

        # 合并缺失特征到大DataFrame
        if missing_features_list:
            new_rows = pd.concat(missing_features_list, ignore_index=True)
            original_len = len(_COMBINED_FEATURES_DF)
            _COMBINED_FEATURES_DF = pd.concat([_COMBINED_FEATURES_DF, new_rows], ignore_index=True)

            # 更新索引信息
            for mcg_id in missing_mcg_ids:
                new_indices = _COMBINED_FEATURES_DF.index[_COMBINED_FEATURES_DF['mcg_id'] == mcg_id].tolist()
                if new_indices:
                    _MCG_ID_INDEX[mcg_id] = new_indices

            # 更新合并特征文件
            with open(_COMBINED_FEATURES_PATH, 'wb') as f:
                pickle.dump({'df': _COMBINED_FEATURES_DF, 'index': _MCG_ID_INDEX}, f)

            print(f"已更新合并特征，现在有 {len(_MCG_ID_INDEX)} 个心磁号")

    # 从DataFrame中获取相关心磁号的行
    valid_mcg_ids = [mcg_id for mcg_id in mcg_ids if mcg_id in _MCG_ID_INDEX]

    if not valid_mcg_ids:
        # 如果没有找到任何心磁号，返回一个有正确列的空DataFrame
        if _COMBINED_FEATURES_DF is not None:
            return _COMBINED_FEATURES_DF.iloc[0:0].drop(columns=['mcg_id'])
        else:
            return pd.DataFrame()

    # 构建索引列表以提高性能
    row_indices = []
    for mcg_id in valid_mcg_ids:
        row_indices.extend(_MCG_ID_INDEX[mcg_id])

    # 使用.iloc进行高效的行选择
    result = _COMBINED_FEATURES_DF.iloc[row_indices].copy()

    # 删除mcg_id列，保持与原始实现一致
    result = result.drop(columns=['mcg_id'])

    return result

def condition_policy(df):
    """
    根据条件修改数据框中的值
    :param df: 输入数据框
    :return: 修改后的数据框
    """
    print(f"当前配置:")
    print(f"  DELETE_SY: {Condition_config.DELETE_SY}")
    print(f"  ONLY_SY: {Condition_config.ONLY_SY}")

    print(f"  DELETE_NH: {Condition_config.DELETE_NH}")
    print(f"  RECUR_301: {Condition_config.RECUR_301}")
    print(f"  META_LABLE: {Condition_config.META_LABLE}")
    print(f"  USE_EXTRA_FEATURE: {Condition_config.USE_EXTRA_FEATURE}")
    print(f"  REFRESH_CLINIC_FEATURE: {Condition_config.REFRESH_CLINIC_FEATURE}")
    print(f"  GEN_CLINIC_EXTRA_FEATURE: {Condition_config.GEN_CLINIC_EXTRA_FEATURE}")
    print(f"  DELETE_HT: {Condition_config.DELETE_HT}")
    print(f"  DELETE_NHT: {Condition_config.DELETE_NHT}")
    print(f"  DELETE_SY3HT: {Condition_config.DELETE_SY3HT}")
    print(f"  DELETE_SY3R: {Condition_config.DELETE_SY3R}")
    print(f"  ONLY_NHT: {Condition_config.ONLY_NHT}")
    print(f"  ONLY_HT: {Condition_config.ONLY_HT}")

    if Condition_config.DELETE_SY:
        # 删除所有十院数据
        df = df[~(df['临床特征-所在医院'] == '上海十院')]

    if Condition_config.ONLY_SY:
        # 仅保留十院数据
        df = df[(df['临床特征-所在医院'] == '上海十院')]

    if Condition_config.DELETE_NH:
        # 删除所有CTA标记为1的数据
        df = df[~(df['广东南海CT备注'] == 1)]

    if Condition_config.RECUR_301:
        # 对因北京慢血流为1而原造影结论为0的数据，综合标签改回0
        df = df[~(df['临床特征-所在医院'] == '北京301')]

    if Condition_config.META_LABLE:
        # 对'综合的标签'为1的数据，修改其为'造影结论'为1
        df.loc[df['综合标签'] == 1, '造影结论'] = 1

    if Condition_config.DELETE_HT:
        # 删除所有高血压标记为1的数据
        df = df[~(df['临床特征-高血压'] == 1)]

    if Condition_config.DELETE_NHT:
        # 删除所有高血压标记为0的数据
        df = df[~(df['临床特征-高血压'] == 0)]

    if Condition_config.ONLY_HT:
        # 保留所有高血压标记为1的数据
        df = df[(df['临床特征-高血压'] == 1)]

    if Condition_config.ONLY_NHT:
        # 保留所有高血压标记为0的数据
        df = df[(df['临床特征-高血压'] == 0)]

    if Condition_config.DELETE_SY3HT:
        # 删除所有十院且高血压且’综合标签分类‘=3的数据
        df = df[~((df['临床特征-所在医院'] == '上海十院') & (df['临床特征-高血压'] == 1) & (df['综合标签分类'] == 3))]
        print(f"已删除所有十院且高血压且'综合标签分类'为3的数据")

    if Condition_config.DELETE_SY3R:
        # 控制十院综合标签为3的数据比例，采用随机删除的方式，控制在十院总数据量的10%以内
        sy3_count = (df['临床特征-所在医院'] == '上海十院') & (df['综合标签分类'] == 3)
        if sy3_count.count() > 0:
            sy_count = df['临床特征-所在医院'] == '上海十院'
            max_allowed = int(sy_count.sum() * 0.1)
            if sy3_count.sum() > max_allowed:
                indices_to_remove = sy3_count[sy3_count].sample(n=sy3_count.sum() - max_allowed).index
                df = df.drop(indices_to_remove)
                print(f"已删除 {len(indices_to_remove)} 条十院3类数据，以控制其比例在10%以内")
                print(f"当前十院3类数据比例为 {df[(df['临床特征-所在医院'] == '上海十院') & (df['综合标签分类'] == 3)].shape[0] / df[df['临床特征-所在医院'] == '上海十院'].shape[0]:.2f}")

    if Condition_config.DELETE_PIANCHA:
        # 删除所有偏差数据
        df = df[~(df['心磁质量'] == "偏差")]
        # df = df[~(df['心磁质量'] == "稍差")]

        print("已删除所有偏差数据")
    return df

def load_single_feature(feature_dir, mcg_id):
    """加载单个特征文件"""
    feature_path = feature_dir / f"{mcg_id}.pkl"
    with open(feature_path, 'rb') as f:
        feat_df = pickle.load(f)
    # 直接用索引删除label开头的列
    label_cols = [col for col in feat_df.columns if col.startswith('label')]
    feat_df.drop(columns=label_cols, inplace=True)
    return feat_df


class CustomStratifiedKFold:
    """支持多特征联合分层的交叉验证

    该类支持基于多个分类特征和目标变量进行分层抽样，确保在每个fold中保持各个特征的分布
    可以选择返回单次分割或多折交叉验证
    """

    def __init__(self, n_splits=5, test_size=None, shuffle=True, random_state=None, min_samples_per_group=2):
        """
        参数:
            n_splits (int): 折数，当test_size不为None时此参数无效
            test_size (float): 测试集比例，取值范围(0,1)，若指定则执行单次分割而非交叉验证
            shuffle (bool): 是否打乱数据
            random_state (int): 随机种子
            min_samples_per_group (int): 每个组合类别的最小样本数，小于此数的组会被归类为稀有类别
        """
        self.n_splits = n_splits
        self.test_size = test_size
        self.shuffle = shuffle
        self.random_state = random_state
        self.min_samples_per_group = min_samples_per_group

    def _create_combined_labels(self, feature_arrays, y):
        """
        将多个特征和标签组合成复合标签

        参数:
            feature_arrays (list): 特征数组列表，每个数组对应一个分层特征
            y: 目标变量数组

        返回:
            list: 组合后的标签列表
        """
        # 确保所有输入都转换为numpy数组
        feature_arrays = [np.array(arr) for arr in feature_arrays]
        y = np.array(y)
        lengths = [len(arr) for arr in feature_arrays]
        assert len(set(lengths)) == 1, "所有特征数组长度必须相同"
        assert len(y) == lengths[0], "特征数组与标签长度必须相同"

        combined = []
        for i in range(len(y)):
            try:
                features = "_".join(str(arr[i]) for arr in feature_arrays)
            except Exception as e:
                print(f"Error at index {i}")
                print("Array lengths:", [len(arr) for arr in feature_arrays])
                print("Array types:", [type(arr) for arr in feature_arrays])
                raise e
            label = str(y[i])
            combined.append(f"{features}_{label}")

        return combined

    def _handle_rare_cases(self, combined_labels):
        """
        处理样本数过少的类别组合

        参数:
            combined_labels (list): 原始组合标签列表

        返回:
            list: 处理后的标签列表
        """
        label_counts = pd.Series(combined_labels).value_counts()
        rare_labels = label_counts[label_counts < self.min_samples_per_group].index

        processed_labels = [
            label if label not in rare_labels else f'rare_case_{label.split("_")[-1]}'
            for label in combined_labels
        ]

        return processed_labels

    def split(self, X, y, *feature_arrays):
        """
        执行分层分割

        参数:
            X: 特征矩阵
            y: 目标变量
            *feature_arrays: 可变数量的分层特征数组

        返回:
            generator: 生成训练集和测试集的索引
        """
        print("X shape:", X.shape)
        # print("y length:", len(y))
        # for i, arr in enumerate(feature_arrays):
        #     print(f"feature array {i} type:", type(arr))
        #     print(f"feature array {i} length:", len(arr))

        combined_labels = self._create_combined_labels(feature_arrays, y)
        processed_labels = self._handle_rare_cases(combined_labels)

        if self.test_size is not None:
            from sklearn.model_selection import StratifiedShuffleSplit
            # 使用StratifiedShuffleSplit进行单次分割
            sss = StratifiedShuffleSplit(
                n_splits=1,
                test_size=self.test_size,
                random_state=self.random_state
            )
            # 只返回单次分割的结果
            for train_idx, test_idx in sss.split(X, processed_labels):
                yield train_idx, test_idx
        else:
            # 使用StratifiedKFold进行交叉验证
            skf = StratifiedKFold(
                n_splits=self.n_splits,
                shuffle=self.shuffle,
                random_state=self.random_state
            )
            # 返回交叉验证的多次分割结果
            for train_idx, test_idx in skf.split(X, processed_labels):
                yield train_idx, test_idx

def process_clinic_features_batch(clinic_data_df: pd.DataFrame) -> pd.DataFrame:
    """
    批量处理临床特征，向量化操作以提高性能 (代码来自用户)

    Args:
        clinic_data_df: 包含所有临床数据的DataFrame

    Returns:
        处理后的临床特征DataFrame
    """
    # 创建结果DataFrame的副本
    result_df = clinic_data_df.copy()

    # 定义处理规则字典，避免重复代码
    processing_rules = {
        'clinic_height': lambda df: pd.to_numeric(df['clinic_height'], errors='coerce').apply(
            lambda x: float(x) if pd.notna(x) and x > 0 else -1),
        'clinic_weight': lambda df: pd.to_numeric(df['clinic_weight'], errors='coerce').apply(
            lambda x: float(x) if pd.notna(x) and x > 0 else -1),
        'clinic_age': lambda df: pd.to_numeric(df['clinic_age'], errors='coerce').apply(
            lambda x: float(x) if pd.notna(x) and x > 0 else -1),
        'clinic_gender': lambda df: df['clinic_gender'].apply(
            lambda x: 1 if x == '男' else (0 if x == '女' else
                                           (int(x) if pd.notna(x) and str(x) in ['0', '1'] else -1))), # 修正: 检查字符串 '0', '1'
    }

    # 批量处理数值型特征
    numeric_features = ['clinic_smoking', 'clinic_drinking', 'clinic_hypertension',
                        'clinic_hyperlipidemia', 'clinic_intervention', 'clinic_diabetes', 'clinic_symp']

    for feature in numeric_features:
        if feature in result_df.columns:
            # 转换为数值，无法转换的变为NaN，然后填充-1
            result_df[feature] = pd.to_numeric(result_df[feature], errors='coerce').fillna(-1).astype(int) # 确保为整数

    # 处理医院特征
    if 'clinic_hospital' in result_df.columns:
        # 确保 HOSPITAL_MAPPING 的值是存在的
        valid_hospital_codes = set(Condition_config.HOSPITAL_MAPPING.values())
        result_df['clinic_hospital'] = result_df['clinic_hospital'].apply(
            lambda x: Condition_config.HOSPITAL_MAPPING.get(str(x), x) if isinstance(x, str) else # 尝试用字符串匹配
            (int(x) if isinstance(x, (int, float)) and pd.notna(x) and int(x) in valid_hospital_codes else -1)
        )
        # 再次检查，确保无法匹配的都变成-1
        result_df['clinic_hospital'] = result_df['clinic_hospital'].apply(
             lambda x: int(x) if pd.notna(x) and isinstance(x, (int, float)) and x in valid_hospital_codes else -1
        )


    # 应用定义的处理规则
    for feature, rule in processing_rules.items():
        if feature in result_df.columns:
            result_df[feature] = rule(result_df)

    # 确保 clinic_gender 为整数类型
    if 'clinic_gender' in result_df.columns:
         result_df['clinic_gender'] = result_df['clinic_gender'].astype(int)

    return result_df


def refresh_clinic_features(feature_df, mcg_ids, excel_path, sheet_name='测试集', verbose: bool = True):
    """
    优化版的刷新临床特征函数，增加了verbose参数用于打印详细更新信息。

    Args:
        feature_df: 特征DataFrame
        mcg_ids: 心磁号Series或列表
        excel_path: Excel文件路径
        sheet_name: sheet名称
        verbose (bool, optional): 是否打印详细的更新过程和变动。默认为 False。

    Returns:
        更新后的特征DataFrame
    """
    start_time = time.time()
    print("开始刷新临床特征...")

    try:
        # 1. 提前转换mcg_ids为字符串类型，避免循环中重复转换
        mcg_ids_str = pd.Series(mcg_ids).astype(str).tolist()
        mcg_id_set = set(mcg_ids_str)  # 使用集合加速查找

        # 2. 读取Excel文件，只读取需要的列以减少内存使用
        columns_to_read = ['心磁号']
        clinic_features_excel = [ # Excel中的列名
            '临床特征-身高', '临床特征-体重', '临床特征-性别', '临床特征-年龄',
            '临床特征-吸烟', '临床特征-饮酒', '临床特征-高血压', '临床特征-高脂血症',
            '临床特征-既往介入', '临床特征-所在医院', '临床特征-糖尿病', '临床特征-典型症状'
        ]
        # 实际需要的列是 '心磁号' + clinic_features_excel 中存在的列
        try:
            # 先尝试读取第一行获取实际存在的列名
            header_df = pd.read_excel(excel_path, sheet_name=sheet_name, nrows=0)
            actual_columns_to_read = ['心磁号'] + [col for col in clinic_features_excel if col in header_df.columns]
            print(f"实际读取的列: {actual_columns_to_read}")
            df_info = pd.read_excel(excel_path, sheet_name=sheet_name, usecols=actual_columns_to_read)
        except Exception as e:
            print(f"读取Excel时发生错误: {e}")
            print(f"请检查文件 '{excel_path}' 的 sheet '{sheet_name}' 是否存在，以及列名是否正确。")
            return feature_df # 返回原始df

        print(f"读取Excel文件: {excel_path}, Sheet: {sheet_name}")
        # df_info = pd.read_excel(excel_path, sheet_name=sheet_name, usecols=columns_to_read + clinic_features)
        print(f"Excel读取完成, 时间: {time.time() - start_time:.2f}秒")

        # 3. 预处理: 转换心磁号为字符串类型
        if '心磁号' not in df_info.columns:
            print("错误：Excel文件中未找到 '心磁号' 列。")
            return feature_df
        df_info['心磁号'] = df_info['心磁号'].astype(str)

        # 4. 筛选出包含于mcg_ids的记录，减少后续处理量
        df_info_filtered = df_info[df_info['心磁号'].isin(mcg_id_set)].copy() # 使用copy避免SettingWithCopyWarning
        print(f"筛选相关记录，从{len(df_info)}条减少到{len(df_info_filtered)}条")

        if len(df_info_filtered) == 0:
            print("没有找到匹配的心磁号记录")
            return feature_df

        # 5. 重命名列以匹配process_clinic_features的输入格式
        column_mapping = {
            '临床特征-身高': 'clinic_height',
            '临床特征-体重': 'clinic_weight',
            '临床特征-性别': 'clinic_gender',
            '临床特征-年龄': 'clinic_age',
            '临床特征-吸烟': 'clinic_smoking',
            '临床特征-饮酒': 'clinic_drinking',
            '临床特征-高血压': 'clinic_hypertension',
            '临床特征-高脂血症': 'clinic_hyperlipidemia',
            '临床特征-既往介入': 'clinic_intervention',
            '临床特征-所在医院': 'clinic_hospital',
            '临床特征-糖尿病': 'clinic_diabetes',
            '临床特征-典型症状': 'clinic_symp'
        }

        # 只重命名存在的列
        rename_dict = {k: v for k, v in column_mapping.items() if k in df_info_filtered.columns}
        df_info_filtered.rename(columns=rename_dict, inplace=True)

        # 添加 feature_df 中存在但 Excel 中可能缺失的 clinic_ 列，并用 NaN 填充
        # 以便 process_clinic_features_batch 能处理所有预期的 clinic_ 列
        expected_clinic_cols = set(column_mapping.values())
        for col in expected_clinic_cols:
             if col not in df_info_filtered.columns and col in feature_df.columns:
                 df_info_filtered[col] = np.nan # 添加缺失列


        # 6. 批量处理临床特征
        print("开始批量处理临床特征...")
        processed_features = process_clinic_features_batch(df_info_filtered)
        print(f"特征批量处理完成, 时间: {time.time() - start_time:.2f}秒")

        # 7. 创建用于更新的结果DataFrame
        result_df = feature_df.copy()

        # 8. 构建心磁号到索引的映射，加速查找
        # 8. 建立 mcg_id -> 行号映射
        mcg_idx_map = {mcg_id: idx for idx, mcg_id in enumerate(mcg_ids_str)}

        # 9. 过滤 processed_features，只保留匹配 mcg_ids 的行
        processed_features = processed_features[processed_features['心磁号'].isin(mcg_idx_map.keys())].copy()
        if processed_features.empty:
            print("警告：没有任何匹配的心磁号，临床特征未更新")
            return feature_df

        # 10. 创建结果副本
        result_df = feature_df.copy()

        # 11. 处理要更新的列
        update_cols = [col for col in processed_features.columns if col in result_df.columns]

        # 12. 添加行索引列
        processed_features['__row_idx__'] = processed_features['心磁号'].map(mcg_idx_map)

        # 13. 初始化 verbose 用的集合和列表
        updated_mcg_ids = set()
        change_details_list = []

        # 14. 批量更新每列
        for col in update_cols:
            row_indices = processed_features['__row_idx__'].values.astype(int)
            new_values = processed_features[col].values

            # 获取旧值
            old_values = result_df.iloc[row_indices, result_df.columns.get_loc(col)].values

            # 检查是否真的要更新 (只在旧值为 NaN 或不同于新值时更新)
            mask = np.logical_or(pd.isna(old_values), old_values != new_values)

            # 批量更新
            result_df.iloc[row_indices[mask], result_df.columns.get_loc(col)] = new_values[mask]

            if mask.any():
                updated_mcg_ids.update(processed_features.loc[mask, '心磁号'])

                if verbose:
                    for idx in np.where(mask)[0]:
                        change_details_list.append(
                            f"  - 样本 MCG_ID: {processed_features.iloc[idx]['心磁号']}, 特征: {col}, 旧值: {old_values[idx]}, 新值: {new_values[idx]}"
                        )
        # 11. 如果 verbose 为 True，打印总结信息
        if verbose:
            print("\n--- 临床特征详细更新报告 ---")
            print(f"总共处理的来自Excel的样本数: {len(processed_features)}")
            print(f"在目标DataFrame中实际更新的样本数量: {len(updated_mcg_ids)}") # 使用集合大小
            if change_details_list:
                print("\n具体数值变动详情:")
                print(len(change_details_list))
                # for detail in change_details_list:
                #     print(detail)
            elif len(updated_mcg_ids) > 0 :
                 print("\n检测到样本更新，但未记录具体数值变动（可能是内部逻辑错误或所有值恰好相同）。")
            else:
                 print("\n没有检测到需要更新的数值变动。")
            print("--- 更新报告结束 ---")

        # 保存临床特征到Excel文件
        saveflag = False
        if saveflag:
            try:
                import os
                print("\n开始保存临床特征到Excel文件...")

                # 选择要保存的临床特征列
                clinic_cols = [col for col in result_df.columns if col.startswith('clinic_')]

                # 创建保存用的DataFrame
                save_df = pd.DataFrame()
                save_df['心磁号'] = mcg_ids_str  # 添加心磁号列

                # 使用mcg_idx_map进行映射，添加临床特征列
                for col in clinic_cols:
                    col_values = []
                    for mcg_id in mcg_ids_str:
                        idx = mcg_idx_map.get(mcg_id)
                        if idx is not None and idx < len(result_df):
                            col_values.append(result_df.iloc[idx][col])
                        else:
                            col_values.append(None)
                    save_df[col] = col_values

                # 将列名从英文映射回中文
                reverse_mapping = {v: k for k, v in column_mapping.items() if v in save_df.columns}
                save_df.rename(columns=reverse_mapping, inplace=True)

                # 生成输出文件名
                output_dir = os.path.dirname(excel_path) if os.path.dirname(excel_path) else '.'
                base_name = os.path.splitext(os.path.basename(excel_path))[0]
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                output_path = f"{output_dir}/{base_name}_clinical_features_{timestamp}.xlsx"

                # 保存到Excel
                save_df.to_excel(output_path, index=False)
                print(f"临床特征已成功保存到: {output_path}")
            except Exception as save_error:
                print(f"保存临床特征到Excel时发生错误: {save_error}")
                import traceback
                traceback.print_exc()

        print(f"\n临床数据刷新成功, 总耗时: {time.time() - start_time:.2f}秒")
        return result_df

    except FileNotFoundError:
        print(f"错误：无法找到 Excel 文件 '{excel_path}'。请检查路径是否正确。")
        return feature_df # 返回原始 df
    except ValueError as ve:
         print(f"处理数据时发生值错误: {ve}")
         import traceback
         traceback.print_exc()
         return feature_df
    except Exception as e:
        print(f"临床特征刷新过程中发生未预料的错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return feature_df # 发生错误时返回原始 df
    """
    优化版的刷新临床特征函数，增加了verbose参数用于打印详细更新信息。

    Args:
        feature_df: 特征DataFrame
        mcg_ids: 心磁号Series或列表
        excel_path: Excel文件路径
        sheet_name: sheet名称
        verbose (bool, optional): 是否打印详细的更新过程和变动。默认为 False。

    Returns:
        更新后的特征DataFrame
    """
    start_time = time.time()
    print("开始刷新临床特征...")

    try:
        # 1. 提前转换mcg_ids为字符串类型，避免循环中重复转换
        mcg_ids_str = pd.Series(mcg_ids).astype(str).tolist()
        mcg_id_set = set(mcg_ids_str)  # 使用集合加速查找

        # 2. 读取Excel文件，只读取需要的列以减少内存使用
        columns_to_read = ['心磁号']
        clinic_features_excel = [ # Excel中的列名
            '临床特征-身高', '临床特征-体重', '临床特征-性别', '临床特征-年龄',
            '临床特征-吸烟', '临床特征-饮酒', '临床特征-高血压', '临床特征-高脂血症',
            '临床特征-既往介入', '临床特征-所在医院', '临床特征-糖尿病', '临床特征-典型症状'
        ]
        # 实际需要的列是 '心磁号' + clinic_features_excel 中存在的列
        try:
            # 先尝试读取第一行获取实际存在的列名
            header_df = pd.read_excel(excel_path, sheet_name=sheet_name, nrows=0)
            actual_columns_to_read = ['心磁号'] + [col for col in clinic_features_excel if col in header_df.columns]
            print(f"实际读取的列: {actual_columns_to_read}")
            df_info = pd.read_excel(excel_path, sheet_name=sheet_name, usecols=actual_columns_to_read)
        except Exception as e:
            print(f"读取Excel时发生错误: {e}")
            print(f"请检查文件 '{excel_path}' 的 sheet '{sheet_name}' 是否存在，以及列名是否正确。")
            return feature_df # 返回原始df

        print(f"读取Excel文件: {excel_path}, Sheet: {sheet_name}")
        # df_info = pd.read_excel(excel_path, sheet_name=sheet_name, usecols=columns_to_read + clinic_features)
        print(f"Excel读取完成, 时间: {time.time() - start_time:.2f}秒")

        # 3. 预处理: 转换心磁号为字符串类型
        if '心磁号' not in df_info.columns:
            print("错误：Excel文件中未找到 '心磁号' 列。")
            return feature_df
        df_info['心磁号'] = df_info['心磁号'].astype(str)

        # 4. 筛选出包含于mcg_ids的记录，减少后续处理量
        df_info_filtered = df_info[df_info['心磁号'].isin(mcg_id_set)].copy() # 使用copy避免SettingWithCopyWarning
        print(f"筛选相关记录，从{len(df_info)}条减少到{len(df_info_filtered)}条")

        if len(df_info_filtered) == 0:
            print("没有找到匹配的心磁号记录")
            return feature_df

        # 5. 重命名列以匹配process_clinic_features的输入格式
        column_mapping = {
            '临床特征-身高': 'clinic_height',
            '临床特征-体重': 'clinic_weight',
            '临床特征-性别': 'clinic_gender',
            '临床特征-年龄': 'clinic_age',
            '临床特征-吸烟': 'clinic_smoking',
            '临床特征-饮酒': 'clinic_drinking',
            '临床特征-高血压': 'clinic_hypertension',
            '临床特征-高脂血症': 'clinic_hyperlipidemia',
            '临床特征-既往介入': 'clinic_intervention',
            '临床特征-所在医院': 'clinic_hospital',
            '临床特征-糖尿病': 'clinic_diabetes',
            '临床特征-典型症状': 'clinic_symp'
        }

        # 只重命名存在的列
        rename_dict = {k: v for k, v in column_mapping.items() if k in df_info_filtered.columns}
        df_info_filtered.rename(columns=rename_dict, inplace=True)

        # 添加 feature_df 中存在但 Excel 中可能缺失的 clinic_ 列，并用 NaN 填充
        # 以便 process_clinic_features_batch 能处理所有预期的 clinic_ 列
        expected_clinic_cols = set(column_mapping.values())
        for col in expected_clinic_cols:
             if col not in df_info_filtered.columns and col in feature_df.columns:
                 df_info_filtered[col] = np.nan # 添加缺失列


        # 6. 批量处理临床特征
        print("开始批量处理临床特征...")
        processed_features = process_clinic_features_batch(df_info_filtered)
        print(f"特征批量处理完成, 时间: {time.time() - start_time:.2f}秒")

        # 7. 创建用于更新的结果DataFrame
        result_df = feature_df.copy()

        # 8. 构建心磁号到索引的映射，加速查找
        # 假设 mcg_ids 的顺序与 feature_df.index 对应
        # 如果 feature_df 有自己的 '心磁号' 列作为标识符，用那个会更鲁棒
        # 例如: # mcg_idx_map = pd.Series(result_df.index, index=result_df['mcg_id_column_name']).to_dict()
        mcg_idx_map = {id_val: idx for idx, id_val in enumerate(mcg_ids_str)} # 假设 mcg_ids 对应 feature_df 的行

        # 9. 准备 verbose 输出所需变量
        updated_samples_count = 0
        updated_mcg_ids = set()
        change_details_list = []

        # 10. 迭代处理后的特征，更新 result_df 并记录变动 (如果verbose=True)
        feature_columns = [col for col in processed_features.columns
                           if col.startswith('clinic_') and col in result_df.columns] # 只更新 result_df 中也存在的 clinic_ 列

        print(f"开始更新 {len(processed_features)} 条记录到目标DataFrame...")
        for _, row in tqdm(processed_features.iterrows(), total=len(processed_features), desc="更新临床数据"):
            mcg_id = row['心磁号']
            if mcg_id in mcg_idx_map:
                result_idx = mcg_idx_map[mcg_id] # 获取在 result_df 中的行索引

                # 检查 result_idx 是否有效
                if result_idx >= len(result_df):
                     print(f"警告：心磁号 {mcg_id} 映射的索引 {result_idx} 超出范围，跳过。")
                     continue

                sample_had_change = False # 标记此样本是否有任何特征被更改

                for col in feature_columns:
                    if col in row and pd.notna(row[col]): # 确保新值有效
                        new_value = row[col]
                        # original_value = result_df.loc[result_idx, col]
                        original_value = result_df.iloc[result_idx, result_df.columns.get_loc(col)]

                        # 检查值是否真的改变了 (处理 NaN 和类型差异)
                        try:
                            # 尝试安全比较，转换类型可能导致错误，例如比较str和int
                            are_equal = False
                            if pd.isna(original_value) and pd.isna(new_value):
                                are_equal = True
                            elif pd.notna(original_value) and pd.notna(new_value):
                                # 如果类型不同，尝试转换成相同类型比较 (比如都转str，或都尝试转float)
                                try:
                                    if isinstance(original_value, (int, float)) and isinstance(new_value, (int, float)):
                                        are_equal = (original_value == new_value)
                                    else: # 对于混合类型，转字符串比较可能更安全
                                        are_equal = (str(original_value) == str(new_value))
                                except: # 转换或比较失败，则认为不相等
                                     are_equal = False
                            else: # 一个是NaN另一个不是，肯定不相等
                                are_equal = False

                            # if not (original_value == new_value or (pd.isna(original_value) and pd.isna(new_value))):
                            if not are_equal:
                                if verbose:
                                    # 记录变动详情
                                    change_details_list.append(
                                        f"  - 样本 MCG_ID: {mcg_id} (索引: {result_idx}), 特征: {col}, 旧值: {original_value}, 新值: {new_value}"
                                    )
                                    updated_mcg_ids.add(mcg_id) # 将此样本加入已更新集合
                                    sample_had_change = True

                                # 执行更新
                                result_df.loc[result_idx, col] = new_value

                        except Exception as compare_err:
                             print(f"\n警告：比较或更新值时出错 - MCG_ID: {mcg_id}, 列: {col}, 旧值: {original_value}, 新值: {new_value}, 错误: {compare_err}")
                             # 仍然尝试更新，但标记可能存在问题
                             try:
                                 result_df.loc[result_idx, col] = new_value
                                 if verbose:
                                     # 即使比较出错也记录更新尝试
                                     change_details_list.append(
                                         f"  - 样本 MCG_ID: {mcg_id} (索引: {result_idx}), 特征: {col}, 旧值: {original_value}, 新值: {new_value} (更新时比较异常)"
                                     )
                                     updated_mcg_ids.add(mcg_id)
                                     sample_had_change = True
                             except Exception as update_err:
                                  print(f"  更新失败: {update_err}")


                    # (可选) 如果你想处理 Excel 中没有提供但需要设为默认值(-1)的情况
                    # elif col not in row or pd.isna(row[col]):
                    #     # 这里可以添加逻辑，例如将 result_df 中对应的列设为 -1
                    #     pass

        # 11. 如果 verbose 为 True，打印总结信息
        if verbose:
            print("\n--- 临床特征详细更新报告 ---")
            print(f"总共处理的来自Excel的样本数: {len(processed_features)}")
            print(f"在目标DataFrame中实际更新的样本数量: {len(updated_mcg_ids)}") # 使用集合大小
            if change_details_list:
                print("\n具体数值变动详情:")
                print(len(change_details_list))
                # for detail in change_details_list:
                #     print(detail)
            elif len(updated_mcg_ids) > 0 :
                 print("\n检测到样本更新，但未记录具体数值变动（可能是内部逻辑错误或所有值恰好相同）。")
            else:
                 print("\n没有检测到需要更新的数值变动。")
            print("--- 更新报告结束 ---")

        # 保存临床特征到Excel文件
        saveflag = False
        if saveflag:
            try:
                import os
                print("\n开始保存临床特征到Excel文件...")

                # 选择要保存的临床特征列
                clinic_cols = [col for col in result_df.columns if col.startswith('clinic_')]

                # 创建保存用的DataFrame
                save_df = pd.DataFrame()
                save_df['心磁号'] = mcg_ids_str  # 添加心磁号列

                # 使用mcg_idx_map进行映射，添加临床特征列
                for col in clinic_cols:
                    col_values = []
                    for mcg_id in mcg_ids_str:
                        idx = mcg_idx_map.get(mcg_id)
                        if idx is not None and idx < len(result_df):
                            col_values.append(result_df.iloc[idx][col])
                        else:
                            col_values.append(None)
                    save_df[col] = col_values

                # 将列名从英文映射回中文
                reverse_mapping = {v: k for k, v in column_mapping.items() if v in save_df.columns}
                save_df.rename(columns=reverse_mapping, inplace=True)

                # 生成输出文件名
                output_dir = os.path.dirname(excel_path) if os.path.dirname(excel_path) else '.'
                base_name = os.path.splitext(os.path.basename(excel_path))[0]
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                output_path = f"{output_dir}/{base_name}_clinical_features_{timestamp}.xlsx"

                # 保存到Excel
                save_df.to_excel(output_path, index=False)
                print(f"临床特征已成功保存到: {output_path}")
            except Exception as save_error:
                print(f"保存临床特征到Excel时发生错误: {save_error}")
                import traceback
                traceback.print_exc()

        print(f"\n临床数据刷新成功, 总耗时: {time.time() - start_time:.2f}秒")
        return result_df

    except FileNotFoundError:
        print(f"错误：无法找到 Excel 文件 '{excel_path}'。请检查路径是否正确。")
        return feature_df # 返回原始 df
    except ValueError as ve:
         print(f"处理数据时发生值错误: {ve}")
         import traceback
         traceback.print_exc()
         return feature_df
    except Exception as e:
        print(f"临床特征刷新过程中发生未预料的错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return feature_df # 发生错误时返回原始 df

def append_features(features_df, append_cur=True, append_ci=True, features_folder="./files/saved_features/"):
            """
            加载特征pkl文件，并追加到self.features_df。 给model trainer 用的
            """
            if append_cur:
                # 加载电流源特征
                cur_feature_path = os.path.join(features_folder, "cur_feature_df_3105.pkl")
                try:
                    cur_feature_df = pd.read_pickle(cur_feature_path)
                except FileNotFoundError:
                    print(f"文件 {cur_feature_path} 未找到。")
                    return
                except Exception as e:
                    print(f"加载文件 {cur_feature_path} 时发生错误: {e}")
                    return
                features_df = pd.merge(features_df, cur_feature_df, on="mcg_file", how="left")  # 合并电流源特征
                # print(cur_feature_df)
            if append_ci:
                # 加载参数特征
                ci_feature_path = os.path.join(features_folder, "ci_feature_df_3105.pkl")
                try:
                    ci_feature_df = pd.read_pickle(ci_feature_path)
                except FileNotFoundError:
                    print(f"文件 {ci_feature_path} 未找到。")
                    return
                except Exception as e:
                    print(f"加载文件 {ci_feature_path} 时发生错误: {e}")
                    return

                # 按mcg_file合并特征到self.features_df
                features_df = pd.merge(features_df, ci_feature_df, on="mcg_file", how="left")  # 合并参数特征
                # print(ci_feature_df)

            print("加载额外特征成功")

            return features_df


class ModelEvaluator:
    def __init__(self):
        self.feature_list = None

    def predict_new_data_old(self, excel_path, feature_dir, version=None, sheet_name='测试集'):
        """
        使用保存的模型对新数据进行预测
        Args:
            excel_path: str, 测试数据的Excel文件路径
            feature_dir: str, 特征文件所在目录
            version: 要使用的模型版本，如果为None则使用最新版本
        Returns:
            tuple: (预测概率, 预测类别, 使用的版本号, 预测结果DataFrame)
        """
        trainer = ModelTrainer()

        # 如果已有特征列表则直接使用,否则在预测时生成
        trainer.features_list = self.feature_list or None

        # 执行预测
        prediction_results = trainer.load_model_and_predict(
            excel_path=excel_path,
            feature_dir=feature_dir,
            version=version,
            sheet_name=sheet_name
        )

        # 缓存特征列表供后续使用
        if not self.feature_list:
            self.feature_list = trainer.features_list

        return prediction_results
    def predict_new_data(self, excel_path, feature_dir, version=None, sheet_name='测试集',
                         generate_shap_explanation=True, shap_top_n_features=3): # Pass SHAP params
        """
        使用保存的模型对新数据进行预测
        Args:
            excel_path: str, 测试数据的Excel文件路径
            feature_dir: str, 特征文件所在目录
            version: 要使用的模型版本，如果为None则使用最新版本
            sheet_name: Excel工作表名
            generate_shap_explanation: bool, 是否为XGBoost模型生成SHAP解释
            shap_top_n_features: int, SHAP解释中显示的顶级特征数量
        Returns:
            tuple: (预测概率, 预测类别, 使用的版本号, 预测结果DataFrame)
        """
        # trainer = ModelTrainer() # Assuming ModelTrainerDemo is the intended class
        trainer = ModelTrainer() # Use the class with SHAP modifications

        # If ModelEvaluator has a cached feature_list (from a previous run perhaps)
        # it could be passed to trainer. This seems to be the intent of self.feature_list
        # However, ModelTrainerDemo's load_model_and_predict loads features from the saved model version.
        # The line `trainer.features_list = self.feature_list or None` seems to imply
        # that ModelTrainer might generate features if not provided.
        # For now, let's assume trainer.load_model_and_predict correctly loads its required features.
        # The self.feature_list caching in ModelEvaluator might be for a different purpose or
        # a remnant of a different workflow. The current load_model_and_predict loads features
        # based on the *saved model's* feature list.

        prediction_results = trainer.load_model_and_predict(
            excel_path=excel_path,
            feature_dir=feature_dir,
            version=version,
            sheet_name=sheet_name,
            generate_shap_explanation=generate_shap_explanation, # Pass through
            shap_top_n_features=shap_top_n_features         # Pass through
        )

        # Caching the feature list used by the trainer for this prediction
        # `trainer.selected_features` would be more accurate if it's set by load_model_and_predict
        # The current ModelTrainerDemo sets `selected_features` variable locally in load_model_and_predict
        # If you want to cache it, `trainer` should expose it as an attribute after loading.
        # For now, let's assume trainer.features_list is what you intend to cache.
        # However, ModelTrainerDemo does not set trainer.features_list globally.
        # Let's assume `selected_features` from the loaded model is what matters.
        # This part of caching needs clarification on what `trainer.features_list` refers to
        # in the context of ModelTrainerDemo.

        # If the goal is to cache the `selected_features` list *used by the loaded model*:
        loaded_model_version = prediction_results[2] # version
        if loaded_model_version and loaded_model_version in trainer.version_info['versions']:
            version_dir = trainer._get_version_dir(loaded_model_version)
            features_path = os.path.join(version_dir, 'features.pkl')
            if os.path.exists(features_path):
                with open(features_path, 'rb') as f:
                    self.feature_list = pickle.load(f) # Cache features from the loaded model
            else: # Fallback if features.pkl isn't there for some reason for that version
                 model_type = trainer.version_info['versions'][loaded_model_version].get('model_type')
                 if model_type == 'DoubleModel':
                     # For double model, we need to decide which feature list to cache, or both
                     # This part is tricky as `selected_features` in load_model_and_predict becomes ambiguous
                     # Let's assume for now we cache the features for the first stage, or the combined set
                     model_path = os.path.join(version_dir, 'model.pkl')
                     if os.path.exists(model_path):
                         with open(model_path, 'rb') as f_model:
                             loaded_model_obj = pickle.load(f_model)
                             if hasattr(loaded_model_obj, 'selected_features1'):
                                self.feature_list = loaded_model_obj.selected_features1
                 # else: self.feature_list might remain None or previous value

        return prediction_results

    def save_predictions(self, predictions_df, output_path):
        predictions_df.to_excel(output_path, index=False)
        print(f"\n预测结果已保存到: {output_path}")
    def save_predictions(self, predictions_df, output_path):
        """
        保存预测结果到Excel文件
        Args:
            predictions_df: DataFrame, 包含预测结果的DataFrame
            output_path: str, 输出文件路径
        """
        predictions_df.to_excel(output_path, index=False)
        print(f"\n预测结果已保存到: {output_path}")

class ModelTrainer:
    def __init__(self, model_dir="./files/saved_models/"):
        """
        初始化模型训练器
        Args:
            model_dir: 模型根目录
        """
        self.model_dir = model_dir
        self.version_info_file = os.path.join(model_dir, 'version_info.json')
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)
        self.features_list = None
        # 初始化或加载版本信息
        self._init_version_info()

    def _init_version_info(self):
        """初始化或加载版本信息文件"""
        if os.path.exists(self.version_info_file):
            with open(self.version_info_file, 'r', encoding='utf-8') as f:
                self.version_info = json.load(f)
        else:
            self.version_info = {
                'versions': {},
                'latest_version': None
            }
            self._save_version_info()

    def _save_version_info(self):
        """保存版本信息到文件"""

        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {str(key): convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, tuple):
                # 如果是版本号元组，转换为字符串
                if len(obj) == 1 and isinstance(obj[0], str):
                    return obj[0]
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj

        # 转换数据
        converted_info = convert_numpy_types(self.version_info)
        with open(self.version_info_file, 'w', encoding='utf-8') as f:
            json.dump(converted_info, f, ensure_ascii=False, indent=2)

    def _get_version_dir(self, version):
        """获取指定版本的目录路径"""
        return os.path.join(self.model_dir, str(version))

    def _create_version_metadata(self, version, model_name, description=None):
        """创建版本元数据"""
        return {
            'version': version,
            'model_name': model_name,
            'description': description,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'feature_count': len(self.selected_features) if hasattr(self, 'selected_features') else None,
            'model_type': str(type(self.best_model).__name__) if hasattr(self, 'best_model') else None
        }

    def train_best_model_old(self, evaluation_results, X_selected_scaled, y, scaler, selected_features,
                             version=None, description=None, aug=True):
        """
        使用全部数据训练最佳模型并保存到指定版本
        Args:
            evaluation_results: evaluate_models_binary的返回结果
            X_selected_scaled: 已经缩放的特征数据
            y: 目标变量
            scaler: StandardScaler实例
            selected_features: 选择的特征列表
            version: 版本标识（如果为None，则自动生成）
            description: 版本描述
        Returns:
            tuple: (best_model, version)
        """
        # 如果未指定版本，则自动生成版本号（年月日_序号）
        if version is None:
            date_str = datetime.now().strftime('%Y%m%d')
            existing_versions = [v for v in self.version_info['versions'].keys()
                               if v.startswith(date_str)]
            version = f"{date_str}_{len(existing_versions) + 1}"

        version_dir = self._get_version_dir(version)
        if not os.path.exists(version_dir):
            os.makedirs(version_dir)

        print(f"\n=== 开始训练模型 版本:{version} ===")

        # 获取最佳模型
        best_model_name = evaluation_results['best_model_name']
        best_model = evaluation_results['best_model']
        self.best_model = best_model  # 保存用于元数据
        self.selected_features = selected_features  # 保存用于元数据
        X_selected_scaled = scaler.transform(X_selected_scaled)
        print(f"使用模型 {best_model_name} 在全量数据上训练")

        best_model = xgb.XGBClassifier(
            learning_rate=0.05,
            n_estimators=600,
            max_depth=6,
            subsample=0.8,
            colsample_bytree=0.8,
            objective='binary:logistic',
            eval_metric=['auc', 'logloss'],
            early_stopping_rounds=300,
        )

        # 数据增强 ------------------------------------------------------------------
        augmentor = StratifiedBalancedDataAugmentor(
            balance_method='smote',  # 可选 adasyn 和 borderline_smote
            noise_method='random_perturbation',
            verbose=True
        )
        # 检查类别分布
        print("\n增强前类别分布:")
        print(pd.Series(y).value_counts(normalize=True))
        # stats = augmentor.get_stratified_stats(X_selected_scaled, y, selected_features,
        #                                        center_col='clinic_hospital')
        # print("Initial distribution by center:", stats)
        # 应用增强
        if aug:
            print(f"应用数据增强...")
            # 依照医院进行平衡
            X_selected_scaled, y = augmentor.balance_by_center(
                X_selected_scaled, y,
                center_col='clinic_hospital',
                selected_features=selected_features,
                target_ratio=[0, 0.6, 0.7, 0.5, 0.7],  # 4个值是固定的，分别对应4个医院，但是具体数值在0-1之间，表示比例，可调，基本0.5以上生效
                noise_ratio=0.0  # 希望调参时可以考虑不同取值
            )
            # 依照全局进行平衡
            # X_selected_scaled, y = augmentor.balance_and_augment(
            #     X_selected_scaled, y,
            #     target_ratio=0.9,  # 全局的平衡性目标 仅一个值 0-1之间 可调参
            #     noise_ratio=0
            # )
            # 检查类别分布
            print("\n增强后类别分布:")
            print(pd.Series(y).value_counts(normalize=True))

        # -----------------------------------------------------------------------
        # 早停策略
        # 分出较小比例的验证集(如10%)
        from sklearn.model_selection import train_test_split
        X_train, X_eval, y_train, y_eval = train_test_split(
            X_selected_scaled, y,
            test_size=0.2,  # 只分出10%
            random_state=42,
            stratify=y
        )

        # # 使用较小的验证集进行早停
        eval_set = [(X_train, y_train), (X_eval, y_eval)]
        best_model.fit(
            X_train, y_train,
            eval_set=eval_set,
            verbose=100
        )

        # from sklearn.svm import SVC # 只在best_model为svm时进行
        # print("使用SVM进行训练")
        # best_model =SVC(
        #     kernel='rbf',
        #     C=1.0,
        #     probability=True,
        #     random_state=42
        # )

        # # 防过拟合测试 ----------------------------------------------------------------
        #
        # # 训练模型
        # best_model.fit(X_selected_scaled, y)

        # 在训练集上评估模型性能
        print("\n=== 训练集性能评估 ===")
        train_probs = best_model.predict_proba(X_selected_scaled)
        train_preds = (train_probs[:, 1] > 0.5).astype(int)

        # 计算训练集上的各项指标
        from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                                     f1_score, roc_auc_score, confusion_matrix)

        train_metrics = {
            'accuracy': accuracy_score(y, train_preds),
            'precision': precision_score(y, train_preds),
            'recall': recall_score(y, train_preds),
            'specificity': recall_score(y, train_preds, pos_label=0),
            'f1': f1_score(y, train_preds),
            'auc': roc_auc_score(y, train_probs[:, 1])
        }

        print("\n训练集评估指标:")
        print(f"AUC: {train_metrics['auc']:.4f}")
        print(f"Accuracy: {train_metrics['accuracy']:.4f}")
        print(f"Precision: {train_metrics['precision']:.4f}")
        print(f"Recall (Sensitivity): {train_metrics['recall']:.4f}")
        print(f"Specificity: {train_metrics['specificity']:.4f}")
        print(f"F1 Score: {train_metrics['f1']:.4f}")

        # 打印混淆矩阵
        conf_matrix = confusion_matrix(y, train_preds)
        print("\n混淆矩阵:")
        print("          预测        Negative  Positive")
        print(f"实际 Negative  {conf_matrix[0, 0]:8d} {conf_matrix[0, 1]:8d}")
        print(f"实际 Positive  {conf_matrix[1, 0]:8d} {conf_matrix[1, 1]:8d}")

        # 分析错误预测的样本数量和比例
        error_mask = train_preds != y
        error_count = np.sum(error_mask)
        print(f"\n错误预测样本数: {error_count}")
        print(f"错误率: {error_count / len(y):.4f}")

        # 如果模型支持特征重要性，则打印
        if hasattr(best_model, 'feature_importances_'):
            print("\n特征重要性（前20个）:")
            feature_importance = pd.DataFrame({
                'feature': selected_features,
                'importance': best_model.feature_importances_
            }).sort_values('importance', ascending=False)
            print(feature_importance.head(20))

        # 保存模型文件
        files_to_save = {
            'model.pkl': best_model,
            'scaler.pkl': scaler,
            'features.pkl': selected_features
        }

        for filename, obj in files_to_save.items():
            filepath = os.path.join(version_dir, filename)
            with open(filepath, 'wb') as f:
                pickle.dump(obj, f)

        # 创建包含训练指标的元数据
        metadata = self._create_version_metadata(version, best_model_name, description)
        metadata.update({
            'train_metrics': train_metrics,
            'feature_count': len(selected_features),
            'sample_count': len(y),
            'class_distribution': pd.Series(y).value_counts().to_dict(),
            'error_rate': float(error_count / len(y))
        })

        # 更新版本信息
        self.version_info['versions'][version] = metadata
        self.version_info['latest_version'] = version
        self._save_version_info()

        # 保存版本元数据到版本目录
        metadata_path = os.path.join(version_dir, 'metadata.json')

        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        print(f"\n模型和配置已保存到版本目录: {version_dir}")
        print(f"版本信息:")
        print(f"- 版本: {version}")
        print(f"- 模型: {best_model_name}")
        print(f"- 创建时间: {metadata['created_at']}")
        if description:
            print(f"- 描述: {description}")

        return best_model, version

    def load_model_and_predict(self, excel_path, feature_dir, version=None, sheet_name='测试集',
                               generate_shap_explanation=True, shap_top_n_features=3):
        """
        加载指定版本（或最新版本）的模型并进行预测，支持双层模型
        Args:
            excel_path: str, 测试数据的Excel文件路径
            feature_dir: str, 特征文件所在目录
            version: 指定要加载的版本，如果为None则使用最新版本
            generate_shap_explanation: bool, 是否为XGBoost模型生成SHAP解释
            shap_top_n_features: int, SHAP解释中显示的顶级特征数量
        Returns:
            tuple: (预测概率, 预测类别, 使用的版本号, DataFrame带预测结果和SHAP解释)
        """
        # 加载版本
        if version is None:
            version = self.version_info.get('latest_version')
            if version is None:
                raise ValueError("没有找到任何可用的模型版本")

        if version not in self.version_info['versions']:
            raise ValueError(f"版本 {version} 不存在")

        version_dir = self._get_version_dir(version)

        # 检查模型类型
        is_double_model = False
        model_type_path = os.path.join(version_dir, 'model_type.txt')
        if os.path.exists(model_type_path):
            with open(model_type_path, 'r') as f:
                model_type = f.read().strip()
                is_double_model = (model_type == 'DoubleModel')
        else:
            # 如果没有模型类型文件，尝试从版本信息判断
            is_double_model = self.version_info['versions'][version].get('model_type') == 'DoubleModel'

        try:
            # 加载模型和配置
            model_path = os.path.join(version_dir, 'model.pkl')
            with open(model_path, 'rb') as f:
                model = pickle.load(f)

            print(f"已加载版本 {version} 的模型: {model.__class__.__name__}")

            # 加载缩放器和特征列表
            if is_double_model:
                # 双层模型获取所需的一切信息
                selected_features1 = model.selected_features1
                selected_features2 = model.selected_features2
                scaler1 = model.scaler1
                scaler2 = model.scaler2
                threshold1 = model.threshold1 if hasattr(model, 'threshold1') else 0.5
                threshold2 = model.threshold2 if hasattr(model, 'threshold2') else 0.5

                print(f"检测到双层模型，阈值设置：threshold1={threshold1}, threshold2={threshold2}")
                print(f"一级模型特征数: {len(selected_features1)}")
                print(f"二级模型特征数: {len(selected_features2)}")

                # 获取两个模型所需的所有特征（合并去重）
                all_needed_features = list(set(selected_features1).union(set(selected_features2)))
                print(f"合并后总特征数: {len(all_needed_features)}")

                # 为了兼容原加载方式，使用一级模型的特征列表
                selected_features = selected_features1
                scaler = scaler1
            else:
                # 普通模型
                scaler_path = os.path.join(version_dir, 'scaler.pkl')
                with open(scaler_path, 'rb') as f:
                    scaler = pickle.load(f)

                features_path = os.path.join(version_dir, 'features.pkl')
                with open(features_path, 'rb') as f:
                    selected_features = pickle.load(f)
                all_needed_features = selected_features  # 普通模型只需要一组特征

                print(f"普通模型特征数: {len(selected_features)}")

        except FileNotFoundError as e:
            raise Exception(f"版本 {version} 的模型文件不完整") from e

        print("\n=== 开始加载测试数据 ===")
        # 加载Excel数据
        print("Loading Excel data...")
        df = pd.read_excel(excel_path, sheet_name=sheet_name)

        # 对测试数据的标签进行条件处理 --------------------------------
        df = condition_policy(df)
        # --------------------------------------------------------
        test_data = df[['心磁号', '造影结论']].copy()

        feature_dir = Path(feature_dir)
        # 使用多进程并行加载特征
        print("Loading features...")
        features_df = get_features(feature_dir, test_data['心磁号'])
        print(f"抽取本批次使用 {len(features_df)} 个心磁文件的特征")


        if Condition_config.USE_EXTRA_FEATURE:
            features_df = append_features(features_df)
        if Condition_config.REFRESH_CLINIC_FEATURE:
            features_df = refresh_clinic_features(features_df,test_data['心磁号'], "D:\std data\\20250520版本\新增数据临床信息.xlsx","测试用")
        if Condition_config.GEN_CLINIC_EXTRA_FEATURE:
            # 计算BMI
            if 'clinic_height' in features_df.columns and 'clinic_weight' in features_df.columns:
                # 将空值替换为-1
                height = features_df['clinic_height'].fillna(-1)
                weight = features_df['clinic_weight'].fillna(-1)

                # 计算BMI，对于任意输入参数为-1的情况,BMI也设为-1
                bmi = np.where(
                    (height > 0) & (weight > 0),
                    weight / ((height / 100) ** 2),  # 身高单位转换为米
                    -1
                )
                features_df['clinic_bmi'] = bmi
                print("已添加clinic_bmi列")
            # 动态生成clinic_hospital的独热编码
            if 'clinic_hospital' in features_df.columns:
                # 生成独热编码（确保前缀与训练时一致）
                onehot_cols = pd.get_dummies(
                    features_df['clinic_hospital'],
                    prefix='clinic_hospital_onehot'
                )

                # 强制对齐训练时的独热编码列（处理未知类别）
                # 使用合并后的特征列表，确保所有需要的独热编码列都被生成
                expected_onehot_columns = [
                    col for col in all_needed_features
                    if col.startswith('clinic_hospital_onehot_')
                ]

                # 对齐列名，未知类别填充0
                onehot_cols = onehot_cols.reindex(
                    columns=expected_onehot_columns,
                    fill_value=0
                )

                # 合并到特征矩阵
                features_df = pd.concat([features_df, onehot_cols], axis=1)
            features_df = enrich_clinical_features(features_df)
        # 更新特征列名
        feature_names = [
            col for col in features_df.columns
            if col != 'mcg_file' and not col.startswith('label')
        ]

        print(f"Loaded {len(feature_names)} features for {len(test_data)} samples")

        # 检查并处理特征 - 使用all_needed_features确保所有需要的特征都存在
        if not all(feature in feature_names for feature in all_needed_features):
            missing_features = [f for f in all_needed_features if f not in feature_names]
            raise ValueError(f"测试数据缺少以下特征: {missing_features}")

        # 预测
        print("\n=== 开始预测 ===")
        shap_explanation_list = ["N/A"] * len(features_df)  # Default for non-XGB or if SHAP disabled

        if is_double_model:
            # 创建自定义双层模型预测方法，直接访问模型内部组件避免特征不匹配问题
            def custom_double_model_predict(model, features_df):
                """自定义双层模型预测方法，处理两级模型不同特征的情况"""
                # 处理一级模型特征
                X_selected1 = features_df[model.selected_features1]
                X_selected_clean1 = X_selected1.fillna(0)

                # 处理clinic列
                clinic_columns1 = [col for col in X_selected1.columns if 'clinic' in col.lower()]
                for col in clinic_columns1:
                    X_selected_clean1.loc[X_selected1[col].isna(), col] = -1

                # 应用一级模型缩放
                X_scaled1 = model.scaler1.transform(X_selected_clean1)

                # 一级模型预测概率
                level1_proba = model.model1.predict_proba(X_scaled1)

                # 使用阈值筛选
                first_level_positives = level1_proba[:, 1] > model.threshold1
                final_predictions = np.zeros(X_scaled1.shape[0], dtype=int)

                # 如果有任何样本通过一级筛选
                if np.any(first_level_positives):
                    # 处理二级模型特征
                    X_selected2 = features_df[model.selected_features2]
                    X_selected_clean2 = X_selected2.fillna(0)

                    # 处理clinic列
                    clinic_columns2 = [col for col in X_selected2.columns if 'clinic' in col.lower()]
                    for col in clinic_columns2:
                        X_selected_clean2.loc[X_selected2[col].isna(), col] = -1

                    # 应用二级模型缩放
                    X_scaled2 = model.scaler2.transform(X_selected_clean2)

                    # 对一级阳性样本进行二级预测
                    level2_proba = model.model2.predict_proba(X_scaled2[first_level_positives])
                    second_level_predictions = (level2_proba[:, 1] > model.threshold2).astype(int)
                    # 打印一级阳性样本中在二级预测为阴的数量
                    print(f"(纯阴)一级阴性样本数量: {np.sum(first_level_positives == 0)}")
                    print(f"(半阴)一级阳性样本中在二级预测为阴的数量: {np.sum(second_level_predictions == 0)}")
                    # 更新最终预测结果
                    final_predictions[first_level_positives] = second_level_predictions

                # 构建最终概率矩阵 [[p(阴性), p(阳性)], ...]
                final_proba = level1_proba.copy()
                if np.any(first_level_positives):
                    level2_proba = model.model2.predict_proba(X_scaled2[first_level_positives])
                    final_proba[first_level_positives] = level2_proba

                return final_proba, final_predictions

            # 使用自定义方法进行预测
            print(f"使用自定义双层模型预测方法")
            probabilities, predictions = custom_double_model_predict(model, features_df)
        else:
            # 普通模型预测
            # 选择特征并填充缺失值
            X_selected = features_df[selected_features]


            # 针对包含'clinic'的列，将其缺失值替换为-1
            clinic_columns = [col for col in X_selected.columns if 'clinic' in col.lower()]
            for col in clinic_columns:
                print(f"处理 '{col}'...")
                X_selected.loc[X_selected[col].isna(), col] = -1
            X_selected_clean = X_selected.fillna(0)

            # 应用特征缩放
            X_scaled = scaler.transform(X_selected_clean)

            # # 将保存X_scaled为xlsx文件，列名与selected_features一致,并将test_data的心磁号作为第一列
            X_scaled_df = pd.DataFrame(X_scaled, columns=selected_features)
            X_scaled_df.insert(0, 'mcg_file', features_df['mcg_file'].values)
            scaled_features_path =  './X_scaled.xlsx'
            X_scaled_df.to_excel(scaled_features_path, index=False)


            #
            # 普通模型预测
            probabilities = model.predict_proba(X_scaled)
            predictions = (probabilities[:, 1] > 0.5).astype(int)

            # --- SHAP EXPLANATION FOR XGBOOST MODIFIED ---

            # Initialize lists for the four new columns with defaults.
            # These lists will be populated or updated based on conditions.
            num_samples_for_shap_cols = X_scaled.shape[0]  # Number of samples we are predicting for

            # Default messages for different scenarios
            default_msg_shap_disabled = "N/A (SHAP Disabled)"
            default_msg_non_xgb = "N/A (Non-XGB)"
            default_msg_shap_error = "N/A (SHAP Error)"
            default_msg_no_contrib = "None"  # When no features meet criteria for a category

            clinic_positive_support_list = [default_msg_shap_disabled] * num_samples_for_shap_cols
            clinic_negative_support_list = [default_msg_shap_disabled] * num_samples_for_shap_cols
            mcg_positive_support_list = [default_msg_shap_disabled] * num_samples_for_shap_cols
            mcg_negative_support_list = [default_msg_shap_disabled] * num_samples_for_shap_cols

            if generate_shap_explanation:
                if isinstance(model, xgb.XGBClassifier):
                    print(
                        f"\n--- Generating detailed SHAP explanations for XGBoost model (top {shap_top_n_features} features per category) ---")
                    try:
                        explainer = shap.TreeExplainer(model)
                        shap_values_calculated = explainer.shap_values(X_scaled)

                        # Determine SHAP values for the positive class
                        if isinstance(shap_values_calculated, list) and len(shap_values_calculated) == 2:
                            # Common for sklearn interface, use SHAP for positive class (class 1)
                            shap_values_for_explanation = shap_values_calculated[1]
                        else:
                            # Assumed to be for the positive class already, or model output is single value
                            shap_values_for_explanation = shap_values_calculated

                        # Ensure shap_values_for_explanation is 2D: (n_samples, n_features)
                        if shap_values_for_explanation.ndim == 1:
                            if X_scaled.shape[1] == 1:  # Single feature case
                                shap_values_for_explanation = shap_values_for_explanation.reshape(-1, 1)
                            elif shap_values_for_explanation.shape[0] == X_scaled.shape[0] * X_scaled.shape[1]:
                                # If it's a flattened array of (n_samples * n_features)
                                shap_values_for_explanation = shap_values_for_explanation.reshape(X_scaled.shape[0],
                                                                                                  X_scaled.shape[1])
                            else:
                                # Unexpected 1D array, cannot reliably reshape to (n_samples, n_features)
                                raise ValueError(
                                    f"Unexpected SHAP values dimension: 1D array of shape {shap_values_for_explanation.shape} for multi-feature data.")

                        for i in range(shap_values_for_explanation.shape[0]):  # For each sample
                            sample_shaps = shap_values_for_explanation[i, :]

                            # Defaults for the current sample if issues arise
                            current_clinic_pos_str = default_msg_shap_error
                            current_clinic_neg_str = default_msg_shap_error
                            current_mcg_pos_str = default_msg_shap_error
                            current_mcg_neg_str = default_msg_shap_error

                            if len(selected_features) == 0:
                                error_msg = "Error: No features selected"
                                current_clinic_pos_str, current_clinic_neg_str, current_mcg_pos_str, current_mcg_neg_str = [
                                                                                                                               error_msg] * 4
                            elif len(sample_shaps) != len(selected_features):
                                print(
                                    f"Warning: SHAP values dimension ({len(sample_shaps)}) mismatch with selected features ({len(selected_features)}). Skipping SHAP for sample {i}.")
                                mismatch_msg = "SHAP dim mismatch"
                                current_clinic_pos_str, current_clinic_neg_str, current_mcg_pos_str, current_mcg_neg_str = [
                                                                                                                               mismatch_msg] * 4
                            else:
                                # Process SHAP values for this sample
                                feature_shap_pairs = []
                                for j, feature_name in enumerate(selected_features):
                                    feature_shap_pairs.append((feature_name, sample_shaps[j]))

                                clinic_features_shaps = []
                                mcg_features_shaps = []
                                for name, shap_val in feature_shap_pairs:
                                    if name.startswith("clinic_") :
                                        # 如果名字里有hospital自动pass,暂不统计
                                        if "hospital" in name:
                                            continue
                                        clinic_features_shaps.append((name, shap_val))
                                    else:
                                        mcg_features_shaps.append((name, shap_val))

                                # Helper function to get top N features and format them
                                def get_formatted_top_n(features_shaps_local, positive_support_local, top_n_local):
                                    if positive_support_local:
                                        # Filter for positive SHAP values, sort descending (highest positive first)
                                        filtered = sorted([fs for fs in features_shaps_local if fs[1] > 1e-6],
                                                          key=lambda x: x[1],
                                                          reverse=True)  # Use a small epsilon for float comparison
                                    else:
                                        # Filter for negative SHAP values, sort ascending (most negative first)
                                        filtered = sorted([fs for fs in features_shaps_local if fs[1] < -1e-6],
                                                          key=lambda x: x[1], reverse=False)  # Use a small epsilon

                                    top_items = filtered[:top_n_local]
                                    return "; ".join([f"{name}: {val:.3f}" for name, val in
                                                      top_items]) if top_items else default_msg_no_contrib

                                current_clinic_pos_str = get_formatted_top_n(clinic_features_shaps, positive_support_local=True,
                                                                             top_n_local=shap_top_n_features)
                                current_clinic_neg_str = get_formatted_top_n(clinic_features_shaps, positive_support_local=False,
                                                                             top_n_local=shap_top_n_features)
                                current_mcg_pos_str = get_formatted_top_n(mcg_features_shaps, positive_support_local=True,
                                                                          top_n_local=shap_top_n_features)
                                current_mcg_neg_str = get_formatted_top_n(mcg_features_shaps, positive_support_local=False,
                                                                          top_n_local=shap_top_n_features)

                            # Update the main lists for the current sample i
                            clinic_positive_support_list[i] = current_clinic_pos_str
                            clinic_negative_support_list[i] = current_clinic_neg_str
                            mcg_positive_support_list[i] = current_mcg_pos_str
                            mcg_negative_support_list[i] = current_mcg_neg_str

                        print("Detailed SHAP explanations generated.")

                    except Exception as e:
                        print(f"Error generating detailed SHAP explanations: {e}")
                        # If a major error occurs during SHAP processing, fill all with a general error message
                        error_msg_for_all = f"{default_msg_shap_error}: {type(e).__name__}"
                        clinic_positive_support_list = [error_msg_for_all] * num_samples_for_shap_cols
                        clinic_negative_support_list = [error_msg_for_all] * num_samples_for_shap_cols
                        mcg_positive_support_list = [error_msg_for_all] * num_samples_for_shap_cols
                        mcg_negative_support_list = [error_msg_for_all] * num_samples_for_shap_cols

                else:  # Model is not XGBoost
                    print("Model is not XGBoost. Detailed SHAP explanations skipped.")
                    clinic_positive_support_list = [default_msg_non_xgb] * num_samples_for_shap_cols
                    clinic_negative_support_list = [default_msg_non_xgb] * num_samples_for_shap_cols
                    mcg_positive_support_list = [default_msg_non_xgb] * num_samples_for_shap_cols
                    mcg_negative_support_list = [default_msg_non_xgb] * num_samples_for_shap_cols
            # else: SHAP explanation generation is disabled (lists already initialized with default_msg_shap_disabled)

            # --- END SHAP EXPLANATION MODIFIED ---

        # 将结果添加到原始DataFrame
        test_data['predicted_prob'] = probabilities[:, 1]
        test_data['predicted_class'] = predictions
        if generate_shap_explanation:
            test_data['shap_clinic_positive_support'] = clinic_positive_support_list
            test_data['shap_clinic_negative_support'] = clinic_negative_support_list
            test_data['shap_mcg_positive_support'] = mcg_positive_support_list
            test_data['shap_mcg_negative_support'] = mcg_negative_support_list

        print(f"\n使用模型版本 {version} 进行预测")
        print(f"版本信息:")
        print(f"- 创建时间: {self.version_info['versions'][version]['created_at']}")
        print(f"- 模型类型: {self.version_info['versions'][version].get('model_type', '标准模型')}")

        if is_double_model:
            print(f"- 阈值设置: threshold1={threshold1}, threshold2={threshold2}")

        # 打印预测结果统计
        print("\n预测结果统计:")
        print(f"预测的阳性样本数: {sum(predictions == 1)}")
        print(f"预测的阴性样本数: {sum(predictions == 0)}")

        # 如果有真实标签，计算性能指标
        if '造影结论' in test_data.columns and len(test_data['造影结论'].unique()) > 1:
            y_true = test_data['造影结论'].values
            print("\n性能评估:")
            # print(y_true)
            # print(predictions)
            print(classification_report(y_true, predictions))
            try:
                auc = roc_auc_score(y_true, probabilities[:, 1])
                print(f"AUC: {auc:.4f}")
            except:
                print("无法计算AUC")

        return probabilities, predictions, version, test_data

    def list_versions(self):
        """列出所有可用的模型版本"""
        print("\n=== 可用的模型版本 ===")
        print(f"最新版本: {self.version_info['latest_version']}\n")
        versions = []
        for version, info in sorted(self.version_info['versions'].items(),
                                    key=lambda x: x[1]['created_at'],
                                    reverse=True):
            versions.append(version)
            try:
                print(f"版本: {version}")
                print(f"- 创建时间: {info['created_at']}")
                # print(f"- 模型: {info['model_name']}")
                print(f"- 特征数量: {info['feature_count']}")
                if info.get('description'):
                    print(f"- 描述: {info['description']}")
                print()
            except:
                print(f"版本 {version} 信息不完整,具体查看version_info文件")
        print(f"总版本数: {len(versions)}")
        return versions

    def optimize_model_params(self, model_type, X, y, selected_features, n_trials=200, timeout=None):
        """
        使用Optuna优化模型参数，包括数据增强参数

        Args:
            model_type: 模型类型（例如'XGBClassifier', 'SVC'等）
            X: 特征数据
            y: 目标变量
            n_trials: Optuna尝试的次数
            timeout: 优化的超时时间（秒）

        Returns:
            dict: 最优参数字典
        """
        import optuna

        def objective(trial):
            # 数据增强参数
            use_augmentation = trial.suggest_categorical('use_augmentation', [True, False])

            aug_params = {}
            if use_augmentation:
                aug_params['balance_method'] = trial.suggest_categorical(
                    'balance_method', ['smote', 'adasyn', 'borderline_smote'])
                # 避免使用'none'作为选项
                aug_params['noise_method'] = trial.suggest_categorical(
                    'noise_method', ['random_perturbation', 'gaussian_noise'])
                aug_params['noise_ratio'] = trial.suggest_float('noise_ratio', 0.0, 0.2)

                # 全局平衡目标比例或按中心平衡
                balance_by_center = trial.suggest_categorical('balance_by_center', [True, False])

                if balance_by_center:
                    aug_params['center_target_ratio'] = [
                        trial.suggest_float('center_ratio_1', 0.5, 1.0),
                        trial.suggest_float('center_ratio_2', 0.5, 1.0),
                        trial.suggest_float('center_ratio_3', 0.5, 1.0),
                        trial.suggest_float('center_ratio_4', 0.5, 1.0),
                        trial.suggest_float('center_ratio_5', 0.5, 1.0)
                    ]
                else:
                    aug_params['global_target_ratio'] = trial.suggest_float('global_target_ratio', 0.5, 1.0)

            # 模型特定参数
            if model_type == 'XGBClassifier' or model_type == 'CRFXGBClassifier':
                model_params = {
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                    'gamma': trial.suggest_float('gamma', 0, 5),
                    'early_stopping_rounds': trial.suggest_int('early_stopping_rounds', 50, 300),
                }
            elif model_type == 'SVC':
                model_params = {
                    'C': trial.suggest_float('C', 0.1, 10.0, log=True),
                    'kernel': trial.suggest_categorical('kernel', ['linear', 'poly', 'rbf', 'sigmoid']),
                    'gamma': trial.suggest_categorical('gamma', ['scale', 'auto']),
                }
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")

            # 使用多个不同随机种子进行重复评估
            n_repeats = 3  # 可以根据计算资源调整
            all_scores = []

            for rep in range(n_repeats):
                cv_seed = 42 + trial.number * 100 + rep
                cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=cv_seed)
                scores = []
                for train_idx, test_idx in cv.split(X, y):
                    X_train_cv, X_test_cv = X.iloc[train_idx], X.iloc[test_idx]
                    y_train_cv, y_test_cv = y.iloc[train_idx], y.iloc[test_idx]

                    # 应用数据增强（如果需要）
                    X_train_aug, y_train_aug = X_train_cv.copy(), y_train_cv.copy()

                    if use_augmentation:
                        try:
                            augmentor = StratifiedBalancedDataAugmentor(
                                balance_method=aug_params.get('balance_method', 'smote'),
                                noise_method=aug_params.get('noise_method', 'random_perturbation'),
                                verbose=False
                            )

                            if aug_params.get('balance_by_center', False):
                                # 按医院进行平衡
                                X_train_aug, y_train_aug = augmentor.balance_by_center(
                                    X_train_aug, y_train_aug,
                                    center_col='clinic_hospital',
                                    selected_features=selected_features,
                                    target_ratio=aug_params.get('center_target_ratio', [1.0, 0.8, 0.5, 0.5, 0.5]),
                                    noise_ratio=aug_params.get('noise_ratio', 0.0)
                                )
                            else:
                                # 全局平衡
                                X_train_aug, y_train_aug = augmentor.balance_and_augment(
                                    X_train_aug, y_train_aug,
                                    target_ratio=aug_params.get('global_target_ratio', 0.9),
                                    noise_ratio=aug_params.get('noise_ratio', 0.0)
                                )
                        except Exception as e:
                            print(f"增强失败: {str(e)}")

                    # 对于需要早停的模型，进一步划分验证集
                    if model_type == 'XGBClassifier' or model_type == 'CRFXGBClassifier':
                        # 分割验证集用于早停
                        val_seed = 42 + trial.number * 10
                        X_tr, X_val, y_tr, y_val = train_test_split(
                            X_train_aug, y_train_aug, test_size=0.2, random_state=val_seed, stratify=y_train_aug
                        )

                        model = xgb.XGBClassifier(
                            objective='binary:logistic',
                            eval_metric=['auc', 'logloss'],
                            **model_params
                        )

                        try:
                            # 使用验证集进行早停
                            eval_set = [(X_tr, y_tr), (X_val, y_val)]
                            model.fit(
                                X_tr, y_tr,
                                eval_set=eval_set,
                                verbose=False
                            )
                        except Exception as e:
                            print(f"XGB训练失败: {str(e)}")
                            scores.append(0.0)
                            continue

                    elif model_type == 'SVC':
                        from sklearn.svm import SVC
                        model = SVC(probability=True, random_state=42, **model_params)

                        try:
                            model.fit(X_train_aug, y_train_aug)
                        except Exception as e:
                            print(f"SVC训练失败: {str(e)}")
                            scores.append(0.0)
                            continue

                    # 评估模型
                    try:
                        y_pred_proba = model.predict_proba(X_test_cv)
                        auc = roc_auc_score(y_test_cv, y_pred_proba[:, 1])
                        scores.append(auc)
                    except Exception as e:
                        print(f"评估失败: {str(e)}")
                        scores.append(0.0)
                all_scores.extend(scores)
            mean_score = np.mean(all_scores)
            std_score = np.std(all_scores)
            # 优化稳定性和性能的组合
            # alpha控制对稳定性的重视程度（0-1之间）
            alpha = 0.2
            return mean_score - alpha * std_score

        # 创建并运行Optuna研究
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials, timeout=timeout)

        # 获取最佳参数
        best_params = study.best_params
        best_score = study.best_value

        # 提取参数
        model_params = {}
        aug_params = {'use_augmentation': best_params.pop('use_augmentation', False)}

        # 如果使用增强，提取相关参数
        if aug_params['use_augmentation']:
            aug_params['balance_method'] = best_params.pop('balance_method', 'smote')
            aug_params['noise_method'] = best_params.pop('noise_method', 'random_perturbation')
            aug_params['noise_ratio'] = best_params.pop('noise_ratio', 0.0)

            if 'balance_by_center' in best_params:
                aug_params['balance_by_center'] = best_params.pop('balance_by_center')

                if aug_params['balance_by_center']:
                    aug_params['center_target_ratio'] = [
                        best_params.pop('center_ratio_1', 0.8),
                        best_params.pop('center_ratio_2', 0.8),
                        best_params.pop('center_ratio_3', 0.5),
                        best_params.pop('center_ratio_4', 0.5),
                        best_params.pop('center_ratio_5', 0.5)
                    ]
                else:
                    aug_params['global_target_ratio'] = best_params.pop('global_target_ratio', 0.9)

        # 剩余参数是模型参数
        model_params = best_params

        return {
            'model_params': model_params,
            'aug_params': aug_params,
            'best_score': best_score
        }

    def train_best_model(self, evaluation_results, X, y, scaler=None, selected_features=None,
                         version=None, description=None, optimize_params=False,
                         n_trials=100, timeout=None, validation_size=0.05,modeltype=None):
        """
        使用优化参数训练最佳模型

        Args:
            X: 特征数据
            y: 目标变量
            model_type: 模型类型 (例如 'XGBClassifier')
            scaler: 特征缩放器
            selected_features: 选择的特征列表
            version: 版本标识（如果为None，则自动生成）
            description: 版本描述
            optimize_params: 是否使用Optuna优化参数
            n_trials: Optuna尝试的次数
            timeout: 优化的超时时间（秒）
            validation_size: 用于早停的验证集比例

        Returns:
            tuple: (best_model, version)
        """

        if not hasattr(self, 'version_info'):
            self.version_info = {'versions': {}, 'latest_version': None}

        # 如果未指定版本，则自动生成版本号
        if version is None:
            date_str = datetime.now().strftime('%Y%m%d')
            existing_versions = [v for v in self.version_info['versions'].keys()
                               if v.startswith(date_str)]
            version = f"{date_str}_{len(existing_versions) + 1}"

        version_dir = self._get_version_dir(version)
        if not os.path.exists(version_dir):
            os.makedirs(version_dir)

        print(f"\n=== 开始训练模型 版本:{version} ===")

        best_model_name = evaluation_results['best_model_name']
        best_model = evaluation_results['best_model']
        self.best_model = best_model  # 保存用于元数据
        self.selected_features = selected_features  # 保存用于元数据
        # 获取best_model的类名
        if modeltype is None:
            model_type = str(type(best_model).__name__)
            model_type = 'XGBClassifier'
            print(model_type)
        else:
            model_type = modeltype


        # 选择特征（如果提供）
        if selected_features is not None and isinstance(X, pd.DataFrame):
            X_selected = X[selected_features]
        else:
            X_selected = X
            selected_features = list(X.columns) if isinstance(X, pd.DataFrame) else None

        # 应用缩放（如果提供）
        if scaler is not None:
            if isinstance(X_selected, pd.DataFrame):
                X_selected_scaled = pd.DataFrame(
                    scaler.transform(X_selected),
                    columns=X_selected.columns,
                    index=X_selected.index
                )
            else:
                X_selected_scaled = scaler.transform(X_selected)
        else:
            X_selected_scaled = X_selected

        # 优化参数
        if optimize_params:
            print("\n=== 开始优化模型参数 ===")
            opt_results = self.optimize_model_params(
                model_type, X_selected_scaled, y, selected_features, n_trials, timeout
            )

            model_params = opt_results['model_params']
            aug_params = opt_results['aug_params']
            best_score = opt_results['best_score']

            print(f"参数优化完成，最佳交叉验证分数: {best_score:.4f}")
            print("最佳模型参数:")
            for param, value in model_params.items():
                print(f"  {param}: {value}")

            if aug_params['use_augmentation']:
                print("最佳增强配置:")
                for param, value in aug_params.items():
                    print(f"  {param}: {value}")
        else:
            # 默认参数
            if model_type == 'XGBClassifier':
                model_params = {
                    'learning_rate': 0.05,
                    'n_estimators': 600,
                    'max_depth': 6,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    # 'early_stopping_rounds': 80,
                }
                model_params = {
                    'n_estimators' : 600,
                    'learning_rate' : 0.01,
                    'max_depth' : 6,
                    'min_child_weight' : 1,
                    'subsample' : 0.9,
                    'colsample_bytree' : 0.9,
                    'gamma' : 0.05,
                    'early_stopping_rounds': 100,
                }

                aug_params = {'use_augmentation': False}
            elif model_type == 'SVC':
                model_params = {
                    'C': 1.0,
                    'kernel': 'rbf',
                    'gamma': 'scale',
                }
                aug_params = {'use_augmentation': False}
            elif model_type == 'MLPClassifier':
                model_params = {
                    'hidden_layer_sizes': (100, 50),
                    'activation': 'relu',
                    'solver': 'adam',
                    'alpha': 0.0001,
                    'learning_rate': 'adaptive',
                    'learning_rate_init': 0.001,
                    'max_iter': 800,
                }
                aug_params = {'use_augmentation': False}

            elif model_type == 'StackingClassifier':
                model_params = {
                }
                aug_params = {'use_augmentation': False}
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
        # 应用数据增强（如果需要）
        X_train_final = X_selected_scaled.copy()
        y_train_final = y.copy()

        # if aug_params.get('use_augmentation', True):
        if True:
            print("\n=== 应用数据增强 ===")
            try:
                # 检查类别分布
                print("\n增强前类别分布:")
                print(pd.Series(y_train_final).value_counts(normalize=True))

                augmentor = StratifiedBalancedDataAugmentor(
                    balance_method=aug_params.get('balance_method', 'smote'),
                    noise_method=aug_params.get('noise_method', 'random_perturbation'),
                    verbose=True
                )

                if aug_params.get('balance_by_center', False):
                    # 按医院进行平衡
                    print("按医院平衡")
                    X_train_final, y_train_final = augmentor.balance_by_center(
                        X_train_final, y_train_final,
                        center_col='clinic_hospital',
                        selected_features=selected_features,
                        target_ratio=aug_params.get('center_target_ratio', [0.0, 0.0, 0.0, 0.6,0.6,0.0]),
                        noise_ratio=aug_params.get('noise_ratio', 0.0)
                    )
                else:
                    # 全局平衡
                    print("全局平衡")
                    X_train_final, y_train_final = augmentor.balance_and_augment(
                        X_train_final, y_train_final,
                        target_ratio=aug_params.get('global_target_ratio', 0.7),
                        noise_ratio=aug_params.get('noise_ratio', 0.0)
                    )

                # 检查类别分布
                print("\n增强后类别分布:")
                print(pd.Series(y_train_final).value_counts(normalize=True))
                print("数据增强已成功应用")

            except Exception as e:
                print(f"数据增强失败: {str(e)}")
                print("将使用原始数据继续训练")

        # 创建最终模型
        if model_type == 'XGBClassifier':
            # 需要处理早停的模型
            if 'early_stopping_rounds' in model_params:
                # 保存早停轮数并从训练参数中移除
                early_stopping_rounds = model_params.pop('early_stopping_rounds')

                # 模型基础参数
                base_params = {
                    'objective': 'binary:logistic',
                    'eval_metric': ['auc', 'logloss'],
                }
                base_params.update(model_params)

                # 创建模型
                best_model = xgb.XGBClassifier(early_stopping_rounds=early_stopping_rounds, **base_params, )

                print("\n=== 训练最终模型（使用早停）===")
                # 为早停分割小部分验证集
                X_train, X_eval, y_train, y_eval = train_test_split(
                    X_train_final, y_train_final,
                    test_size=validation_size,
                    random_state=42,
                    stratify=y_train_final
                )

                # 训练带早停的模型
                eval_set = [(X_train, y_train), (X_eval, y_eval)]
                best_model.fit(
                    X_train, y_train,
                    eval_set=eval_set,
                    verbose=100
                )
            else:
                # 无早停的模型
                base_params = {
                    'objective': 'binary:logistic',
                    'eval_metric': ['auc', 'logloss'],
                }
                base_params.update(model_params)

                best_model = xgb.XGBClassifier(**base_params)
                print("\n=== 训练最终模型（无早停，使用全量数据）===")
                best_model.fit(X_train_final, y_train_final)
        elif model_type == 'SVC':
            from sklearn.svm import SVC
            # 创建SVC模型
            svc_params = {
                'probability': True,
                'random_state': 42,
            }
            svc_params.update(model_params)

            best_model = SVC(**svc_params)
            print("\n=== 训练最终模型（SVC，使用全量数据）===")
            best_model.fit(X_train_final, y_train_final)
        elif model_type == 'MLPClassifier':
            from sklearn.neural_network import MLPClassifier
            # 创建MLP模型
            mlp_params = {
                'random_state': 42,
            }
            mlp_params.update(model_params)

            best_model = MLPClassifier(**mlp_params)
            print("\n=== 训练最终模型（MLP，使用全量数据）===")
            best_model.fit(X_train_final, y_train_final)
        elif model_type == 'StackingClassifier':
            from model_trainer.model_factory import ModelFactory
            models = ModelFactory.create_all_models(
                random_state=42,
                include_stacking=True  # not by_center  # not by_center #
            )
            best_model  = models['Stacking']
            print("\n=== 训练最终模型（Stacking，使用全量数据）===")
            best_model.fit(X_train_final, y_train_final)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        # 在训练集上评估模型
        print("\n=== 训练集性能评估 ===")
        if hasattr(best_model, 'predict_proba'):
            train_probs = best_model.predict_proba(X_selected_scaled)
            train_preds = (train_probs[:, 1] > 0.5).astype(int)
        else:
            train_preds = best_model.predict(X_selected_scaled)
            train_probs = None

        # 计算指标
        train_metrics = {
            'accuracy': accuracy_score(y, train_preds),
            'precision': precision_score(y, train_preds),
            'recall': recall_score(y, train_preds),
            'specificity': recall_score(y, train_preds, pos_label=0),
            'f1': f1_score(y, train_preds),
        }

        # 如果有概率预测，添加AUC
        if train_probs is not None:
            train_metrics['auc'] = roc_auc_score(y, train_probs[:, 1])

        # 打印评估指标
        print("\n训练集评估指标:")
        for metric, value in train_metrics.items():
            print(f"{metric.capitalize()}: {value:.4f}")

        # 打印混淆矩阵
        conf_matrix = confusion_matrix(y, train_preds)
        print("\n混淆矩阵:")
        print("          预测        Negative  Positive")
        print(f"实际 Negative  {conf_matrix[0, 0]:8d} {conf_matrix[0, 1]:8d}")
        print(f"实际 Positive  {conf_matrix[1, 0]:8d} {conf_matrix[1, 1]:8d}")

        # 计算错误率
        error_mask = train_preds != y
        error_count = np.sum(error_mask)
        error_rate = error_count / len(y)
        print(f"\n错误预测样本数: {error_count}")
        print(f"错误率: {error_rate:.4f}")

        # 如果模型支持特征重要性，则打印
        if hasattr(best_model, 'feature_importances_') and selected_features:
            print("\n特征重要性（前20个）:")
            feature_importance = pd.DataFrame({
                'feature': selected_features,
                'importance': best_model.feature_importances_
            }).sort_values('importance', ascending=False)
            print(feature_importance.head(20))

        # 保存模型和相关文件
        files_to_save = {
            'model.pkl': best_model,
        }

        if scaler is not None:
            files_to_save['scaler.pkl'] = scaler

        if selected_features is not None:
            files_to_save['features.pkl'] = selected_features

        for filename, obj in files_to_save.items():
            filepath = os.path.join(version_dir, filename)
            with open(filepath, 'wb') as f:
                pickle.dump(obj, f)

        # 创建元数据
        metadata = {
            'version': version,
            'model_type': model_type,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'description': description,
            'train_metrics': train_metrics,
            'feature_count': len(selected_features) if selected_features else X.shape[1],
            'sample_count': len(y),
            'class_distribution': pd.Series(y).value_counts().to_dict(),
            'error_rate': float(error_rate),
        }

        # 添加优化信息
        if optimize_params:
            metadata['optimization'] = {
                'optimized': True,
                'best_score': best_score,
                'model_params': model_params,
                'aug_params': aug_params
            }
        else:
            metadata['optimization'] = {
                'optimized': False,
                'model_params': model_params,
                'aug_params': aug_params
            }

        # 更新版本信息
        self.version_info['versions'][version] = metadata
        self.version_info['latest_version'] = version
        self._save_version_info()

        # 保存版本元数据
        metadata_path = os.path.join(version_dir, 'metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        print(f"\n模型和配置已保存到版本目录: {version_dir}")
        print(f"版本信息:")
        print(f"- 版本: {version}")
        print(f"- 模型: {model_type}")
        print(f"- 优化: {'是' if optimize_params else '否'}")
        print(f"- 创建时间: {metadata['created_at']}")
        if description:
            print(f"- 描述: {description}")

        return best_model, version




#
# train_and_save_model (analysis,
#         evaluation_results = results,
#         version='v2.5.03',
#         description='BalanceBoruta600'
#                     '0623版特征'
#                     '训测集，删除偏差'
#                     '综合标签',
#     )
#
#
#
#
# # 3. 测试集
# # 内测 ---------------------------------------------------------------
# evaluator = ModelEvaluator()  # 读取过数据后会缓存 不用重新定义
# # 预测新数据
# version = 'v2.5.02'
#
# probs, preds, version, results_df = evaluator.predict_new_data(
#     feature_dir="./files/saved_features/file_features_V250623/",
#     # excel_path='D:\std data\\20250520版本\\0627训测集.xlsx',
#     excel_path='./files/data/data_index/20250520版本/0523训测集.xlsx',
#     sheet_name='外验集',
#     version = version,  # 可选，不指定则使用最新版本
# )
#
#
# res = get_test_metrics(result_df=results_df,
#                        external_excel_path='./files/data/data_index/20250520版本/0523训测集.xlsx',
#                        sheet_name='外验集',
#                        fixed_threshold=0.4)  # 32似乎可
#
#
# probs, preds, version, results_df = evaluator.predict_new_data(
#     feature_dir="./files/saved_features/file_features_V250623/",
#     # excel_path='D:\std data\\20250520版本\\0627训测集.xlsx',
#     excel_path='./files/data/data_index/20250520版本/0605训测集.xlsx',
#     # excel_path='./files/data/data_index/20250520版本/0523训测集.xlsx',
#     sheet_name='外测集加新分层外测十院',#'','外验集',
#     version = version,  # 可选，不指定则使用最新版本
# )
#
# res = get_test_metrics(result_df=results_df,
#                        external_excel_path='./files/data/data_index/20250520版本/0605训测集.xlsx',
#                        sheet_name='外测集加新分层外测十院',
#                        fixed_threshold=0.6)  #
#
#
#
#
#
# results_analysis, analyzer = analyze_clinical_risk_comprehensive(
#     result_df=results_df,
#     external_excel_path='./files/data/data_index/20250520版本/0605训测集.xlsx',
#     sheet_name='外测集加新分层外测十院',
#     output_dir='./my_analysis',
#     target_acc=0.8,
#     target_sen=0.8,
#     target_spe=0.6
# )
#
#
# optimize_hospital_thresholds(result_df=results_df,external_excel_path='./files/data/data_index/20250520版本/0605训测集.xlsx',
#                        sheet_name='外测集加新分层外测十院',metric='acc')
#
# opt_res = optimize_hospital_thresholds(result_df=results_df,external_excel_path='./files/data/data_index/20250520版本/0523训测集.xlsx',
#                        sheet_name='外验集',metric='constrained_acc',min_sensitivity=0.36)
#
#
#
# hospital_thresholds = {
#     '广东南海':0.3,
#     '上海六院': 0.3,
#     '上海中山': 0.3,
#     '安医': 0.3,
#     '上海十院': 0.5,
#     '北京301': 0.6,
# }
#
#
#
# res = get_test_metrics(result_df=results_df,
#                        external_excel_path='./files/data/data_index/20250520版本/0605训测集.xlsx',
#                        sheet_name='外测集加新分层外测十院',
#                        hospital_thresholds=hospital_thresholds,
#                        #  fixed_threshold = 0.32
#                        )  # 32似乎可
#
#
# results_df['predicted_class'] = (results_df['predicted_prob'] >= 0.55).astype(int)
#
# evaluator.save_predictions(res['result_df'], './files/results/predictions' + version + 'threshold_wise.xlsx')