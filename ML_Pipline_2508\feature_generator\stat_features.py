'''
@Project ：ML_Pipline 
@File    ：stat_features.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/8/19 上午11:32 
@Discribe：
'''

import os
import threading

import numpy as np
import pandas as pd

from feature_generator.utils.get_QRSwave_features import entropy_features_qrs, func_frequency_feature_qrs, \
    time_features_qrs
from feature_generator.utils.get_Twave_features import time_features_t, entropy_features_t, frequency_features_t, \
    EEG_features

from feature_generator.stats_utils import *


os.environ['OPENBLAS_NUM_THREADS'] = '4'
print_lock = threading.Lock()
finish_num = 0
arr_df = []


class StatFeatures:

    def __init__(self, basic_args_dict=None, label=None):
        self.basic_args_dict = basic_args_dict
        self.label = label
        self.file_path, self.save_path, self.file_name, data_frame, result_peak, self.interpolated_data = basic_args_dict.values()
        self.signal_channels = np.array(data_frame.iloc[:, 1:]).T
        self.t_gap_loc, self.t_gini_window_length, self.qrs_gap_loc, self.qrs_gini_window_length = 1, 3, 1, 3
        self.loc_p_peak, self.loc_q_peak, self.loc_r_peak, self.loc_s_peak, self.loc_t_onset, self.loc_t_peak, self.loc_t_end = \
            (result_peak['p-peak'], result_peak['q-peak'], result_peak['r-peak'], result_peak['s-peak'],
             result_peak['t-onset'], result_peak['t-peak'], result_peak['t-end'])

    @staticmethod
    def build_dict(*args):
        return {k: v for arg in args if arg for k, v in arg.items()}

    @staticmethod
    def euclidean_distance(matrix1, matrix2):
        return np.linalg.norm(matrix1 - matrix2)

    @staticmethod
    def cosine_similarity(matrix1, matrix2):
        return (np.sum(np.multiply(matrix1, matrix2)) /
                (np.linalg.norm(matrix1) * np.linalg.norm(matrix2)))

    # @functools.cache
    def get_stat_features(self, basic_args_dict=None, label=None) -> pd.DataFrame:
        basic_args_dict = basic_args_dict or self.basic_args_dict
        label = label or self.label
        # 获取一般统计特征
        result_ = self.get_init_features()
        result_['label'] = label or ''
        result_ = self.build_dict(result_, {'label': label})
        return_df = pd.DataFrame(result_, index=[0])

        return return_df

    @staticmethod
    def unified_normalization(arrlist):
        """
        波段内归一化方案：保持正负值的物理相对关系，每个波段独立归一化
        输入：包含x个波段的list，每个元素为ndarray (36, timelength)
        输出：归一化后的同结构list，值域约[-1, 1]，每个波段独立归一化
        """
        normalized_list = []
        for arr in arrlist:
            # 计算当前波段的最大绝对值
            max_abs = np.max(np.abs(arr))

            # 防止除零（当当前波段所有数据均为0时）
            if max_abs == 0:
                normalized_list.append(arr)  # 如果最大绝对值为0，则保持原始波段数据
            else:
                normalized_arr = arr / max_abs  # 对当前波段进行归一化
                normalized_list.append(normalized_arr)

        return normalized_list
    def get_advanced_features(self):
        # 预先计算所有可能用到的索引以避免重复计算
        segments = {
            'P': (max(0, self.loc_p_peak - 10), min(self.loc_p_peak + 10, self.loc_q_peak)),
            'QRS': (self.loc_q_peak, self.loc_s_peak),
            'T': (self.loc_t_onset, self.loc_t_end)
        }

        features = {
            'MCG_ID': self.file_name.replace('.txt', '')
        }
        signal_channels =self.signal_channels
        # 预先计算时间点数组以复用
        time_points = np.arange(max(seg[1] - seg[0] for seg in segments.values()))

        # 对每个区段进行特征提取
        for seg_name, (start, end) in segments.items():
            # 提取当前区段的信号 - 一次性操作
            if end < start:
                start = end-10
            seg_signal = signal_channels[:, start:end]
            seg_length = end - start

            # 使用numpy的向量化操作一次性计算最大最小值
            max_vals = np.max(seg_signal, axis=1)  # shape: (36,)
            min_vals = np.min(seg_signal, axis=1)  # shape: (36,)

            # 计算极值比 - 使用where避免除零问题
            min_vals_safe = np.where(min_vals != 0, min_vals, 1)
            max_min_ratio = np.abs(max_vals / min_vals_safe)
            max_min_ratio = np.where(min_vals != 0, max_min_ratio, 1.0)

            # 计算曲线下面积
            # 1. 分别计算正负面积
            pos_mask = seg_signal > 0
            neg_mask = seg_signal < 0

            areas = np.zeros(36)
            pos_areas = np.zeros(36)
            neg_areas = np.zeros(36)

            # 使用预计算的时间点数组切片
            t = time_points[:seg_length]

            for ch in range(36):
                # 检查是否存在正值
                has_pos = np.any(pos_mask[ch])
                if has_pos:
                    pos_signal = np.where(pos_mask[ch], seg_signal[ch], 0)
                    pos_areas[ch] = np.trapz(pos_signal, t)
                else:
                    pos_areas[ch] = 0

                # 检查是否存在负值
                has_neg = np.any(neg_mask[ch])
                if has_neg:
                    neg_signal = np.where(neg_mask[ch], seg_signal[ch], 0)
                    neg_areas[ch] = abs(np.trapz(neg_signal, t))
                else:
                    neg_areas[ch] = 0

                # 总面积
                areas[ch] = pos_areas[ch] + neg_areas[ch]

            # 计算统计特征
            stats = {
                f'{seg_name}_max_min_ratio_mean': np.mean(max_min_ratio),
                f'{seg_name}_max_min_ratio_std': np.std(max_min_ratio),
                f'{seg_name}_max_min_ratio_median': np.median(max_min_ratio),
                f'{seg_name}_area_total_mean': np.mean(areas),
                f'{seg_name}_area_total_std': np.std(areas),
                f'{seg_name}_area_total_median': np.median(areas),
                f'{seg_name}_area_pos_mean': np.mean(pos_areas),
                f'{seg_name}_area_pos_std': np.std(pos_areas),
                f'{seg_name}_area_neg_mean': np.mean(neg_areas),
                f'{seg_name}_area_neg_std': np.std(neg_areas),
                # 处理正负面积比例的特殊情况
                f'{seg_name}_area_ratio': np.mean(pos_areas) / (np.mean(neg_areas) + 1e-10) if np.any(
                    neg_areas > 0) else (1e4 if np.any(pos_areas > 0) else 1.0),
                f'{seg_name}_has_positive': int(np.any(pos_areas > 0)),
                f'{seg_name}_has_negative': int(np.any(neg_areas > 0)),
                f'{seg_name}_max_area_channel': np.argmax(areas),
                f'{seg_name}_min_area_channel': np.argmin(areas)
            }
            features.update(stats)

            # 如果需要每个通道的具体特征，使用字典推导式一次性生成
            channel_features = {
                f'{seg_name}_ch{ch}_{feat}': val[ch]
                for ch in range(36)
                for feat, val in {
                    'max_min_ratio': max_min_ratio,
                    'area_total': areas,
                    'area_pos': pos_areas,
                    'area_neg': neg_areas
                }.items()
            }
            features.update(channel_features)

        # 合并原有特征
        original_features = self.get_peak_dist_features()
        features.update(original_features)

        return features

    def get_peak_dist_features(self):
        signal_channels =self.signal_channels
        qt_time = self.loc_t_end - self.loc_q_peak
        # 每个时刻点的最大最小值
        max_values = np.max(signal_channels, axis=0)
        min_values = -np.min(signal_channels, axis=0)
        # 上下包络的相似度，即对称性
        cosine_similarityValue = self.cosine_similarity(max_values, min_values)

        return {
            'MCG_ID': self.file_name.replace('.txt', ''),
            'qr_div_qt': (self.loc_r_peak - self.loc_q_peak) / qt_time,
            'rs_div_qt': (self.loc_s_peak - self.loc_r_peak) / qt_time,
            'tt_div_qt': (self.loc_t_end - self.loc_t_onset) / qt_time,
            'qrs_div_qt': (self.loc_s_peak - self.loc_q_peak) / qt_time,
            'loc_p_peak': self.loc_p_peak,
            'loc_q_peak': self.loc_q_peak,
            'loc_r_peak': self.loc_r_peak,
            'loc_s_peak': self.loc_s_peak,
            'loc_t_onset': self.loc_t_onset,
            'loc_t_peak': self.loc_t_peak,
            'loc_t_end': self.loc_t_end,
            'RT_euclidean_distance': self.euclidean_distance(self.interpolated_data[self.loc_r_peak],
                                                             self.interpolated_data[self.loc_t_peak]),
            'RT_cosine_similarity': self.cosine_similarity(self.interpolated_data[self.loc_r_peak],
                                                           self.interpolated_data[self.loc_t_peak]),
            'max_min_simlar_distance': cosine_similarityValue
        }

    def get_wave_time_features(self):
        TWaveTimeFeature = time_features_t(self.interpolated_data, self.signal_channels, self.loc_t_onset,
                                           self.loc_t_peak, self.loc_t_end,
                                           self.t_gap_loc, save_path=self.save_path, filename=self.file_name,
                                           imShow=False)
        QRSWaveTimeFeature = time_features_qrs(self.interpolated_data, self.signal_channels, self.loc_q_peak,
                                               self.loc_r_peak, self.loc_s_peak,
                                               self.qrs_gap_loc, save_path=self.save_path, filename=self.file_name,
                                               imShow=False)
        QRWaveTimeFeature = time_features_qrs(self.interpolated_data, self.signal_channels, self.loc_q_peak,
                                              self.loc_q_peak, self.loc_r_peak,
                                              self.qrs_gap_loc, band='qr', park_name='qpeak')
        RSWaveTimeFeature = time_features_qrs(self.interpolated_data, self.signal_channels, self.loc_r_peak,
                                              self.loc_r_peak, self.loc_s_peak,
                                              self.qrs_gap_loc, band='rs', park_name='speak')
        return TWaveTimeFeature, QRSWaveTimeFeature, QRWaveTimeFeature, RSWaveTimeFeature

    def get_entropy_features(self):
        TWaveEntropyFeature = entropy_features_t(self.signal_channels, self.loc_t_onset, self.loc_t_peak,
                                                 self.loc_t_end, self.t_gini_window_length)
        QRSWaveEntropyFeatures = entropy_features_qrs(self.signal_channels, self.loc_q_peak, self.loc_r_peak,
                                                      self.loc_s_peak,
                                                      self.qrs_gini_window_length)
        QRWaveEntropyFeatures = entropy_features_qrs(self.signal_channels, self.loc_q_peak, self.loc_q_peak,
                                                     self.loc_r_peak,
                                                     self.qrs_gini_window_length, band='qr')
        RSWaveEntropyFeatures = entropy_features_qrs(self.signal_channels, self.loc_r_peak, self.loc_r_peak,
                                                     self.loc_s_peak,
                                                     self.qrs_gini_window_length, band='rs')
        return TWaveEntropyFeature, QRSWaveEntropyFeatures, QRWaveEntropyFeatures, RSWaveEntropyFeatures

    def get_frequency_features(self):
        TWaveFrequencyFeature = frequency_features_t(self.signal_channels, self.loc_t_onset, self.loc_t_peak,
                                                     self.loc_t_end)
        QRSWaveFrequencyFeature = func_frequency_feature_qrs(self.signal_channels, self.loc_q_peak, self.loc_r_peak,
                                                             self.loc_s_peak)
        QRWaveFrequencyFeature = func_frequency_feature_qrs(self.signal_channels, self.loc_q_peak, self.loc_q_peak,
                                                            self.loc_r_peak, band='qr')
        RSWaveFrequencyFeature = func_frequency_feature_qrs(self.signal_channels, self.loc_r_peak, self.loc_r_peak,
                                                            self.loc_s_peak, band='rs')
        return TWaveFrequencyFeature, QRSWaveFrequencyFeature, QRWaveFrequencyFeature, RSWaveFrequencyFeature

    def get_init_features(self):
        # 追加 对数据进行的操作需进行良好的标准化；
        self.signal_channels =np.array( self.unified_normalization(self.signal_channels))
        peak_dist_features = self.get_advanced_features()

        EEGFeature = EEG_features(self.signal_channels)

        TWaveTimeFeature, QRSWaveTimeFeature, QRWaveTimeFeature, RSWaveTimeFeature = self.get_wave_time_features()
        TWaveEntropyFeature, QRSWaveEntropyFeatures, QRWaveEntropyFeatures, RSWaveEntropyFeatures = self.get_entropy_features()
        TWaveFrequencyFeature, QRSWaveFrequencyFeature, QRWaveFrequencyFeature, RSWaveFrequencyFeature = self.get_frequency_features()

        TimeFeatures = self.build_dict(TWaveTimeFeature, QRSWaveTimeFeature, QRWaveTimeFeature, RSWaveTimeFeature)
        EntropyFeatures = self.build_dict(TWaveEntropyFeature, QRSWaveEntropyFeatures, QRWaveEntropyFeatures,
                                          RSWaveEntropyFeatures)
        FrequencyFeatures = self.build_dict(TWaveFrequencyFeature, QRSWaveFrequencyFeature, QRWaveFrequencyFeature,
                                            RSWaveFrequencyFeature)

        return self.build_dict(peak_dist_features, EEGFeature, TimeFeatures, EntropyFeatures, FrequencyFeatures)



# from feature_generator.stats_utils import (
#     format_feature_name,  # Already assumed to be used internally by StatFeatures
#     # Time features (if you fully moved time_features_t/qrs into stats_utils and renamed them)
#     time_features_t as calculate_t_wave_time_features,  # Alias to new name
#     time_features_qrs as calculate_qrs_wave_time_features,  # Alias to new name
#
#     # Entropy features
#     calculate_t_wave_entropy_features,
#     calculate_qrs_wave_entropy_features,
#
#     # Frequency features
#     calculate_t_wave_frequency_features,
#     calculate_qrs_wave_frequency_features,
#
#     # EEG features
#     calculate_eeg_features_std_names  # Already used this new name
# )

class StatFeaturesnew:

    def __init__(self, basic_args_dict: dict, label: str = None):
        self.basic_args_dict = basic_args_dict
        self.label = label

        # Robust access to basic_args_dict
        self.file_path = basic_args_dict.get('file_path')
        self.save_path = basic_args_dict.get('save_path')
        self.file_name = basic_args_dict.get('file_name', '') # Provide default for MCG_ID
        data_frame = basic_args_dict.get('data_frame')
        result_peak = basic_args_dict.get('result_peak')
        self.interpolated_data = basic_args_dict.get('interpolated_data')

        if data_frame is None or result_peak is None or self.interpolated_data is None:
            raise ValueError("data_frame, result_peak, and interpolated_data must be provided in basic_args_dict")

        # Ensure signal_channels is a 2D numpy array (channels x time)
        if isinstance(data_frame, pd.DataFrame):
            self.signal_channels_original = np.array(data_frame.iloc[:, 1:]).T
        else: # Assuming it might already be a numpy array
            self.signal_channels_original = np.array(data_frame).T # Or adjust as per actual input type

        # Store normalized signal channels separately, normalization happens in get_init_features
        self.signal_channels_normalized = None

        # Configuration for feature extraction (can be tuned)
        self.t_gap_loc = 1
        self.t_gini_window_length = 3
        self.qrs_gap_loc = 1
        self.qrs_gini_window_length = 3

        # Group peak locations
        self.peak_locs = {
            'p_peak': result_peak.get('p-peak'),
            'q_peak': result_peak.get('q-peak'),
            'r_peak': result_peak.get('r-peak'),
            's_peak': result_peak.get('s-peak'),
            't_onset': result_peak.get('t-onset'),
            't_peak': result_peak.get('t-peak'),
            't_end': result_peak.get('t-end')
        }
        # Validate peak locations (optional, but good practice)
        for peak_name, loc in self.peak_locs.items():
            if loc is None:
                # Consider raising an error or handling missing peaks gracefully
                print(f"Warning: Peak location '{peak_name}' is missing.")


    @staticmethod
    def build_dict(*args):
        """Merges multiple dictionaries."""
        merged_dict = {}
        for d in args:
            if d: # Ensure dictionary is not None
                merged_dict.update(d)
        return merged_dict

    @staticmethod
    def euclidean_distance(matrix1, matrix2):
        return np.linalg.norm(matrix1 - matrix2)

    @staticmethod
    def cosine_similarity(matrix1, matrix2):
        # Add epsilon to prevent division by zero if norms are zero
        norm1 = np.linalg.norm(matrix1)
        norm2 = np.linalg.norm(matrix2)
        if norm1 == 0 or norm2 == 0:
            return 0.0 # Or handle as appropriate (e.g., NaN, specific value)
        return (np.sum(np.multiply(matrix1, matrix2)) / (norm1 * norm2))

    def get_stat_features(self, basic_args_dict: dict = None, label: str = None) -> pd.DataFrame:
        # If new args are passed, re-initialize. This might not be the desired behavior.
        # Consider if get_stat_features should always use the instance's initialized state
        # or if it's intended to be callable with different data.
        # For now, let's assume it uses the instance's state primarily.
        if basic_args_dict is not None or label is not None:
            # This suggests the instance might be reconfigured.
            # If so, __init__ should be callable multiple times or a reset method is needed.
            # Or, these parameters should not be part of get_stat_features if it's meant to use instance state.
            # Sticking to using instance state for this refactor pass:
            pass # self.basic_args_dict and self.label are already set in __init__

        all_features = self.get_init_features()
        all_features['label'] = self.label or '' # Use instance label
        # build_dict is redundant here if all_features is already the complete dict
        # return_df = pd.DataFrame(self.build_dict(all_features, {'label': self.label or ''}), index=[0])
        return_df = pd.DataFrame(all_features, index=[0])
        return return_df

    @staticmethod
    def unified_normalization(signal_array_2d: np.ndarray):
        """
        Performs channel-wise normalization on a 2D numpy array (channels x time).
        Maintains positive/negative relationships by dividing by max absolute value per channel.
        Input: ndarray (num_channels, timelength)
        Output: Normalized ndarray, values in [-1, 1], each channel normalized independently.
        """
        normalized_array = np.zeros_like(signal_array_2d, dtype=float)
        for i in range(signal_array_2d.shape[0]): # Iterate over channels
            channel_data = signal_array_2d[i, :]
            max_abs = np.max(np.abs(channel_data))
            if max_abs == 0:
                normalized_array[i, :] = channel_data # Avoid division by zero
            else:
                normalized_array[i, :] = channel_data / max_abs
        return normalized_array

    def get_mcg_id(self) -> str:
        """Extracts MCG ID from the file name."""
        return self.file_name.replace('.txt', '') if self.file_name else "UNKNOWN_ID"

    # ... (previous code) ...

    def _extract_qrs_family_features(self,
                                     qrs_feature_func,
                                     signal_data: np.ndarray, # This will be self.signal_channels_normalized
                                     interpolated_signal_data: np.ndarray = None, # For time_features_qrs
                                     gap_loc_or_window_length: int = None,
                                     is_time_feature: bool = False):
        """
        Helper to extract features for QRS, QR, and RS segments using a common QRS function.
        """
        features = {}
        # Define segment configurations: (q_loc, r_loc, s_loc, band_name, park_name_for_time_features)
        # Note: 'r_loc' in this context is the 'middle peak' of the segment for the function call.
        qrs_segment_configs = [
            (self.peak_locs['q_peak'], self.peak_locs['r_peak'], self.peak_locs['s_peak'], 'qrs', 'rpeak'),
            (self.peak_locs['q_peak'], self.peak_locs['q_peak'], self.peak_locs['r_peak'], 'qr',  'qpeak'),
            (self.peak_locs['r_peak'], self.peak_locs['r_peak'], self.peak_locs['s_peak'], 'rs',  'speak'),
        ]

        for q_loc, r_loc, s_loc, band, park_name in qrs_segment_configs:
            if any(loc is None for loc in [q_loc, r_loc, s_loc]):
                print(f"Warning: Skipping {band} feature extraction due to missing peak locations.")
                continue

            # Common arguments for all QRS-like feature functions
            args = {
                'signal_channels_normalized': signal_data,
                'loc_q_peak': q_loc,
                'loc_r_peak': r_loc, # This is the "middle" or reference peak for the segment
                'loc_s_peak': s_loc,
                'band': band,
            }

            if is_time_feature: # time_features_qrs
                args.update({
                    'DATA_TEMP_Spline': interpolated_signal_data,
                    'gap_loc': gap_loc_or_window_length, # self.qrs_gap_loc
                    'park_name': park_name,
                    'save_path': self.save_path,
                    'filename': self.file_name,
                    'imShow': False
                })
            elif qrs_feature_func == calculate_qrs_wave_entropy_features: # entropy_features_qrs
                args['qrs_gini_window_length'] = gap_loc_or_window_length # self.qrs_gini_window_length
            # func_frequency_feature_qrs doesn't need extra args beyond common ones for this helper

            features.update(qrs_feature_func(**args))
        return features

    def get_wave_time_features(self) -> dict:
        """Extracts time-domain features for T, QRS, QR, and RS waves."""
        if self.signal_channels_normalized is None or self.interpolated_data is None:
            raise ValueError("Normalized signals or interpolated data not available.")
        if self.peak_locs['t_onset'] is None or self.peak_locs['t_peak'] is None or self.peak_locs['t_end'] is None:
            print("Warning: Skipping T-wave time feature extraction due to missing peak locations.")
            t_wave_time_feature = {}
        else:
            t_wave_time_feature = calculate_t_wave_time_features(
                DATA_TEMP_Spline=self.interpolated_data,
                signal_channels_normalized=self.signal_channels_normalized, # Should this be original or normalized?
                                                                 # The external functions might assume raw scale.
                                                                 # For now, assuming normalized. Re-evaluate if results change.
                loc_t_onset=self.peak_locs['t_onset'],
                loc_t_peak=self.peak_locs['t_peak'],
                loc_t_end=self.peak_locs['t_end'],
                gap_loc=self.t_gap_loc,
                save_path=self.save_path,
                filename=self.file_name,
                imShow=False
            )

        qrs_family_time_features = self._extract_qrs_family_features(
            qrs_feature_func=calculate_qrs_wave_time_features,
            signal_data=self.signal_channels_normalized, # See comment above for T-wave
            interpolated_signal_data=self.interpolated_data,
            gap_loc_or_window_length=self.qrs_gap_loc,
            is_time_feature=True
        )
        return self.build_dict(t_wave_time_feature, qrs_family_time_features)


    def get_entropy_features(self) -> dict:
        """Extracts entropy-based features for T, QRS, QR, and RS waves."""
        if self.signal_channels_normalized is None:
            raise ValueError("Normalized signals not available.")

        if self.peak_locs['t_onset'] is None or self.peak_locs['t_peak'] is None or self.peak_locs['t_end'] is None:
            print("Warning: Skipping T-wave entropy feature extraction due to missing peak locations.")
            t_wave_entropy_feature = {}
        else:
            t_wave_entropy_feature = calculate_t_wave_entropy_features(
                signal_channels_normalized=self.signal_channels_normalized,
                loc_t_onset=self.peak_locs['t_onset'],
                loc_t_peak=self.peak_locs['t_peak'],
                loc_t_end=self.peak_locs['t_end'],
                t_gini_window_length=self.t_gini_window_length
            )

        qrs_family_entropy_features = self._extract_qrs_family_features(
            qrs_feature_func=calculate_qrs_wave_entropy_features,
            signal_data=self.signal_channels_normalized,
            gap_loc_or_window_length=self.qrs_gini_window_length
        )
        return self.build_dict(t_wave_entropy_feature, qrs_family_entropy_features)

    def get_frequency_features(self) -> dict:
        """Extracts frequency-domain features for T, QRS, QR, and RS waves."""
        if self.signal_channels_normalized is None:
            raise ValueError("Normalized signals not available.")

        if self.peak_locs['t_onset'] is None or self.peak_locs['t_peak'] is None or self.peak_locs['t_end'] is None:
            print("Warning: Skipping T-wave frequency feature extraction due to missing peak locations.")
            t_wave_frequency_feature = {}
        else:
            t_wave_frequency_feature = calculate_t_wave_frequency_features(
                signal_channels_normalized=self.signal_channels_normalized,
                loc_t_onset=self.peak_locs['t_onset'],
                loc_t_peak=self.peak_locs['t_peak'],
                loc_t_end=self.peak_locs['t_end']
            )

        qrs_family_frequency_features = self._extract_qrs_family_features(
            qrs_feature_func=calculate_qrs_wave_frequency_features,
            signal_data=self.signal_channels_normalized
            # No gap_loc_or_window_length needed for frequency features as per original structure
        )
        return self.build_dict(t_wave_frequency_feature, qrs_family_frequency_features)


    def _calculate_segment_area_features(self, seg_signal: np.ndarray) -> dict:
        """Helper to calculate area-based features for a signal segment."""
        if seg_signal.shape[1] == 0:  # Empty segment
            return {
                'area_total': np.zeros(seg_signal.shape[0]),
                'area_pos': np.zeros(seg_signal.shape[0]),
                'area_neg': np.zeros(seg_signal.shape[0]),
            }

        time_points = np.arange(seg_signal.shape[1])

        pos_mask = seg_signal > 0
        neg_mask = seg_signal < 0

        # Vectorized area calculation
        pos_signals_masked = np.where(pos_mask, seg_signal, 0)
        neg_signals_masked = np.where(neg_mask, seg_signal, 0)

        # np.trapz calculates along axis=1 for each channel
        areas_pos = np.trapz(pos_signals_masked, x=time_points, axis=1)
        areas_neg = np.abs(np.trapz(neg_signals_masked, x=time_points, axis=1))  # Absolute value for negative area
        areas_total = areas_pos + areas_neg  # Sum of magnitudes of positive and negative areas

        return {
            'area_total': areas_total,
            'area_pos': areas_pos,
            'area_neg': areas_neg,
        }

    def get_advanced_features(self) -> dict:
        """Extracts advanced features from P, QRS, and T segments."""
        if self.signal_channels_normalized is None:
            raise ValueError("Normalized signals not available.")

        signal_channels = self.signal_channels_normalized  # Use normalized signals

        # Define segments: name, start_loc_key, end_loc_key
        # For P-wave, the definition is relative to p_peak and q_peak
        p_start = max(0, self.peak_locs.get('p_peak', 0) - 10)
        p_end = min(self.peak_locs.get('p_peak', 0) + 10,
                    self.peak_locs.get('q_peak', p_start + 20))  # ensure p_end > p_start

        segment_definitions = {
            'P': (p_start, p_end),
            'QRS': (self.peak_locs['q_peak'], self.peak_locs['s_peak']),
            'T': (self.peak_locs['t_onset'], self.peak_locs['t_end'])
        }

        features = {'MCG_ID': self.get_mcg_id()}

        for seg_name, (start_loc, end_loc) in segment_definitions.items():
            if start_loc is None or end_loc is None or start_loc >= end_loc:
                print(
                    f"Warning: Skipping advanced features for segment '{seg_name}' due to invalid/missing peak locations or zero duration.")
                # Add NaN placeholders for this segment's features to maintain consistent output structure
                stat_keys = ['max_min_ratio_mean', 'max_min_ratio_std', 'max_min_ratio_median',
                             'area_total_mean', 'area_total_std', 'area_total_median',
                             'area_pos_mean', 'area_pos_std', 'area_neg_mean', 'area_neg_std',
                             'area_ratio', 'has_positive', 'has_negative',
                             'max_area_channel', 'min_area_channel']
                chan_feat_keys = ['max_min_ratio', 'area_total', 'area_pos', 'area_neg']
                for key in stat_keys: features[f'{seg_name}_{key}'] = np.nan
                for ch in range(signal_channels.shape[0]):
                    for feat_key in chan_feat_keys: features[f'{seg_name}_ch{ch}_{feat_key}'] = np.nan
                continue

            seg_signal = signal_channels[:, start_loc:end_loc]  # (num_channels, segment_length)

            if seg_signal.shape[1] == 0:  # Segment is empty
                print(f"Warning: Segment '{seg_name}' is empty after slicing.")
                # Handle similarly to above, adding NaN placeholders
                continue

            # Max/Min ratio features
            max_vals = np.max(seg_signal, axis=1)  # (num_channels,)
            min_vals = np.min(seg_signal, axis=1)  # (num_channels,)

            # Avoid division by zero for ratio; assign 0 or a large number if min_val is 0
            # or if max_val and min_val have different signs making ratio less meaningful.
            # Original logic: np.abs(max_vals / np.where(min_vals != 0, min_vals, 1))
            # A common approach for max/min ratio is to handle signs:
            # If min_vals is 0, ratio is undefined (or inf). If signs differ, ratio is negative.
            # The original code uses abs, so only magnitude matters.
            max_min_ratio = np.zeros_like(max_vals)
            non_zero_min_mask = min_vals != 0
            # Calculate ratio only where min_vals is not zero
            max_min_ratio[non_zero_min_mask] = np.abs(max_vals[non_zero_min_mask] / min_vals[non_zero_min_mask])
            # For min_vals == 0: if max_vals is also 0, ratio is 0 (or NaN). If max_vals !=0, ratio is inf.
            # Original assigns 1.0 where min_vals == 0. Let's stick to that for consistency for now.
            max_min_ratio[~non_zero_min_mask] = 1.0

            # Area features using helper
            area_calcs = self._calculate_segment_area_features(seg_signal)
            areas_total_ch = area_calcs['area_total']
            areas_pos_ch = area_calcs['area_pos']
            areas_neg_ch = area_calcs['area_neg']

            # For statistical summary of channel features
            seg_stats = {}
            summary_metrics_map = {
                "MAX_MIN_CHANNEL_RATIO": max_min_ratio,
                "CHANNEL_AREA_TOTAL": areas_total_ch,
                "CHANNEL_AREA_POS": areas_pos_ch,
                "CHANNEL_AREA_NEG": areas_neg_ch,
            }

            for metric_key, data_array in summary_metrics_map.items():
                if data_array.size > 0:
                    seg_stats[format_feature_name(seg_name, "SIGNAL", metric_key, "MEAN")] = np.mean(data_array)
                    seg_stats[format_feature_name(seg_name, "SIGNAL", metric_key, "STD")] = np.std(data_array)
                    seg_stats[format_feature_name(seg_name, "SIGNAL", metric_key, "MEDIAN")] = np.median(data_array)
                else:
                    seg_stats[format_feature_name(seg_name, "SIGNAL", metric_key, "MEAN")] = np.nan
                    seg_stats[format_feature_name(seg_name, "SIGNAL", metric_key, "STD")] = np.nan
                    seg_stats[format_feature_name(seg_name, "SIGNAL", metric_key, "MEDIAN")] = np.nan

            # Area ratio
            mean_pos_area = np.mean(areas_pos_ch) if areas_pos_ch.size > 0 else 0
            mean_neg_area = np.mean(areas_neg_ch) if areas_neg_ch.size > 0 else 0
            area_ratio_val = 1.0  # Default
            if mean_neg_area > 1e-10:
                area_ratio_val = mean_pos_area / mean_neg_area
            elif mean_pos_area > 1e-10:
                area_ratio_val = 1e4
            seg_stats[format_feature_name(seg_name, "SIGNAL", "CHANNEL_AREA_POS_NEG_RATIO", "VALUE")] = area_ratio_val

            # Other summary stats
            if areas_total_ch.size > 0:
                seg_stats[
                    format_feature_name(seg_name, "SIGNAL", "CHANNEL_AREA_TOTAL", "MAX_VALUE_CHANNEL_IDX")] = np.argmax(
                    areas_total_ch)
                seg_stats[
                    format_feature_name(seg_name, "SIGNAL", "CHANNEL_AREA_TOTAL", "MIN_VALUE_CHANNEL_IDX")] = np.argmin(
                    areas_total_ch)
            else:
                seg_stats[
                    format_feature_name(seg_name, "SIGNAL", "CHANNEL_AREA_TOTAL", "MAX_VALUE_CHANNEL_IDX")] = np.nan
                seg_stats[
                    format_feature_name(seg_name, "SIGNAL", "CHANNEL_AREA_TOTAL", "MIN_VALUE_CHANNEL_IDX")] = np.nan

            seg_stats[format_feature_name(seg_name, "SIGNAL", "HAS_POSITIVE_AREA", "FLAG")] = int(
                np.any(areas_pos_ch > 1e-10))
            seg_stats[format_feature_name(seg_name, "SIGNAL", "HAS_NEGATIVE_AREA", "FLAG")] = int(
                np.any(areas_neg_ch > 1e-10))

            features.update(seg_stats)

            # Per-channel features
            channel_features_map_adv = {  # Renamed to avoid conflict with outer scope if any
                'MAX_MIN_CHANNEL_RATIO': max_min_ratio,
                'CHANNEL_AREA_TOTAL': areas_total_ch,
                'CHANNEL_AREA_POS': areas_pos_ch,
                'CHANNEL_AREA_NEG': areas_neg_ch
            }
            for feat_key, feat_values_ch in channel_features_map_adv.items():
                for ch_idx in range(signal_channels.shape[0]):  # num_channels
                    if feat_values_ch.size > ch_idx:
                        # For per-channel, the statistic_or_peakinfo is effectively the channel index itself
                        features[format_feature_name(seg_name, "SIGNAL", feat_key, channel_idx=ch_idx)] = \
                        feat_values_ch[ch_idx]
                    else:
                        features[format_feature_name(seg_name, "SIGNAL", feat_key, channel_idx=ch_idx)] = np.nan

            # If a segment was skipped due to invalid peaks, fill with NaNs
            # (This logic was already in the refactored get_advanced_features)
            # Ensure the NaN filling uses the new format_feature_name structure
            # Example for NaN filling part:
            # else: # Segment skipped
            #     stat_metrics_nan = ["MAX_MIN_CHANNEL_RATIO", "CHANNEL_AREA_TOTAL", "CHANNEL_AREA_POS", "CHANNEL_AREA_NEG"]
            #     stat_types_nan = ["MEAN", "STD", "MEDIAN"]
            #     for metric_nan in stat_metrics_nan:
            #         for stat_type_nan in stat_types_nan:
            #             features[format_feature_name(seg_name, "SIGNAL", metric_nan, stat_type_nan)] = np.nan
            #     # ... and for other specific keys like MAX_VALUE_CHANNEL_IDX, per-channel values etc.
            # This NaN filling part needs careful implementation to match all generated feature names.

        # Merge with peak distance features (which uses original interpolated data for RT distance)
        features.update(self.get_peak_dist_features(normalized_signal_envelope=signal_channels))
        return features

    def get_peak_dist_features(self, normalized_signal_envelope: np.ndarray) -> dict:
        """Extracts features related to peak locations, distances, and signal envelope."""
        # QT time calculation
        qt_time = None
        if self.peak_locs['t_end'] is not None and self.peak_locs['q_peak'] is not None:
            qt_time = self.peak_locs['t_end'] - self.peak_locs['q_peak']
            if qt_time <= 0:  # Prevent division by zero or nonsensical ratios
                print(f"Warning: QT interval is non-positive ({qt_time}). Ratios will be NaN.")
                qt_time = np.nan
        else:
            qt_time = np.nan

        # Calculate time differences, handle Nones
        qr_time = (self.peak_locs['r_peak'] - self.peak_locs['q_peak']) if all(
            self.peak_locs.get(k) is not None for k in ['r_peak', 'q_peak']) else np.nan
        rs_time = (self.peak_locs['s_peak'] - self.peak_locs['r_peak']) if all(
            self.peak_locs.get(k) is not None for k in ['s_peak', 'r_peak']) else np.nan
        tt_interval = (self.peak_locs['t_end'] - self.peak_locs['t_onset']) if all(
            self.peak_locs.get(k) is not None for k in ['t_end', 't_onset']) else np.nan
        qrs_duration = (self.peak_locs['s_peak'] - self.peak_locs['q_peak']) if all(
            self.peak_locs.get(k) is not None for k in ['s_peak', 'q_peak']) else np.nan

        rt_euclidean_dist = np.nan
        rt_cosine_sim = np.nan
        if all(self.peak_locs.get(k) is not None for k in ['r_peak', 't_peak']) and self.interpolated_data is not None:
            # Ensure peaks are within bounds of interpolated_data
            r_idx, t_idx = self.peak_locs['r_peak'], self.peak_locs['t_peak']
            if 0 <= r_idx < len(self.interpolated_data) and 0 <= t_idx < len(self.interpolated_data):
                rt_euclidean_dist = self.euclidean_distance(
                    self.interpolated_data[r_idx],
                    self.interpolated_data[t_idx]
                )
                rt_cosine_sim = self.cosine_similarity(
                    self.interpolated_data[r_idx],
                    self.interpolated_data[t_idx]
                )
            else:
                print("Warning: R or T peak out of bounds for interpolated_data.")

        # Envelope similarity using the passed normalized signal
        # The original code used self.signal_channels, which would be normalized by get_init_features
        max_values_env = np.max(normalized_signal_envelope, axis=0)  # Max across channels for each time point
        min_values_env = -np.min(normalized_signal_envelope, axis=0)  # Min across channels (inverted)
        cosine_similarity_envelope = self.cosine_similarity(max_values_env, min_values_env)

        peak_features = {}
        # MCG_ID is handled at a higher level or kept simple
        # peak_features['MCG_ID'] = self.get_mcg_id()

        # Time Ratios
        ratios_map = {
            "QR": qr_time, "RS": rs_time, "TT": tt_interval, "QRS": qrs_duration
        }
        for seg_prefix, time_val in ratios_map.items():
            ratio = time_val / qt_time if not np.isnan(time_val) and not np.isnan(qt_time) and qt_time != 0 else np.nan
            peak_features[format_feature_name(seg_prefix, "PEAKDURATION", "RATIO_VS_QTINTERVAL", "VALUE")] = ratio

        # Peak Locations
        peak_loc_map = {
            "P": self.peak_locs['p_peak'], "Q": self.peak_locs['q_peak'],
            "R": self.peak_locs['r_peak'], "S": self.peak_locs['s_peak'],
            "T_ONSET": self.peak_locs['t_onset'], "T": self.peak_locs['t_peak'],
            # Changed "TPEAK" to "T" for segment part
            "T_END": self.peak_locs['t_end']
        }
        for peak_name_key, loc_val in peak_loc_map.items():
            # Use segment name from key, e.g. P from P, T from T_PEAK
            segment_for_name = peak_name_key.split('_')[0]  # "P", "Q", "R", "S", "T"
            metric_suffix = peak_name_key.replace(segment_for_name, "").replace("_", "")  # "ONSET", "END", "" for peak
            metric_name = f"LOCATION_INDEX{'_' + metric_suffix if metric_suffix else ''}"
            peak_features[format_feature_name(segment_for_name, "PEAK", metric_name, "VALUE")] = loc_val

        # RT Distances/Similarities
        peak_features[format_feature_name("RT", "INTERPOLATEDSIGNAL", "EUCLIDEAN_DISTANCE_PEAKPOINTS",
                                          "VALUE")] = rt_euclidean_dist
        peak_features[
            format_feature_name("RT", "INTERPOLATEDSIGNAL", "COSINE_SIMILARITY_PEAKPOINTS", "VALUE")] = rt_cosine_sim

        # Envelope Similarity
        peak_features[format_feature_name("FULLWAVE", "CHANNELENVELOPE", "MAX_MIN_COSINE_SIMILARITY",
                                          "VALUE")] = cosine_similarity_envelope

        return peak_features

        # return {
        #     'MCG_ID': self.get_mcg_id(),  # Already added in get_advanced_features, but can be redundant
        #     'qr_div_qt': qr_time / qt_time if not np.isnan(qr_time) and not np.isnan(qt_time) else np.nan,
        #     'rs_div_qt': rs_time / qt_time if not np.isnan(rs_time) and not np.isnan(qt_time) else np.nan,
        #     'tt_div_qt': tt_interval / qt_time if not np.isnan(tt_interval) and not np.isnan(qt_time) else np.nan,
        #     'qrs_div_qt': qrs_duration / qt_time if not np.isnan(qrs_duration) and not np.isnan(qt_time) else np.nan,
        #     'loc_p_peak': self.peak_locs['p_peak'],
        #     'loc_q_peak': self.peak_locs['q_peak'],
        #     'loc_r_peak': self.peak_locs['r_peak'],
        #     'loc_s_peak': self.peak_locs['s_peak'],
        #     'loc_t_onset': self.peak_locs['t_onset'],
        #     'loc_t_peak': self.peak_locs['t_peak'],
        #     'loc_t_end': self.peak_locs['t_end'],
        #     'RT_euclidean_distance': rt_euclidean_dist,
        #     'RT_cosine_similarity': rt_cosine_sim,
        #     'max_min_simlar_distance': cosine_similarity_envelope
        #     # Renamed from 'max_min_simlar_distance' to 'max_min_envelope_similarity' for clarity
        # }
        # ... (previous code) ...

    def get_init_features(self) -> dict:
        """
        Orchestrates the extraction of all initial features.
        Normalization of signal_channels happens here.
        """
        # Step 1: Normalize signal channels. This state is stored in self.signal_channels_normalized
        # and used by subsequent feature extractors.
        if self.signal_channels_original is None:
            raise ValueError("Original signal channels not available for normalization.")
        self.signal_channels_normalized = self.unified_normalization(self.signal_channels_original)

        # Step 2: Extract various feature sets
        # Advanced features (P, QRS, T segment stats) and peak distance features
        # Note: get_advanced_features internally calls get_peak_dist_features
        advanced_and_peak_dist_features = self.get_advanced_features()

        # EEG features (uses normalized signals)
        if self.signal_channels_normalized is None:  # Should be set by now
            eeg_features = {}  # Or handle error
        else:
            eeg_features = calculate_eeg_features_std_names(self.signal_channels_normalized)

        # Time, Entropy, and Frequency features for T, QRS, QR, RS waves
        # These methods now use self.signal_channels_normalized and self.interpolated_data internally
        time_domain_wave_features = self.get_wave_time_features()
        entropy_wave_features = self.get_entropy_features()
        frequency_wave_features = self.get_frequency_features()

        # Step 3: Combine all features
        # The order of build_dict matters if there are overlapping keys (last one wins)
        # MCG_ID might be present in advanced_and_peak_dist_features already.
        all_features = self.build_dict(
            advanced_and_peak_dist_features,  # Contains MCG_ID
            eeg_features,
            time_domain_wave_features,
            entropy_wave_features,
            frequency_wave_features
        )

        # Ensure MCG_ID is present, e.g. from get_advanced_features or add it here if necessary
        if 'MCG_ID' not in all_features:
            all_features['MCG_ID'] = self.get_mcg_id()

        # print all feature names
        # for key in all_features:
        #     print(key, all_features[key])

        return all_features
