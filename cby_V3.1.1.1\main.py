"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/12/13 16:30
desc:
    综合参数和可视化
environment: py39
run:
    python ParamsServer.py
"""


from utils.get_metrics import *
from ParamsServer import Server


if __name__ == '__main__':
    mcgdir = './static/mcg10'
    rtsdir = './static/rts10'
    ids = ['SHLY_2024_002080', 'BJ_TT_000614', '464-王雅', 'SL_B_001075-姜声英', 'SHSY_2023_000467', 'BJ_TT_002610', 'AHYK_2023_000644', 'SL_DSA_001592', '109-张玉龙', 'BJ_TT_001097']
    lls = [['ID', 'time_raw', 'waveform', 'mcgpole', 'disps1', 'results', 'time_new', 'matrixes', 'mts_new15']]
    for item in ids:
        print('————%s:' % item)
        savedir = '%s/%s' % (rtsdir, item)
        new_folder(savedir)
        filename = '%s/%s.txt' % (mcgdir, item)
        my_server = Server()
        # 历史版本-1.时刻点
        ll = [item]
        t1 = time.time()
        value = my_server.get_times1(filename)
        ll.append(time.time()-t1)  # 运行时间
        print(value)  # 打印结果
        with open('%s/time_raw.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-2.波形描述参数
        t1 = time.time()
        data_dict = {}
        # data_dict = {'Q': 252, 'R': 277, 'S': 312, 'T_on': 464, 'T': 535, 'T_end': 570}  # 输入模板
        waveform = my_server.get_waveform(filename, data_dict)
        ll.append(time.time()-t1)
        print(waveform)
        with open('%s/waveform.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-3.磁图描述参数
        t1 = time.time()
        mcgpole = my_server.get_mcgpole(filename, data_dict)
        ll.append(time.time()-t1)
        print(mcgpole)
        with open('%s/mcgpole.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-4.离散度参数
        t1 = time.time()
        value = my_server.get_disps1(filename, data_dict)
        ll.append(time.time()-t1)
        print(value)
        with open('%s/disps1.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-5.心磁结论
        t1 = time.time()
        paras = {}
        # paras = {
        #     'Q_index': -0.022053412822669,
        #     'S_index': 0.843123855929008,
        #     'TT_index': 1.83742183051828,
        #     'Q_angle': -152.1,
        #     'R_angle': -11.8,
        #     'S_angle': 160.3,
        #     'T_angle': -52.5,
        #     'QRyc': 0,
        #     'RSyc': 0,
        #     'Tyc': 0,
        #     'score': 0.210579261183739}  # 输入模板
        paras2 = {'waveform': waveform, 'mcgpole': mcgpole}
        value = my_server.get_results(filename, paras, data_dict, paras2)
        ll.append(time.time()-t1)
        print(value)
        with open('%s/results.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 新版本-1.时刻点
        t1 = time.time()
        doc = open(filename, 'r')
        filelines = doc.readlines()  # 以txt文件的行字符串为输入: 原始36通道数据
        doc.close()
        ts = my_server.get_tms250805(filelines)
        ll.append(time.time()-t1)
        print(ts)
        with open('%s/time_new.json' % savedir, "w") as f:  # 保存结果
            json.dump(ts, f, indent=4)
        # 新版本-2.插值
        t1 = time.time()
        ms = my_server.get_matrix250805(filename)
        ll.append(time.time()-t1)
        print(ms['Code'])  # 矩阵太大, 不直接打印
        with open('%s/matrixes.pkl' % savedir, 'wb') as f:  # 保存结果
            pickle.dump(ms['matrixes'], f)
        # 新版本-3.新参数
        with open('%s/matrixes.pkl' % savedir, 'rb') as f:  # 读取插值数据
            matrixes = pickle.load(f)
        times = []
        # times = [39, 79, 156, 251, 273, 290, 319, 349, 430, 488, 546, 608, 674]
        t1 = time.time()
        mts = my_server.get_mts250805(filelines, matrixes, times)
        ll.append(time.time()-t1)
        print(mts)
        with open('%s/mts_new15.json' % savedir, "w") as f:  # 保存结果
            json.dump(mts, f, indent=4)
        ll = ll[:1]+[round(t,6) for t in ll[1:]]
        lls.append(ll)
    from utils.get_mts20250804 import list_to_xlsx  # 导出运行时间
    ll = ['Avg.Time']
    ll += [round(np.average([lls[j][i] for j in range(1, len(lls))]),6) for i in range(1, len(lls[0]))]
    lls.append(ll)
    list_to_xlsx(lls, './static/runtimes.xlsx')
