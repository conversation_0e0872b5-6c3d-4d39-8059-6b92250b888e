"""
@Project ：ML_Pipline
@File    ：cross_sample_features.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2024/8/26 上午10:29
@Discribe：
"""
from typing import Dict, Optional, Any, List

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import shap
from joblib import dump, load
from sklearn.decomposition import PCA, FastICA, TruncatedSVD
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import KBinsDiscretizer
from sklearn.svm import SVC

from feature_selector.select_utils import DataScaler
from model_trainer.models import (load_and_prepare_data, process_safe_features, Models,
                                  get_model_predict_results, plot_folds_results)


def merge_features(train_feature):
    merged_data = []
    feature_names = []

    for group_name, feature_array in train_feature.items():
        if feature_array.ndim == 1:
            feature_array = feature_array.reshape(-1, 1)
        n_features = feature_array.shape[1]
        # Create feature names with group name and index
        group_feature_names = [f"{group_name}_{i}" for i in range(n_features)]
        feature_names.extend(group_feature_names)
        # Append the feature array
        print('append feature name:', group_feature_names)
        merged_data.append(feature_array)

    # Concatenate all feature arrays along the second axis (columns)
    merged_data = np.hstack(merged_data)

    # Convert to DataFrame for easier handling
    merged_df = pd.DataFrame(merged_data, columns=feature_names)

    return merged_df


class CrossSampleFeatureManager:
    """
    A class for managing cross-sample feature extraction and dimensionality reduction.

    This class provides methods to fit various models on training data and transform
    new data using these fitted models. It supports both supervised and unsupervised
    methods, including PCA, SVD, GMM, LDA, SVM, and ICA.

    Attributes:
        models (Dict[str, Any]): Dictionary to store fitted models.
        version (str): Version of the manager.
        use_raw_data (bool): Flag to indicate whether to use raw data.
        normalization_method (str): Method used for data normalization.
        scaler (DataScaler): DataScaler object for feature preprocessing.
        active_methods (List[str]): List of active feature extraction methods.
    """

    def __init__(self, normalization_method: str = 'softmax', version: str = '1.0'):
        """
        Initialize the CrossSampleFeatureManager.

        Args:
            normalization_method (str): Method used for data normalization. Defaults to 'softmax'.
            version (str): Version of the manager. Defaults to '1.0'.
        """
        self.models: Dict[str, Any] = {}
        self.version: str = version
        self.use_raw_data: bool = False
        self.normalization_method: str = normalization_method
        self.scaler: DataScaler = DataScaler()
        self.active_methods: List[str] = []

    def preprocess_features(self, X: np.ndarray) -> np.ndarray:
        """
        Preprocess the input features using the specified normalization method.

        Args:
            X (np.ndarray): Input features to preprocess.

        Returns:
            np.ndarray: Preprocessed features.

        Raises:
            ValueError: If there's an error during preprocessing.
        """
        try:
            return self.scaler.scale_data(X, method=self.normalization_method)
        except ValueError as e:
            raise ValueError(f"Error in preprocessing features: {str(e)}")

    def fit_model(self, model_name: str, model: Any, X: np.ndarray, y: Optional[np.ndarray] = None) -> None:
        """
        Fit a model and store it in the models dictionary.

        Args:
            model_name (str): Name of the model.
            model (Any): The model object to fit.
            X (np.ndarray): Input features.
            y (Optional[np.ndarray]): Target values, if applicable.
        """
        print(f"Fitting {model_name}...")
        if y is not None and model_name in ['lda', 'svm']:
            model.fit(X, y)
        else:
            model.fit(X)
        self.models[model_name] = model
        self.active_methods.append(model_name)

    def fit(self, X: np.ndarray, y: Optional[np.ndarray] = None, raw_data: Optional[np.ndarray] = None,
            n_components_dict: Optional[Dict[str, Any]] = None) -> None:
        """
        Fit selected cross-sample feature models based on n_components_dict.

        Args:
            X (np.ndarray): Input features.
            y (Optional[np.ndarray]): Target values for supervised methods.
            raw_data (Optional[np.ndarray]): Raw data for additional feature extraction.
            n_components_dict (Optional[Dict[str, Any]]): Dictionary specifying the number of components for each model.
        """
        if n_components_dict is None:
            n_components_dict = {}

        print(f"Fitting cross sample features... (normalization method: {self.normalization_method})")
        X_preprocessed = self.preprocess_features(X)

        # Clear previous active methods
        self.active_methods.clear()

        # Fit models based on n_components_dict
        if 'pca' in n_components_dict:
            self.fit_model('pca', PCA(n_components=n_components_dict['pca']), X_preprocessed)

        if 'svd' in n_components_dict:
            self.fit_model('svd', TruncatedSVD(n_components=n_components_dict['svd']), X_preprocessed)

        if 'ica' in n_components_dict:
            self.fit_model('ica', FastICA(n_components=n_components_dict['ica'], max_iter=10000), X_preprocessed)

        if 'gmm' in n_components_dict:
            if 'pca' in self.active_methods:
                gmm_data = self.models['pca'].transform(X_preprocessed)
            else:
                gmm_data = X_preprocessed
            self.fit_model('gmm', GaussianMixture(n_components=n_components_dict['gmm']), gmm_data)

        if y is not None:
            if 'lda' in n_components_dict:
                self.fit_model('lda', LinearDiscriminantAnalysis(n_components=n_components_dict['lda']), X_preprocessed,
                               y)
            if 'svm' in n_components_dict:
                self.fit_model('svm', SVC(kernel=n_components_dict.get('svm', 'linear')), X_preprocessed, y)

        if raw_data is not None and self.use_raw_data:
            self.fit_raw_data_features(raw_data)

        print(f"Fitting complete! Active methods: {', '.join(self.active_methods)}")

    def transform(self, X: np.ndarray, raw_data: Optional[np.ndarray] = None) -> Dict[str, np.ndarray]:
        """
        Transform input data using fitted feature models.

        Args:
            X (np.ndarray): Input features to transform.
            raw_data (Optional[np.ndarray]): Raw data for additional feature extraction.

        Returns:
            Dict[str, np.ndarray]: Dictionary of transformed features.
        """
        print(f"Transforming cross sample features... (normalization method: {self.normalization_method})")
        X_preprocessed = self.preprocess_features(X)

        features = {}
        for model_name in self.active_methods:
            print(f"Transforming {model_name}...")
            if model_name in ['pca', 'svd', 'ica', 'lda']:
                features[model_name] = self.models[model_name].transform(X_preprocessed)
            elif model_name == 'gmm':
                gmm_data = features.get('pca', X_preprocessed)
                features['gmm_score'] = self.models['gmm'].score_samples(gmm_data)
            elif model_name == 'svm':
                features['svm'] = self.models['svm'].predict(X_preprocessed)

        if raw_data is not None and self.use_raw_data:
            raw_features = self.transform_raw_data(raw_data)
            features.update(raw_features)

        print(f"Transformation complete! Features: {', '.join(features.keys())}")
        return features

    def fit_raw_data_features(self, raw_data: np.ndarray) -> None:
        """
        Fit features using raw data.

        Args:
            raw_data (np.ndarray): Raw input data.
        """
        # Implement raw data feature extraction here
        pass

    def transform_raw_data(self, raw_data: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Transform raw data into features.

        Args:
            raw_data (np.ndarray): Raw input data.

        Returns:
            Dict[str, np.ndarray]: Dictionary of extracted features from raw data.
        """
        # Implement raw data feature transformation here
        return {}

    def set_use_raw_data(self, use_raw: bool) -> None:
        """
        Set whether to use raw data for feature extraction.

        Args:
            use_raw (bool): Flag to indicate whether to use raw data.
        """
        self.use_raw_data = use_raw

    def save_models(self, path: str) -> None:
        """
        Save all fitted models to a file.

        Args:
            path (str): Path to save the models.
        """
        dump({'models': self.models, 'active_methods': self.active_methods},
             f"{path}/cross_sample_models_{self.version}.joblib")

    def load_models(self, path: str) -> None:
        """
        Load all models from a file.

        Args:
            path (str): Path to load the models from.
        """
        loaded_data = load(f"{path}/cross_sample_models_{self.version}.joblib")
        self.models = loaded_data['models']
        self.active_methods = loaded_data['active_methods']

    def update_version(self, new_version: str) -> None:
        """
        Update the version of the manager.

        Args:
            new_version (str): New version string.
        """
        self.version = new_version


def vis_cross_features(train_features, test_features, Y_train, Y_test):
    # 在训练集上区分度
    gmm_f = train_features['gmm_score']
    plt.scatter(gmm_f, Y_train * gmm_f, c=Y_train)
    plt.title("GMM train")
    plt.show()

    pca_f = train_features['pca']
    plt.scatter(pca_f[:, 1], pca_f[:, 2], c=Y_train)
    plt.title("PCA train")
    plt.show()

    lda_f = train_features['lda'][:, 0]
    plt.scatter(lda_f, lda_f, c=Y_train)
    plt.title("LDA train")
    plt.show()

    ica_f = train_features['ica']
    plt.scatter(ica_f[:, 0], ica_f[:, 1], c=Y_train)
    plt.title("ICA train")
    plt.show()

    # 在测试集上区分度 ------------------
    gmm_f = test_features['gmm_score']
    plt.scatter(gmm_f, Y_test * gmm_f, c=Y_test)
    plt.title("GMM test")
    plt.show()

    pca_f = test_features['pca']
    plt.scatter(pca_f[:, 1], pca_f[:, 2], c=Y_test)
    plt.title("PCA test")
    plt.show()

    lda_f = test_features['lda'][:, 0]
    plt.scatter(lda_f, lda_f, c=Y_test)
    plt.title("LDA test")
    plt.show()

    ica_f = test_features['ica']
    plt.scatter(ica_f[:, 0], ica_f[:, 1], c=Y_test)
    plt.title("ICA test")
    plt.show()


def vis_score_on_pred(model, merged_test_df, Y_test):
    # 在测试集上进行预测
    y_pred = model.predict(merged_test_df)

    # 假设'gmm_score_0'是第0列
    gmm_score_0 = merged_test_df['gmm_score_0']

    # 创建正确和错误的布尔索引
    correct = (Y_test == y_pred).to_numpy()
    incorrect = ~correct

    # 确保 Y_test 也是 numpy 数组
    Y_test_np = Y_test.to_numpy()

    # 创建一个3x2的子图布局
    fig, axs = plt.subplots(3, 2, figsize=(8, 10))

    # 1. 散点图：正确vs错误分类
    axs[0, 0].scatter(gmm_score_0[correct], Y_test[correct], c='green', label='正确分类', alpha=0.6, s=2)
    axs[0, 0].scatter(gmm_score_0[incorrect], Y_test[incorrect], c='red', label='错误分类', alpha=0.6, s=2)
    axs[0, 0].set_xlabel('gmm_score_0')
    axs[0, 0].set_ylabel('实际类别')
    axs[0, 0].set_title('gmm_score_0 vs 分类结果')
    axs[0, 0].legend()

    # 2. 正确分类数据的分布图（区分正负样本）
    sns.histplot(gmm_score_0[correct & (Y_test_np == 1)], ax=axs[1, 1], kde=True, color='darkgreen',
                 label='正确分类-正样本', alpha=0.5)
    sns.histplot(gmm_score_0[correct & (Y_test_np == 0)], ax=axs[1, 1], kde=True, color='lightgreen',
                 label='正确分类-负样本', alpha=0.5)
    axs[1, 1].set_title('正确分类数据的分布 (正/负样本)')
    axs[1, 1].set_xlabel('gmm_score_0')
    axs[1, 1].legend()

    # 3. 错误分类数据的分布图（区分正负样本）
    sns.histplot(gmm_score_0[incorrect & (Y_test_np == 1)], ax=axs[1, 0], kde=True, color='darkred',
                 label='错误分类-正样本', alpha=0.5)
    sns.histplot(gmm_score_0[incorrect & (Y_test_np == 0)], ax=axs[1, 0], kde=True, color='lightcoral',
                 label='错误分类-负样本', alpha=0.5)
    axs[1, 0].set_title('错误分类数据的分布 (正/负样本)')
    axs[1, 0].set_xlabel('gmm_score_0')
    axs[1, 0].legend()

    # 4. 正确和错误分类数据的对比分布图（区分正负样本）
    # sns.kdeplot(gmm_score_0[correct & (Y_test_np == 1)], ax=axs[1, 1], color='darkgreen', label='正确分类-正样本')
    # sns.kdeplot(gmm_score_0[correct & (Y_test_np == 0)], ax=axs[1, 1], color='lightgreen', label='正确分类-负样本')
    # sns.kdeplot(gmm_score_0[incorrect & (Y_test_np == 1)], ax=axs[1, 1], color='darkred', label='错误分类-正样本')
    # sns.kdeplot(gmm_score_0[incorrect & (Y_test_np == 0)], ax=axs[1, 1], color='lightcoral', label='错误分类-负样本')
    # axs[1, 1].set_title('正确vs错误分类数据的分布对比 (正/负样本)')
    # axs[1, 1].set_xlabel('gmm_score_0')
    # axs[1, 1].legend()
    sns.kdeplot(gmm_score_0[correct], ax=axs[0, 1], color='darkgreen', label='正确分类')
    sns.kdeplot(gmm_score_0[incorrect], ax=axs[0, 1], color='darkred', label='错误分类')
    axs[0, 1].set_title('正确vs错误分类数据的分布对比 (全样本)')
    axs[0, 1].set_xlabel('gmm_score_0')
    axs[0, 1].legend()
    # 5. 正样本的正确和错误分类对比
    sns.kdeplot(gmm_score_0[correct & (Y_test_np == 1)], ax=axs[2, 0], color='darkgreen', label='正确分类-正样本')
    sns.kdeplot(gmm_score_0[incorrect & (Y_test_np == 1)], ax=axs[2, 0], color='darkred', label='错误分类-正样本')
    axs[2, 0].set_title('正样本: 正确 vs 错误分类')
    axs[2, 0].set_xlabel('gmm_score_0')
    axs[2, 0].legend()

    # 6. 负样本的正确和错误分类对比
    sns.kdeplot(gmm_score_0[correct & (Y_test_np == 0)], ax=axs[2, 1], color='lightgreen', label='正确分类-负样本')
    sns.kdeplot(gmm_score_0[incorrect & (Y_test_np == 0)], ax=axs[2, 1], color='lightcoral', label='错误分类-负样本')
    axs[2, 1].set_title('负样本: 正确 vs 错误分类')
    axs[2, 1].set_xlabel('gmm_score_0')
    axs[2, 1].legend()

    plt.tight_layout()
    plt.show()


def engineer_gmm_features(df, gmm_col='gmm_score_0', manual_thresholds=None, n_bins=4):
    """
    对 GMM 特征进行工程处理

    :param df: 包含 GMM 特征的 DataFrame
    :param gmm_col: GMM 特征列名
    :param manual_thresholds: 手动设置的阈值列表，如 [-72, -67]
    :param n_bins: 自动分箱的数量（当 manual_thresholds 为 None 时使用）
    :return: 添加了新特征的 DataFrame
    """
    df = df.copy()

    # 1. 分箱处理
    if manual_thresholds:
        df['gmm_binned'] = pd.cut(df[gmm_col], bins=[-np.inf] + manual_thresholds + [np.inf],
                                  labels=range(len(manual_thresholds) + 1)).to_numpy()
    else:
        kbd = KBinsDiscretizer(n_bins=n_bins, encode='ordinal', strategy='quantile')
        df['gmm_binned'] = kbd.fit_transform(df[[gmm_col]])

    # 2. 创建与阈值的距离特征
    if manual_thresholds:
        for i, threshold in enumerate(manual_thresholds):
            df[f'gmm_dist_to_threshold_{i}'] = (df[gmm_col] - threshold).abs()

    # 3. 特征交互
    for col in df.columns:
        if col != gmm_col and col != 'gmm_binned' and not col.startswith('gmm_dist_to_threshold'):
            df[f'gmm_interaction_{col}'] = df[gmm_col] * df[col]

    return df


def process_data_with_gmm(train_df, test_df, gmm_col='gmm_score_0', manual_thresholds=[-72, -67]):
    # 处理训练集
    new_train_df = engineer_gmm_features(train_df, gmm_col, manual_thresholds)

    # 处理测试集
    new_test_df = engineer_gmm_features(test_df, gmm_col, manual_thresholds)

    return new_train_df, new_test_df


def emphasize_gmm_features(X,  emphasis_score = 2.0):
    return [emphasis_score if feature.startswith('gmm_') else 1.0 for feature in X.columns]


def analyze_feature_importance_with_shap(model, X, plot_type='bar'):
    """
    使用 SHAP 值分析特征重要性

    :param model: 训练好的模型（假设是 XGBoost 模型）
    :param X: 特征数据框
    :param plot_type: 图表类型，可以是 'bar' 或 'beeswarm'
    """
    # 创建 SHAP 解释器
    explainer = shap.TreeExplainer(model)
    shap_values = explainer.shap_values(X)

    # 绘制 SHAP 值摘要图
    if plot_type == 'bar':
        shap.summary_plot(shap_values, X, plot_type="bar", show=False)
    elif plot_type == 'beeswarm':
        shap.summary_plot(shap_values, X, show=False)
    else:
        raise ValueError("Unsupported plot type. Choose 'bar' or 'beeswarm'.")

    plt.title("SHAP Feature Importance")
    plt.tight_layout()
    plt.show()

    # 返回 SHAP 值，以便进行进一步分析
    return shap_values


if __name__ == '__main__':
    plt.rcParams['font.family'] = 'SimHei'
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
    # ----------------------------- 加载所有训练集id的 特征以及相应标签 作为训练数据 -----------------------------------
    results = []
    models_trained = []
    fold = 3
    norm_methods = ['StandardScaler',  # 'MinMaxScaler', 'MaxAbsScaler', 'Normalizer', 'PowerTransformer (Yeo-Johnson)',
                    'QuantileTransformer (normal)', 'SigmoidTransformer']
    tmp_comp_dict = {
        'pca': 15,
        # 'svd': 5,
        'gmm': 5,
        'lda': 1,
        'ica': 30,
        # 'svm': 'sigmoid'
    }
    for fold in range(5):
        # for norm_method in norm_methods:
        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl="./files/data/data_index/data_V0826_cons/S42-F5.pkl",
            features_pkl="./files/saved_features/features_V0821_all_tspt_2534.pkl",
            selected_features_pkl="./files/saved_features/features_V0821_all_tspt_2534.pkl",
            # selected_features_pkl="./files/saved_features/selected_features/features_V0826F5S42_tspt_boruta_top1369.pkl",
            # selected_features_pkl="./files/saved_features/selected_features/features_V0826F5S42_tstt_stable_top300.pkl", # 经选择特征也可以试试。
            selected_feature_list=None,
            fold=fold)

        # 获取无异常字符的安全特征
        safe_train_data, safe_test_data, safe_valid_data, safe_train_valid_data = [
            process_safe_features(x) for x in [train_data, test_data, valid_data, train_valid_data]]
        X_train, Y_train, X_test, Y_test = safe_train_valid_data, train_valid_data['label'], safe_test_data, test_data[
            'label']

        # x_outer_test =process_safe_features(test_df)
        # ------------------------------ 训练跨样本特征的中间模型 ------------------------------------------------
        manager = CrossSampleFeatureManager(normalization_method='StandardScaler')
        manager.fit(X_train, Y_train, n_components_dict=tmp_comp_dict)

        # 转化获取训练集特征、测试集特征
        train_features = manager.transform(X_train)
        test_features = manager.transform(X_test)
        # outer_test_features = manager.transform(x_outer_test)

        # ----------------------------------- 可视化 --------------------------------------------------------
        # vis_cross_features(train_features, test_features, Y_train, Y_test)

        # ----------------------------------- 测试跨样本特征的局部精度表现 ------------------------------------
        # 合并特征为dataframe
        merged_train_df = merge_features(train_features)
        merged_test_df = merge_features(test_features)
        # merged_outer_test_df = merge_features(outer_test_features)

        # --------------------- 处理GMM特征测试
        new_train_df_gmm, new_test_df_gmm = process_data_with_gmm(merged_train_df, merged_test_df,
                                                                  )#manual_thresholds=None

        # new_outer_test_df = engineer_gmm_features(merged_outer_test_df, gmm_col='gmm_score_0', manual_thresholds=[-72, -67])

        # 测试这些特征的局部精度表现
        models = Models()
        models.xgb_model()
        model, model_name = models.model, models.model_name
        # model.fit(new_train_df.drop(columns = ['label','mcg_file']), train_valid_data['label'])

        # feature_weights = emphasize_gmm_features(new_train_df_gmm,emphasis_score=0.1)
        # model.fit(new_train_df_gmm, train_valid_data['label'], feature_weights=feature_weights)

        # shap_values = analyze_feature_importance_with_shap(model, new_train_df_gmm, plot_type='beeswarm')
        # vis_score_on_pred(model, new_test_df_gmm, Y_test)

        # 将特征合并进原始特征，根据id合并 -----

        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl="./files/data/data_index/data_V0826_cons/S42-F5.pkl",
            features_pkl="./files/saved_features/features_V0821_all_tspt_2534.pkl",
            selected_features_pkl="./files/saved_features/selected_features/features_V0826F5S42_tspt_boruta_top1369.pkl",
            # selected_features_pkl="./files/saved_features/selected_features/features_V0826F5S42_tstt_stable_top300.pkl", # 经选择特征也可以试试。
            selected_feature_list=None,
            fold=fold)


        new_train_df = pd.merge(train_valid_data, new_train_df_gmm, how='left', left_index=True, right_index=True)
        new_test_df = pd.merge(test_data, new_test_df_gmm, how='left', left_index=True, right_index=True)
        new_train_valid = pd.concat([new_train_df, new_test_df], axis=0)
        # new_outer_test_df = pd.merge(test_df, new_outer_test_df,how='left', left_index=True, right_index=True)
        # new_features = pd.concat([new_train_valid, new_outer_test_df], axis=0)
        # import pickle
        # with open("./files/saved_features/features_V0826F5S42_boruta_top1369_embed96.pkl", 'wb') as f:
        #     pickle.dump(new_features, f)
        # 合并训练/测试数据
        model.fit(new_train_df.drop(columns = ['label','mcg_file']), train_valid_data['label'])
        # ---------------------

        result_dict = get_model_predict_results(model=model,
                                                x_train=new_train_df.drop(columns = ['label','mcg_file']),
                                                y_train=train_valid_data['label'],
                                                x_test=new_test_df.drop(columns = ['label','mcg_file']),
                                                y_test=test_data['label'],
                                                )
        # result_dict = get_model_predict_results(model=model,
        #                                         x_train=new_train_df,
        #                                         y_train=train_valid_data['label'],
        #                                         x_test=new_test_df,
        #                                         y_test=test_data['label'],
        #                                         )
        results.append(result_dict)
        models_trained.append(model)

    avg = plot_folds_results(results, mode='valid_result_threshold')

    save_res = {'models_trained': models_trained, 'results': results}
    # 保存跨样本特征的中间模型
    manager.save_models("path/to/save")

    # 加载跨样本特征的中间模型
    # manager.load_models("path/to/save")

    # 对新样本应用特征转换
