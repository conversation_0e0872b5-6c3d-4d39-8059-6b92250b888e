'''
@Project ：pythonProject 
@File    ：fun_colletion.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/7/29 9:39 
'''
import math
import numpy as np
pi = math.pi


"""
用于插值
"""


def cubic_splines(x, ys):
    """
    三次样条插值函数
    :param x: 输入数组 X
    :param ys: 输入数组 Y
    :return z: 数据点处的二阶导数
    """
    ys = np.reshape(ys, (-1, 6))
    dy = np.diff(ys, axis=-1)
    dx = np.diff(x)

    n = x.shape[0]
    A = np.zeros((n, n))
    B = np.zeros(shape=(ys.shape[0], n))
    A[0, 0] = 1
    A[n - 1, n - 1] = 1
    for i in range(1, n - 1):
        A[i, i - 1] = dx[i - 1]
        A[i, i] = 2 * (dx[i - 1] + dx[i])
        A[i, i + 1] = dx[i]
        B[:, i] = 6 * ((dy[:, i] / dx[i]) - (dy[:, i - 1] / dx[i - 1]))

    As = np.tile(A, (B.shape[0], 1, 1))
    zs = np.linalg.solve(As, B)
    return zs


def interpolates(x, y, z, x_new):
    """
    插值函数
    :param x: 输入数组 X
    :param y: 输入数组 Y
    :param z: 数据点处的二阶导数
    :param x_new: 要求插值的点
    :return y_new : 对应的插值值
    """
    n = x.shape[0]
    h = np.diff(x)
    idx = np.searchsorted(x, x_new) - 1
    idx = np.clip(idx, 0, n-2)

    A = (x[idx + 1] - x_new) / h[idx]
    B = 1 - A
    C = (1 / 6) * (A ** 3 - A) * h[idx] ** 2
    D = (1 / 6) * (B ** 3 - B) * h[idx] ** 2
    y_new = A * y[:, idx] + B * y[:, idx + 1] + C * z[:, idx] + D * z[:, idx + 1]
    return y_new


def mcg_interpolate(d_ori, num):
    X = np.array([0, 4, 8, 12, 16, 20])
    Y = np.array([0, 4, 8, 12, 16, 20])
    values = np.linspace(0, 20, num + 1)  # [1:]
    Xi = np.tile(values, (num + 1, 1))
    Yi = Xi.T
    Bs = d_ori.reshape((-1, 6, 6))

    Zi = np.zeros((Bs.shape[0], Xi.shape[0], Xi.shape[1]))
    z_x = np.array([cubic_splines(X, Bs[:, i, :]) for i in range(Bs.shape[1])])
    z_y = np.zeros((Bs.shape[0], 6, num + 1))

    for i in range(Xi.shape[0]):
        for k in range(Bs.shape[1]):
            z_y[:, k, :] = interpolates(X, Bs[:, k, :], z_x[k], Xi[i, :])

        for j in range(Xi.shape[1]):
            Zi[:, i, j] = interpolates(Y, z_y[:, :, j], cubic_splines(Y, z_y[:, :, j]), Yi[i, j])

    return Zi

