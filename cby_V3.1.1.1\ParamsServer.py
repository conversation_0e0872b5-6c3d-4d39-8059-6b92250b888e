"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/12/13 16:30
desc:
    综合参数和可视化
environment: py39
run:
    python ParamsServer.py
"""


from utils.get_metrics import *


class Server:
    def __init__(self):
        self.model = 'model'

    def get_times1(self, filename):
        # 时刻点计算
        try:
            ls36 = get_mcg36(filename, 0)
            tm0 = gettime(filename)[:-1]
            tm0 = cal_PhPr0(ls36, tm0)  # 时刻点-时相参数
            Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr = tm0
            output = {
                'Code': 1,
                'loc_p_head': int(Ph),
                'loc_p_peak': int(Pp),
                'loc_p_rear': int(Pr),
                'loc_q_head': int(Qh),
                'loc_q_peak': int(Qp),
                'loc_r_peak': int(Rp),
                'loc_s_peak': int(Sp),
                'loc_s_rear': int(Sr),
                'loc_t_head': int(Th),
                'loc_t_onset': int(To),
                'loc_t_peak': int(Tp),
                'loc_t_end': int(Te),
                'loc_t_rear': int(Tr)
            }
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output

    def get_waveform(self, filename, idxs=None):
        # 波形描述
        try:
            output = bxms1(filename, idxs)
            output['Code'] = 1
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output

    def get_mcgpole(self, filename, idxs=None):
        # 磁极描述
        try:
            Zqr = extractmt('%s/ischemic_mfm_Z/rkdb_qr22.txt' % self.model)
            Zrs = extractmt('%s/ischemic_mfm_Z/rkdb_rs22.txt' % self.model)
            Ztt = extractmt('%s/ischemic_mfm_Z/rkdb_tt22.txt' % self.model)
            output = cjms1(filename, idxs, Zqr, Zrs, Ztt)
            output['Code'] = 1
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output

    def get_disps1(self, filename, idxs=None):
        # 离散度计算: Q/R/S/T波, 正负
        try:
            output = lsds1(filename, idxs)
            output['Code'] = 1
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output

    def get_results(self, filename, paras, idxs=None, paras2={}):
        # 心磁图综合结果
        try:
            try:
                waveform, mcgpole = paras2['waveform'], paras2['mcgpole']
            except:
                waveform, mcgpole = {}, {}
            if waveform=={} and mcgpole=={}:
                waveform = self.get_waveform(filename, idxs)
                mcgpole = self.get_mcgpole(filename, idxs)
            output = zhjg1(paras, waveform, mcgpole)
            output['Code'] = 1
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output

    def get_tms250805(self, filelines):
        '''
        新时刻点算法:
            输入: 36通道原始数据-行字符串的列表(或原始心磁文件)
            输出: 13时刻点
        '''
        try:
            from utils.get_mts20250804 import gettimes
            Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr = gettimes(filelines)[:13]
            output = {
                'Code': 1,
                'loc_p_head': int(Ph),
                'loc_p_peak': int(Pp),
                'loc_p_rear': int(Pr),
                'loc_q_head': int(Qh),
                'loc_q_peak': int(Qp),
                'loc_r_peak': int(Rp),
                'loc_s_peak': int(Sp),
                'loc_s_rear': int(Sr),
                'loc_t_head': int(Th),
                'loc_t_onset': int(To),
                'loc_t_peak': int(Tp),
                'loc_t_end': int(Te),
                'loc_t_rear': int(Tr)
            }
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output

    def get_matrix250805(self, filename, t1=0, t2=0):
        '''
        插值算法:
            输入: 原始心磁txt文件(6*6), 可选地, 输入起止时刻点t1/t2
            输出: 插值结果, 维度N*100*100, N维列表的每个元素是100*100的numpy数组
        '''
        try:
            from utils.get_mts20250804 import cal_interpolate
            matrixes = cal_interpolate(filename, t1, t2)
            output = {
                'Code': 1,
                'matrixes': matrixes
            }
            # # 本地保存: **.pkl
            # with open(pklname, 'wb') as f:
            #     pickle.dump(matrixes, f)
            # # 本地读取
            # with open(pklname, 'rb') as f:
            #     matrixes = pickle.load(f)
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output

    def get_mts250805(self, filelines, matrixes, idxs=None):
        '''
        新参数算法:
            输入: 
                36通道原始数据-行字符串的列表(或原始心磁文件)
                N*100*100的插值结果, N维列表的每个元素是100*100的numpy数组
                信息矩阵地址
                13时刻点列表
            输出: 15个参数的字典
        '''
        try:
            from utils.get_mts20250804 import gn_mts2
            rts = gn_mts2(filelines, matrixes, idxs)
            output = {'Code': 1}
            for k in ['mfm_TT_ta', 'mfm_ag_tta', 'mfm_TT_tta', 'mfm_TT_areaf', 'space_twavedt_iv', 'space_twavedt_ur', 'time_tpnrt1', 'mfm_QR_ra', 'pcdm_QR_rca', 'mfm_ag_qrsa', 'pcdm_QR_qra', 'space_qrswv_r', 'space_qrswv_q', 'mfm_ag_qrstta', 'amplitude_RT']:
                output[k] = rts[k]
            return output
        except Exception as e:
            output = {'Error': str(e), 'Code': 0}
            return output


if __name__ == '__main__':
    # # ### 1.历史版接口-保留
    # item = 'BJ_TT_000358'
    # filename = './datasets/%s.txt' % item
    # data_dict = {}
    # # data_dict = {'Q': 252, 'R': 277, 'S': 312, 'T_on': 464, 'T': 535, 'T_end': 570}
    # item = 'SHLY_2024_002056'
    # # for item in ['SY_TT_000168', 'BJ_TT_001217', 'SY_TT_000824', 'GDNH_2024_000026', 'SY_TT_000054', 'BJ_TT_000358']:
    # # print('\n', item)
    # filename = './datasets/%s.txt' % item
    # my_server = Server()
    # value = my_server.get_times1(filename)
    # print(value)
    # waveform = my_server.get_waveform(filename, data_dict)
    # print(waveform)
    # mcgpole = my_server.get_mcgpole(filename, data_dict)
    # print(mcgpole)
    # value = my_server.get_disps1(filename, data_dict)
    # print(value)
    # paras = {}
    # # paras = {
    # #     'Q_index': -0.022053412822669,
    # #     'S_index': 0.843123855929008,
    # #     'TT_index': 1.83742183051828,
    # #     'Q_angle': -152.1,
    # #     'R_angle': -11.8,
    # #     'S_angle': 160.3,
    # #     'T_angle': -52.5,
    # #     'QRyc': 0,
    # #     'RSyc': 0,
    # #     'Tyc': 0,
    # #     'score': 0.210579261183739}
    # paras2 = {'waveform': waveform, 'mcgpole': mcgpole}
    # # data_dict = {'Q': 295, 'R': 317, 'S': 338, 'T_on': 538, 'T': 590, 'T_end': 620}
    # value = my_server.get_results(filename, paras, data_dict, paras2)
    # print(value)
    
    # ### 2.新参数表: 随机抽取10例
    # cby-15参数: mfm_TT_ta, mfm_ag_tta, mfm_TT_tta, mfm_TT_areaf, space_twavedt_iv, space_twavedt_ur, time_tpnrt1, mfm_QR_ra, pcdm_QR_rca, mfm_ag_qrsa, pcdm_QR_qra, space_qrswv_r, space_qrswv_q, mfm_ag_qrstta, amplitude_RT
    mcgdir = './static/mcg10'
    rtsdir = './static/rts10'
    ids = ['SHLY_2024_002080', 'BJ_TT_000614', '464-王雅', 'SL_B_001075-姜声英', 'SHSY_2023_000467', 'BJ_TT_002610', 'AHYK_2023_000644', 'SL_DSA_001592', '109-张玉龙', 'BJ_TT_001097']
    lls = [['ID', 'time_raw', 'waveform', 'mcgpole', 'disps1', 'results', 'time_new', 'matrixes', 'mts_new15']]
    for item in ids:
        print('————%s:' % item)
        savedir = '%s/%s' % (rtsdir, item)
        new_folder(savedir)
        filename = '%s/%s.txt' % (mcgdir, item)
        my_server = Server()
        # 历史版本-1.时刻点
        ll = [item]
        t1 = time.time()
        value = my_server.get_times1(filename)
        ll.append(time.time()-t1)  # 运行时间
        print(value)  # 打印结果
        with open('%s/time_raw.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-2.波形描述参数
        t1 = time.time()
        data_dict = {}
        # data_dict = {'Q': 252, 'R': 277, 'S': 312, 'T_on': 464, 'T': 535, 'T_end': 570}  # 输入模板
        waveform = my_server.get_waveform(filename, data_dict)
        ll.append(time.time()-t1)
        print(waveform)
        with open('%s/waveform.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-3.磁图描述参数
        t1 = time.time()
        mcgpole = my_server.get_mcgpole(filename, data_dict)
        ll.append(time.time()-t1)
        print(mcgpole)
        with open('%s/mcgpole.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-4.离散度参数
        t1 = time.time()
        value = my_server.get_disps1(filename, data_dict)
        ll.append(time.time()-t1)
        print(value)
        with open('%s/disps1.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 历史版本-5.心磁结论
        t1 = time.time()
        paras = {}
        # paras = {
        #     'Q_index': -0.022053412822669,
        #     'S_index': 0.843123855929008,
        #     'TT_index': 1.83742183051828,
        #     'Q_angle': -152.1,
        #     'R_angle': -11.8,
        #     'S_angle': 160.3,
        #     'T_angle': -52.5,
        #     'QRyc': 0,
        #     'RSyc': 0,
        #     'Tyc': 0,
        #     'score': 0.210579261183739}  # 输入模板
        paras2 = {'waveform': waveform, 'mcgpole': mcgpole}
        value = my_server.get_results(filename, paras, data_dict, paras2)
        ll.append(time.time()-t1)
        print(value)
        with open('%s/results.json' % savedir, "w") as f:  # 保存结果
            json.dump(value, f, indent=4)
        # 新版本-1.时刻点
        t1 = time.time()
        doc = open(filename, 'r')
        filelines = doc.readlines()  # 以txt文件的行字符串为输入: 原始36通道数据
        doc.close()
        ts = my_server.get_tms250805(filelines)
        ll.append(time.time()-t1)
        print(ts)
        with open('%s/time_new.json' % savedir, "w") as f:  # 保存结果
            json.dump(ts, f, indent=4)
        # 新版本-2.插值
        t1 = time.time()
        ms = my_server.get_matrix250805(filename)
        ll.append(time.time()-t1)
        print(ms['Code'])  # 矩阵太大, 不直接打印
        with open('%s/matrixes.pkl' % savedir, 'wb') as f:  # 保存结果
            pickle.dump(ms['matrixes'], f)
        # 新版本-3.新参数
        with open('%s/matrixes.pkl' % savedir, 'rb') as f:  # 读取插值数据
            matrixes = pickle.load(f)
        times = []
        # times = [39, 79, 156, 251, 273, 290, 319, 349, 430, 488, 546, 608, 674]
        t1 = time.time()
        mts = my_server.get_mts250805(filelines, matrixes, times)
        ll.append(time.time()-t1)
        print(mts)
        with open('%s/mts_new15.json' % savedir, "w") as f:  # 保存结果
            json.dump(mts, f, indent=4)
        ll = ll[:1]+[round(t,6) for t in ll[1:]]
        lls.append(ll)
    from utils.get_mts20250804 import list_to_xlsx  # 导出运行时间
    ll = ['Avg.Time']
    ll += [round(np.average([lls[j][i] for j in range(1, len(lls))]),6) for i in range(1, len(lls[0]))]
    lls.append(ll)
    list_to_xlsx(lls, './static/runtimes.xlsx')
