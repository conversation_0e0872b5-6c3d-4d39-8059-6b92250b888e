"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_data_inspect
date: 2024/12/25 下午3:17
desc: 
"""

import pickle
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import shap
import xgboost as xgb
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from umap import UMAP

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号问题

class DataDifferenceAnalyzer:
    def __init__(self, feature_file1, feature_file2, selected_feature_file, excel_file):
        self.feature_file1 = feature_file1
        self.feature_file2 = feature_file2
        self.selected_feature_file = selected_feature_file
        self.excel_file = excel_file

    def load_data(self):
        # 加载特征文件
        with open(self.feature_file1, 'rb') as f:
            self.data1 = pickle.load(f)
        with open(self.feature_file2, 'rb') as f:
            self.data2 = pickle.load(f)
        with open(self.selected_feature_file, 'rb') as f:
            self.selected_features = pickle.load(f).columns.tolist()

        # 加载excel文件获取标签信息
        self.labels_df = pd.read_excel(self.excel_file)

        # 提取选定特征的数据
        self.df1 = pd.DataFrame(self.data1)[self.selected_features]
        self.df2 = pd.DataFrame(self.data2)[self.selected_features]

        # 添加组别标识
        self.df1['group'] = 'group1'
        self.df2['group'] = 'group2'

        # 合并数据
        self.combined_df = pd.concat([self.df1, self.df2])

    def demographic_analysis(self):
        """人口统计学特征分析"""
        # 合并标签信息
        demo_df1 = pd.merge(self.df1, self.labels_df[['mcg_id', 'age', 'gender', 'label']],
                            left_on='mcg_file', right_on='mcg_id')
        demo_df2 = pd.merge(self.df2, self.labels_df[['mcg_id', 'age', 'gender', 'label']],
                            left_on='mcg_file', right_on='mcg_id')

        # 统计分析
        try:
            gender_chi2 = stats.chi2_contingency([[
                len(demo_df1[demo_df1['gender'] == '男']),
                len(demo_df1[demo_df1['gender'] == '女'])
            ], [
                len(demo_df2[demo_df2['gender'] == '男']),
                len(demo_df2[demo_df2['gender'] == '女'])
            ]])[1]
        except:
            gender_chi2 = None

        try:
            label_chi2 = stats.chi2_contingency([[
                len(demo_df1[demo_df1['label_y'] == 1]),
                len(demo_df1[demo_df1['label_y'] == 0])
            ], [
                len(demo_df2[demo_df2['label_y'] == 1]),
                len(demo_df2[demo_df2['label_y'] == 0])
            ]])[1]
        except:
            label_chi2 = None

        results = {
            'age': {
                'group1_mean': demo_df1['age'].mean(),
                'group1_std': demo_df1['age'].std(),
                'group2_mean': demo_df2['age'].mean(),
                'group2_std': demo_df2['age'].std(),
                'p_value': stats.ttest_ind(demo_df1['age'], demo_df2['age']).pvalue
            },
            'gender': {
                'group1': demo_df1['gender'].value_counts().to_dict(),
                'group2': demo_df2['gender'].value_counts().to_dict(),
                'p_value': gender_chi2
            },
            'label': {
                'group1': demo_df1['label_y'].value_counts().to_dict(),
                'group2': demo_df2['label_y'].value_counts().to_dict(),
                'p_value': label_chi2
            }
        }
        return results

    def feature_difference_analysis(self):
        """特征差异分析"""
        # 用于存储数值特征的统计差异
        diff_stats = {}
        # 记录非数值特征和被过滤的特征
        non_numeric_features = []
        filtered_features = []

        # 预处理：创建数据副本并填充 NaN
        df1_processed = self.df1.copy()
        df2_processed = self.df2.copy()

        for feature in self.selected_features:
            # 跳过 mcg_file、group 列和 label 开头的特征
            if feature in ['mcg_file', 'group'] or feature.startswith('label'):
                filtered_features.append(feature)
                continue

            # 检查是否为数值型特征
            try:
                # 尝试转换为数值类型并填充 NaN
                values1 = pd.to_numeric(df1_processed[feature], errors='raise')
                values2 = pd.to_numeric(df2_processed[feature], errors='raise')

                # 用 -1 填充 NaN
                values1 = values1.fillna(-1)
                values2 = values2.fillna(-1)

                # 计算统计差异
                t_stat, p_value = stats.ttest_ind(values1, values2)
                effect_size = abs(values1.mean() - values2.mean()) / \
                              np.sqrt((values1.std() ** 2 + values2.std() ** 2) / 2)

                diff_stats[feature] = {
                    'p_value': p_value,
                    'effect_size': effect_size,
                    'group1_mean': values1.mean(),
                    'group1_std': values1.std(),
                    'group2_mean': values2.mean(),
                    'group2_std': values2.std()
                }
            except (ValueError, TypeError):
                non_numeric_features.append(feature)
                continue

        # 打印非数值特征的信息
        if non_numeric_features:
            print(f"以下特征为非数值型，已跳过统计分析：{', '.join(non_numeric_features)}")
        if filtered_features:
            print(f"以下特征被过滤：{', '.join(filtered_features)}")

        # 转换为DataFrame并按效应值大小排序
        if diff_stats:
            return pd.DataFrame(diff_stats).T.sort_values('effect_size', ascending=False)
        else:
            print("警告：没有找到可用于统计分析的数值型特征")
            return pd.DataFrame()

    def dimensionality_reduction_viz(self):
        """降维可视化"""
        # 选择要使用的特征
        features_to_use = []
        non_numeric_features = []

        # 创建数据副本
        combined_df_processed = self.combined_df.copy()

        for feature in self.selected_features:
            # 跳过 mcg_file、group 和 label 开头的特征
            if feature in ['mcg_file', 'group'] or feature.startswith('label'):
                continue

            # 检查是否为数值型特征
            try:
                # 转换为数值类型并填充 NaN
                combined_df_processed[feature] = pd.to_numeric(combined_df_processed[feature], errors='raise')
                combined_df_processed[feature] = combined_df_processed[feature].fillna(-1)
                features_to_use.append(feature)
            except (ValueError, TypeError):
                non_numeric_features.append(feature)

        if non_numeric_features:
            print(f"降维分析跳过以下非数值特征：{', '.join(non_numeric_features)}")

        if not features_to_use:
            print("警告：没有找到可用于降维的数值型特征")
            return None

        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(combined_df_processed[features_to_use])

        # PCA降维
        pca = PCA(n_components=2)
        pca_result = pca.fit_transform(scaled_data)

        # UMAP降维
        umap = UMAP(n_components=2, random_state=42)
        umap_result = umap.fit_transform(scaled_data)

        return {
            'pca': {
                'result': pca_result,
                'explained_variance': pca.explained_variance_ratio_
            },
            'umap': {
                'result': umap_result
            }
        }

    def model_discrimination_analysis(self):
        """模型区分度分析"""
        # 准备数据
        X = self.combined_df.drop(['group', 'mcg_file'], axis=1)
        y = (self.combined_df['group'] == 'group2').astype(int)

        # 划分训练测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # 训练XGBoost模型
        model = xgb.XGBClassifier(random_state=42)
        model.fit(X_train, y_train)

        # 预测和评估
        y_pred = model.predict(X_test)
        y_prob = model.predict_proba(X_test)

        # SHAP值计算
        explainer = shap.TreeExplainer(model)
        shap_values = explainer.shap_values(X_test)

        return {
            'classification_report': classification_report(y_test, y_pred),
            'confusion_matrix': confusion_matrix(y_test, y_pred),
            'feature_importance': dict(zip(X.columns, model.feature_importances_)),
            'shap_values': shap_values,
            'test_data': X_test
        }

    def group_label_shap_analysis(self):
        """分析每个组内label的SHAP值"""
        # 获取标签信息
        demo_df1 = pd.merge(self.df1, self.labels_df[['mcg_id', 'label']],
                            left_on='mcg_file', right_on='mcg_id')
        demo_df2 = pd.merge(self.df2, self.labels_df[['mcg_id', 'label']],
                            left_on='mcg_file', right_on='mcg_id')

        results = {}

        # Group 1的分析
        X1 = self.df1.drop(['mcg_file', 'group','label'], axis=1)
        y1 = demo_df1['label_y']

        # Group 2的分析
        X2 = self.df2.drop(['mcg_file', 'group','label'], axis=1)
        y2 = demo_df2['label_y']

        for group_name, X, y in [("group1", X1, y1), ("group2", X2, y2)]:
            if len(X) > 0 and len(np.unique(y)) > 1:
                # 划分训练测试集
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

                # 训练模型
                model = xgb.XGBClassifier(random_state=42)
                model.fit(X_train, y_train)

                # SHAP值计算
                explainer = shap.TreeExplainer(model)
                shap_values = explainer.shap_values(X_test)

                results[group_name] = {
                    'shap_values': shap_values,
                    'test_data': X_test
                }

        return results
    def generate_report(self):
        """生成分析报告"""
        # 1. 人口统计学分析
        demo_results = self.demographic_analysis()

        # 2. 特征差异分析
        feature_diff = self.feature_difference_analysis()

        # 3. 降维分析
        dim_reduction = self.dimensionality_reduction_viz()

        # 4. 模型区分度分析
        model_results = self.model_discrimination_analysis()

        # 生成报告内容
        report = {
            'demographic_analysis': demo_results,
            'feature_difference': {
                'top_different_features': feature_diff.head(10).to_dict(),
                'total_significant_features': sum(feature_diff['p_value'] < 0.05)
            },
            'dimensionality_reduction': {
                'pca_variance_explained': dim_reduction['pca']['explained_variance'].tolist(),
            },
            'model_discrimination': {
                'classification_report': model_results['classification_report'],
                'top_important_features': dict(sorted(
                    model_results['feature_importance'].items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10])
            }
        }

        return report

    def visualize_all_shap_results(self):
        """可视化所有SHAP结果"""
        # 1. Group区分的SHAP值
        group_disc_results = self.model_discrimination_analysis()

        # 2. 各Group内部的label区分SHAP值
        group_label_results = self.group_label_shap_analysis()

        plt.figure(figsize=(15, 8))

        # Group区分的SHAP值
        # plt.subplot(131)
        shap.summary_plot(
            group_disc_results['shap_values'],
            group_disc_results['test_data'],
            show=False,
            plot_type="dot",
            max_display=10,
            alpha=0.5,
            title="Group Discrimination"
        )
        plt.title("Group Discrimination SHAP", fontsize=12, pad=20)
        plt.show()
        # Group 1的label区分SHAP值
        if 'group1' in group_label_results:
            plt.figure(figsize=(15, 8))

            # plt.subplot(132)
            shap.summary_plot(
                group_label_results['group1']['shap_values'],
                group_label_results['group1']['test_data'],
                show=False,
                plot_type="dot",
                max_display=10,
                alpha=0.5,
                title="Group 1 Label Discrimination"
            )
            plt.title("Group 1 Label SHAP", fontsize=12, pad=20)
            plt.show()
        # Group 2的label区分SHAP值
        if 'group2' in group_label_results:
            plt.figure(figsize=(15, 8))

            # plt.subplot(133)
            shap.summary_plot(
                group_label_results['group2']['shap_values'],
                group_label_results['group2']['test_data'],
                show=False,
                plot_type="dot",
                max_display=10,
                alpha=0.5,
                title="Group 2 Label Discrimination"
            )
            plt.title("Group 2 Label SHAP", fontsize=12, pad=20)
            plt.show()
        # plt.tight_layout()
        # plt.show()
    def visualize_results(self):
        """可视化结果"""
        # 获取标签信息
        demo_df1 = pd.merge(self.df1, self.labels_df[['mcg_id', 'label']],
                            left_on='mcg_file', right_on='mcg_id')
        demo_df2 = pd.merge(self.df2, self.labels_df[['mcg_id', 'label']],
                            left_on='mcg_file', right_on='mcg_id')
        labels = pd.concat([demo_df1['label_y'], demo_df2['label_y']])

        # 1. PCA可视化（带边缘分布）
        dim_reduction = self.dimensionality_reduction_viz()
        if dim_reduction:
            # 创建图形和网格
            fig = plt.figure(figsize=(12, 12))
            gs = fig.add_gridspec(3, 3)

            # 主散点图
            ax_main = fig.add_subplot(gs[1:, :-1])
            # 右侧分布图
            ax_right = fig.add_subplot(gs[1:, -1])
            # 上方分布图
            ax_top = fig.add_subplot(gs[0, :-1])

            groups = self.combined_df['group'].values
            # 设置不同的标记样式和颜色
            markers = {'group1_0': 'o', 'group1_1': 's',
                       'group2_0': '^', 'group2_1': 'D'}
            colors = {'group1_0': '#1f77b4', 'group1_1': '#ff7f0e',
                      'group2_0': '#2ca02c', 'group2_1': '#d62728'}

            # 分别绘制每个组合
            for g, l, c, m, label in [
                ('group1', 0, colors['group1_0'], markers['group1_0'], 'Group 1 - Label 0'),
                ('group1', 1, colors['group1_1'], markers['group1_1'], 'Group 1 - Label 1'),
                ('group2', 0, colors['group2_0'], markers['group2_0'], 'Group 2 - Label 0'),
                ('group2', 1, colors['group2_1'], markers['group2_1'], 'Group 2 - Label 1')
            ]:
                mask = (groups == g) & (labels == l)
                ax_main.scatter(dim_reduction['pca']['result'][mask, 0],
                                dim_reduction['pca']['result'][mask, 1],
                                c=c, marker=m, s=30, label=label, alpha=0.6,
                                edgecolor='white', linewidth=0.5)

            # 设置主图样式
            ax_main.set_xlabel('First Principal Component', fontsize=12)
            ax_main.set_ylabel('Second Principal Component', fontsize=12)
            ax_main.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
            ax_main.grid(True, linestyle='--', alpha=0.3)

            # 绘制上方分布图
            for g, l, c in [
                ('group1', 0, colors['group1_0']),
                ('group1', 1, colors['group1_1']),
                ('group2', 0, colors['group2_0']),
                ('group2', 1, colors['group2_1'])
            ]:
                mask = (groups == g) & (labels == l)
                sns.kdeplot(data=dim_reduction['pca']['result'][mask, 0],
                            ax=ax_top, color=c, fill=True, alpha=0.4,
                            linewidth=2)
            ax_top.set_xticks([])
            ax_top.set_ylabel('Density')

            # 绘制右侧分布图（横向）
            for g, l, c in [
                ('group1', 0, colors['group1_0']),
                ('group1', 1, colors['group1_1']),
                ('group2', 0, colors['group2_0']),
                ('group2', 1, colors['group2_1'])
            ]:
                mask = (groups == g) & (labels == l)
                sns.kdeplot(data=dim_reduction['pca']['result'][mask, 1],
                            ax=ax_right, color=c, fill=True, alpha=0.4,
                            linewidth=2, vertical=True)
            ax_right.set_yticks([])
            ax_right.set_xlabel('Density')

            plt.tight_layout()
            plt.show()

        # 2. SHAP值可视化（分组）
        self.visualize_all_shap_results()


        # 3. 人口统计学特征可视化
        demo_results = self.demographic_analysis()

        # 年龄分布
        plt.figure(figsize=(10, 6))
        # plt.subplot(131)
        sns.boxplot(x='group', y='clinic_age', data=pd.concat([
            demo_df1.assign(group='Group 1')[['clinic_age', 'group']],
            demo_df2.assign(group='Group 2')[['clinic_age', 'group']]
        ]))
        plt.title('Age Distribution')
        plt.tight_layout()
        plt.show()

        # 性别分布
        plt.figure(figsize=(10, 6))
        # plt.subplot(132)
        gender_data = pd.DataFrame({
            'Group 1': demo_df1['clinic_gender'].value_counts(normalize=True),
            'Group 2': demo_df2['clinic_gender'].value_counts(normalize=True)
        }) * 100
        gender_data.plot(kind='bar')
        plt.title('Gender Distribution (%)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

        # 标签分布
        plt.figure(figsize=(10, 6))
        # plt.subplot(133)
        label_data = pd.DataFrame({
            'Group 1': demo_df1['label_y'].value_counts(normalize=True),
            'Group 2': demo_df2['label_y'].value_counts(normalize=True)
        }) * 100
        label_data.plot(kind='bar')
        plt.title('Label Distribution (%)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

        # plt.savefig('demographic_viz.png')
        # plt.close()

        # 4. 特征差异分析可视化
        diff_stats = self.feature_difference_analysis()
        if not diff_stats.empty:
            plt.figure(figsize=(15, 6))

            # 效应值和p值的组合图
            # plt.subplot(121)
            top_features = diff_stats.head(10)
            plt.barh(range(len(top_features)), top_features['effect_size'])
            plt.yticks(range(len(top_features)), top_features.index)
            plt.title('Top 10 Features by Effect Size')

            # 添加显著性标记
            for i, p_value in enumerate(top_features['p_value']):
                if p_value < 0.001:
                    plt.text(top_features['effect_size'].iloc[i], i, '***', va='center')
                elif p_value < 0.01:
                    plt.text(top_features['effect_size'].iloc[i], i, '**', va='center')
                elif p_value < 0.05:
                    plt.text(top_features['effect_size'].iloc[i], i, '*', va='center')

            plt.tight_layout()
            plt.xlim(0.6,1)
            plt.show()

            # plt.savefig('feature_diff_viz.png')
            # plt.close()





def get_mcg_ids_from_excel(excel_path):
    """
    从Excel文件中读取心磁号列表
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)

        # 假设心磁号列的列名为"心磁号"，如果不是请修改
        mcg_column = "心磁号"
        if mcg_column not in df.columns:
            raise ValueError(f"Excel文件中没有找到'{mcg_column}'列")

        # 获取心磁号列表并去除重复值和空值
        mcg_ids = df[mcg_column].dropna().unique().tolist()
        print(f"从Excel中读取到 {len(mcg_ids)} 个唯一的心磁号")
        return mcg_ids

    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return []


def find_feature_files(base_dir, mcg_ids):
    """
    在指定目录中查找对应mcg_id的特征文件
    """
    feature_files = {}
    base_path = Path(base_dir)

    # 遍历所有mcg_id
    for mcg_id in mcg_ids:
        # 构造可能的文件名格式
        possible_filenames = [
            f"{mcg_id}.pkl",
            f"feature_{mcg_id}.pkl",
            # 可以添加其他可能的文件名格式
        ]

        found = False
        # 递归搜索目录
        for pkl_file in base_path.rglob("*.pkl"):
            if any(possible_name in pkl_file.name for possible_name in possible_filenames):
                feature_files[mcg_id] = pkl_file
                found = True
                break

        if not found:
            print(f"警告: 未找到MCG ID {mcg_id}对应的特征文件")

    print(f"找到 {len(feature_files)} 个特征文件")
    return feature_files


def merge_feature_files(feature_files):
    """
    合并所有特征文件为一个DataFrame
    每个特征文件作为DataFrame中的一行
    """
    all_features = []

    for mcg_id, file_path in feature_files.items():
        try:
            with open(file_path, 'rb') as f:
                features = pickle.load(f)
                # 确保features是一个字典或类似的结构
                if isinstance(features, (dict, pd.Series, pd.DataFrame)):
                    # 转换为DataFrame的一行
                    feature_df = features
                    # 添加mcg_id列
                    feature_df['mcg_id'] = mcg_id
                    all_features.append(feature_df)
                else:
                    print(f"警告: MCG ID {mcg_id} 的特征格式不正确，跳过此文件")
        except Exception as e:
            print(f"读取特征文件 {file_path} 时出错: {e}")
            continue

    if all_features:
        # 垂直堆叠所有特征
        merged_df = pd.concat(all_features, ignore_index=True)
        print(f"成功合并 {len(merged_df)} 个特征文件到DataFrame中")
        return merged_df
    else:
        print("没有成功读取任何特征文件")
        return None


def save_merged_features(merged_df, output_path):
    """
    保存合并后的特征DataFrame
    """
    if merged_df is None:
        print("没有可保存的特征数据")
        return

    try:
        with open(output_path, 'wb') as f:
            pickle.dump(merged_df, f)
        print(f"成功保存合并后的特征DataFrame到: {output_path}")
        print(f"DataFrame形状: {merged_df.shape}")
        print(f"列数: {len(merged_df.columns)}")
    except Exception as e:
        print(f"保存合并特征时出错: {e}")


def pre_process():
    # 合并一下零散的外部验证特征；
    # 配置路径
    excel_path = r"E:\个人临时文件夹\王月霞\中山跑微循环模型结果2024-12-12\中山微循环.xlsx"
    feature_base_dir = r"D:\wyh\Code\deployments\MlPiplineDeployment\files\saved_features\file_features"
    output_path = "./files/saved_features/feature_V241119_mc_clinic_outer89.pkl"

    # 1. 获取mcg_ids
    mcg_ids = get_mcg_ids_from_excel(excel_path)
    if not mcg_ids:
        return

    # 2. 查找特征文件
    feature_files = find_feature_files(feature_base_dir, mcg_ids)
    if not feature_files:
        return

    # 3. 合并特征文件
    merged_features = merge_feature_files(feature_files)
    if not merged_features:
        return

    # 4. 保存合并后的特征
    save_merged_features(merged_features, output_path)

    # 5. 打印统计信息
    print("\n统计信息:")
    print(f"Excel中的MCG ID数量: {len(mcg_ids)}")
    print(f"找到的特征文件数量: {len(feature_files)}")
    print(f"成功合并的特征数量: {len(merged_features)}")

    # 6. 检查是否有缺失的MCG ID
    missing_mcg_ids = set(mcg_ids) - set(merged_features.keys())
    if missing_mcg_ids:
        print("\n以下MCG ID未找到对应的特征文件:")
        for mcg_id in missing_mcg_ids:
            print(mcg_id)


def save_report_to_word(report, output_path="analysis_report.docx"):
    """
    将分析报告保存为格式化的Word文档

    Args:
        report (dict): 分析报告字典
        output_path (str): 输出文件路径
    """
    from docx import Document
    from docx.enum.text import WD_ALIGN_PARAGRAPH

    # 创建文档
    doc = Document()

    # 设置标题
    title = doc.add_heading('数据分析报告', level=0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 1. 人口统计学分析
    doc.add_heading('1. 人口统计学分析', level=1)
    demo_results = report['demographic_analysis']

    # 年龄分析
    doc.add_heading('1.1 年龄分析', level=2)
    age_table = doc.add_table(rows=1, cols=3)
    age_table.style = 'Table Grid'
    header_cells = age_table.rows[0].cells
    header_cells[0].text = '统计量'
    header_cells[1].text = 'Group 1'
    header_cells[2].text = 'Group 2'

    age_stats = [
        ('年龄均值', demo_results['age']['group1_mean'], demo_results['age']['group2_mean']),
        ('年龄标准差', demo_results['age']['group1_std'], demo_results['age']['group2_std']),

    ]

    for stat in age_stats:
        row_cells = age_table.add_row().cells
        row_cells[0].text = stat[0]
        row_cells[1].text = f"{stat[1]:.2f}" if isinstance(stat[1], (float, int)) else str(stat[1])
        row_cells[2].text = f"{stat[2]:.2f}" if isinstance(stat[2], (float, int)) else str(stat[2])

    doc.add_paragraph()

    # 性别分布
    doc.add_heading('1.2 性别分布', level=2)
    gender_table = doc.add_table(rows=1, cols=3)
    gender_table.style = 'Table Grid'
    header_cells = gender_table.rows[0].cells
    header_cells[0].text = '性别'
    header_cells[1].text = 'Group 1 '
    header_cells[2].text = 'Group 2 '

    # 获取所有可能的性别类别
    all_genders = set(demo_results['gender']['group1'].keys()) | set(demo_results['gender']['group2'].keys())

    for gender in all_genders:
        row_cells = gender_table.add_row().cells
        row_cells[0].text = gender
        # 使用get方法，如果性别不存在则返回0
        row_cells[1].text = f"{demo_results['gender']['group1'].get(gender, 0):.1f}"
        row_cells[2].text = f"{demo_results['gender']['group2'].get(gender, 0):.1f}"

    p = doc.add_paragraph(f"\n性别分布p值: {demo_results['gender']['p_value']:.3f}")

    # 2. 特征差异分析
    doc.add_heading('2. 特征差异分析', level=1)
    p = doc.add_paragraph(f"显著性特征数量: {report['feature_difference']['total_significant_features']}")

    # 展示top差异特征
    doc.add_heading('2.1 Top 10 差异特征', level=2)
    features_table = doc.add_table(rows=1, cols=6)
    features_table.style = 'Table Grid'
    header_cells = features_table.rows[0].cells
    header_cells[0].text = '特征名称'
    header_cells[1].text = 'p值'
    header_cells[2].text = '效应值'
    header_cells[3].text = 'Group1均值'
    header_cells[4].text = 'Group2均值'
    header_cells[5].text = '显著性'

    # 获取特征名列表（从任意属性字典中获取）
    feature_names = list(report['feature_difference']['top_different_features']['p_value'].keys())

    for feature in feature_names:
        stats = {
            'p_value': report['feature_difference']['top_different_features']['p_value'][feature],
            'effect_size': report['feature_difference']['top_different_features']['effect_size'][feature],
            'group1_mean': report['feature_difference']['top_different_features']['group1_mean'][feature],
            'group2_mean': report['feature_difference']['top_different_features']['group2_mean'][feature]
        }

        row_cells = features_table.add_row().cells
        row_cells[0].text = feature
        row_cells[1].text = f"{stats['p_value']:.3e}"  # 使用科学计数法
        row_cells[2].text = f"{stats['effect_size']:.3f}"
        row_cells[3].text = f"{stats['group1_mean']:.3e}"
        row_cells[4].text = f"{stats['group2_mean']:.3e}"

        # 添加显著性标记
        if stats['p_value'] < 0.001:
            row_cells[5].text = '***'
        elif stats['p_value'] < 0.01:
            row_cells[5].text = '**'
        elif stats['p_value'] < 0.05:
            row_cells[5].text = '*'
        else:
            row_cells[5].text = 'ns'

    # 3. 降维分析
    doc.add_heading('3. 降维分析', level=1)
    p = doc.add_paragraph('PCA解释方差比例:')
    for i, var in enumerate(report['dimensionality_reduction']['pca_variance_explained']):
        p.add_run(f'\nPC{i + 1}: {var:.3f}')

    # 4. 模型区分度分析
    doc.add_heading('4. 模型区分度分析', level=1)

    # 分类报告
    doc.add_heading('4.1 分类性能报告', level=2)
    p = doc.add_paragraph(report['model_discrimination']['classification_report'])

    # Top重要特征
    doc.add_heading('4.2 区分两组数据的 Top 10 重要特征', level=2)
    importance_table = doc.add_table(rows=1, cols=2)
    importance_table.style = 'Table Grid'
    header_cells = importance_table.rows[0].cells
    header_cells[0].text = '特征名称'
    header_cells[1].text = '重要性得分'

    for feature, importance in report['model_discrimination']['top_important_features'].items():
        row_cells = importance_table.add_row().cells
        row_cells[0].text = feature
        row_cells[1].text = f"{importance:.3f}"

    # 保存文档
    doc.save(output_path)
    print(f"报告已保存至: {output_path}")

if __name__ == "__main__":

    analyzer = DataDifferenceAnalyzer(
        feature_file1="./files/saved_features/feature_V241119_mc_clinic_675.pkl",
        feature_file2="./files/saved_features/feature_V241119_mc_clinic_outer89.pkl",
        selected_feature_file='./files/saved_features/selected_features/features_V1119F5S42_mc_boruta_top196_clinic5.pkl',
        excel_file='./files/data/中山微循环_所有数据分布分析.xlsx'  # 需要提供包含标签信息的Excel文件
    )

    # 加载数据
    analyzer.load_data()

    # 生成报告
    report = analyzer.generate_report()

    # 生成可视化
    analyzer.visualize_results()

    # 保存报告
    save_report_to_word(report, output_path='analysis_report.docx')
