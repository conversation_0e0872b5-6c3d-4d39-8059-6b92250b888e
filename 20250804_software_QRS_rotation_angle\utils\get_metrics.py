"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

file: 
date: 2024/01/25 16:46
desc: 指标生成汇总
"""


from utils.utils import *
from utils.get_diagnosises import *
from utils.cal_interpolates import cal_interpolate
# from utils.gettimes1 import *
from utils.gettimes1 import gettime
import os
import datetime
import time
import scipy.special
import shutil
import itertools
# from joblib import Parallel, delayed
import random
import ast
import sys

# from flask import make_response


def analysis_disp1(d0, d1):
    '''观察轨迹的规律: mess_path, disp_path'''
    pts = txt_to_list('%s/idx0.txt' % d0)
    ids = get_lines1('%s/ids.txt' % d0)
    l1 = pts[2]
    # # TT异常数据分割333+262=595
    # d2 = '%s/trajects/tt_observe/tt1' % d1
    # d3 = '%s/trajects/tt_observe/0' % d1
    # d4 = '%s/trajects/tt_observe/1' % d1
    # for i in l1[0]:
    #     print(i)
    #     g1 = '%s/%s.png' % (d2, ids[i])
    #     g2 = '%s/%s.png' % (d3, ids[i])
    #     shutil.copyfile(g1, g2)
    # for i in l1[1]:
    #     g1 = '%s/%s.png' % (d2, ids[i])
    #     g2 = '%s/%s.png' % (d4, ids[i])
    #     shutil.copyfile(g1, g2)
    # # 缺血数据分割285+310=595
    # d3 = '%s/trajects/tt_isc/0' % d1
    # d4 = '%s/trajects/tt_isc/1' % d1
    # new_folder([d3, d4])
    # for i in range(285):
    #     print(i)
    #     g1 = '%s/%s.png' % (d2, ids[i])
    #     g2 = '%s/%s.png' % (d3, ids[i])
    #     shutil.copyfile(g1, g2)
    # for i in range(285, 595):
    #     g1 = '%s/%s.png' % (d2, ids[i])
    #     g2 = '%s/%s.png' % (d4, ids[i])
    #     shutil.copyfile(g1, g2)
    # 统计主分支
    d2 = '%s/branchs/tt1' % d1
    l2 = []
    d3 = '%s/trajects/tt_isc/00' % d1
    for item in os.listdir(d2):
        item = item[:-4]+'.txt'
        b1 = txt_to_list('%s/%s' % (d2, item))
        b2 = []
        b3 = []
        # for i in range(4, len(b1)):
        for i in range(4):
            for j in range(len(b1[i])):
                b2.append(b1[i][j])  # 分支
                for k in range(len(b1[i][j])):
                    b3.append(b1[i][j][k][0])  # 帧
        ts = max(b3)-min(b3)+1
        b4 = [round(sum(b2[i][j][1] for j in range(len(b2[i])))/ts*100, 1) for i in range(len(b2))]  # 有效长度
        # l2.append(max(b3)-min(b3))
        # b5 = [round(cal_num0(b2[i], 1, 100/ts, 1), 1) for i in range(len(b2))]  # 主极子长度
        # b6 = [round(cal_nonbd0(b2[i], 2, 3, 5, 96), 1) for i in range(len(b2))]  # 非边界率
        if min(b4) == 100:
            print(b4)
        # l2.append(min(b4))
        # if item[:-4] == 'SY_TT_000829':
        #     print(item[:-4], b4, b5, b6)
    l2.sort()
    print(l2)
    # 小分支筛除
    tx = 'qrs'
    d2 = '%s/branchs/%s' % (d1, tx)
    d3 = '%s/branchs/%s1' % (d1, tx)
    new_folder(d3)
    f00 = '%s/branchs/%s1_筛选.txt' % (d1, tx)
    b00 = []
    l0 = os.listdir(d2)
    for i0 in range(len(l0)):
        print('se_start', i0, 'end', len(l0)-i0)
        item = l0[i0]
        b1 = txt_to_list('%s/%s' % (d2, item))
        b3 = []
        for i in range(len(b1)):
            for j in range(len(b1[i])):
                for k in range(len(b1[i][j])):
                    b3.append(b1[i][j][k][0])  # 帧
        ts = max(b3)-min(b3)+1  # 统计帧数
        b2 = []
        b01 = []
        for i in range(4):
            b3 = []
            for j in range(len(b1[i])):
                br = b1[i][j].copy()
                if not rm_small_brs1(br, ts, 0):
                    b3.append(b1[i][j])  # 分支
            b2.append(b3)
            b01.append(len(b1[i])-len(b3))
        for i in range(4, len(b1)):
            b3 = []
            for j in range(len(b1[i])):
                br = b1[i][j].copy()
                if not rm_small_brs1(br, ts, 1):
                    b3.append(b1[i][j])  # 分支
            b2.append(b3)
            b01.append(len(b1[i])-len(b3))
        write_str('\n'.join([str(b2[i]) for i in range(len(b2))]), '%s/%s' % (d3, item))
        b00.append([item[:-4], b01])
    write_str('\n'.join([str(b00[i]) for i in range(len(b00))]), f00)
    # # 重新绘制轨迹
    # tx = 'tt'
    # d2 = '%s/branchs/%s1' % (d1, tx)
    # d3 = '%s/trajects/%s_observe/%s2' % (d1, tx, tx)
    # title = 'tracject_%s' % tx
    # new_folder(d3)
    # l0 = os.listdir(d2)
    # for i0 in range(len(l0)):
    #     print('ds_start', i0, 'end', len(l0)-i0)
    #     item = l0[i0]
    #     g1 = '%s/%s.png' % (d3, item[:-4])
    #     pts, lns = get_pts_lns1('%s/%s' % (d2, item))
    #     fig = plt.figure(dpi=120, figsize=(8, 8))
    #     ax = fig.add_subplot(111, projection='3d')
    #     draw111(ax, pts, lns)
    #     ax.set_title(title, pad=15, fontsize='10')
    #     plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    #     plt.close()
    # 主分支平面化
    tx = 'tt'
    d2 = '%s/branchs/%s1' % (d1, tx)
    d3 = '%s/trajects/%s_observe/%s3' % (d1, tx, tx)
    title = 'plane_%s' % tx
    new_folder(d3)
    # l0 = os.listdir(d2)
    l0 = get_lines1('%s/ids.txt' % d0)  # 全部ids顺序——
    # i0 = 2
    mts0, mts1 = [], []
    mts2, mts3, mts4 = [], [], []
    mts5, mts6 = [], []
    for i0 in range(len(l0)):
        # print('ds_start', i0, l0[i0], 'end', len(l0)-i0)
        print('ds_start', i0, 'end', len(l0)-i0)
        item = l0[i0]
        ts, pmtj, nmtj, xs1, ys1, xs2, ys2 = get_pts_lns2('%s/%s.txt' % (d2, item))
        # 主分支平面
        # xs1, ys1, xs2, ys2 = get_pts_lns2('%s/%s' % (d2, item))
        # fig = plt.figure(dpi=120, figsize=(8, 8))
        # plt.title(title)
        # plt.plot(xs1, ys1, c='r')
        # plt.scatter(xs1, ys1, c='r')
        # plt.plot(xs2, ys2, c='b')
        # plt.scatter(xs2, ys2, c='b')
        # g1 = '%s/%s.png' % (d3, item[:-4])
        # plt.savefig(g1, bbox_inches='tight', pad_inches=0)
        # plt.close()
        # 主分支参数2*15=2*(3+4*3)=30, 3*13=39
        e0, mt0 = get_tt_mtj_mts0(ts, pmtj, xs1, ys1, 1)
        e1, mt1 = get_tt_mtj_mts0(ts, nmtj, xs2, ys2, -1)
        mts0.append(mt0)
        mts1.append(mt1)
        mt2, mt3, mt4 = get_tt_mtj_dimts0(pmtj, nmtj, e0, e1, ts, mf)
        mts2.append(mt2)
        mts3.append(mt3)
        mts4.append(mt4)
        # 次分支参数2*18=36
        mf = txt_to_list('%s/branchs/mf/%s.txt' % (d1, item))
        mt1, mt2 = get_pts_lns3([], mf, '%s/%s.txt' % (d2, item))
        mts5.append(mt1)
        mts6.append(mt2)
    save_txt(mts0, 'list', '%s_mtj_mt0.txt' % d3)  # 主分支单极子参数
    save_txt(mts1, 'list', '%s_mtj_mt1.txt' % d3)
    save_txt(mts2, 'list', '%s_mtj_mt2.txt' % d3)  # 主分支偶极子参数
    save_txt(mts3, 'list', '%s_mtj_mt3.txt' % d3)
    save_txt(mts4, 'list', '%s_mtj_mt4.txt' % d3)
    save_txt(mts5, 'list', '%s_mtj_mt5.txt' % d3)
    save_txt(mts6, 'list', '%s_mtj_mt6.txt' % d3)
    # tx, i = 2, 1
    for tx in range(0, 7):
        l0 = txt_to_list('%s_mtj_mt%d.txt' % (d3, tx))
        l0 = [[l0[i][j] for i in range(len(l0))] for j in range(len(l0[0]))]
        for i in range(len(l0)):
            l2 = l0[i]  # 参数集
            l1 = seg_scale0(l2, 10)  # 线性调整后的参数集
            l2.sort()
            l1.sort()
            print(tx, i, l2[0], l2[-1], l1[0], l1[-1])
    # plt.hist(l0[0], bins=10)
    # plt.show()
    # plt.close()
    # plt.hist(l1, bins=10)
    # plt.show()
    # print(len(l0), len(l0[2]))
    return d1


def seg_scale0(l1, y0):
    '''分段线性调整数据列表'''
    vs, ys = get_split_lis0(l1, y0)
    l2 = cal_scale_lis0(l1, vs, ys)
    return l2


def cal_scale_lis0(l1, vs, ys):
    '''计算线性调整数据列表'''
    l2 = []
    for i in range(len(l1)):
        v0 = l1[i]
        v1 = ys[-1]
        if v0 <= vs[0]:
            v1 = ys[0]
        elif v0 >= vs[-1]:
            v1 = ys[-1]
        else:
            for j in range(len(vs)-1):
                x0, x1 = vs[j], vs[j+1]
                if v0 < x1:
                    y0, y1 = ys[j], ys[j+1]
                    v1 = y0 + (y1-y0)*(v0-x0)/(x1-x0)
                    break
            v1 = max(ys[0], min(ys[-1], v1))
        l2.append(round(v1, 3))
    return l2


def get_inter0(l1, ik):
    '''列表ik等分的中间值'''
    ls = []
    n1 = len(l1)/ik
    for i in range(1, int(ik)):
        ls.append(l1[round(i*n1)])
    return ls


def get_split_lis0(l0, y0, n0=10):
    '''10等分的分裂列表数值: 全正/全负/正负'''
    mi, ma = min(l0), max(l0)
    if ma <= 0:  # 负值
        l1 = [l0[i] for i in range(len(l0)) if l0[i] < 0]
        l1.sort()
        l2 = get_inter0(l1, n0)
        y1 = y0*ma/mi
        ys = [-y1-(y0-y1)*i/n0 for i in range(n0+1)]
        ys.sort()
    elif mi >= 0:  # 正值
        l1 = [l0[i] for i in range(len(l0)) if l0[i] > 0]
        l1.sort()
        l2 = get_inter0(l1, n0)
        y1 = y0*mi/ma
        ys = [y1+(y0-y1)*i/n0 for i in range(n0+1)]
    else:  # 正负都有
        l1 = [l0[i] for i in range(len(l0)) if l0[i] < 0]
        l1.sort()
        l3 = get_inter0(l1, n0/2)
        l1 = [l0[i] for i in range(len(l0)) if l0[i] > 0]
        l1.sort()
        l4 = get_inter0(l1, n0/2)
        l2 = l3 + [0] + l4
        ys1 = [-y0*i*2/n0 for i in range(int(n0/2+1))]
        ys1.sort()
        ys2 = [y0*i*2/n0 for i in range(1, int(n0/2+1))]
        ys = ys1 + ys2
    ls = [mi] + l2 + [ma]
    return ls, ys


def get_tt_stj_mts0(b2, mf, amf):
    '''次分支参数提取'''
    snbd, selg, sapa, sasp, saec, saep = get_tt_stj_mts1(b2[1], b2[2], mf, amf)
    # inbd, ielg, iapa, iasp, iaec, iaep = get_tt_stj_mts1(b2[2], b2[3], mf, amf)
    # anbd = snbd + inbd
    # aelg = selg + ielg
    # aapa = sapa + iapa
    # aasp = sasp + iasp
    # aaec = saec + iaec
    # aaep = saep + iaep
    # anbd, aelg, aapa, aasp, aaec, aaep = get_round([anbd, aelg, aapa, aasp, aaec, aaep], [1, 1, 1, 1, 1, 1])
    # return [snbd, selg, sapa, sasp, saec, saep, inbd, ielg, iapa, iasp, iaec, iaep, anbd, aelg, aapa, aasp, aaec, aaep]
    return [snbd, selg, sapa, sasp, saec, saep]


def get_tt_mtj_dimts0(pmtj, nmtj, e0, e1, ts, mf):
    '''
    计算TT主分支的偶极子指标:
        主分支匹配有效长度、主段匹配有效长度、非边界率
        主分支mtj、主段msg
        整体转角、所在象限、正负指向面积、偶极子距离的最大值、均值、均方差
        偶极子中心轨迹路径、跨度、弹性曲率
    '''
    p0, n0, mg0 = get_mf_dxyndy0(pmtj, nmtj, mf)  # 主分支
    p1, n1, mg1 = get_mf_dxyndy0(pmtj[e0[0]:e0[1]], nmtj[e1[0]:e1[1]], mf)  # 主段
    mts0 = cal_tt_mtj_dimts0(mg0, p0, n0, ts, 2, 3, 5, 96)  # 主分支13个参数
    mts1 = cal_tt_mtj_dimts0(mg1, p1, n1, ts, 2, 3, 5, 96)  # 主段13个参数
    mts2 = cal_tt_mtj_dimts1(mts0, mts1)  # 主分支-主段的13个参数差
    return mts0, mts1, mts2


def diff(l0, l1, iks=0):
    '''两个值/列表作差'''
    if type(l0) == list:
        if type(iks) == list:
            l2 = []
            for i in range(len(iks)):
                l2.append(diff(l0, l1, iks[i]))
            return l2
        else:
            return l0[iks]-l1[iks]
    else:
        return l0-l1


def cal_bianchang0(Z):
    '''矩阵的1区域边数量: 边界数量nm1, 弧线数量nm2'''
    nm1, nm2 = 0, 0
    for i in range(Z.shape[0]):
        for j in range(Z.shape[1]):
            if Z[i, j] == 1:
                fg = 0
                if i in [0, 99] or j in [0, 99]:
                    nm2 += 1
                else:
                    for i1,j1 in [[-1,0],[1,0],[0,1],[0,-1]]:
                        if Z[i-i1,j-j1] == 0:
                            nm1 += 1
                            break
                # if fg:
                #     nm += 1
    return nm1, nm2


def cal_disp_mes1(Z, v0=0.3, ik=0):
    '''计算离散度信息: 磁极的周长、面积、圆形度、形态个数'''
    from PIL import Image
    from scipy.ndimage import binary_fill_holes
    from skimage import measure
    if ik == 1:  # 负磁极
        matrix = np.where(Z<v0, 1, 0).astype(np.uint8)
    else:
        matrix = np.where(Z>v0, 1, 0).astype(np.uint8)
    image = Image.fromarray(matrix*255)  # 转换为图像
    filled_image = binary_fill_holes(np.array(image))  # 填充小孔
    binary_image = filled_image > 0  # 转换二值图像
    label_image = measure.label(binary_image)  # 测量连通组件
    areas = []  # 面积
    bjls = []  # 边界率
    bjcos = []  # 边界坐标
    es = []
    # perimeters = []
    for region in measure.regionprops(label_image):  # 遍历每个连通组件
        areas.append(region.area)  # 计算面积
        l1 = region.coords
        l1 = [list(l1[i]) for i in range(len(l1))]
        cx = np.mean(np.array([l1[i][0] for i in range(len(l1))]))
        cy = np.mean(np.array([l1[i][1] for i in range(len(l1))]))
        l2 = [product2(cx-l1[i][0], cy-l1[i][1]) for i in range(len(l1))]
        ma = max(l2)
        mi = sum(l2)/len(l2)
        e = product2(ma, mi, 1)/mi
        es.append(e)
        # print(list(l1[0]))
        # print(l1)
        Z0 = np.zeros((100, 100), dtype=int)
        for [i, j] in region.coords:
            Z0[i, j] = 1
        nm1, nm2 = cal_bianchang1(Z0, 1)  # 边界坐标
        bjls.append(len(nm2)/len(nm1+nm2))
        bjcos.append(sort_pts0(nm1))  # 弧线段
    #     # perimeters.append(round(region.perimeter))  # 计算周长
    # print(len(areas))
    # ar = sum(areas)
    # # pr0 = sum(perimeters)
    # pr1, pr2 = cal_bianchang0(matrix)
    # # print(pr1, pr2)
    # pr = pr1 + pr2
    # br = cal_boundrt0(pr1, pr2)
    # cr = 0
    # if ar:
    #     cr = pr*pr/4/math.pi/ar
    # nm = len(measure.regionprops(label_image))
    # ar, pr1, cr, br = get_round([ar/100, pr1/1000, cr, br], [3, 3, 3, 3])
    # return [ar, pr1, cr, nm, br]
    return areas, bjls, bjcos# , es


def cal_disp_mes2(Z, v1=0.3):
    ls = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
    px, py = np.unravel_index(np.argmax(Z), Z.shape)
    pts = [[[[px, py]]]]
    for v0 in ls:
        if not v0 >= v1:
            continue
        matrix = np.where(Z>v0, 1, 0).astype(np.uint8)
        pt2 = sort_pts0(cal_bianchang1(matrix))
        pts.append(pt2)
        # if not v0 < 0.4:
        #     continue
        print(v0)
        if len(pts) > 1:
            ds = cal_pts_dis0(pts[-1], pts[-2], 13-int(v0*10))
            # print(ds)
        # print(v0, len(pt2))
        # print(pt2)
    ls = [-0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3, -0.2, -0.1]
    px, py = np.unravel_index(np.argmin(Z), Z.shape)
    pts = [[[[px, py]]]]
    for v0 in ls:
        if not v0 <= -v1:
            continue
        matrix = np.where(Z<v0, 1, 0).astype(np.uint8)
        pt2 = sort_pts0(cal_bianchang1(matrix))
        pts.append(pt2)
        print(v0)
        if len(pts) > 1:
            ds = cal_pts_dis0(pts[-1], pts[-2], 13+int(v0*10))
    return Z


def cal_connec0(ps1, ps2):
    '''匹配连通区域: 形态ps1<ps2'''
    p2, b = [], []
    f1 = []
    for i in range(len(ps2)):
        b1 = []
        for p0 in ps2[i]:
            b1 += p0
        p2.append(b1)  # 单连通弧线点集
        b += b1  # 源弧线汇总点集
        f1.append([[], []])  # 统计目标索引、距离均值
    for i1 in range(len(ps1)):
        p1 += ps1[i1]
        a, ds, p0s = [], [], []
        for p0 in p1:  # 现弧线汇总
            a += p0
        for i in range(len(a)):
            d = [product2(a[i][0]-b[j][0],a[i][1]-b[j][1]) for j in range(len(b))]
            ds.append(round(min(d), 2))
            p0s.append(b[int(d.index(min(d)))])  # 匹配源点集
        iks = [[j for j in range(len(p0s)) if p0s[j] in p2[i]] for i in range(len(p2))]  # 分类源点集
        dss = [[ds[j] for j in iks[i]] for i in range(len(iks))]  # 统计距离集
        for i in range(len(dss)):
            d0 = dss[i]
            if d0 != []:
                f1[i][0].append(i1)
                f1[i][1].append(sum(d0)/len(d0))
            iks = [j for j in range(len(p0s)) if p0s[j]]


def cal_elm_nums0(ls):
    '''统计列表的不重复数量'''
    l0 = []
    for a in ls:
        if a not in l0:
            l0.append(a)
    return len(l0)


def cal_boundrt0(pr1, pr2):
    '''计算边界缺失的磁极边界率: 0-1-2'''
    br = 2
    if pr1:  # 全边界=2
        bi = pr2 / pr1
        if bi > 2/math.pi:
            br = min(1, (bi-2/math.pi)/(1-2/math.pi))+1
        else:
            br = min(1, bi*math.pi/2)
    return br


def cal_disp_mes0(matrix, v1=0.3):
    '''统计离散度信息: 磁极的周长、面积、圆形度、形态个数'''
    rs = [[], [], [], [], []]
    ls = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
    for v0 in ls:
        if not v0 >= v1:
            continue
        rs0 = cal_disp_mes1(matrix, v0, 0)
        rs[0].append(rs0[0])
        rs[1].append(rs0[1])
        rs[2].append(rs0[2])
        rs[3].append(rs0[3])
        rs[4].append(rs0[4])
    rs1 = [[], [], [], [], []]
    ls = [-0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3, -0.2, -0.1]
    for v0 in ls:
        if not v0 <= -v1:
            continue
        rs0 = cal_disp_mes1(matrix, v0, 1)
        rs1[0].append(rs0[0])
        rs1[1].append(rs0[1])
        rs1[2].append(rs0[2])
        rs1[3].append(rs0[3])
        rs1[4].append(rs0[4])
    return rs, rs1


def disp_rks2(d0, d1, d2):
    '''离散度参数: mess_path, data_path, disp_path'''
    # 形态图汇总
    ls = ['0', '5', '10', '15', '20', '20+']
    d3 = '%s/mcgimg/peaks/disp_rks1' % d2
    # for tx in ls:
    #     d4 = '%s/%s' % (d3, tx)
    #     for item in os.listdir(d4):
    #         f0 = '%s/%s' % (d4, item)
    #         f1 = '%s/all/%s' % (d3, item)
    #         shutil.copyfile(f0, f1)
    # # 汇总disp1参数
    # l0 = []
    # for tx in ls:
    #     f0 = '%s/mess/%s/pn_disp1.txt' % (d3, tx)
    #     l1 = get_lines1(f0)
    #     l0 += l1
    # l2 = get_item(l0, 1, [])
    # a = sorted(enumerate(l2), key=lambda item: item[1])
    # b = [a[i][0] for i in range(len(a))]
    # # strs = '\n'.join([l0[i] for i in b])
    # # write_str(strs, '%s/disp1_all.txt' % d3)
    # strs = '\n'.join([str(l2[i]) for i in b])
    # write_str(strs, '%s/disp1_mts.txt' % d3)
    # l3 = get_item(l0, 0, [])
    # strs = '\n'.join([l3[i] for i in b])
    # write_str(strs, '%s/disp1_ids.txt' % d3)
    # 等级文件夹
    vs = [0.1, 0.2, 0.4, 0.6, 1.0, 2.0]
    l0 = txt_to_list('%s/disp1_mts.txt' % d3)
    l1 = get_lines1('%s/disp1_ids.txt' % d3)
    # n = [0]
    # # n.append(sum(1 for i in range(len(l0)) if l0[i] < vs[0]))
    # for j in range(len(vs)):
    #     n.append(sum(1 for i in range(len(l0)) if l0[i] < vs[j]))
    #     # if j < len(vs) - 1:
    #     #     n2 = sum(1 for i in range(len(l0)) if l0[i] >= vs[j])
    #     #     n.append(n1-n2)
    #     # else:
    #     #     n.append(n1)
    # n.append(len(l0))
    # # m = [0] + [sum(n[:i]) for i in range(len(n))]
    # m=n
    # vs = ['-'+str(vs[0])] + vs
    # for i in range(len(m)-1):
    #     j, k = m[i], m[i+1]
    #     print(j, k)
    #     d4 = '%s/mcgimg/peaks/disp_rks2/%s' % (d2, str(vs[i]))
    #     new_folder(d4)
    #     for item in l1[j:k]:
    #         f0 = '%s/all/%s.png' % (d3, item)
    #         f1 = '%s/%s.png' % (d4, item)
    #         shutil.copyfile(f0, f1)
    for i in range(len(l0)):
        d0 = '%s/mcgimg/peaks/disp_rks3/%.1f' % (d2, l0[i])
        new_folder(d0)
        f0 = '%s/all/%s.png' % (d3, l1[i])
        f1 = '%s/%s.png' % (d0, l1[i])
        shutil.copyfile(f0, f1)
    return d2


def add_list_tj1(l0, l1):
    if type(l1) == list:
        for item in l1:
            l0 = add_list_tj1(l0, item)
        return l0
    else:
        l0.append(l1)
        return l0


def write_all_lsd0(itms, tj11, d8, d7):
    '''写入全部离散度信息: '''
    # 排序qts, jhs, jls, xts, lsds = lis0
    lsds = [max(tj11[i][0][-1][-1], tj11[i][1][-1][-1]) for i in range(len(tj11))]
    st = sorted(enumerate(lsds), key=lambda item: item[1])
    idx = [index for index, value in st]
    vs = [value for index, value in st]
    itms = [itms[i] for i in idx]
    p_qts = [tj11[i][0][0] for i in idx]
    p_jhs = [tj11[i][0][1] for i in idx]
    p_jls = [tj11[i][0][2] for i in idx]
    p_xts = [tj11[i][0][3] for i in idx]
    p_lsd = [tj11[i][0][4] for i in idx]
    n_qts = [tj11[i][1][0] for i in idx]
    n_jhs = [tj11[i][1][1] for i in idx]
    n_jls = [tj11[i][1][2] for i in idx]
    n_xts = [tj11[i][1][3] for i in idx]
    n_lsd = [tj11[i][1][4] for i in idx]
    strs = '\n'.join([str(p_qts[i])+'; '+str(n_qts[i]) for i in range(len(p_qts))])
    write_str(strs, '%s/messages/pn_qts.txt' % d8)
    strs = '\n'.join([str(p_jhs[i])+'; '+str(n_jhs[i]) for i in range(len(p_jhs))])
    write_str(strs, '%s/messages/pn_jhs.txt' % d8)
    strs = '\n'.join([str(p_jls[i])+'; '+str(n_jls[i]) for i in range(len(p_jls))])
    write_str(strs, '%s/messages/pn_jls.txt' % d8)
    strs = '\n'.join([str(p_xts[i])+'; '+str(n_xts[i]) for i in range(len(p_xts))])
    write_str(strs, '%s/messages/pn_xts.txt' % d8)
    strs = '\n'.join([str(p_lsd[i])+'; '+str(n_lsd[i]) for i in range(len(p_lsd))])
    write_str(strs, '%s/messages/pn_lsd.txt' % d8)
    write_str('\n'.join(itms), '%s/messages/ids.txt' % d8)
    tj11 = [tj11[i] for i in idx]
    # for i in range(len(vs)):
    # l0 = [-1, 1, 3, 5, 8, 11]  # 取
    # for i1 in range(len(l0)-1):
    #     v0, v1 = l0[i1], l0[i1+1]
    #     idx0 = [i for i in range(len(vs)) if vs[i]<v1 and vs[i]>v0]
    #     for i in idx0:
    #         dp, dn = tj11[i][0][-1][-1],tj11[i][1][-1][-1]
    #         f0 = '%s/%s.png' % (d7, itms[i])
    #         f1 = '%s/%d/%s_%.2f_%.2f.png' % (d8, i1, itms[i], dp, dn)
    #         shutil.copyfile(f0, f1)
    return d8


def write_all_lsd1(d8, d7):
    '''分级文件夹: d8目录, pnlsd1生成正负离散度列表文档'''
    ls0 = txt_to_list('%s/messages/pn_lsd1.txt' % d8)  # 正负离散度列表
    ls1 = [[ls0[2*i][-1], ls0[2*i+1][-1]] for i in range(int(len(ls0)/2))]
    ids = get_lines1('%s/messages/ids.txt' % d8)
    for i in range(len(ls1)):
        f0 = '%s/%s.png' % (d7, ids[i])
        if max(ls1[i]) < 1:
            d = 0
        elif max(ls1[i]) < 3:
            d = 1
        elif max(ls1[i]) >= 8:
            d = 4
        elif max(ls1[i])>=5 or sum(ls1[i])>5.3:
            d = 3
        else:
            d = 2
        f1 = '%s/%d/%s_%.2f_%.2f.png' % (d8, d, ids[i], ls1[i][0], ls1[i][1])
        shutil.copyfile(f0, f1)
    return d8


def disp_rks1(d0, d1, d2):
    '''离散度参数: mess_path, data_path, disp_path'''
    # ls = ['0', '5', '10', '15', '20', '20+']
    itms = []
    tj11, tj22, tj33, tj44, tj55 = [], [], [], [], []
    # tj11, tj22 = [], []
    d7 = '%s/mcgimg/peaks/disp_rks1/resall' % d2
    # d7 = '%s/mcgimg/peaks/disp_rks3' % d2
    d8 = '%s/mcgimg/peaks/disp_rks43' % d2  # 保存位置
    # new_folder(d8)
    # 微调算法
    # d81 = '%s/2' % d8
    # for item in os.listdir(d81):
    #     f1, f2 = float(item[-13:-9]), float(item[-8:-4])
    #     if f1+f2>5.5:
    #         f0 = '%s/%s' % (d81, item)
    #         f1 = '%s/cache1/2_55/%s' % (d8, item)
    #         shutil.copyfile(f0, f1)
    #     elif f1+f2>5:
    #         f0 = '%s/%s' % (d81, item)
    #         f1 = '%s/cache1/2_50/%s' % (d8, item)
    #         shutil.copyfile(f0, f1)
    #     elif max(f1, f2)<3.3:
    #         f0 = '%s/%s' % (d81, item)
    #         f1 = '%s/cache1/2_33/%s' % (d8, item)
    #         shutil.copyfile(f0, f1)
    #     else:
    #         f0 = '%s/%s' % (d81, item)
    #         f1 = '%s/cache1/2_34/%s' % (d8, item)
    #         shutil.copyfile(f0, f1)
    d7 = '%s/mcgimg/peaks/disp_rks41/cache1/2_50' % d2
    d7 = d1
    cnt = 0
    l1 = os.listdir(d7)
    for item in os.listdir(d7):
        cnt += 1
        # if cnt > 20:
        #     continue
        print(cnt, 'left', len(l1)-cnt, item)
        # if item[:-4] != '200-X王莉理_339':
        # # if item[:-4] != 'SY_TT_000870_665':
        # # # if item not in l7:
        #     continue
        # if cnt != 38:
        #     continue
        # mcgfile = '%s/%s.txt' % (d1, item[:-8])
        mcgfile = '%s/%s' % (d1, item)
        # mcgfile = '%s/%s.txt' % (d1, item[:-18])
        j = int(item[-7:-4])
        # j = int(item[-17:-14])
        Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettime(mcgfile)
        for j in [Qp, Rp, Sp, Tp]:
            M0 = cal_interpolate(mcgfile, Qp=j)
            Z = norms0(M0[j])
            # 连通嵌套
            itms.append(item[:-4])
            tj1, tj2, tj3, tj4, tj5 = [], [], [], [], []
            tj1, tj2 = [], []
            ls = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3]#, 0.2, 0.1]
            zbs, qts = cal_zb_qt0(ls, Z)
            lis0 = cal_jh_jl_xt0(zbs, qts, Z)
            dp = lis0[-1][-1]
            # qts, jhs, jls, xts, lsds = lis0
            tj1.append(lis0)
            # tj1.append(lsds)
            # tj2.append(lsd0)
            # tj1 = add_list_tj1(tj1, jhs)
            # tj2 = add_list_tj1(tj2, jls)
            # tj3 = add_list_tj1(tj3, xts)
            ls = [-0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3]#, -0.2, -0.1]
            zbs, qts = cal_zb_qt0(ls, Z)
            lis0 = cal_jh_jl_xt0(zbs, qts, Z)
            dn = lis0[-1][-1]
            # print(item, dp)
            tj1.append(lis0)
            # tj1.append(lsds)
            # tj2.append(lsd0)
            tj11.append(tj1)
            ls1 = [dp, dn]
            if max(ls1) < 1:
                d = 0
            elif max(ls1) < 3:
                d = 1
            elif max(ls1) >= 8:
                d = 4
            elif max(ls1)>=5 or sum(ls1)>5.3:
                d = 3
            else:
                d = 2
            img1 = '%s/%d/%s_%d_%.2f_%.2f.png' % (d8, d, item[:-4], j, ls1[0], ls1[1])
            if not os.path.exists(img1):
                draw_mcg0(Z, img1, ct=1)
            # tj22.append(tj2)
    # print(tj11)
    # d00 = write_all_lsd0(itms, tj11, d8, d7)
    # d00 = write_all_lsd1(d8, d7)
    # print(tj22)
    # print(tj11)
    # print(tj22)
    # print(tj33)
    # print(itms)
    return d2


def cal_diff_area0(a, display=0):
    '''面积差分波动性计算'''
    a1 = [a[i+1]-a[i] for i in range(len(a)-1)]
    b = [a1[i+1]-a1[i] for i in range(len(a1)-1)]
    mean = round(sum(b)/len(b), 1)  # 避免除法爆炸——
    if mean == 0:
        c = [((b[i]-mean))**2 for i in range(len(b))]
    else:
        c = [((b[i]-mean)/mean)**2 for i in range(len(b))]
    c.reverse()
    v = sum(c[i]*(i+1) for i in range(len(c))) / len(c)
    return v#*sum(a)/len(a)  # 按周长调整/小的不算


def cal_diff_peri0(a):
    '''周长差分波动性计算'''
    b = [a[i+1]-a[i] for i in range(len(a)-1)]
    mean = round(sum(b)/len(b), 1)
    if mean == 0:
        c = [((b[i]-mean))**2 for i in range(len(b))]
    else:
        c = [((b[i]-mean)/mean)**2 for i in range(len(b))]
    c.reverse()
    v = sum(c[i]*(i+1) for i in range(len(c))) / len(c)
    return v*sum(a)/len(a)  # 按周长调整/小的不算


def write_mts0(mes, d0):
    txs = ['p_perimeter.txt', 'p_area.txt', 'p_circularity.txt', 'p_formnums.txt', 'p_boundrt.txt', 'n_perimeter.txt', 'n_area.txt', 'n_circularity.txt', 'n_formnums.txt', 'n_boundrt.txt', 'ids.txt']
    for i in range(len(mes)):
        l0 = mes[i]
        f0 = '%s/%s' % (d0, txs[i])
        write_str('\n'.join([str(l0[j]) for j in range(len(l0))]), f0)
    return d0


def disp_rks0(d0, d1, d2):
    '''离散度分级(源算法): mess_path, data_path, disp_path'''
    ls = [0, 5, 10, 15, 20]
    ids = get_lines1('%s/ids.txt' % d0)
    tf0 = get_lines1('%s/times.txt' % d0)
    for i in range(300):
        print('start', i, 'end', 300-i)
        item = ids[i]
        mcgfile = '%s/%s.txt' % (d1, item)
        ts0 = ast.literal_eval('[%s]' % tf0[i])
        mt0 = len(get_lines1(mcgfile)) - 1
        for j in ts0[1:]:
            if j <= mt0:
                # if j < 100 or j > 999:  # 检查时刻点
                #     print(item)
                #     break
                M0 = cal_interpolate(mcgfile, Qp=j)
                Z0 = M0[j]
                Z = norms0(Z0)  # 正负归一化
                pn = statpn(Z, vmin=0.3)  # 多极子
                dpp, dpn = caldp(pn)  # 离散度
                dp = max(dpp, dpn)
                tx0 = '20+'
                for k in range(len(ls)):
                    if dp <= ls[k]:
                        tx0 = str(ls[k])
                        break
                new_folder('%s/mcgimg/peaks/disp_rks1/%s' % (d2, tx0))
                g1 = '%s/mcgimg/peaks/disp_rks1/%s/%s_%d.png' % (d2, tx0, item, j)
                g00 = draw_mcg0(Z, g1, 'dp: %f, %f' % (dpp, dpn), 1)
    return d2


def get_trajects_mts0(f0, times):
    M0 = cal_interpolate0(f0)
    


def cal_traject1(d0, d1, d2):
    '''极子统计、轨迹信息、轨迹生成: mess_path, data_path, disp_path'''
    ids = get_lines1('%s/ids.txt' % d0)
    tms = get_lines1('%s/times.txt' % d0)
    # 写入极子和轨迹分支信息
    d3 = '%s/poles' % d2
    d4 = '%s/branchs' % d2
    d5 = '%s/qrs' % d4
    d6 = '%s/tt' % d4
    d7 = '%s/mf' % d4
    new_folder([d3, d4, d5, d6, d7])
    for i in range(len(ids)):
        print('start', i, 'end', len(ids)-i)
        M0 = cal_interpolate0('%s/%s.txt' % (d1, ids[i]))  # 插值——
        times = ast.literal_eval('[%s]' % tms[i])
        f1 = '%s/%s.txt' % (d5, ids[i])
        f2 = '%s/%s.txt' % (d3, ids[i])
        f3 = '%s/%s.txt' % (d6, ids[i])
        f00 = get_traject1(times, 0, 2, M0, f1, pnfile=f2)
        f00 = get_traject1(times, 3, 5, M0, f3, pnfile=f2)
        l0 = get_mf0(M0)
        save_txt(l0, 'list', '%s/%s.txt' % (d7, ids[i]))
    # # 绘制多极子轨迹图
    # d3 = '%s/trajects' % d2
    # d7 = '%s/qrs' % d3
    # d8 = '%s/tt' % d3
    # new_folder([d3, d7, d8])
    # for i in range(len(ids)):
    #     print('start', i, 'end', len(ids)-i)
    #     fs = ['%s/%s.txt' % (d5, ids[i]), '%s/%s.txt' % (d6, ids[i])]
    #     gs = ['%s/%s.png' % (d7, ids[i]), '%s/%s.png' % (d8, ids[i])]
    #     txs = ['tracject_QRS', 'tracject_TT']
    #     if os.path.exists(gs[0]):
    #         continue
    #     for j in [0, 1]:
    #         pts, lns = get_pts_lns1(fs[j])
    #         fig = plt.figure(dpi=120, figsize=(8, 8))
    #         ax = fig.add_subplot(111, projection='3d')
    #         draw111(ax, pts, lns)
    #         ax.set_title(txs[j], pad=15, fontsize='10')
    #         plt.savefig(gs[j], bbox_inches='tight', pad_inches=0)
    #         plt.close()
    return d2


def draw_mcg0(Z, savedir, title='', ct=0):
    plt.rcParams['figure.figsize'] = (8, 8)
    plt.rcParams['savefig.dpi'] = 64
    plt.matshow(Z, cmap='jet')
    plt.xticks([])
    plt.yticks([])
    plt.axis('off')
    plt.margins(0, 0)
    plt.draw()
    if title:
        plt.title(title)
    if ct:
        C = plt.contour(
            Z,
            colors='k',
            levels=np.linspace(-1, 1, 21))  # 10等分
        plt.clabel(C, inline=True, fontsize=12)  # 添加高度值
    plt.savefig(savedir, bbox_inches='tight', pad_inches=0)
    plt.close()
    return savedir


def draw_mcg00(Z, img1, title=''):
    if not os.path.exists(img1):
        try:
            draw_mcg0(Z, img1, title)
        except:
            pass


def rmlist(l0, ik):
    l1 = l0.copy()
    l1 = [i for i in l1 if i != ik]
    for i in range(len(l1)):
        if l1[i] > ik:
            l1[i] -= 1
    return l1


def observe0(d0, d1, d2):
    '''提取可视化观察规律(设计): mess_path, obse_path, disp_path'''
    ids = get_lines1('%s/ids.txt' % d0)
    pts = txt_to_list('%s/idx0.txt' % d0)
    tms = get_lines1('%s/times.txt' % d0)
    ids0, ids1 = pts[-1][0], pts[-1][1]
    d3 = '%s/mcgimg' % d1
    d4 = '%s/peaks' % d3
    d20 = '%s/mcgimgs' % d2
    txs = ['Q', 'R', 'S', 'T']
    ls = ['%s/%s' % (d4, tx) for tx in txs]
    new_folder([d3, d4, d20]+ls)  # 新建0/1文件夹——
    for i in ids0:
        l1 = get_split(tms[i])
        ts = [int(l1[0]), int(l1[1]), int(l1[2]), int(l1[4])]
        for j in range(len(ts)):
            f0 = '%s/%s/%d.png' % (d20, ids[i], ts[j])
            if os.path.exists(f0):
                f1 = '%s/%s/0/%s.png' % (d4, txs[j], ids[i])
                if not os.path.exists(f1):
                    shutil.copyfile(f0, f1)
            # else:
            #     print(ids[i])
    for i in ids1:
        l1 = get_split(tms[i])
        ts = [int(l1[0]), int(l1[1]), int(l1[2]), int(l1[4])]
        for j in range(len(ts)):
            f0 = '%s/%s/%d.png' % (d20, ids[i], ts[j])
            if os.path.exists(f0):
                f1 = '%s/%s/1/%s.png' % (d4, txs[j], ids[i])
                if not os.path.exists(f1):
                    shutil.copyfile(f0, f1)
    # 提取全部的QRS/T波峰的0/1
    for tx in txs:
        for i in [0, 1]:
            for item in os.listdir('%s/%s/%d' % (d4, tx, i)):
                f0 = '%s/%s/%d/%s' % (d4, tx, i, item)
                f1 = '%s/%d/%s_%s.png' % (d4, i, item[:-4], tx)
                if not os.path.exists(f1):
                    shutil.copyfile(f0, f1)
    return d2


def display0(d0, d1, d2):
    '''绘制等磁图和形态图: mess_path, data_path, disp_path'''
    ids = get_lines1('%s/ids.txt' % d0)
    tms = get_lines1('%s/times.txt' % d0)
    d3 = '%s/mcgimgs' % d2
    d4 = '%s/mcgforms' % d2
    new_folder([d3, d4])
    strs, n0 = [], 0
    l0 = []
    # for i in range(len(ids)):
    for i in range(323, len(ids)):
        print('start:', i, 'left:', len(ids)-i)
        d5 = '%s/%s' % (d3, ids[i])
        new_folder(d5)
        f0 = '%s/%s.txt' % (d1, ids[i])
        #     if not os.path.exists(f0):
        #         print(ids[i])
        #         l0.append(i)
        # l0.reverse()
        # l1 = get_lines1('%s/labels_raw.txt' % d0)
        # pts = txt_to_list('%s/idx0.txt' % d0)
        # for i in l0:
        #     ids.pop(i)
        #     tms.pop(i)
        #     l1.pop(i)
        #     for j in range(len(pts)):
        #         pts[j][0] = rmlist(pts[j][0], i)
        #         pts[j][1] = rmlist(pts[j][1], i)
        # write_str('\n'.join(ids), '%s/ids.txt' % d0)
        # write_str('\n'.join(tms), '%s/times.txt' % d0)
        # write_str('\n'.join(l1), '%s/labels_raw.txt' % d0)
        # save_txt(pts, 'list', '%s/idx0.txt' % d0)
        M0 = cal_interpolate(f0)
        l1 = get_split(tms[i])
        Q, R, S, T1, T2, T3 = int(l1[0]), int(l1[1]), int(l1[2]), int(l1[3]), int(l1[4]), int(l1[5])
        Q, R, S, T1, T2, T3 = xiuzheng([Q, R, S, T1, T2, T3], len(M0)-1)
        for j in range(Q, S+1):
            img1 = '%s/%d.png' % (d5, j)
            if not os.path.exists(img1):
                draw_mcg00(M0[j], img1)
        for j in range(T1, T3+1):
            img1 = '%s/%d.png' % (d5, j)
            if not os.path.exists(img1):
                draw_mcg00(M0[j], img1)
    return d1


def xiuzheng(t1, t2):
    if type(t1) == list:
        l0 = []
        for i in range(len(t1)):
            l0.append(xiuzheng(t1[i], t2))
        return l0
    else:
        return min(t1, t2)


def get_split1(f1, f2):
    '''QR/RS/TT异常标签'''
    l0 = get_lines1(f1)
    ls = [[[], []], [[], []], [[], []]]
    for i in range(len(l0)):
        l1 = get_split(l0[i])
        for j in [2, 3, 4]:
            ls[j-2][int(l1[j])].append(i)
    save_txt(ls, 'list', f2)
    return f2


def add_f3(pts, f4, f3, f2):
    '''添加1506阴阳标签'''
    lbs = '0000x00x100x00101001x0111111x11x011x11010110x100'  # 类型
    tx20 = ['0000x000x', '11010111x1x1111']
    tx2 = tx20.copy()
    for j in range(len(tx20)):
        l0 = []
        for i in range(int(len(tx20[j])/3)):
            l0.append(tx20[j][3*i:3*i+3])
        tx2[j] = l0.copy()
    l00 = [[], []]
    for i in range(16):
        lbs1 = lbs[3*i:3*i+3]
        for j in range(len(tx2)):
            if lbs1 in tx2[j]:
                l00[j] += pts[i]
                break
    ids0, ids1 = l00[0], l00[1]
    l0 = get_lines1(f3)
    l1 = get_lines1(f4)
    num = len(l0)
    l0 += [l1[i] for i in ids0]
    l0 += [l1[i] for i in ids1]
    write_str('\n'.join(l0), f3)
    l0 = get_lines1(f2)
    l1 = []
    l2 = list(range(num, len(ids0)+num))
    # l2 = [i+num for i in l2]
    l1.append(l2.copy())
    num += len(l2)
    l2 = list(range(num, len(ids1)+num))
    l1.append(l2.copy())
    # l2 = [i+num for i in l2]
    l0.append(str(l1))
    write_str('\n'.join(l0), f2)
    return f2


def write_times0(f3, f4):
    '''获取时刻点并写入: 614+994'''
    ts = []
    l0 = get_lines1('./datasets/history/2023/614labels1.txt')[1:]
    for i in range(len(l0)):
        l1 = get_split(l0[i])
        ts.append([l1[j] for j in [0, 2, 3, 4, 5, 6, 7]])
    ts += write_xlsx0('./datasets/cache/955例一致数据时刻点.xlsx', [1, 8])
    ts += write_xlsx0('./datasets/cache/551例不一致数据时刻点.xlsx', [1, 8, 9, 10, 11, 12, 13], tx='551例不一致数据时刻点')
    l1 = get_lines1(f3)
    l0 = []
    for i in range(len(l1)):
        for j in range(len(ts)):
            if l1[i] == ts[j][0]:
                l0.append(', '.join(ts[j][1:]))
                break
    write_str('\n'.join(l0), f4)
    return f4


def data_label0(f0, d0):
    '''标签整理'''
    f1 = '%s/labels_raw.txt' % d0
    # f00 = write_xlsx0(f0, [1, 6], f1)  # 原始标签
    f2 = '%s/idx0.txt' % d0
    # f00 = get_split1(f1, f2)
    # l1 = get_item(get_lines1(f1), 0, [])
    f3 = '%s/ids.txt' % d0
    # write_str('\n'.join([str(l1[i]) for i in range(len(l1))]), f3)
    # 整理1506一致性数据994例-0000x000x和11010111x1x1111-纯阴纯阳
    pts = txt_to_list('./results/consistent/messages/idx0.txt')[0]
    f4 = './results/consistent/messages/ids.txt'
    # f00 = add_f3(pts, f4, f3, f2)
    # 写入时刻点
    f4 = '%s/times.txt' % d0
    # f00 = write_times0(f3, f4)
    return d0


def revi_models1(d0, d1, d3, lbs, nm, iks, tx2):
    '''d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = str(nm)
    for i in range(len(nm)):
        comb = list(range(sum(nm[:i+1])))
        strs = revi_mode0(ls, comb, pts, iks, tx2, strs)
    write_str(strs, '%s/%s/sort_results.txt' % (d3, lbs))
    return d3


def revi_models8(d0, d1, d2, d3, lbs, iks):
    '''测试: mess_path, mode_path, metr_path, resu_path, lbs版本'''
    combs = txt_to_list('%s/%s/comb.txt' % (d1, lbs))[0]
    comb, p0 = combs[1], combs[2]
    # print(combs)
    pts = txt_to_list('%s/idx.txt' % d0)
    id0 = get_lines1('%s/ids.txt' % d0)
    ids0, ids1 = [], []
    j = 0
    for i in range(len(iks[j])):
        ids0 += pts[iks[j][i]][0]
        ids1 += pts[iks[j][i]][1]
    ids = ids0+ids1
    id0 = [id0[i] for i in ids]  # id
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d1, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d1, lbs))), 0, [])
    l1 = [max(rm_element(rkl0[i][1], 100)) for i in comb]
    l2 = [min(rkl0[i][1]) for i in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 综合诊断参数——
    mts0 = [txt_to_list('%s/%s.txt' % (d2, mtnms[i])) for i in comb]  # 指标
    l0, l1 = [], []  # 诊断和综合诊断结果
    for i in ids:  # 诊断
        l0.append([diag_res0(mts0[j][i], rkl0[comb[j]]) for j in range(len(mts0))])
    l1 = comb_res3(l0, n01, n00)  # 综合诊断
    l2 = [id0[i]+', '+str(l0[i])+', '+str(l1[i]) for i in range(len(id0))]
    write_str('\n'.join(l2), '%s/%s_results0.txt' % (d3, lbs))
    return d3


def revi_models9(d0, d1, d2, d3, lbs, iks):
    '''审查全部指标组合结果tr-va: d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    f1, tx = 'tr-va_V3_trva_trva', 'tr-va'
    # f1, tx = 'trva_V3_trva_trva', 'trva'
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    combs = txt_to_list('%s/%s/%s.txt' % (d2, lbs, f1))
    # pts = txt_to_list('%s/idx.txt' % d0)  # 索引——
    pts = txt_to_list('%s/idx1.txt' % d0)  # 索引——
    rkl0 = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    mtnms = get_lines1(('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s_%s/%s.txt' % (d1, lbs, tx, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = ''
    for i in range(len(combs)):
        strs = revi_mode6(ls, combs[i][1], pts, iks, strs)
    write_str(strs, '%s/%s.txt' % (d4, f1))
    return d3


def revi_models6(d0, d1, d2, d3, lbs, iks, nm, tx2=''):
    '''d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    # combs = txt_to_list('%s/%s_va/combs_all.txt' % (d2, lbs))
    # l0 = get_lines1('%s/%s/combs_all.txt' % (d2, lbs))
    # l0.reverse()
    # write_str('\n'.join(l0), '%s/%s/combs_all0.txt' % (d2, lbs))
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    # combs = txt_to_list('%s/%s/V3_trva_trva.txt' % (d2, lbs))
    pts = txt_to_list('%s/idx1.txt' % d0)
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = str(nm)
    # comb = [1, 2, 5, 6, 10, 13, 14, 17, 18, 28, 38, 39, 40, 41, 52, 54, 64, 66, 70, 73, 77, 78]
    # ids = get_lines1('%s/ids.txt' % d0)
    # strs = revi_mode3(ls, comb, pts, iks, strs, ids)  # 315例测试集结果
    # strs = revi_mode3(ls, combs[-1][1], pts, iks, strs)
    # print([mtnms[i] for i in combs[-1][1]])
    for i in range(len(combs)):
        # strs = revi_mode5(ls, combs[i][1], pts, iks, tx2, strs)
        # strs = revi_mode5(ls, combs[i][1], pts, iks, strs)
        # strs = revi_mode3(ls, combs[i][1], pts, iks, strs)
        strs = revi_mode6(ls, combs[i][1], pts, iks, strs)
    new_folder('%s/%s' % (d3, lbs))
    # write_str(strs, '%s/%s/search_tr_tr1.txt' % (d3, lbs))
    write_str(strs, '%s/%s/search_list.txt' % (d3, lbs))
    # write_str(strs, '%s/%s/V3_trva_trva.txt' % (d3, lbs))
    return d3


def revi_models2(d0, d1, d2, d3, lbs, iks, nm, tx2):
    '''d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    combs = txt_to_list('%s/%s_va/combs_all.txt' % (d2, lbs))
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = str(nm)
    for i in range(len(combs)):
        strs = revi_mode0(ls, combs[i][1], pts, iks, tx2, strs)
    # print(strs)
    write_str(strs, '%s/%s/search_results_va.txt' % (d3, lbs))
    return d3


def get_mts_rks0(d0, d2, d3, lbs):
    '''d0信息, d2搜索, d3审查, lbs版本'''
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    comb = combs[-1][1]  # 最终模型
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    strs = 'metrics, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in comb])
    write_str(strs, '%s/%s/model_rks00.txt' % (d3, lbs))
    return d2


def get_comb_mess0(d0, d1, d2, d3, d4, lbs, iks):
    '''导出信息: d0信息, d1诊断, d2搜索, d3审查, d4指标, lbs版本'''
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    comb = combs[-2][1]  # 最终模型
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    idnms = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d4, mtnms[i])))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l1, l2 = [l1[j] for j in comb], [l2[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l1 = [[l0[i][j] for j in comb] for i in range(len(l0))]  # 诊断
    l0 = comb_res1(l1.copy(), n01, n00)  # 综合诊断
    l3 = [[l3[j][k] for j in comb] for k in range(len(l3[0]))]  # 指标
    ids0, ids1 = [], []
    for i in range(len(iks)):
        ids0 += pts[iks[i]][0]
        ids1 += pts[iks[i]][1]
    ids0 = [i for i in ids0 if l0[i] >= 24]  # 假阳
    ids1 = [i for i in ids1 if l0[i] < 24]  # 假阴
    strs = 'id, %s' % ', '.join([mtnms[i] for i in comb])
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[i][j]) for j in range(len(l3[0]))])) for i in ids0+ids1])
    write_str(strs, '%s/%s/model_mts.txt' % (d3, lbs))
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in comb])
    write_str(strs, '%s/%s/model_rks.txt' % (d3, lbs))
    strs = 'id, [%s]; FinalPer, Predict' % ', '.join([mtnms[i] for i in comb])
    strs += '\n'+'\n'.join(['%s, %s; %d, 1' % (idnms[i], str(l1[i]), l0[i]) for i in ids0])
    strs += '\n'+'\n'.join(['%s, %s; %d, 0' % (idnms[i], str(l1[i]), l0[i]) for i in ids1])
    write_str(strs, '%s/%s/model_dgs.txt' % (d3, lbs))
    return d2


def get_comb_mess3(d0, d1, d2, d3, d4, lbs, iks, ids0):
    '''导出信息: d0信息, d1诊断, d2搜索, d3审查, d4指标, lbs版本'''
    rkl0 = txt_to_list('%s/%s/utility_rks.txt' % (d0, lbs))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    # d00 = diag_resu0(d4, mtnms, rkl0, d1, lbs)  # 诊断
    combs = txt_to_list('%s/%s/combs_all_raw.txt' % (d2, lbs))
    comb = combs[-2][1]  # 最终模型
    idnms = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
    l0, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d4, mtnms[i])))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l1, l2 = [l1[j] for j in comb], [l2[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l1 = [[l0[i][j] for j in comb] for i in range(len(l0))]  # 诊断
    l0 = comb_res1(l1.copy(), n01, n00)  # 综合诊断
    l00 = [0 for i in range(len(idnms))]
    for i in ids0:
        if l0[i] >= 24:
            l00[i] = 1
        else:
            l00[i] = 0
    strs = 'id, Predict_raw'
    strs += '\n'+'\n'.join(['%s, %d' % (idnms[i], l00[i]) for i in ids0])
    write_str(strs, '%s/%s/model_dgs_raw.txt' % (d3, lbs))


def replace_mts1(d1, d2, ids0, mtnms):
    '''指标替换: d1源指标, d2现指标, ids0源指标索引'''
    cnt = 0
    for item in mtnms:
        f1 = '%s/%s.txt' % (d1, item)
        f2 = '%s/%s.txt' % (d2, item)
        l0 = get_lines1(f1)
        l1 = get_lines1(f2)
        l00 = [l0[i] for i in range(len(l0)) if i not in ids0]
        l11 = [l1[i] for i in range(len(l1)) if i not in ids0]
        # if item == 'time_ttflt':
        #     for i in range(len(l00)):
        #         print(l00[i], l11[i])
        if l00 == l11:
            continue
        print(item)
        cnt += 1
    print(cnt)


def replace_mts0(d1, d2, ids0, mtnms):
    '''指标替换: d1源指标, d2现指标, ids0源指标索引'''
    for item in mtnms:
        print(item)
        f1 = '%s/%s.txt' % (d1, item)
        f2 = '%s/%s.txt' % (d2, item)
        l0 = get_lines1(f1)
        l1 = get_lines1(f2)
        for i in range(len(ids0)):
            j = ids0[i]
            l0[j] = l1[i]
        write_str('\n'.join(l0), f1)


def get_comb_mess4(d0, d1, d2, d3, d4, lbs, iks):
    '''导出信息: d0信息, d1诊断, d2搜索, d3审查, d4指标, lbs版本'''
    rkl0 = txt_to_list('%s/%s/utility_rks.txt' % (d0, lbs))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    d00 = diag_resu0(d4, mtnms, rkl0, d1, lbs)  # 诊断
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    comb, p0 = combs[-1][1], combs[-1][2]  # 最终模型
    idnms = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
    ids0 = list(range(len(idnms)))
    l0, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d4, mtnms[i])))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l1, l2 = [l1[j] for j in comb], [l2[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l1 = [[l0[i][j] for j in comb] for i in range(len(l0))]  # 诊断
    l0 = comb_res3(l1.copy(), n01, n00)  # 综合诊断
    l00 = []
    for i in ids0:
        if l0[i] >= p0:
            l00.append(1)
        else:
            l00.append(0)
    new_folder('%s/%s' % (d3, lbs))
    l3 = [[l3[j][k] for j in comb] for k in range(len(l3[0]))]  # 指标
    strs = 'id, %s' % ', '.join([mtnms[i] for i in comb])
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[i][j]) for j in range(len(l3[0]))])) for i in ids0])
    write_str(strs, '%s/%s/model0_mts.txt' % (d3, lbs))
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in comb])
    write_str(strs, '%s/%s/model0_rks.txt' % (d3, lbs))
    strs = 'id, [%s]; FinalPer, Predict' % ', '.join([mtnms[i] for i in comb])
    strs += '\n'+'\n'.join(['%s, %s; %d, %d' % (idnms[i], str(l1[i]), l0[i], l00[i]) for i in ids0])
    write_str(strs, '%s/%s/model0_dgs.txt' % (d3, lbs))
    l01 = change_pers0(l0.copy(), p0)
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l01[i], l00[i]) for i in ids0])
    write_str(strs, '%s/%s/results.txt' % (d3, lbs))
    return d2


def get_comb_mess1(d0, d1, d2, d3, d4, lbs, iks):
    '''导出信息: d0信息, d1诊断, d2搜索, d3审查, d4指标, lbs版本'''
    rkl0 = txt_to_list('%s/%s/utility_rks.txt' % (d0, lbs))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    d00 = diag_resu0(d4, mtnms, rkl0, d1, lbs)  # 诊断
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    comb, p0 = combs[-1][1], combs[-1][2]  # 最终模型
    idnms = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
    ids0 = list(range(len(idnms)))
    l0, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d4, mtnms[i])))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l1, l2 = [l1[j] for j in comb], [l2[j] for j in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l1 = [[l0[i][j] for j in comb] for i in range(len(l0))]  # 诊断
    l0 = comb_res3(l1.copy(), n01, n00)  # 综合诊断
    l00 = []
    for i in ids0:
        if l0[i] >= p0:
            l00.append(1)
        else:
            l00.append(0)
    new_folder('%s/%s' % (d3, lbs))
    l3 = [[l3[j][k] for j in comb] for k in range(len(l3[0]))]  # 指标
    strs = 'id, %s' % ', '.join([mtnms[i] for i in comb])
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[i][j]) for j in range(len(l3[0]))])) for i in ids0])
    write_str(strs, '%s/%s/model0_mts.txt' % (d3, lbs))
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in comb])
    write_str(strs, '%s/%s/model0_rks.txt' % (d3, lbs))
    strs = 'id, [%s]; FinalPer, Predict' % ', '.join([mtnms[i] for i in comb])
    strs += '\n'+'\n'.join(['%s, %s; %d, %d' % (idnms[i], str(l1[i]), l0[i], l00[i]) for i in ids0])
    write_str(strs, '%s/%s/model0_dgs.txt' % (d3, lbs))
    return d2


def get_comb_mess2(d0, d1, d2, d3, d4, lbs, iks):
    '''导出信息: d0信息, d1诊断, d2搜索, d3审查, d4指标, lbs版本'''
    rkl0 = txt_to_list('%s/%s/utility_rks.txt' % (d0, lbs))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    # d00 = diag_resu0(d4, mtnms, rkl0, d1, lbs)  # 诊断
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    tx = ['895', '901']
    # tx = ['885', '901']
    # pers = [28, 15]
    for i0 in range(2):
        comb, p0 = combs[i0][1], combs[i0][2]  # 最终模型
        idnms = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
        ids0 = list(range(len(idnms)))
        l0, l1, l2, l3 = [], [], [], []
        for i in range(len(mtnms)):
            l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
            l1.append(max(rkl0[i][1]))
            l2.append(min(rkl0[i][1]))
            l3.append(txt_to_list('%s/%s.txt' % (d4, mtnms[i])))
        l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
        l1, l2 = [l1[j] for j in comb], [l2[j] for j in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
        l1 = [[l0[i][j] for j in comb] for i in range(len(l0))]  # 诊断
        l0 = comb_res3(l1.copy(), n01, n00)  # 综合诊断
        l00 = []
        for i in ids0:
            if l0[i] >= p0:
                l00.append(1)
            else:
                l00.append(0)
        new_folder('%s/%s' % (d3, lbs))
        l3 = [[l3[j][k] for j in comb] for k in range(len(l3[0]))]  # 指标
        strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in comb])
        write_str(strs, '%s/%s/model0.%s_rks.txt' % (d3, lbs, tx[i0]))
        strs = 'id, Predict'
        strs += '\n'+'\n'.join(['%s, %d' % (idnms[i], l0[i]) for i in ids0])
        write_str(strs, '%s/%s/model0.%s_results_raw.txt' % (d3, lbs, tx[i0]))
        l0 = change_pers0(l0, p0)
        strs = 'id, Predict'
        strs += '\n'+'\n'.join(['%s, %d' % (idnms[i], l0[i]) for i in ids0])
        write_str(strs, '%s/%s/model0.%s_results.txt' % (d3, lbs, tx[i0]))
    return d2


def revi_models3(d0, d1, d2, d3, lbs, iks, tx2):
    '''d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = ''
    for i in range(len(combs)):
        strs = revi_mode0(ls, combs[i][1], pts, iks, tx2, strs)
    # print(strs)
    write_str(strs, '%s/%s/search_results.txt' % (d3, lbs))
    return d3


def revi_models5(d0, d1, d2, d3, lbs, nm, iks, tx2):
    '''d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = 'all metrics'
    for i in range(sum(nm)):
        strs = revi_mode1(ls, [i], pts, iks, tx2, strs, mtnms[i])
    # print(strs)
    write_str(strs, '%s/%s/search_results_mts.txt' % (d3, lbs))
    return d3


def revi_models0(d0, d1, d2, d3, lbs, iks, tx2):
    '''d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = ''
    for i in range(len(combs)):
        strs = revi_mode0(ls, combs[i][1], pts, iks, tx2, strs)
    write_str(strs, '%s/%s/search_results.txt' % (d3, lbs))
    return d3


def revi_models4(d0, d1, d2, d3, lbs, iks, tx2):
    '''d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    strs = ''
    for i in range(len(combs)):
        strs = revi_mode0(ls, combs[i][1], pts, iks, tx2, strs)
    write_str(strs, '%s/%s/search_results.txt' % (d3, lbs))
    return d3


def get_metrics2(f0, d0, d1):
    '''提取标签、指标: f0, metr_path, mess_path'''
    # 提取id/指标: ids.txt/metrics_cls.txt——
    data = pd.read_excel(f0, sheet_name='120例一致性数据').values
    # data = pd.read_excel(f0, sheet_name='Sheet1').values
    mtnms0 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    for i in range(2, len(mtnms0)+2):
        l1 = get_xlsx_value(data, i)
        f1 = '%s1/%s.txt' % (d0, mtnms0[i-2])  # 新建metrics1文件夹——
        write_str('\n'.join(l1), f1)
        l2 = get_item(get_lines(f1), 0, [])
        l2 = [str(l2[j]) for j in range(len(l2))]
        f2 = '%s/%s.txt' % (d0, mtnms0[i-2])
        write_str('\n'.join(l2), f2)
    # l1 = get_xlsx_value(data, len(mtnms0)+2)
    # l2 = [i for i in range(len(l1)) if l1[i] == '0']
    # l3 = [l2]+[[i for i in range(len(l1)) if l1[i] == '1']]
    # write_str(str(l3)+'\n'+str(l3), '%s/idx.txt' % d1)  # 复制了2次*————
    return d1


def get_metrics1(f0, d0, d1, d2):
    '''
    全部指标生成
        f0表格, d0数据, d1指标, d2信息
    '''
    # # 1.标签整理
    # data = pd.read_excel(f0, sheet_name='Sheet1').values
    # data = pd.read_excel(f0, sheet_name='中山').values
    # data = pd.read_excel(f0, sheet_name='十院').values
    # data = pd.read_excel(f0, sheet_name='六院').values
    # data = pd.read_excel(f0, sheet_name='跑模型数据').values
    # data = pd.read_excel(f0, sheet_name='2023年').values
    data = pd.read_excel(f0, sheet_name='匹配').values
    vs = []
    # for i in [1]+list(range(3, 9)):
    # for i in [1]+list(range(4, 10)):
    # for i in [1]+list(range(5, 11)):
    # for i in [1]+list(range(3, 9)):
    # for i in [1]+list(range(2, 8)):
    # for i in [1]:
    # for i in range(1, 8):
    for i in [1, 4, 5, 6, 7, 8, 9]:
        vs.append(get_xlsx_value(data, i))
    ids = vs[0]
    # print(ids)
    # print('\n'.join(ids))
    # ids = get_item(get_lines(f0), 0, [])
    # write_str('\n'.join(ids), '%s/ids.txt' % d2)
    tmlis = [[int(vs[i][j]) for i in range(1, 7)] for j in range(len(vs[0]))]
    # print(tmlis)
    # write_str('\n'.join([str(tmlis[i]) for i in range(len(tmlis))]), '%s/times.txt' % d2)
    # 2.指标生成-message复制
    # ids = get_item(get_lines('%s/ids.txt' % d2), 0, [])
    d2 = get_metr0(d0, ids, d1, d2, tmlis)
    # d2 = get_metr0(d0, ids, d1, d2)
    return d2


def get_line_finetuning0():
    '''线性调整策略'''
    # 1.提取id
    # datadir = './datasets/所有心磁数据2024-3-14'
    # l1 = os.listdir(datadir)
    # random.shuffle(l1)
    # l1 = l1[:int(len(l1)/4)]
    # l1 = [l1[i][:-4] for i in range(len(l1))]
    # l1 = sorted(l1)
    # write_str('\n'.join(l1), './models/messages/ids.txt')
    # # 2.分组生成指标, 聚合指标
    data_path = './datasets/所有心磁数据2024-3-14'
    # metr_path = './models/metrics_raw'
    # mess_path = './models/messages'
    # # ik = int(sys.argv[1])
    # # d1 = get_metrics0(data_path, metr_path, mess_path, ik)
    # metr_path2 = './models/metrics_rwal'
    # for item in os.listdir('%s/0' % (metr_path)):
    #     l1 = []
    #     for i in range(22):
    #         l1 += get_lines1('%s/%d/%s' % (metr_path, i, item))
    #     write_str('\n'.join(l1), '%s/%s' % (metr_path2, item))
    # 3.线性调整策略
    mess_path = './models/messages'
    metr_path = './models/metrics_rwal'
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls0.txt' % mess_path)[0])
    l3 = []
    for i in range(len(mtnms)):
        l1 = txt_to_list('%s/%s.txt' % (metr_path, mtnms[i]))
        # print(i+1, len(mtnms)-1-i, mtnms[i], min(l1), max(l1))
        if min(l1) >= 0:
            l3.append([0, 10])
        elif max(l1) <= 0:
            l3.append([-10, 0])
        else:
            l3.append([-10, 10])
    l3 = [str(l3[i]) for i in range(len(l3))]
    l1 = txt_to_list('%s/metrics_rg.txt' % mess_path)
    print(len(mtnms))
    l2 = [mtnms[i] for i in range(len(mtnms)) if l1[i] == [0]]
    print(l2)
    # # *.插值错误导出id
    # l1 = os.listdir(data_path)
    # l3 = []
    # for i in range(len(l1)):
    #     print(i+1, len(l1)-1-i)
    #     f0 = '%s/%s' % (data_path, l1[i])
    #     l2 = get_lines1(f0)
    #     m2 = cal_interpolate0(f0)
    #     if len(m2) != len(l2):
    #         l3.append(l1[i][:-4])
    #         print('error:', l1[i])
    # write_str('\n'.join(l3), './models/messages/int_error.txt')
    # # 导出全部ids/删除全部pkl/重新插值pkl
    # d0 = data_path
    # d1 = './models/messages'
    # d2 = './datasets/mcg_pkls'
    # # l0 = os.listdir(d0)
    # # l0 = [l0[i][:-4] for i in range(len(l0))]
    # # write_str('\n'.join(l0), '%s/ids_exists.txt' % d1)
    # # l2 = os.listdir(d2)
    # # l2 = [l2[i][:-4] for i in range(len(l2))]
    # # l1 = [i for i in l0 if i not in l2]
    # # write_str('\n'.join(l1), '%s/ids_left.txt' % d1)
    # # l3 = [i for i in l2 if i not in l0]
    # # for i in l3:
    # #     os.remove('%s/%s.pkl' % (d2, i))
    # l1 = get_lines1('%s/ids_exists.txt' % d1)
    # l0 = []
    # for i in range(len(l1)):
    #     # if i > 1:
    #     #     continue
    #     # print(i+1, len(l1)-1-i)
    #     item = l1[i]
    #     l2 = get_lines1('%s/%s.txt' % (d0, item))
    #     with open('%s/%s.pkl' % (d2, item), 'rb') as f:
    #         # loaded_data0 = f.read()
    #         loaded_data = pickle.load(f)
    #         # loaded_data = make_response(loaded_data0)
    #         if len(l2) != len(loaded_data):
    #             l0.append(item)
    #             print(i+1, item, len(l2), len(loaded_data))
    #         else:
    #             print(i+1, len(l1)-1-i)
    # write_str('\n'.join(l0), '%s/ids_left.txt' % d1)
    # # debug插值——
    # ids = get_lines1('./models/messages/int_error.txt')
    # d0 = './datasets/mcg_pkls'
    # item = '528-enka shu'
    # # for item in ids:
    # f0 = '%s/%s.txt' % (data_path, item)
    # l2 = get_lines1(f0)
    # f1 = '%s/%s.pkl' % (d0, item)
    # if os.path.exists(f1):
    #     os.remove(f1)
    # m2 = cal_interpolate0(f0)
    # print(item, len(m2), len(l2))
    # if len(m2) != len(l2):
    #     print(item, len(m2), len(l2))
    #     # print(m2[-1])
    #         # shutil.rmtree(f1)
    #     # else:
    #     #     # m3 = cal_interpolate(f0)
    #     #     print('Not exist!', type(m2))
    #     #     # print(m2[5], m2[300])
    #     #     # print(type(m2[5]), type(m2[300]))
    #     #     # l1 = []
    #     #     # for i in range(len(m2)):
    #     #     #     l1.append(str(type(m2[i])))
    #     #     # print('\n'.join(l1))
    #     #     # print('Not exist!', len(m2[200]))
    #     #     # if m3[300] != m2[300]:
    #     #     #     print('data error')
    # else:
    #     print(item)


# def get_metrics0(f0, d0, d1, d2, ik):
# def get_metrics0(f0, d0, d1, d2):
# def get_metrics0(d0, d1, d2):
def get_metrics0(d0, d1, d2, ik):
    '''
    全部指标生成
        f0表格, d0数据, d1指标, d2信息
    '''
    # # 1.标签整理——
    # l1 = get_lines1('%s/label_raw.txt' % d2)
    # l21 = get_item(l1, 1, [])
    # l22 = get_item(l1, 2, [])
    # l23 = get_item(l1, 3, [])
    # l2 = ['%d%d%d' % (l21[i], l22[i], l23[i]) for i in range(len(l21))]
    # # 1.1 全部标签类型-提取索引
    # l11 = ['000', '001', '002', '010', '011', '012', '020', '021', '100', '101', '102', '110', '111', '112', '120', '121']
    # l3 = [str([i for i in range(len(l2)) if l2[i] == l11[j]]) for j in range(len(l11))]
    # write_str('\n'.join(l3), '%s/idx0.txt' % d2)
    # # 1.2 偏阳性/阴性-数据分割
    # # l00 = [['000', '020', '002'], ['111', '121', '112']]
    # l01 = [['100', '120', '102'], ['110', '101', '012', '001', '021', '011']]
    # n1 = [[20, 19, 75], [8, 8, 44, 3, 4, 8]]
    # n2 = [[78, 75, 303], [35, 32, 176, 12, 15, 30]]  # 外部验证分组抽取
    # n3 = [[364, 240], [91, 60]]  # 训练/验证集数量
    # l4 = [[[], []], [[], []], [[], []]]  # 训练/验证/测试
    # for i in [0, 1]:  # 只能抽取一次!
    #     l31 = []
    #     for j in range(len(l01[i])):
    #         l30 = [i1 for i1 in range(len(l2)) if l2[i1] == l01[i][j]]
    #         random.shuffle(l30)
    #         l4[2][i] += l30[:n1[i][j]]  # 测试集数量
    #         l31 += l30[n1[i][j]:n1[i][j]+n2[i][j]]
    #     l4[1][i] += l31[:n3[1][i]]
    #     l4[0][i] += l31[n3[1][i]:n3[1][i]+n3[0][i]]
    # l4 = [str(l4[i]) for i in range(3)]
    # l1 = get_lines1('%s/idx.txt' % d2) + l4
    # write_str('\n'.join(l1), '%s/idx.txt' % d2)
    # 2.指标生成
    ids = get_item(get_lines('%s/ids.txt' % d2), 0, [])
    ts = txt_to_list('%s/times.txt' % (d2))
    # d00 = get_metr2(d0, ids, d1, d2, ts)
    d00 = get_metr0(d0, ids, d1, d2, ts)
    # # *指标生成
    # ids = get_lines1('%s/ids.txt' % d2)
    # ids = ids[100*ik:min(len(ids), 100*(ik+1))]
    # d1 = '%s/%d' % (d1, ik)
    # new_folder(d1)
    # d00 = get_metr0(d0, ids, d1, d2)
    return d2


def cal_hcmmts0(d0, d1, d2, d3):
    '''
    计算心肌肥厚相关参数0: anal_path, mess_path, data_path, metr_path
        时相QR, QRS, ST, ST1, TT, QT, noq
        振幅qrs, qrs1, qrs2, r, qrs3
        面积qrs, tt, qrstt, qrs1, qrs2, qrs3
        积分qrs, tt1, tt2, qrstt11, qrstt12, qrstt21, qrstt22
        复极段相对平滑指数stph
        平均角度qrs, tt, qrstt, 电轴dz
    '''
    ids = get_lines1('%s/ids.txt' % d1)
    tms = txt_to_list('%s/times.txt' % d1)
    i=2
    ls1 = []
    mtnms = mts_default1()
    # print(mtnms)
    for i in range(len(ids)):
        print(i+1, len(ids)-1-i)
        f0 = '%s/%s.txt' % (d2, ids[i])
        lis = gettime(f0, noqs=1)
        l1 = cal_tmphase0(lis, tms[i])
        l11 = get_mcg36(f0)
        l2 = cal_zhfu0(l11, tms[i])
        l3 = cal_qxarea0(l11, tms[i])
        l4 = cal_integral0(l11, tms[i])
        stph = cal_stph0(l11, l1)
        Ms = cal_interpolate0(f0)
        l5 = cal_ctmts0(Ms, tms[i])
        ls1.append(l1[:-1]+l2+l3+l4+[stph]+l5)
        print(ls1[0])
    ls1 = [[ls1[i][j] for i in range(len(ls1))] for j in range(len(ls1[0]))]
    for i in range(len(mtnms)):
        save_txt(ls1[i], 'list', '%s/%s.txt' % (d3, mtnms[i]))
    return d3


def cal_hcmmts1(d0, d1, d2, d3):
    '''
    计算心肌肥厚相关参数1: anal_path, mess_path, data_path, metr_path
        偶极矩dpmm
    '''
    ids = get_lines1('%s/ids.txt' % d1)
    tms = txt_to_list('%s/times.txt' % d1)
    ls1 = []
    mtnms = mts_default2()
    for i in range(len(ids)):
        print(i+1, len(ids)-1-i)
        f0 = '%s/%s.txt' % (d2, ids[i])
        lis = gettime(f0)
        l0 = get_mcg36(f0)
        ms = cal_interpolate0(f0)
        dpmm = cal_dpmm0(l0, lis[3]+50)
        l1 = cal_twivs0(l0, ms, tms[i])
        l2 = cal_stcg0(l0, lis)
        ls1.append([dpmm, l1, l2])
    ls1 = [[ls1[i][j] for i in range(len(ls1))] for j in range(len(ls1[0]))]
    for i in range(len(mtnms)):
        save_txt(ls1[i], 'list', '%s/%s.txt' % (d3, mtnms[i]))
    # 分割参数: T波倒置系列/ST段改变系列
    tx = mts_default2()[1:]
    for i1 in range(len(tx)):
        l1 = txt_to_list('%s/%s.txt' % (d3, tx[i1]))
        l1 = [[l1[i][j] for i in range(len(l1))] for j in range(len(l1[0]))]
        for j in range(len(l1)):
            save_txt(l1[j], 'list', '%s/%s_%d.txt' % (d3, tx[i1], j))
    # # # mcg展示图
    # d4 = '%s/mcg' % d0
    # new_folder(d4)
    # ids1 = [ids[i] for i in idx[0]]
    # tms1 = [tms[i] for i in idx[0]]
    # for i in range(len(ids1)):
    #     mcgfile = '%s/%s.txt' % (d2, ids1[i])
    #     matrixes = cal_interpolate0(mcgfile)
    #     M0 = matrixes[tms1[i][4]]
    #     Z = norms0(M0)  # 正负归一化
    #     f0 = '%s/0_%s.png' % (d4, ids1[i])
    #     drawmcg(Z, f0, ct=1)
    # ids1 = [ids[i] for i in idx[1]]
    # tms1 = [tms[i] for i in idx[1]]
    # for i in range(len(ids1)):
    #     mcgfile = '%s/%s.txt' % (d2, ids1[i])
    #     matrixes = cal_interpolate0(mcgfile)
    #     M0 = matrixes[tms1[i][4]]
    #     Z = norms0(M0)  # 正负归一化
    #     f0 = '%s/1_%s.png' % (d4, ids1[i])
    #     drawmcg(Z, f0, ct=1)
    # # timewaves展示图
    # d4 = '%s/timewaves' % d0
    # new_folder(d4)
    # ids1 = [ids[i] for i in idx[0]]
    # tms1 = [tms[i] for i in idx[0]]
    # for i in range(len(ids1)):
    #     mcgfile = '%s/%s.txt' % (d2, ids1[i])
    #     f0 = '%s/0_%s.png' % (d4, ids1[i])
    #     lis = gettime(mcgfile, f0=f0, tm=tms1[i])
    # ids1 = [ids[i] for i in idx[1]]
    # tms1 = [tms[i] for i in idx[1]]
    # for i in range(len(ids1)):
    #     mcgfile = '%s/%s.txt' % (d2, ids1[i])
    #     f0 = '%s/1_%s.png' % (d4, ids1[i])
    #     lis = gettime(mcgfile, f0=f0, tm=tms1[i])


def cal_hcmmts2(d0, d1, d2, d3):
    '''
    补充心肌肥厚相关参数2:
    偶极矩连续结果和最大值时刻, 平均角度重做, QRS平均心电轴
    '''
    ids = get_lines1('%s/ids.txt' % d1)
    tms = txt_to_list('%s/times.txt' % d1)
    ls1, ls2 = [], []
    mtnms = ['mfm_ag_qrsa1', 'mfm_ag_tta1', 'mfm_ag_qrstta1', 'mfm_ag_rdz1', 'mfm_ag_rdz2']
    for i in range(len(ids)):
        print(i+1, len(ids)-1-i)
        f0 = '%s/%s.txt' % (d2, ids[i])
        lis = gettime(f0)
        l0 = get_mcg36(f0)
        ojjs = [cal_dpmm0(l0, lis[3]+j) for j in range(71)]  # 偶极矩s
        ls2.append(ojjs)
        Ms = cal_interpolate0(f0)
        qrs = cal_meana1(Ms, lis[4], lis[6])  # 平均角度
        tt = cal_meana1(Ms, lis[9], lis[11])  # 平均角度
        qrstt = qrs-tt
        dz1 = (360-(qrs+90)) % 360  # 电轴
        dz20 = cal_meandz0(f0, Ms, lis[4], lis[6])
        dz2 = (360-dz20) % 360
        ls1.append([qrs, tt, qrstt, dz1, dz2])
    save_txt(ls2, 'list', '%s/ojss.txt' % d3)
    # ls1 = [[ls1[i][j] for i in range(len(ls1))] for j in range(len(ls1[0]))]
    # for i in range(len(mtnms)):
    #     save_txt(ls1[i], 'list', '%s/%s.txt' % (d3, mtnms[i]))
    # # 曲线和最值、极值
    # ls2 = txt_to_list('%s/ojss.txt' % d3)
    # d4 = '%s/ojjs' % d0
    # new_folder(d4)
    # xs = [i+1 for i in range(71)]
    # for i in range(len(ids)):
    #     g1 = '%s/%s.png' % (d4, ids[i])
    #     ys = ls2[i]
    #     j = ys.index(max(ys))
    #     if j == len(ys)-1:
    #         print(j, j)
    #     else:
    #         i1, i2 = int(j/10)*10, int(j/10)*10+10
    #         if ys[i2]>ys[i1]:
    #             print(j, i2)
    #         else:
    #             print(j, i1)
        # iks = [i for i in range(1, len(xs)-1) if xs[i]>xs[i-1] and xs[i]>xs[i+1]]
        # xss1 = [xs[i] for i in iks]
        # yss1 = [ys[i] for i in iks]
        # xss = [xs[i] for i in range(len(xs)) if i not in iks]
        # yss = [ys[i] for i in range(len(ys)) if i not in iks]
        # plt.rcParams['savefig.dpi'] = 1024
        # plt.plot(xs, ys, c='k', linewidth=0.5)
        # plt.scatter(xss, yss, c='k', s=2)
        # plt.scatter(xss1, yss1, c='r', s=2)
        # plt.scatter(xs[ys.index(max(ys))], max(ys), c='r', s=5)
        # plt.savefig(g1)
        # plt.close()
    return d3


def get_all_metrics0(f0, d0, d1, d2):
    '''
    全部指标生成
        f0样本, d0数据, d1保存, d2信息
    '''
    ids = get_item(get_lines(f0), 0, [])
    d2 = get_metr1(d0, ids, d1, d2)
    return d2


def analysis_metrics0(d0, d1, d2):
    '''分析指标的效用: 训练集和测试集的分布一致性: d0指标, d1分析, d2信息'''
    d1 = '%s/metrics_distrib_density_map' % d1
    pts = txt_to_list('%s/idx.txt' % d2)  # 标签索引
    # new_folder(d1)
    # for lbs in ['spect', 'qfr', 'spect_qfr', 'all']:  # 版本
    #     for fd in [10]:
    #         d2 = '%s/%s_%d' % (d1, lbs, fd)
    #         new_folder(d2)
    f3 = analysis_metr0(d0, d1, 'spect_10',  pts[0], pts[1], 10)
    f2 = '%s/spect_10_para.txt' % d1
    f3 = '%s/spect_10_para_sort.txt' % d1
    f4 = '%s/spect_10_mess.txt' % d1
    f5 = '%s/spect_10_mess_sort.txt' % d1
    idex = sort_text(f2, f3, 1, 0, f4, f5)
    return f4


def diag_results0(d0, d1, d2, lbs):
    '''诊断结果、效用因子计算和排序: d0信息, d1指标, d2诊断, lbs版本'''
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    f1 = '%s/%s/ranks0.txt' % (d0, lbs)
    f2 = '%s/%s/mts.txt' % (d0, lbs)
    f3 = '%s/%s/rks.txt' % (d0, lbs)
    # mtnms = split_mt_rk(f1, f2, f3)  # 分级拆分
    mtnms = get_item(get_lines1(f1), 0, [])
    rkmes = txt_to_list(f3)
    # d3 = diag_resu0(d1, mtnms, rkmes, d2, lbs)  # 诊断
    f4 = cal_utility0(d0, lbs, mtnms, d2, pts)  # 效用排序、索引
    return d2


def diag_results3(d0, d1, d2, lbs):
    '''单指标诊断: d0模型, d1指标, d2诊断, lbs版本'''
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_item(get_lines1('%s/%s/mts0.txt' % (d0, lbs)), 0, [])
    d4 = '%s/0' % d3
    new_folder(d4)
    rkmes = txt_to_list('%s/%s/rks0.txt' % (d0, lbs))
    d5 = diag_resu0(d1, mtnms, rkmes, d4)  # 诊断
    d4 = '%s/1' % d3
    new_folder(d4)
    rkmes = txt_to_list('%s/%s/rks1.txt' % (d0, lbs))
    d5 = diag_resu0(d1, mtnms, rkmes, d4)  # 诊断
    return d2


def get_combdiag_one0(f0, d1, d2, idxs):
    '''单人综合诊断: 心磁f0, 矩阵目录d1, 模型目录d2, 时刻点idxs'''
    mts = get_metr_one0(f0, d1, idxs)
    mtnms0 = mts_default0(1)
    # print(len(mts))
    # print(len(mtnms0))
    strs = [mtnms0[i]+', '+str(mts[i]) for i in range(len(mts)) if mtnms0[i] != '']
    print(len(strs))
    # print('\n'.join(strs))
    # mtnms = get_lines1('%s/utility_mts.txt' % d2)
    # rkmes = txt_to_list('%s/utility_rks.txt' % d2)
    # comb = txt_to_list('%s/comb.txt' % d2)[-1][1]
    # l1, l2 = [], []  # 统计和计算参数
    # # print('1', comb)
    # for i in comb:
    #     l1.append(max(rm_element(rkmes[i][1], 100)))
    #     l2.append(min(rkmes[i][1]))
    # n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    # # print(n01, n00)
    # l00 = []
    # for k in range(len(comb)):
    #     i = comb[k]
    #     m0 = mtnms[i]
    #     j = mtnms0.index(m0)
    #     # print(m0, j)
    #     mt0 = mts[j]
    #     if type(mt0) == str:
    #         mt0 = int(mt0)
    #     else:
    #         mt0 = float('%.3f' % mt0)
    #     # if k > 6:
    #     #     print(mts[j], mt0)
    #     # print(i, mt0, rkmes[i])
    #     l00.append(diag_res0(mt0, rkmes[i]))
    # l1 = comb_res3([l00], n01, n00)
    # print(l00)
    # print(l1)
    # return l1[0]


def diag_results2(d1, d2, d3, lbs, iks):
    '''诊断结果、效用因子计算和排序: d1信息 d2指标, d3诊断, lbs版本'''
    # # **.测试自动分级
    # rkmes = txt_to_list('%s/%s/auto_rks.txt' % (d1, lbs))
    # mtnms0 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    # save_txt(mtnms0, 'list', '%s/%s/mts.txt' % (d1, lbs))
    
    # 1.2单指标诊断**
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)))
    rkmes = txt_to_list('%s/%s/rks.txt' % (d1, lbs))
    d00 = diag_resu0(d2, mtnms, rkmes, d3, lbs)  # 诊断
    # 2.效用计算和排序
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    pts = txt_to_list('%s/idx1.txt' % d1)  # 标签索引——
    ids0, ids1 = [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids0 += pts[iks[1][i]][0]
        ids1 += pts[iks[1][i]][1]
    lis1 = []
    for i in range(len(mtnms)):
        item = mtnms[i]
        l0 = txt_to_list('%s/%s/%s.txt' % (d3, lbs, item))
        l1 = [l0[j] for j in ids0]
        l2 = [l0[j] for j in ids1]
        # print(mtnms[i])
        auc, acc, f1sc = cal_utility1(l1, l2)
        lis1.append([auc, acc, f1sc])
    save_txt(lis1, 'list', '%s/%s/utility_all.txt' % (d1, lbs))
    data = np.array(lis1)
    # auc>acc>F1得分
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    # acc>F1得分>auc
    # idex = np.lexsort((-1 * data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    save_txt(lis1, 'list', '%s/%s/utility_sort.txt' % (d1, lbs))
    lns = get_lines1('%s/%s/rks.txt' % (d1, lbs))
    lns = [lns[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_rks.txt' % (d1, lbs))
    lns = [mtnms[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_mts.txt' % (d1, lbs))
    return d2


def diag_results5(d1, d2, d3, lbs, iks):
    '''tr诊断结果、效用因子计算和排序: d1信息 d2指标, d3诊断, lbs版本'''
    # 1.单指标诊断
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    rkmes = txt_to_list('%s/%s/rks.txt' % (d1, lbs))
    d00 = diag_resu0(d2, mtnms, rkmes, d3, '%s' % (lbs))  # 诊断
    # 2.效用计算和排序
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——
    ids0, ids1 = [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    lis1 = []
    for i in range(len(mtnms)):
        item = mtnms[i]
        l0 = txt_to_list('%s/%s/%s.txt' % (d3, lbs, item))
        l1 = [l0[j] for j in ids0]
        l2 = [l0[j] for j in ids1]
        # print(mtnms[i])
        auc, acc, f1sc = cal_utility1(l1, l2)
        lis1.append([auc, acc, f1sc])
    # save_txt(lis1, 'list', '%s/%s/utility_all.txt' % (d1, lbs))
    data = np.array(lis1)
    # auc>acc>F1得分
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    # acc>F1得分>auc
    # idex = np.lexsort((-1 * data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    save_txt(lis1, 'list', '%s/%s/utility_sort.txt' % (d1, lbs))
    lns = get_lines1('%s/%s/rks.txt' % (d1, lbs))
    lns = [lns[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_rks.txt' % (d1, lbs))
    lns = [mtnms[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_mts.txt' % (d1, lbs))
    return d2


def diag_results4(d1, d2, d3, lbs, iks):
    '''诊断结果、效用因子计算和排序: d1信息 d2指标, d3诊断, lbs版本'''
    # tx = 'tr-va'
    tx = 'trva'
    # 1.单指标诊断
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)))  # 复制！
    rkmes = txt_to_list('%s/%s/rks_%s.txt' % (d1, lbs, tx))
    d00 = diag_resu0(d2, mtnms, rkmes, d3, '%s_%s' % (lbs, tx))  # 诊断
    # 2.效用计算和排序
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——
    ids0, ids1 = [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids0 += pts[iks[1][i]][0]
        ids1 += pts[iks[1][i]][1]
    lis1 = []
    for i in range(len(mtnms)):
        item = mtnms[i]
        l0 = txt_to_list('%s/%s_%s/%s.txt' % (d3, lbs, tx, item))
        l1 = [l0[j] for j in ids0]
        l2 = [l0[j] for j in ids1]
        # print(mtnms[i])
        auc, acc, f1sc = cal_utility1(l1, l2)
        lis1.append([auc, acc, f1sc])
    # save_txt(lis1, 'list', '%s/%s/utility_all.txt' % (d1, lbs))
    data = np.array(lis1)
    # auc>acc>F1得分
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    # acc>F1得分>auc
    # idex = np.lexsort((-1 * data[:, 0], -1 * data[:, 2], -1 * data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    save_txt(lis1, 'list', '%s/%s/utility_sort_%s.txt' % (d1, lbs, tx))
    lns = get_lines1('%s/%s/rks_%s.txt' % (d1, lbs, tx))
    lns = [lns[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_rks_%s.txt' % (d1, lbs, tx))
    lns = [mtnms[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_mts_%s.txt' % (d1, lbs, tx))
    return d2


def add_ids0(d0, f0):
    '''添加不一致数据id: mess_path'''
    sorts0 = ['021', '001', '012', '011']
    sorts1 = ['120', '110', '102', '100']
    sorts2 = ['010', '101']
    ids0 = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
    data = pd.read_excel(f0, sheet_name='Sheet1').values
    vs = []
    for i in [1, 4, 2, 3, 5]:
        vs.append(get_xlsx_value(data, i))
    vs = [[vs[i][j] for i in range(len(vs))] for j in range(len(vs[0]))]
    print(len(vs), len(vs[0]))
    save_txt(vs, 'list', '%s/ids_raw.txt' % d0)
    strs = ''
    for sorts in [sorts0, sorts1, sorts2]:
        l0 = []
        for ik in sorts:
            a = [char for char in ik]
            l1 = []
            for i in range(len(vs)):
                if vs[i][1:4] == a:
                    item = vs[i][0]
                    for j in range(len(ids0)):
                        if ids0[j] == item:
                            l1.append(j)
                            break
            l0.append(l1)
        strs += '\n%s' % str(l0)
    write_str(strs, '%s/idx.txt' % d0, 'a')
    return d0


def add_ids1(d0, f0):
    '''添加不一致数据id: mess_path'''
    sorts = ['000', '020', '021', '002', '001', '010', '012', '011', '111', '121', '120', '112', '110', '101', '102', '100']
    ids0 = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
    data = pd.read_excel(f0, sheet_name='Sheet1').values
    vs = []
    for i in [1, 4, 2, 3, 5]:
        vs.append(get_xlsx_value(data, i))
    vs = [[vs[i][j] for i in range(len(vs))] for j in range(len(vs[0]))]
    print(len(vs), len(vs[0]))
    strs = ''
    l0 = []
    for ik in sorts:
        a = [char for char in ik]
        l1 = []
        for i in range(len(vs)):
            if vs[i][1:4] == a:
                item = vs[i][0]
                for j in range(len(ids0)):
                    if ids0[j] == item:
                        l1.append(j)
                        break
        l0.append(l1)
    strs += '%s' % str(l0)
    write_str(strs, '%s/idx0.txt' % d0)
    return d0


def get_weights(d2):
    l1 = get_lines1('%s/results_sort.txt' % d2)[1:]
    ls = []
    for j in range(1, len(l1)):
        l2 = get_split(l1[j])
        if l2[1]+l2[2]+l2[3] == '110':
            # ls.append(int(l2[4])+int(l2[5])+int(l2[6]))
            # ls.append(round((3*int(l2[4])+1*int(l2[5])+96*int(l2[6]))/100))
            ls.append(round((63*int(l2[4])+2*int(l2[5])+35*int(l2[6]))/100))
    ls.sort()
    print(ls)
    return d2


def collect0(d0, tx):
    '''3合1模型结果整合: resu_path'''
    f1 = '%s/%s/all_1506/results.txt' % (d0, 'model_955_20240415')
    f2 = '%s/%s/all_1506/results.txt' % (d0, 'model-1_20240429')
    f3 = '%s/%s/all_1506/results.txt' % (d0, 'model-0_20240429')
    new_folder('%s/%s' % (d0, tx))
    new_folder('%s/%s/all_1506' % (d0, tx))
    f0 = '%s/%s/all_1506/results.txt' % (d0, tx)
    strs = 'id, model_955, model-1, model-0, per_955, per-1, per-0\n'
    l1 = get_lines1(f1)[1:]
    l2 = get_lines1(f2)[1:]
    l3 = get_lines1(f3)[1:]
    l0 = []
    for i in range(len(l1)):
        l10 = get_split(l1[i])
        l20 = get_split(l2[i])
        l30 = get_split(l3[i])
        l00 = [l10[0], l10[3], l20[3], l30[3], l10[2], l20[2], l30[2]]
        l0.append(', '.join(l00))
    strs += '\n'.join(l0)
    write_str(strs, f0)
    return d0


def collect1(d0, d1, tx, tx1):
    '''分类查看16: mess_path, resu_path'''
    pts = txt_to_list('%s/idx0.txt' % d0)[0]
    lbs = '0000x00x100x00101001x0111111x11x011x11010110x100'  # 类型
    l1 = get_lines1('%s/%s/all_1506/results.txt' % (d1, tx))[1:]
    new_folder('%s/%s/%s' % (d1, tx, tx1))
    for i in range(16):
        strs = 'id, model_955, model-1, model-0, per_955, per-1, per-0\n'
        lbs1 = lbs[3*i:3*i+3]
        l0 = [l1[j] for j in pts[i]]
        strs += '\n'.join(l0)
        write_str(strs, '%s/%s/%s/%s.txt' % (d1, tx, tx1, lbs1))
    return d1


def collect2(d0, d1, tx, tx1):
    '''分类查看16: mess_path, resu_path'''
    pts = txt_to_list('%s/idx0.txt' % d0)[0]
    lbs = '0000x00x100x00101001x0111111x11x011x11010110x100'  # 类型
    l1 = get_lines1('%s/%s/all_1506/results.txt' % (d1, tx))[1:]
    new_folder('%s/%s/%s' % (d1, tx, tx1))
    tx20 = ['0000x000x', '1001x010x010', '01x0010x1011', '11010111x1x1111']
    tx2 = tx20.copy()
    for j in range(len(tx20)):
        l0 = []
        for i in range(int(len(tx20[j])/3)):
            l0.append(tx20[j][3*i:3*i+3])
        tx2[j] = l0.copy()
    l00 = [[], [], [], []]
    for i in range(16):
        lbs1 = lbs[3*i:3*i+3]
        for j in range(len(tx2)):
            if lbs1 in tx2[j]:
                l00[j] += pts[i]
                break
    ls = ['000', '100', '010', '101', '001', '110', '011', '111']
    strs0 = 'type, 000, 100, 010, 101, 001, 110, 011, 111'
    for i in range(len(tx20)):
        l0 = [l1[j] for j in l00[i]]
        l01 = [get_split(l0[j]) for j in range(len(l0))]
        l01 = [l01[j][1]+l01[j][2]+l01[j][3] for j in range(len(l01))]
        idx, tx3 = [], []
        for ik in ls:
            l02 = [j for j in range(len(l01)) if l01[j] == ik]
            idx += l02
            tx3.append(str(len(l02)))
        strs0 += '\n%s, %s' % (tx20[i], ', '.join(tx3))
        strs = 'id, model_955, model-1, model-0, per_955, per-1, per-0\n'
        strs += '\n'.join([l0[j] for j in idx])
        write_str(strs, '%s/%s/%s/%s.txt' % (d1, tx, tx1, tx20[i]))
    write_str(strs0, '%s/%s/%s/nums.txt' % (d1, tx, tx1))
    ls = ['000', '100', '010', '101', '001', '110', '011', '111']
    ln0 = get_lines1('%s/%s/%s/nums.txt' % (d1, tx, tx1))[1:]
    ls0 = []
    for i in range(len(ln0)):
        ln1 = get_split(ln0[i])
        ls1 = []
        for j in range(1, len(ln1)):
            ls1.append(int(ln1[j]))
        ls0.append(ls1)
    strs = 'vers, p, n, pn, p+, pn+p, n-, pn+p+n'
    for i in range(1, len(ls0[0])):
        p = sum(ls0[3][i:]) / sum(ls0[3])
        n = sum(ls0[0][:i]) / sum(ls0[0])
        pn = (sum(ls0[0][:i])+sum(ls0[3][i:])) / sum(ls0[0]+ls0[3])
        pj = sum(ls0[2][i:]) / sum(ls0[2])
        pnjp = (sum(ls0[0][:i])+sum(ls0[2][i:])+sum(ls0[3][i:])) / sum(ls0[0]+ls0[2]+ls0[3])
        nj = sum(ls0[1][:i]) / sum(ls0[1])
        pnjpjn = (sum(ls0[0][:i])+sum(ls0[1][:i])+sum(ls0[2][i:])+sum(ls0[3][i:])) / sum(ls0[0]+ls0[1]+ls0[2]+ls0[3])
        strs += '\n%s, %s' % (ls[i], str(get_round([p, n, pn, pj, pnjp, nj, pnjpjn], [3, 3, 3, 3, 3, 3, 3])))
    write_str(strs, '%s/%s/%s/vers.txt' % (d1, tx, tx1))
    strs = 'vers, p, n, pn, p+, pn+p, n+, pn+p+n'
    for i in range(1, len(ls0[0])):
        p = sum(ls0[3][i:]) / sum(ls0[3])
        n = sum(ls0[0][:i]) / sum(ls0[0])
        pn = (sum(ls0[0][:i])+sum(ls0[3][i:])) / sum(ls0[0]+ls0[3])
        pj = sum(ls0[2][i:]) / sum(ls0[2])
        pnjp = (sum(ls0[0][:i])+sum(ls0[2][i:])+sum(ls0[3][i:])) / sum(ls0[0]+ls0[2]+ls0[3])
        nj = sum(ls0[1][i:]) / sum(ls0[1])
        pnjpjn = (sum(ls0[0][:i])+sum(ls0[1][i:])+sum(ls0[2][i:])+sum(ls0[3][i:])) / sum(ls0[0]+ls0[1]+ls0[2]+ls0[3])
        strs += '\n%s, %s' % (ls[i], str(get_round([p, n, pn, pj, pnjp, nj, pnjpjn], [3, 3, 3, 3, 3, 3, 3])))
    write_str(strs, '%s/%s/%s/vers1.txt' % (d1, tx, tx1))
    return d1


def get_cls0(f0):
    ls = ['000', '100', '010', '101', '001', '110', '011', '111']
    l0 = get_lines1(f0)[1:]
    l1 = [[], [], [], [], [], [], [], []]
    l01 = [get_split(l0[j]) for j in range(len(l0))]
    l02 = [l01[j][1]+l01[j][2]+l01[j][3] for j in range(len(l01))]
    k = 0
    for i in range(len(l02)):
        for j in range(k, len(ls)):
            if l02[i] == ls[j]:
                l1[j].append([int(l01[i][4]), int(l01[i][5]), int(l01[i][6])])
                k = j
                break
    return l1


def get_wacc(wg, l0, l2, l3):
    ls0 = [round((l0[i][0]*wg[0]+l0[i][1]*wg[1]+l0[i][2]*wg[2])/100) for i in range(len(l0))]
    ls2 = [round((l2[i][0]*wg[0]+l2[i][1]*wg[1]+l2[i][2]*wg[2])/100) for i in range(len(l2))]
    ls3 = [round((l3[i][0]*wg[0]+l3[i][1]*wg[1]+l3[i][2]*wg[2])/100) for i in range(len(l3))]
    ls1 = ls2+ls3
    # print(len(ls0), len(ls1))
    l12 = list(set(ls0+ls1))
    l12.sort()
    l12.append(min(101, l12[-1]+1))
    # print(l12)
    p0, nm0 = 0, max(len(ls0), len(ls1))
    nms = [0, 0, 0]
    for i in range(len(l12)):
        p1 = l12[i]
        nm1 = sum(1 for pi in ls0 if pi >= p1)
        nm2 = sum(1 for pi in ls2 if pi < p1)
        nm3 = sum(1 for pi in ls3 if pi < p1)
        # print(p1, nm1)
        nm = nm1+nm2+nm3
        if nm < nm0:
            p0 = p1
            nm0 = nm
            acc0 = 1-nm0/len(ls0+ls1)
            acc1 = 1-nm1/len(ls0)
            acc2 = 1-nm2/len(ls2)
            acc3 = 1-nm3/len(ls3)
            nms = [nm1, nm2, nm3]
    acc0, acc1, acc2, acc3 = get_round([acc0, acc1, acc2, acc3], [3, 3, 3, 3])
    wg0 = wg + nms
    return wg0, [p0, nm0, acc0, acc1, acc2, acc3]


def displaytime(t1, cnt, dp, cn, rslis=[], f1=''):
    if cnt % dp == 0:
        if rslis:
            write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
        t2 = time.time()
        sec = int((t2-t1) * (cn-cnt) / dp)
        dspy0 = 'start: %d, left: %d, end_time: ' % (cnt, cn-cnt)
        hr, mn, sc = sec//3600, (sec%3600)//60, sec%60
        if hr:
            dspy0 += '%dh' % hr
        if mn:
            dspy0 += '%dmin' % mn
        if sc:
            dspy0 += '%ds' % sc
        print(dspy0)
        return t2
    else:
        return t1

def collect3(d0, d1, tx, tx1):
    '''分类查看16: mess_path, resu_path'''
    ls = ['000', '100', '010', '101', '001', '110', '011', '111']
    l0 = get_cls0('%s/%s/%s/0000x000x.txt' % (d1, tx, tx1))
    l1 = get_cls0('%s/%s/%s/1001x010x010.txt' % (d1, tx, tx1))
    l2 = get_cls0('%s/%s/%s/01x0010x1011.txt' % (d1, tx, tx1))
    l3 = get_cls0('%s/%s/%s/11010111x1x1111.txt' % (d1, tx, tx1))
    # # 权重排序
    # dp, cn0, cn = 1000, 0, 5151*8
    # t1 = time.time()
    # for li in range(len(ls)):
    #     mgs, wgs = [], []
    #     for i in range(101):
    #         for j in range(101-i):
    #             k = 100-i-j
    #             cn0 += 1
    #             t1 = displaytime(t1, cn0, dp, cn)
    #             wg0, rs0 = get_wacc([i, j, k], l0[li], l2[li], l3[li])
    #             wgs.append(wg0)
    #             mgs.append(rs0)
    #     lis1 = mgs.copy()
    #     data = np.array(lis1)
    #     idex = np.lexsort((data[:, 0], -1 * data[:, 4], -1 * data[:, 5], -1 * data[:, 3], data[:, 1], -1 * data[:, 2]))
    #     sorted_data = data[idex]
    #     lis1 = sorted_data.tolist()
    #     wgs = [wgs[i] for i in idex]
    #     strs = 'w1, w2, w3, nm1, nm2, nm3, p0, nm0, acc0, acc1, acc2, acc3\n'+'\n'.join([str(wgs[i]+lis1[i]) for i in range(len(lis1))])
    #     write_str(strs, '%s/%s/%s/sort_%s.txt' % (d1, tx, tx1, ls[li]))
    # # 提取nm1/2/3错误数量的组合
    # for s0 in ls:
    #     l1 = txt_to_list('%s/%s/%s/nms_%s.txt' % (d1, tx, tx1, s0))
    #     l2 = []
    #     for i in range(len(l1)):
    #         if l1[i][3:6] not in l2:
    #             l2.append(l1[i][3:6])
    #     write_str('\n'.join([str(l2[i]) for i in range(len(l2))]), '%s/%s/%s/nms_new_%s.txt' % (d1, tx, tx1, s0))
    # # 8种类型的组合搜索
    # s, c, ls1 = 1, [], []
    # for s0 in ls:
    #     l1 = txt_to_list('%s/%s/%s/nms_new_%s.txt' % (d1, tx, tx1, s0))
    #     ls1.append(l1)
    #     s *= len(l1)
    #     c.append(len(l1))
    # ls0 = []
    # print(c)
    # for i in range(len(c)):
    #     j = c[len(c)-1-i]
    #     if len(ls0) == 0:
    #         ls0 = [[k] for k in range(j)]
    #     else:
    #         ls0 = [[int(k/len(ls0))]+ls0[int(k%len(ls0))] for k in range(j*len(ls0))]
    # # ls0 = [[ls0[i][j] for i in range(len(ls0))] for j in range(len(ls0[0]))]
    # print(len(ls0), len(ls0[0]))
    # l0 = [269, 7, 55, 13, 22, 7, 23, 18]
    # l2 = [18, 2, 6, 5, 31, 2, 23, 35]
    # l3 = [31, 70, 23, 41, 3, 122, 11, 279]
    # s1, s2, s3 = sum(l0), sum(l2), sum(l3)
    # p0, p1, rslis = 0.85, 0.75, []
    # f1 = '%s/%s/%s/search_combs.txt' % (d1, tx, tx1)
    # acc00 = 0.8
    # cn0 = 0
    # t1 = time.time()
    # for i in range(len(ls0)):
    #     cn0 += 1
    #     displaytime(t1, cn0, 1000, s, rslis, f1)
    #     c1, c2, c3 = 0, 0, 0
    #     for j in range(len(ls0[i])):
    #         nms = ls1[j][ls0[i][j]]
    #         c1 += nms[0]
    #         c2 += nms[1]
    #         c3 += nms[2]
    #     acc1 = 1 - (c1+c3) / (s1+s3)
    #     if acc1 > p0:
    #         acc2 = 1 - c2/s2
    #         if acc2 > p1:
    #             acc0 = 1 - (c1+c2+c3) / (s1+s2+s3)
    #             if acc0 > acc00:
    #                 acc3 = 1 - c1/s1
    #                 acc4 = 1 - c3/s3
    #                 j = 0
    #                 # for j in range(min(len(rslis), 3)):
    #                 #     if acc0 > rslis[j][0]:
    #                 #         break
    #                 acc0, acc1, acc2, acc3, acc4 = get_round([acc0, acc1, acc2, acc3, acc4], [3, 3, 3, 3, 3])
    #                 # rslis.insert(j, [acc0, acc1, acc2, acc3, acc4, ls0[i]])
    #                 rslis.append([acc0, acc1, acc2, acc3, acc4, ls0[i]])
    #                 # print('get one!\n', rslis[:min(len(rslis), 3)])
    #                 print('get one!')
    #                 # if len(rslis) >= 3:
    #                 #     acc00 = rslis[2][0]
    # write_str('\n'.join([str(rs0) for rs0 in rslis]), f1)
    # # 人工提取候选数量(最终选择一个)
    # l1 = get_lines1('%s/%s/%s/search_combs.txt' % (d1, tx, tx1))
    # l3 = []
    # for i in range(len(l1)):
    #     l2 = get_split(l1[i])
    #     if '0.88' in l2[1]:
    #         l3.append(l1[i])
    # write_str('\n'.join(l3), '%s/%s/%s/search_combs1.txt' % (d1, tx, tx1))
    # 整合结果、再搜索-亚健康人群acc排序
    ll0 = [269, 7, 55, 13, 22, 7, 23, 18]
    ll1 = [73, 126, 9, 72, 18, 30, 7, 55]
    ll2 = [18, 2, 6, 5, 31, 2, 23, 35]
    ll3 = [31, 70, 23, 41, 3, 122, 11, 279]
    s0, s1, s2, s3 = sum(ll0), sum(ll1), sum(ll2), sum(ll3)
    # n0, n2, n3 = 63, 29, 55
    n0, n2, n3 = 64, 28, 55
    pnjp = 1 - (n0+n2+n3)/(s0+s2+s3)
    pn = 1 - (n0+n3)/(s0+s3)
    pj = 1 - n2/s2
    n = 1 - n0/s0
    p = 1 - n3/s3
    pnjp, pn, pj, n, p = get_round([pnjp, pn, pj, n, p], [3, 3, 3, 3, 3])
    ls0 = ['010', '001', '110', '011']
    # wgs = [[0, 6, 19], [15, 2, 0], [5, 1, 0], [9, 2, 4]]
    wgs = [[0, 6, 19], [15, 2, 0], [6, 0, 0], [9, 2, 4]]
    ns0 = 73
    ns0 += cal_nums0(l1[3], [9, 0, 1], 58, 0)
    mgss = []
    nms = []
    for i in range(len(ls0)):
        for j1 in range(len(ls)):
            if ls0[i] == ls[j1]:
                break
        ln1 = get_lines1('%s/%s/%s/sort_%s.txt' % (d1, tx, tx1, ls0[i]))[1:]
        lists = [ast.literal_eval(line) for line in ln1]
        mgs = []
        for j in range(len(lists)):
            if lists[j][3:6] == wgs[i]:
                nm = cal_nums0(l1[i], lists[j][:3], lists[j][6], 0)
                mgs.append(lists[j][:3]+[lists[j][6], nm])
                nms.append(nm)
        # print(nms)
        mgss.append(mgs)
    # print(nms)  # 第0/1/3序列只有一种数量nm
    mgss[0] = [mgss[0][0]]
    mgss[1] = [mgss[1][0]]
    mgss[3] = [mgss[3][0]]
    rts = []
    for i in range(len(mgss[0])):
        for j in range(len(mgss[1])):
            for k in range(len(mgss[2])):
                for k1 in range(len(mgss[3])):
                    n1 = ns0 + mgss[0][i][-1] + mgss[1][j][-1] + mgss[2][k][-1] + mgss[3][k1][-1]
                    nj = 1 - n1/s1  # 阳性准确率
                    allp = 1 - (n0+n1+n2+n3)/(s0+s1+s2+s3)
                    alln = 1-(n0+s1-n1+n2+n3)/(s0+s1+s2+s3)
                    nj, allp, alln = get_round([nj, allp, alln], [3, 3, 3])
                    rts.append([i, j, k, k1, pnjp, pn, pj, n, p, nj, allp, alln])
    data = np.array(rts)
    idex = np.lexsort((data[:, 8], data[:, 7], data[:, 6], data[:, 5], data[:, 4], data[:, 11], -1 * data[:, 10], data[:, 3], data[:, 2], data[:, 1], data[:, 0], -1 * data[:, 9]))
    sorted_data = data[idex]
    rts = sorted_data.tolist()
    strs = 'i, j, k, k1, pn+p, pn, p+, n, p, n+, all+, all-\n' + '\n'.join([str(rts[i]) for i in range(len(rts))])
    write_str(strs, '%s/%s/%s/search_nj.txt' % (d1, tx, tx1))
    return d1


def cal_nums0(l0, wg, p1, mode=0):
    '''概率加权-阳性/阴性数量统计: l0三概率值, wg三权重值, p1概率阈值, mode0阴性'''
    l2 = [round((l0[i][0]*wg[0]+l0[i][1]*wg[1]+l0[i][2]*wg[2])/100) for i in range(len(l0))]
    if mode:
        nm1 = sum(1 for pi in l2 if pi >= p1)
    else:
        nm1 = sum(1 for pi in l2 if pi < p1)
    return nm1


def get_prob0(nms, d0, m1, d1, d2):
    '''3合1-单模型: 指标、诊断、综合诊断/阈值调整/01'''
    combs = txt_to_list('%s/%s/comb.txt' % (d0, m1))[0]
    comb, p0 = combs[1], combs[2]
    mtnms0 = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, m1))), 0, [])
    rkls = txt_to_list(('%s/%s/utility_rks.txt' % (d0, m1)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2, l3 = [], [], [], []
    l0, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        mts = txt_to_list('%s/%s.txt' % (d1, mtnms[i]))  # 指标
        l3.append(mts)  # 指标
        l11 = []
        for j in range(len(mts)):
            l11.append(diag_res0(mts[j], rkl0[i]))
        l4.append(l11)  # 诊断
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l4 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 综合诊断参数
    l0 = comb_res3(l4, n01, n00)  # 综合诊断
    l5 = change_pers0(l0, p0)
    d3 = '%s/%s' % (d2, m1)
    new_folder(d3)
    strs0 = 'id, %s\n' % ', '.join([mtnms[i] for i in range(len(mtnms))])
    l3 = [nms[i]+', '+str([l3[j][i] for j in range(len(l3))])[1:-1] for i in range(len(nms))]
    write_str(strs0 + '\n'.join(l3), '%s/metrics.txt' % d3)
    l4 = [nms[i]+', '+str(l4[i])[1:-1] for i in range(len(nms))]
    write_str(strs0 + '\n'.join(l4), '%s/diagnosis.txt' % d3)
    l00 = [0 for i in range(len(l0))]
    for i in range(len(l00)):
        if l0[i] >= p0:
            l00[i] = 1
    strs = 'id, p0, p1, 0/1\n'+'\n'.join(['%s, %d, %d, %d' % (nms[i], l0[i], l5[i], l00[i]) for i in range(len(nms))])
    write_str(strs, '%s/results.txt' % d3)
    return l5


def cal_tp0(ps):
    '''计算预测类型'''
    strs = ''
    for p0 in ps:
        if p0 < 50:
            strs += '0'
        else:
            strs += '1'
    return strs


def cal_final_prob0(ps, wts):
    '''加权计算概率'''
    if len(wts) == 3:
        v0 = sum(ps)
        v1, v2 = wts[1]
        p1, p2 = wts[2]
        p0 = round(min(p2, max(p1, p1 + (v0-v1)/(v2-v1)*(p2-p1))))
    else:  # 3
        # print(wts)
        w1, w2, w3 = wts[1]
        v0 = round((w1*ps[0]+w2*ps[1]+w3*ps[2])/100)
        v1, v2, v3 = wts[2]
        # p1, p2, p3 = 0, 50, 100
        if v0 < v2:
            v2 = max(v1+1, v2-1)
            p0 = round(min(49, max(0, (v0-v1)/(v2-v1)*49)))
        else:
            p0 = round(min(100, max(50, 50+(v0-v2)/(v3-v2)*50)))
    return p0


def diag_3_1(l1, f1):
    '''3合1加权诊断: l1概率集, f1权重文档'''
    l0 = txt_to_list(f1)
    l2 = []
    for i in range(len(l1)):
        tp = cal_tp0(l1[i])
        for j in range(len(l0)):
            if l0[j][0] == tp:
                break
        p0 = cal_final_prob0(l1[i], l0[j])  # 最终概率
        l2.append(tp+', '+str(l1[i]+[p0])[1:-1])
    return l2


def test3_1(ids, d0, d1, d2, mds, lbs, d4):
    '''3合1诊断: ids, mode_path, metr_path, resu_path, models, '3_1', mess_path'''
    m1, m2, m3, m31 = mds
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)  # 结果
    nms = get_item(get_lines(ids), 0, [])  # 标签索引
    l1 = []
    for mi in [m1, m2, m3]:
        l0 = get_prob0(nms, d0, mi, d1, d3)
        l1.append(l0)
    l1 = [[l1[i][j] for i in range(len(l1))] for j in range(len(l1[0]))]
    l2 = diag_3_1(l1, '%s/%s/wts.txt' % (d0, m31))
    strs = 'id, type, p1, p2, p3, p0\n'+'\n'.join([nms[i]+', '+l2[i] for i in range(len(nms))])
    write_str(strs, '%s/results.txt' % d3)
    '''测试预测结果
    l1 = get_lines1('%s/3_1/results.txt' % d2)[1:]
    l2 = get_lines1('%s/models3_0/all_1506/results.txt' % d2)[1:]
    for i in range(len(l1)):
        l3 = get_split(l1[i])[1:5]
        l4 = get_split(l2[i])
        l4 = [l4[1]+l4[2]+l4[3]]+l4[4:]
        for j in range(len(l3)):
            if l3[j] != l4[j]:
                print(i)
                break
    '''
    # # 分类acc测试
    # l1 = get_lines1('%s/%s/results.txt' % (d2, lbs))[1:]
    # l2 = []
    # for i in range(len(l1)):
    #     l2.append(int(get_split(l1[i])[-1]))
    # pts = txt_to_list('%s/idx0.txt' % d4)[0]
    # tx0 = '0000x00x100x00101001x0111111x11x011x11010110x100'  # 类型
    # tx1 = ['0000x000x', '1001x010x010', '01x0010x1011', '11010111x1x1111']
    # tx1 = [[tx1[i][3*j:3*j+3] for j in range(int(len(tx1[i])/3))] for i in range(len(tx1))]
    # l0 = [[], [], [], []]
    # for i in range(len(pts)):
    #     tp = tx0[3*i:3*i+3]
    #     for j in range(len(tx1)):
    #         if tp in tx1[j]:
    #             break
    #     l0[j] += pts[i]
    # # for i in range(4):
    # #     print(len(l0[i]))
    # strs = cal_accs0(l2, l0)
    # # 目标3
    # l3 = [l1[i] for i in l0[1]]
    # ls = ['000', '100', '010', '101', '001', '110', '011', '111']
    # ln = [[] for i in range(len(ls))]
    # for i in range(len(l3)):
    #     l4 = get_split(l3[i])
    #     for j in range(len(ls)):
    #         if l4[1] == ls[j]:
    #             break
    #     ln[j].append(int(l4[-1]))
    # for i in range(8):
    #     n1 = sum(1 for j in range(len(ln[i])) if ln[i][j] < 50)
    #     print(n1, len(ln[i]))
    return d2


def cal_accs0(l20, l00):
    l1 = [l20[i] for i in l00[0]]
    l2 = [l20[i] for i in l00[1]]
    l3 = [l20[i] for i in l00[2]]
    l4 = [l20[i] for i in l00[3]]
    s1, s2, s3, s4 = len(l1), len(l2), len(l3), len(l4)
    n1 = sum(1 for i in range(len(l1)) if l1[i] < 50)
    n2 = sum(1 for i in range(len(l2)) if l2[i] >= 50)
    n3 = sum(1 for i in range(len(l3)) if l3[i] >= 50)
    n4 = sum(1 for i in range(len(l4)) if l4[i] >= 50)
    a0 = round((n1+n3+n4)/(s1+s3+s4), 3)
    a1 = round((n1+n4)/(s1+s4), 3)
    a2 = round(n3/s3, 3)
    a3 = round(n1/s1, 3)
    a4 = round(n4/s4, 3)
    a5 = round(n2/s2, 3)
    a6 = round((n1+n2+n3+n4)/(s1+s2+s3+s4), 3)
    print(a0, a1, a2, a3, a4, a5, a6)
    return [a0, a1, a2, a3, a4, a5, a6]


def test3(d0, d1, d2, d3, d4, iks, lbs, lbs0, lbs1):
    '''测试结果: mode_path, mess_path, metr_path, diag_path, resu_path'''
    combs = txt_to_list('%s/%s/comb.txt' % (d0, lbs))[0]
    comb, p0 = combs[1], combs[2]
    idnms = get_item(get_lines('%s/ids.txt' % d1), 0, [])  # 标签索引
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    mtnms0 = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkls = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s/%s.txt' % (d3, lbs0, mtnms[i])))  # 诊断
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d2, mtnms[i])))  # 指标
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0, n01, n00)  # 综合诊断
    l5 = change_pers0(l0, p0)
    # ids = []
    l00 = [0 for i in range(len(l0))]
    # ids0, ids1 = [], []
    # for i in range(len(iks[0])):
    #     ids0 += pts[iks[0][i]][0]
    #     ids1 += pts[iks[0][i]][1]
    for i in range(len(l00)):
        if l0[i] >= p0:
            l00[i] = 1
    new_folder('%s/%s' % (d4, lbs))
    new_folder('%s/%s/%s' % (d4, lbs, lbs1))
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/%s/%s/metrics_rank.txt' % (d4, lbs, lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[j][i]) for j in range(len(l3))])) for i in range(len(l0))])
    write_str(strs, '%s/%s/%s/metrics.txt' % (d4, lbs, lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in range(len(l0))])
    write_str(strs, '%s/%s/%s/diagnosis.txt' % (d4, lbs, lbs1))
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l5[i], l00[i]) for i in range(len(l0))])
    write_str(strs, '%s/%s/%s/results.txt' % (d4, lbs, lbs1))
    return d2


def data_split2(f0):
    '''提取训练集测试集的索引: 阴性阳性偏阳性'''
    pts = txt_to_list(f0)
    tr0 = pts[0][0]+pts[1][0]
    tr1 = pts[0][1]+pts[1][1]
    tr2 = pts[3][1]+pts[4][1]
    te0 = pts[2][0]
    te1 = pts[2][1]
    te2 = pts[5][1]
    return tr0, tr1, tr2, te0, te1, te2


def test00(d1, d2, tx):
    '''综合诊断结果: diag_path, mode_path, lbs'''
    combs = txt_to_list('%s/%s/comb.txt' % (d2, tx))[0]
    comb, p0 = combs[1], combs[2]
    mtnms0 = get_item(get_lines('%s/%s/utility_mts.txt' % (d2, tx)), 0, [])
    rkls = txt_to_list('%s/%s/utility_rks.txt' % (d2, tx))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s/%s.txt' % (d1, tx, mtnms[i])))  # 诊断
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    return l5


def get_ik01(p0, p1):  # 获取2合1模型的类型
    if p0<50 and p1<50:
        ik = 0
    elif p0<50 and p1>=50:
        ik = 1
    elif p0>=50 and p1<50:
        ik = 2
    else:
        ik = 3
    return ik


def test01(l1, l2, d2, tx):
    '''2合1综合诊断: model1, model2, mode_path, lbs'''
    wgs = txt_to_list('%s/%s/comb.txt' % (d2, tx))
    l12 = [[l1[i], l2[i]] for i in range(len(l1))]
    l3, l4 = [], []
    for p0, p1 in l12:
        ik = get_ik01(p0, p1)
        p3 = (wgs[ik][0]*p0+wgs[ik][1]*p1)/100
        l3.append(p3)
        l4.append(change_per0(p3, wgs[ik][2]))
    return l3, l4


def yz00(pts, iks, d4, tx):
    '''验证综合诊断的结果的指标: auc0, acc0, sen0, spe0/1/2/3'''
    l0 = txt_to_list('%s/%s.txt' % (d4, tx))
    ids0, ids1 = get_lable_data01(pts, iks, 0)
    l10, l20 = [l0[i] for i in ids0], [l0[i] for i in ids1]
    ids0, ids1 = get_lable_data01(pts, iks, 1)
    l11, l21 = [l0[i] for i in ids0], [l0[i] for i in ids1]
    ids0, ids1 = get_lable_data01(pts, iks, 2)
    l13, l23 = [l0[i] for i in ids0], [l0[i] for i in ids1]
    auc2, roc0 = cal_auc0(l10+l11, l20+l21)
    p1 = 50
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc2, sen2, spe2 = roc[0]  # 敏感度
    auc0, roc0 = cal_auc0(l10, l20)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc0, sen0, spe0 = roc[0]
    auc1, roc0 = cal_auc0(l11, l21)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc1, sen1, spe1 = roc[0]
    auc3, roc0 = cal_auc0(l13, l23)
    roc = [roc0[i] for i in range(len(roc0)) if roc0[i][0] >= p1]
    _, acc3, sen3, spe3 = roc[0]
    strs = 'Auc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Tr' % (auc0, acc0, sen0, spe0)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Va' % (auc1, acc1, sen1, spe1)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-TrVa' % (auc2, acc2, sen2, spe2)
    strs += '\nAuc-%.3f+Acc-%.3f+Sen-%.3f+Spe-%.3f-Te' % (auc3, acc3, sen3, spe3)
    print(strs)
    return d4


def stat_nums1(strs, l12, ik, ids):
    '''统计4种2合1诊断类型的数量: [tr0, tr1, tr2, te0, te1, te2]'''
    strs += '\n%s' % ik
    for id0 in ids:
        l0 = [l12[i] for i in id0]
        if ik == '00':
            nm = sum(1 for p1, p2 in l0 if p1<50 and p2<50)
        elif ik == '01':
            nm = sum(1 for p1, p2 in l0 if p1<50 and p2>=50)
        elif ik == '10':
            nm = sum(1 for p1, p2 in l0 if p1>=50 and p2<50)
        else:
            nm = sum(1 for p1, p2 in l0 if p1>=50 and p2>=50)
        strs += ', %d' % nm
    return strs


def save_trte012(l12, tr0, tr1, tr2, te0, te1, te2, d4):
    '''保存2合1类型诊断的数量'''
    strs = 'type, tr0, tr1, tr2, te0, te1, te2'  # 
    for tx in ['00', '01', '10', '11']:
        strs = stat_nums1(strs, l12, tx, [tr0, tr1, tr2, te0, te1, te2])
    write_str(strs, '%s/trte012_nums.txt' % d4)


def save_pp4s(l12, ik, ids, d4):
    '''保存4种类型的概率对'''
    l1 = []
    for id0 in ids:
        l0 = [l12[i] for i in id0]
        if ik == '00':
            l1.append([[p1, p2] for p1, p2 in l0 if p1<50 and p2<50])
        elif ik == '01':
            l1.append([[p1, p2] for p1, p2 in l0 if p1<50 and p2>=50])
        elif ik == '10':
            l1.append([[p1, p2] for p1, p2 in l0 if p1>=50 and p2<50])
        else:
            l1.append([[p1, p2] for p1, p2 in l0 if p1>=50 and p2>=50])
        # print(len(l1))
    save_txt(l1, 'list', '%s/pers_%s.txt' % (d4, ik))


def cal_wg01(lis0, l0, l1, l2, i, j, ik):
    '''计算4种类型的最优阈值: acc, spe, sen1, sen2'''
    l012 = sorted(set(l0+l1+l2))
    rocs = []
    for p1 in l012:
        n0 = sum(1 for p0 in l0 if p0 >= p1)  # spe错误数量-
        n1 = sum(1 for p0 in l1 if p0 < p1)  # sen1数量-
        n2 = sum(1 for p0 in l2 if p0 < p1)  # sen2数量-
        ns = [n0, n1, n2]
        rocs.append([sum([ns[j] for j in i]) for i in ik]+[p1])
    data = np.array(rocs)
    idex = np.lexsort((data[:, 4], data[:, 3], data[:, 2], data[:, 1], data[:, 0]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    lis2 = [l[-1] for l in lis1 if l[:-1] == lis1[0][:-1]]
    # [[i, j, acc, spe, sen1, sen2], [p1, p2, ...]]
    lis0.append([[i, j]+get_round(lis1[0][:-1], [0, 0, 0, 0]), lis2])
    return lis0


def cal_wg0(lis0, l0, l1, l2, i, j, ik):
    '''计算4种类型的最优阈值: acc, spe, sen1, sen2——原始!'''
    l012 = sorted(set(l0+l1+l2))
    rocs = []
    for p1 in l012:
        n0 = sum(1 for p0 in l0 if p0 >= p1)  # spe错误数量-
        n1 = sum(1 for p0 in l1 if p0 < p1)  # sen1数量-
        n2 = sum(1 for p0 in l2 if p0 < p1)  # sen2数量-
        n3 = n0+n1+n2
        rocs.append([n3, n0, n1, n2, p1])  # acc, spe, sen1, sen2
    data = np.array(rocs)
    idex = np.lexsort((data[:, 4], data[:, ik[3]], data[:, ik[2]], data[:, ik[1]], data[:, ik[0]]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    lis2 = [l[-1] for l in lis1 if l[:-1] == lis1[0][:-1]]
    lis0.append([[i, j]+get_round(lis1[0][:-1], [0, 0, 0, 0]), lis2])
    return lis0


def search_wg0(d4, tx, ik):
    '''
    搜索4种类型的最优权重&阈值:
        ik=[0, 1, 2, 3]表示排序依次为: acc, spe, sen1, sen2
    '''
    l0, l1, l2 = txt_to_list('%s/pers_%s.txt' % (d4, tx))[:3]
    # i=5
    lis0 = []
    if len(ik) == 2:
        ik1, ik = ik[0], ik[1]
    else:
        ik1 = ik
    for i in range(101):  # 0-100
        j = 100-i
        l00, l01, l02 = [[(p1*i+p2*j)/100 for p1, p2 in ls] for ls in [l0, l1, l2]]
        lis0 = cal_wg01(lis0, l00, l01, l02, i, j, ik1)
    lis1 = [l[0][2:] for l in lis0]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 3], data[:, 2], data[:, 1], data[:, 0]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    idx = [idex[i] for i in range(len(lis1)) if lis1[i] == lis1[0]]
    lis00 = [lis0[i] for i in idx]
    # [[i, j, acc, spe, sen1, sen2], [p1, p2, ...]]——修正!
    write_str('\n'.join([str(l) for l in lis00]), '%s/wgs_%s.txt' % (d4, tx))
    # print(tx)
    # for i in idex:
    #     print(lis0[i])
    return d4


def cal_wg1(lis0, l0, l1, l2, i, j, p1):
    '''测试集上的阈值结果: acc, spe, sen1, sen2'''
    n0 = sum(1 for p0 in l0 if p0 >= p1)  # spe错误数量-
    n1 = sum(1 for p0 in l1 if p0 < p1)  # sen1数量-
    n2 = sum(1 for p0 in l2 if p0 < p1)  # sen2数量-
    n3 = n0+n1+n2
    lis0.append([i, j, n3, n0, n1, n2, p1])  # acc, spe, sen1, sen2
    return lis0


def search_wg1(d4, tx):
    '''
    测试集上的全部结果导出: 探索4种类型的阈值&权重选取策略: 
        最后一个权重&阈值(model1的权重最大)
    '''
    l0, l1, l2 = txt_to_list('%s/pers_%s.txt' % (d4, tx))[3:]
    ls0 = txt_to_list('%s/wgs_%s.txt' % (d4, tx))
    lis0 = []
    for i1 in range(len(ls0)):
        lis1 = []
        for p1 in ls0[i1][1]:
            i, j = ls0[i1][0][:2]
            l00, l01, l02 = [[(p1*i+p2*j)/100 for p1, p2 in ls] for ls in [l0, l1, l2]]
            lis1 = cal_wg1(lis1, l00, l01, l02, i, j, p1)
        lis0.append(lis1)
    write_str('\n'.join([str(l) for l in lis0]), '%s/te_%s.txt' % (d4, tx))
    return d4


def test4(d0, d1, d2, d3, lbs):
    s1, s2, s12 = 'model1', 'model2', 'model12'
    if lbs[-2:] == '_1':
        s1, s2, s12 = 'model1_1', 'model2_1', 'model12_1'
    l1 = test00(d1, d2, s1)
    l2 = test00(d1, d2, s2)
    l3, l4 = test01(l1, l2, d2, s12)  # 诊断结果
    tr0, tr1, tr2, te0, te1, te2 = data_split2('%s/idx1.txt' % d0)  # 索引
    ids = get_item(get_lines('%s/ids.txt' % d0), 0, [])  # 标签索引
    tr, te = tr0+tr1+tr2, te0+te1+te2
    l0 = [0 for p in l4]
    for i in range(len(l4)):
        if l4[i] >= 50:
            l0[i] = 1
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    s0 = 'id, [p1, p2], Predict_final, 0/1\n'
    strs = s0+'\n'.join(['%s, [%d, %d], %.2f, %d' % (ids[i], l1[i], l2[i], l4[i], l0[i]) for i in tr])
    write_str(strs, '%s/results_tr.txt' % d4)
    strs = s0+'\n'.join(['%s, [%d, %d], %.2f, %d' % (ids[i], l1[i], l2[i], l4[i], l0[i]) for i in te])
    write_str(strs, '%s/results_te.txt' % d4)
    return d4


def revi_models12(d0, d1, d2, d3, lbs):
    '''测试2合1模型结果: mess_path, diag_path, mode_path, revi_path, lbs'''
    s1, s2, s12 = 'model1', 'model2', 'model12'
    if lbs[-2:] == '_1':
        s1, s2, s12 = 'model1_1', 'model2_1', 'model12_1'
    l1 = test00(d1, d2, s1)
    l2 = test00(d1, d2, s2)
    l3, l4 = test01(l1, l2, d2, s12)  # 诊断结果
    tr0, tr1, tr2, te0, te1, te2 = data_split2('%s/idx1.txt' % d0)  # 索引
    # idx = txt_to_list('%s/label4s.txt' % d0)[2]  # 偏阳性测试结果
    # pts = txt_to_list('%s/idx0.txt' % d0)
    # iks = []
    # for i in idx:
    #     iks += pts[i]
    # tr2 = [i for i in tr2 if i in iks]
    # te2 = [i for i in te2 if i in iks]
    # print(len(tr2))
    # print(len(te2))
    # n1 = sum(1 for i in tr2 if l4[i]>=50)
    # n2 = sum(1 for i in te2 if l4[i]>=50)
    # print(n1/len(tr2))
    # print(n2/len(te2))
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    strs = '\n'.join([str([l3[i], l4[i]]) for i in range(len(l3))])
    write_str(strs, '%s/%s.txt' % (d4, s12))
    f00 = revi_mode7(l4, tr0, tr1, tr2, te0, te1, te2, '%s/tr_te.txt' % d4)
    return d4


def comb_models0(d0, d1, d2, d3, lbs):
    '''2合1模型-组合搜索: mess_path, diag_path, mode_path, sear_path, lbs'''
    s1, s2, s12 = 'model1', 'model2', 'model12'
    if lbs[-2:] == '_1':
        s1, s2, s12 = 'model1_1', 'model2_1', 'model12_1'
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    # l5 = test00(d1, d2, s1)  # 单次生成-综合诊断——
    # save_txt(l5, 'list', '%s/%s.txt' % (d4, s1))
    # l5 = test00(d1, d2, s2)
    # save_txt(l5, 'list', '%s/%s.txt' % (d4, s2))
    # pts = txt_to_list('%s/idx1.txt' % d0)  # 单次验证-综合诊断——
    # d00 = yz00(pts, [[0], [1], [2]], d4, s1)
    # d00 = yz00(pts, [[3], [4], [5]], d4, s2)
    # tr0, tr1, tr2, te0, te1, te2 = data_split2('%s/idx1.txt' % d0)  # 索引
    # l1 = txt_to_list('%s/%s.txt' % (d4, s1))
    # l2 = txt_to_list('%s/%s.txt' % (d4, s2))
    # l12 = [[l1[i], l2[i]] for i in range(len(l1))]
    # save_txt(l12, 'list', '%s/%s.txt' % (d4, s12))  # 单次保存-综合诊断——
    # save_trte012(l12, tr0, tr1, tr2, te0, te1, te2, d4)  # 单次保存-数量统计——
    # for tx in ['00', '01', '10', '11']:  # 单次保存-分类概率对——
    #     save_pp4s(l12, tx, [tr0, tr1, tr2, te0, te1, te2], d4)
    txs = ['00', '01', '10', '11']
    # iks = [[[0, 1, 2, 3], [3, 2, 0, 1]], [[0, 1, 3, 2], [1, 0, 3, 2]], [[0, 1, 2, 3], [0, 1, 2, 3]], [[0, 2, 3, 1], [0, 1, 2, 3]]]
    # iks = [[0, 1, 2, 3], [0, 1, 3, 2], [0, 1, 2, 3], [0, 2, 3, 1]]
    # iks = [[0, 1, 2, 3], [0, 1, 3, 2], [0, 1, 3, 2], [0, 2, 3, 1]]
    # iks = [[[0, 2], [0, 1, 2], [0], [1]]]
    # iks.append([[0, 2], [0, 1, 2], [0], [1]])
    # iks.append([[0, 1, 2], [0, 2], [0], [1]])
    # iks.append([[0, 1, 2], [0, 2], [0], [1]])
    # for i in range(len(txs)):
    #     d00 = search_wg0(d4, txs[i], iks[i])  # 权重&阈值组合, 保存修正n012!
    for i in range(len(txs)):
        d00 = search_wg1(d4, txs[i])  # 测试&固定模型
    return d3


def test21(d0, d1, d2, d3, d4, lbs):
    '''外部测试结果te: mode_path, mess_path, metr_path, diag_path, resu_path'''
    # tx = 'tr-va'
    tx = 'trva'
    d5 = '%s/%s' % (d4, lbs)
    new_folder(d5)
    # 1.外部验证测试
    combs = txt_to_list('%s/%s/comb_%s.txt' % (d0, lbs, tx))[0]
    comb, p0 = combs[1], combs[2]
    # idnms = get_item(get_lines('%s/ids.txt' % d1), 0, [])  # 标签——
    # ids = txt_to_list('%s/idx_te.txt' % d1)[0]  # 标签索引————
    # ts = txt_to_list('%s/times_te.txt' % d1)  # 时刻点——
    idnms = get_item(get_lines('%s/ids1.txt' % d1), 0, [])  # 标签——
    ids = txt_to_list('%s/idx_te1.txt' % d1)[0]  # 标签索引————
    # ts = txt_to_list('%s/times_te1.txt' % d1)  # 时刻点——
    mtnms0 = get_lines1('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx))
    rkls = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s_%s/%s.txt' % (d3, lbs, tx, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    l00 = [0 for i in range(len(l0))]
    for i in ids:
        if l0[i] >= p0:
            l00[i] = 1
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/rank_%s.txt' % (d5, tx))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in ids])
    write_str(strs, '%s/diagnosis_%s.txt' % (d5, tx))
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l5[i], l00[i]) for i in ids])
    write_str(strs, '%s/results_%s.txt' % (d5, tx))
    # # 2.保存为pkl-id/pro/pred/times——
    # l0 = get_lines1('%s/results_%s.txt' % (d5, tx))[1:]
    # l1 = [get_item(l0, 0, []), get_item(l0, 1, []), get_item(l0, 3, [])]
    # l2 = [[l1[i][j] for i in range(len(l1))] for j in range(len(l1[0]))]
    # l3 = [l2[i]+ts[i] for i in range(len(l2))]
    # N = np.array([[l3[i][0], *map(int, l3[i][1:])] for i in range(len(l3))], dtype=object)
    # f00 = write_pkl(N, '%s/外部0829_%s.pkl' % (d5, tx))
    return d2


def test22(d0, d1, d2, d3, d4, lbs, f0):
    '''外部测试结果te: mode_path, mess_path, metr_path, diag_path, resu_path'''
    # 0.标签准备
    data = pd.read_excel(f0, sheet_name='Sheet3').values
    vs = []
    for i in [1, 3, 4, 5, 6, 7, 8]:
        vs.append(get_xlsx_value(data, i))
    ids = vs[0]
    # write_str('\n'.join(ids), '%s/ids2.txt' % d1)  # 写入id索引/一次性——
    # write_str('\n'.join(ids), '%s/ids.txt' % d1, 'a')  # 检查换行
    # n1 = len(get_lines1('%s/ids.txt' % d1))
    # save_txt([list(range(n1,n1+len(ids)))], 'list', '%s/idx_te2.txt' % d1)
    # ids0 = get_lines1('./results/consistent3/messages/ids.txt')
    # idx = get_idxs(ids0, ids)
    # d6 = './results/consistent3/metrics'
    # for item in os.listdir(d6):  # 添加指标/一次性——
    #     l1 = get_lines1('%s/%s' % (d6, item))
    #     l1 = [l1[i] for i in idx]
    #     write_str('\n'+'\n'.join(l1), '%s/%s' % (d2, item), 'a')
    # tx = 'trva'  # 添加诊断/一次性——
    # mtnms = get_lines1('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx))
    # rkmes = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    # d00 = diag_resu0(d2, mtnms, rkmes, d3, '%s_%s' % (lbs, tx))  # 诊断
    
    tx = 'trva'
    d5 = '%s/%s' % (d4, lbs)
    new_folder(d5)
    # 1.外部验证测试
    combs = txt_to_list('%s/%s/comb_%s.txt' % (d0, lbs, tx))[0]
    comb, p0 = combs[1], combs[2]
    idnms = get_item(get_lines('%s/ids.txt' % d1), 0, [])  # 标签——
    ids = txt_to_list('%s/idx_te2.txt' % d1)[0]  # 标签索引————
    mtnms0 = get_lines1('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx))
    rkls = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s_%s/%s.txt' % (d3, lbs, tx, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    l00 = [0 for i in range(len(l0))]
    for i in ids:
        if l0[i] >= p0:
            l00[i] = 1
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/rank_%s1.txt' % (d5, tx))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in ids])
    write_str(strs, '%s/diagnosis_%s1.txt' % (d5, tx))
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l5[i], l00[i]) for i in ids])
    write_str(strs, '%s/results_%s1.txt' % (d5, tx))
    return d2


def test2(d0, d1, d2, d3, d4, iks, lbs, lbs0, lbs1):
    '''测试结果: mode_path, mess_path, metr_path, diag_path, resu_path'''
    combs = txt_to_list('%s/%s/comb.txt' % (d0, lbs))[0]
    comb, p0 = combs[1], combs[2]
    idnms = get_item(get_lines('%s/ids.txt' % d1), 0, [])  # 标签索引
    pts = txt_to_list('%s/idx1.txt' % d1)  # 标签索引————
    mtnms0 = get_item(get_lines('%s/%s/utility_mts.txt' % (d0, lbs)), 0, [])
    rkls = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s/%s.txt' % (d3, lbs0, mtnms[i])))  # 诊断
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d2, mtnms[i])))  # 指标
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    # ids = []
    l00 = [0 for i in range(len(l0))]
    ids0, ids1 = [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    for i in ids0:
        if l0[i] >= p0:
            l00[i] = 1
            # ids.append(i)
    for i in ids1:
        if l0[i] >= p0:
            l00[i] = 1
        # if l0[i] < p0:
        #     l00[i] = 0
        #     ids.append(i)
    new_folder('%s/%s' % (d4, lbs))
    new_folder('%s/%s/%s' % (d4, lbs, lbs1))
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/%s/%s/metrics_rank.txt' % (d4, lbs, lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[j][i]) for j in range(len(l3))])) for i in ids0+ids1])
    write_str(strs, '%s/%s/%s/metrics.txt' % (d4, lbs, lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in ids0+ids1])
    write_str(strs, '%s/%s/%s/diagnosis.txt' % (d4, lbs, lbs1))
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l5[i], l00[i]) for i in ids0+ids1])
    write_str(strs, '%s/%s/%s/results.txt' % (d4, lbs, lbs1))
    return d2


def test0(d0, d1, d2, d3, d4, iks, lbs, lbs0, lbs1):
    '''测试结果: mode_path, mess_path, metr_path, diag_path, resu_path'''
    combs = txt_to_list('%s/%s/comb.txt' % (d0, lbs))[0]
    comb, p0 = combs[1], combs[2]
    idnms = get_item(get_lines('%s/ids.txt' % d1), 0, [])  # 标签索引
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    mtnms0 = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkls = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s/%s.txt' % (d3, lbs0, mtnms[i])))  # 诊断
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d2, mtnms[i])))  # 指标
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    ids = []
    l00 = [0 for i in range(len(l0))]
    ids0, ids1 = [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    for i in ids0:
        if l0[i] >= p0:
            l00[i] = 1
            ids.append(i)
    for i in ids1:
        if l0[i] < p0:
            l00[i] = 0
            ids.append(i)
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/%s/%s/metrics_rank.txt' % (d4, lbs,lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[j][i]) for j in range(len(l3))])) for i in ids])
    write_str(strs, '%s/%s/%s/metrics.txt' % (d4, lbs, lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in ids])
    write_str(strs, '%s/%s/%s/diagnosis.txt' % (d4, lbs, lbs1))
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l5[i], l00[i]) for i in ids])
    write_str(strs, '%s/%s/%s/results.txt' % (d4, lbs, lbs1))
    return d2


def test1(d0, d1, d2, d3, d4, iks, lbs, lbs0, lbs1):
    '''测试结果: mode_path, mess_path, metr_path, diag_path, resu_path'''
    combs = txt_to_list('%s/%s/comb.txt' % (d0, lbs))[0]
    comb, p0 = combs[1], combs[2]
    idnms = get_item(get_lines('%s/ids.txt' % d1), 0, [])  # 标签索引
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    mtnms0 = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkls = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l1, l2, l3 = [], [], [], []
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s/%s.txt' % (d3, lbs0, mtnms[i])))  # 诊断
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d2, mtnms[i])))  # 指标
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    ids = []
    l00 = [0 for i in range(len(l0))]
    for i in range(len(iks)):
        for j in range(len(pts[iks[i]])):
            ids += pts[iks[i]][j]
    for i in ids:
        if l0[i] >= p0:
            l00[i] = 1
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/%s/%s/metrics_rank.txt' % (d4, lbs, lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[j][i]) for j in range(len(l3))])) for i in ids])
    write_str(strs, '%s/%s/%s/metrics.txt' % (d4, lbs, lbs1))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in ids])
    write_str(strs, '%s/%s/%s/diagnosis.txt' % (d4, lbs, lbs1))
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l5[i], l00[i]) for i in ids])
    write_str(strs, '%s/%s/%s/results.txt' % (d4, lbs, lbs1))
    return d2


def diag_results1(d0, d1, d2, d3, lbs, ik0, iks):
    '''诊断结果、效用因子计算和排序: d0分析, d1信息 d2指标, d3诊断, lbs版本'''
    # 1.1数据准备
    # f0 = '%s/%s/rks.txt' % (d0, lbs)
    # new_folder('%s/%s' % (d1, lbs))
    # f1 = '%s/%s/rks.txt' % (d1, lbs)
    # shutil.copyfile(f0, f1)
    # mtidx = txt_to_list('%s/%s/utility_idx.txt' % (d0, lbs))[0]
    # mtnms0 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    # mtnms = [mtnms0[mtidx[i]] for i in range(ik0)]
    # write_str('\n'.join(mtnms), '%s/%s/mts.txt' % (d1, lbs))  # 写入指标
    # 1.2单指标诊断**
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    rkmes = txt_to_list('%s/%s/rks.txt' % (d1, lbs))
    d6 = diag_resu0(d2, mtnms, rkmes, d3, lbs)  # 诊断
    # 2.效用计算和排序
    f0 = '%s/metrics_cls.txt' % d1
    mtidx = txt_to_list('%s/%s/utility_idx.txt' % (d0, lbs))[0]
    list0 = [mtidx[i] for i in range(ik0)]
    # fd1 = ['%s/%s/rks.txt' % (d1, lbs), d2]  # 分级文档和指标
    fd1 = '%s/%s' % (d3, lbs)  # 诊断结果
    d4 = '%s/%s' % (d1, lbs)
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    idex = get_utility_sort0(f0, list0, fd1, [ids0, ids1], d4)
    return d2


def revi_results0(d1, d2, lbs, d3):
    '''spect模型way1/2的审查: tr_roc, te_roc, acc'''
    pts = txt_to_list('%s/idx.txt' % d3)  # 标签索引
    d3 = '%s/%s' % (d1, lbs)
    d4 = '%s/%s' % (d2, lbs)
    new_folder(d4)
    items = os.listdir(d3)
    # i1 = 0
    for i1 in range(len(items)):
        print(items[i1])
        l0 = txt_to_list('%s/%s' % (d3, items[i1]))
        d5 = '%s/%s' % (d4, items[i1][:-4])
        new_folder(d5)
        lbs0 = ['spect', 'qfr', 'imr', 'all']
        revi_resu0(l0, d5, lbs0, pts, 0)
        revi_resu0(l0, d5, lbs0, pts, 1)
        revi_resu0(l0, d5, lbs0, pts, 2)
        revi_resu0(l0, d5, lbs0, pts, 3, 1)
    return d4


def annalysis_results0(d0, lbs):
    '''分析结果: d0审查, lbs版本'''
    d1 = '%s/%s' % (d0, lbs)
    f1 = '%s/way2_results.txt' % d1
    tx1 = ['spect', 'all', 'qfr', 'imr']
    strs = []
    for i1 in range(4):
        item = 'way2_V%d' % (i1+1)
        strs0 = annalysis_resu0(d1, item, tx1)
        strs.append(strs0)
    write_str('\n\n'.join(strs), f1)
    return d0


def comb_diag0(d0, d1, d2, d3, lbs, nm, iks, tx1):
    '''综合诊断: d0信息, d1诊断, d2综合诊断, d3审查, lbs版本'''
    mtnms0 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[0])
    mtidx = txt_to_list('%s/%s/utility_idx.txt' % (d0, lbs))[0]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    d4, d5, d6 = '%s/%s' % (d1, lbs), '%s/%s' % (d2, lbs), '%s/%s' % (d3, lbs)
    l0 = txt_to_list('%s/%s/utility_rks.txt' % (d0, lbs))
    d2 = comb_dg0(mtnms0, mtidx, nm, d4, pts, d5, d6, l0, iks, tx1)
    return d2


def comb_results0(d0, d1, d2, lbs):
    '''综合诊断: d0信息, d1诊断, d2综合诊断, lbs版本, pts标签索引'''
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    nm = [0, 18, 25, 31, 24]
    mtss = metrics_cls0(d0, lbs, nm)  # 指标分类
    for i1 in range(4):
        f1 = comb_resu1(d0, d1, mtss, nm, i1, d2, lbs, pts)
    return d2


def comb_results2(d0, d1, d2, lbs):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    nm = [0, 18, 25, 31, 24]
    rk0 = txt_to_list('%s/%s/rks_sort.txt' % (d0, lbs))
    ls11, ls12 = [0, 0.3, 0.4, 0.6, 1], [50, 55, 75, 90, 100]
    ls01, ls02 = [0, 0.3, 0.6, 1], [50, 55, 90, 100]
    ls = [ls11, ls12, ls01, ls02]
    mtss = metrics_cls0(d0, lbs, nm)  # 指标分类
    l0 = []
    for i2 in range(4):
        for j2 in range(len(mtss[i2])):
            l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtss[i2][j2])))
    l0 = [[l0[i2][j2] for i2 in range(len(l0))] for j2 in range(len(l0[0]))]
    comb1 = [0, 2, 4, 5, 6, 7, 8, 9, 13, 14, 15, 17]
    comb2 = comb1 + [22, 23, 27, 28, 29, 30, 36]
    comb3 = comb2 + [46, 56, 57, 63, 67]
    comb41 = comb3 + [76, 82, 88, 93, 95]
    comb42 = comb3 + [76, 82, 88, 93, 97]
    combs = [comb1, comb2, comb3, comb41, comb42]
    tx1 = ['comb1', 'comb2', 'comb3', 'comb41', 'comb42']
    iks = [[0], [1], [2, 3], [4, 5], [0, 1], [2, 3, 4, 5], [0, 1, 2, 3, 4, 5]]
    tx2 = ['spect_tr', 'spect_te', 'qfr', 'imr', 'spect', 'qfrimr', 'all']
    f1 = '%s/%s/way2_results.txt' % (d2, lbs)
    strs = '————results'
    p1 = 45
    # for i2 in range(len(combs)):
    #     comb = combs[i2]
    #     strs += '\n%s:' % tx1[i2]
    #     l1 = [max(rk0[i2][1]) for i2 in comb]
    #     l2 = [min(rk0[i2][1]) for i2 in comb]
    #     n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
    #     # j2 = 0
    #     for j2 in range(len(iks)):
    #         ids0, ids1 = [], []
    #         for k2 in range(len(iks[j2])):
    #             ids0 += pts[iks[j2][k2]][0]
    #             ids1 += pts[iks[j2][k2]][1]
    #         l1 = [[l0[i3][j3] for j3 in comb] for i3 in ids0]
    #         l2 = [[l0[i3][j3] for j3 in comb] for i3 in ids1]
    #         l11 = comb_res2(l1)
    #         l22 = comb_res2(l2)
    
    # ls1, ls2 = [], []
    # for k2 in range(len(l11)):
    #     if l11[k2] > 0:
    #         a=l1[k2]
    #         a.sort()
    #         print([a[i]-50 for i in range(19)])
    #         # ls1.append([a[i]-50 for i in range(19)])
    # print('pos')
    # for k2 in range(len(l22)):
    #     if l22[k2] < 0:
    #         a=l2[k2]
    #         a.sort()
    #         print([a[i]-50 for i in range(19)])
    #         # ls2.append([a[i]-50 for i in range(19)])
    # auc, roc = cal_auc0(l1, l2)
    # for k2 in range(len(roc)):
    #     print(roc[k2][0])
    #     if roc[k2][2] < roc[k2][3]:
    #         continue
    #     k3 = k2
    # print(roc[k3])
    # for j2 in range(len(l1)):
    #     print(l1[j2])
    for i2 in range(len(combs)):
        comb = combs[i2]
        strs += '\n%s:' % tx1[i2]
        l1 = [max(rk0[i2][1]) for i2 in comb]
        l2 = [min(rk0[i2][1]) for i2 in comb]
        n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
        for j2 in range(len(iks)):
            ids0, ids1 = [], []
            for k2 in range(len(iks[j2])):
                ids0 += pts[iks[j2][k2]][0]
                ids1 += pts[iks[j2][k2]][1]
            l1 = [[l0[i3][j3] for j3 in comb] for i3 in ids0]
            l2 = [[l0[i3][j3] for j3 in comb] for i3 in ids1]
            l1 = comb_res1(l1, n01, n00)
            l2 = comb_res1(l2, n01, n00)
            auc, roc = cal_auc0(l1, l2)
            for k2 in range(len(roc)):
                if roc[k2][0] >= p1:
                    acc, sen, spe = roc[k2][1], roc[k2][2], roc[k2][3]
                    break
            lis0 = [p1, get_round(auc, 3), acc, sen, spe]
            strs += '\n%s, %s' % (tx2[j2], str(lis0))
    write_str(strs, f1)
    return d2


def comb_results4(d0, d1, d2, lbs):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    nm = [0, 18, 25, 31, 24]
    rk0 = txt_to_list('%s/%s/rks_sort.txt' % (d0, lbs))
    rk1, rk2 = [max(rk0[i1][1]) for i1 in range(len(rk0))], [min(rk0[i1][1]) for i1 in range(len(rk0))]
    mtss = metrics_cls0(d0, lbs, nm)  # 指标分类
    l0 = []
    for i2 in range(4):
        for j2 in range(len(mtss[i2])):
            l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtss[i2][j2])))
    l0 = [[l0[i2][j2] for i2 in range(len(l0))] for j2 in range(len(l0[0]))]
    for vers in [1, 2, 3, 4]:
        f0 = '%s/%s/V%d1_search_results.txt' % (d2, lbs, vers)
        f1 = '%s/%s/V%d1_tests1.txt' % (d2, lbs, vers)
        combs = txt_to_list(f0)
        # combs = [combs[i1][1] for i1 in range(len(combs))]
        # print(combs)
        iks = [[0], [1], [0, 1], [2], [3], [2, 3], [4], [5], [4, 5], [2, 3, 4, 5], [0, 1, 2, 3, 4, 5]]
        tx2 = ['spect_tr', 'spect_te', 'spect', 'qfr_tr', 'qfr_te', 'qfr', 'imr_tr', 'imr_te', 'imr', 'qfrimr', 'all']
        strs = '————results'
        p1 = 35
        for i1 in range(len(combs)):
            comb = combs[i1][1]
            l1, l2 = [rk1[i2] for i2 in comb], [rk2[i2] for i2 in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[i2][j2] for j2 in comb] for i2 in range(len(l0))]
            l00 = cal_per(l00, n01, n00)
            if i1 > 0:
                strs += '\n\n'
            strs += '\ncomb%d: %s' % (i1, str(comb))
            l1 = [max(rk0[i2][1]) for i2 in comb]
            l2 = [min(rk0[i2][1]) for i2 in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            # j2 = 0
            for j2 in range(len(iks)):
                ids0, ids1 = [], []
                for k2 in range(len(iks[j2])):
                    ids0 += pts[iks[j2][k2]][0]
                    ids1 += pts[iks[j2][k2]][1]
                l1, l2 = [l00[i2] for i2 in ids0], [l00[i2] for i2 in ids1]
                auc, roc = cal_auc0(l1, l2)
                for k2 in range(len(roc)):
                    if roc[k2][0] >= p1:
                        acc, sen, spe = roc[k2][1], roc[k2][2], roc[k2][3]
                        break
                lis0 = [p1, get_round(auc, 3), acc, sen, spe]
                strs += '\n%s:\n%s' % (tx2[j2], str(lis0))
        write_str(strs, f1)
    return d2


def comb_results5(d0, d1, d2, lbs):
    '''指标搜索: spect模型生成qfr上的测试结果'''
    ids = get_lines1('%s/ids.txt' % d0)  # 标签索引
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    nm = [0, 18, 25, 31, 24]
    rk0 = txt_to_list('%s/%s/rks_sort.txt' % (d0, lbs))
    rk1, rk2 = [max(rk0[i1][1]) for i1 in range(len(rk0))], [min(rk0[i1][1]) for i1 in range(len(rk0))]
    mtss = metrics_cls0(d0, lbs, nm)  # 指标分类
    l0 = []
    for i2 in range(4):
        for j2 in range(len(mtss[i2])):
            l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtss[i2][j2])))
    l0 = [[l0[i2][j2] for i2 in range(len(l0))] for j2 in range(len(l0[0]))]
    vers = 4
    f0 = '%s/%s/V%d1_search_results.txt' % (d2, lbs, vers)
    f1 = '%s/%s/V%d1_results.txt' % (d2, lbs, vers)
    combs = txt_to_list(f0)
    iks = [[2, 3]]
    tx2 = ['qfr']
    strs = 'id, results'
    p1 = 35
    i1 = 0
    comb = combs[i1][1]
    l1, l2 = [rk1[i2] for i2 in comb], [rk2[i2] for i2 in comb]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
    l00 = [[l0[i2][j2] for j2 in comb] for i2 in range(len(l0))]
    l00 = cal_per(l00, n01, n00)
    j2 = 0
    ids0, ids1 = [], []
    for k2 in range(len(iks[j2])):
        ids0 += pts[iks[j2][k2]][0]
        ids1 += pts[iks[j2][k2]][1]
    for i2 in range(len(l00)):
        if l00[i2] < 35:
            l00[i2] = 0
        else:
            l00[i2] = 1
    for i2 in ids0:
        strs += '\n%s, %d' % (ids[i2], l00[i2])
    for i2 in ids1:
        strs += '\n%s, %d' % (ids[i2], l00[i2])
    write_str(strs, f1)
    return d2


def comb_results6(d0, d1, d2, lbs):
    '''指标搜索: spect模型生成qfr上的测试结果'''
    ids = get_lines1('%s/ids.txt' % d0)  # 标签索引
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    nm = [0, 18, 25, 31, 24]
    rk0 = txt_to_list('%s/%s/rks_sort.txt' % (d0, lbs))
    rk1, rk2 = [max(rk0[i1][1]) for i1 in range(len(rk0))], [min(rk0[i1][1]) for i1 in range(len(rk0))]
    mtss = metrics_cls0(d0, lbs, nm)  # 指标分类
    l0 = []
    for i2 in range(4):
        for j2 in range(len(mtss[i2])):
            l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtss[i2][j2])))
    l0 = [[l0[i2][j2] for i2 in range(len(l0))] for j2 in range(len(l0[0]))]
    combs = txt_to_list('%s/%s/combs_all.txt' % (d2, lbs))
    ls = [l0, rk1, rk2]
    iks = [[0, 1], [2, 3], [4, 5]]
    tx2 = ['spect', 'qfr', 'imr']
    strs = ''
    for i in range(len(combs)):
        strs = revi_mode0(ls, combs[i][1], pts, iks, tx2, strs)
    write_str(strs, '%s/%s/search_results.txt' % (d2, lbs))
    return d2


def search_diag2(d0, d1, d2, lbs, nm, iks):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    ts = [[], [], [], []]
    for i in range(len(iks[0])):
        ts[0] += pts[iks[0][i]][0]
        ts[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ts[2] += pts[iks[1][i]][0]
        ts[3] += pts[iks[1][i]][1]
    comb0 = [4, 5, 6, 8, 9, 11, 12, 17, 19]
    ls = [l0, l1, l2]
    i = 1
    # for i in range(len(nm)):
    f1 = '%s/V%d_combs.txt' % (d3, i+1)
    comb0 = search_dg0(nm, i, comb0, ls, ts, f1, 5)
    return d2


def search_diag0(d0, d1, d2, lbs, nm, i1, iks):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    ts = [[], [], [], []]
    for i in range(len(iks[0])):
        ts[0] += pts[iks[0][i]][0]
        ts[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ts[2] += pts[iks[1][i]][0]
        ts[3] += pts[iks[1][i]][1]
    comb0 = []
    ls = [l0, l1, l2]
    # i = 0
    for i in range(len(nm)):
        f1 = '%s/V%d_combs.txt' % (d3, i+1)
        comb0 = search_dg0(nm, i1[i], i, comb0, ls, ts, f1)
    return d2


def search_diag3(d0, d1, d2, lbs, nm, iks):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    d3 = '%s/%s_va' % (d2, lbs)
    new_folder(d3)
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rkl0[i][1]))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    ts = [[], [], [], []]
    for i in range(len(iks[0])):
        ts[0] += pts[iks[0][i]][0]
        ts[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ts[2] += pts[iks[1][i]][0]
        ts[3] += pts[iks[1][i]][1]
    comb0 = [0, 1, 2, 4, 7, 8, 9, 11, 21, 22, 23, 31, 41, 42, 44, 46]
    ls = [l0, l1, l2]
    i = 6
    # for i in range(len(nm)):
    f1 = '%s/V%d_combs.txt' % (d3, i+1)
    comb0 = search_dg0(nm, i, comb0, ls, ts, f1)
    return d2


def search_diag1_0(d0, d1, d2, lbs, iks):
    '''指标搜索: mess_path, diag_path, sear_path, lbs版本, iks'''
    # nm = [20, 30, 30], zd1 = [10, 7, 7]  # 最多选取数量——
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引——
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    s1, s2, s3, s4 = 'V1_mgd0', 'V1_mgd1', 'V2_mgd', 'V3_mgd'
    # s1, s2, s3, s4 = 'V1_acc0', 'V1_acc1', 'V2_acc', 'V3_acc'
    # s1, s2, s3, s4 = 'V1_wacc0', 'V1_wacc1', 'V2_wacc', 'V3_wacc'
    # s1, s2, s3, s4 = 'V1_wbce0', 'V1_wbce1', 'V2_wbce', 'V3_wbce'
    comb0 = []
    ts = get_trva_idx0(pts, iks, [0], [0, 1])  # tr搜trva
    comb0 = search_dg1_0(0, 20, [4], 10, comb0, ls, ts, '%s/%s.txt'% (d3, s1))
    # comb0 = [6, 13, 16, 18]
    comb0 = search_dg1_0(0, 20, [0, 3], 10, comb0, ls, ts, '%s/%s.txt'% (d3, s2))
    # comb0 = [1, 2, 5, 7, 8, 12, 14, 16, 18]
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜va
    comb0 = search_dg1_0(20, 30, [0, 3], 7, comb0, ls, ts, '%s/%s.txt'% (d3, s3))
    ts = get_trva_idx0(pts, iks, [0, 1], [0, 1])  # trva搜trva
    comb0 = search_dg1_0(50, 30, [0, 3], 7, comb0, ls, ts, '%s/%s.txt'% (d3, s4))
    return d3


def search_diag5(d0, d1, d2, lbs, nm, iks):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    pts = txt_to_list('%s/idx1.txt' % d0)  # 标签索引——
    comb0 = []
    ts = get_trva_idx0(pts, iks, [0], [0, 1])  # tr搜trva
    # comb0 = search_dg1(nm, 0, comb0, ls, ts, '%s/V1_tr_trva.txt' % d3)
    comb0 = [2, 3, 5, 7, 9, 10, 11, 13, 16]
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜va
    comb0 = search_dg1(nm, 1, comb0, ls, ts, '%s/V2_tr_va.txt' % d3)
    ts = get_trva_idx0(pts, iks, [0, 1], [0, 1])  # trva搜trva
    comb0 = search_dg1(nm, 2, comb0, ls, ts, '%s/V3_trva_trva.txt' % d3)
    return d2


def search_diag6(d0, d1, d2, lbs, nm, iks):
    '''快速组合搜索: d0信息, d1诊断, d2搜索, lbs版本
    可选的: tx, pts, comb0, save.txt'''
    # tx = 'trva'  # trva/tr-va两种搜索方法
    tx = 'tr-va'  # trva/tr-va两种搜索方法
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_item(get_lines(('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx))), 0, [])
    rkl0 = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s_%s/%s.txt' % (d1, lbs, tx, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    ls = [l0, l1, l2]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引——
    comb0 = []
    ts = get_trva_idx0(pts, iks, [0], [0, 1])  # tr搜trva
    comb0 = search_dg1(nm, 0, comb0, ls, ts, '%s/%s_V1_tr_trva.txt' % (d3, tx), 8)
    # comb0 = [0, 4, 5, 7, 9, 15, 16, 18]  # tr-va
    # comb0 = [3, 4, 5, 8, 10, 16, 17, 19]
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜va
    comb0 = search_dg1(nm, 1, comb0, ls, ts, '%s/%s_V2_tr_va.txt' % (d3, tx), 5)
    ts = get_trva_idx0(pts, iks, [0, 1], [0, 1])  # trva搜trva
    comb0 = search_dg1(nm, 2, comb0, ls, ts, '%s/%s_V3_trva_trva.txt' % (d3, tx), 6)
    return d2


def search_diag1(d0, d1, d2, lbs, nm, iks):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/utility_rks.txt' % (d0, lbs)))
    l0, l1, l2 = [], [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引——
    ts = [[], [], [], []]
    for i in range(len(iks[0])):
        ts[0] += pts[iks[0][i]][0]
        ts[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ts[2] += pts[iks[1][i]][0]
        ts[3] += pts[iks[1][i]][1]
    ts[2] = ts[0] + ts[2]  # tr阈值+tr/va最高
    ts[3] = ts[1] + ts[3]
    comb0 = []
    # comb0 = [1, 3, 5, 7, 8, 9, 16, 18, 19]
    # comb0 = [1, 3, 5, 7, 8, 9, 16, 18, 19, 20, 39, 40, 45]
    # comb0 = [1, 3, 5, 7, 8, 9, 16, 18, 19, 20, 24, 36, 44, 46]
    comb0 = [0, 1, 3, 4, 5, 10, 15, 17, 18, 22, 35, 37, 47, 48]
    ls = [l0, l1, l2]
    i = 2
    # for i in range(len(nm)):
    f1 = '%s/V%d_combs_tr-va3.txt'% (d3, i+1)
    # f1 = '%s/V%d_combs_tr_te.txt'% (d3, i+1)
    # f1 = '%s/V%d_combs.txt'% (d3, i+1)
    comb0 = search_dg1(nm, i, comb0, ls, ts, f1)  # tr阈值+te最高
    ts = [[], [], [], []]
    for i in range(len(iks[0])):
        ts[0] += pts[iks[0][i]][0]
        ts[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ts[2] += pts[iks[1][i]][0]
        ts[3] += pts[iks[1][i]][1]
    # comb0 = [1, 2, 5, 6, 10, 13, 14, 17, 18]
    i = 1
    f1 = '%s/V%d_combs_tr_va1.txt'% (d3, i+1)
    # comb0 = search_dg1(nm, i, comb0, ls, ts, f1)  # tr阈值+te最高
    ts = [[], [], [], []]
    for i in range(len(iks[0])):
        ts[0] += pts[iks[0][i]][0]
        ts[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ts[2] += pts[iks[1][i]][0]
        ts[3] += pts[iks[1][i]][1]
    ts[0] = ts[0] + ts[2]  # tr阈值+tr/va最高
    ts[1] = ts[1] + ts[3]
    # # comb0 = [1, 2, 5, 6, 10, 13, 14, 17, 18, 38, 39, 40]  # 1
    # # comb0 = [1, 2, 5, 6, 10, 13, 14, 17, 18, 38, 39, 42]  # 2
    # # comb0 = [1, 2, 5, 6, 10, 13, 14, 17, 18, 24, 30, 38, 39, 40]  # 3
    # # comb0 = [1, 2, 5, 6, 10, 13, 14, 17, 18, 28, 38, 39, 40, 41]  # 4
    # # comb0 = [1, 2, 5, 6, 10, 13, 14, 17, 18, 24, 30, 32, 38, 39, 40]  # 11
    # # comb0 = [1, 2, 5, 6, 10, 13, 14, 17, 18, 24, 30, 32, 38, 39, 42]  # 12
    # comb0 = [0, 1, 3, 4, 5, 10, 15, 17, 18, 21, 29, 35, 39, 44, 45]  # 13
    # i = 2
    # f1 = '%s/V%d_combs_tr-va2.txt'% (d3, i+1)  # 第三轮搜索，4支线——
    # comb0 = search_dg3(nm, i, comb0, ls, ts, f1)  # tr阈值+tr最高
    return d2


def revi_models7(d0, d1, d2, d3, d4, lbs, iks):
    '''d0模型, d1信息, d2诊断, d3搜索, d4审查, lbs版本'''
    mtnms = get_item(get_lines(('%s/%s/mts0.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/rks0.txt' % (d0, lbs)))
    rkl1 = txt_to_list(('%s/%s/rks1.txt' % (d0, lbs)))
    l00, l01, l0, l1 = [], [], [], []
    for i in range(len(mtnms)):
        l00.append(txt_to_list('%s/%s/0/%s.txt' % (d2, lbs, mtnms[i])))
        l01.append(txt_to_list('%s/%s/1/%s.txt' % (d2, lbs, mtnms[i])))
        l0.append([max(rm_element(rkl0[i][1], 100)), min(rkl0[i][1])])
        l1.append([max(rm_element(rkl1[i][1], 100)), min(rkl1[i][1])])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    ids0, ids1, ids2 = [], [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    for i in range(len(pts[iks[1][0]])):
        ids2 += pts[iks[1][0]][i]
    ls = [l00, l01, l0, l1]
    ids = [ids0, ids1, ids2]
    rks = [rkl0, rkl1]
    combs = txt_to_list('%s/%s/combs_all.txt' % (d3, lbs))
    strs = ''
    for i in range(len(combs)):
        strs = revi_mode4(combs[i][1], ls, ids, rks, strs)
    new_folder('%s/%s' % (d4, lbs))
    write_str(strs, '%s/%s/search_results.txt' % (d4, lbs))
    return d3


def search_diag4(d0, d1, d2, lbs, nm, iks):
    '''指标搜索: d0模型, d1信息, d2诊断, d3搜索, lbs版本'''
    mtnms = get_item(get_lines(('%s/%s/mts0.txt' % (d0, lbs))), 0, [])
    rkl0 = txt_to_list(('%s/%s/rks0.txt' % (d0, lbs)))
    rkl1 = txt_to_list(('%s/%s/rks1.txt' % (d0, lbs)))
    l00, l01, l0, l1 = [], [], [], []
    for i in range(len(mtnms)):
        l00.append(txt_to_list('%s/%s/0/%s.txt' % (d2, lbs, mtnms[i])))
        l01.append(txt_to_list('%s/%s/1/%s.txt' % (d2, lbs, mtnms[i])))
        l0.append([max(rm_element(rkl0[i][1], 100)), min(rkl0[i][1])])
        l1.append([max(rm_element(rkl1[i][1], 100)), min(rkl1[i][1])])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    ids0, ids1, ids2 = [], [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    for i in range(len(pts[iks[1][0]])):
        ids2 += pts[iks[1][0]][i]
    print(len(ids0), len(ids1), len(ids2))
    # comb0 = []
    # ls = [l00, l01, l0, l1]
    # ids = [ids0, ids1, ids2]
    # rks = [rkl0, rkl1]
    # # i = 1
    # d4 = '%s/%s' % (d3, lbs)
    # new_folder(d4)
    # for i in range(len(nm)):
    #     f1 = '%s/V%d_combs_tr-te1.txt'% (d4, i+1)
    #     comb0 = search_dg2(nm, i, comb0, ls, ids, rks, f1)
    return d2


def comb_results3(d0, d1, d2, lbs):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    f0 = '%s/%s/V31_search_results.txt' % (d2, lbs)
    combs = txt_to_list(f0)
    comb0 = combs[0][1]
    # 开始
    nm = [0, 18, 25, 31, 24]
    rk0 = txt_to_list('%s/%s/rks_sort.txt' % (d0, lbs))
    mtss = metrics_cls0(d0, lbs, nm)  # 指标分类
    n1, n2 = nm[4], sum(nm[:4])
    l0 = []
    cnt0, dspy, p1 = 0, 1000, 35
    idsp1, idsp2 = pts[0], pts[1]
    idqf = [pts[2][0]+pts[3][0], pts[2][1]+pts[3][1]]
    acc1, acc2 = len(idsp1[0]+idsp1[1])*0.7, len(idsp2[0]+idsp2[1])*0.8, 
    acc3 = len(idqf[0]+idqf[1])  # *0.7
    acc0, rslis = 0, []
    for i2 in range(4):
        for j2 in range(len(mtss[i2])):
            l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtss[i2][j2])))
    l0 = [[l0[i2][j2] for i2 in range(len(l0))] for j2 in range(len(l0[0]))]
    rk1, rk2 = [max(rk0[i1][1]) for i1 in range(len(rk0))], [min(rk0[i1][1]) for i1 in range(len(rk0))]
    cnt = 0
    for i in range(5, 6):
        cnt += scipy.special.comb(n1, i)
    print('count: %d' % cnt)
    t1 = time.time()
    for i1 in range(5, 6):
        print('---------start combinations(%d, %d)' % (n1, i1))
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i1))).tolist()
        for comb in combs:
            comb = comb0+[comb[i2]+n2 for i2 in range(len(comb))]
            cnt0 += 1
            if cnt0 % dspy == 0:
                t2 = time.time()
                sec = int((t2-t1) * (cnt-cnt0) / dspy)
                dspy0 = 'start: %d, left: %d, end_time: ' % (cnt0, cnt-cnt0)
                hr, mn, sc = sec//3600, (sec%3600)//60, sec%60
                if hr:
                    dspy0 += '%dh' % hr
                if mn:
                    dspy0 += '%dmin' % mn
                if sc:
                    dspy0 += '%ds' % sc
                print(dspy0)
                t1 = t2
            l1, l2 = [rk1[i2] for i2 in comb], [rk2[i2] for i2 in comb]
            n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)
            l00 = [[l0[i2][j2] for j2 in comb] for i2 in range(len(l0))]
            l00 = cal_per(l00, n01, n00)
            acc = cal_acc(l00, p1, idqf) / acc3
            if acc > acc0:
                if cal_acc(l00, p1, idsp1) <= acc1:
                    continue
                if cal_acc(l00, p1, idsp2) <= acc2:
                    continue
                i3 = 0
                for i3 in range(len(rslis)):
                    if acc > rslis[i3][0]:
                        break
                rslis.insert(i3, [acc, comb])
                if len(rslis) > 3:
                    rslis.pop()
                print('get one!\n', rslis)
                acc0 = rslis[-1][0]
    f1 = '%s/%s/V41_search_results.txt' % (d2, lbs)
    strs = '\n'.join([str(rslis[i1]) for i1 in range(len(rslis))])
    write_str(strs, f1)
    return d2


def comb_results1(d0, d1, d2, lbs):
    '''指标搜索: d0信息, d1诊断, d2搜索, lbs版本'''
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引
    nm = [0, 18, 25, 31, 24]
    rk0 = txt_to_list('%s/%s/rks_sort.txt' % (d0, lbs))
    mtss = metrics_cls0(d0, lbs, nm)  # 指标分类
    mts0 = mtss[3]
    n1, n2 = len(mts0), 74
    comb1 = [0, 2, 4, 5, 6, 7, 8, 9, 13, 14, 15, 17]
    comb2 = comb1 + [22, 23, 27, 28, 29, 30, 36]
    comb3 = comb2 + [46, 56, 57, 63, 67]
    ids0, ids1 = pts[0], pts[1]
    l0 = []
    for i2 in range(4):
        for j2 in range(len(mtss[i2])):
            l0.append(txt_to_list('%s/%s/%s.txt' % (d1, lbs, mtss[i2][j2])))
    l0 = [[l0[i2][j2] for i2 in range(len(l0))] for j2 in range(len(l0[0]))]  # *98
    ls0 = [[l0[i2] for i2 in ids0[0]], [l0[i2] for i2 in ids0[1]], [l0[i2] for i2 in ids1[0]], [l0[i2] for i2 in ids1[1]]]
    cnt0, d1 = 0, 1000
    # f1 = '%s/%s/V2/search_results0.txt' % (d2, lbs)
    f1 = '%s/%s/V4/search_results.txt' % (d2, lbs)
    f2 = '%s/%s/V4/search_comb.txt' % (d2, lbs)
    # rts = 'idx, tr, te'
    # cbs = 'comb'
    p1 = 45  # 设定阈值
    i1s = list(np.arange(5, 6))
    lisss0 = [rk0, ls0, p1]
    rts, cbs = [], []
    for i1 in i1s:
        combs = np.array(list(itertools.combinations(list(np.arange(n1)), i1))).tolist()
        combs = [comb3+[n2+combs[j1][k1] for k1 in range(len(combs[j1]))] for j1 in range(len(combs))]
        cmrtss = []
        for comb in combs:
            cnt0 += 1
            if cnt0 % d1 == 0:
                print(cnt0)
            cmrts = comb_resu2(comb, lisss0)
            cmrtss.append(cmrts)
        # cmrtss = Parallel(n_jobs=-1)(delayed(comb_resu2)(comb, lisss0) for comb in combs)
        cmrts0 = [cmrtss[j1] for j1 in range(len(cmrtss)) if cmrtss[j1] != []]
        if len(cmrts0) > 0:
            cbs += [cmrts0[j1][0] for j1 in range(len(cmrts0))]
            rts += [cmrts0[j1][1] for j1 in range(len(cmrts0))]
    save_txt(rts, 'list', f1)
    save_txt(cbs, 'list', f2)
    return d2


def extract_metrics0(d0, d1, d2):
    '''整合全部指标、ids、提取space/time单个指标: d0原始指标, d1信息, d2指标'''
    # 1.从原始表格提取time/space参数
    strs1 = 'id, rtsgnum, rtsgnum2, posi_R, posi_Q, posi_T, posi_area_tt, posi_area_qrs, zeroqrs, zeroR, zeroT, zeroRTrot, zeroRTrot1'
    mtsp = extract_metr1('space', 'tr1', 'te1', d0, d1, d2, strs1)
    strs2 = 'id, qsitv, htrt, pnum, tpnrt1, tpnrt2, rtrt, rtrt3, tnirt, tnirt1, qrsttrt, stsl, stsl1, ttflt, ttflt1, ttstpp, ttstpn, ttstprt, strs, areaqj, tsm, tsm1, tmdf, tmdf1, stfs'
    mttm = extract_metr1('time', 'tr2', 'te2', d0, d1, d2, strs2)
    # 2.从3个标签文件夹整合数据
    mtmpraw = extract_metr2('mfm_pcdm_all_raw', d0, d1)
    mtmp = extract_metr3('mfm_pcdm_all', 'space_all.txt', d1)
    return d1


def extract_metrics1(d0, d1):
    '''标签id, spect/qfr/imr'''
    tx1 = ['data_all_raw', 'train', 'test']
    tx2 = [['spect', 'qfr', 'imr'], ['spect', 'qfr', 'imr', 'qfr_only', 'repeat'], ['spect', 'qfr', 'imr']]
    for i1 in range(len(tx1)):
        f1 = '%s/%s.xlsx' % (d0, tx1[i1])
        for j1 in range(len(tx2[i1])):
            d2 = extract_metr4(f1, tx2[i1][j1], '%s/%s' % (d0, tx1[i1]))
    f2ls = get_lines('%s/ids.txt' % d1)
    f2ls = get_item(f2ls, 0, [])  # 提取全部id
    idxlis = extract_metr5(d0, tx2[0], tx1, f2ls)
    idxf = '%s/idx.txt' % d1
    f3 = save_txt(idxlis, 'list', idxf)
    return idxf


def extract_metrics2(d0, d1):
    '''提取mfm/pcdm单个指标'''
    d2 = '%s/mfm_pcdm_all' % d0
    for d3 in os.listdir(d2):
        for f4 in os.listdir('%s/%s' % (d2, d3)):
            if 'metrics' not in f4:
                continue
            mtlns = get_lines('%s/%s/%s' % (d2, d3, f4))
            mtstrs = get_split(mtlns[0])
            for i1 in range(1, len(mtstrs)):
                mtf = '%s/%s_%s.txt' % (d1, d3[9:], mtstrs[i1])
                mts = get_item(mtlns[1:], i1, [])
                mts = [str(mts[i2]) for i2 in range(len(mts))]
                write_str('\n'.join(mts), mtf)
    return d1


def metrics_intp_post(ysb, rdmef, ctrds, root_path):
    '''插值后指标计算(202310定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # tardir = '%s/jys_folder' % root_path
    unzip_file(ysb, tardir)
    mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    mtsdir = '%s/metrics_intp_post' % tardir
    new_folder(mtsdir)
    shutil.copyfile(rdmef, '%s/readme_intp_post.txt' % mtsdir)
    for item in os.listdir(mcgdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (mcgdir, item)
    ids = get_idstms(biao1, 'Sheet1', mode='id')
    mflis, mslis, dflis, dslis = def_mts_mfm(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        print(timelis0)
        mtsall = get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt)
        diagsall = get_rks_prop_intp_post(mtsall)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
        dslis = stat_mtstrs_intp_pre(diagsall, dslis, ids[i11])
    write_strlis(mslis, mflis)
    write_strlis(dslis, dflis)
    shutil.rmtree(mcgdir)
    return mtsdir


# def metrics_intp_pre(ysb, rdmef, ctrds, root_path):
#     '''插值前指标计算(202308定版), 输出结果文档20240125原始代码'''
#     ctrd_qr, ctrd_rs, ctrd_qrs, ctrd_tt = ctrds
#     Zqr = extractmt(ctrd_qr)
#     Zrs = extractmt(ctrd_rs)
#     Zqrs = extractmt(ctrd_qrs)
#     Ztt = extractmt(ctrd_tt)
#     now = datetime.datetime.now()
#     tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
#     # tardir = '%s/jys_folder' % root_path
#     unzip_file(ysb, tardir)
#     mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
#     mtsdir = '%s/metrics_intp_pre' % tardir
#     new_folder(mtsdir)
#     shutil.copyfile(rdmef, '%s/readme_intp_pre.txt' % mtsdir)
#     for item in os.listdir(mcgdir):
#         if item[-4:] == 'xlsx':
#             biao1 = '%s/%s' % (mcgdir, item)
#     ids, timelis = get_idstms(biao1, 'Sheet1')
#     metricfiles, metricstrs = def_mts_intp_pre(mtsdir)
#     for i11 in range(len(ids)):
#         print('start:', i11+1, 'left:', len(ids)-1-i11)
#         mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
#         mcglines = get_lines(mfm_file)
#         Qp, Te = timelis[i11][0], timelis[i11][5]
#         matrixes, mf = cv_interpolate(mfm_file, Qp, min(Te, len(mcglines)-10))
#         timelis[i11] = change_QS(timelis[i11], matrixes, mf)
#         mtsall = get_mfm_mts_intp_pre(timelis[i11], matrixes, mcglines, Zqr, Zrs, Zqrs, Ztt)
#         metricstrs = stat_mtstrs_intp_pre(mtsall, metricstrs, ids[i11])
#     write_strlis(metricstrs, metricfiles)
#     shutil.rmtree(mcgdir)
#     return mtsdir


def metrics_mfm_pcdm(ysb, rdmef, rdmef1, ctrds, ctrds1, root_path):
    '''等磁图和电流密度图指标计算(202402定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    ctrd_qr1, ctrd_rs1, ctrd_tt1 = ctrds1
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    Zqr1 = extractmt(ctrd_qr1)
    Zrs1 = extractmt(ctrd_rs1)
    Ztt1 = extractmt(ctrd_tt1)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # tardir = '%s/jys_folder' % root_path
    unzip_file(ysb, tardir)
    mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    mtsdir = '%s/metrics_mfm_pcdm' % tardir
    new_folder(mtsdir)
    shutil.copyfile(rdmef, '%s/readme_mfm.txt' % mtsdir)
    shutil.copyfile(rdmef1, '%s/readme_pcdm.txt' % mtsdir)
    for item in os.listdir(mcgdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (mcgdir, item)
    ids = get_idstms(biao1, 'Sheet1', mode='id')
    mflis, mslis, dflis, dslis = def_mts_mfm(mtsdir)
    mflis1, mslis1 = def_mts_pcdm(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        # print(mfm_file)
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        # mfm
        mtsall = get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt)
        diagsall = get_rks_prop_intp_post(mtsall)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
        dslis = stat_mtstrs_intp_pre(diagsall, dslis, ids[i11])
        # pcdm
        dfs = pcdm_default()
        mtsall1 = get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1)
        mslis1 = stat_mtstrs_intp_pre(mtsall1, mslis1, ids[i11])
    write_strlis(mslis, mflis)
    write_strlis(dslis, dflis)
    write_strlis(mslis1, mflis1)
    shutil.rmtree(mcgdir)
    return mtsdir


def metrics_mfm_pcdm725(mtsdir, rgt1, ids, mcgdir, ctrds, ctrds1):
    '''等磁图和电流密度图指标计算(202402定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    ctrd_qr1, ctrd_rs1, ctrd_tt1 = ctrds1
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    Zqr1 = extractmt(ctrd_qr1)
    Zrs1 = extractmt(ctrd_rs1)
    Ztt1 = extractmt(ctrd_tt1)
    mflis, mslis, dflis, dslis = def_mts_mfm(mtsdir)
    mflis1, mslis1 = def_mts_pcdm(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+rgt1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        # print(mfm_file)
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        # mfm
        mtsall = get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt)
        diagsall = get_rks_prop_intp_post(mtsall)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
        dslis = stat_mtstrs_intp_pre(diagsall, dslis, ids[i11])
        # pcdm
        dfs = pcdm_default()
        mtsall1 = get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1)
        mslis1 = stat_mtstrs_intp_pre(mtsall1, mslis1, ids[i11])
    write_strlis(mslis, mflis)
    write_strlis(dslis, dflis)
    write_strlis(mslis1, mflis1)
    # shutil.rmtree(mcgdir)
    return mtsdir


def metrics_pcdm(ysb, rdmef, ctrds, root_path):
    '''等磁图和电流密度图指标计算(202402定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # tardir = '%s/jys_folder' % root_path
    unzip_file(ysb, tardir)
    mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    mtsdir = '%s/metrics_pcdm' % tardir
    new_folder(mtsdir)
    shutil.copyfile(rdmef, '%s/readme_pcdm.txt' % mtsdir)
    for item in os.listdir(mcgdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (mcgdir, item)
    ids = get_idstms(biao1, 'Sheet1', mode='id')
    mflis, mslis = def_mts_pcdm(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        # print(mfm_file)
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        dfs = pcdm_default()
        mtsall = get_pcdm_mts(timelis0, matrixes, dfs, Zqr, Zrs, Ztt)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
    write_strlis(mslis, mflis)
    shutil.rmtree(mcgdir)
    return mtsdir


def data_split0(d1):
    '''spect_qfr_tr数据分割/保存索引: d1信息, 373阴性取75, 244阳性取49'''
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    tr0, tr1 = pts[0][0] + pts[2][0], pts[0][1] + pts[2][1]
    va0, va1 = random.sample(tr0, 75), random.sample(tr1, 49)
    strs = '\n%s\n%s' % (str([tr0, tr1]), str([va0, va1]))
    write_str(strs, '%s/idx.txt' % d1, 'a')
    return '%s/idx.txt' % d1


def add_ids2(d1):
    '''初步分级、效用参数: d1信息'''
    # 构建model-1和model-0的数据分割
    pts = txt_to_list('%s/idx0.txt' % d1)[0]  # 标签索引
    row0 = [[10, 14, 15], [8, 9, 11, 12, 13]]
    row1 = [[0, 1, 3], [2, 4, 5, 6, 7]]
    nms0 = [[241, 319], [60, 80], [75, 100]]
    nms1 = [[265, 87], [66, 22], [83, 27]]
    pts1 = []
    ids = [[], []]
    for i in range(2):  # 0/1label
        for j in range(len(row0[i])):
            ids[i] += pts[row0[i][j]]
    for i in range(3):  # tr/va/te
        print(len(ids[0]))
        print(len(ids[1]))
        rdl0 = random.sample(ids[0], nms0[i][0])
        rdl1 = random.sample(ids[1], nms0[i][1])
        pts1.append([rdl0, rdl1])
        ids[0] = [ids[0][j] for j in range(len(ids[0])) if ids[0][j] not in rdl0]
        ids[1] = [ids[1][j] for j in range(len(ids[1])) if ids[1][j] not in rdl1]
    ids = [[], []]
    for i in range(2):  # 0/1label
        for j in range(len(row1[i])):
            ids[i] += pts[row1[i][j]]
    for i in range(3):  # tr/va/te
        print(len(ids[0]))
        print(len(ids[1]))
        rdl0 = random.sample(ids[0], nms1[i][0])
        rdl1 = random.sample(ids[1], nms1[i][1])
        pts1.append([rdl0, rdl1])
        ids[0] = [ids[0][j] for j in range(len(ids[0])) if ids[0][j] not in rdl0]
        ids[1] = [ids[1][j] for j in range(len(ids[1])) if ids[1][j] not in rdl1]
    pts1 = [str(pts1[i]) for i in range(len(pts1))]
    write_str('\n'.join(pts1), '%s/idx1.txt' % d1)
    return d1


def analysis_metrics4(d0, d1, d2, lbs, iks):
    '''初步分级、效用参数: d0分析, d1信息, d2指标, lbs版本, pts索引'''
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    print(len(mtnms))
    pts = txt_to_list('%s/idx1.txt' % d1)  # 标签索引
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    d3 = '%s/%s' % (d0, lbs)
    new_folder(d3)
    f1, f2 = '%s/ranks0.txt' % d3, '%s/utility_all.txt' % d3
    strs1, strs2 = [], []
    # i = 119
    for i in range(len(mtnms)):
        item = mtnms[i]
        print('start:', i+1, item, 'end:', len(mtnms)-i-1)
        lis0 = txt_to_list('%s/%s.txt' % (d2, item))
        mtlis = []
        for ids2 in [ids0, ids1]:
            for j in range(2):
                mtlis.append([lis0[j2] for j2 in ids2[j]])
        rlis, xys2 = make_ranks1(mtlis[0], mtlis[1])  # 自动化分级
        strs1.append(str([rlis, xys2]))
        l1, l2 = [], []
        rkme0 = [rlis, xys2]
        for j in range(len(mtlis[0])):
            mt0 = mtlis[0][j]
            l1.append(diag_res0(mt0, rkme0))
        for j in range(len(mtlis[1])):
            mt0 = mtlis[1][j]
            l2.append(diag_res0(mt0, rkme0))
        auc, acc, f1sc = cal_utility1(l1, l2)
        strs2.append(str([auc, acc, f1sc]))
    write_str('\n'.join(strs1), f1)
    write_str('\n'.join(strs2), f2)
    # 排序
    f3, f4 = '%s/utility_sort.txt' % d3, '%s/rank1.txt' % d3
    f5 = '%s/utility_idx.txt' % d3
    lis1, lis2 = txt_to_list(f2), txt_to_list(f1)
    data = np.array(lis1)
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    lis2 = [lis2[i] for i in list(idex)]
    save_txt(lis1, 'list', f3)
    save_txt(lis2, 'list', f4)
    write_str(str(list(idex)), f5)
    # 筛选 80+41
    n1 = 80
    mtnms0 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    mtnms1 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[1])
    rkls0 = get_lines1('%s/%s/rank1.txt' % (d0, lbs))
    mtidx = txt_to_list('%s/%s/utility_idx.txt' % (d0, lbs))[0]
    mtnms = [mtnms0[mtidx[i]] for i in range(n1)]  # 取前80个指标
    rkls = rkls0[:n1]
    mtnms1 = [mtnms1[i] for i in range(len(mtnms1)) if mtnms1[i] not in mtnms]
    mtnms += mtnms1
    new_folder('%s/%s' % (d1, lbs))
    write_str('\n'.join(mtnms), '%s/%s/mts.txt' % (d1, lbs))  # 写入指标
    for i in range(len(mtnms1)):
        mt0 = mtnms1[i]
        for j in range(len(mtnms0)):
            if mtnms0[j] == mt0:
                ik = j
                break
        for j in range(len(mtidx)):
            if mtidx[j] == ik:
                rkls.append(rkls0[j])
                break
    write_str('\n'.join(rkls), '%s/%s/rks0.txt' % (d1, lbs))  # 写入指标
    return d1


def cmpr_analy0(d0, f0, d1):
    '''cmpr标签整理: mess_path, f0, data_path
    ids, label_raw3s, clinical, label, times
    仿真数据迁移: squid2, opm, error
    1763=648+1115=0.632'''
    # # 1.写入标签-一次——
    # data = pd.read_excel(f0, sheet_name='汇总表格20240820').values
    # vs = []
    # for i in [1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13]:
    #     vs.append(get_xlsx_value(data, i))
    # ids = vs[0]
    # write_str('\n'.join(ids), '%s/ids.txt' % d0)
    # ls = [str(vs[4][i]) for i in range(len(vs[4]))]
    # write_str('\n'.join(ls), '%s/clinical.txt' % d0)
    # ls = [str(vs[5][i]) for i in range(len(vs[5]))]
    # write_str('\n'.join(ls), '%s/label.txt' % d0)
    # idx0 = [i for i in range(len(vs[5])) if vs[5][i]=='0']
    # idx1 = [i for i in range(len(vs[5])) if vs[5][i]=='1']
    # write_str(str([idx0,idx1]), '%s/idx1763.txt' % d0)
    # ls = [str(vs[1][i])+str(vs[2][i])+str(vs[3][i]) for i in range(len(vs[1]))]
    # write_str('\n'.join(ls), '%s/label_raw.txt' % d0)
    # ls = [[int(vs[i][j]) for i in [6,7,8,9,10,11]] for j in range(len(vs[6]))]
    # save_txt(ls, 'list', '%s/times.txt' % d0)
    # # 1526索引
    # data = pd.read_excel(f0, sheet_name='重整一致性数1526例20240826').values
    # ids = get_xlsx_value(data, 1)
    # l0 = get_xlsx_value(data, 6)
    # ids0 = [ids[i] for i in range(len(ids)) if l0[i]=='0']
    # ids1 = [ids[i] for i in range(len(ids)) if l0[i]=='1']
    # ids = get_lines1('%s/ids.txt' % d0)
    # l1 = [get_idxs(ids,ids0,[]), get_idxs(ids,ids1,[])]
    # write_str(str(l1), '%s/idx1526.txt' % d0)
    # # 2.仿真数据迁移-一次——
    # cnt = 0
    # for item in os.listdir('%s/结果' % d1):
    #     cnt += 1
    #     print(cnt)
    #     f1 = '%s/结果/%s/b_squid_sim.txt' % (d1, item)
    #     f2 = '%s/squid2/%s.txt' % (d1, item)
    #     l1 = get_lines1(f1)
    #     l2 = [get_item(l1, i, [], jd=6) for i in range(37)]
    #     l3 = ['\t'.join([format(l2[i][j], '.6f') for i in range(len(l2))]) for j in range(len(l2[0]))]
    #     write_str('\n'.join(l3), f2)
    #     f1 = '%s/结果/%s/b_opm_sim.txt' % (d1, item)
    #     f2 = '%s/opm/%s.txt' % (d1, item)
    #     l1 = get_lines1(f1)
    #     l2 = [get_item(l1, i, [], jd=6) for i in range(37)]
    #     l3 = ['\t'.join([format(l2[i][j], '.6f') for i in range(len(l2))]) for j in range(len(l2[0]))]
    #     write_str('\n'.join(l3), f2)
    #     f1 = '%s/结果/%s/error.txt' % (d1, item)
    #     f2 = '%s/error/%s.txt' % (d1, item)
    #     l1 = get_lines1(f1)
    #     l2 = [get_item(l1, i, [], jd=10) for i in range(2)]
    #     l3 = [[format(l2[i][j], '.10f') for i in range(len(l2))] for j in range(len(l2[0]))]
    #     save_txt(l3, 'list', f2)
    # # 3.时刻点计算13——
    # for tx in ['squid2', 'opm']:
    #     ids = get_lines1('%s/ids.txt' % d0)
    #     tms = [gettime('%s/%s/%s.txt' % (d1, tx, ids[i]))[:-1] for i in range(len(ids))]
    #     save_txt(tms, 'list', '%s/%s_times.txt' % (d0, tx))
    
    # 3*.时刻点优化后计算13——
    # for tx in ['squid2', 'opm']:
    for tx in ['opm']:
        ids = get_lines1('%s/ids.txt' % d0)
        tms = [gettime('%s/%s/%s.txt' % (d1, tx, ids[i]))[:-1] for i in range(len(ids))]
        save_txt(tms, 'list', '%s/%s_times1.txt' % (d0, tx))
    return d0


def cmpr_getmts0(d0, d1, d2):
    '''squid2/opm参数生成1763: mess_path, metr_path, data_path'''
    # # 0.准备bash脚本-1763/30=59
    # ids = get_lines1('%s/ids.txt' % d0)
    # n0 = 30
    # l1 = []
    # for i in range(math.ceil(len(ids)/n0)):
    #     l1.append('python comprehensive.py %d %d > logs/output%d.log &' % (n0, i+1, i+1))
    # l1.append('wait\npython comprehensive.py %d 0' % n0)
    # write_str('\n'.join(l1), './now0.sh')
    # 1.分段生成/汇总
    # tx = 'squid2'
    tx = 'opm'
    ids = get_lines1('%s/ids.txt' % d0)
    tms = txt_to_list('%s/times.txt' % d0)
    i1, i2 = int(sys.argv[1]), int(sys.argv[2])  # 数量, 序号
    if i2 > 0:
        k1, k2 = max(0, (i2-1)*i1), min(len(ids), i2*i1)  # 初始结束索引
        d3 = '%s/%s' % (d2, tx)
        d4 = '%s/%s_1' % (d1, tx)
        d5 = '%s/%d' % (d4, k2)
        new_folder([d4, d5])
        d00 = get_metr0(d3, ids[k1:k2], d5, d0, tms[k1:k2])  # 生成参数
    else:
        d4 = '%s/%s_1' % (d1, tx)
        d5 = '%s/%s' % (d1, tx)
        new_folder([d4, d5])
        l1 = os.listdir(d4)
        l1 = [int(l1[i]) for i in range(len(l1))]
        l1.sort()
        for mt in os.listdir('%s/%d' % (d4, i1)):
            l2 = hb_lis0([get_lines1('%s/%d/%s' % (d4,l1[i],mt)) for i in range(len(l1))])
            write_str('\n'.join(l2), '%s/%s' % (d5,mt))
    
    # # **.检查时刻点的时长对应
    # # tx = 'squid1'
    # # tx = 'squid2'
    # tx = 'opm'
    # ids = get_lines1('%s/ids.txt' % d0)
    # tms = txt_to_list('%s/times.txt' % d0)
    # d3 = '%s/%s' % (d2, tx)
    # for i in range(len(ids)):
    #     l1 = get_lines1('%s/%s.txt' % (d3, ids[i]))
    #     if len(l1) == max(tms[i]):
    #         print(i, ids[i], '==')
    #     elif len(l1) < max(tms[i]):
    #         print(i, ids[i], '<')
    return d1


def cmpr_get_spimg0(d0, d1, d2):
    '''生成空间波组图: mess_path, anal_path, data_path'''
    # d3 = '%s/space_waves' % d1
    # d3 = '%s/time_waves' % d1
    # d3 = '%s/rms' % d1
    # d3 = '%s/rms_p' % d1
    d3 = '%s/rms_p1' % d1
    # d3 = '%s/avg' % d1
    # d3 = '%s/space_waves1' % d1
    # d3 = '%s/time_waves1' % d1
    # tx = 'squid2'
    tx = 'opm'
    # for tx in ['squid2', 'opm']:
    d4 = '%s/%s' % (d3, tx)
    new_folder([d3, d4])
    ids = get_lines1('%s/ids.txt' % d0)
    tms = txt_to_list('%s/%s_times.txt' % (d0, tx))
    for i in range(len(ids)):
        # if i < 1:
        print('start:', i+1, 'left:', len(ids)-i)
        f0 = '%s/%s/%s.txt' % (d2, tx, ids[i])
        g1 = '%s/%s.png' % (d4, ids[i])
        # ls36 = get_mcg36(f0, 0)  # 36*N
        # cmpr_draw0(ls36, g1)  # 空间
        # # tms = gettime(f0)
        # cmpr_draw1(ls36, tms[i], g1)  # 时间
        l0 = get_rms0(f0)
        cmpr_draw3(l0, tms[i], g1)  # rms
        # l1 = [np.mean([ls36[i][j] for i in range(len(ls36))]) for j in range(len(ls36[0]))]
        # cmpr_draw3(l1, tms[i], g1)  # avg
    return d1


def cmpr_analy1(d0, d1, d2):
    '''TP段基线: mess_path, anal_path, data_path'''
    # # 0.数据准备-一次——
    # ids = get_lines1('%s/ids.txt' % d0)
    # random.shuffle(ids)
    # write_str('\n'.join(ids[:100]), '%s/ids_now.txt' % d0)
    
    # # 1.ls36+avg
    ids = get_lines1('%s/ids_now.txt' % d0)
    idx = get_idxs(get_lines1('%s/ids.txt' % d0), ids, [])
    # for tx in ['squid2', 'opm']:
    #     d3 = '%s/%s_TPbase' % (d1, tx)
    #     new_folder(d3)
    #     l0 = txt_to_list('%s/%s_times.txt' % (d0, tx))
    #     tms = [l0[i] for i in idx]
    #     for i in range(len(ids)):
    #         # if i < 1:
    #         print('start:', i+1, 'left:', len(ids)-i)
    #         f0 = '%s/%s/%s.txt' % (d2, tx, ids[i])
    #         g1 = '%s/%s.png' % (d3, ids[i])
    #         # ls36, l0 = get_mcg36(f0, 0), get_rms0(f0)  # ls36+rms
    #         ls36 = get_mcg36(f0, 0)  # ls36+avg
    #         l0 = [np.mean([ls36[i][j] for i in range(len(ls36))]) for j in range(len(ls36[0]))]
    #         Tr, Ph = tms[i][-1], tms[i][0]
    #         l0 = l0[Tr:]+l0[:Ph]
    #         ls36 = [ls36[i][Tr:]+ls36[i][:Ph] for i in range(len(ls36))]
    #         cmpr_draw2(ls36, l0, g1)
    # # 2.TP基线设计
    # for tx in ['squid2', 'opm']:
    #     # d3 = '%s/%s_TPbase1' % (d1, tx)  # 去除奇异值-演示
    #     # d3 = '%s/%s_TPbase2' % (d1, tx)  # 导出奇异数据-tmwv
    #     d3 = '%s/%s_TPbase3' % (d1, tx)
    #     new_folder(d3)
    #     l0 = txt_to_list('%s/%s_times.txt' % (d0, tx))
    #     tms = [l0[i] for i in idx]
    #     for i in range(len(ids)):
    #         # if i < 1:
    #         # print('start:', i+1, 'left:', len(ids)-i)
    #         f0 = '%s/%s/%s.txt' % (d2, tx, ids[i])
    #         g1 = '%s/%s.png' % (d3, ids[i])
    #         # ls36, l0 = get_mcg36(f0, 0), get_rms0(f0)  # ls36+rms
    #         ls36 = get_mcg36(f0, 0)  # ls36+avg
    #         l0 = [np.mean([ls36[i][j] for i in range(len(ls36))]) for j in range(len(ls36[0]))]
    #         M = max(get_mai0(ls36))
    #         Tr, Ph = tms[i][-1], tms[i][0]
    #         l1, l2 = l0[Tr:], l0[:Ph]
    #         # cmpr_iqr1(l1+l2, g1)  # 去除奇异值-演示
    #         # if l1!=[] and l2!=[]:  # 导出奇异数值
    #         #     m1, m2 = cal_iqr0(l1), cal_iqr0(l2)
    #         #     print(i, round(abs(m1-m2)/M, 3))
    #         # if l1==[] or l2==[]:  # 导出奇异数据-tmwv
    #         #     continue
    #         # m1, m2 = cal_iqr0(l1), cal_iqr0(l2)
    #         # if abs(m1-m2)/M >= 0.03:
    #         #     print(ids[i])
    #         #     shutil.copyfile('%s/time_waves/%s/%s.png' % (d1,tx,ids[i]), g1)
    #         if l1!=[] and l2!=[]:
    #             m1, m2 = cal_iqr0(l1), cal_iqr0(l2)
    #             if abs(m1-m2)/M >= 0.03:
    #                 continue
    #         print('start:', i+1, 'left:', len(ids)-i)
    #         m1 = cal_iqr0(l1+l2)
    #         ls36 = [ls36[i][Tr:]+ls36[i][:Ph] for i in range(len(ls36))]
    #         m2 = cal_iqr0(hb_lis0(ls36))
    #         cmpr_draw4(ls36, m1, m2, g1)
    
    # 3.TP基线结果导出-基线值v/no, 修正mcg, 生成时刻点, 修正时间/空间波组图
    ids = get_lines1('%s/ids.txt' % d0)
    d30 = '%s/space_waves_1' % d1
    d31 = '%s/time_waves_1' % d1
    new_folder([d30, d31])
    for tx in ['squid2', 'opm']:
        tms = txt_to_list('%s/%s_times.txt' % (d0, tx))
        # l00 = []  # 基线修正数值-step1
        # for i in range(len(ids)):
        #     print('start:', i+1, 'left:', len(ids)-i)
        #     f0 = '%s/%s/%s.txt' % (d2, tx, ids[i])
        #     ls36 = get_mcg36(f0, 0)
        #     l0 = [np.mean([ls36[i][j] for i in range(len(ls36))]) for j in range(len(ls36[0]))]
        #     M = max(get_mai0(ls36))
        #     Tr, Ph = tms[i][-1], tms[i][0]
        #     l1, l2 = l0[Tr:], l0[:Ph]
        #     m0 = round(cal_iqr0(l1+l2), 6)
        #     if l1!=[] and l2!=[]:
        #         m1, m2 = cal_iqr0(l1), cal_iqr0(l2)
        #         if abs(m1-m2)/M >= 0.03:
        #             m0 = 'no'
        #     l00.append(m0)
        # save_txt(l00, 'list', '%s/%s_TPbase.txt' % (d0, tx))
        # d3 = '%s/%s_1' % (d2, tx)  # 导出mcg-step2
        # new_folder(d3)
        # l00 = get_lines1('%s/%s_TPbase.txt' % (d0, tx))
        # for i in range(len(ids)):
        #     print('start:', i+1, 'left:', len(ids)-i)
        #     f0 = '%s/%s/%s.txt' % (d2, tx, ids[i])
        #     f1 = '%s/%s.txt' % (d3, ids[i])
        #     if l00[i] == 'no':
        #         shutil.copyfile(f0, f1)
        #         continue
        #     cmpr_cgmcg0(f0, f1, float(l00[i]))
        # tms = [gettime('%s/%s_1/%s.txt' % (d2, tx, ids[i]))[:-1] for i in range(len(ids))]
        # save_txt(tms, 'list', '%s/%s_1_times.txt' % (d0, tx))  # 导出时刻点-step3
        d40 = '%s/%s' % (d30, tx)  # 生成时间/空间波组图-step4
        d41 = '%s/%s' % (d31, tx)
        new_folder([d40, d41])
        tms = txt_to_list('%s/%s_1_times.txt' % (d0, tx))
        for i in range(len(ids)):
            print('start:', i+1, 'left:', len(ids)-i)
            f0 = '%s/%s_1/%s.txt' % (d2, tx, ids[i])
            g10 = '%s/%s.png' % (d40, ids[i])
            g11 = '%s/%s.png' % (d41, ids[i])
            ls36 = get_mcg36(f0, 0)  # 36*N
            # cmpr_draw0(ls36, g10)  # 空间
            cmpr_draw1(ls36, tms[i], g11)  # 时间
    return d1


def cmpr_tms1(d0, d1, d2):
    '''Th/Tr时刻点优化-设计: mess_path, anal_path, data_path; 0929'''
    # 1.准备-文件夹/数据
    d1 = '%s/tms1' % d1
    new_folder(d1)
    d2 = '%s/opm_1' % d2
    ids0 = get_lines1('%s/ids.txt' % d0)
    ids = get_lines1('%s/ids_now.txt' % d0)
    # 2.时刻点计算
    # tms = [gettime('%s/%s.txt' % (d2, ids[i]))[:-1] for i in range(len(ids))]
    # save_txt(tms, 'list', '%s/tms1.txt' % d1)
    d3 = '%s/timews1' % d1
    d4 = '%s/spacews1' % d1
    new_folder([d3, d4])
    tms = txt_to_list('%s/tms1.txt' % d1)
    for i in range(len(ids)):
        print('start:', i+1, 'left:', len(ids)-i)
        item, ts = ids[i], tms[i]
        # if item != 'SY_TT_000347':
        #     continue
        g1 = '%s/%s.png' % (d3, item)
        g2 = '%s/%s.png' % (d4, item)
        ls36 = get_mcg36('%s/%s.txt' % (d2, item), 0)
        cmpr_draw1(ls36, ts, g1)  # 时间
        cmpr_draw0(ls36, g2, [ts[8], ts[12]])  # 空间
    # # 3.时刻点检查
    # tms = txt_to_list('%s/tms1.txt' % d1)
    # for i in range(len(ids)):
    #     ts = tms[i]
    #     # for i in range(len(ts)-1):  # 全部13时刻点不倒置
    #     #     if ts[i+1]-ts[i]<0:
    #     #         print(ts)
    #     # if ts[8]-ts[7]<50:  # Th>=Sr+50
    #     if ts[12]-ts[5]>500:
    #         print(ts, ids[i])
    # ts = [t[12]-t[8] for t in tms]
    # a = ts.copy()
    # a.sort()
    # print(a[:20], '\n', a[-20:])
    return d1


def Cal_stseg0(f0, f1):
    '''ST参数-Qh/Sr/Th时刻计算: 心磁f0, TP基线微调后心磁f1'''
    ts = gettime(f0)[:-1]  # 时刻点计算
    _, _ = cmpr_TPbase1(f0, f1, ts)  # 保存微调后心磁
    th0 = 20  # 前后延长20ms(心率60)
    th1 = 5  # 波峰波谷最小间距
    ths = [0.1, 0.3, 0.13, 0.7, 0.045]
    dhs = [55, 40, 10, 60]
    qhs0, qhs, srs, rss = [], [], [], []
    t0 = round(th0*len(get_lines1(f1))/600)
    ls36 = get_mcg36(f1, 0)
    ts = gettime(f1)  # 新时刻点
    Qh, Sr, Th = ts[3]-t0, ts[7]+t0, ts[8]
    for i0 in range(36):  # 统计信息
        l2 = ls36[i0][Qh:Sr+1]
        M0 = max(max(l2), -min(l2))
        ik0 = [0, len(l2)-1]
        ik, _ = signal.find_peaks(np.array(l2), distance=th1)
        ik1 = [j for j in ik]
        ik, _ = signal.find_peaks(-np.array(l2), distance=th1)
        ik2 = [j for j in ik]
        pt0 = sorted(set(ik0+ik1+ik2))
        l3 = [round(l2[i1]/M0,3) for i1 in range(len(l2))]
        Qh0, QRSs, Sr0, Rs = cmpr_qrspt1(l3, pt0, ths, dhs, ik1, ik2)
        qhs.append(Qh0)
        srs.append(Sr0)
        rss.append(Rs)
        mg = [[l3[ii] for ii in jj] for jj in QRSs]
        if 1.0 in mg[0] or -1.0 in mg[0]:
            qhs0.append(Qh0)
    rss = hb_lis0(rss)
    if len(qhs0)>2:
        qh0 = cmpr_jzd0(qhs0, max(min(rss)-1,0), 0)[-1]  # 综合起止点
    else:
        qh0 = cmpr_jzd0(qhs, max(min(rss)-1,0), 0)[-1]
    t1, t2 = 62+qh0, 109+qh0
    sr0s = cmpr_jzd0(srs, min(max(rss)+1,Sr+1-Qh), 1)
    sr1s = [t00 for t00 in sr0s if t00>=t1 and t00<=t2]
    if sr1s:
        sr0 = sr1s[0]
    else:
        srs1 = [t00 for t00 in srs if t00>=t1 and t00<=t2]
        if srs1:
            sr0 = cmpr_jzd0(srs1, min(max(rss)+1,Sr+1-Qh), 1)[0]
        else:
            sr0 = sr0s[0]
    return [qh0, sr0, Th]


def cmpr_qrsbo1(d0, d1, d20, d3, d31, d40):
    '''QRS复合识别和参数开发-计算-1526例: mess_path, anal_path, data_path, metr_path, diag_path, resu_path'''
    # 1.准备-文件夹/数据
    ids = get_lines1('%s/ids.txt' % d0)
    d1 = '%s/QRS2' % d1
    new_folder(d1)
    # d1 = '%s/QRSspace' % d1  # 生成100例的空间波组图
    # new_folder(d1)
    d1 = '%s/1526_QRS' % d1  # 生成100例的空间波组图
    new_folder(d1)
    mtnms0 = ['space_qrswv%d' % i for i in range(1, 37)]  # 单通道参数
    vls0 = [[] for i in range(1, 37)]
    idx1, idx2 = txt_to_list('%s/idx1526.txt' % d0)[0]  # 索引
    n0, p0 = 0.05, 0.95  # 特异性阈值
    bl = len(idx2)/len(idx1)  # 阴阳均衡比例
    n00 = len(idx1+idx2)*2  # 总数
    lbs = 'model_1526'
    d4 = '%s/%s' % (d0, lbs)  # 信息文件夹
    new_folder(d4)
    d41 = '%s/qrswv' % d4
    new_folder(d41)
    tx = 'opm'
    tpqrs = ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
    # for tx in ['opm', 'squid2']:  # 生成单参数
    d2 = '%s/%s' % (d20, tx)  # 源心磁文件夹
    d21 = '%s/%s_2' % (d20, tx)  # 调整后心磁文件夹
    new_folder(d21)
    # # 2.时刻点计算
    # tms = [gettime('%s/%s.txt' % (d2, ids[i]))[:-1] for i in range(len(ids))]
    # save_txt(tms, 'list', '%s/%s_tms1.txt' % (d1, tx))
    # # 3.TP基线微调
    # tms = txt_to_list('%s/%s_tms1.txt' % (d1, tx))
    # tms2 = []
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item = ids[i]
    #     f0 = '%s/%s.txt' % (d2, item)
    #     f1 = '%s/%s.txt' % (d21, item)
    #     l1, l2 = cmpr_TPbase1(f0, f1, tms[i])  # 保存心磁
    #     tms2.append(gettime(f1)[:-1])
    # save_txt(tms2, 'list', '%s/%s_tms2.txt' % (d1, tx))  # 新时刻点
    # # 4.单参数导出
    # tms = txt_to_list('%s/%s_tms2.txt' % (d1, tx))
    d2 = d21  # 数据集文件夹
    th0 = 20  # 前后延长20ms(心率60)
    th1 = 5  # 波峰波谷最小间距
    ths = [0.1, 0.3, 0.13, 0.7, 0.045]
    dhs = [55, 40, 10, 60]
    qhs0, qhs, srs, rss = [], [], [], []
    # ids = get_lines1('%s/ids_now.txt' % d0)
    ids = [ids[i0] for i0 in idx1+idx2]
    lss = []
    for i in range(len(ids)):
        if i > 0:
            continue
        print('start:', i+1, 'left:', len(ids)-i)
        item = ids[i]
        # ts = tms[i]
        f0 = '%s/%s.txt' % (d2, item)
        t0 = round(th0*len(get_lines1(f0))/600)
        ls36 = get_mcg36(f0, 0)
        ts = gettime(f0)
        Qh, Sr = ts[3]-t0, ts[7]+t0
        l0s = []
        l1s = []
        for i0 in range(36):  # 统计信息
            l2 = ls36[i0][Qh:Sr+1]
            M0 = max(max(l2), -min(l2))
            ik0, ik1, ik2 = cal_bfbg0(l2, th1)
            pt0 = sorted(set(ik0+ik1+ik2))
            l3 = [round(l2[i1]/M0,3) for i1 in range(len(l2))]
            Qh0, QRSs, Sr0, Rs = cmpr_qrspt1(l3, pt0, ths, dhs, ik1, ik2)
            mg = [[l3[ii] for ii in jj] for jj in QRSs]
            l1s.append(mg)
            strs, fg = cmpr_tpqrs0(mg, tpqrs)
            strs += '\n'+str(mg)
            alp = [ii+Qh for ii in hb_lis0(QRSs)]
            l0s.append([strs, fg, alp, [Qh0+Qh, Sr0+Qh]])
            vls0[i0].append(fg+1)
            qhs.append(Qh0)
            srs.append(Sr0)
            rss.append(Rs)
            mg = [[l3[ii] for ii in jj] for jj in QRSs]
            if 1.0 in mg[0] or -1.0 in mg[0]:
                qhs0.append(Qh0)
        # lss.append([item, l1s])
        g1 = '%s/%s.png' % (d1, item)
        cmpr_draw8(ls36, l0s, g1)  # 绘制空间波组图
    # save_txt(lss, 'list', '%s/1526_space_qrswv_values.txt' % d40)
    #         # # 综合起止点Qh/Sr
    #         # rss = hb_lis0(rss)
    #         # if len(qhs0)>2:
    #         #     qh0 = cmpr_jzd0(qhs0, max(min(rss)-1,0), 0)[-1]  # 综合起止点
    #         # else:
    #         #     qh0 = cmpr_jzd0(qhs, max(min(rss)-1,0), 0)[-1]
    #         # t1, t2 = 62+qh0, 109+qh0
    #         # sr0s = cmpr_jzd0(srs, min(max(rss)+1,Sr+1-Qh), 1)
    #         # sr1s = [t00 for t00 in sr0s if t00>=t1 and t00<=t2]
    #         # if sr1s:
    #         #     sr0 = sr1s[0]
    #         # else:
    #         #     srs1 = [t00 for t00 in srs if t00>=t1 and t00<=t2]
    #         #     if srs1:
    #         #         sr0 = cmpr_jzd0(srs1, min(max(rss)+1,Sr+1-Qh), 1)[0]
    #         #     else:
    #         #         sr0 = sr0s[0]
    #     for i0 in range(36):
    #         save_txt(vls0[i0], 'list', '%s/%s/%s.txt' % (d3, tx, mtnms0[i0]))
    # # 5.1特异性参数sqop/分级-1526一致性数据
    # NN = 12  # 单参数类型个数
    # rk0 = list(range(2, NN+1))
    # rks, nss = [], []  # 分级信息/数量分布
    # rts0, rts1 = [], []  # 特异性参数0/1
    # rts2 = []  # 特异性总集
    # for i in range(36):
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d3, 'opm', mtnms0[i]))
    #     l1, l2 = [[l0[i0] for i0 in idx] for idx in [idx1, idx2]]
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d3, 'squid2', mtnms0[i]))
    #     l1 += [l0[i0] for i0 in idx1]  # sqop参数集0/1
    #     l2 += [l0[i0] for i0 in idx2]
    #     ns = [[sum(1 for i0 in l if i0==i1+1) for i1 in range(NN)] for l in [l1,l2]]
    #     ps, m12, n12 = [], 0, 0
    #     for j in range(NN):
    #         n1, n2, p1 = ns[0][j]*bl, ns[1][j], 0
    #         if n1+n2 > 0:
    #             p1 = n2/(n1+n2)
    #             if p1 >= p0:
    #                 n12 += n1+n2
    #             elif p1 <= 1-p0:
    #                 m12 += n1+n2
    #         ps.append(round(100*p1))
    #     nss.append([ns, []])
    #     rks.append([rk0, ps])
    #     if n12:
    #         jg0 = [mtnms0[i], ps, round(n12/n00,3)]
    #         rts2.append(jg0)
    #     if n12/n00 > n0:
    #         rts1.append([mtnms0[i], ps, round(n12/n00,3)])
    #     if m12/n00 > n0:
    #         rts0.append([mtnms0[i], ps, round(m12/n00,3)])
    # write_str('\n'.join(mtnms0), '%s/mts.txt' % d41)
    # save_txt(nss, 'list', '%s/%s_nums0.txt' % (d41, 'sqop'))
    # save_txt(rks, 'list', '%s/%s_rks0.txt' % (d41, 'sqop'))
    # # save_txt(rts0, 'list', '%s/%s_spe0.txt' % (d41, 'sqop'))  # 无
    # save_txt(rts1, 'list', '%s/%s_spe1.txt' % (d41, 'sqop'))
    # save_txt(rts2, 'list', '%s/%s_spe2.txt' % (d41, 'sqop'))
    # # 5.2有效性参数-诊断、效用排序
    # mtnms = get_lines1('%s/mts.txt' % d41)
    # for tx in ['opm', 'squid2']:  # 诊断
    #     rkmes = txt_to_list('%s/%s_rks0.txt' % (d41, 'sqop'))
    #     d00 = diag_resu0('%s/%s' % (d3, tx), mtnms, rkmes, d31, tx)
    # lis1 = []
    # for i in range(len(mtnms)):
    #     item = mtnms[i]
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d31, 'opm', item))
    #     l1, l2 = [[l0[i] for i in idx] for idx in [idx1, idx2]]
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d31, 'squid2', item))
    #     l1 += [l0[i] for i in idx1]
    #     l2 += [l0[i] for i in idx2]
    #     auc, acc, f1sc = cal_utility1(l1, l2)
    #     lis1.append([auc, acc, f1sc])
    # save_txt(lis1, 'list', '%s/%s_uty.txt' % (d41, 'sqop'))
    # l1 = txt_to_list('%s/%s_uty.txt' % (d41, 'sqop'))
    # lis1 = [[i]+l1[i] for i in range(len(l1))]
    # l0 = [lis1[i] for i in range(len(lis1)) if lis1[i][1]+lis1[i][3]>1.2 or lis1[i][2]+lis1[i][3]>1.2]
    # data = np.array(l0)  # Auc+Acc>1.2 or Acc+F1>1.2, 排序Auc>F1>Acc
    # idex = np.lexsort((data[:, 0], -1*data[:, 2], -1*data[:, 3], -1*data[:, 1]))
    # sorted_data = data[idex]
    # lis1 = sorted_data.tolist()
    # l3 = ['%s, %.3f, %.3f, %.3f' % (mtnms[int(l[0])], l[1], l[2], l[3]) for l in lis1]
    # save_txt(l3, 'list', '%s/%s_sort.txt' % (d41, 'sqop'))
    # save_txt([mtnms[int(l[0])] for l in lis1],'list','%s/%s_mts.txt' % (d41,'sqop'))
    # save_txt([rkmes[int(l[0])] for l in lis1],'list','%s/%s_rks.txt' % (d41,'sqop'))
    # 6.分布参数计算/分级/广泛效用&特异性
    rks = txt_to_list('%s/%s_rks0.txt' % (d41, 'sqop'))
    combs=[[1,2,3,4,5,6,7,8,9,10,11,12,15,16,17], [20,23,24,25,26,27,28,29,30,31,32,33,34,35,36], [1,2,3,9,10], [4,5,9,10,11,15,16,17]]
    nm1 = ['neg', 'pos', 'Qr', 'rS']  # 分布参数4
    for i1 in range(len(combs)):
        iks = combs[i1]
        print(i1, iks)
        rkss = [rks[i-1] for i in iks]
        # opm-指标/诊断/综合得分/特异性
        mt1 = [txt_to_list('%s/%s/space_qrswv%d.txt' % (d3, 'opm', i)) for i in iks]
        dg1 = [[diag_res0(m0, rkss[i]) for m0 in mt1[i]] for i in range(len(mt1))]
        dg1 = [[dg1[i][j] for i in range(len(dg1))] for j in range(len(dg1[0]))]
        dg1 = [round(sum([p00/10-5 for p00 in l2]),1) for l2 in dg1]
        save_txt(dg1, 'list', '%s/%s/space_qrswv_%s.txt' % (d3, 'opm', nm1[i1]))
        l10, l11 = [[dg1[i] for i in idx] for idx in [idx1, idx2]]
        # squid2
        mt2 = [txt_to_list('%s/%s/space_qrswv%d.txt' % (d3, 'squid2', i)) for i in iks]
        dg2 = [[diag_res0(m0, rkss[i]) for m0 in mt2[i]] for i in range(len(mt2))]
        dg2 = [[dg2[i][j] for i in range(len(dg2))] for j in range(len(dg2[0]))]
        dg2 = [round(sum([p00/10-5 for p00 in l2]),1) for l2 in dg2]
        save_txt(dg2, 'list', '%s/%s/space_qrswv_%s.txt' % (d3, 'squid2', nm1[i1]))
        l20, l21 = [[dg2[i] for i in idx] for idx in [idx1, idx2]]
        # sqop-合并参数/分级
        al0, al1 = l10+l20, l11+l21
        rs1, rs2, _, _ = ranks_spemt0([], [], al0, al1, '', p0)
        # print('sqop spe', rs1, rs2)
        v1 = min(al0+al1)-1
        xyss1 = make_ranks2(al0, al1, mode=0, v1=v1, lis0=[])
        rts = auto_rks0(xyss1, 'mt1')
        # print('sqop rks', rts)
        # 导出分级、特异性、效用
        v2 = rs1[0][0][2][0]  # >v0为特异-阳性检出率
        n1 = sum(1 for m0 in l10 if m0>=v2)
        n2 = sum(1 for m0 in l11 if m0>=v2)
        # print('opm_spe:', get_round([n2/(n1+n2), (n1+n2)/len(idx1+idx2)], [2, 2]))
        n1 = sum(1 for m0 in l20 if m0>=v2)
        n2 = sum(1 for m0 in l21 if m0>=v2)
        # print('squid2_spe:', get_round([n2/(n1+n2), (n1+n2)/len(idx1+idx2)], [2, 2]))
        n1 = sum(1 for m0 in al0 if m0>=v2)
        n2 = sum(1 for m0 in al1 if m0>=v2)
        # print('sqop_spe:', get_round([n2/(n1+n2), (n1+n2)/len(idx1+idx2)/2], [2, 2]))
        vs = [v0 for v0 in rts[0] if v0 < v2] + [v2]
        rts = [vs, rts[1][:len(vs)]+[round(100*n2/(n1+n2))]]
        # print(rts)
        l10, l11 = [[diag_res0(m0, rts) for m0 in l] for l in [l10, l11]]
        auc, acc, f1sc = cal_utility1(l10, l11)
        # print('opm:', auc, acc, f1sc)
        l10, l11 = [[diag_res0(m0, rts) for m0 in l] for l in [l20, l21]]
        auc, acc, f1sc = cal_utility1(l10, l11)
        # print('squid2:', auc, acc, f1sc)
        l10, l11 = [[diag_res0(m0, rts) for m0 in l] for l in [al0, al1]]
        auc, acc, f1sc = cal_utility1(l10, l11)
        # print('sqop:', auc, acc, f1sc)
    
    # # 导出分布信息
    # l1 = txt_to_list('%s/sqop_rks0.txt' % d41)
    # l2 = txt_to_list('%s/sqop_nums0.txt' % d41)
    # for i in range(6):
    #     l21 = [[] for i0 in range(6)]
    #     for j in range(6):
    #         l3, l4 = l1[i*6+j][1], l2[i*6+j][0][1]
    #         for k in range(12):
    #             if l3[k]>72 and l4[k]>70:
    #                 l21[j].append(str(k+1))
    #     l21 = ['/'.join(l22) for l22 in l21]
    #     # print(' '.join(l21))
    #     print(l21)
    
    # ids = get_lines1('%s/ids_now.txt' % d0)
    # rootdir = '%s/QRS1' % d1
    # # d2 = '%s/opm_2' % d2
    # d22 = '%s/波和起止点1' % rootdir
    # new_folder(d22)
    # l1 = ids
    # for i in range(len(l1)):
    #     item = l1[i]
    #     print('start:', i+1, 'left:', len(l1)-i)
    #     l0 = txt_to_list('%s/%s.txt' % (d22, item))[1:]
    #     l0 = [qrs[1] for qrs in l0]
        
    return d0


def cmpr_tms2(d0, d1, d2):
    '''RP基线/Qh/Sr时刻点优化: mess_path, anal_path, data_path'''
    rootdir = '%s/QRS1' % d1
    d2 = '%s/opm_2' % d2
    ids = get_lines1('%s/ids_now.txt' % d0)
    idx = txt_to_list('%s/idx100.txt' % d0)[0]
    # # tms = [gettime('%s/%s.txt' % (d2, ids[i]))[:-1] for i in range(len(ids))]
    # # save_txt(tms, 'list', '%s/tms1/tms2.txt' % d1)
    # tms = txt_to_list('%s/tms1/tms2.txt' % d1)
    # d11 = '%s/QhSr' % rootdir
    # new_folder([rootdir, d11])
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item, ts = ids[i], tms[i]
    #     g1 = '%s/%s.png' % (d11, item)
    #     ls36 = get_mcg36('%s/%s.txt' % (d2, item), 0)
    #     cmpr_draw0(ls36, g1, [ts[3], ts[7]])  # 空间
    
    # l1 = ['BJ_TT_001853', 'SY_TT_000347', 'SHLY_2023_000686', 'SY_TT_000937', 'SHSY_2023_000656', 'SHLY_2023_000222', 'BJ_TT_001829', 'BJ_TT_001201', 'BJ_TT_001432', 'SHZS_2022_000242', 'SY_TT_000692', 'BJ_TT_000889', 'SHLY_2023_000150', 'BJ_TT_000502']
    # d12 = '%s/时刻点扩张' % rootdir
    # d12 = '%s/候选点' % rootdir
    # new_folder(d12)
    # d22 = '%s/波和起止点' % rootdir
    # d22 = '%s/波和起止点1' % rootdir
    d22 = '%s/波和起止点1_2' % rootdir
    new_folder(d22)
    d23 = '%s/cache' % rootdir
    new_folder(d23)
    th0 = 20  # 前后延长20ms(心率60)
    th1 = 5  # 波峰波谷最小间距
    ths = [0.1, 0.3, 0.13, 0.7, 0.045]
    dhs = [55, 40, 10, 60]
    # l1 = ['SY_TT_000280']
    l1 = ids
    l111 = ['BJ_TT_001201', 'BJ_TT_001256', 'PLAG_2024_000275', 'BJ_TT_001338', 'PLAG_2024_000216', 'SHZS_2023_000091']
    l112 = []
    for i in range(len(l1)):
        item = l1[i]
        if item in l111:
            continue
        # print('start:', i+1, 'left:', len(l1)-i)
        # if item != 'PLAG_2024_000418':
        #     continue
        # if i != 3:
        #     continue
        # print(i+1, item)
        f0 = '%s/%s.txt' % (d2, item)
        f1 = '%s/%s.txt' % (d22[:-2], item)
        ls = txt_to_list(f1)[1:]
        # l112.append(sum(1 for ms0 in ls if len(hb_lis0(ms0[1]))==3))
        # for ms0 in ls:
        #     if len(ms0[1])==3:
        #         strs = ''
        #         for i11 in ms0[1]:
        #             a, b = max(i11), min(i11)
        #             if a>0.7:
        #                 strs += 'P'
        #             elif b>0:
        #                 strs += 'p'
        #             elif b<-0.7:
        #                 strs += 'N'
        #             else:
        #                 strs += 'n'
        #         l112.append(strs)
        n0, n1 = txt_to_list(f1)[0]
        # N0 = len(get_lines1(f0))
        # ln = (n1-n0)*600/N0
        # if int(ln)<40 or int(ln)>90:
        # if int(ln)>=40 and int(ln)<=90:
        if n1-n0<62 and n1-n0>109:
            continue
        # print('start:', i+1, 'left:', len(l1)-i)
        # l112.append((n1-n0)*600/N0)
        t0 = round(th0*len(get_lines1(f0))/600)
        ls36 = get_mcg36(f0, 0)
        ts = gettime(f0)
        Qh, Sr = ts[3]-t0, ts[7]+t0
        # g1 = '%s/%s.png' % (d12, item)  # 空间
        # cmpr_draw0(ls36, g1, [Qh, Sr])
        # d13 = '%s/%s' % (d12, item)
        # new_folder(d13)
        mgs1 = []
        for i0 in range(36):  # 统计信息
            l2 = ls36[i0][Qh:Sr+1]
            M0 = max(max(l2), -min(l2))
            ik0 = [0, len(l2)-1]
            ik, _ = signal.find_peaks(np.array(l2), distance=th1)
            ik1 = [j for j in ik]
            ik, _ = signal.find_peaks(-np.array(l2), distance=th1)
            ik2 = [j for j in ik]
            pt0 = sorted(set(ik0+ik1+ik2))
            l3 = [round(l2[i1]/M0,3) for i1 in range(len(l2))]
            mgs1.append([M0, l3, pt0, ik1, ik2])
        # qhs, qrss, rss, srs = [], [], [], []
        mgs2 = []
        # n0 = 0
        qhs0, qhs, srs = [], [], []
        for i0 in range(36):  # 计算波和起止点
            # if i0 != 5:
            #     continue
            # print(i0+1)
            M0, l3, pt0, ik1, ik2 = mgs1[i0]
            # print(i0+1, [pt0[i1]+Qh for i1 in range(len(pt0))])
            # print('序号', i0+1)
            Qh0, QRSs, Sr0, Rs = cmpr_qrspt1(l3, pt0, ths, dhs, ik1, ik2)
            qhs.append(Qh0)
            srs.append(Sr0)
            mg = [[l3[ii] for ii in jj] for jj in QRSs]
            if 1.0 in mg[0] or -1.0 in mg[0]:
                qhs0.append(Qh0)
            mgs2.append([Qh0, QRSs, Sr0, Rs])
            # l21 = []
            # for l22 in QRSs:
            #     l21.append([l3[i1] for i1 in l22])
            # print('序号', i0+1, l21)
            
            # pt0 = bxs
            # # l2 = [l3[i1]*M0 for i1 in range(len(l3))]
            # strs = str([round(l3[i1],3) for i1 in pt0])
            # strs += '\n'+str([Qh+i1 for i1 in pt0])
            # g1 = '%s/%s_%d.png' % (d23, item, i0+1)
            # xss = list(range(len(ls36[i0])))
            # plt.rcParams['savefig.dpi'] = 512
            # plt.plot(xss, ls36[i0], c='k', linewidth=0.5)
            # plt.plot([xss[0],xss[-1]], [0,0], linewidth=0.5)
            # for i1 in pt0:
            #     plt.scatter(Qh+i1, l3[i1]*M0, c='r', s=2)
            # plt.title(strs)
            # plt.savefig(g1)
            # plt.close()
        # l112.append(n0)
        # # 综合起止点
        # qhs, srs = [[mg[ii] for mg in mgs2] for ii in [0, 2]]
        rss = hb_lis0([mg[3] for mg in mgs2])
        # qh0 = cmpr_jzd0(qhs, max(min(rss)-1,0), 0)[0]  # 综合起止点
        if len(qhs0)>2:
            qh0 = cmpr_jzd0(qhs0, max(min(rss)-1,0), 0)[-1]  # 综合起止点
        else:
            qh0 = cmpr_jzd0(qhs, max(min(rss)-1,0), 0)[-1]  # 综合起止点
        # t1, t2 = 40*N0/600+qh0, 90*N0/600+qh0
        t1, t2 = 62+qh0, 109+qh0
        sr0s = cmpr_jzd0(srs, min(max(rss)+1,Sr+1-Qh), 1)
        sr1s = [t00 for t00 in sr0s if t00>=t1 and t00<=t2]
        # print(N0, qh0, sr1s, t1, t2)
        if sr1s:
            sr0 = sr1s[0]
            # print('get one')
        else:
            srs1 = [t00 for t00 in srs if t00>=t1 and t00<=t2]
            if srs1:
                sr0 = cmpr_jzd0(srs1, min(max(rss)+1,Sr+1-Qh), 1)[0]
                # print('re get')
            else:
                sr0 = sr0s[0]
                # print('raw')
        print(item, qh0+Qh, sr0+Qh, sr0-qh0)
        d23 = '%s/%s' % (d22, item)
        new_folder(d23)
        # qhs1, srs1 = [], []
        # qrs00 = []
        l11 = [[qh0+Qh, sr0+Qh]]
        l12 = []
        for i0 in range(36):
            M0, l3, pt0, ik1, ik2 = mgs1[i0]
            Qh0, QRSs, Sr0, Rs = mgs2[i0]
            # # 综合起止点后，修改复合波和起止点
            # qjp, alp = [], []
            # for ps in QRSs:
            #     if max(ps)<qh0:
            #         continue
            #     if min(ps)>sr0:
            #         continue
            #     qjp.append(ps)
            #     alp += ps
            # Qh0 = cmpr_qhsr0(pt0, min(alp), 0)
            # Sr0 = cmpr_qhsr0(pt0, max(alp), 1)
            # # 绘制-单通道-QRS复合信息+时刻点；QRS复合点，起止点，综合起止线
            alp = hb_lis0(QRSs)
            # mg = [[l3[ii] for ii in jj] for jj in QRSs]
            # print(mg)
            # strs = str(mg)+'\n'+str([Qh+i1 for i1 in alp])
            # g1 = '%s/%s_%d.png' % (d23, item, i0+1)
            # xss = list(range(len(ls36[i0])))
            # plt.rcParams['savefig.dpi'] = 512
            # plt.plot(xss, ls36[i0], c='k', linewidth=0.5)
            # plt.plot([xss[0],xss[-1]], [0,0], linewidth=0.5)
            # for i1 in [qh0, sr0]:
            #     plt.plot([Qh+i1,Qh+i1], [min(ls36[i0]),max(ls36[i0])], linewidth=0.5)
            # for i1 in alp:
            #     plt.scatter(Qh+i1, M0*l3[i1], c='r', s=2)
            # for i1 in [Qh0, Sr0]:
            #     plt.scatter(Qh+i1, M0*l3[i1], c='g', s=2)
            # plt.title(strs)
            # plt.savefig(g1)
            # plt.close()
            # # 信息收集
            # l11.append([i0+1, mg, Qh0+Qh, Sr0+Qh])
            alp = [ii+Qh for ii in alp]
            l12.append([[qh0+Qh, sr0+Qh], alp, [Qh0+Qh, Sr0+Qh]])
        # save_txt(l11, 'list', '%s/%s.txt' % (d22, item))
        # 空间波组图-各通道起止点
        g1 = '%s/%s.png' % (d22, item)  # 空间
        cmpr_draw0_1(ls36, g1, l12)
    
    # l112 = set(l112)
    # print(l112)
    return d1


def daochu0(anal_path, mess_path, metr_path, resu_path):
    '''导出1526T波信息'''
    l0 = get_lines1('%s/ids.txt' % mess_path)
    ids0, ids1 = txt_to_list('%s/idx1526.txt' % mess_path)[0]
    l1 = get_lines1('%s/tbo3/opm_types.txt' % anal_path)
    ids = ids0+ids1
    # ls = [[l0[i], l1[i]] for i in ids]
    # # save_txt(ls, 'list', '%s/1526_types.txt' % resu_path)
    # l2, l3 = [], []
    # for i in range(36):
    #     l2.append(txt_to_list('%s/opm/space_twave_rtp%d.txt' % (metr_path, i+1)))
    #     l3.append(txt_to_list('%s/opm/space_twave_rtn%d.txt' % (metr_path, i+1)))
    # l4 = [[[l2[i][j], -l3[i][j]] for i in range(36)] for j in range(len(l2[0]))]
    # l5 = [[l0[i], l4[i]] for i in ids]
    # save_txt(l5, 'list', '%s/1526_values.txt' % resu_path)
    
    # # 1526QRS复合情况
    # l1 = txt_to_list('%s/opm/space_twavedt_ur1.txt' % metr_path)
    # ls = [[l0[i], l1[i]] for i in ids]
    # save_txt(ls, 'list', '%s/1526_space_twavedt_ur1.txt' % resu_path)
    # l1 = txt_to_list('%s/opm/space_twavedt_iv1.txt' % metr_path)
    # ls = [[l0[i], l1[i]] for i in ids]
    # save_txt(ls, 'list', '%s/1526_space_twavedt_iv1.txt' % resu_path)
    '''导出1526QRS复合情况'''
    # tpqrs = ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
    # l1 = [txt_to_list('%s/opm/space_qrswv%d.txt' % (metr_path, i)) for i in range(1, 37)]
    # l1 = [[l0[i], [tpqrs[l1[j][i]-1] for j in range(36)]] for i in ids]
    # save_txt(l1, 'list', '%s/1526_space_qrswv_types.txt' % resu_path)
    # for tx in ['neg', 'pos', 'Qr', 'rS']:
    #     l1 = txt_to_list('%s/opm/space_qrswv_%s.txt' % (metr_path, tx))
    #     l1 = [[l0[i], l1[i]] for i in ids]
    #     save_txt(l1, 'list', '%s/1526_space_qrswv_%s.txt' % (resu_path, tx))
    
    # 测试保存10000参数
    l1 = [[[[-0.119], [1.0], [-0.11]] for j in range(100)] for i in range(100)]
    save_txt(l1, 'list', '%s/1526_space_qrswv_%s.txt' % (resu_path, 'nows'))
    
    return resu_path


def cmpr_tbo1(d0, d1, d20):
    '''T波识别和参数开发-设计-100例: mess_path, anal_path, data_path; 09*'''
    # 1.准备-文件夹/数据
    d1 = '%s/tbo1' % d1
    new_folder(d1)
    d2 = '%s/opm_1' % d20
    d21 = '%s/100opm_1' % d0
    new_folder(d21)
    ids = get_lines1('%s/ids_now.txt' % d0)
    tx = 'opm'
    # ids0 = get_lines1('%s/ids.txt' % d0)
    # idx = get_idxs(ids0, ids, [])
    # # 2.时刻点计算
    # tms = [gettime('%s/%s.txt' % (d2, ids[i]))[:-1] for i in range(len(ids))]
    # save_txt(tms, 'list', '%s/tms1.txt' % d1)
    # # 3.基线微调
    # tms = txt_to_list('%s/tms1.txt' % d1)
    # l00, l01 = [], []  # TP基线差, 基线值
    # ts = []
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item = ids[i]
    #     f0 = '%s/%s.txt' % (d2, item)
    #     f1 = '%s/%s.txt' % (d21, item)
    #     l1, l2 = cmpr_TPbase1(f0, f1, tms[i])  # 保存心磁
    #     l00.append(l1)
    #     l01.append(l2)
    #     ts.append(gettime(f1)[:-1])
    # save_txt(l00, 'list', '%s/%s_TPbase_er1.txt' % (d1, tx))  # 基线误差
    # save_txt(l01, 'list', '%s/%s_TPbase1.txt' % (d1, tx))  # 基线误差
    # save_txt(ts, 'list', '%s/%s_1_times.txt' % (d1, tx))
    # 4.1 导出时间波组图/T波分布图
    tms = txt_to_list('%s/%s_1_times.txt' % (d1, tx))
    d11 = '%s/timews1' % d1
    new_folder(d11)
    d12 = '%s/Tspace' % d1
    new_folder(d12)
    d2 = d21
    th = 20  # 峰值间距
    ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item = ids[i]
    #     ts = tms[i]
    #     Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
    #     ls36 = get_mcg36('%s/%s.txt' % (d2, item), 0)
    #     g11 = '%s/%s.png' % (d11, item)
    #     cmpr_draw1(ls36, tms[i], g11)  # 时间
    #     l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    #     l3 = []
    #     l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    #     M = max(get_mai0(ls36))
    #     for i in range(len(l1)):  # 基于双比例
    #         M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
    #         l2 = l1[i]
    #         v0, v1, j0, j1 = 0, 0, 0, 0
    #         ik, _ = signal.find_peaks(-np.array(l2), distance=th)
    #         l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         ik, _ = signal.find_peaks(np.array(l2), distance=th)
    #         l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         if l30:
    #             l4 = [l2[j] for j in l30]
    #             v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
    #         if l31:
    #             l4 = [l2[j] for j in l31]
    #             v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
    #         f00, f01 = v0/M, v1/M
    #         f10, f11 = abs(v0/M0), abs(v1/M0)
    #         l3.append(cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 1))
    #     g1 = '%s/%s.png' % (d12, item)
    #     cmpr_draw6(ls36, l3, [Th, Tr], g1)
    # # 4.2 T波分布信息
    # lx = ['a', 'e', 'c', 'd', 'b']
    # msh = ['upright', 'inversion', 'posneg_bidirect', 'negpos_bidirect', 'low']
    # tms = txt_to_list('%s/%s_1_times.txt' % (d1, tx))
    # l0s, l3s = [], []
    # for i in range(len(ids)):
    #     item, ts = ids[i], tms[i]
    #     print('start:', i+1, 'end:', len(ids)-i-1)
    #     Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
    #     ls36 = get_mcg36('%s/%s.txt' % (d2, item), 0)
    #     l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    #     l00, l3 = [], []
    #     l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    #     M = max(get_mai0(ls36))
    #     for i in range(len(l1)):  # 基于双比例
    #         M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
    #         l2 = l1[i]
    #         v0, v1, j0, j1 = 0, 0, 0, 0
    #         ik, _ = signal.find_peaks(-np.array(l2), distance=th)
    #         l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         ik, _ = signal.find_peaks(np.array(l2), distance=th)
    #         l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         if l30:
    #             l4 = [l2[j] for j in l30]
    #             v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
    #         if l31:
    #             l4 = [l2[j] for j in l31]
    #             v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
    #         f00, f01 = v0/M, v1/M
    #         f10, f11 = abs(v0/M0), abs(v1/M0)
    #         tx, _ = cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths)
    #         zl = lx[msh.index(tx)]
    #         l3.append(zl)
    #         if zl == 'c':
    #             l00.append(get_round([f11, f10], [3, 3]))
    #         else:
    #             l00.append(get_round([f10, f11], [3, 3]))
    #     l0s.append(l00)
    #     l3s.append(l3)
    # save_txt(l0s, 'list', '%s/values.txt' % d1)
    # save_txt(l3s, 'list', '%s/types.txt' % d1)
    # l1, l2 = l0s, l3s
    # l1 = ['\n'.join([str(l1[i][j*6:j*6+6]) for j in range(6)]) for i in range(len(l1))]
    # l2 = ['\n'.join([''.join(l2[i][j*6:j*6+6]) for j in range(6)]) for i in range(len(l2))]
    # l0 = ['\n'.join([ids[i], l2[i], l1[i]]) for i in range(len(l1))]
    # write_str('\n'.join(l0), '%s/messages.txt' % d1)
    # 5.1 0/1文件夹-观测规律-分布参数
    d30 = '%s/Tlb0' % d1
    d31 = '%s/Tlb1' % d1
    new_folder([d30, d31])
    ids = get_lines1('%s/ids_now.txt' % d0)
    for i in range(len(ids)):
        f0 = '%s/%s.png' % (d12, ids[i])
        if i < 32:
            f1 = '%s/%s.png' % (d30, ids[i])
        else:
            f1 = '%s/%s.png' % (d31, ids[i])
        shutil.copyfile(f0, f1)
    
    return d1


def cmpr_tbo2(d0, d1, d20, d3, d31):
    '''T波识别和参数开发-计算-1526例: mess_path, anal_path, data_path, metr_path, diag_path; 09*'''
    # 1.准备-文件夹/数据
    d1 = '%s/tbo2' % d1
    new_folder(d1)
    d2 = '%s/opm' % d20
    d21 = '%s/opm_2' % d20
    new_folder(d21)
    ids = get_lines1('%s/ids.txt' % d0)
    tx = 'opm'
    # # 2.时刻点计算
    # tms = [gettime('%s/%s.txt' % (d2, ids[i]))[:-1] for i in range(len(ids))]
    # save_txt(tms, 'list', '%s/tms1.txt' % d1)
    # # 3.基线微调
    # tms = txt_to_list('%s/tms1.txt' % d1)
    # l00, l01 = [], []  # TP基线差, 基线值
    # ts = []
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item = ids[i]
    #     f0 = '%s/%s.txt' % (d2, item)
    #     f1 = '%s/%s.txt' % (d21, item)
    #     l1, l2 = cmpr_TPbase1(f0, f1, tms[i])  # 保存心磁
    #     l00.append(l1)
    #     l01.append(l2)
    #     ts.append(gettime(f1)[:-1])
    # save_txt(l00, 'list', '%s/%s_TPbase_er1.txt' % (d1, tx))  # 基线误差
    # save_txt(l01, 'list', '%s/%s_TPbase1.txt' % (d1, tx))  # 基线误差
    # save_txt(ts, 'list', '%s/%s_2_times.txt' % (d1, tx))
    # # 4.1 导出时间波组图/T波分布图
    # tms = txt_to_list('%s/%s_2_times.txt' % (d1, tx))
    # d11 = '%s/timews1' % d1
    # new_folder(d11)
    # d12 = '%s/Tspace' % d1
    # new_folder(d12)
    d2 = d21
    th = 20  # 峰值间距
    ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item = ids[i]
    #     ts = tms[i]
    #     Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
    #     ls36 = get_mcg36('%s/%s.txt' % (d2, item), 0)
    #     g11 = '%s/%s.png' % (d11, item)
    #     cmpr_draw1(ls36, tms[i], g11)  # 时间
    #     l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    #     l3 = []
    #     l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    #     M = max(get_mai0(ls36))
    #     for i in range(len(l1)):  # 基于双比例
    #         M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
    #         l2 = l1[i]
    #         v0, v1, j0, j1 = 0, 0, 0, 0
    #         ik, _ = signal.find_peaks(-np.array(l2), distance=th)
    #         l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         ik, _ = signal.find_peaks(np.array(l2), distance=th)
    #         l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         if l30:
    #             l4 = [l2[j] for j in l30]
    #             v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
    #         if l31:
    #             l4 = [l2[j] for j in l31]
    #             v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
    #         f00, f01 = v0/M, v1/M
    #         f10, f11 = abs(v0/M0), abs(v1/M0)
    #         l3.append(cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 1))
    #     g1 = '%s/%s.png' % (d12, item)
    #     cmpr_draw6(ls36, l3, [Th, Tr], g1)
    # # 4.2 T波分布信息
    # # lx = ['a', 'e', 'c', 'd', 'b']
    # # msh = ['upright', 'inversion', 'posneg_bidirect', 'negpos_bidirect', 'low']
    # # tms = txt_to_list('%s/%s_2_times.txt' % (d1, tx))
    # # l0s, l3s = [], []
    # # for i in range(len(ids)):
    # #     item, ts = ids[i], tms[i]
    # #     print('start:', i+1, 'end:', len(ids)-i-1)
    # #     Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
    # #     ls36 = get_mcg36('%s/%s.txt' % (d2, item), 0)
    # #     l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    # #     l00, l3 = [], []
    # #     l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    # #     M = max(get_mai0(ls36))
    # #     for i in range(len(l1)):  # 基于双比例
    # #         M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
    # #         l2 = l1[i]
    # #         v0, v1, j0, j1 = 0, 0, 0, 0
    # #         ik, _ = signal.find_peaks(-np.array(l2), distance=th)
    # #         l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
    # #         ik, _ = signal.find_peaks(np.array(l2), distance=th)
    # #         l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
    # #         if l30:
    # #             l4 = [l2[j] for j in l30]
    # #             v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
    # #         if l31:
    # #             l4 = [l2[j] for j in l31]
    # #             v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
    # #         f00, f01 = v0/M, v1/M
    # #         f10, f11 = abs(v0/M0), abs(v1/M0)
    # #         tx, _ = cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths)
    # #         zl = lx[msh.index(tx)]
    # #         l3.append(zl)
    # #         if zl == 'c':
    # #             l00.append(get_round([f11, f10], [3, 3]))
    # #         else:
    # #             l00.append(get_round([f10, f11], [3, 3]))
    # #     l0s.append(l00)
    # #     l3s.append(l3)
    # # save_txt(l0s, 'list', '%s/values.txt' % d1)
    # # save_txt(l3s, 'list', '%s/types.txt' % d1)
    # l1 = txt_to_list('%s/values.txt' % d1)
    # l2 = txt_to_list('%s/types.txt' % d1)
    # l1 = ['\n'.join([str(l1[i][j*6:j*6+6]) for j in range(6)]) for i in range(len(l1))]
    # l2 = ['\n'.join([''.join(l2[i][j*6:j*6+6]) for j in range(6)]) for i in range(len(l2))]
    # l0 = ['\n'.join([ids[i], l2[i], l1[i]]) for i in range(len(l1))]
    # write_str('\n'.join(l0), '%s/messages.txt' % d1)
    # # 5.1 单参数-导出
    mtnms0 = ['space_twave%d' % i for i in range(1, 37)]
    # ty = ['a', 'b', 'c', 'd', 'e']
    # l0 = txt_to_list('%s/types.txt' % d1)
    # l0 = [[l0[i][j] for i in range(len(l0))] for j in range(len(l0[0]))]
    # for i in range(len(l0)):
    #     l1 = [ty.index(l0[i][j])+1 for j in range(len(l0[i]))]
    #     save_txt(l1, 'list', '%s/%s/%s.txt' % (d3, tx, mtnms0[i]))
    # l1 = txt_to_list('%s/values.txt' % d1)
    # # mtnms1 = ['space_twave_rtp%d' % i for i in range(1, 37)]
    # # mtnms2 = ['space_twave_rtn%d' % i for i in range(1, 37)]
    # # l10 = [[l1[i][j] for i in range(len(l1))] for j in range(len(l1[0]))]
    # # l11, l12 = [[] for i in range(36)], [[] for i in range(36)]
    # # for i in range(len(l10)):
    # #     l11, l12 = [], []
    # #     for j in range(len(l0[i])):
    # #         if l0[i][j] == 'c':
    # #             l11.append(l10[i][j][0])  # p
    # #             l12.append(l10[i][j][1])
    # #         else:
    # #             l11.append(l10[i][j][1])  # p
    # #             l12.append(l10[i][j][0])
    # #     save_txt(l11, 'list', '%s/%s/%s.txt' % (d3, tx, mtnms1[i]))
    # #     save_txt(l12, 'list', '%s/%s/%s.txt' % (d3, tx, mtnms2[i]))
    # # 5.2 单参数-特异性参数-筛选
    # n0, p0 = 0.05, 0.95
    # idx1, idx2 = txt_to_list('%s/idx1526.txt' % d0)[0]
    # bl = len(idx2)/len(idx1)
    # n00 = len(idx1+idx2)
    # rts0, rts1 = [], []
    # rts2 = []
    # for i in range(36):
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d3, tx, mtnms0[i]))
    #     l1, l2 = [[l0[i0] for i0 in idx] for idx in [idx1, idx2]]
    #     ns = [[sum(1 for i0 in l if i0==i1+1) for i1 in range(5)] for l in [l1,l2]]
    #     ps, m12, n12 = [], 0, 0
    #     for j in range(5):
    #         n1, n2, p1 = ns[0][j]*bl, ns[1][j], 0
    #         if n1+n2 > 0:
    #             p1 = n2/(n1+n2)
    #             if p1 >= p0:
    #                 n12 += n1+n2
    #             elif p1 <= 1-p0:
    #                 m12 += n1+n2
    #         ps.append(round(p1,3))
    #     if n12:
    #         ps0 = [round(p00*100) for p00 in ps]
    #         jg0 = [mtnms0[i], ps0, round(n12/n00,3)]
    #         print(jg0)
    #         rts2.append(jg0)
    # #     if n12/n00 > n0:
    # #         rts1.append([mtnms0[i], ps, round(n12/n00,3)])
    # #     if m12/n00 > n0:
    # #         rts0.append([mtnms0[i], ps, round(m12/n00,3)])
    # # # save_txt(rts0, 'list', '%s/results_36_spe0.txt' % d1)  # 无
    # # save_txt(rts1, 'list', '%s/results_36_spe1.txt' % d1)
    # save_txt(rts2, 'list', '%s/results_36_spe2.txt' % d1)
    # # 5.31 单参数-有效性参数-筛选
    lbs = 'model_1526'
    d4 = '%s/%s' % (d0, lbs)
    new_folder(d4)
    # # write_str('\n'.join(mtnms0), '%s/mts.txt' % d4)
    # rk0 = [2, 3, 4, 5]
    # rks, nss = [], []
    # for i in range(36):
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d3, tx, mtnms0[i]))
    #     l1, l2 = [[l0[i0] for i0 in idx] for idx in [idx1, idx2]]
    #     ns = [[sum(1 for i0 in l if i0==i1+1) for i1 in range(5)] for l in [l1,l2]]
    #     ps = []
    #     for j in range(5):
    #         n1, n2, p1 = ns[0][j]*bl, ns[1][j], 0
    #         if n1+n2 > 0:
    #             p1 = n2/(n1+n2)
    #         ps.append(round(100*p1))
    #     nss.append([ns, []])
    #     rks.append([rk0, ps])
    # save_txt(nss, 'list', '%s/%s_nums0.txt' % (d4, tx))
    # save_txt(rks, 'list', '%s/%s_rks0.txt' % (d4, tx))
    # # 5.32.诊断、效用排序
    # mtnms = get_lines1('%s/mts.txt' % d4)
    # # for tx in ['squid2', 'opm']:  # 诊断
    # rkmes = txt_to_list('%s/%s_rks0.txt' % (d4, tx))
    # d00 = diag_resu0('%s/%s' % (d3, tx), mtnms, rkmes, d31, tx)
    # # for tx in ['squid2', 'opm']:  # 效用排序
    # lis1 = []
    # for i in range(len(mtnms)):
    #     item = mtnms[i]
    #     l00 = txt_to_list('%s/%s/%s.txt' % (d31, tx, item))
    #     l0, l1 = [[l00[i] for i in idx] for idx in [idx1, idx2]]
    #     auc, acc, f1sc = cal_utility1(l0, l1)
    #     lis1.append([auc, acc, f1sc])
    # save_txt(lis1, 'list', '%s/%s_uty.txt' % (d4, tx))
    # l1 = txt_to_list('%s/%s_uty.txt' % (d4, tx))
    # lis1 = [[i]+l1[i] for i in range(len(l1))]
    # l0 = [lis1[i] for i in range(len(lis1)) if lis1[i][1]+lis1[i][3]>1.2 or lis1[i][2]+lis1[i][3]>1.2]
    # data = np.array(l0)  # Auc+Acc>1.2 or Acc+F1>1.2, 排序Auc>F1>Acc
    # idex = np.lexsort((data[:, 0], -1*data[:, 2], -1*data[:, 3], -1*data[:, 1]))
    # sorted_data = data[idex]
    # lis1 = sorted_data.tolist()
    # l3 = ['%s, %.3f, %.3f, %.3f' % (mtnms[int(l[0])], l[1], l[2], l[3]) for l in lis1]
    # save_txt(l3, 'list', '%s/%s_sort.txt' % (d4, tx))
    # save_txt([mtnms[int(l[0])] for l in lis1], 'list', '%s/%s_mts.txt' % (d4,tx))
    # save_txt([rkmes[int(l[0])] for l in lis1], 'list', '%s/%s_rks.txt' % (d4,tx))
    # # 6.1 分布参数-1+2*2=5
    # rks = txt_to_list('%s/%s_rks0.txt' % (d4, tx))
    # combs = [[3, 9, 10], [22, 28, 29, 31, 32, 33, 34], [21, 27, 32, 33, 34]]
    # # iks = combs[2]  # 特异性参数
    # # rkss = [rks[i-1] for i in iks]
    # # l1 = [txt_to_list('%s/%s/space_twave%d.txt' % (d3, tx, i)) for i in iks]
    # # l11 = [[diag_res0(m0, rkss[i]) for m0 in l1[i]] for i in range(len(rkss))]
    # # # for l2 in l11:
    # # #     l0, l1 = [[l2[i] for i in idx] for idx in [idx1, idx2]]
    # # #     auc, acc, f1sc = cal_utility1(l0, l1)
    # # #     print(auc, acc, f1sc)
    # # l1 = [[l11[i][j] for i in range(len(l11))] for j in range(len(l11[0]))]
    # # l2 = cmpr_diag0(l1)
    # # l0, l1 = [[l2[i] for i in idx] for idx in [idx1, idx2]]
    # # # auc, acc, f1sc = cal_utility1(l0, l1)
    # # # print(auc, acc, f1sc)
    # # # save_txt(l2, 'list', '%s/%s/space_twavedt_iv1.txt' % (d3, tx))
    # # ps = sorted(set(l2))
    # # l01 = l0+l1
    # # n01 = len(l01)
    # # for p0 in ps:
    # #     n1 = sum(1 for p1 in l01 if p1 == p0)
    # #     print(p0, round(n1/n01,3))
    # # # rks0 = [ps[1:], ps]
    # # # print(rks0)
    # idx0 = txt_to_list('%s/idx100.txt' % d0)[0]
    # iks1, iks2 = combs[0], sorted(set(combs[1]+combs[2]))  # 有效参数
    # rts1 = [[0.8, 1.3, 1.6, 2.1, 2.4, 3.7, 4.5, 4.7], [25, 61, 63, 64, 65, 72, 75, 76, 96]]
    # rts2 = [[0.1, 0.3, 0.7, 0.8, 1.0, 1.6, 2.1, 2.3, 2.4, 6.0], [39, 40, 41, 42, 45, 46, 47, 48, 49, 76, 95]]
    # vs, ps = [], []
    # mts, dgs = [], []
    # for i0 in range(2):
    #     iks = [iks1, iks2][i0]
    #     rts = [rts1, rts2][i0]
    #     rkss = [rks[i-1] for i in iks]
    #     l1 = [txt_to_list('%s/%s/space_twave%d.txt' % (d3, tx, i)) for i in iks]
    #     mts.append(l1)
    #     l11 = [[diag_res0(m0, rkss[i]) for m0 in l1[i]] for i in range(len(rkss))]
    # # for l2 in l11:
    # #     l0, l1 = [[l2[i] for i in idx] for idx in [idx1, idx2]]
    # #     auc, acc, f1sc = cal_utility1(l0, l1)
    # #     print(auc, acc, f1sc)
    #     l1 = [[l11[i][j] for i in range(len(l11))] for j in range(len(l11[0]))]
    #     dgs.append(l1)
    #     l2 = cmpr_diag1(l1)
    # # ms0, ms1 = [[l2[i] for i in idx] for idx in [idx1, idx2]]
    # # n1 = sum(1 for m0 in ms0 if m0>=4.7)
    # # n2 = sum(1 for m0 in ms1 if m0>=4.7)
    # # print(n2/(n1+n2), (n1+n2)/len(idx1+idx2))
    # # rs1, rs2, _, _ = ranks_spemt0([], [], ms0, ms1, 'space_twavedt_ur2', 0.95)
    # # print(rs1, rs2)
    # # l0, l1 = ms0, ms1
    # # v1 = min(l0+l1)-1
    # # xyss1 = make_ranks2(l0, l1, mode=0, v1=v1, lis0=[])
    # # rts = auto_rks0(xyss1, 'space_twavedt_ur1')
    # # rts = auto_rks0(xyss1, 'space_twavedt_iv1')
    # # print(rts)
    #     l11 = [diag_res0(m0, rts) for m0 in l2]
    #     vs.append(l2)
    #     ps.append(l11)
    # # l0, l1 = [[l11[i] for i in idx] for idx in [idx1, idx2]]
    # # auc, acc, f1sc = cal_utility1(l0, l1)
    # # print(auc, acc, f1sc)
    # # save_txt(l2, 'list', '%s/%s/space_twavedt_ur1.txt' % (d3, tx))
    # # save_txt(l2, 'list', '%s/%s/space_twavedt_iv1.txt' % (d3, tx))
    # # 1.导出100例数据的2分布参数
    # ids0 = get_lines1('%s/ids_now.txt' % d0)
    # # vs = [[vs[0][i], vs[1][i]] for i in idx0]
    # # ps = [[ps[0][i], ps[1][i]] for i in idx0]
    # # strs = 'ID, upright_mt, p, inversion_mt, p'
    # # for i in range(len(ids0)):
    # #     strs += '\n%s, %s' % (ids0[i], [[vs[i][0],ps[i][0]],[vs[i][1],ps[i][1]]])
    # # write_str(strs, '%s/space_twavedt_results.txt' % d4)
    # mts = [[[mts[0][j][i] for j in range(len(mts[0]))], [mts[1][j][i] for j in range(len(mts[1]))]] for i in idx0]
    # dgs = [[dgs[0][i], dgs[1][i]] for i in idx0]
    # strs = 'ID, upright_mt, ps, inversion_mt, ps'
    # for i in range(len(ids0)):
    #     strs += '\n%s, %s' % (ids0[i], [[mts[i][0],dgs[i][0]],[mts[i][1],dgs[i][1]]])
    # write_str(strs, '%s/space_twavedt_mtsdgs.txt' % d4)
    
    return d1


def cmpr_qrst0(d0, d1, d2, d3, d4, d5):
    '''T波/QRS复合参数的基准点调整:
    mess_path, anal_path, data_path, metr_path, diag_path, resu_path'''
    # 1.准备-标签/数据/文件夹
    ids = get_lines1('%s/ids.txt' % d0)
    d30 = '%s/qrswv0' % d3  # 单参数
    d31 = '%s/opm' % d30
    d32 = '%s/squid2' % d30
    d33 = '%s/twave0' % d3
    d34 = '%s/opm' % d33
    d35 = '%s/squid2' % d33
    new_folder([d30, d31, d32, d33, d34, d35])
    tpqrs = ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
    th0 = 20  # 前后延长20ms(心率60)
    th1 = 5  # 波峰波谷最小间距
    ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]  # T波-双比例阈值
    ths1 = [0.1, 0.3, 0.13, 0.7, 0.045]
    dhs = [55, 40, 10, 60]
    # tx = 'opm'
    tx, ikk = sys.argv[1], int(sys.argv[2])
    kk1, kk2 = 30*(ikk-1), min(30*ikk, len(ids))
    # for tx in ['opm', 'squid2']:
    # i=0
    rcx, rcy = [], []
    mts1, mts2 = [], []
    # for i in range(len(ids)):
    for i in range(kk1, kk2):
        print('start:', i+1, 'left:', len(ids)-i)
        item = ids[i]
        f0 = '%s/%s_2/%s.txt' % (d2, tx, item)
        t0 = round(th0*len(get_lines1(f0))/600)
        ts = gettime(f0)
        Qh, Rp, Sr, Th, Tr = ts[3], ts[5], ts[7], ts[8], ts[12]
        Qh1, Sr1 = Qh-t0, Sr+t0
        Z = cal_interpolate(f0)  # 插值结果
        px, py, nx, ny = get_dipole(Z[Rp])
        cx, cy = get_round([(px+nx)/2, (py+ny)/2], [0,0])
        rcx.append(cx)
        rcy.append(cy)
        # if i%10 == 0:
        #     write_str(str(rcx), '%s/%s_rcx.txt' % (d3, tx))
        #     write_str(str(rcy), '%s/%s_rcy.txt' % (d3, tx))
        ls36 = [[Z[kk][ii, jj] for kk in range(len(Z))] for ii in range(Z[0].shape[0]) for jj in range(Z[0].shape[1])]
        qrs0 = []  # QRS复合参数
        tt0 = []  # T波参数
        M = max(get_mai0(ls36))  # T波-全局最大幅值
        for i0 in range(len(ls36)):
            # QRS复合参数
            l2 = ls36[i0][Qh1:Sr1+1]
            M0 = max(max(l2), -min(l2))
            ik0, ik1, ik2 = cal_bfbg0(l2, th1)
            pt0 = sorted(set(ik0+ik1+ik2))
            l3 = [round(l2[i1]/M0,3) for i1 in range(len(l2))]
            Qh0, QRSs, Sr0, Rs = cmpr_qrspt1(l3, pt0, ths1, dhs, ik1, ik2)
            mg = [[l3[ii] for ii in jj] for jj in QRSs]
            strs, fg = cmpr_tpqrs0(mg, tpqrs)
            qrs0.append(fg+1)
            # T波参数
            l11, l2 = ls36[i0][Qh:Sr], ls36[i0][Th:Tr]
            M0 = max(max(l11), -min(l11))  # T波-QRS最大幅值
            v0, v1, j0, j1 = 0, 0, 0, 0
            ik0, ik1, ik2 = cal_bfbg0(l2, th1)
            l30 = [j for j in ik2 if j!=0 and j!=len(l2)-1]
            l31 = [j for j in ik1 if j!=0 and j!=len(l2)-1]
            if l30:
                l4 = [l2[j] for j in l30]
                v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
            if l31:
                l4 = [l2[j] for j in l31]
                v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
            f00, f01, f10, f11 = v0/M, v1/M, abs(v0/M0), abs(v1/M0)
            zl = cmpr_gettx3([f00,f01,f10,f11,j0,j1], ths)
            tt0.append(zl)
        mts1.append(qrs0)
        mts2.append(tt0)
    write_str(str(rcx), '%s/cache_%s/rcx_%d.txt' % (d3, tx, ikk))
    write_str(str(rcy), '%s/cache_%s/rcy_%d.txt' % (d3, tx, ikk))
    write_str(str(mts1), '%s/cache_%s/mts1_%d.txt' % (d3, tx, ikk))
    write_str(str(mts2), '%s/cache_%s/mts2_%d.txt' % (d3, tx, ikk))
    
    return d4


def cmpr_tbo3(d0, d1, d20, d3, d31):
    '''T波分布参数sqop-1526*2例: mess_path, anal_path, data_path, metr_path, diag_path; 09*'''
    # 1.准备-文件夹/数据
    ids = get_lines1('%s/ids.txt' % d0)  # 心磁ID
    d1 = '%s/tbo3' % d1  # 根文件夹
    new_folder(d1)
    mtnms0 = ['space_twave%d' % i for i in range(1, 37)]  # 单通道参数
    n0, p0 = 0.05, 0.95  # 特异性阈值
    idx1, idx2 = txt_to_list('%s/idx1526.txt' % d0)[0]  # 索引
    bl = len(idx2)/len(idx1)  # 阴阳均衡比例
    n00 = len(idx1+idx2)*2  # 总数
    lbs = 'model_1526'
    d4 = '%s/%s' % (d0, lbs)  # 信息文件夹
    new_folder(d4)
    # # # tx = 'opm'  # 单参数————
    # # tx = 'squid2'
    # for tx in ['opm', 'squid2']:
    #     d2 = '%s/%s' % (d20, tx)  # 源心磁文件夹
    #     d21 = '%s/%s_2' % (d20, tx)  # 调整后心磁文件夹
    #     new_folder(d21)
    #     # 2.时刻点计算
    #     tms = [gettime('%s/%s.txt' % (d2, ids[i]))[:-1] for i in range(len(ids))]
    #     save_txt(tms, 'list', '%s/%s_tms1.txt' % (d1, tx))
    #     # 3.TP基线微调
    #     tms = txt_to_list('%s/%s_tms1.txt' % (d1, tx))
    #     tms2 = []
    #     for i in range(len(ids)):
    #         print('start:', i+1, 'left:', len(ids)-i)
    #         item = ids[i]
    #         f0 = '%s/%s.txt' % (d2, item)
    #         f1 = '%s/%s.txt' % (d21, item)
    #         l1, l2 = cmpr_TPbase1(f0, f1, tms[i])  # 保存心磁
    #         tms2.append(gettime(f1)[:-1])
    #     save_txt(tms2, 'list', '%s/%s_tms2.txt' % (d1, tx))
    #     # 4.单参数导出
    #     tms = txt_to_list('%s/%s_tms2.txt' % (d1, tx))
    #     d2 = d21  # 数据集文件夹
    #     th = 20  # 峰值间距
    #     ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]  # 单参数阈值
    #     l3s = []
    #     lx = ['a', 'e', 'c', 'd', 'b']  # 对应符号
    #     msh = ['upright', 'inversion', 'posneg_bidirect', 'negpos_bidirect', 'low']
    #     for i in range(len(ids)):
    #         print('start:', i+1, 'left:', len(ids)-i)
    #         item = ids[i]
    #         ts = tms[i]
    #         Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
    #         ls36 = get_mcg36('%s/%s.txt' % (d2, item), 0)
    #         l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    #         l33 = []
    #         l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    #         M = max(get_mai0(ls36))
    #         for i in range(len(l1)):  # 基于双比例
    #             M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
    #             l2 = l1[i]
    #             v0, v1, j0, j1 = 0, 0, 0, 0
    #             ik, _ = signal.find_peaks(-np.array(l2), distance=th)
    #             l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #             ik, _ = signal.find_peaks(np.array(l2), distance=th)
    #             l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #             if l30:
    #                 l4 = [l2[j] for j in l30]
    #                 v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
    #             if l31:
    #                 l4 = [l2[j] for j in l31]
    #                 v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
    #             f00, f01 = v0/M, v1/M
    #             f10, f11 = abs(v0/M0), abs(v1/M0)
    #             tx0, _ = cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 0)
    #             zl = lx[msh.index(tx0)]  # T波类型
    #             l33.append(zl)
    #         l3s.append(l33)
    #     save_txt(l3s, 'list', '%s/%s_types.txt' % (d1, tx))  # 类型集
    #     ty = ['a', 'b', 'c', 'd', 'e']  # 对应1/2/3/4/5
    #     l0 = txt_to_list('%s/%s_types.txt' % (d1, tx))
    #     l0 = [[l0[i][j] for i in range(len(l0))] for j in range(len(l0[0]))]
    #     for i in range(len(l0)):
    #         l1 = [ty.index(l0[i][j])+1 for j in range(len(l0[i]))]  # T波数值
    #         save_txt(l1, 'list', '%s/%s/%s.txt' % (d3, tx, mtnms0[i]))  # 指标
    # # 5.1特异性参数sqop/分级-1526一致性数据
    # rk0 = [2, 3, 4, 5]
    # rks, nss = [], []  # 分级信息/数量分布
    # rts0, rts1 = [], []  # 特异性参数0/1
    # rts2 = []  # 特异性总集
    # for i in range(36):
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d3, 'opm', mtnms0[i]))
    #     l1, l2 = [[l0[i0] for i0 in idx] for idx in [idx1, idx2]]
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d3, 'squid2', mtnms0[i]))
    #     l1 += [l0[i0] for i0 in idx1]  # sqop参数集0/1
    #     l2 += [l0[i0] for i0 in idx2]
    #     ns = [[sum(1 for i0 in l if i0==i1+1) for i1 in range(5)] for l in [l1,l2]]
    #     ps, m12, n12 = [], 0, 0
    #     for j in range(5):
    #         n1, n2, p1 = ns[0][j]*bl, ns[1][j], 0
    #         if n1+n2 > 0:
    #             p1 = n2/(n1+n2)
    #             if p1 >= p0:
    #                 n12 += n1+n2
    #             elif p1 <= 1-p0:
    #                 m12 += n1+n2
    #         ps.append(round(100*p1))
    #     nss.append([ns, []])
    #     rks.append([rk0, ps])
    #     if n12:
    #         jg0 = [mtnms0[i], ps, round(n12/n00,3)]
    #         rts2.append(jg0)
    #     if n12/n00 > n0:
    #         rts1.append([mtnms0[i], ps, round(n12/n00,3)])
    #     if m12/n00 > n0:
    #         rts0.append([mtnms0[i], ps, round(m12/n00,3)])
    # write_str('\n'.join(mtnms0), '%s/mts.txt' % d4)
    # save_txt(nss, 'list', '%s/%s_nums0.txt' % (d4, 'sqop'))
    # save_txt(rks, 'list', '%s/%s_rks0.txt' % (d4, 'sqop'))
    # # save_txt(rts0, 'list', '%s/%s_spe0.txt' % (d4, 'sqop'))  # 无
    # save_txt(rts1, 'list', '%s/%s_spe1.txt' % (d4, 'sqop'))
    # save_txt(rts2, 'list', '%s/%s_spe2.txt' % (d4, 'sqop'))
    # # 5.2有效性参数-诊断、效用排序
    # mtnms = get_lines1('%s/mts.txt' % d4)
    # for tx in ['opm', 'squid2']:  # 诊断
    #     rkmes = txt_to_list('%s/%s_rks0.txt' % (d4, 'sqop'))
    #     d00 = diag_resu0('%s/%s' % (d3, tx), mtnms, rkmes, d31, tx)
    # lis1 = []
    # for i in range(len(mtnms)):
    #     item = mtnms[i]
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d31, 'opm', item))
    #     l1, l2 = [[l0[i] for i in idx] for idx in [idx1, idx2]]
    #     l0 = txt_to_list('%s/%s/%s.txt' % (d31, 'squid2', item))
    #     l1 += [l0[i] for i in idx1]
    #     l2 += [l0[i] for i in idx2]
    #     auc, acc, f1sc = cal_utility1(l1, l2)
    #     lis1.append([auc, acc, f1sc])
    # save_txt(lis1, 'list', '%s/%s_uty.txt' % (d4, 'sqop'))
    # l1 = txt_to_list('%s/%s_uty.txt' % (d4, 'sqop'))
    # lis1 = [[i]+l1[i] for i in range(len(l1))]
    # l0 = [lis1[i] for i in range(len(lis1)) if lis1[i][1]+lis1[i][3]>1.2 or lis1[i][2]+lis1[i][3]>1.2]
    # data = np.array(l0)  # Auc+Acc>1.2 or Acc+F1>1.2, 排序Auc>F1>Acc
    # idex = np.lexsort((data[:, 0], -1*data[:, 2], -1*data[:, 3], -1*data[:, 1]))
    # sorted_data = data[idex]
    # lis1 = sorted_data.tolist()
    # l3 = ['%s, %.3f, %.3f, %.3f' % (mtnms[int(l[0])], l[1], l[2], l[3]) for l in lis1]
    # save_txt(l3, 'list', '%s/%s_sort.txt' % (d4, 'sqop'))
    # save_txt([mtnms[int(l[0])] for l in lis1],'list','%s/%s_mts.txt' % (d4,'sqop'))
    # save_txt([rkmes[int(l[0])] for l in lis1],'list','%s/%s_rks.txt' % (d4,'sqop'))
    # 6.分布参数计算/分级/广泛效用&特异性
    rks = txt_to_list('%s/%s_rks0.txt' % (d4, 'sqop'))
    combs = txt_to_list('%s/%s_combs.txt' % (d4, 'sqop'))[1]
    nm1 = ['ur', 'iv']  # 分布参数2
    # i1 = 1
    for i1 in range(len(combs)):
        iks = combs[i1]
        print(i1, iks)
        rkss = [rks[i-1] for i in iks]
        # opm-指标/诊断/综合得分/特异性
        mt1 = [txt_to_list('%s/%s/space_twave%d.txt' % (d3, 'opm', i)) for i in iks]
        dg1 = [[diag_res0(m0, rkss[i]) for m0 in mt1[i]] for i in range(len(mt1))]
        print('opm dg')
        for dg in dg1:  # 每个单参数的效用
            l0, l1 = [[dg[i] for i in idx] for idx in [idx1, idx2]]
            auc, acc, f1sc = cal_utility1(l0, l1)
            print(auc, acc, f1sc)
        dg1 = [[dg1[i][j] for i in range(len(dg1))] for j in range(len(dg1[0]))]
        dg1 = cmpr_diag1(dg1)
        # save_txt(dg1, 'list', '%s/%s/space_twavedt_%s1.txt' % (d3, 'opm', nm1[i1]))
        l10, l11 = [[dg1[i] for i in idx] for idx in [idx1, idx2]]
        rs1, rs2, _, _ = ranks_spemt0([], [], l10, l11, 'space_twavedt_ur1', 0.95)
        print('opm spe', rs1, rs2)
        # squid2
        mt2 = [txt_to_list('%s/%s/space_twave%d.txt' % (d3, 'squid2', i)) for i in iks]
        dg2 = [[diag_res0(m0, rkss[i]) for m0 in mt2[i]] for i in range(len(mt2))]
        print('squid2 dg')
        for dg in dg2:  # 每个单参数的效用
            l0, l1 = [[dg[i] for i in idx] for idx in [idx1, idx2]]
            auc, acc, f1sc = cal_utility1(l0, l1)
            print(auc, acc, f1sc)
        dg2 = [[dg2[i][j] for i in range(len(dg2))] for j in range(len(dg2[0]))]
        dg2 = cmpr_diag1(dg2)
        # save_txt(dg2, 'list', '%s/%s/space_twavedt_%s1.txt' % (d3, 'squid2', nm1[i1]))
        l20, l21 = [[dg2[i] for i in idx] for idx in [idx1, idx2]]
        rs1, rs2, _, _ = ranks_spemt0([], [], l20, l21, 'space_twavedt_ur1', 0.95)
        print('squid2 spe', rs1, rs2)
        # sqop-合并参数/分级
        al0, al1 = l10+l20, l11+l21
        rs1, rs2, _, _ = ranks_spemt0([], [], al0, al1, 'space_twavedt_ur1', 0.95)
        print('sqop spe', rs1, rs2)
        v1 = min(al0+al1)-1
        xyss1 = make_ranks2(al0, al1, mode=0, v1=v1, lis0=[])
        rts = auto_rks0(xyss1, 'mt1')
        print('sqop rks', rts)
        
        v2 = rs1[0][0][2][0]
        n1 = sum(1 for m0 in l10 if m0>=v2)
        n2 = sum(1 for m0 in l11 if m0>=v2)
        print(get_round([n2/(n1+n2), (n1+n2)/len(idx1+idx2)], [2, 2]))
        n1 = sum(1 for m0 in l20 if m0>=v2)
        n2 = sum(1 for m0 in l21 if m0>=v2)
        print(get_round([n2/(n1+n2), (n1+n2)/len(idx1+idx2)], [2, 2]))
        n1 = sum(1 for m0 in al0 if m0>=v2)
        n2 = sum(1 for m0 in al1 if m0>=v2)
        print(get_round([n2/(n1+n2), (n1+n2)/len(idx1+idx2)/2], [2, 2]))
        vs = [v0 for v0 in rts[0] if v0 < v2] + [v2]
        rts = [vs, rts[1][:len(vs)]+[round(100*n2/(n1+n2))]]
        print(rts)
        l10, l11 = [[diag_res0(m0, rts) for m0 in l] for l in [l10, l11]]
        auc, acc, f1sc = cal_utility1(l10, l11)
        print('opm:', auc, acc, f1sc)
        l10, l11 = [[diag_res0(m0, rts) for m0 in l] for l in [l20, l21]]
        auc, acc, f1sc = cal_utility1(l10, l11)
        print('squid2:', auc, acc, f1sc)
        l10, l11 = [[diag_res0(m0, rts) for m0 in l] for l in [al0, al1]]
        auc, acc, f1sc = cal_utility1(l10, l11)
        print('sqop:', auc, acc, f1sc)
    
    return d20


def cmpr_tbo_test1(ids, d0, d1, d2):
    '''测试2个T波分布参数: mess_path, data_path, test_path'''
    tms = [gettime('%s/%s.txt' % (d1, ids[i]))[:-1] for i in range(len(ids))]
    d11 = '%s_1' % d1
    new_folder(d11)
    d21 = '%s/Tspace' % d2
    new_folder(d21)
    d22 = '%s/metrics' % d2
    new_folder(d22)
    lbs = 'model_1526'
    d4 = '%s/%s' % (d0, lbs)
    # t2s = []
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item = ids[i]
    #     f0 = '%s/%s.txt' % (d1, item)
    #     f1 = '%s/%s.txt' % (d11, item)
    #     # l1, l2 = cmpr_TPbase1(f0, f1, tms[i])  # 保存心磁
    #     t2s.append(gettime(f1)[:-1])
    # th = 20  # 峰值间距
    # ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]
    # l3s = []
    # lx = ['a', 'e', 'c', 'd', 'b']
    # msh = ['upright', 'inversion', 'posneg_bidirect', 'negpos_bidirect', 'low']
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     item = ids[i]
    #     ts = t2s[i]
    #     Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
    #     ls36 = get_mcg36('%s/%s.txt' % (d11, item), 0)
    #     l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    #     l3, l33 = [], []
    #     l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    #     M = max(get_mai0(ls36))
    #     for i in range(len(l1)):  # 基于双比例
    #         M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
    #         l2 = l1[i]
    #         v0, v1, j0, j1 = 0, 0, 0, 0
    #         ik, _ = signal.find_peaks(-np.array(l2), distance=th)
    #         l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         ik, _ = signal.find_peaks(np.array(l2), distance=th)
    #         l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         if l30:
    #             l4 = [l2[j] for j in l30]
    #             v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
    #         if l31:
    #             l4 = [l2[j] for j in l31]
    #             v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
    #         f00, f01 = v0/M, v1/M
    #         f10, f11 = abs(v0/M0), abs(v1/M0)
    #         # l3.append(cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 1))
    #         tx, _ = cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 0)
    #         zl = lx[msh.index(tx)]
    #         l33.append(zl)
    #     l3s.append(l33)
    #     g1 = '%s/%s.png' % (d21, item)
    #     # cmpr_draw6(ls36, l3, [Th, Tr], g1)  # 空间波组图
    # # save_txt(l3s, 'list', '%s/types.txt' % d2)  # 类型集
    # mtnms0 = ['space_twave%d' % i for i in range(1, 37)]
    # ty = ['a', 'b', 'c', 'd', 'e']
    # l0 = l3s
    # l0 = [[l0[i][j] for i in range(len(l0))] for j in range(len(l0[0]))]
    # for i in range(len(l0)):
    #     l1 = [ty.index(l0[i][j])+1 for j in range(len(l0[i]))]
    #     save_txt(l1, 'list', '%s/%s.txt' % (d22, mtnms0[i]))  # 指标
    rks = txt_to_list('%s/opm_rks0.txt' % d4)
    combs = txt_to_list('%s/opm_combs.txt' % d4)[1]
    rtss = txt_to_list('%s/opm_rtss.txt' % d4)
    vs, ps = [], []
    mts, dgs = [], []
    for i0 in range(2):
        iks = combs[i0]
        rts = rtss[i0]
        rkss = [rks[i-1] for i in iks]
        l1 = [txt_to_list('%s/space_twave%d.txt' % (d22, i)) for i in iks]
        mts.append(l1)
        l11 = [[diag_res0(m0, rkss[i]) for m0 in l1[i]] for i in range(len(rkss))]
        l1 = [[l11[i][j] for i in range(len(l11))] for j in range(len(l11[0]))]
        dgs.append(l1)
        l2 = cmpr_diag1(l1)
        l11 = [diag_res0(m0, rts) for m0 in l2]
        vs.append(l2)
        ps.append(l11)
    # vs = [[vs[0][i], vs[1][i]] for i in range(len(ids))]
    ps = [[ps[0][i], ps[1][i]] for i in range(len(ids))]
    strs = 'ID, upright_mt, p, inversion_mt, p'
    for i in range(len(ids)):
        strs += '\n%s, %s' % (ids[i], [[vs[i][0],ps[i][0]],[vs[i][1],ps[i][1]]])
    write_str(strs, '%s/space_twavedt_results.txt' % d2)
    mts = [[[mts[0][j][i] for j in range(len(mts[0]))], [mts[1][j][i] for j in range(len(mts[1]))]] for i in range(len(ids))]
    dgs = [[dgs[0][i], dgs[1][i]] for i in range(len(ids))]
    strs = 'ID, upright_mt, ps, inversion_mt, ps'
    for i in range(len(ids)):
        strs += '\n%s, %s' % (ids[i], [[mts[i][0],dgs[i][0]],[mts[i][1],dgs[i][1]]])
    write_str(strs, '%s/space_twavedt_mtsdgs.txt' % d2)
    
    return d2


def cmpr_tbo_test2(ids, d0, d1, d2):
    '''测试2个T波分布参数: mess_path, data_path, test_path'''
    d11 = '%s_1' % d1  # 新心磁地址
    d21, d22 = '%s/Tspace' % d2, '%s/metrics' % d2  # 空间图/指标地址
    new_folder([d11, d21, d22])
    lbs = 'model_1526'
    d4 = '%s/%s' % (d0, lbs)  # 模型信息
    tms0, tms1, mts0 = [], [], []  # 原/新时刻点集/单参数集
    th = 20  # 峰值间距
    ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]  # 单参数阈值
    msh = ['upright', 'low', 'posneg_bidirect', 'negpos_bidirect', 'inversion']
    rks = txt_to_list('%s/sqop_rks0.txt' % d4)  # 单参数分级
    combs = txt_to_list('%s/sqop_combs.txt' % d4)[1]  # 分布参数-组合
    rtss = txt_to_list('%s/sqop_rkss.txt' % d4)  # 分布参数分级
    scs, ps, mts1, dgs = [[], []], [[], []], [[], []], [[], []]
    for i in range(len(ids)):
        print('start:', i+1, 'left:', len(ids)-i)
        item = ids[i]
        tm0 = gettime('%s/%s.txt' % (d1, ids[i]))[:-1]  # 1.原时刻
        tms0.append(tm0)
        f0, f1 = '%s/%s.txt' % (d1, item), '%s/%s.txt' % (d11, item)
        _, _ = cmpr_TPbase1(f0, f1, tm0)  # 2.新心磁
        tm1 = gettime(f1)[:-1]  # 2.新时刻
        tms1.append(tm1)
        ls36 = get_mcg36(f1, 0)
        ts0 = [tm1[3], tm1[7], tm1[8], tm1[12]]
        g1 = '%s/%s.png' % (d21, item)
        ms0 = cmpr_t_mt0(ls36, g1, ts0, msh, ths, th)  # 2.空间分布图
        mts0.append(ms0)
        for i0 in range(2):
            iks, rts = combs[i0], rtss[i0]
            rkss, ms1 = [rks[i1-1] for i1 in iks], [ms0[i1-1] for i1 in iks]
            dg0 = [diag_res0(ms1[i1], rkss[i1]) for i1 in range(len(ms1))]  # 3.诊断
            sc0 = cmpr_diag1([dg0])[0]  # 3.综合得分
            p1 = diag_res0(sc0, rts)  # 3.概率
            mts1[i0].append(ms1)
            dgs[i0].append(dg0)
            scs[i0].append(sc0)
            ps[i0].append(p1)
    save_txt(tms0, 'list', '%s/tms0.txt' % d2)  # 保存时刻点
    save_txt(tms1, 'list', '%s/tms1.txt' % d2)
    for i in range(len(mts0[0])):
        lis = [mts0[j][i] for j in range(len(mts0))]
        save_txt(lis, 'list', '%s/space_twave%d.txt' % (d22, i+1))
    l0 = ['ID, upright_mts, dgs, inversion_mts, dgs']  # 保存参数和诊断s
    l0 += ['%s, %s' % (ids[i], str([mts1[0][i],dgs[0][i],mts1[1][i],dgs[1][i]])) for i in range(len(ids))]
    strs = '\n'.join(l0)
    write_str(strs, '%s/space_twavedt_mtsdgs.txt' % d2)
    l0 = ['ID, upright_sc, p, inversion_sc, p']  # 保存得分和概率
    l0 += ['%s, %s' % (ids[i], str([scs[0][i],ps[0][i],scs[1][i],ps[1][i]])) for i in range(len(ids))]
    strs = '\n'.join(l0)
    write_str(strs, '%s/space_twavedt_scp.txt' % d2)
    
    return d2


def default_disp1():
    '''离散度参数的分级结果: 20单参数、自动化分级'''
    rks = [[[0.171, 0.62, 1.103, 1.134, 1.155, 1.224, 1.268, 1.298, 1.343, 1.381, 3.26], [44, 55, 54, 53, 39, 40, 41, 42, 43, 44, 52, 58]], [[0.457, 0.485, 0.527, 0.587, 0.681, 0.772, 0.874, 0.968, 1.005, 1.016, 1.028, 1.045, 1.069, 1.095, 1.115, 1.141, 1.179, 1.214, 1.236, 1.326, 1.404, 1.462, 1.602, 1.979, 2.19, 2.695, 3.324, 3.652, 3.755], [46, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 49]], [[0.731, 1.03, 1.406, 3.283], [38, 43, 49, 58, 68]], [[0.004, 0.008, 0.017, 0.026, 0.041, 0.057, 0.078, 0.098, 0.116, 0.133, 0.145, 0.163, 0.173, 0.694, 0.824, 1.002], [53, 54, 55, 47, 46, 45, 44, 43, 57, 58, 59, 60, 61, 48, 42, 41, 61]], [[0.096, 0.605, 0.965, 0.991], [47, 46, 45, 44, 56]], [[0.014, 0.087, 0.558, 1.664, 1.73, 1.871, 2.078, 2.364, 2.685, 2.954, 3.012, 3.08, 3.139, 3.171, 3.2, 3.229, 3.247, 3.266, 3.287, 3.303, 3.791], [54, 55, 40, 46, 58, 57, 56, 55, 54, 53, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 52, 55]], [[1.272, 1.321, 1.598, 1.845, 5.567], [46, 63, 64, 65, 49, 55]], [[0.088, 0.111, 0.143, 0.166, 0.189, 0.219, 0.244, 0.253, 0.905, 1.004, 1.162, 2.616, 2.782, 3.001, 3.047, 3.079, 3.129, 3.163, 3.189, 3.222, 3.264, 3.278, 3.304, 3.342, 3.408, 3.452, 3.493, 3.534, 3.613, 3.712, 3.804, 3.867, 3.962, 4.333, 5.255, 6.269, 6.651], [34, 33, 32, 31, 30, 29, 28, 27, 57, 58, 57, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 61]], [[1.066, 1.28, 3.025], [52, 49, 44, 60]], [[1.194, 2.613], [46, 47, 59]], [[1.007, 1.586, 1.792, 1.988, 2.407, 2.707], [64, 34, 41, 48, 49, 64, 67]], [[1.169, 1.225, 1.349, 1.5, 1.698, 2.275], [44, 51, 52, 49, 52, 53, 56]], [[0.213, 0.509, 1.008, 1.058, 1.133, 1.19, 1.207, 1.225, 1.302, 1.412, 1.554, 1.577, 1.639, 1.66, 1.694, 1.793, 1.899, 1.922, 1.969, 2.184, 2.232, 2.283, 2.327, 2.389, 2.518, 2.648, 2.687, 2.72, 2.801, 3.305, 3.512, 3.613, 3.739, 3.908, 4.124, 4.366, 5.564], [94, 93, 92, 91, 90, 89, 88, 87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 77, 76, 75, 74, 73, 72, 71, 70, 69, 68, 67, 66, 44, 42, 39, 52, 38, 42, 52, 56, 59]], [[0.019, 0.176, 0.414, 0.683, 0.945, 1.078, 1.145, 1.244, 1.278, 1.356, 1.401, 1.461, 1.52, 1.552, 1.612, 1.643, 1.735, 1.802, 1.863, 1.921, 2.034, 2.066, 2.13, 2.509, 3.204, 3.401, 3.415, 3.431, 3.448, 3.837, 4.541, 4.643, 4.695, 4.745, 4.811, 4.9, 4.977, 5.061, 5.122], [74, 73, 72, 71, 70, 69, 68, 67, 66, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 48, 45, 44, 43, 42, 41, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59]], [[0.185, 0.878, 1.59], [43, 42, 54, 74]], [[0.339, 0.557, 1.999], [40, 36, 55, 72]], [[1.068, 1.498, 1.516, 1.519, 1.528, 1.533, 1.542, 1.552, 1.561, 1.577, 1.589, 1.608, 1.616, 1.629, 1.649, 1.656, 1.665, 1.69, 1.692, 1.99, 3.145, 4.752], [38, 34, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 42, 54, 65, 68]], [[0.081, 0.177, 0.27, 0.381, 0.409, 0.412, 0.427, 0.465, 0.472, 0.5, 0.522, 0.571, 0.619, 0.667, 0.705, 0.72, 0.725, 0.727], [40, 41, 42, 43, 44, 48, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 49, 71, 77]], [[0.187, 0.427, 0.67, 1.451, 1.714, 2.348], [26, 38, 42, 48, 51, 55, 76]], [[0.151, 0.982, 1.102, 1.278, 1.383, 3.006, 3.312, 4.086, 4.174, 4.291, 4.392, 4.452, 4.556, 4.628, 4.69, 4.811, 4.872, 4.964, 5.053, 5.231, 5.447, 5.551, 5.662, 5.81, 5.95, 6.087, 6.3, 6.476, 6.754, 7.303, 7.766, 8.413, 9.898], [23, 24, 25, 26, 27, 39, 46, 60, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 60]]]
    return rks


def default_twv():
    '''T波参数的默认值: 单参数分级、分布参数组合、分布参数分级'''
    rks = [[[2, 3, 4, 5], [67, 48, 47, 49, 47]], [[2, 3, 4, 5], [77, 49, 40, 47, 40]], [[2, 3, 4, 5], [96, 65, 86, 53, 35]], [[2, 3, 4, 5], [100, 82, 82, 85, 40]], [[2, 3, 4, 5], [100, 84, 77, 71, 42]], [[2, 3, 4, 5], [100, 73, 67, 78, 40]], [[2, 3, 4, 5], [57, 47, 40, 31, 53]], [[2, 3, 4, 5], [61, 44, 100, 50, 45]], [[2, 3, 4, 5], [75, 56, 65, 44, 38]], [[2, 3, 4, 5], [88, 77, 60, 54, 40]], [[2, 3, 4, 5], [96, 89, 57, 68, 41]], [[2, 3, 4, 5], [94, 77, 57, 57, 40]], [[2, 3, 4, 5], [49, 50, 40, 51, 66]], [[2, 3, 4, 5], [50, 46, 100, 43, 64]], [[2, 3, 4, 5], [55, 48, 75, 47, 45]], [[2, 3, 4, 5], [57, 55, 53, 58, 44]], [[2, 3, 4, 5], [66, 64, 86, 78, 43]], [[2, 3, 4, 5], [83, 64, 48, 59, 42]], [[2, 3, 4, 5], [45, 56, 46, 48, 84]], [[2, 3, 4, 5], [45, 68, 90, 53, 95]], [[2, 3, 4, 5], [43, 63, 82, 87, 95]], [[2, 3, 4, 5], [40, 51, 77, 68, 79]], [[2, 3, 4, 5], [40, 43, 79, 100, 65]], [[2, 3, 4, 5], [59, 47, 57, 61, 52]], [[2, 3, 4, 5], [43, 60, 83, 53, 81]], [[2, 3, 4, 5], [42, 77, 92, 77, 99]], [[2, 3, 4, 5], [41, 75, 80, 82, 97]], [[2, 3, 4, 5], [34, 65, 65, 76, 95]], [[2, 3, 4, 5], [33, 45, 60, 89, 84]], [[2, 3, 4, 5], [49, 42, 57, 75, 67]], [[2, 3, 4, 5], [39, 58, 59, 51, 81]], [[2, 3, 4, 5], [38, 69, 71, 60, 93]], [[2, 3, 4, 5], [37, 74, 69, 76, 97]], [[2, 3, 4, 5], [33, 65, 61, 81, 96]], [[2, 3, 4, 5], [33, 48, 43, 67, 82]], [[2, 3, 4, 5], [43, 44, 71, 80, 65]]]
    combs = [[3, 6, 9, 10], [21, 22, 27, 28, 29, 31, 32, 33, 34]]
    rtss = [[[1.8, 5.0], [36, 62, 96]], [[0.1, 0.3, 0.5, 0.8, 0.9, 1.0, 1.3, 1.4, 2.2, 2.4, 2.7, 2.9, 6.1], [38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 74, 95]]]
    return [rks, combs, rtss]


def default_qrswv():
    '''QRS波参数的默认值: 单参数分级、分布参数组合、分布参数分级'''
    rks = [[[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [53, 46, 53, 100, 94, 39, 51, 62, 83, 44, 45, 37]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [52, 41, 48, 82, 88, 44, 52, 69, 80, 75, 40, 42]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [73, 51, 57, 70, 78, 80, 62, 70, 93, 57, 38, 40]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [57, 91, 42, 60, 66, 100, 100, 80, 95, 82, 37, 52]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [100, 100, 59, 49, 60, 0, 100, 81, 93, 100, 35, 52]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [100, 100, 73, 49, 46, 0, 0, 66, 86, 91, 36, 50]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [48, 41, 67, 77, 100, 40, 43, 61, 81, 73, 50, 42]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [55, 37, 69, 100, 94, 40, 52, 66, 84, 90, 44, 42]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [48, 39, 51, 100, 90, 48, 63, 80, 100, 57, 44, 42]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [56, 44, 74, 51, 89, 69, 87, 91, 95, 100, 40, 52]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [63, 100, 53, 52, 56, 0, 100, 76, 93, 100, 37, 58]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [100, 100, 77, 49, 43, 0, 0, 68, 85, 78, 38, 75]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [58, 58, 59, 100, 100, 34, 39, 62, 82, 100, 54, 70]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [54, 46, 68, 100, 84, 40, 51, 63, 100, 75, 49, 71]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [46, 39, 48, 100, 100, 44, 71, 82, 100, 80, 50, 62]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [57, 37, 42, 87, 91, 65, 78, 89, 100, 100, 41, 45]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [78, 46, 47, 49, 42, 84, 70, 76, 82, 79, 42, 48]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [100, 53, 53, 37, 41, 100, 100, 64, 70, 61, 47, 59]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [65, 69, 60, 100, 100, 37, 48, 65, 88, 100, 52, 73]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [76, 78, 61, 0, 100, 39, 48, 68, 100, 100, 50, 81]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [64, 72, 42, 100, 100, 43, 55, 76, 100, 100, 42, 88]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [63, 51, 45, 88, 63, 50, 53, 81, 100, 100, 40, 42]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [82, 57, 51, 53, 49, 64, 68, 72, 60, 59, 44, 32]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [96, 100, 62, 49, 43, 77, 100, 62, 58, 57, 46, 37]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [82, 80, 70, 0, 100, 38, 46, 64, 80, 100, 48, 67]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [84, 100, 57, 0, 0, 42, 47, 64, 100, 100, 39, 82]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [77, 100, 45, 100, 0, 45, 49, 71, 100, 100, 38, 80]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [75, 55, 43, 53, 100, 55, 60, 54, 100, 100, 36, 56]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [86, 82, 53, 54, 55, 68, 84, 100, 62, 61, 38, 31]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [98, 90, 61, 55, 45, 84, 100, 73, 48, 43, 41, 39]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [82, 100, 60, 0, 0, 40, 43, 55, 100, 100, 44, 49]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [80, 89, 56, 0, 0, 42, 50, 62, 100, 100, 39, 49]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [78, 100, 50, 40, 0, 45, 55, 68, 100, 100, 36, 71]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [77, 82, 46, 67, 0, 60, 65, 60, 100, 100, 34, 54]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [82, 90, 50, 48, 71, 65, 79, 57, 79, 78, 38, 32]], [[2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], [92, 93, 56, 50, 51, 91, 25, 60, 60, 30, 41, 34]]]
    combs = [[1,2,3,4,5,6,7,8,9,10,11,12,15,16,17], [20,23,24,25,26,27,28,29,30,31,32,33,34,35,36], [1,2,3,9,10], [4,5,9,10,11,15,16,17]]
    rtss = [[[-15.9, -15.2, -14.8, -14.4, -14.3, -14.0, -13.7, -13.5, -13.3, -13.0, -12.7, -12.3, -12.1, -11.7, -11.4, -11.0, -10.7, -10.2, -9.9, -9.4, -9.1, -8.7, -8.2, -7.8, -7.4, -6.8, -6.4, -5.9, -5.4, -5.0, -4.4, -3.9, -3.4, -2.9, -2.2, -1.7, 3.3, 10.1], [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 61, 83, 95]], [[-17.1, -16.2, -15.4, -14.9, -14.4, -13.8, -13.4, -13.1, -12.6, -12.0, -11.4, -10.9, -10.4, -9.9, -9.3, -8.8, -8.3, -7.7, -7.2, -6.8, -6.0, -5.4, -4.6, -4.2, -3.5, -2.6, -1.9, -1.3, -1.2, -0.6, 0.1, 0.6, 1.9, 2.7, 3.6, 4.7, 5.6, 16.3], [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 57, 58, 59, 60, 61, 62, 63, 64, 80, 95]], [[-5.1, -4.9, -4.8, -4.6, -4.3, -4.1, -4.0, -3.8, -3.5, -3.4, -3.1, -2.8, -2.5, -2.1, -1.5, -1.1, -0.7, -0.2, 0.3, 0.4, 7.3], [26, 28, 29, 30, 31, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 72, 96]], [[0.3, 5.9], [34, 80, 95]]]
    return [rks, combs, rtss]


def if_qrsyc1(ms0):
    '''判定QRS异常波型'''
    rks, combs, rtss = default_qrswv()
    tx, lx = [0, 1, -1, 2], 0
    QRSwv0, QRSwv1 = [], []
    mts = [0, 0, 0, 0]  # 4参数
    for i0 in [0, 2, 3]:  # 上方
        iks, rts = combs[i0], rtss[i0]
        rkss, ms1 = [rks[i1-1] for i1 in iks], [ms0[i1-1] for i1 in iks]
        dg0 = [diag_res0(ms1[i1], rkss[i1]) for i1 in range(len(ms1))]  # 诊断
        sc0 = round(sum([p00/10-5 for p00 in dg0]),1)  # 评分
        mts[i0] = sc0
        if sc0>=rts[0][-1]:  # 特异性激活
            for i1 in range(len(iks)):
                if dg0[i1]>=70:  # 单参数异常
                    mg = [iks[i1], ms1[i1]]
                    if mg not in QRSwv0:  # 通道、异常类型
                        QRSwv0.append(mg)
    if QRSwv0:
        lx = lx + 1
    iks, rts = combs[1], rtss[1]  # 下方
    rkss, ms1 = [rks[i1-1] for i1 in iks], [ms0[i1-1] for i1 in iks]
    dg0 = [diag_res0(ms1[i1], rkss[i1]) for i1 in range(len(ms1))]  # 诊断
    sc0 = round(sum([p00/10-5 for p00 in dg0]),1)  # 评分
    mts[1] = sc0
    if sc0>=rts[0][-1]:  # 特异性激活
        for i1 in range(len(iks)):
            if dg0[i1]>=70:  # 单参数异常
                mg = [iks[i1], ms1[i1]]
                if mg not in QRSwv1:  # 通道、异常类型
                    QRSwv1.append(mg)
    if QRSwv1:
        lx = lx + 2
    QRSwv = match_cn1(QRSwv0+QRSwv1)
    qrsyc = round(sum([diag_res0(mts[i],rtss[i])/10-5 for i in range(len(mts))])+11, 1)
    output = {
        'space_qrswv_neg': mts[0],
        'space_qrswv_pos': mts[1],
        'space_qrswv_Qr': mts[2],
        'space_qrswv_rS': mts[3]
    }
    return [tx[lx], QRSwv, qrsyc, output]


def if_styc1(ls36, tm2):
    '''判定ST异常波型:'''
    analyzer = STSegmentAnalyzer()
    ltg, lyd, wz1, wz2 = analyzer.analyze_case(ls36, np.array(tm2))
    tx = 0
    if ltg:
        tx = 1
        if lyd:
            tx = 2
    elif lyd:
        tx = -1
    ltg = [[i1, 1] for i1 in ltg]
    lyd = [[i1, 2] for i1 in lyd]
    STwv = match_cn1(ltg+lyd)
    STreg = [wz1, wz2]
    return [tx, STwv, STreg]


def match_cn1(mg):
    '''通道的符号对应'''
    a = ['A', 'B', 'C', 'D', 'E', 'F']
    b = ['1', '2', '3', '4', '5', '6']
    ms = []
    for ik, jk in mg:
        ik -= 1
        # i, j = int(ik % 6), int(ik / 6)
        i, j =  int(ik / 6), int(ik % 6)
        tx = a[i]+b[j]
        ms.append([tx, jk])
    return ms


def if_ttyc1(ms0):
    '''判定TT异常波型'''
    rks, combs, rtss = default_twv()
    twvs = [[], []]
    tx = ['', '']
    mts = []
    for i0 in range(2):
        iks, rts = combs[i0], rtss[i0]
        rkss, ms1 = [rks[i1-1] for i1 in iks], [ms0[i1-1] for i1 in iks]
        dg0 = [diag_res0(ms1[i1], rkss[i1]) for i1 in range(len(ms1))]  # 3.诊断
        sc0 = round(sum([max(p0/10-6,0) for p0 in dg0]),1)  # 3.综合得分
        mts.append(sc0)
        if sc0>=rts[0][-1]:  # 特异性激活
            for i1 in range(len(iks)):
                if dg0[i1]>=70:  # 单参数异常
                    mg, tx0 = [iks[i1], ms1[i1]], str(ms1[i1])
                    if mg not in twvs[i0]:  # 通道、异常类型
                        twvs[i0].append(mg)
                    if tx0 not in tx[i0]:  # 异常类型
                        tx[i0] += tx0
    for i in range(2):
        if tx[i]:
            tx[i] = int(tx[i])
        else:
            tx[i] = 0
    twvs = match_cn1(twvs[0]+twvs[1])  # 匹配通道符号
    ttyc = round(sum([diag_res0(mts[i],rtss[i])/10-5 for i in range(len(mts))])+3, 1)
    output = {
        'space_twavedt_ur': mts[0],
        'space_twavedt_iv': mts[1]
    }
    return [tx[0], tx[1], twvs, ttyc, output]


# def bxms1(ls36, qrs_out, tm1, idxs):
def bxms1(filename, idxs):
    '''波形描述: '''
    output = {}
    ls36 = get_mcg36(filename, 0)
    tm0 = gettime(filename)[:-1]
    tm0 = cal_PhPr0(ls36, tm0)  # 时刻点-时相参数
    tm0 = finetune_tms2(tm0, idxs)  # 修正时刻点
    tmps = cal_tmps1(tm0, ls36)  # 时相参数
    output.update(tmps)
    lns = get_lines1(filename)
    # l0 = ['timephase_HR', 'timephase_P', 'timephase_PR', 'timephase_QRS', 'timephase_QT', 'timephase_QTc', 'amplitude_QRS', 'amplitude_RT']
    # rt1 = [tmps[i] for i in l0]
    # print(rt1)
    ls36n, lnsn = cmpr_TPbase1(filename, '', tm0)  # TP基线微调
    tm1 = gettime(lnsn)[:-1]
    ths1, dhs1 = [0.1, 0.3, 0.13, 0.7, 0.045], [55, 40, 10, 60]
    t0 = round(20*len(lns)/600)  # 前后延长20ms(心率60)
    ts1 = [tm1[3]-t0, tm1[7]+t0]
    qrswv0, qhs, srs, sr0 = cmpr_qrs_mt1(ls36n, ts1, ths1, dhs1, th1=5)
    tm2 = [tm1[0], tm1[7], tm1[8], tm1[12]]  # ST段输入时刻=Ph/Sr/Th/Tr
    ts0 = [tm1[3], tm1[7], tm1[8], tm1[12]]  # T波段输入时刻=Qh/Sr/Th/Tr
    ths0 = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]  # T单参数阈值
    twv0 = cmpr_t_mt1(ls36n, ts0, ths0, th=20)  # T波类型
    qhsrs = [[int(qhs[i0]), int(srs[i0])] for i0 in range(len(qhs))]  # QRS起止点
    stt = [int(sr0), int(tm1[8]), int(tm1[12])]  # 统一Sr/Th/Tr=ST和T波的起始和结束
    stt.insert(1, min(stt[0]+80, stt[1]))
    QRSyc = [0, 0, 0]
    if tmps['timephase_QRS']>120:
        QRSyc[0] = 1
    fw1 = [3.17, 36.55]  # 正常范围
    if tmps['amplitude_QRS']<fw1[0]:
        QRSyc[1] = -1
    elif tmps['amplitude_QRS']>=fw1[1]:
        QRSyc[1] = 1
    QRSyc[2], QRSwv, qrsyc, qrsps = if_qrsyc1(qrswv0)
    STTyc = [0, '', '']
    STTyc[0], STwv, STreg = if_styc1(ls36n, tm2)
    STTyc[1], STTyc[2], Twv, ttyc, ttps = if_ttyc1(twv0)
    output.update({
        'QRSyc': QRSyc,
        'QRSwv': QRSwv,
        'qrsyc': qrsyc,
        'STTyc': STTyc,
        'STwv': STwv,
        'STreg': STreg,
        'Twv': Twv,
        'ttyc': ttyc,
        'stt': stt,
        'qhsrs': qhsrs
    })
    return output


def get_jdzd(l11, t):
    '''列表的极大值的最大值'''
    ts, vs = [], []
    for t2 in range(1, len(l11)-1):
        if l11[t2]>l11[t2-1] and l11[t2]>l11[t2+1]:
            ts.append(t2)
            vs.append(l11[t2])
    if vs:
        t1 = ts[vs.index(max(vs))]+t
        return [t1]
    else:
        return []


def get_Pp1(l0, t1, Qh):
    '''获取Pp, t1原时刻点, t0=Qh'''
    t3 = []
    t0 = max(0,Qh-200)
    l11 = [l0[i] for i in range(t0, Qh)]
    t2 = l11.index(max(l11))
    if t2>0 and t2<Qh-t0-1:
        if l11[t2]>l11[t2-1] and l11[t2]>l11[t2+1]:
            t3 = [t2+t0]
    if t3 == []:
        t3 = get_jdzd(l11, t0)
    if t3:
        return t3[0]
    else:
        return t1


def get_jxdj(lis, l11):
    '''极小值递减'''
    vms0 = []
    for t2 in lis:
        if l11[t2]<l11[t2-1] and l11[t2]<l11[t2+1]:
            if vms0 == []:
                vms0.append([t2, l11[t2]])
            elif l11[t2]<vms0[-1][1]:
                vms0.append([t2, l11[t2]])
    return vms0


def cal_p_pr0(vms0, vms1, Qh):
    '''添加P波段和PR间期限制: PR=120~200, P=80~110'''
    if vms0:
        ts = [i for i in range(len(vms0)) if vms0[i][0]>Qh-200 and vms0[i][0]<Qh-120]
        if ts:
            vms0 = [vms0[i] for i in ts]
            if vms1:
                t1 = min([v[0] for v in vms0])+80
                t2 = max([v[0] for v in vms0])+110
                ts = [i for i in range(len(vms1)) if vms1[i][0]>t1 and vms1[i][0]<t2]
                if ts:
                    vms1 = [vms1[i] for i in ts]
    return [vms0, vms1]


def get_jxwd(vms, l0):
    '''获取稳定极小值点'''
    if len(vms) == 1:
        l0 = [vms[0][0]]
    elif len(vms)>1:
        cb = [vms[i+1][1]/vms[i][1] for i in range(len(vms)-1)]
        if min(cb)<0.55:
            for i in range(len(cb)):
                if cb[len(cb)-1-i]<0.55:
                    l0 = [vms[i+1][0]]
                    break
        elif min(cb)>0.8:
            l0 = [vms[0][0]]
        else:
            l0 = [vms[0][0]]
            for i in range(len(cb)):
                if cb[i]>0.8:
                    break
                l0.append(vms[i+1][0])
    return l0


def cal_PhPr0(ls36, tms):
    '''PhPr计算'''
    l0 = [max([abs(ls36[j][i]) for j in range(36)]) for i in range(len(ls36[0]))]
    Pp = get_Pp1(l0, tms[1], tms[3])
    tms[1] = Pp
    vms0, vms1 = [], []
    lis = list(range(max(1,Pp-90), max(1,Pp-30)))
    lis.reverse()
    vms0 = get_jxdj(lis, l0)
    lis = list(range(min(tms[3]-1,Pp+30), min(tms[3]-1,Pp+90)))
    vms1 = get_jxdj(lis, l0)
    vms0, vms1 = cal_p_pr0(vms0, vms1, tms[3])
    phs = get_jxwd(vms0, [max(1,Pp-50)])
    prs = get_jxwd(vms1, [min(tms[3]-1,Pp+50)])
    tps = [[t1, t2] for t1 in phs for t2 in prs]
    sc = [abs(t2-t1-95) for t1,t2 in tps]
    tms[0], tms[2] = tps[sc.index(min(sc))]
    return tms


def change_jd0(a):
    '''调整角度范围: 0-180dy0--180, 360-180dy0-180'''
    if a>=180:
        return round(360-a, 1)
    else:
        return round(-a, 1)


def cal_jj0(a1, a2):
    '''计算夹角'''
    a=a1-a2
    if a>180:
        a=a-360
    elif a<-180:
        a=a+360
    return a


def change_jd1(a):
    '''调整角度范围: 0--180dy0-180, 0-180dy360-180'''
    if a>=0:
        return round(360-a, 1)
    else:
        return round(-a, 1)


def change_jd2(a):
    '''调整角度范围: 0-90dy0--90, 90-360dy270-0'''
    if a>=90:
        return round(360-a, 1)
    else:
        return round(-a, 1)


def change_jd3(a):
    '''调整角度范围: 0-180对应 0--180, 180-360对应180-0, 0--180对应0-180, -180--360对应-180-0'''
    if a<=-180:
        return round(-360-a, 1)
    elif a<=180:
        return round(-a, 1)
    else:
        return round(360-a, 1)


def cal_qrzdmts0(M0, Qp, Rp):
    '''QR转动单参数'''
    alis, rlis = [], []
    bl = (Rp-Qp)/7
    for i in range(8):
        px, py, nx, ny = get_dipole(M0[round(Qp+bl*i-1)])
        alis.append(calangle(ny - py, px - nx))
        if i > 0:
            rlis.append(cal_jj0(alis[-2], alis[-1]))
    qra = abs(cal_jj0(alis[0], alis[-1]))
    qa = change_jd0(alis[0])
    return get_round([qa, qra, max([0]+rlis)], [1, 1, 1])


# def cjms1(tm1, Ms, lns, Zqr, Zrs, Ztt, idxs):
def cjms1(filename, idxs, Zqr, Zrs, Ztt):
    '''磁极描述: '''
    output = {}
    ls36 = get_mcg36(filename, 0)
    tm0 = gettime(filename)[:-1]
    tm0 = cal_PhPr0(ls36, tm0)  # 时刻点-时相参数
    tm0 = finetune_tms2(tm0, idxs)  # 修正时刻点
    lns = get_lines1(filename)
    Ms = cal_interpolate(filename)
    ts0 = [tm0[i] for i in [4, 5, 6, 9, 10, 11]]
    disps = cal_disps(Ms, ts0)
    output.update(disps)
    # 其他参数-评分
    mfmls, _, _, _, _, tttj = default_nms0()
    l0 = get_mfm_mts_intp_post(ts0, Ms, lns, Zqr, Zrs, Ztt)
    l50 = get_traject_mts0(Ms, ts0, get_mfs(lns, md=2))
    nms = hb_lis0(mfmls+tttj)
    mts = hb_lis0(l0+l50)
    zd1 = {k: v for k, v in zip(nms, mts) if k != ''}  # 全部原参数
    nms0, rks, combs, sz = default_nms1()
    l0 = [zd1[k] for k in nms0]  # 相关单参数
    dg0 = [diag_res0(l0[i], rks[i]) for i in range(len(l0))]  # 诊断
    mts0 = []
    for i1 in range(len(combs)):
        comb = combs[i1]
        sc0 = round(max(0, sum([dg0[i-1]/10-5 for i in comb])+sz[i1]), 1)
        mts0.append(sc0)
    # QRS磁场角度-单参数激活
    txs = ['mfm_QR_qra', 'mfm_RS_rsa', 'mfm_QR_qa', 'mfm_QR_ra', 'mfm_RS_sa']
    l0 = [zd1[k] for k in txs]
    l1 = l0[:2]
    l1 += [change_jd0(l0[i]) for i in range(2,5)]
    l0 = l1
    qrsjd = [0, 0, 0, 0, 0]
    rgs = [[69.2, 221.0], [115.3, 256.8], [9.5, 146.3], [-83.1, -4.5]]  # 正常范围
    rgs1 = [-151.2, 37.4]  # sa异常范围
    for i in range(len(l0)-1):
        if l0[i]<rgs[i][0] or l0[i]>rgs[i][1]:
            qrsjd[i] = 1
    if l0[4]>rgs1[0] and l0[4]<rgs1[1]:
        qrsjd[4] = 1
    qrsjd.append(max(qrsjd))
    # QR转动异常类型识别
    qa, qra, cwa = cal_qrzdmts0(Ms, tm0[4], tm0[5])
    if qra <= 27.6:  # 不转动
        qrzd = 2
    elif qra <= 43.2:  # 轻微转动
        qrzd = 3
    elif cwa >= 87.8:  # 顺时针
        qrzd = 4
    elif qa<=-60.6 or qa>=163.3:  # 混乱
        qrzd = 5
    else:
        qrzd = 1
    # TT磁场角度-单参数激活
    ttjd = []
    if mts0[3]>=12.2:
        m0 = zd1['mfm_TT_ta']
        if m0<43.3 or m0>97.3:
            ttjd = [1]
        else:
            if dg0[21]>50:
                ttjd = [1, 2]
            else:
                ttjd = [2]
    # # T波段磁极角度稳定性
    # tstb = 0
    # if zd1['mfm_TT_tta'] >= 55.7:
    #     tstb = 1
    # QRS/T波平均磁场角度, QRS-T夹角
    qrsag = cal_meana1(Ms, tm0[3], tm0[7])
    ttag = cal_meana1(Ms, tm0[8], tm0[12])
    qrsttag = change_jd3(qrsag-ttag)
    qrsag = change_jd0(qrsag)
    ttag = change_jd0(ttag)
    output.update({
        'QRS_center': mts0[0],
        'T_pos_trajectory': mts0[1],
        'T_center': mts0[2],
        'QRS_angle': qrsjd,
        'QRS_ave_angle': qrsag,
        'QR_rot_angle': qra,
        'QR_rotation': qrzd,
        'T_angle': mts0[3],
        'T_angle_type': ttjd,
        'T_ave_angle': ttag,
        'T_angle_stable': zd1['mfm_TT_tta'],
        'QRS_T_ave_angle': qrsttag
    })
    return output


def xiuzheng1(paras):
    if paras == {}:  # 默认值-正常
        paras = {
            'Q_index': -0.022053412822669,
            'S_index': 0.843123855929008,
            'TT_index': 1.83742183051828,
            'Q_angle': 102.1,
            'R_angle': -11.8,
            'S_angle': 160.3,
            'T_angle': -52.5,
            'QRyc': 0,
            'RSyc': 0,
            'Tyc': 0,
            'score': 0.210579261183739}
    tx = ['Q_index', 'S_index', 'TT_index', 'Q_angle', 'R_angle', 'S_angle', 'T_angle', 'QRyc', 'RSyc', 'Tyc']
    jds = [3, 3, 3, 1, 1, 1, 1, 0, 0, 0]
    for x, j in zip(tx, jds):
        paras[x] = round(paras[x], j)
    paras['score'] = round(paras['score'])
    return paras


def zhjg1(paras, waveform, mcgpole):
    '''计算心磁图综合结果'''
    paras = xiuzheng1(paras)
    tx0 = ['Q_index', 'S_index', 'TT_index', 'Q_angle', 'R_angle', 'S_angle', 'T_angle']
    Q_index, S_index, TT_index, Qa, Ra, Sa, Ta = [paras[x] for x in tx0]
    tx1 = ['qrsyc', 'ttyc', 'QRS_p_disp', 'QRS_n_disp', 'T_p_disp', 'T_n_disp', 'QRS_center', 'T_center', 'T_angle', 'T_angle_stable', 'T_ave_angle', 'QRS_T_ave_angle', 'QR_rot_angle']
    mcgpole.update(waveform)
    qrsyc, ttyc, QRS_p_disp, QRS_n_disp, T_p_disp, T_n_disp, QRS_center, T_center, T_angle, T_angle_stable, T_ave_angle, QRS_T_ave_angle, QR_rot_angle = [mcgpole[x] for x in tx1]
    mts = [qrsyc, ttyc, QRS_p_disp, QRS_n_disp, T_p_disp, T_n_disp, QRS_center, T_center, T_angle, T_angle_stable, Q_index, S_index, TT_index, T_ave_angle, QRS_T_ave_angle, Qa, Ra, Sa, Ta, QR_rot_angle]
    l4 = cal_yc1(mcgpole)
    # print(mcgpole)
    # print('统计异常信息', l4)
    tx2 = ['QRyc', 'RSyc', 'Tyc']
    l4 += [paras[x] for x in tx2]
    tys = [[[], [], [24.3]], [[], [], [7.0]], [[], [], [11.3]], [[], [], [11.7]], [[], [], [13.4]], [[], [], [14.1]], [[], [], [10.7]], [[], [], [11.2]], [[], [], [12.2]], [[], [], [55.7]], [[], [], [-0.013]], [[0.32], [], []], [[], [], [8.579]], [[-99.5], [], [-35.2]], [[-92.8], [], []], [[9.5], [], [146.3]], [[-83.1], [], [-4.5]], [[], [-151.2, 37.4], []], [[-97.3], [], [-43.3]], [[43.2], [], []]]
    qtys = [[[], [], [27.9]], [[], [], [12.1]], [], [], [], [], [], [], [], [], [], [[0.094], [], []], [], [[-107.8], [], [-8.5]], [], [[], [-148.1, -60.6], []], [[-95.9], [], [10.3]], [[], [-125, -52.4], []], [[-113.0], [], [-8.1]], []]
    l1 = cal_tyx0(mts, qtys)
    # print('qtyx', l1)
    l2 = cal_tyx0(mts, tys)
    # print('tyx', l2)
    n1, n2, n4 = sum(l1), sum(l2), sum(l4)
    # print('allinall:', n1, n2, paras['score'], n4)
    rk = cal_cd1(n1, n2, paras['score'], n4)
    output = {'Result':rk}
    return output


def cal_cd1(n1, n2, s3, n4):
    '''心磁图综合结果: n1强特异性数量/n2特异性数量/s3得分/n4异常数量'''
    s3 = round(10*s3)
    if n1>=2:
        return 3
    elif n2>=2 or s3>=9 or n4>=5:
        return 2
    elif n1==0 and s3<7 and n4<2:
        return 0
    else:
        return 1


def cal_tyx0(mt, tys):
    '''3区间的特异性激活计算: [[a0],[a1,a2],[a3], <=, >=&<=, >='''
    l0 = [0 for _ in range(len(mt))]
    for i in range(len(mt)):
        rk = tys[i]
        m0 = mt[i]
        if rk:
            if rk[0]:
                if m0<rk[0][0]:
                    l0[i] = 1
            if rk[1]:
                if m0>rk[1][0] and m0<rk[1][1]:
                    l0[i] = 1
            if rk[2]:
                if m0>rk[2][0]:
                    l0[i] = 1
    return l0


def cal_yc1(mcgpole):
    '''统计异常信息'''
    l4 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    if mcgpole['QRSyc'] != [0,0,0]:
        l4[0] = 1
    if mcgpole['STTyc'][0]:
        l4[1] = 1
    if mcgpole['STTyc'][1:] != [0, 0]:
        l4[2] = 1
    if mcgpole['QRS_p_disp_type']+mcgpole['QRS_n_disp_type'] != []:
        l4[3] = 1
    if mcgpole['T_p_disp_type']+mcgpole['T_n_disp_type'] != []:
        l4[4] = 1
    if mcgpole['T_pos_trajectory']>=12.5:
        l4[5] = 1
    if mcgpole['QRS_center']>=10.7:
        l4[6] = 1
    if mcgpole['T_center']>=11.2:
        l4[7] = 1
    if mcgpole['QRS_angle'][5]:
        l4[8] = 1
    if mcgpole['QR_rotation']!=1:
        l4[9] = 1
    if mcgpole['T_angle_type'] or mcgpole['T_angle_stable']>=55.7:
        l4[10] = 1
    return l4


def lsds1(filename, idxs):
    '''计算离散度:Q/R/S/T波, 正负, 离散类型'''
    output = {}
    if idxs:
        Qp, Rp, Sp, To, Tp, Te = [idxs[x] for x in ['Q', 'R', 'S', 'T_on', 'T', 'T_end']]
    else:
        ls36 = get_mcg36(filename, 0)
        tm0 = gettime(filename)[:-1]
        tm0 = cal_PhPr0(ls36, tm0)  # 时刻点-时相参数
        Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr = tm0
    Ms = cal_interpolate(filename)
    disps = []
    for t0 in [Qp, Rp, Sp, Tp]:
        dpn = []
        Z = norms0(Ms[t0-1])
        ls = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3]#, 0.2, 0.1]
        zbs, qts = cal_zb_qt0(ls, Z)
        lis0 = cal_jh_jl_xt0(zbs, qts, Z)
        dpn.append(round(lis0[-1][-1], 3))
        if max(lis0[-2]) == 0:
            dpn.append([])
        else:
            dpn.append([round(v0/sum(lis0[-2]),3) for v0 in lis0[-2]])
        ls = [-0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3]#, -0.2, -0.1]
        zbs, qts = cal_zb_qt0(ls, Z)
        lis0 = cal_jh_jl_xt0(zbs, qts, Z)
        dpn.append(round(lis0[-1][-1], 3))
        if max(lis0[-2]) == 0:
            dpn.append([])
        else:
            dpn.append([round(v0/sum(lis0[-2]),3) for v0 in lis0[-2]])
        disps.append(dpn)
    output.update({
        'Q_disp_p_n': disps[0],
        'R_disp_p_n': disps[1],
        'S_disp_p_n': disps[2],
        'T_disp_p_n': disps[3]
    })
    return output


def cal_disps(Ms, ts0):
    '''QRS/T波段正/负磁极离散度'''
    output = {}
    Qp, Rp, Sp, To, Tp, Te = ts0
    ts = [[Qp, Sp], [To, Te]]
    ls1, ls2 = get_disp1(Ms, ts)  # 单帧离散度
    l0, l01 = get_mt0(ls1, ls2, [Qp, Rp, Sp, To, Tp, Te])  # 离散度单参数、类型
    rks = default_disp1()
    combs = [[1, 2, 3, 4, 5, 6, 7], [8, 9, 10, 11, 12, 13, 14], [15, 16, 17], [18, 19, 20]]
    dg0 = [diag_res0(l0[i], rks[i]) for i in range(len(l0))]  # 诊断-单参数
    rk2 = [11.3, 11.7, 13.4, 14.1]
    mts, disps = [], []
    for i1 in range(len(combs)):
        comb = combs[i1]
        sc0 = round(max(0, sum([dg0[i-1]/10-5 for i in comb])+7), 1)  # 综合评分
        mts.append(sc0)
        l2 = []
        if sc0>=rk2[i1]:  # 特异性激活
            ps = [dg0[i-1] for i in comb]  # 概率
            lx = [l01[i-1] for i in comb]  # 离散度类型集
            rt0 = sorted(enumerate(ps), key=lambda x: x[1], reverse=True)
            idx = [x[0] for x in rt0]
            ps = [x[1] for x in rt0]
            lx = [lx[i] for i in idx]
            for i in range(len(ps)):
                if max(lx[i])>0:
                    fg = int(ps[i]/10)
                    l2 = [lx[i1] for i1 in range(i,len(ps)) if int(ps[i1]/10)==fg and max(lx[i1])>0]
                    break
            if l2:
                l2 = [[l2[i][j]/sum(l2[i]) for j in range(len(l2[i]))] for i in range(len(l2))]
                l2 = [sum([l2[i][j] for i in range(len(l2))]) for j in range(len(l2[0]))]
                int(l2.index(max(l2))+1)
            else:
                sc0 = rk2[i1]-0.1
        disps.append([sc0, l2])
    output.update({
        'QRS_p_disp': disps[0][0],
        'QRS_p_disp_type': disps[0][1],
        'QRS_n_disp': disps[1][0],
        'QRS_n_disp_type': disps[1][1],
        'T_p_disp': disps[2][0],
        'T_p_disp_type': disps[2][1],
        'T_n_disp': disps[3][0],
        'T_n_disp_type': disps[3][1]
    })
    return output


def get_mt0(ls1, ls2, ts):
    '''从离散度1列表中提取单参数: Q/R/S/QR均值/RS均值/QR最大值/RS最大值、负磁极、T/TT均值/TT最大值、负磁极'''
    # 离散度
    Qp, Rp, Sp, To, Tp, Te = ts
    dp1, dn1 = ls1[0]  # QRS正磁极和负磁极
    dp1, dp2 = dp1[:Rp-Qp+1], dp1[Rp-Qp:]
    dn1, dn2 = dn1[:Rp-Qp+1], dn1[Rp-Qp:]
    dp3, dn3 = ls1[1]
    mt0 = [dp1[0], dp2[0], dp2[-1], np.average(dp1), np.average(dp2), max(dp1), max(dp2)]  # Q/R/S/QR/RS均值/最大值
    j01, j02 = dp1.index(max(dp1)), dp2.index(max(dp2))
    mt1 = [dn1[0], dn2[0], dn2[-1], np.average(dn1), np.average(dn2), max(dn1), max(dn2)]  # 负磁极
    j11, j12 = dn1.index(max(dn1)), dn2.index(max(dn2))
    mt2 = [dp3[Tp-To], np.average(dp3), max(dp3)]  # T/TT均值/最大值
    mt3 = [dn3[Tp-To], np.average(dn3), max(dn3)]  # 负磁极
    j2, j3 = dp3.index(max(dp3)), dn3.index(max(dn3))
    # 离散类型
    dp1, dn1 = ls2[0]  # QRS正磁极和负磁极
    dp1, dp2 = dp1[:Rp-Qp+1], dp1[Rp-Qp:]
    dn1, dn2 = dn1[:Rp-Qp+1], dn1[Rp-Qp:]
    dp3, dn3 = ls2[1]
    jz01 = [np.average([dp1[j][i] for j in range(len(dp1))]) for i in range(3)]
    jz02 = [np.average([dp2[j][i] for j in range(len(dp2))]) for i in range(3)]
    nt0 = [dp1[0], dp2[0], dp2[-1], jz01, jz02, dp1[j01], dp2[j02]]
    jz11 = [np.average([dn1[j][i] for j in range(len(dn1))]) for i in range(3)]
    jz12 = [np.average([dn2[j][i] for j in range(len(dn2))]) for i in range(3)]
    nt1 = [dn1[0], dn2[0], dn2[-1], jz11, jz12, dn1[j11], dn2[j12]]  # 负磁极
    jz2 = [np.average([dp3[j][i] for j in range(len(dp3))]) for i in range(3)]
    jz3 = [np.average([dn3[j][i] for j in range(len(dn3))]) for i in range(3)]
    nt2 = [dp3[Tp-To], jz2, dp3[j2]]  # T/TT均值/最大值
    nt3 = [dn3[Tp-To], jz3, dn3[j3]]  # 负磁极
    l1 = [round(m0, 3) for m0 in mt0+mt1+mt2+mt3]
    l2 = [get_round(n0, [3,3,3]) for n0 in nt0+nt1+nt2+nt3]
    return [l1, l2]


def get_disp1(matrixes, ts):
    '''计算离散度1: 对时刻段ts=[[t1,t2], ...]'''
    ls1 = []
    ls3 = []
    for t1, t2 in ts:
        ls2 = [[], []]
        ls4 = [[], []]
        for t0 in range(t1, t2+1):
            Z = norms0(matrixes[t0])
            ls = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3]#, 0.2, 0.1]
            zbs, qts = cal_zb_qt0(ls, Z)
            lis0 = cal_jh_jl_xt0(zbs, qts, Z)
            dp = round(lis0[-1][-1], 3)
            ls2[0].append(dp)
            ls4[0].append(lis0[-2])
            ls = [-0.9, -0.8, -0.7, -0.6, -0.5, -0.4, -0.3]#, -0.2, -0.1]
            zbs, qts = cal_zb_qt0(ls, Z)
            lis0 = cal_jh_jl_xt0(zbs, qts, Z)
            dn = round(lis0[-1][-1], 3)
            ls2[1].append(dn)
            ls4[1].append(lis0[-2])
        ls1.append(ls2)
        ls3.append(ls4)
    return [ls1, ls3]


def cal_tmps1(tm1, ls36):
    '''波形描述-时相参数: HR/P/PR/QRS/QT/QTc、QRS-AP、R/T'''
    Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr = tm1
    RR = len(ls36[0])
    l0 = hb_lis0([[ls36[j][i] for i in range(Qh, Sr+1)] for j in range(36)])
    QRSp, QRSn = max(max(l0), 0), min(min(l0), 0)
    l0 = [ls36[i][Rp] for i in range(36)]  # 最大正+最大负
    Rzf = max(0, max(l0)) - min(0, min(l0))
    l0 = [ls36[i][Tp] for i in range(36)]
    Tzf = max(0, max(l0)) - min(0, min(l0))
    output = {}
    output['timephase_HR'] = round(60 * 1000 / RR, 3)
    output['timephase_P'] = int(Pr - Ph)
    output['timephase_PR'] = int(Qh - Ph)
    output['timephase_QRS'] = int(Sr - Qh)
    output['timephase_QT'] = int(Tr - Qh)
    output['timephase_QTc'] = round(output['timephase_QT'] / np.sqrt(RR / 1000), 3)
    output['amplitude_QRS'] = round(QRSp - QRSn, 3)
    output['amplitude_RT'] = 0
    if Tzf:
        output['amplitude_RT'] = round(Rzf / Tzf, 3)
    return output


def finetms1(tm1, qhs, srs):
    '''时刻点调整'''
    Qh0, Sr0 = min(qhs), max(srs)
    tm1[3] = min(Qh0, tm1[3])
    tm1[7] = max(Sr0, tm1[7])
    return tm1


def vis_twave1(f0, f1, d0, d1):
    '''T波异常点亮-一次性: f0/f1心磁, mess_path, test_path'''
    rks = txt_to_list('%s/model_1526/twave/sqop_rks0.txt' % d0)  # 单参数分级
    ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]  # 单参数阈值
    combs = [[3, 6, 9, 10], [21, 22, 27, 28, 29, 31, 32, 33, 34]]
    rtss = [[[1.8, 5.0], [36, 62, 96]], [[0.1, 0.3, 0.5, 0.8, 0.9, 1.0, 1.3, 1.4, 2.2, 2.4, 2.7, 2.9, 6.1], [38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 74, 95]]]
    tm0 = gettime(f0)[:-1]
    # ls36 = cmpr_TPbase1(f0, '', tm0)
    _, _ = cmpr_TPbase1(f0, f1, tm0)
    ls36 = get_mcg36(f1, 0)
    tm1 = gettime(f1)[:-1]
    print(tm1)
    ts0 = [tm1[3], tm1[7], tm1[8], tm1[12]]  # Qp/Sr/Th/Tr
    ms0 = cmpr_t_mt1(ls36, ts0, ths, th=20)
    print('time', ts0[2:])
    for i0 in range(2):
        iks, rts = combs[i0], rtss[i0]
        rkss, ms1 = [rks[i1-1] for i1 in iks], [ms0[i1-1] for i1 in iks]
        dg0 = [diag_res0(ms1[i1], rkss[i1]) for i1 in range(len(ms1))]  # 3.诊断
        sc0 = round(sum([max(p0/10-6,0) for p0 in dg0]),1)  # 3.综合得分
        if sc0>=rts[0][-1]:
            l00 = [[iks[i1], ms1[i1], dg0[i1]] for i1 in range(len(iks)) if dg0[i1]>=70]
            print(l00)
        p1 = diag_res0(sc0, rts)
        print(dg0, sc0, p1)
    return d1


def vis_qrswv1(f0, f1, d0, d1):
    '''QRS波异常点亮-一次性: f0/f1心磁, mess_path, test_path'''
    rks = txt_to_list('%s/model_1526/qrswv/sqop_rks0.txt' % d0)  # 单参数分级
    ths, dhs = [0.1, 0.3, 0.13, 0.7, 0.045], [55, 40, 10, 60]
    combs = [[1,2,3,4,5,6,7,8,9,10,11,12,15,16,17], [20,23,24,25,26,27,28,29,30,31,32,33,34,35,36], [1,2,3,9,10], [4,5,9,10,11,15,16,17]]
    rtss = [[[-15.9, -15.2, -14.8, -14.4, -14.3, -14.0, -13.7, -13.5, -13.3, -13.0, -12.7, -12.3, -12.1, -11.7, -11.4, -11.0, -10.7, -10.2, -9.9, -9.4, -9.1, -8.7, -8.2, -7.8, -7.4, -6.8, -6.4, -5.9, -5.4, -5.0, -4.4, -3.9, -3.4, -2.9, -2.2, -1.7, 3.3, 10.1], [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 61, 83, 95]], [[-17.1, -16.2, -15.4, -14.9, -14.4, -13.8, -13.4, -13.1, -12.6, -12.0, -11.4, -10.9, -10.4, -9.9, -9.3, -8.8, -8.3, -7.7, -7.2, -6.8, -6.0, -5.4, -4.6, -4.2, -3.5, -2.6, -1.9, -1.3, -1.2, -0.6, 0.1, 0.6, 1.9, 2.7, 3.6, 4.7, 5.6, 16.3], [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 57, 58, 59, 60, 61, 62, 63, 64, 80, 95]], [[-5.1, -4.9, -4.8, -4.6, -4.3, -4.1, -4.0, -3.8, -3.5, -3.4, -3.1, -2.8, -2.5, -2.1, -1.5, -1.1, -0.7, -0.2, 0.3, 0.4, 7.3], [26, 28, 29, 30, 31, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 72, 96]], [[0.3, 5.9], [34, 80, 95]]]
    tm0 = gettime(f0)[:-1]
    _, _ = cmpr_TPbase1(f0, f1, tm0)
    # tpqrs = ['R', 'RR', 'qR', 'QR', 'Qr', 'Rs', 'RS', 'rS', 'Q', 'QQ', 'QRS', 'QRSs']
    th0 = 20  # 前后延长20ms(心率60)
    t0 = round(th0*len(get_lines1(f1))/600)
    ls36 = get_mcg36(f1, 0)
    tm1 = gettime(f1)[:-1]
    ts0 = [tm1[3]-t0, tm1[7]+t0]
    ms0, qhs, srs = cmpr_qrs_mt1(ls36, ts0, ths, dhs, th1=5)
    tm2 = [[qhs[i0], srs[i0]] for i0 in range(len(qhs))]
    print('time', tm2)
    for i0 in range(4):
        iks, rts = combs[i0], rtss[i0]
        rkss, ms1 = [rks[i1-1] for i1 in iks], [ms0[i1-1] for i1 in iks]
        dg0 = [diag_res0(ms1[i1], rkss[i1]) for i1 in range(len(ms1))]  # 3.诊断
        sc0 = round(sum([p00/10-5 for p00 in dg0]),1)  # 3.综合得分
        if sc0>=rts[0][-1]:
            l00 = [[iks[i1], ms1[i1], dg0[i1]] for i1 in range(len(iks)) if dg0[i1]>=70]
            print(l00)
        p1 = diag_res0(sc0, rts)
        print(dg0, sc0, p1)
    return d1


def cmpr_TPbase1(f0, f1, tms):
    '''TP段基线微调-分通道'''
    ls36 = get_mcg36(f0, 0)
    Tr, Ph = tms[12], tms[0]
    jxs, Ms, ls2 = [], [], []
    for j in range(len(ls36)):
        l0 = ls36[j]
        M = max(max(l0), -min(l0))
        l1, l2 = l0[Tr:], l0[:Ph]
        m0 = round(cal_iqr0(l1+l2), 6)
        Ms.append(m0)  # 基线
        jx = 0  # 基线TP差
        if l1!=[] and l2!=[]:
            m1, m2 = cal_iqr0(l1), cal_iqr0(l2)
            jx = round(abs(m1-m2)/M, 6)
        jxs.append(jx)
        ls2.append([l0[k]-m0 for k in range(len(l0))])
    if f1:
        ls2 = [[k]+[ls2[j][k] for j in range(len(ls2))] for k in range(len(ls2[0]))]
        l0 = ['\t'.join(format(ls2[k][j],'.6f') for j in range(len(ls2[k]))) for k in range(len(ls2))]
        write_str('\n'.join(l0), f1)  # 保存心磁
        return [jxs, Ms]
    else:
        ls1 = ls2.copy()
        ls2 = [[k]+[ls2[j][k] for j in range(len(ls2))] for k in range(len(ls2[0]))]
        l0 = ['\t'.join(format(ls2[k][j],'.6f') for j in range(len(ls2[k]))) for k in range(len(ls2))]
        return ls1, l0


def cmpr_analy11(d0, d1, d2):
    '''TP段基线-单通道微调: mess_path, anal_path, data_path'''
    # # 0.数据准备-一次——
    # ids = get_lines1('%s/ids.txt' % d0)
    # random.shuffle(ids)
    # write_str('\n'.join(ids[:100]), '%s/ids_now.txt' % d0)
    
    # # 3.TP基线结果导出-基线值v/no, 修正mcg, 生成时刻点, 修正时间/空间波组图
    # ids = get_lines1('%s/ids.txt' % d0)
    # d30 = '%s/space_waves_1' % d1
    # d31 = '%s/time_waves_1' % d1
    # new_folder([d30, d31])
    # # for tx in ['squid2', 'opm']:
    # # for tx in ['opm']:
    # #     tlis = []
    # #     tms = txt_to_list('%s/%s_times1.txt' % (d0, tx))
    # #     l00, l01 = [], []  # 基线TP差, 基线-step1
    # #     d3 = '%s/%s_1' % (d2, tx)  # 导出mcg-step2
    # #     new_folder(d3)
    # #     for i in range(len(ids)):
    # #         print('start:', i+1, 'left:', len(ids)-i)
    # #         f0 = '%s/%s/%s.txt' % (d2, tx, ids[i])  # 源心磁
    # #         f1 = '%s/%s.txt' % (d3, ids[i])  # 修正心磁
    # #         ls36 = get_mcg36(f0, 0)
    # #         Tr, Ph = tms[i][12], tms[i][0]
    # #         jxs, Ms, ls2 = [], [], []
    # #         for j in range(len(ls36)):
    # #             l0 = ls36[j]
    # #             M = max(max(l0), -min(l0))
    # #             l1, l2 = l0[Tr:], l0[:Ph]
    # #             m0 = round(cal_iqr0(l1+l2), 6)
    # #             Ms.append(m0)  # 基线
    # #             jx = 0  # 基线TP差
    # #             if l1!=[] and l2!=[]:
    # #                 m1, m2 = cal_iqr0(l1), cal_iqr0(l2)
    # #                 jx = round(abs(m1-m2)/M, 6)
    # #             jxs.append(jx)
    # #             ls2.append([l0[k]-m0 for k in range(len(l0))])
    # #         l00.append(jxs)
    # #         l01.append(Ms)
    # #         ls2 = [[k]+[ls2[j][k] for j in range(len(ls2))] for k in range(len(ls2[0]))]
    # #         l0 = ['\t'.join(format(ls2[k][j],'.6f') for j in range(len(ls2[k]))) for k in range(len(ls2))]
    # #         write_str('\n'.join(l0), f1)  # 保存心磁
    # #         tlis.append(gettime(f1)[:-1])
    # #     save_txt(l00, 'list', '%s/%s_TPbase_er1.txt' % (d0, tx))  # 基线误差
    # #     save_txt(l01, 'list', '%s/%s_TPbase1.txt' % (d0, tx))  # 基线误差
    # #     save_txt(tlis, 'list', '%s/%s_1_times.txt' % (d0, tx))  # time_new-step3
    
    # 时刻点检测
    tx = 'opm'
    ts = txt_to_list('%s/%s_1_times.txt' % (d0, tx))
    ids0 = get_lines1('%s/ids.txt' % d0)
    ids = get_lines1('%s/ids_now.txt' % d0)
    idx = get_idxs(ids0, ids, [])
    # ts = [ts[i] for i in idx]
    # ts = [t[10]-t[9] for t in ts]
    cnt = 0
    for t in ts:
        cnt += 1
        # for i in range(7):  # Sr前没有倒置-正确!
        #     if t[i+1]-t[i]<0:  # 
        #         print(cnt)
        # if t[10]-t[9]<0 or t[11]-t[10]<0:  # To/Tp/Te没有倒置-正确!
        #     print(cnt)
        if t[12]-t[11]<0:
            # print(cnt)
            print(t)
            print(ids0[cnt-1])
        # if t>120:
        #     print(t)
        #     print(ids[cnt-1])
    # Rp/Tp-看图, To/Te-看图
    # Sr前-打印
    # Th/Tr位置
    
    # # print(ts[ids.index('SY_TT_000347')])
    # st = [(t[12]-t[9])-(t[9]-t[8]) for t in ts]
    # a = st.copy()
    # a.sort()
    # b = [t for t in a if t<0]
    # # print(b)
    # # # ids = [ids[i] for i in range(len(st)) if st[i]<0]
    # # # ts = [ts[i] for i in range(len(st)) if st[i]<0]
    # # # print(len(ids))
    # # # for i in range(len(ids)):
    # # #     item = ids[i]
    # # #     g1 = '%s/time_waves_1/er0/%s.png' % (d1, item)
    # # #     f0 = '%s/%s_1/%s.txt' % (d2, tx, ids[i])
    # # #     ls36 = get_mcg36(f0, 0)  # 36*N
    # # #     cmpr_draw1(ls36, ts[i], g1)  # 时间
    # st = [t[12]-t[8] for t in ts]
    # st = [t[12]-t[5] for t in ts]
    # a = st.copy()
    # a.sort()
    # # b = [t for t in a if t>250]
    # b = [t for t in a if t>400]
    # print(len(b)/1763)
    # print(b)
    # print(a[:20])
    # print(a[-20:])
    
    # d40 = '%s/%s' % (d30, tx)  # 生成时间/空间波组图-step4
    # d41 = '%s/%s' % (d31, tx)
    # new_folder([d40, d41])
    # ids = get_lines1('%s/ids.txt' % d0)
    # # ids = get_lines1('%s/ids_now.txt' % d0)
    # # idx = get_idxs(ids0, ids, [])
    # tms = txt_to_list('%s/%s_1_times.txt' % (d0, tx))
    # # tms = [tms[i] for i in idx]
    # for i in range(len(ids)):
    #     print('start:', i+1, 'left:', len(ids)-i)
    #     f0 = '%s/%s_1/%s.txt' % (d2, tx, ids[i])
    #     g10 = '%s/%s.png' % (d40, ids[i])
    #     g11 = '%s/%s.png' % (d41, ids[i])
    #     ls36 = get_mcg36(f0, 0)  # 36*N
    #     ts = tms[i]
    #     cmpr_draw0(ls36, g10, [ts[8], ts[12]])  # 空间
    #     cmpr_draw1(ls36, tms[i], g11)  # 时间
    
    
    # # # 1.复制空间波组图
    # ids = ['BJ_TT_000262', 'BJ_TT_000287', 'BJ_TT_000358', 'BJ_TT_000375', 'BJ_TT_000418', 'BJ_TT_000455', 'BJ_TT_000477', 'BJ_TT_000482', 'BJ_TT_000484', 'BJ_TT_000488', 'BJ_TT_000502', 'BJ_TT_000512', 'BJ_TT_000547', 'BJ_TT_000578', 'BJ_TT_000598', 'BJ_TT_000630', 'BJ_TT_000790', 'BJ_TT_000791', 'BJ_TT_000800', 'BJ_TT_000806']
    # # d30 = '%s/ThTr' % d1
    # # d3 = '%s/imgs_raw' % d30
    # # new_folder([d30, d3])
    # # d4 = '%s/space_waves_mgs' % d1
    # # for item in ids:
    # #     f0 = '%s/%s.png' % (d4, item)
    # #     f1 = '%s/%s.png' % (d3, item)
    # #     shutil.copyfile(f0, f1)
    # # 2.SrTp段曲线拟合定位Th
    # # ids = ['BJ_TT_000418', 'BJ_TT_000512']
    # ids = ['SY_TT_000347']
    # l0 = get_lines1('%s/ids.txt' % d0)
    # idx = [l0.index(i) for i in ids]
    # l0 = txt_to_list('%s/opm_1_times.txt' % d0)
    # tm = [l0[i] for i in idx]
    # dr = '%s/opm_1' % d2
    # # d3 = '%s/ThTr/Th_fit' % d1
    # # d3 = '%s/ThTr/Tr_fit' % d1
    # d3 = '%s/ThTr/ThTr_new' % d1
    # new_folder(d3)
    # # d5 = '%s/ThTr/Th_fit1' % d1
    # # new_folder(d5)
    # d5 = '%s/ThTr/Tr_fit1' % d1
    # new_folder(d5)
    # th = 20
    # # ids = get_lines1('%s/ids.txt' % d0)
    # # tm = txt_to_list('%s/opm_1_times.txt' % d0)
    # # i, j = 0, 18
    # vls = []
    # for i in range(len(ids)):
    #     item, ts = ids[i], tm[i]
    #     d4 = '%s/%s' % (d3, item)
    #     new_folder(d4)
    #     ls36 = get_mcg36('%s/%s.txt' % (dr, item), 0)
    #     tlis = gettime('%s/%s.txt' % (dr, item))
    #     Sr, Tp, N1 = ts[7], ts[10], len(ls36[0])
    #     Te = ts[11]
    #     print(i, item)
    #     l00 = []
    #     yss = []
    #     for j in range(len(ls36)):
    #         ys = ls36[j][Sr:Tp+1]
    #         # ys = ls36[j][Tp:N1]
    #         xs = list(range(len(ys)))
    #         # ys1 = ls36[j][Tp:Te]
    #         # xs1 = list(range(len(ys1)))
    #         m, _ = cal_ks0(xs, ys)
    #         # m, _ = cal_ks0(xs1, ys1)
    #         if m > 0:
    #             ys = [v-min(ys) for v in ys]
    #             # ys = [max(ys)-v for v in ys]
    #         else:
    #             ys = [max(ys)-v for v in ys]
    #             # ys = [v-min(ys) for v in ys]
    #         v1, v2 = ys[-1], max(ys)
    #         # v1, v2 = ys[0], max(ys)
    #         vv = 15
    #         ysm = gaussian_filter1d(ys, sigma=vv)
    #         if ys.index(v2)>len(ys)/2 and v1>0.6*v2:
    #         # if ys.index(v2)<len(ys)/2 and v1>0.6*v2:
    #             data1 = np.diff(np.array(ysm), n=1)
    #             lss = []
    #             lss1 = data1.tolist()
    #             for k0 in lss1:
    #                 if k0 < 0:
    #                     lss.append(0)
    #                 else:
    #                     lss.append(1)
    #             lns = cal_nums01(lss)
    #             if len(lns) > 3:
    #                 v0 = max(lns)
    #                 ik = lns.index(v0)
    #                 if sum(lns[ik+1:])<0.3 and ik > 0:
    #                 # if sum(lns[:ik])<0.3 and ik < len(lns)-1:
    #                     if ik == len(lns)-1:
    #                     # if ik == 0:
    #                         if sum(lns[-2:])>0.55:
    #                         # if sum(lns[:2])>0.5:
    #                             print(int((j+6)/6), j%6+1)
    #                             l00.append(lns)
    #                             yss.append(ys)
    #                     else:
    #                         if v0 + max(lns[ik+1], lns[ik-1])>0.55:
    #                         # if v0 + max(lns[ik+1], lns[ik-1])>0.5:
    #                             print(int((j+6)/6), j%6+1)
    #                             l00.append(lns)
    #                             yss.append(ys)
    #             else:
    #                 print(int((j+6)/6), j%6+1)
    #                 l00.append(lns)
    #                 yss.append(ys)
    #         # else:
    #         #     l00.append([])
    #         # bgs = signal.find_peaks(-ysm, distance=th)[0]
    #         # bgs = [i for i in bgs]
    #         # l00.append(bgs)
    #     # print(i, len(l00))
    #     # vls.append(len(l00))
    #     # for j in range(6):
    #     #     print(l00[6*j:6*j+6])
    #         # # print(bgs)
    #         # # g1 = '%s/%s_%.1f.png' % (d3, item, vv)
    #         # g1 = '%s/%s_%d.png' % (d4, item, j)
    #         # plt.figure()
    #         # plt.style.use('bmh')
    #         # plt.rcParams['savefig.dpi'] = 512
    #         # # plt.text(len(ys)/2, max(ys)*9/10, str(bgs), ha='center', va='bottom', fontsize=8)
    #         # # plt.title()
    #         # plt.plot(xs, ys, linewidth=0.5)
    #         # plt.plot(xs, ysm, color='r', linewidth=0.5)
    #         # plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    #         # plt.close()
    #     # print(len(yss))
    #     ys0 = [np.sqrt(sum([yss[k][j]**2 for k in range(len(yss))])/len(yss)) for j in range(len(yss[0]))]
    #     pts = get_hxd0(ys0)
    #     M1 = max(hb_lis0(yss))
    #     rts = [round(ys0[i1]/M1,3) for i1 in pts]
    #     r0 = 0.203
    #     # r1 = 0.188
    #     for i1 in range(len(rts)):
    #         if rts[len(rts)-1-i1]<r0:
    #             k0 = pts[len(rts)-1-i1]
    #         # if rts[i1]<r1:
    #         #     k0 = pts[i1]
    #             break
    #     ks = []
    #     for ys in yss:  # 多个有效通道的最近极小点
    #         pt = signal.find_peaks(-np.array(ys), distance=th)[0]
    #         ds = [abs(i1-k0) for i1 in pt]
    #         ik = [pt[i1] for i1 in range(len(pt)) if ds[i1]==min(ds)]
    #         ks += ik
    #     # print(k0, ks)
    #     k1 = cal_iqr0(ks)
    #     ks = sorted(set(ks))
    #     ds = [abs(i1-k1) for i1 in ks]
    #     ik = [ks[i1] for i1 in range(len(ks)) if ds[i1]==min(ds)]
    #     # k2 = ik[-1]
    #     k2 = ik[-1]+Sr
    #     # print(k2)
    #     # k2 = ik[0]
    #     # k2 = ik[0]+Tp
    #     # print(k0, round(k1,2), k2)
    #     # print(k2)
    #     g1 = '%s/%s_spwvs.png' % (d5, item)
    #     cmpr_draw0(ls36, g1, [k2])
        
    #     tx = str(pts)+'\n'+str(rts)
    #     # print(len(yss))
    #     plt.figure()
    #     plt.style.use('bmh')
    #     plt.rcParams['savefig.dpi'] = 512
    #     plt.text(len(ys0)/2, M1*9/10, tx, ha='center', va='bottom', fontsize=8)
    #     g1 = '%s/%s_all.png' % (d3, item)
    #     for ys in yss:
    #         plt.plot(xs, ys, linewidth=0.5)
    #     plt.plot(xs, ys0, color='r', linewidth=2)
    #     plt.scatter(pts, [ys0[i0] for i0 in pts], c='k', s=50)
    #     plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    #     plt.close()
    # # print(vls)
    return d1


def cmpr_analy2(d0, d1, d2):
    '''T波识别: mess_path, anal_path, data_path'''
    ids = get_lines1('%s/ids_now.txt' % d0)
    idx = get_idxs(get_lines1('%s/ids.txt' % d0), ids, [])
    for tx in ['squid2', 'opm']:
        d3 = '%s/%s_Tp' % (d1, tx)
        new_folder(d3)
        tms = txt_to_list('%s/%s_1_times.txt' % (d0, tx))
        tms = [tms[i] for i in idx]
        for i in range(len(ids)):
            f0 = '%s/%s_1/%s.txt' % (d2, tx, ids[i])
            g1 = '%s/%s.png' % (d3, ids[i])
            ls36 = get_mcg36(f0, 0)  # 36*N
            Sr, Tr = tms[i][7], tms[i][-1]
            print('start:', i+1, 'left:', len(ids)-i)
            l0 = [ls36[i][Sr:Tr+1] for i in range(len(ls36))]
            cmpr_draw5(ls36, l0, [Sr,Tr], g1)
    return d1


def cmpr_twvs0(d0, d1, d2):
    '''mess_path, anal_path, data_path'''
    # # d3 = '%s/Twvs0' % d1
    # # d3 = '%s/Twvs1' % d1
    # # d3 = '%s/Twvs2' % d1
    # # d3 = '%s/Twvs3' % d1
    # # d3 = '%s/space_waves_mgs' % d1
    # d3 = '%s/space_waves_mgs1' % d1
    # new_folder(d3)
    th = 20  # 峰值间距
    ths = [-0.062, -0.086, 0.104, 0.067, 0.086, 0.123]
    # ids0 = get_lines1('%s/ids.txt' % d0)
    # tms = txt_to_list('%s/opm_1_times.txt' % d0)
    # ids = get_lines1('%s/ids_now.txt' % d0)
    # idx = get_idxs(ids0, ids, [])
    # tms = [tms[i] for i in idx]
    # # l0 = os.listdir('%s/T_mark' % d1)
    # # ids = [l[:-4] for l in l0]
    # # iks = [int(l[-4:]) for l in ids]
    # # iks.sort()
    # # ids = ['BJ_TT_%06d' % l for l in iks]
    # # for item in ids0:
    # for i0 in range(len(ids)):
    #     item = ids[i0]
    #     print(i0, item)
    #     # ts = tms[ids0.index(item)]
    #     ts = tms[i0]
    #     Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
    #     f0 = '%s/opm_1/%s.txt' % (d2, item)
    #     ls36 = get_mcg36(f0, 0)
    #     l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
    #     l3 = []
    #     l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
    #     M = max(get_mai0(ls36))
    #     for i in range(len(l1)):  # 基于双比例
    #         M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
    #         l2 = l1[i]
    #         v0, v1, j0, j1 = 0, 0, 0, 0
    #         ik, _ = signal.find_peaks(-np.array(l2), distance=th)
    #         l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         ik, _ = signal.find_peaks(np.array(l2), distance=th)
    #         l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
    #         if l30:
    #             l4 = [l2[j] for j in l30]
    #             v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
    #         if l31:
    #             l4 = [l2[j] for j in l31]
    #             v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
    #         f00, f01 = v0/M, v1/M
    #         f10, f11 = abs(v0/M0), abs(v1/M0)
    #         l3.append(cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths, 1))
    #     g1 = '%s/%s.png' % (d3, item)
    #     cmpr_draw6(ls36, l3, [Th, Tr], g1)
    
    # # d4 = '%s/T_mgs' % d0
    d4 = '%s/T_mgs1' % d0
    new_folder(d4)
    # # lx = ['a', 'b', 'c', 'd', 'e']
    lx = ['a', 'e', 'c', 'd', 'b']
    msh = ['upright', 'inversion', 'posneg_bidirect', 'negpos_bidirect', 'low']
    ids = get_lines1('%s/ids.txt' % d0)
    tms = txt_to_list('%s/opm_1_times.txt' % d0)
    l0s, l3s = [], []
    for i in range(len(ids)):
        item, ts = ids[i], tms[i]
        print('start:', i+1, 'end:', len(ids)-i-1)
        Qh, Sr, Th, Tr = ts[3], ts[7], ts[8], ts[12]
        ls36 = get_mcg36('%s/opm_1/%s.txt' % (d2, item), 0)
        l1 = [ls36[i][Th:Tr] for i in range(len(ls36))]
        l00, l3 = [], []
        l11 = [ls36[i][Qh:Sr] for i in range(len(ls36))]
        M = max(get_mai0(ls36))
        for i in range(len(l1)):  # 基于双比例
            M0 = max(max(l11[i]), -min(l11[i]))  # QRS最大幅值
            l2 = l1[i]
            v0, v1, j0, j1 = 0, 0, 0, 0
            ik, _ = signal.find_peaks(-np.array(l2), distance=th)
            l30 = [j for j in ik if j!=0 and j!=len(l2)-1]
            ik, _ = signal.find_peaks(np.array(l2), distance=th)
            l31 = [j for j in ik if j!=0 and j!=len(l2)-1]
            if l30:
                l4 = [l2[j] for j in l30]
                v0, j0 = min(0, min(l4)), l30[l4.index(min(l4))]
            if l31:
                l4 = [l2[j] for j in l31]
                v1, j1 = max(0, max(l4)), l31[l4.index(max(l4))]
            f00, f01 = v0/M, v1/M
            f10, f11 = abs(v0/M0), abs(v1/M0)
            tx, _ = cmpr_gettx2([f00,f01,f10,f11,j0,j1], ths)
            zl = lx[msh.index(tx)]
            l3.append(zl)
            if zl == 'c':
                l00.append(get_round([f11, f10], [3, 3]))
            else:
                l00.append(get_round([f10, f11], [3, 3]))
        l0s.append(l00)
        l3s.append(l3)
    save_txt(l0s, 'list', '%s/values.txt' % d4)
    save_txt(l3s, 'list', '%s/types.txt' % d4)
    
    # 统计1526例结果
    ids = get_lines1('%s/ids.txt' % d0)
    idx1, idx2 = txt_to_list('%s/idx1526.txt' % d0)[0]
    idx = idx1 + idx2
    ids = [ids[i] for i in idx]
    l1 = txt_to_list('%s/values.txt' % d4)
    l2 = txt_to_list('%s/types.txt' % d4)
    save_txt([l1[i] for i in idx], 'list', '%s/values_1526.txt' % d4)
    save_txt([l2[i] for i in idx], 'list', '%s/types_1526.txt' % d4)
    l1 = txt_to_list('%s/values_1526.txt' % d4)
    l2 = txt_to_list('%s/types_1526.txt' % d4)
    l0 = [ids[i]+', '+str([[l2[i][j]]+l1[i][j] for j in range(len(l1[i]))]) for i in range(len(l1))]
    write_str('\n'.join(l0), '%s/messages1526.txt' % d4)
    
    # 导出100例结果
    ids0 = get_lines1('%s/ids.txt' % d0)
    ids = get_lines1('%s/ids_now.txt' % d0)
    idx = get_idxs(ids0, ids, [])
    idx1, idx2 = txt_to_list('%s/idx1763.txt' % d0)[0]
    idx1 = [i0 for i0 in idx1 if i0 in idx]
    idx2 = [i0 for i0 in idx2 if i0 in idx]
    idx = idx1 + idx2
    ids = [ids0[i] for i in idx]
    l1 = txt_to_list('%s/values.txt' % d4)
    l2 = txt_to_list('%s/types.txt' % d4)
    save_txt([l1[i] for i in idx], 'list', '%s/values_100.txt' % d4)
    save_txt([l2[i] for i in idx], 'list', '%s/types_100.txt' % d4)
    l1 = txt_to_list('%s/values_100.txt' % d4)
    l2 = txt_to_list('%s/types_100.txt' % d4)
    l1 = ['\n'.join([str(l1[i][j*6:j*6+6]) for j in range(6)]) for i in range(len(l1))]
    l2 = ['\n'.join([''.join(l2[i][j*6:j*6+6]) for j in range(6)]) for i in range(len(l2))]
    l0 = ['\n'.join([ids[i], l2[i], l1[i]]) for i in range(len(l1))]
    write_str('\n'.join(l0), '%s/messages100.txt' % d4)
    
    return d1


def cmpr_twvs1(d0, d1, d2):
    '''mess_path, anal_path, data_path'''
    # d4 = '%s/T_mgs' % d0
    # new_folder(d4)
    # # l1 = txt_to_list('%s/values_1526.txt' % d4)
    # l2 = txt_to_list('%s/types_1526.txt' % d4)
    # m1, m2 = l2[:611], l2[611:]
    # l3 = []
    # tx = ['a', 'b', 'c', 'd', 'e']
    # for i in range(36):
    #     l4 = [sum(1 for y in m1 if y[i]==x) for x in tx]
    #     l4 += [sum(1 for y in m2 if y[i]==x) for x in tx]
    #     l3.append(l4)
    # print(l3)
    
    l1 = txt_to_list('%s/opm_1_times.txt' % d0)
    l2 = [l[11]-l[5] for l in l1]
    # ids = get_lines1('%s/ids.txt' % d0)
    # ids1 = [ids[i] for i in range(len(l2)) if l2[i]<150]
    # dr1 = '%s/time_waves_1/now0' % d1
    # new_folder(dr1)
    # for item in ids1:
    #     f0 = '%s/time_waves_1/opm/%s.png' % (d1, item)
    #     f1 = '%s/%s.png' % (dr1, item)
    #     shutil.copyfile(f0, f1)
    fs = [2.5, 10, 25, 50, 60, 70, 75, 80, 90, 95, 97.5]
    n = 1763
    l2.sort()
    ls = []
    for i in fs:
        n1 = math.floor(n*i/100)
        ls.append('%.1f  %d' % (i, l2[n1]))
    print('\n'.join(ls))
    # l3 = [t for t in l2 if t<401]
    # print(len(l3))
    # print(l2[:100])
    # print(l2[-100:])
    # print(len(l2))
    # print(min(l2))
    # print(max(l2))
    # print(len(l1[0]))
    return d2


def cmpr_selmts0(d0, d1, d2):
    '''特异性筛选: mess_path, metr_path, resu_path; acc>0.95, num>0.5'''
    d2 = '%s/spe_mts' % d2
    new_folder(d2)
    mts = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[0])
    mts1 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[1])  # 角度/象限类
    mts0 = [m for m in mts if m not in mts1]
    idx0, idx1 = txt_to_list('%s/idx1763.txt' % d0)[0]
    for tx in ['squid2', 'opm']:
        rs1, rs2 = [], []
        for i in range(len(mts0)):  # 两端特异性
            ms = txt_to_list('%s/%s/%s.txt' % (d1, tx, mts0[i]))
            ms0, ms1 = [[ms[i] for i in idx] for idx in [idx0, idx1]]
            rs1, rs2, _, _ = ranks_spemt0(rs1, rs2, ms0, ms1, mts0[i], 0.95)
        lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
        data = np.array(lis1)
        idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
        rs1 = [rs1[i] for i in idex]
        l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
        write_str('\n'.join(l1), '%s/%s_edges_0.95.txt' % (d2, tx))
        rs1 = rs2
        lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
        data = np.array(lis1)
        idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
        rs1 = [rs1[i] for i in idex]
        l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
        write_str('\n'.join(l1), '%s/%s_edges_0.05.txt' % (d2, tx))
        rs1, rs2 = [], []
        for i in range(len(mts1)):  # 中间特异性(角度/象限)
            ms = txt_to_list('%s/%s/%s.txt' % (d1, tx, mts1[i]))
            ms0, ms1 = [[ms[i] for i in idx] for idx in [idx0, idx1]]
            rs1, rs2 = ranks_spemt1(rs1, rs2, ms0, ms1, mts1[i], 0.95)
        lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
        data = np.array(lis1)
        idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
        rs1 = [rs1[i] for i in idex]
        l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
        write_str('\n'.join(l1), '%s/%s_inters_0.95.txt' % (d2, tx))
        rs1 = rs2
        lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
        data = np.array(lis1)
        idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
        rs1 = [rs1[i] for i in idex]
        l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
        write_str('\n'.join(l1), '%s/%s_inters_0.05.txt' % (d2, tx))
    return d2


# def cmpr_autorks0(d1, d2, lbs, iks):
#     '''自动化分级: mess_path, metr_path; squid2/opm=tr/va
#     options: 参数集'''
#     d3 = '%s/%s' % (d1, lbs)
#     new_folder(d3)
#     mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
#     write_str('\n'.join(mtnms), '%s/mts.txt' % d3)
#     pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
#     ids0, ids1 = [[], []], [[], []]
#     for i in range(len(iks[0])):
#         ids0[0] += pts[iks[0][i]][0]
#         ids0[1] += pts[iks[0][i]][1]
#     for i in range(len(iks[1])):
#         ids1[0] += pts[iks[1][i]][0]
#         ids1[1] += pts[iks[1][i]][1]
#     rks = []
#     for i in range(len(mtnms)):
#         item = mtnms[i]
#         print(i+1, item)
#         l0 = txt_to_list('%s/squid2/%s.txt' % (d2, item))  # tr
#         l1 = txt_to_list('%s/opm/%s.txt' % (d2, item))  # va
#         mtlis = []
#         for i2 in range(2):  # tr
#             mtlis.append([l0[j2] for j2 in ids0[i2]])
#         for i2 in range(2):  # va
#             mtlis.append([l1[j2] for j2 in ids1[i2]])
#         v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
#         xyss1 = make_ranks2(mtlis[0]+mtlis[2], mtlis[1]+mtlis[3], mode=0, v1=v1, lis0=[])
#         rts = auto_rks0(xyss1, item)
#         rks.append(rts)
#     save_txt(rks, 'list', '%s/rks_trva.txt' % d3)
#     return d1


def cmpr_selmts1(d0, d1, d2, d3):
    '''效用排序: mess_path, metr_path, diag_path, resu_path
    自动化分级、诊断和效用排序'''
    lbs = 'model_1763'
    d4 = '%s/%s' % (d0, lbs)
    new_folder(d4)
    # # 1.自动化分级
    # mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[0])
    # # write_str('\n'.join(mtnms), '%s/mts.txt' % d3)  # 一次——
    # idx0, idx1 = txt_to_list('%s/idx1763.txt' % d0)[0]
    # for tx in ['squid2', 'opm']:
    #     rks = []
    #     for i in range(len(mtnms)):
    #         item = mtnms[i]
    #         print(i+1, item)
    #         l00 = txt_to_list('%s/%s/%s.txt' % (d1, tx, item))
    #         l0, l1 = [[l00[i] for i in idx] for idx in [idx0, idx1]]
    #         v1 = min(l0+l1)-1
    #         xyss1 = make_ranks2(l0, l1, mode=0, v1=v1, lis0=[])
    #         rts = auto_rks0(xyss1, item)
    #         rks.append(rts)
    #     save_txt(rks, 'list', '%s/%s_rks0.txt' % (d4, tx))
    # # 2.诊断、效用排序
    # mtnms = get_lines1('%s/mts.txt' % d4)
    # for tx in ['squid2', 'opm']:  # 诊断
    #     rkmes = txt_to_list('%s/%s/%s_rks0.txt' % (d0, lbs, tx))
    #     d00 = diag_resu0('%s/%s' % (d1, tx), mtnms, rkmes, d2, tx)
    # idx0, idx1 = txt_to_list('%s/idx1763.txt' % d0)[0]
    # for tx in ['squid2', 'opm']:  # 效用排序
    #     lis1 = []
    #     for i in range(len(mtnms)):
    #         item = mtnms[i]
    #         l00 = txt_to_list('%s/%s/%s.txt' % (d2, tx, item))
    #         l0, l1 = [[l00[i] for i in idx] for idx in [idx0, idx1]]
    #         auc, acc, f1sc = cal_utility1(l0, l1)
    #         lis1.append([auc, acc, f1sc])
    #     data = np.array(lis1)  # auc>acc>F1得分
    #     idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    #     sorted_data = data[idex]
    #     lis1 = sorted_data.tolist()
    #     save_txt(lis1, 'list', '%s/%s/%s_sort.txt' % (d0, lbs, tx))
    #     lns = get_lines1('%s/%s/%s_rks0.txt' % (d0, lbs, tx))
    #     lns = [lns[i] for i in list(idex)]
    #     write_str('\n'.join(lns), '%s/%s/%s_rks.txt' % (d0, lbs, tx))
    #     lns = [mtnms[i] for i in list(idex)]
    #     write_str('\n'.join(lns), '%s/%s/%s_mts.txt' % (d0, lbs, tx))
    # 3.筛选指标-Auc+Acc>1.2 or Acc+F1>1.2, 排序Auc>F1>Acc
    d5 = '%s/uty_mts' % d3
    new_folder(d5)
    for tx in ['squid2', 'opm']:
        mtnms = get_lines1('%s/%s/%s_mts.txt' % (d0, lbs, tx))
        l1 = txt_to_list('%s/%s/%s_sort.txt' % (d0, lbs, tx))
        lis1 = [[i]+l1[i] for i in range(len(l1))]
        l0 = [lis1[i] for i in range(len(lis1)) if lis1[i][1]+lis1[i][3]>1.2 or lis1[i][2]+lis1[i][3]>1.2]
        data = np.array(l0)
        idex = np.lexsort((data[:, 0], -1 * data[:, 2], -1 * data[:, 3], -1 * data[:, 1]))
        sorted_data = data[idex]
        lis1 = sorted_data.tolist()
        l3 = ['%s, %.3f, %.3f, %.3f' % (mtnms[int(l[0])], l[1], l[2], l[3]) for l in lis1]
        # l3 = [get_round(lis1[i],[0,3,3,3]) for i in range(len(lis1))]
        save_txt(l3, 'list', '%s/%s_uty.txt' % (d5, tx))
    return d3


def cmpr_selmts2(d0):
    '''筛选分类: resu_path; 特异且有效、特异且无效、不特异且有效'''
    d1 = '%s/sel_mts' % d0
    new_folder(d1)
    # ls = []
    # for tx in ['squid2', 'opm']:
    #     # # 整理
    #     # l1 = get_lines1('%s/spe_mts/%s_edges_0.95.txt' % (d0, tx))
    #     # l1 += get_lines1('%s/spe_mts/%s_inters_0.95.txt' % (d0, tx))
    #     # l2 = [get_item(l1, ik=i, lis0=[], jd=6) for i in [-2, -1]]
    #     # l2 = [[l2[i][j] for i in range(2)] for j in range(len(l2[0]))]
    #     # l2 = [l2[i] for i in range(len(l2)) if l2[i][0]>=0.05]
    #     # l1 = [[i, l2[i][0]] for i in range(len(l2))]
    #     # data = np.array(l1)
    #     # idx = np.lexsort((data[:, 0], -1 * data[:, 1]))
    #     # l3 = [l2[i] for i in idx]
    #     # save_txt(l3, 'list', '%s/%s_spe.txt' % (d1, tx))
    #     # shutil.copyfile('%s/uty_mts/%s_uty.txt' % (d0, tx), '%s/%s_uty.txt' % (d1, tx))
    #     # 汇总
    #     mt0 = get_item(get_lines1('%s/%s_uty.txt' % (d1, tx)), 0, [])
    #     l1 = txt_to_list('%s/%s_spe.txt' % (d1, tx))
    #     mt1 = [l[1] for l in l1]
    #     save_txt([mt0, mt1], 'list', '%s/%s_sel.txt' % (d1, tx))  # 一次——
    # 匹配: 91/29/17=sqop有效/特异/共同
    l1, l2 = txt_to_list('%s/squid2_sel.txt' % d1)
    l3, l4 = txt_to_list('%s/opm_sel.txt' % d1)
    l13 = [m for m in l1 if m in l3]
    l24 = [m for m in l2 if m in l4]
    l1234 = [m for m in l13 if m in l24]
    save_txt([l13, l24, l1234], 'list', '%s/sqop_sel.txt' % d1)
    return d0


def analysis_metrics5(d0, d1, lbs, f0, f1):
    '''model_1403综合数据模型: 整理标签、指标
    root_path, data_path, 'model_1403', f0, f1
    1403tr, 351te; 414+706tr, 103+177va; 518+883=1401最终
    model_1526一致性模型: 整理标签、指标
    1220tr, 306te; 390+585tr,  98+147va; 488+732=1220最终
    '''
    # 1.标签、分割、时刻点
    data = pd.read_excel(f0, sheet_name='建模').values
    vs = []
    for i in [1, 2, 3, 4, 5, 6, 7, 8]:
        vs.append(get_xlsx_value(data, i))
    ids = vs[0]
    l1 = get_lines1('%s/messages/ids.txt' % d0)
    ids0 = [i for i in ids if not i in l1]
    write_str('\n'+'\n'.join(ids0), '%s/messages/ids.txt' % d0, 'a')  # 补充一次——
    ids = ['SHLY_2024_000557', 'SHLY_2024_000775', 'SHLY_2024_000671', 'SHLY_2024_000746', 'SHLY_2024_000598', 'SHLY_2024_000970', 'SHLY_2024_000905', 'SHLY_2024_000737', 'SHLY_2024_000670']
    l2 = get_lines1('./results/consistent3/messages/ids.txt')
    idx = get_idxs(l2, ids, [])
    print(idx)
    for item in os.listdir('%s/metrics' % d0):  # 添加指标一次——
        f3 = '%s/metrics/%s' % (d0, item)
        l3 = get_lines1(f3)
        # if len(l3) != 1754:
        #     print(item, 1)
        f4 = '%s3/metrics/%s' % (d0, item)
        l4 = get_lines1(f4)
        l3 += [l4[i] for i in idx]
        # if len(l3) != 1763:
        #     print(item, 2)
        write_str('\n'.join(l3), f3)
    
    # tmlis = [[int(vs[i][j]) for i in range(2, 8)] for j in range(len(vs[0]))]
    # write_str('\n'.join(ids), '%s/messages/ids.txt' % d0)
    # save_txt(tmlis, 'list', '%s/messages/times.txt' % d0)
    # # 数据分割-索引
    # l11 = get_lines1('%s/messages/ids.txt' % d0)
    # l00 = get_idxs(l11, ids, [])
    # l0, l1 = l00[:488], l00[488:]
    # print(len(l0), len(l1))
    # # list(range(518)), list(range(518, 1403)) # 外部保留验证
    # random.shuffle(l0)
    # random.shuffle(l1)
    # # trn, trp = l0[:414], l1[:706]
    # # van, vap = l0[414:414+104], l1[706:706+177]
    # trn, trp = l0[:390], l1[:585]
    # van, vap = l0[390:390+98], l1[585:585+147]
    # l2 = [[trn, trp], [van, vap]]
    # save_txt(l2, 'list', '%s/messages/idx.txt' % d0)
    # # 外部保留验证-运行一次————
    # data = pd.read_excel(f1, sheet_name='外部保留验证').values
    # vs = []
    # for i in [1, 2, 3, 4, 5, 6, 7, 8]:
    #     vs.append(get_xlsx_value(data, i))
    # ids = vs[0]
    # l1 = get_lines1('%s/messages/ids.txt' % d0)
    # idx = get_idxs(l1, ids, [])
    # tmlis = [[int(vs[i][j]) for i in range(2, 8)] for j in range(len(vs[0]))]
    # write_str('\n'.join(ids), '%s/messages/ids_te.txt' % d0)
    # save_txt(tmlis, 'list', '%s/messages/times_te.txt' % d0)
    # n1 = len(get_lines1('%s/messages/ids.txt' % d0))
    # save_txt([idx], 'list', '%s/messages/idx_te.txt' % d0)
    # write_str('\n'+'\n'.join(ids), '%s/messages/ids.txt' % d0, 'a')
    # # 2.指标提取
    # ids0 = get_lines1('%s3/messages/ids.txt' % d0)
    # ids = get_lines1('%s/messages/ids.txt' % d0)
    # iks = get_idxs(ids0, ids, [])  # ids在ids0的索引集
    # for item in os.listdir('%s3/metrics' % d0):
    #     l1 = get_lines1('%s3/metrics/%s' % (d0, item))
    #     l2 = [l1[i] for i in iks]
    #     write_str('\n'.join(l2), '%s/metrics/%s' % (d0, item))
    return d0


def data_split1(d0):
    '''心肌缺血特异性指标的指标分割: 1647=474/842+119/212, 1940=474/1077+119/270'''
    pts = txt_to_list('%s/idx0.txt' % d0)
    l0 = txt_to_list('%s/label4s.txt' % d0)
    l00 = hb_lis0([pts[i] for i in l0[0]])  # 阴性
    l01 = hb_lis0([pts[i] for i in l0[3]])  # 阳性
    l02 = hb_lis0([pts[i] for i in l0[2]+l0[3]])  # +偏阳性
    random.shuffle(l0)
    random.shuffle(l01)
    random.shuffle(l02)
    ls = [[[], []], [[], []], [[], []], [[], []]]
    ls[0][0] += l00[:474]
    ls[0][1] += l01[:842]
    ls[1][0] += l00[474:]
    ls[1][1] += l01[842:]
    ls[2][0] += l00[:474]
    ls[2][1] += l02[:1077]
    ls[3][0] += l00[474:]
    ls[3][1] += l02[1077:]
    write_str('\n'.join([str(ls[i]) for i in range(len(ls))]), '%s/idx2.txt' % d0)
    return d0


def search_spe_mts1(d0, d1, d2):
    '''心肌缺血特异性指标trte: mess_path, metr_path, rank_path'''
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[0])
    mtnms2 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[2])
    mtnms = [mt for mt in mtnms if mt not in mtnms2]
    pts = txt_to_list('%s/idx2.txt' % d0)
    # ik1, ik2, tx = 0, 1, 'spemts_1647'
    ik1, ik2, tx = 2, 3, 'spemts_1940'
    # l00 = []
    # for i in range(len(mtnms)):
    #     l00.append(txt_to_list('%s/%s.txt' % (d1, mtnms[i])))
    # ls0 = [[l00[i][j] for j in pts[ik1][0]] for i in range(len(l00))]
    # ls1 = [[l00[i][j] for j in pts[ik1][1]] for i in range(len(l00))]
    # ls2 = [[l00[i][j] for j in pts[ik2][0]] for i in range(len(l00))]
    # ls3 = [[l00[i][j] for j in pts[ik2][1]] for i in range(len(l00))]
    # rs1, rs2 = [], []
    # for i in range(len(mtnms)):
    #     rs1, rs2 = ranks_spemt2(rs1, rs2, ls0[i], ls1[i], ls2[i], ls3[i], mtnms[i], rg=0.95)
    # lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    # data = np.array(lis1)
    # idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    # rs1 = [rs1[i] for i in idex]
    # l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    # d3 = '%s/%s' % (d2, tx)
    # new_folder(d3)
    # write_str('\n'.join(l1), '%s/tr_trte_edges_0.95.txt' % d3)
    # rs1=rs2
    # lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    # data = np.array(lis1)
    # idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    # rs1 = [rs1[i] for i in idex]
    # l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    # write_str('\n'.join(l1), '%s/tr_trte_edges_0.05.txt' % d3)
    l00 = []
    for i in range(len(mtnms2)):
        l00.append(txt_to_list('%s/%s.txt' % (d1, mtnms2[i])))
    ls0 = [[l00[i][j] for j in pts[ik1][0]] for i in range(len(l00))]
    ls1 = [[l00[i][j] for j in pts[ik1][1]] for i in range(len(l00))]
    ls2 = [[l00[i][j] for j in pts[ik2][0]] for i in range(len(l00))]
    ls3 = [[l00[i][j] for j in pts[ik2][1]] for i in range(len(l00))]
    rs1, rs2 = [], []
    for i in range(len(mtnms2)):
        rs1, rs2 = ranks_spemt3(rs1, rs2, ls0[i], ls1[i], ls2[i], ls3[i], mtnms2[i], rg=0.95)
    lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    rs1 = [rs1[i] for i in idex]
    l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    d3 = '%s/%s' % (d2, tx)
    new_folder(d3)
    write_str('\n'.join(l1), '%s/tr_trte_inters_0.95.txt' % d3)
    rs1=rs2
    lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    rs1 = [rs1[i] for i in idex]
    l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    write_str('\n'.join(l1), '%s/tr_trte_inters_0.05.txt' % d3)
    return d1


def search_spe_mts0(d0, d1, d2):
    '''心肌缺血特异性指标: mess_path, metr_path, rank_path'''
    # d00 = data_split1(d0)  # 标签整理
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[0])
    mtnms2 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d0)[2])
    mtnms = [mt for mt in mtnms if mt not in mtnms2]
    pts = txt_to_list('%s/idx2.txt' % d0)
    # ik1, ik2, tx = 0, 1, 'spemts_1647'
    ik1, ik2, tx = 2, 3, 'spemts_1940'
    l00 = []
    for i in range(len(mtnms)):
        l00.append(txt_to_list('%s/%s.txt' % (d1, mtnms[i])))
    ls0 = [[l00[i][j] for j in pts[ik1][0]+pts[ik2][0]] for i in range(len(l00))]
    ls1 = [[l00[i][j] for j in pts[ik1][1]+pts[ik2][1]] for i in range(len(l00))]
    rs1, rs2 = [], []
    for i in range(len(mtnms)):
        rs1, rs2, _, _ = ranks_spemt0(rs1, rs2, ls0[i], ls1[i], mtnms[i], rg=0.95)
    lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    rs1 = [rs1[i] for i in idex]
    l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    d3 = '%s/%s' % (d2, tx)
    new_folder(d3)
    write_str('\n'.join(l1), '%s/all_edges_0.95.txt' % d3)
    rs1=rs2
    lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    rs1 = [rs1[i] for i in idex]
    l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    write_str('\n'.join(l1), '%s/all_edges_0.05.txt' % d3)
    l00 = []
    for i in range(len(mtnms2)):
        l00.append(txt_to_list('%s/%s.txt' % (d1, mtnms2[i])))
    ls0 = [[l00[i][j] for j in pts[ik1][0]+pts[ik2][0]] for i in range(len(l00))]
    ls1 = [[l00[i][j] for j in pts[ik1][1]+pts[ik2][1]] for i in range(len(l00))]
    rs1, rs2 = [], []
    for i in range(len(mtnms2)):
        rs1, rs2 = ranks_spemt1(rs1, rs2, ls0[i], ls1[i], mtnms2[i], rg=0.95)
    lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    rs1 = [rs1[i] for i in idex]
    l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    d3 = '%s/%s' % (d2, tx)
    new_folder(d3)
    write_str('\n'.join(l1), '%s/all_inters_0.95.txt' % d3)
    rs1=rs2
    lis1 = [[i, rs1[i][-2]] for i in range(len(rs1))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1 * data[:, 1]))
    rs1 = [rs1[i] for i in idex]
    l1 = ['%s, %.4f, %s' % (str(r[0]), r[1], r[2]) for r in rs1]
    write_str('\n'.join(l1), '%s/all_inters_0.05.txt' % d3)
    return d1


def analysis_metrics3(d0, d1, d2, lbs, iks):
    '''初步分级、效用参数: d0分析, d1信息, d2指标, lbs版本, pts索引'''
    # # # ### 全部指标导出、名称、说明
    # # ls = get_lines1('%s/metrics_cls.txt' % d1)
    # # mtnms = ast.literal_eval(ls[0])
    # # mtnms += ['']
    # # mtnms += ast.literal_eval(ls[2])
    # # mtnms += ['']
    # # mtnms += ast.literal_eval(ls[3])
    # # write_str(', \n'.join(mtnms), '%s/指标名称.txt' % d1)
    # # 0.标签整理——2合1模型
    # pts = txt_to_list('%s/idx.txt' % d1)
    # p0 = txt_to_list('%s/idx0.txt' % d1)
    # l1 = []
    # for i in [9, 11]:
    #     l1 += p0[i]
    # random.shuffle(l1)  # 一次性抽取——
    # l0 = [[[], []], [[], []], [[], []], [[], []], [[], []], [[], []]]
    # # l0 += l0.copy()
    # l0[0][0] = pts[0][0]
    # l0[1][0] = pts[1][0]
    # l0[2][0] = pts[2][0]
    # l0[0][1] = pts[0][1]+l1[:51]
    # l0[1][1] = pts[1][1]+l1[51:51+13]
    # l0[2][1] = pts[2][1]+l1[51+13:51+13+18]
    # l1 = []
    # for i in [1, 4, 5, 7, 8, 10, 14]:
    #     l1 += p0[i]
    # random.shuffle(l1)  # 一次性抽取——
    # l0[3][0] = pts[0][0]
    # l0[4][0] = pts[1][0]
    # l0[5][0] = pts[2][0]
    # l0[3][1] = l1[:549]
    # l0[4][1] = l1[549:549+138]
    # l0[5][1] = l1[549+138:549+138+174]
    # write_str('\n'.join([str(l0[i]) for i in range(len(l0))]), '%s/idx1.txt' % d1)
    # 1.自动化分级和排序
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    print(len(mtnms))
    pts = txt_to_list('%s/idx1.txt' % d1)  # 标签索引
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    d3 = '%s/%s' % (d0, lbs)
    new_folder(d3)
    strs1, strs2 = [], []
    # i = 0
    for i in range(len(mtnms)):
        item = mtnms[i]
        print('start:', i+1, item, 'end:', len(mtnms)-i-1)
        lis0 = txt_to_list('%s/%s.txt' % (d2, item))
        mtlis = []
        for ids2 in [ids0, ids1]:
            for j in range(2):
                mtlis.append([lis0[j2] for j2 in ids2[j]])
        rlis, xys2 = make_ranks1(mtlis[0], mtlis[1])  # 自动化分级
        strs1.append(str([rlis, xys2]))
        l1, l2 = [], []
        rkme0 = [rlis, xys2]
        for j in range(len(mtlis[0])):
            mt0 = mtlis[0][j]
            l1.append(diag_res0(mt0, rkme0))
        for j in range(len(mtlis[1])):
            mt0 = mtlis[1][j]
            l2.append(diag_res0(mt0, rkme0))
        auc, acc, f1sc = cal_utility1(l1, l2)
        strs2.append(str([auc, acc, f1sc]))
    write_str('\n'.join(strs1), '%s/ranks0.txt' % d3)
    write_str('\n'.join(strs2), '%s/utility_all.txt' % d3)
    # 排序——auc>acc>f1sc
    lis1 = txt_to_list('%s/utility_all.txt' % d3)
    lis2 = txt_to_list('%s/ranks0.txt' % d3)
    data = np.array(lis1)
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    lis2 = [lis2[i] for i in list(idex)]
    save_txt(lis1, 'list', '%s/utility_sort.txt' % d3)
    save_txt(lis2, 'list', '%s/rank1.txt' % d3)
    write_str(str(list(idex)), '%s/utility_idx.txt' % d3)
    # 筛选 80+41
    n1 = 80
    mtnms0 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    mtnms1 = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[1])
    rkls0 = get_lines1('%s/rank1.txt' % d3)
    mtidx = txt_to_list('%s/utility_idx.txt' % d3)[0]
    mtnms = [mtnms0[mtidx[i]] for i in range(n1)]  # 取前80个指标
    rkls = rkls0[:n1]
    mtnms1 = [mtnms1[i] for i in range(len(mtnms1)) if mtnms1[i] not in mtnms]
    mtnms += mtnms1
    d4 = '%s/%s' % (d1, lbs)
    new_folder(d4)
    write_str('\n'.join(mtnms), '%s/mts.txt' % d4)  # 写入指标
    for i in range(len(mtnms1)):
        mt0 = mtnms1[i]
        for j in range(len(mtnms0)):
            if mtnms0[j] == mt0:
                ik = j
                break
        for j in range(len(mtidx)):
            if mtidx[j] == ik:
                rkls.append(rkls0[j])
                break
    write_str('\n'.join(rkls), '%s/rks0.txt' % d4)  # 写入指标
    return d1


def analysis_metrics2(d0, d1, d2, lbs, iks):
    '''初步分级、效用参数: d0分析, d1信息, d2指标, lbs版本, pts索引'''
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    d3 = '%s/%s' % (d0, lbs)
    new_folder(d3)
    f1, f2 = '%s/ranks0.txt' % d3, '%s/utility_all.txt' % d3
    strs1, strs2 = [], []
    # i = 119
    for i in range(len(mtnms)):
        item = mtnms[i]
        print('start:', i+1, item, 'end:', len(mtnms)-i-1)
        lis0 = txt_to_list('%s/%s.txt' % (d2, item))
        mtlis = []
        for ids2 in [ids0, ids1]:
            for i in range(2):
                mtlis.append([lis0[j2] for j2 in ids2[i]])
        rlis, xys2 = make_ranks1(mtlis[0], mtlis[1])
        strs1.append(str([rlis, xys2]))
        ut0 = [[], [], [], []]
        for i1 in range(4):
            for j2 in range(len(mtlis[i1])):
                ut0[i1].append(xys2[-1]-50)
                for k2 in range(len(rlis)):
                    if mtlis[i1][j2] < rlis[k2]:
                        ut0[i1][j2] = xys2[k2]-50
                        break
        nm1 = round(100*(sum(ut0[1])-sum(ut0[0]))/(len(ut0[0]+ut0[1])))
        nm2 = round(100*(sum(ut0[3])-sum(ut0[2]))/(len(ut0[2]+ut0[3])))
        if nm1 < 0 or nm2 < 0:
            strs2.append(str([nm1, nm2, nm1+nm2, min(nm1, nm2), -1]))
        else:
            strs2.append(str([nm1, nm2, nm1+nm2, min(nm1, nm2), 1]))
    write_str('\n'.join(strs1), f1)
    write_str('\n'.join(strs2), f2)
    # 排序
    f3, f4 = '%s/utility_sort.txt' % d3, '%s/rank1.txt' % d3
    f5 = '%s/utility_idx.txt' % d3
    lis1, lis2 = txt_to_list(f2), txt_to_list(f1)
    data = np.array(lis1)
    idex = np.lexsort((-1 * data[:, 1], -1 * data[:, 0], -1 * data[:, 2], -1 * data[:, 3], -1 * data[:, 4]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    lis2 = [lis2[i] for i in list(idex)]
    save_txt(lis1, 'list', f3)
    save_txt(lis2, 'list', f4)
    write_str(str(list(idex)), f5)
    return d1


def analysis_metrics1(d0, d1, d2, lbs):
    '''tr/va划分、初步分级、效用参数: d0分析, d1信息, d2指标, lbs版本, pts索引'''
    # f1 = data_split0(d1)  # 数据分割-添加新索引-只能1次！
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    ids0, ids1 = pts[6], pts[7]
    d3 = '%s/%s' % (d0, lbs)
    new_folder(d3)
    f1, f2 = '%s/ranks0.txt' % d3, '%s/utility_all.txt' % d3
    strs1, strs2 = [], []
    # i = 4
    for i in range(len(mtnms)):
        item = mtnms[i]
        print('start:', i+1, item, 'end:', len(mtnms)-i-1)
        lis0 = txt_to_list('%s/%s.txt' % (d2, item))
        mtlis = []
        for ids2 in [ids0, ids1]:
            for i in range(2):
                mtlis.append([lis0[j2] for j2 in ids2[i]])
        rlis, xys2 = make_ranks1(mtlis[0], mtlis[1])
        strs1.append(str([rlis, xys2]))
        ut0 = [[], [], [], []]
        for i1 in range(4):
            for j2 in range(len(mtlis[i1])):
                ut0[i].append(xys2[-1]-50)
                for k2 in range(len(rlis)):
                    if mtlis[i1][j2] < rlis[k2]:
                        ut0[i1][j2] = xys2[k2]-50
                        break
        nm1 = round(100*(sum(ut0[1])-sum(ut0[0]))/(len(ut0[0]+ut0[1])))
        nm2 = round(100*(sum(ut0[3])-sum(ut0[2]))/(len(ut0[2]+ut0[3])))
        if nm1 < 0 or nm2 < 0:
            strs2.append(str([nm1, nm2, nm1+nm2, min(nm1, nm2), -1]))
        else:
            strs2.append(str([nm1, nm2, nm1+nm2, min(nm1, nm2), 1]))
    write_str('\n'.join(strs1), f1)
    write_str('\n'.join(strs2), f2)
    # 排序
    f3, f4 = '%s/utility_sort.txt' % d3, '%s/rank1.txt' % d3
    f5 = '%s/utility_idx.txt' % d3
    lis1, lis2 = txt_to_list(f2), txt_to_list(f1)
    data = np.array(lis1)
    data1 = np.array(lis2)
    idex = np.lexsort((-1 * data[:, 1], -1 * data[:, 0], -1 * data[:, 2], -1 * data[:, 3], -1 * data[:, 4]))
    sorted_data, sorted_data1 = data[idex], data1[idex]
    lis1 = sorted_data.tolist()
    lis2 = sorted_data1.tolist()
    save_txt(lis1, 'list', f3)
    save_txt(lis2, 'list', f4)
    write_str(str(list(idex)), f5)
    return d1


def sele_mts(d0, d1, d2, d3, lbs, iks):
    '''一票否决指标筛选: d0分析, d1信息, d2指标, d3分级, lbs版本'''
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    lis = []
    for i in range(len(mtnms)):
        item = mtnms[i]
        if 'disp' not in item:
            continue
        num = 0
        l0 = txt_to_list('%s/%s.txt' % (d2, item))
        d4 = '%s/%s' % (d3, lbs)
        new_folder(d4)
        mtlis = []
        for ids2 in [ids0, ids1]:
            for i2 in range(2):
                mtlis.append([l0[j2] for j2 in ids2[i2]])
        lis0 = False
        v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
        mode = 0
        xyss1 = make_ranks(mtlis[0], mtlis[1], mode=mode, v1=v1, lis0=lis0)
        l1 = get_0_100_nms(xyss1)
        v1 = max(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])+1
        mode = 1
        xyss = make_ranks(mtlis[2], mtlis[3], mode=mode, v1=v1, lis0=lis0)
        xyss.reverse()
        l2 = get_0_100_nms(xyss)
        if l1:
            num += l1[1]
        if l2:
            num += l2[1]
        lis.append([i, round(num)])
        print(item, l1, l2)
    data = np.array(lis)
    idx = np.lexsort((data[:, 1], -1 * data[:, 1]))
    strs = '\n'.join(['%s, %d' % (mtnms[i], data[i][1]) for i in idx])
    print(strs)
    # write_str(strs, '%s/%s_0_100_sort.txt' % (d0, lbs))
    # sorted_data = data[idex]
    # lis = sorted_data.tolist()
    # print(lis)
    # print([mtnms[i] for i in idex])
    # print('\n'.join([str(xyss[i]) for i in range(len(xyss))]))


def ranks_metrics3(d0, d1, d2, d3, lbs, iks):
    '''tr_va协同分级: d0模型, d1信息, d2指标, d3分级, lbs版本'''
    comb = txt_to_list('%s/%s/comb.txt' % (d0, lbs))[0][1]
    mtnms0 = get_item(get_lines(('%s/%s/utility_mts.txt' % (d0, lbs))), 0, [])
    mtnms = [mtnms0[i] for i in comb]
    # write_str('\n'.join(mtnms), '%s/%s/mts0.txt' % (d0, lbs))  # 单次写入——
    # rkls = get_lines1('%s/%s/utility_rks.txt' % (d0, lbs))
    # rkls = [rkls[i] for i in comb]
    # write_str('\n'.join(rkls), '%s/%s/rks0.txt' % (d0, lbs))  # 单次写入——
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    ids0, ids1 = [], []
    for i in range(len(iks[0])):
        ids0 += pts[iks[0][i]][0]
        ids1 += pts[iks[0][i]][1]
    for i in range(len(pts[iks[1][0]])):
        ids1 += pts[iks[1][0]][i]
    i = 18
    item = mtnms[i]
    print(item)
    l0 = txt_to_list('%s/%s.txt' % (d2, item))
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    f2 = '%s/%s.txt' % (d4, item)
    mtlis = []
    mtlis.append([l0[j2] for j2 in ids0])
    mtlis.append([l0[j2] for j2 in ids1])
    lis0, pers = [], []
    # lis0 = [0.053, 0.274]
    # pers = [62, 38, 50]
    mode = 0
    if not mode:
        v1 = min(mtlis[0]+mtlis[1])-1
    else:
        v1 = max(mtlis[0]+mtlis[1])+1
    # v1 = 0.053
    # xyss1 = make_ranks(mtlis[0], mtlis[1], f2, mode=mode, v1=v1, lis0=lis0)
    if pers:
        rkme0 = [lis0, pers]
        # rkme0 = [[0.071, 0.282], [60, 35, 55]]
        print(rkme0)
        print(get_utility1(l0, [ids0, ids1], rkme0))  # 联动效用因子计算
    return d4


def ranks_metrics4(d0, d1, d2, d3, lbs, iks):
    '''tr单独分级: anal_path, mess_path, metr_path, rank_path, lbs版本'''
    # # 整理指标
    # mts = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    # l1 = txt_to_list('%s/%s/utility_idx.txt' % (d0, lbs))[0]
    # strs = [mts[i] for i in l1]
    # write_str('\n'.join(strs), '%s/%s/mts.txt' % (d0, lbs))  # 提取排序指标——
    # new_folder('%s/%s' % (d1, lbs))
    # shutil.copyfile('%s/%s/mts.txt' % (d0, lbs), '%s/%s/mts.txt' % (d1, lbs))
    # 分级
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
    ids0 = [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    i = 20
    item = mtnms[i]
    print(item)
    l0 = txt_to_list('%s/%s.txt' % (d2, item))
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    f2 = '%s/%s.txt' % (d4, item)
    mtlis = []
    for i2 in range(2):
        mtlis.append([l0[j2] for j2 in ids0[i2]])
    lis0, pers = [], []
    lis0 = [-7, 9, 39]
    pers = [100, 55, 42, 100]
    mode = 0
    if not mode:
        v1 = min(mtlis[0]+mtlis[1])-1
    else:
        v1 = max(mtlis[0]+mtlis[1])+1
    v1 = -7
    xyss1 = make_ranks(mtlis[0], mtlis[1], f2, mode=mode, v1=v1, lis0=lis0)
    if pers:
        rkme0 = [lis0, pers]
        # rkme0 = [[130.9, 201.2], [40, 49, 60]]
        print(rkme0)
        print(get_utility1(l0, [ids0[0], ids0[1]], rkme0))  # 联动效用因子计算
    return d3


def ranks_metrics21(d1, d2, d3, lbs):
    '''心肌肥厚参数t/u检验: mess_path, metr_path, rank_path'''
    l1 = get_lines1('%s/metrics_cls.txt' % d1)
    # l1 = get_lines1('%s/metrics_cls1.txt' % d1)
    ids0, ids1 = txt_to_list('%s/idx.txt' % d1)[0]
    mtnms = ast.literal_eval(l1[0]) + ast.literal_eval(l1[1])
    # mtnms = ast.literal_eval(l1[0])
    ps = []
    for i in range(len(mtnms)):
        mt = mtnms[i]
        l1 = txt_to_list('%s/%s.txt' % (d2, mt))
        dt1, dt2 = [l1[i] for i in ids0], [l1[i] for i in ids1]
        # p0, _ = tucq_test0(dt1, dt2, 'u')
        p0, _ = tucq_test0(dt1, dt2, 't')
        ps.append([i, p0])
    data = np.array(ps)
    idex = np.lexsort((data[:, 0], data[:, 1]))
    d4 = '%s/%s' % (d1, lbs)
    new_folder(d4)
    l0 = ['%f' % ps[i][1] for i in idex]
    # write_str('\n'.join(l0), '%s/u_test.txt' % d4)
    # write_str('\n'.join([mtnms[i] for i in idex]), '%s/u_mts.txt' % d4)
    write_str('\n'.join(l0), '%s/t_test.txt' % d4)
    write_str('\n'.join([mtnms[i] for i in idex]), '%s/t_mts.txt' % d4)
    return d3


def auto_ranks_mts2(d1, d2, lbs, iks):
    '''tr自动化多区间分级: d1信息, d2指标, lbs版本, iks索引'''
    d3 = '%s/%s' % (d1, lbs)
    new_folder(d3)
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
    ids0 = [[], []]
    rks = []
    for i in range(len(iks[0])):  # 只有训练集——
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(mtnms)):
        item = mtnms[i]
        print(i+1, item)
        l0 = txt_to_list('%s/%s.txt' % (d2, item))
        mtlis = []
        for i2 in range(2):
            mtlis.append([l0[j2] for j2 in ids0[i2]])
        v1 = min(mtlis[0]+mtlis[1])-1
        xyss1 = make_ranks2(mtlis[0], mtlis[1], mode=0, v1=v1, lis0=[])
        na = max(len(mtlis[0]), len(mtlis[1]))
        rts = auto_rks0(xyss1, item, na)
        rks.append(rts)
    save_txt(rks, 'list', '%s/rks.txt' % d3)
    return d1


def auto_ranks_mts0(d1, d2, lbs, iks):
    '''tr自动化多区间分级: d1信息, d2指标, lbs版本, iks索引'''
    d3 = '%s/%s' % (d1, lbs)
    new_folder(d3)
    # 1.自动化多区间分级(trva/tr)
    # mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    rks, rks1 = [], []
    for i in range(len(mtnms)):
        # if mtnms[i] != 'pcdm_TT_q':
        #     continue
        # if i+1 != 135:
        #     continue
        item = mtnms[i]
        print(i+1, item)
        l0 = txt_to_list('%s/%s.txt' % (d2, item))
        mtlis = []
        for ids2 in [ids0, ids1]:
            for i2 in range(2):
                mtlis.append([l0[j2] for j2 in ids2[i2]])
        v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
        # xyss1 = make_ranks2(mtlis[0], mtlis[1], mode=0, v1=v1, lis0=[])
        xyss1 = make_ranks2(mtlis[0]+mtlis[2], mtlis[1]+mtlis[3], mode=0, v1=v1, lis0=[])
        # # d5 = '%s/pts_raw7' % d3
        # # new_folder(d5)
        # rts = auto_rks0(xyss1, d5, item)
        rts = auto_rks0(xyss1, item)
        rks.append(rts)
        xyss1 = make_ranks2(mtlis[0], mtlis[1], mode=0, v1=v1, lis0=[])
        rts = auto_rks0(xyss1, item)
        rks1.append(rts)
        # if rts[0]:
        #     print(i+1, item, rts)
        # if max(rts[1])>100:  # 检查错误分级结果
        #     print(i+1, item, rts)
        # elif min(rts[1])<0:
        #     print(i+1, item, rts)
        # elif min(rts[1])>50 or max(rts[1])<50:
        #     print(i+1, item, rts)
    # save_txt(rks, 'list', '%s/%s_auto1/rks.txt' % (d1, lbs))
    save_txt(rks, 'list', '%s/rks_trva.txt' % d3)
    # save_txt(rks1, 'list', '%s/rks_tr.txt' % d3)
    # 2.合并trva和tr, 得到公共分级tr-va
    l1 = txt_to_list('%s/rks_trva.txt' % d3)
    l2 = txt_to_list('%s/rks_tr.txt' % d3)
    l3 = [hb_ranks0(l1[i], l2[i]) for i in range(len(l1))]
    save_txt(l3, 'list', '%s/rks_tr-va.txt' % d3)
    return d1


def sqop_xy0(d1):
    '''总体效用计算: d1信息; 指标自动化分级和排序后, 对照计算auc/acc/F1s的误差, 再排序'''
    # # 1.两两对比-误差从小到大排序
    # for vs1, vs2, tx0 in [['squid1','squid2','sqsq'], ['squid2','opm','sqop']]:
    # # vs1, vs2 = 'squid1', 'squid2'  # 选择对比数据集——
    # # d2 = '%s/%s' % (d1, 'sqsq')
    # # vs1, vs2 = 'squid2', 'opm'
    #     d2 = '%s/%s' % (d1, tx0)
    #     new_folder(d2)
    #     mt1 = get_lines1('%s/%s/utility_mts.txt' % (d1, vs1))
    #     mt2 = get_lines1('%s/%s/utility_mts.txt' % (d1, vs2))
    #     idx2 = get_idxs(mt2, mt1, [])
    #     l1 = txt_to_list('%s/%s/utility_sort.txt' % (d1, vs1))
    #     l2 = txt_to_list('%s/%s/utility_sort.txt' % (d1, vs2))
    #     l2 = [l2[i] for i in idx2]  # 规置到mt1的顺序
    #     for i0, tx in [[0,'auc'],[1,'acc'],[2,'F1s']]:
    #         lis1 = [[i, abs(l1[i][i0]-l2[i][i0])] for i in range(len(l1))]
    #         data = np.array(lis1)
    #         idex = np.lexsort((data[:, 0], data[:, 1]))  # 从小到大排序
    #         sorted_data = data[idex]
    #         lis1 = sorted_data.tolist()
    #         lis1 = [[mt1[int(lis1[i][0])], round(lis1[i][1],3)] for i in range(len(lis1))]
    #         save_txt(lis1, 'list', '%s/utility_%s.txt' % (d2, tx))
    # 2.三者对比-误差排序
    vs1, vs2, vs3 = 'squid1', 'squid2', 'opm'
    d2 = '%s/%s' % (d1, 'sqsqop')
    new_folder(d2)
    mt1 = get_lines1('%s/%s/utility_mts.txt' % (d1, vs1))
    mt2 = get_lines1('%s/%s/utility_mts.txt' % (d1, vs2))
    mt3 = get_lines1('%s/%s/utility_mts.txt' % (d1, vs3))
    idx2 = get_idxs(mt2, mt1, [])
    idx3 = get_idxs(mt3, mt1, [])
    l1 = txt_to_list('%s/%s/utility_sort.txt' % (d1, vs1))
    l2 = txt_to_list('%s/%s/utility_sort.txt' % (d1, vs2))
    l3 = txt_to_list('%s/%s/utility_sort.txt' % (d1, vs3))
    l2 = [l2[i] for i in idx2]  # 规置到mt1的顺序
    l3 = [l3[i] for i in idx3]  # 规置到mt1的顺序
    for i0, tx in [[0,'auc'],[1,'acc'],[2,'F1s']]:
        lis1 = [[i, abs(l1[i][i0]-l2[i][i0]), abs(l3[i][i0]-l2[i][i0])] for i in range(len(l1))]
        data = np.array(lis1)
        idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
        sorted_data = data[idex]
        lis1 = sorted_data.tolist()
        lis1 = [[mt1[int(lis1[i][0])], round(lis1[i][1],3), round(lis1[i][2],3)] for i in range(len(lis1))]
        save_txt(lis1, 'list', '%s/utility_%s.txt' % (d2, tx))
    return d1


def sqop_xy1(d1, d3, iks):
    '''个体效用计算: d1信息, d3诊断, iks索引; 自动诊断的阴阳错误率/平均差/差阴阳50, 再排序'''
    vs1, vs2, vs3 = 'squid1', 'squid2', 'opm'
    d2 = '%s/%s' % (d1, 'sqsqop')
    new_folder(d2)
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
    ids0 = [[], []]
    for i in range(len(iks[0])):  # 只有训练集——
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    ids = ids0[0]+ids0[1]
    lis1, lis2, lis3 = [], [], []
    for i in range(len(mtnms)):
        item = mtnms[i]
        l1 = txt_to_list('%s/%s/%s.txt' % (d3, vs1, item))
        l2 = txt_to_list('%s/%s/%s.txt' % (d3, vs2, item))
        l3 = txt_to_list('%s/%s/%s.txt' % (d3, vs3, item))
        l1 = [l1[j] for j in ids]
        l2 = [l2[j] for j in ids]
        l3 = [l3[j] for j in ids]
        dg_err1 = sum(1 for j in range(len(l1)) if (l1[j]-50)*(l2[j]-50)<0)/len(l1)
        dg_err2 = sum(1 for j in range(len(l2)) if (l3[j]-50)*(l2[j]-50)<0)/len(l2)
        lis1.append([i, round(dg_err1,3), round(dg_err2,3)])
        dg_eab1 = sum([abs(l1[j]-l2[j]) for j in range(len(l1))])/(len(l1)*100)
        dg_eab2 = sum([abs(l3[j]-l2[j]) for j in range(len(l2))])/(len(l2)*100)
        lis2.append([i, round(dg_eab1,3), round(dg_eab2,3)])
        ls1, ls2 = [], []
        for j in range(len(l2)):
            if (l1[j]-50)*(l2[j]-50)>=0:  # 同阴阳
                ls1.append(abs(l1[j]-l2[j]))
            else:
                ls1.append(50)
            if (l3[j]-50)*(l2[j]-50)>=0:  # 同阴阳
                ls2.append(abs(l3[j]-l2[j]))
            else:
                ls2.append(50)
        lis3.append([i, round(sum(ls1)/(len(l1)*100),3), round(sum(ls2)/(len(l2)*100),3)])
    for ls, tx in [[lis1,'dg_err'],[lis2,'dg_eab'],[lis3,'dg_epn']]:
        data = np.array(ls)
        idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
        sorted_data = data[idex]
        ls = sorted_data.tolist()
        ls = [[mtnms[int(ls[i][0])], round(ls[i][1],3), round(ls[i][2],3)] for i in range(len(ls))]
        save_txt(ls, 'list', '%s/utility_%s.txt' % (d2, tx))
    return d1


def sqop_xiaoyong0(d1, d2, d3, iks):
    '''squid_opm效用不变性分析: d1信息, d2指标, d3诊断, iks索引'''
    # # 1.自动化分级/诊断/效用排序-3个数据集
    # auto_ranks_mts2(d1, '%s/squid1' % d2, 'squid1', iks)
    # auto_ranks_mts2(d1, '%s/squid2' % d2, 'squid2', iks)
    # auto_ranks_mts2(d1, '%s/opm' % d2, 'opm', iks)
    # d00 = diag_results5(d1, '%s/squid1' % d2, d3, 'squid1', iks)
    # d00 = diag_results5(d1, '%s/squid2' % d2, d3, 'squid2', iks)
    # d00 = diag_results5(d1, '%s/opm' % d2, d3, 'opm', iks)
    # # 2.效用不变性指标计算
    # d00 = sqop_xy0(d1)
    d00 = sqop_xy1(d1, d3, iks)
    return d1


def sqop_fenbu0(d1, d2, iks):
    '''squid_opm分布不变性分析: d1信息, d2指标, iks索引'''
    # 1.分布曲线-数值分布/数量变化分布
    vs1, vs2, vs3 = 'squid1', 'squid2', 'opm'
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
    ids0 = [[], []]
    for i in range(len(iks[0])):  # 只有训练集——
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    na = max(len(ids0[0]), len(ids0[1]))
    ls1 = []
    for j in range(len(mtnms)):
        item = mtnms[j]
        print(j+1, item)
        l1 = txt_to_list('%s/%s/%s.txt' % (d2, vs1, item))
        l2 = txt_to_list('%s/%s/%s.txt' % (d2, vs2, item))
        l3 = txt_to_list('%s/%s/%s.txt' % (d2, vs3, item))
        mts1, mts2, mts3 = [], [], []
        for i2 in range(2):
            mts1.append([l1[j2] for j2 in ids0[i2]])
            mts2.append([l2[j2] for j2 in ids0[i2]])
            mts3.append([l3[j2] for j2 in ids0[i2]])
        # sqop_draw0(mts1, mts2, mts3, d1, item)  # 数值分布曲线
        xyss1 = make_ranks2(mts1[0], mts1[1], mode=0, v1=min(mts1[0]+mts1[1])-1, lis0=[])
        xyss2 = make_ranks2(mts1[0], mts2[1], mode=0, v1=min(mts2[0]+mts2[1])-1, lis0=[])
        xyss3 = make_ranks2(mts3[0], mts3[1], mode=0, v1=min(mts3[0]+mts3[1])-1, lis0=[])
        # sqop_draw1(xyss1, xyss2, xyss3, na, d1, item)  # 数量变化分布曲线
        l1 = mts1[0]+mts1[1]
        l2 = mts2[0]+mts2[1]
        l3 = mts3[0]+mts3[1]
        lis1 = [abs(l2[i]-l1[i]) for i in range(len(l1))]
        luocha1 = max(l1)-min(l1)
        data1 = np.array(lis1)
        fem1 = trim_mean(data1, 0.02, axis=0) / luocha1
        lis1 = [abs(l2[i]-l3[i]) for i in range(len(l2))]
        luocha2 = max(l2)-min(l2)
        data1 = np.array(lis1)
        fem2 = trim_mean(data1, 0.02, axis=0) / luocha2
        ls1.append([j, round(fem1, 3), round(fem2, 3)])
    # 2.波动误差平均
    data = np.array(ls1)
    idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
    sorted_data = data[idex]
    ls = sorted_data.tolist()
    ls = [[mtnms[int(ls[i][0])], round(ls[i][1],3), round(ls[i][2],3)] for i in range(len(ls))]
    save_txt(ls, 'list', '%s/sqsqop/distrib_fem.txt' % d1)
    # # 分级误差均值
    # l1 = txt_to_list('%s/%s/rks.txt' % (d1, vs1))
    # l2 = txt_to_list('%s/%s/rks.txt' % (d1, vs2))
    # l3 = txt_to_list('%s/%s/rks.txt' % (d1, vs3))
    # ls1, ls2 = sqop_draw2(l1, l2, l3, d1, mtnms)  # 分级误差均值
    # data = np.array(ls1)
    # idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
    # sorted_data = data[idex]
    # ls = sorted_data.tolist()
    # ls = [[mtnms[int(ls[i][0])], round(ls[i][1],3), round(ls[i][2],3)] for i in range(len(ls))]
    # save_txt(ls, 'list', '%s/sqsqop/distrib_eab.txt' % d1)
    # data = np.array(ls2)
    # idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
    # sorted_data = data[idex]
    # ls = sorted_data.tolist()
    # ls = [[mtnms[int(ls[i][0])], round(ls[i][1],3), round(ls[i][2],3)] for i in range(len(ls))]
    # save_txt(ls, 'list', '%s/sqsqop/distrib_epn.txt' % d1)
    return d1


def sqop_shuzhi0(d1, d2, iks):
    '''squid_opm数值稳定性分析: d1信息, d2指标, iks索引'''
    # 1.描述性统计量-截断均值/中位数/标准差
    vs1, vs2, vs3 = 'squid1', 'squid2', 'opm'
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
    ids0 = [[], []]
    for i in range(len(iks[0])):  # 只有训练集——
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    ids = ids0[0]+ids0[1]
    # lis1, lis2, lis3 = [], [], []
    # for j in range(len(mtnms)):
    #     item = mtnms[j]
    #     print(j+1, item)
    #     l1 = txt_to_list('%s/%s/%s.txt' % (d2, vs1, item))
    #     l2 = txt_to_list('%s/%s/%s.txt' % (d2, vs2, item))
    #     l3 = txt_to_list('%s/%s/%s.txt' % (d2, vs3, item))
    #     mts1, mts2, mts3 = [l1[i] for i in ids], [l2[i] for i in ids], [l3[i] for i in ids]
    #     ms1, zws1, bzc1 = sqop_dsta0(mts1, mts2)
    #     ms2, zws2, bzc2 = sqop_dsta0(mts2, mts3)
    #     lis1.append([j, ms1, ms2])
    #     lis2.append([j, zws1, zws2])
    #     lis3.append([j, bzc1, bzc2])
    # for ls, tx in [[lis1,'ms'],[lis2,'zws'],[lis3,'bzc']]:
    #     data = np.array(ls)
    #     idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
    #     sorted_data = data[idex]
    #     ls = sorted_data.tolist()
    #     ls = [[mtnms[int(ls[i][0])], round(ls[i][1],3), round(ls[i][2],3)] for i in range(len(ls))]
    #     save_txt(ls, 'list', '%s/sqsqop/data_%s.txt' % (d1, tx))
    # 2.Spearman斯皮尔曼相关系数/多项式拟合/拟合误差
    d3 = '%s/sqop/datafit_mts' % d1
    new_folder(d3)
    ls = []
    ls1, ls2, ls3, ls4 = [], [], [], []
    for j in range(len(mtnms)):
        item = mtnms[j]
        # if item != 'time_qrsttrt':
        #     continue
        print(j+1, item)
        l1 = txt_to_list('%s/%s/%s.txt' % (d2, vs1, item))
        l2 = txt_to_list('%s/%s/%s.txt' % (d2, vs2, item))
        l3 = txt_to_list('%s/%s/%s.txt' % (d2, vs3, item))
        l1, l2, l3 = [l1[i] for i in ids], [l2[i] for i in ids], [l3[i] for i in ids]
        lis1 = [[i, l1[i], l2[i]] for i in range(len(l1))]
        data = np.array(lis1)
        idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
        ys1 = [data[i, 1] for i in idex]
        ys2 = [data[i, 2] for i in idex]
        # l12 = sqop_draw3(ys1, ys2, d3, item)
        # rho1, _ = stats.spearmanr(ys1, ys2)
        lis1 = [[i, l2[i], l3[i]] for i in range(len(l2))]
        data = np.array(lis1)
        idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
        ys1 = [data[i, 1] for i in idex]
        ys2 = [data[i, 2] for i in idex]
        l23 = sqop_draw3(ys1, ys2, d3, item)
        # rho2, _ = stats.spearmanr(ys1, ys2)
        # ls.append([j, round(1-rho1, 3), round(1-rho2, 3)])  # spearman相关系数——
        # 拟合后, 截断平均、描述性统计量误差(sqsq不拟合)——
        ls1.append([j, round(cal_trim_er0(l1, l2), 3), round(cal_trim_er0(l23, l3), 3)])
        ms1, zws1, bzc1 = sqop_dsta0(l1, l2)
        ms2, zws2, bzc2 = sqop_dsta0(l23, l3)
        ls2.append([j, ms1, ms2])
        ls3.append([j, zws1, zws2])
        ls4.append([j, bzc1, bzc2])
    # data = np.array(ls)
    # idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
    # sorted_data = data[idex]
    # ls = sorted_data.tolist()
    # ls = [[mtnms[int(ls[i][0])], round(ls[i][1],3), round(ls[i][2],3)] for i in range(len(ls))]
    # save_txt(ls, 'list', '%s/sqsqop/data_spearman.txt' % d1)
    # 拟合后, 截断平均、描述性统计量误差——
    for ls,tx in [[ls1,'fem'],[ls2,'ms'],[ls3,'zws'],[ls4,'bzc']]:
        data = np.array(ls)
        idex = np.lexsort((data[:, 0], data[:, 2], data[:, 1]))  # 从小到大排序
        sorted_data = data[idex]
        ls = sorted_data.tolist()
        ls = [[mtnms[int(ls[i][0])], round(ls[i][1],3), round(ls[i][2],3)] for i in range(len(ls))]
        save_txt(ls, 'list', '%s/sqsqop/datafit_%s.txt' % (d1, tx))
    return d1


def sqop_shaixuan0(d1, d2):
    '''筛选和排序指标: d1信息, d2结果
    各不变形指标-筛除异常程度大的, 3种不变性递进排序'''
    d3 = '%s/sqsqop' % d2
    new_folder(d3)
    # # 1.筛选和排序指标
    # a = [0.1, 0.1, 0.3, 0.6, 0.13, 0.3, 0.7, 0.26, 0.4, 0.8, 1.5, 1.3, 0.6, 0.7, 0.18, 0.5, 0.8]
    # b = ['utility_auc', 'utility_acc', 'utility_F1s', 'utility_dg_err', 'utility_dg_eab', 'utility_dg_epn', 'distrib_fem', 'distrib_eab', 'distrib_epn', 'data_spearman', 'data_ms', 'data_zws', 'data_bzc', 'datafit_fem', 'datafit_ms', 'datafit_zws', 'datafit_bzc']
    # c = ['utility_auc', 'utility_acc', 'distrib_fem', 'distrib_eab', 'data_zws', 'datafit_ms', 'data_spearman']
    # l0 = txt_to_list('%s/sqsqop/utility_auc.txt' % d1)
    # mtnms = [l0[i][0] for i in range(len(l0))]
    # mts1 = []
    # for i in range(len(b)):  # 筛选异常度高的指标
    #     l1 = txt_to_list('%s/sqsqop/%s.txt' % (d1, b[i]))
    #     for ls1 in l1:
    #         if ls1[1]>=a[i] or ls1[2]>=a[i]:
    #             mts1.append(ls1[0])
    # mtnms = [m0 for m0 in mtnms if m0 not in mts1]
    # l00 = [mtnms]
    # for i in range(len(c)):  # 统计排序信息uti/dis/dat/all/spearman
    #     l1 = txt_to_list('%s/sqsqop/%s.txt' % (d1, c[i]))
    #     idx = get_idxs([l1[i][0] for i in range(len(l1))], mtnms, [])
    #     l00.append([l1[i][2] for i in idx])  # sqsq的auc/acc全为0, 不添加
    # lis1 = [sqop_getls0(l00, i) for i in range(len(mtnms))]
    # data = np.array(lis1)
    # idex = np.lexsort((data[:, 4], data[:, 2], data[:, 1], data[:, 0], data[:, 3]))
    # l00 = [[l00[i][j] for i in range(len(l00))] for j in range(len(mtnms))]
    # l01 = [l00[i] for i in idex]
    # lis2 = [[mtnms[i]]+lis1[i] for i in idex]
    # save_txt(l00, 'list', '%s/all_mgs.txt' % d3)
    # save_txt(l01, 'list', '%s/sorted_mgs.txt' % d3)
    # save_txt(lis2, 'list', '%s/sorted_rts.txt' % d3)
    # # 再筛选: 3均值<0.04, spearman<0.3, 均值s<0.07
    # l0 = txt_to_list('%s/sorted_rts.txt' % d3)
    # l1 = [l for l in l0 if sqop_sx0(l)]
    # save_txt(l1, 'list', '%s/sorted_rts1.txt' % d3)
    # # 2.整合不同类型的指标-前缀, 添加序号
    # l0 = txt_to_list('%s/sorted_rts1.txt' % d3)
    # ms = [l0[i][0] for i in range(len(l0))]
    # ms = [m[:m.index('_')+1] for m in ms]
    # ms = list(set(ms))
    # for tx in ms:
    #     l1 = [l0[i]+[i+1] for i in range(len(l0)) if tx in l0[i][0]]
    #     l2 = [[l[-1] for l in l1]]+[l[:-1] for l in l1]
    #     save_txt(l2, 'list', '%s/%stops.txt' % (d3, tx))
    
    a = ['mfm_TT_ta', 'mfm_TT_q', 'mfm_RS_rsa', 'pcdm_RS_q', 'pcdm_TT_rca', 'mfm_RS_sa', 'mfm_TT_areaf', 'time_tpnrt1', 'space_rtsgnum2', 'mfm_QR_ra', 'pcdm_TT_iami', 'pcdm_RS_qra', 'mfm_disp1_Q_n', 'mfm_TTtj_n_snbd', 'time_rtrt3', 'pcdm_QR_center', 'mfm_QR_q', 'mfm_QR_nm', 'pcdm_QR_aread', 'mfm_disp1_T_n', 'mfm_disp1_S_p', 'mfm_QR_aread']
    # l0 = txt_to_list('%s/sqsqop/utility_dg_err.txt' % d1)
    l0 = txt_to_list('%s/sqsqop/sorted_rts1.txt' % d2)
    l1 = [l0[i][0] for i in range(len(l0))]
    a = [ai for ai in a if ai in l1]
    idx = get_idxs(l1, a)
    idx = sorted(idx)
    print([l1[i] for i in idx])
    print([l0[i][4] for i in idx])
    return d2


def sqop_datapre0(d0, d1, d2):
    '''squid_opm数据整理'''
    # # 1.数据整理
    # d00 = '%s/raw' % d0
    # d3 = '%s/error' % d0
    # d4 = '%s/squid2' % d0
    # d5 = '%s/opm' % d0
    # for item in os.listdir(d00):
    #     f0 = '%s/%s/b_squid_sim.txt' % (d00, item)
    #     f1 = '%s/%s.txt' % (d4, item)
    #     l1 = get_lines1(f0)
    #     l2 = [get_item(l1, i, [], jd=6) for i in range(37)]
    #     l3 = ['\t'.join([format(l2[i][j], '.6f') for i in range(len(l2))]) for j in range(len(l2[0]))]
    #     write_str('\n'.join(l3), f1)
    #     f0 = '%s/%s/b_opm_sim.txt' % (d00, item)
    #     f1 = '%s/%s.txt' % (d5, item)
    #     l1 = get_lines1(f0)
    #     l2 = [get_item(l1, i, [], jd=6) for i in range(37)]
    #     l3 = ['\t'.join([format(l2[i][j], '.6f') for i in range(len(l2))]) for j in range(len(l2[0]))]
    #     write_str('\n'.join(l3), f1)
    #     f0 = '%s/%s/error.txt' % (d00, item)
    #     f1 = '%s/%s.txt' % (d3, item)
    #     l1 = get_lines1(f0)
    #     l2 = [get_item(l1, i, [], jd=10) for i in range(2)]
    #     l3 = [[format(l2[i][j], '.10f') for i in range(len(l2))] for j in range(len(l2[0]))]
    #     save_txt(l3, 'list', f1)
    # # 2.指标生成——本地插值！
    # data_path, mess_path, metr_path = d0, d1, d2
    # d1 = '%s/squid1_all' % metr_path
    # d2 = '%s/squid2_all' % metr_path
    # d3 = '%s/opm_all' % metr_path
    # new_folder([d1, d2, d3])
    # ids0 = get_lines1('%s/ids.txt' % mess_path)
    # i1 = int(sys.argv[1])
    # k1 = max((i1-1)*30, 0)  # int(2162/30)+1=73次运行——
    # k2 = min(i1*30, len(ids0))
    # ids = [ids0[i] for i in range(k1, k2)]
    # d0 = '%s/squid1' % data_path
    # d1 = '%s/%d' % (d1, i1)  # 指标保存
    # new_folder(d1)
    # print('squid1')
    # d00 = get_metr0(d0, ids, d1, mess_path)
    # d0 = '%s/squid2' % data_path
    # d1 = '%s/%d' % (d2, i1)  # 指标保存
    # new_folder(d1)
    # print('squid2')
    # d00 = get_metr0(d0, ids, d1, mess_path)
    # d0 = '%s/opm' % data_path
    # d1 = '%s/%d' % (d3, i1)  # 指标保存
    # new_folder(d1)
    # print('opm')
    # d00 = get_metr0(d0, ids, d1, mess_path)
    # # 汇总
    # d11 = d1[:-4]
    # d21 = d2[:-4]
    # d31 = d3[:-4]
    # new_folder([d11, d21, d31])
    # for item in os.listdir('%s/1' % d1):
    #     l1 = []
    #     for i in range(1, 74):
    #         l1 += get_lines1('%s/%d/%s' % (d1, i, item))
    #     write_str('\n'.join(l1), '%s/%s' % (d11, item))
    #     l1 = []
    #     for i in range(1, 74):
    #         l1 += get_lines1('%s/%d/%s' % (d2, i, item))
    #     write_str('\n'.join(l1), '%s/%s' % (d21, item))
    #     l1 = []
    #     for i in range(1, 74):
    #         l1 += get_lines1('%s/%d/%s' % (d3, i, item))
    #     write_str('\n'.join(l1), '%s/%s' % (d31, item))
    return d0


def sqop_analy0(d0, d1):
    '''sqop建模: sqop数据集, 抽取外部验证, sq/op=tr/va
    955=414+541, te=83+108'''
    l0 = txt_to_list('%s/idx.txt' % d0)[0]
    l1, l2 = l0
    random.shuffle(l1)
    random.shuffle(l2)
    te0, te1 = l1[:83], l2[:108]
    tr0, tr1 = l1[83:], l2[108:]
    strs = str([tr0, tr1])+'\n'+str([te0, te1])
    # write_str('\n'+strs, '%s/idx.txt' % d0, 'a')  # 索引-一次性——
    l0 = txt_to_list('%s/sqsqop/sorted_rts1.txt' % d1)
    l1 = [l0[i][0] for i in range(len(l0))]
    save_txt([l1], 'list', '%s/metrics_cls1.txt' % d0)
    return d1


def sqop_autorks0(d1, d2, lbs, iks):
    '''自动化分级: mess_path, metr_path; squid2/opm=tr/va
    options: 参数集'''
    d3 = '%s/%s' % (d1, lbs)
    new_folder(d3)
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls1.txt' % d1)[0])
    write_str('\n'.join(mtnms), '%s/mts.txt' % d3)
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——idx.txt
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    rks = []
    for i in range(len(mtnms)):
        item = mtnms[i]
        print(i+1, item)
        l0 = txt_to_list('%s/squid2/%s.txt' % (d2, item))  # tr
        l1 = txt_to_list('%s/opm/%s.txt' % (d2, item))  # va
        mtlis = []
        for i2 in range(2):  # tr
            mtlis.append([l0[j2] for j2 in ids0[i2]])
        for i2 in range(2):  # va
            mtlis.append([l1[j2] for j2 in ids1[i2]])
        v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
        xyss1 = make_ranks2(mtlis[0]+mtlis[2], mtlis[1]+mtlis[3], mode=0, v1=v1, lis0=[])
        rts = auto_rks0(xyss1, item)
        rks.append(rts)
    save_txt(rks, 'list', '%s/rks_trva.txt' % d3)
    return d1


def sqop_diag0(d1, d2, d3, lbs, iks):
    '''诊断结果、效用因子计算和排序: d1信息 d2指标, d3诊断, lbs版本'''
    tx = 'trva'
    # # 1.单指标诊断
    # mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)))
    # rkmes = txt_to_list('%s/%s/rks_%s.txt' % (d1, lbs, tx))
    # d00 = diag_resu0('%s/squid2' % d2, mtnms, rkmes, d3, '%s_tr' % lbs)
    # d00 = diag_resu0('%s/opm' % d2, mtnms, rkmes, d3, '%s_va' % lbs)
    # 2.效用计算和排序
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    lis1 = []
    for i in range(len(mtnms)):
        item = mtnms[i]
        l0 = txt_to_list('%s/%s_tr/%s.txt' % (d3, lbs, item))
        l1 = [l0[j] for j in ids0[0]]
        l2 = [l0[j] for j in ids0[1]]
        l0 = txt_to_list('%s/%s_va/%s.txt' % (d3, lbs, item))
        l3 = [l0[j] for j in ids1[0]]
        l4 = [l0[j] for j in ids1[1]]
        auc, acc, f1sc = cal_utility1(l1+l3, l2+l4)
        lis1.append([auc, acc, f1sc])
    data = np.array(lis1)
    # auc>acc>F1得分
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    save_txt(lis1, 'list', '%s/%s/utility_sort_%s.txt' % (d1, lbs, tx))
    lns = get_lines1('%s/%s/rks_%s.txt' % (d1, lbs, tx))
    lns = [lns[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_rks_%s.txt' % (d1, lbs, tx))
    lns = [mtnms[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_mts_%s.txt' % (d1, lbs, tx))
    return d2


def sqop_diag1(d1, d2, d3, lbs, iks):
    '''诊断结果、效用因子计算和排序: d1信息 d2指标, d3诊断, lbs版本'''
    tx = 'trva'
    # # 1.单指标诊断
    # mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)))
    # rkmes = txt_to_list('%s/%s/rks_%s.txt' % (d1, lbs, tx))
    # d00 = diag_resu0('%s/squid2' % d2, mtnms, rkmes, d3, '%s_tr' % lbs)
    # d00 = diag_resu0('%s/opm' % d2, mtnms, rkmes, d3, '%s_va' % lbs)
    # 2.效用计算和排序
    mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引——
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    lis1 = []
    for i in range(len(mtnms)):
        item = mtnms[i]
        l0 = txt_to_list('%s/%s_tr/%s.txt' % (d3, lbs, item))
        l1 = [l0[j] for j in ids0[0]]
        l2 = [l0[j] for j in ids0[1]]
        l0 = txt_to_list('%s/%s_va/%s.txt' % (d3, lbs, item))
        l3 = [l0[j] for j in ids1[0]]
        l4 = [l0[j] for j in ids1[1]]
        err = cal_err0(l1+l2, l3+l4)
        if err < 0.75:
            lis1.append([0, 0, err, 0])
        else:
            auc, acc, f1sc = cal_utility1(l1+l3, l2+l4)
            lis1.append([auc, acc, err, f1sc])
    data = np.array(lis1)
    # auc>acc>err>F1得分
    idex = np.lexsort((-1 * data[:, 3], -1 * data[:, 2], -1 * data[:, 1], -1 * data[:, 0]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    save_txt(lis1, 'list', '%s/%s/utility_sort_%s.txt' % (d1, lbs, tx))
    lns = get_lines1('%s/%s/rks_%s.txt' % (d1, lbs, tx))
    lns = [lns[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_rks_%s.txt' % (d1, lbs, tx))
    lns = [mtnms[i] for i in list(idex)]
    write_str('\n'.join(lns), '%s/%s/utility_mts_%s.txt' % (d1, lbs, tx))
    return d2


def sqop_search0(d0, d1, d2, lbs, nm, iks):
    '''快速组合搜索: d0信息, d1诊断, d2搜索, lbs版本
    可选的: tx, pts, comb0, save.txt'''
    tx = 'trva'  # trva/tr-va两种搜索方法
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_lines1(('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx)))
    rkl0 = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    l0, l01 = [], []  # tr/va诊断结果
    l1, l2 = [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s_tr/%s.txt' % (d1, lbs, mtnms[i])))
        l01.append(txt_to_list('%s/%s_va/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l01 = [[l01[j][k] for j in range(len(l01))] for k in range(len(l01[0]))]
    ls = [l0, l01, l1, l2]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引——
    comb0 = []
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜trva
    comb0 = sqop_scdg0(nm, 0, comb0, ls, ts, '%s/V1_tr_trva.txt' % d3, 11)
    comb0 = sqop_scdg0(nm, 1, comb0, ls, ts, '%s/V2_tr_va.txt' % d3, 8)
    comb0 = sqop_scdg0(nm, 2, comb0, ls, ts, '%s/V3_trva_trva.txt' % d3, 8)
    return d2


def sqop_search1(d0, d1, d2, lbs, nm, iks):
    '''快速组合搜索: d0信息, d1诊断, d2搜索, lbs版本
    可选的: tx, pts, comb0, save.txt'''
    tx = 'trva'  # trva/tr-va两种搜索方法
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_lines1(('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx)))
    rkl0 = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    l0, l01 = [], []  # tr/va诊断结果
    l1, l2 = [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s_tr/%s.txt' % (d1, lbs, mtnms[i])))
        l01.append(txt_to_list('%s/%s_va/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l01 = [[l01[j][k] for j in range(len(l01))] for k in range(len(l01[0]))]
    ls = [l0, l01, l1, l2]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引——
    comb0 = []
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜va
    comb0 = sqop_scdg1(nm, 0, comb0, ls, ts, '%s/V1_sp_trva.txt' % d3, 11)
    comb0 = sqop_scdg1(nm, 1, comb0, ls, ts, '%s/V2_sp_trva.txt' % d3, 8)
    comb0 = sqop_scdg1(nm, 2, comb0, ls, ts, '%s/V3_sp_trva.txt' % d3, 8)
    return d2


def sqop_search2(d0, d1, d2, lbs, nm, iks):
    '''快速组合搜索: d0信息, d1诊断, d2搜索, lbs版本
    可选的: tx, pts, comb0, save.txt'''
    tx = 'trva'  # trva/tr-va两种搜索方法
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_lines1(('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx)))
    rkl0 = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    l0, l01 = [], []  # tr/va诊断结果
    l1, l2 = [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s_tr/%s.txt' % (d1, lbs, mtnms[i])))
        l01.append(txt_to_list('%s/%s_va/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l01 = [[l01[j][k] for j in range(len(l01))] for k in range(len(l01[0]))]
    ls = [l0, l01, l1, l2]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引——
    comb0 = []
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜trva
    comb0 = sqop_scdg2(nm, 0, comb0, ls, ts, '%s/V1_tr_trva.txt' % d3, 8)
    comb0 = sqop_scdg2(nm, 1, comb0, ls, ts, '%s/V2_tr_va.txt' % d3, 5)
    comb0 = sqop_scdg2(nm, 2, comb0, ls, ts, '%s/V3_trva_trva.txt' % d3, 6)
    return d2


def sqop_search3(d0, d1, d2, lbs, nm, iks):
    '''快速组合搜索: d0信息, d1诊断, d2搜索, lbs版本
    可选的: tx, pts, comb0, save.txt'''
    tx = 'trva'  # trva/tr-va两种搜索方法
    d3 = '%s/%s' % (d2, lbs)
    new_folder(d3)
    mtnms = get_lines1(('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx)))
    rkl0 = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    l0, l01 = [], []  # tr/va诊断结果
    l1, l2 = [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s_tr/%s.txt' % (d1, lbs, mtnms[i])))
        l01.append(txt_to_list('%s/%s_va/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l01 = [[l01[j][k] for j in range(len(l01))] for k in range(len(l01[0]))]
    ls = [l0, l01, l1, l2]
    pts = txt_to_list('%s/idx.txt' % d0)  # 标签索引——
    comb0 = []
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜trva
    comb0 = sqop_scdg3(nm, 0, comb0, ls, ts, '%s/V1_sp_trva.txt' % d3, 8)
    comb0 = sqop_scdg3(nm, 1, comb0, ls, ts, '%s/V2_sp_trva.txt' % d3, 5)
    comb0 = sqop_scdg3(nm, 2, comb0, ls, ts, '%s/V3_sp_trva.txt' % d3, 6)
    return d2


def sqop_review0(d0, d1, d2, d3, lbs, iks):
    '''审查组合结果trva: d0信息, d1诊断, d2搜索, d3审查, lbs版本'''
    f1, tx = 'V3_sp_trva', 'trva'
    # f1, tx = 'V3_trva_trva', 'trva'
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    combs = txt_to_list('%s/%s/%s.txt' % (d2, lbs, f1))
    pts = txt_to_list('%s/idx.txt' % d0)  # 索引——
    rkl0 = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    mtnms = get_lines1(('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx)))
    l0, l01 = [], []  # tr/va诊断结果
    l1, l2 = [], []
    for i in range(len(mtnms)):
        l0.append(txt_to_list('%s/%s_tr/%s.txt' % (d1, lbs, mtnms[i])))
        l01.append(txt_to_list('%s/%s_va/%s.txt' % (d1, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
    l0 = [[l0[j][k] for j in range(len(l0))] for k in range(len(l0[0]))]
    l01 = [[l01[j][k] for j in range(len(l01))] for k in range(len(l01[0]))]
    ls = [l0, l01, l1, l2]
    ts = get_trva_idx0(pts, iks, [0], [1])  # tr搜va
    strs = ''
    for i in range(len(combs)):
        strs = sqop_revi0(ls, combs[i], ts, strs)
    write_str(strs, '%s/%s.txt' % (d4, f1))
    return d3


def sqop_test1(d0, d1, d2, d3, d4, ids):
    '''实际测试=诊断+综合诊断: mode_path, mess_path, metr_path, diag_path, test_path'''
    mtnms0 = get_lines1('%s/utility_mts_trva.txt' % d0)
    rkmes = txt_to_list('%s/utility_rks_trva.txt' % d0)
    d00 = diag_resu0(d2, mtnms0, rkmes, d3)  # 诊断
    _, comb, p0 = txt_to_list('%s/comb_trva.txt' % d0)[0]
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkmes[i] for i in comb]
    l4, l41 = [], []  # tr/va诊断结果
    l1, l2 = [], []
    l3, l31 = [], []  # tr/va指标结果
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s.txt' % (d3, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/%s.txt' % (d2, mtnms[i])))  # 指标
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    l00 = [0 for i in range(len(l0))]
    for i in range(len(l0)):
        if l0[i] >= p0:
            l00[i] = 1
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/ranks.txt' % d4)
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (ids[i], ', '.join([str(l3[j][i]) for j in range(len(l3))])) for i in range(len(ids))])
    write_str(strs, '%s/metrics.txt' % d4)
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (ids[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in range(len(ids))])
    write_str(strs, '%s/diagnosis.txt' % d4)
    strs = 'id, %s' % ', '.join(mtnms)
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (ids[i], l0[i], l5[i], l00[i]) for i in range(len(ids))])
    write_str(strs, '%s/results.txt' % d4)
    return d4


def sqop_test0(d0, d1, d2, d3, d4, lbs):
    '''外部测试结果te: mode_path, mess_path, metr_path, diag_path, resu_path'''
    tx = 'trva'
    d5 = '%s/%s_tr' % (d4, lbs)
    new_folder(d5)
    combs = txt_to_list('%s/%s/comb_%s.txt' % (d0, lbs, tx))[0]
    comb, p0 = combs[1], combs[2]
    idnms = get_item(get_lines('%s/ids.txt' % d1), 0, [])  # 标签——
    ids = txt_to_list('%s/idx.txt' % d1)[1]  # 标签索引————
    # ids = ids[0]+ids[1]
    strs = ['%s, 0' % idnms[ids[0][i]] for i in range(len(ids[0]))]
    strs += ['%s, 1' % idnms[ids[1][i]] for i in range(len(ids[1]))]
    write_str('\n'.join(strs), '%s/label_tr.txt' % d4)
    mtnms0 = get_lines1('%s/%s/utility_mts_%s.txt' % (d0, lbs, tx))
    rkls = txt_to_list(('%s/%s/utility_rks_%s.txt' % (d0, lbs, tx)))
    mtnms = [mtnms0[i] for i in comb]
    rkl0 = [rkls[i] for i in comb]
    l4, l41 = [], []  # tr/va诊断结果
    l1, l2 = [], []
    l3, l31 = [], []  # tr/va指标结果
    for i in range(len(mtnms)):
        l4.append(txt_to_list('%s/%s_tr/%s.txt' % (d3, lbs, mtnms[i])))
        l41.append(txt_to_list('%s/%s_va/%s.txt' % (d3, lbs, mtnms[i])))
        l1.append(max(rm_element(rkl0[i][1], 100)))
        l2.append(min(rkl0[i][1]))
        l3.append(txt_to_list('%s/squid2/%s.txt' % (d2, mtnms[i])))  # 指标
        l31.append(txt_to_list('%s/opm/%s.txt' % (d2, mtnms[i])))  # 指标
    l0 = [[l4[j][k] for j in range(len(l4))] for k in range(len(l4[0]))]
    l01 = [[l41[j][k] for j in range(len(l41))] for k in range(len(l41[0]))]
    n01, n00 = sum(l1)-50*len(l1), 50*len(l2)-sum(l2)  # 参数计算
    l0 = comb_res3(l0.copy(), n01, n00)  # 综合诊断
    l01 = comb_res3(l01.copy(), n01, n00)  # 综合诊断
    l5 = change_pers0(l0.copy(), p0)
    l6 = change_pers0(l01.copy(), p0)
    l00 = [0 for i in range(len(l0))]
    l02 = [0 for i in range(len(l01))]
    for i in range(len(l0)):
        if l0[i] >= p0:
            l00[i] = 1
        if l01[i] >= p0:
            l02[i] = 1
    strs = 'metric, [[values], [pers]]\n%s' % '\n'.join(['%s, %s' % (mtnms[i], str(rkl0[i])) for i in range(len(mtnms))])
    write_str(strs, '%s/rank_%s.txt' % (d5, tx))
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l3[j][i]) for j in range(len(l3))])) for i in ids])
    write_str(strs, '%s/metrics_squid2.txt' % d5)
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l31[j][i]) for j in range(len(l31))])) for i in ids])
    write_str(strs, '%s/metrics_opm.txt' % d5)
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l4[j][i]) for j in range(len(l4))])) for i in ids])
    write_str(strs, '%s/diagnosis_squid2.txt' % d5)
    strs = 'id, %s' % ', '.join(mtnms)
    strs += '\n%s' % '\n'.join(['%s, %s' % (idnms[i], ', '.join([str(l41[j][i]) for j in range(len(l41))])) for i in ids])
    write_str(strs, '%s/diagnosis_opm.txt' % d5)
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l0[i], l5[i], l00[i]) for i in ids])
    write_str(strs, '%s/results_squid2.txt' % d5)
    strs = 'id, Predict_raw, Predict_final, 0/1'
    strs += '\n'+'\n'.join(['%s, %d, %d, %d' % (idnms[i], l01[i], l6[i], l02[i]) for i in ids])
    write_str(strs, '%s/results_opm.txt' % d5)
    return d4


def auto_ranks_mts1(d1, d2, lbs, iks):
    '''tr自动化多区间分级: d1信息, d2指标, lbs版本, iks索引'''
    d3 = '%s/%s' % (d1, lbs)
    new_folder(d3)
    # 1.自动化多区间分级(trva/tr)
    # mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls1.txt' % d1)[0])
    pts = txt_to_list('%s/idx1.txt' % d1)  # 标签索引——
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    rks, rks1 = [], []
    for i in range(len(mtnms)):
        if mtnms[i] != 'pcdm_QR_rca':
            continue
        # if i+1 != 135:
        #     continue
        item = mtnms[i]
        # print(i+1, item)
        l0 = txt_to_list('%s/%s.txt' % (d2, item))
        mtlis = []
        for ids2 in [ids0, ids1]:
            for i2 in range(2):
                mtlis.append([l0[j2] for j2 in ids2[i2]])
        v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
        xyss1 = make_ranks2(mtlis[0], mtlis[1], mode=0, v1=v1, lis0=[])
        # xyss1 = make_ranks2(mtlis[0]+mtlis[2], mtlis[1]+mtlis[3], mode=0, v1=v1, lis0=[])
        d5 = '%s/dp_01' % d3
        new_folder(d5)
        rts = auto_rks0(xyss1, d5, item)
        # rts = auto_rks0(xyss1, item)
        # rks.append(rts)
        # xyss1 = make_ranks2(mtlis[0], mtlis[1], mode=0, v1=v1, lis0=[])
        # rts = auto_rks0(xyss1, item)
        # rks1.append(rts)
        if rts[0]:
            print(i+1, item, rts)
        # if max(rts[1])>100:  # 检查错误分级结果
        #     print(i+1, item, rts)
        # elif min(rts[1])<0:
        #     print(i+1, item, rts)
        # elif min(rts[1])>50 or max(rts[1])<50:
        #     print(i+1, item, rts)
    # save_txt(rks, 'list', '%s/%s_auto1/rks.txt' % (d1, lbs))
    # save_txt(rks, 'list', '%s/rks_trva.txt' % d3)
    # save_txt(rks1, 'list', '%s/rks_tr.txt' % d3)
    # # 2.合并trva和tr, 得到公共分级tr-va
    # l1 = txt_to_list('%s/rks_trva.txt' % d3)
    # l2 = txt_to_list('%s/rks_tr.txt' % d3)
    # l3 = [hb_ranks0(l1[i], l2[i]) for i in range(len(l1))]
    # save_txt(l3, 'list', '%s/rks_tr-va.txt' % d3)
    return d1


def ranks_metrics2(d1, d2, d3, lbs, iks):
    '''人工tr_va协同分级: d1信息, d2指标, d3分级, lbs版本'''
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    # mtnms = get_item(get_lines1('%s/%s/mts.txt' % (d1, lbs)), 0, [])
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls1.txt' % d1)[0])
    pts = txt_to_list('%s/idx1.txt' % d1)  # 标签索引——idx.txt
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    i2 = 0
    # for i in range(len(mtnms)):
    item = mtnms[i2]
    print(item)
    l0 = txt_to_list('%s/%s.txt' % (d2, item))
    f2 = '%s/%s_tr.txt' % (d4, item)
    f3 = '%s/%s_va.txt' % (d4, item)
    mtlis = []
    for ids2 in [ids0, ids1]:
        for i2 in range(2):
            mtlis.append([l0[j2] for j2 in ids2[i2]])
    lis0, pers = [], []
    # lis0 = [61.8, 74.7, 128.4]
    # pers = [72, 58, 46, 62]
    mode = 0
    if not mode:
        v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
    else:
        v1 = max(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])+1
    # v1 = 25
    xyss1 = make_ranks2(mtlis[0], mtlis[1], f2, mode=mode, v1=v1, lis0=lis0)
    xyss2 = make_ranks2(mtlis[2], mtlis[3], f3, mode=mode, v1=v1, lis0=lis0)
    if pers:
        rkme0 = [lis0, pers]
        # rkme0 = [[1.4], [46, 67]]
        print(rkme0)
        print(get_utility1(l0, [ids0[0]+ids1[0], ids0[1]+ids1[1]], rkme0))  # 联动效用因子计算
    return d4


def ranks_metrics1(d0, d1, d2, d3, lbs, iks):
    '''tr_va协同分级: d0分析, d1信息, d2指标, d3分级, lbs版本'''
    mtnms = ast.literal_eval(get_lines1('%s/metrics_cls.txt' % d1)[0])
    pts = txt_to_list('%s/idx.txt' % d1)  # 标签索引
    ids0, ids1 = [[], []], [[], []]
    for i in range(len(iks[0])):
        ids0[0] += pts[iks[0][i]][0]
        ids0[1] += pts[iks[0][i]][1]
    for i in range(len(iks[1])):
        ids1[0] += pts[iks[1][i]][0]
        ids1[1] += pts[iks[1][i]][1]
    mtidx = txt_to_list('%s/%s/utility_idx.txt' % (d0, lbs))[0]  # 指标dx
    i2 = 95
    item = mtnms[mtidx[i2]]
    print(item)
    l0 = txt_to_list('%s/%s.txt' % (d2, item))
    d4 = '%s/%s' % (d3, lbs)
    new_folder(d4)
    f2 = '%s/%s_tr.txt' % (d4, item)
    f3 = '%s/%s_va.txt' % (d4, item)
    mtlis = []
    for ids2 in [ids0, ids1]:
        for i2 in range(2):
            mtlis.append([l0[j2] for j2 in ids2[i2]])
    lis0 = False
    # lis0 = [56.3, 63.5, 69.6]
    v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
    # v1 = max(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])+1
    # v1 = 69.6
    mode = 0
    xyss1 = make_ranks(mtlis[0], mtlis[1], f2, mode=mode, v1=v1, lis0=lis0)
    xyss2 = make_ranks(mtlis[2], mtlis[3], f3, mode=mode, v1=v1, lis0=lis0)
    # xs1, ys1 = [xyss1[i2][0] for i2 in range(len(xyss1))], [xyss1[i2][-1] for i2 in range(len(xyss1))]
    # xs2, ys2 = [xyss2[i2][0] for i2 in range(len(xyss2))], [xyss2[i2][-2] for i2 in range(len(xyss2))]
    # print(xs1, ys1)
    # plt.plot(xs1, ys1)
    # plt.plot(xs2, ys2)
    # plt.plot([-5, 5], [0.5, 0.5], c='k')
    # plt.show()
    return d4


def ranks_metrics0(d0, d1, d2, d3, lbs):
    '''细化分级47有效+62辅助: d0分析, d1指标, d2分级, d3信息'''
    f1 = '%s/metrics_distrib_density_map/spect_10_mess_classify_hand.txt' % d0
    mtnms = get_item(get_lines1(f1)[1:], 0, [])  # 指标名称列表
    pts = txt_to_list('%s/idx.txt' % d3)  # 标签索引
    ids0, ids1 = pts[0], pts[1]
    # n0, n1, n2, n3 = len(ids0[0]), len(ids0[1]), len(ids1[0]), len(ids1[1])
    i2 = 26
    # for i2 in range(len(mtnms)):
    item = mtnms[i2]
    print(item)
    lis0 = txt_to_list('%s/%s.txt' % (d1, item))
    d4 = '%s/%s' % (d2, lbs)
    new_folder(d4)
    f2 = '%s/%s_tr.txt' % (d4, item)
    f3 = '%s/%s_te.txt' % (d4, item)
    mtlis = []
    for ids2 in [ids0, ids1]:
        for i2 in range(2):
            mtlis.append([lis0[j2] for j2 in ids2[i2]])
    lis0 = False
    # lis0 = [0.33, 1.08]
    v1 = min(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])-1
    v1 = max(mtlis[0]+mtlis[1]+mtlis[2]+mtlis[3])+1
    # v1 = 1.08
    mode = 1
    xyss1 = make_ranks(mtlis[0], mtlis[1], f2, mode=mode, v1=v1, lis0=lis0)
    xyss2 = make_ranks(mtlis[2], mtlis[3], f3, mode=mode, v1=v1, lis0=lis0)
    xs1, ys1 = [xyss1[i2][0] for i2 in range(len(xyss1))], [xyss1[i2][-1] for i2 in range(len(xyss1))]
    xs2, ys2 = [xyss2[i2][0] for i2 in range(len(xyss2))], [xyss2[i2][-2] for i2 in range(len(xyss2))]
    print(xs1, ys1)
    plt.plot(xs1, ys1)
    plt.plot(xs2, ys2)
    plt.plot([-5, 5], [0.5, 0.5], c='k')
    plt.show()
    # f4 = make_ranks(mtlis[0], mtlis[1], f2, mode=0, n12m=n12m, lis0=lis0, mode0=0)
    # f4 = make_ranks(mtlis[2], mtlis[3], f3, mode=0, n12m=n12m, lis0=lis0, mode0=0)
    return d1
