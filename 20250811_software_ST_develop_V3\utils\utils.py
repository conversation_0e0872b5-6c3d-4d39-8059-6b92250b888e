"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/01/18 16:40
desc: 小工具
    解压缩
    提取id和时刻点
environment: pfafn36
run:
    python utils.py
option:
    结果目录、心磁文档、时刻点文档, plt.show()
"""


import zipfile
import pandas as pd
from utils.StatPNs import *
import math
import numpy as np
import re
from shapely.geometry import Polygon, MultiLineString
from shapely.geometry.multipolygon import MultiPolygon
from shapely.ops import unary_union
# import matplotlib.pyplot as plt
import os
import cv2
import ast
import matplotlib.pyplot as plt
from scipy import signal
from scipy.ndimage import gaussian_filter1d

from utils.cal_interpolates import cal_interpolate, cal_interpolate0
from utils.gettimes import gettime
from utils.space_wave import SpaceWave
from utils.time_wave import TimeWave
from utils.data import Data


def addqs(qs, p1):
    pq1 = [0, 90]
    pq2 = [90, 180]
    pq3 = [180, 270]
    pq4 = [270, 360]
    pqs = [pq1, pq2, pq3, pq4]
    for j1 in range(4):
        if p1[0] <= pqs[j1][1] and pqs[j1][0] < p1[1]:  # 取低不取高
            pq = str(j1 + 1)
            if pq not in qs:
                qs.append(pq)
    return qs


def cal_cettrj_pts(matrixes_list, times_list, seqs=[1, 2]):
    '''
    中心轨迹点集计算，返回全部人员的点集列表
    matrixes_list插值矩阵list
    times_list时刻点list
    seqs起止时刻点, 如QpRp
    '''
    cttjpts = []
    for i11 in range(len(matrixes_list)):
        matrixes, times = matrixes_list[i11], times_list[i11]
        time1, time2 = int(times[seqs[0]]), int(times[seqs[1]])
        cjpts = cal_cjpts(matrixes, time1, time2)  # 计算中心轨迹点集
        cttjpts.append(cjpts)
    return cttjpts


def cal_cjpt(matrixes, i1):
    '''计算一帧的偶极子中心, 最多4个点'''
    matrix = matrixes[i1-1]
    px, py, nx, ny = get_dipole(matrix)
    cx, cy = (px + nx) / 2, (py + ny) / 2
    cors = get_dipole_ct(cx, cy)
    return cors


def cal_cjpts(matrixes, time1, time2=None):
    '''计算QR中心轨迹点集[[x0, y0], [x1, y1], ..., [xN, yN]], 或返回一帧中心轨迹'''
    if time2:
        cjpts = []
        for i1 in range(time1, time2+1):
            cjpt = cal_cjpt(matrixes, i1)
            for cors in cjpt:
                if cors not in cjpts:
                    cjpts.append(cors)
    else:
        cjpts = cal_cjpt(matrixes, time1)
    return cjpts


def cal_fr(matrixes, dfs, r_peak, s_peak):
    '''计算单帧信息PCDM, RS为例'''
    frfile = []
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        v, ca, x, y, dp, dv, mda = plot_pic(matrix, dfs)
        v, ca, x, y, dp, dv, mda = get_round([v, ca, x, y, dp, dv, mda], [6, 1, 0, 0, 1, 1, 1])
        frfile.append([j, v, ca, x, y, dp, dv, mda])
    Mv = max([frfile[j][1] for j in range(len(frfile))])
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        ca = frfile[j-r_peak][2]
        px, py, nx, ny = get_dipole(matrix)
        dx, dy = ny - py, px - nx  # 坐标和角度指向
        da = calangle(dx, dy)  # 指向角度
        q = calquad(ca)  # 象限
        r = abs(da - ca)
        ia = min(r, 360 - r)  # 夹角
        qrd = pow(dx*dx+dy*dy, 1/2)  # 偶极子距离
        fx, fy = calangcors(frfile[j-r_peak][1] / Mv, ca)
        mx, my = calangcors(qrd / pow(50*50+50*50, 1/2), ca)
        da, ia, fx, fy, mx, my = get_round([da, ia, fx, fy, mx, my], [1, 1, 3, 3, 3, 3])
        frfile[j-r_peak].extend([q, da, ia, fx, fy, mx, my])
    return frfile


def cal_frrt(matrixes, r_peak, s_peak):
    '''计算单帧信息和转角信息, RS为例'''
    Mf = calmf(matrixes, r_peak, s_peak)
    frfile = []
    for j in range(r_peak, s_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        a, q, qrd, fx, fy, mx, my, cx, cy = statpnpt(matrix, Mf)
        Z = norms0(matrix)  # 正负归一化
        pn = statpn(Z, vmin=0.3)  # 多极子
        dpp, dpn = caldp(pn)  # 离散度
        pnp, pnn = calpn(pn)  # 主极子数量
        ep, en = calplf(Z)  # 离心率
        a, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, ep, en = get_round([a, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, ep, en], [1, 1, 3, 3, 3, 3, 1, 1, 1, 1, 3, 3])
        frfile.append([j, a, q, qrd, fx, fy, mx, my, cx, cy, dpp, dpn, pnp, pnn, ep, en])  # 信息集
    return frfile


def cal_noqrs(lis0):
    '''综合QRS波形异常指标, 321分别表示Q/R/S异常'''
    noqrs = ''
    for i4 in range(len(lis0)):
        if lis0[i4] == 1:
            noqrs += str(len(lis0)-i4)
    if noqrs == '':
        return '0'
    else:
        return noqrs


def cal_pnptm(matrixes, mflis0, time12):
    '''获取时刻点修正信息, time12为时刻点区间'''
    pnptm2 = []
    for j1 in time12:
        matrix = matrixes[j1-1]
        mf = mflis0[j1-1]
        px, py, nx, ny = get_dipole(matrix)
        pna = calangle(ny-py, px-nx)  # 指向角度
        matrix = norms0(matrix)
        pn = statpn(matrix, vmin=0.3)
        dpp, dpn = caldp(pn)
        mf, dpp, dpn, pna = get_round([mf, dpp, dpn, pna], [1, 1, 1, 1])
        pnptm2.append([j1, mf, dpp, dpn, pna])
    return pnptm2


def cal_qra_q(alist):
    qs, ps = [], []
    for i1 in range(len(alist) - 1):
        difag = abs(alist[i1] - alist[i1 + 1])
        if difag < 180:
            p1 = (min(alist[i1], alist[i1 + 1]), max(alist[i1], alist[i1 + 1]))
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # 向量指向长度 == min-max
            ps.append(p2)
        else:
            p1 = (0, min(alist[i1], alist[i1 + 1]))
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # 0-min
            ps.append(p2)
            p1 = (max(alist[i1], alist[i1 + 1]), 360)
            qs = addqs(qs, p1)
            p2 = ((0, p1[0]), (0, p1[1]))  # max-360
            ps.append(p2)
    mp = MultiLineString(ps)
    qra = unary_union(mp).length  # 角度跨度
    q = statqs(qs)  # 象限
    return qra, q


def cal_stabs(dpp0, dpps, dpns, pntts, rg12):
    '''计算稳定性列表'''
    stabs = []
    if min(dpp0) >= 10:
        for j1 in rg12:
            if dpps[j1] > 10:  # 稳定阈值——
                dpps[j1] = 1
            else:
                dpps[j1] = 0
    else:
        for j1 in rg12:
            if dpp0[j1] >= 10:  # 稳定判定——
                dpps[j1] = 1
            else:
                dpps[j1] = 0
    for j1 in rg12:
        if dpns[j1] > 10:  # 稳定阈值——
            dpns[j1] = 1
        else:
            dpns[j1] = 0
        if pntts[j1] > 60:  # 稳定阈值——
            pntts[j1] = 1
        else:
            pntts[j1] = 0
        stabs.append(max(dpps[j1], dpns[j1], pntts[j1]))
    return stabs


def calalis(frfile, ik=1):
    '''正负指向指标'''
    alist = []
    for i1 in range(len(frfile)):
        alist.append(frfile[i1][ik])
    qa, ra = get_ht(frfile, ik=ik)  # Q、R波角度
    qra, q = cal_qra_q(alist)
    m1, m2, a1, a2 = stataf(alist)  # 角度范围
    return qa, ra, qra, q, m1, m2, a1, a2


def calalis_tt(frfile, t_idx):
    '''正负指向指标-TT'''
    alist, rtlist = [], []
    for i1 in range(len(frfile)):
        alist.append(frfile[i1][1])
        if i1 > 0:
            rota = abs(alist[-1]-alist[-2])
            rota = min(360-rota, rota)
            rtlist.append(rota)
    rtlist.sort()  # TT50、TT75
    tt50 = np.median(rtlist)  # 中位数
    tt75 = np.percentile(rtlist, (75))  # 第3四分位数
    ta = frfile[t_idx][1]  # T波角度
    tta, q = cal_qra_q(alist)
    m1, m2, a1, a2 = stataf(alist)  # 角度范围
    return ta, tt50, tt75, tta, q, m1, m2, a1, a2


def calangcors(V1, ca):
    fx = V1 * math.cos(ca / 180 * np.pi)
    fy = V1 * math.sin(ca / 180 * np.pi)
    return fx, fy


def calangle(dx, dy):
    '''[0, 360)'''
    z = math.sqrt(dx * dx + dy * dy)
    if dx >= 0 and dy >= 0:
        angle = math.degrees(math.asin(dy / z))
    elif dx < 0 and dy >= 0:
        angle = 180 - math.degrees(math.asin(dy / z))
    elif dx < 0 and dy < 0:
        angle = 180 + math.degrees(math.asin(-dy / z))
    elif dx >= 0 and dy < 0:
        angle = 360 - math.degrees(math.asin(-dy / z))
    return round(angle, 1)


def calarea(frfile, ix, iy):
    xss, yss, pgs = [], [], []
    for j1 in range(len(frfile)):
        xss.append(frfile[j1][ix])
        yss.append(frfile[j1][iy])
        if j1 > 0:
            pg1 = Polygon([[0, 0], [xss[-2], yss[-2]], [xss[-1], yss[-1]]])
            pgs.append(pg1)
    mpg = MultiPolygon(pgs)
    mpgv = unary_union(mpg).area  # 面积
    return mpgv


def calarea1(xss, yss):
    pgs = []
    for j1 in range(len(xss) - 1):
        pg1 = Polygon([[0, 0], [xss[j1], yss[j1]], [xss[j1 + 1], yss[j1 + 1]]])
        pgs.append(pg1)
    mpg = MultiPolygon(pgs)
    mpgv = unary_union(mpg).area  # 面积
    return mpgv


def calavacors(alis, lis):
    xss, yss = [], []
    for i1 in range(len(alis)):
        dx = np.cos(alis[i1] * np.pi / 180)
        dy = np.sin(alis[i1] * np.pi / 180)
        dz = 0
        for j1 in range(len(lis[i1][0])):
            fx, fy = lis[i1][0][j1], lis[i1][1][j1]
            fz = pow(fx*fx+fy*fy, 1/2)
            dz += fz
        dz = dz / len(lis[i1][0])  # 平均归一化幅值
        xs, ys = calptcors(dz, dx, dy)
        xss.append(xs)
        yss.append(ys)
    return xss, yss


def caldp(pns):
    disps = [0, 0]  # 无极子和单极子都是0
    wk = 2  # 权重
    for j1 in range(2):
        if len(pns[j1]) != 0:
            corx = pns[j1][0][1]
            cory = pns[j1][0][2]
            for j2 in range(1, len(pns[j1])):
                dx = abs(pns[j1][j2][1]-corx)
                dy = abs(pns[j1][j2][2]-cory)
                dk = pow(dx*dx+dy*dy, 1/2) / 10
                vk = pns[j1][j2][0]
                disps[j1] = disps[j1] + wk * dk * vk
    disps[0] = round(disps[0], 1)
    disps[1] = round(-1*disps[1], 1)
    return disps[0], disps[1]


def calfag(ags):
    ps = []
    for i1 in range(len(ags) - 1):
        difag = abs(ags[i1] - ags[i1 + 1])
        if difag < 180:
            p1 = (min(ags[i1], ags[i1 + 1]), max(ags[i1], ags[i1 + 1]))
            p2 = ((0, p1[0]), (0, p1[1]))  # 向量指向长度 == min-max
            ps.append(p2)
        else:
            p1 = (0, min(ags[i1], ags[i1 + 1]))
            p2 = ((0, p1[0]), (0, p1[1]))  # 0-min
            ps.append(p2)
            p1 = (max(ags[i1], ags[i1 + 1]), 360)
            p2 = ((0, p1[0]), (0, p1[1]))  # max-360
            ps.append(p2)
    mp = MultiLineString(ps)
    fag = unary_union(mp).length  # 角度跨度
    return fag


def caljr(frfile, rlis, seq, seqs1, seqs2):
    js, ns, tlis, alis, mse3, ts, ags, rts, fxs, fys, mxs, mys, xss, yss, xss1, yss1 = [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []
    for i1 in range(len(frfile)):
        fline = frfile[i1]
        tlis.append(fline[0])  # 时刻
        alis.append(fline[seq])  # 角度
        fxs.append(fline[seqs1[0]])  # 坐标..
        fys.append(fline[seqs1[1]])
        mxs.append(fline[seqs2[0]])
        mys.append(fline[seqs2[1]])
    tlis.append(fline[0] + 1)
    rlis1 = sorted(list(np.absolute(rlis)), reverse=True)  # 绝对值逆序
    for i1 in range(7):
        rmse = np.std(rlis1[i1:i1 + 3])
        mse3.append(rmse)
    m = 0
    if mse3[4] <= 2.23:
        for j1 in range(4):  # 前4个
            k1 = 3 - j1
            if mse3[k1] > 2.23:
                m = k1 + 1
                break
    else:
        for j1 in range(3):  # 5、6、7
            k1 = 6 - j1
            if mse3[k1] > 2.23:
                m = k1 + 1
                break
    rlis2 = rlis1[:m]  # 小波范围
    ths = [0]  # 起始点——
    m = 0
    rtss = []
    for i1 in range(len(rlis)):
        rtss.append(rlis[i1])
        if abs(rlis[i1]) in rlis2:
            rtss.pop(-1)
            rts.append(rtss)
            rtss = []
            m += 1
            js.append(rlis[i1])
            ths.append(i1 + 1)  # range——
        if i1 == len(rlis) - 1:
            rts.append(rtss)
    wg = rlis1[m]  # 小波范围
    ths.append(len(alis))  # 补充终点——
    for i1 in range(len(ths) - 1):
        ts.append([tlis[ths[i1]], tlis[ths[i1 + 1]]])  # 时刻段
        ags.append(alis[ths[i1]:ths[i1 + 1]])  # 角度
        xss.append(fxs[ths[i1]:ths[i1 + 1]])
        yss.append(fys[ths[i1]:ths[i1 + 1]])
        xss1.append(mxs[ths[i1]:ths[i1 + 1]])
        yss1.append(mys[ths[i1]:ths[i1 + 1]])
    for i1 in range(m + 1):
        if len(rts[i1]) != 0:
            a = ags[i1][0] - np.average(rts[i1])  # 平均角度=a1-av.rn
        else:
            a = ags[i1][0]
        if a >= 360:  # 规范角度
            a = a - 360
        elif a < 0:
            a = a + 360
        q = calquad(a)
        if len(ags[i1]) <= 1:
            f, r, v, mse, af, ad = 1, 0, 0, 0, 0, 0
        else:
            f = len(ags[i1])  # 帧数
            r = calfag(ags[i1])  # 计算角度转角——
            v, vlis = 0, []
            for j1 in range(len(rts[i1])):
                vlis.append(abs(rts[i1][j1]))
            v = np.average(vlis)  # 角速度
            mse = np.std(rts[i1])  # 均方差
            af = calarea1(xss[i1], yss[i1])
            ad = calarea1(xss1[i1], yss1[i1])
        ns.append([f, r, v, mse, a, q, af, ad])
    return wg, m, m+1, js, ts, ns


def calmas(frfile, ik, mode='max'):
    '''列表的最大(最小)、均值、均方差'''
    lis = []
    for i1 in range(len(frfile)):
        lis.append(frfile[i1][ik])
    dppma = max(lis)  # 最大值
    dppmi = min(lis)  # 最大值
    dppav = np.average(lis)  # 均值
    dppmse = np.std(lis)
    if mode == 'min':
        return dppma, dppmi, dppav, dppmse
    else:
        return dppma, dppav, dppmse


def calmf(matrixes, q_peak, r_peak):
    '''间期QR的最大幅值差'''
    flist = []
    for j1 in range(q_peak, r_peak + 1):
        matrix = matrixes[j1 - 1]  # 扩张心磁
        flist.append(matrix.max() - matrix.min())  # 幅值差
    Mf = max(flist)
    return Mf


def calmfl(frfile, ik):
    lis = []
    pnpflu = 0
    for i1 in range(len(frfile)):
        line = frfile[i1]
        lis.append(line[ik])
        if i1 > 1:
            if lis[-1] != lis[-2]:
                pnpflu += 1  # 波动次数
    pnpma = max(lis)  # 最大值
    return pnpma, pnpflu


def calns(ns, nsik=4):
    '''计算等级分布类别, nsik为等级数量'''
    qs = ''
    for i1 in range(nsik):
        if ns[nsik-1-i1] > 0:
            qs += str(nsik-1-i1)
    cdfile = [qs]
    for i1 in range(nsik):
        cdfile.append(ns[i1])
    if nsik == 5:
        mt1 = (2*ns[3]+4*ns[4]-2*ns[1]-4*ns[0]) / sum(ns[:nsik])
        mt2 = (3*ns[3]+5*ns[4]-3*ns[1]-5*ns[0]) / sum(ns[:nsik])
        mt1, mt2 = get_round([mt1, mt2], [2, 2])
        cdfile.extend([mt1, mt2])
    return cdfile


def calplf(Z):
    cors, pap, pan, mcgs = statpf(Z)
    lisc = [[], [], [], []]
    pe, ne = 0, 0  # 初始化——
    if pap != 0:
        for k1 in range(len(cors[0])):
            lisc[0].append(cors[0][k1][0])
            lisc[1].append(cors[0][k1][1])
        cpx = np.average(lisc[0])  # 中心点坐标
        cpy = np.average(lisc[1])
        for k1 in range(len(cors[0])):
            dx = cors[0][k1][0] - cpx
            dy = cors[0][k1][1] - cpy
            vk = mcgs[0][k1]
            dk = pow(dx * dx + dy * dy, 1 / 2)
            pe += dk * vk
        pe = pe / pap  # 离心率归一化=除磁极面积
    if pan != 0:
        for k1 in range(len(cors[1])):
            lisc[2].append(cors[1][k1][0])
            lisc[3].append(cors[1][k1][1])
        cnx = np.average(lisc[2])
        cny = np.average(lisc[3])
        for k1 in range(len(cors[1])):
            dx = cors[1][k1][0] - cnx
            dy = cors[1][k1][1] - cny
            vk = -mcgs[1][k1]
            dk = pow(dx * dx + dy * dy, 1 / 2)
            ne += dk * vk
        ne = ne / pan
    return pe, ne


def calpn(pns):  # 计算主极子数量
    pnp, pnn = 0, 0
    for i1 in range(len(pns[0])):
        if pns[0][i1][0] > 0.8:
            pnp += 1
    for i1 in range(len(pns[1])):
        if pns[1][i1][0] < -0.8:
            pnn += 1
    return pnp, pnn


def calptcors(V1, dx, dy):
    '''角度坐标计算'''
    dz = pow(dx * dx + dy * dy, 1 / 2)
    fx = V1 * dx / dz
    fy = V1 * dy / dz
    return fx, fy


def calqrc(frfile, ix, iy):
    xss, yss, pts = [], [], []
    qrcd, qrcar = 0, 0
    for j1 in range(len(frfile)):
        xss.append(frfile[j1][iy])
        yss.append(frfile[j1][ix])
        pts.append((xss[-1], yss[-1]))  # 点集
        if j1 > 0:
            dx = xss[-1] - xss[-2]
            dy = yss[-1] - yss[-2]
            qrcd += pow(dx * dx + dy * dy, 1 / 2)
    if len(pts) > 2:
        cvpts = convex_hull(pts)  # 凸包点集, 绘制——
        qrcar = getAreaOfPolyGonbyVector(cvpts)  # 面积
        qrcar = qrcar / 1000  # 归一化
    qrcre9 = calregion(yss, xss)  # 9区域分布
    return qrcd, qrcar, qrcre9


def calquad(p1):  # 计算象限
    pq1234 = [90, 180, 270, 360]
    for j1 in range(4):
        if p1 < pq1234[j1]:
            break
    return str(j1+1)


def calregion(xss, yss):  # 计算9区域分布[0, 33) [33, 67) [67, 100)=33, 34, 33
    rgs = [[0, 33], [33, 67], [67, 100]]  # 坐标取0/100——
    rg9s = []
    for i1 in range(len(xss)):
        for j1 in range(3):
            if xss[i1] >= rgs[j1][0] and xss[i1] < rgs[j1][1]:
                for j2 in range(3):
                    if yss[i1] >= rgs[j2][0] and yss[i1] <= rgs[j2][1]:
                        # rg9 = int(3 * (2 - j1) + j2 + 1)
                        rg9 = int(3 * j1 + j2 + 1)
                        if rg9 not in rg9s:
                            rg9s.append(rg9)
                        break
                break
    rg9s.sort()
    qrcre9 = ''
    for i1 in range(len(rg9s)):
        qrcre9 = qrcre9 + str(rg9s[i1])
    return qrcre9


def calrot(a1, a2):
    r = a2 - a1
    if r < -180:
        r = -360 - r
    elif r > 180:
        r = r - 360
    else:
        r = -r
    return r


def calrt(frfile, ik=2):
    '''计算角度'''
    rlis = []
    for i1 in range(1, len(frfile)):
        ra = calrot(frfile[i1-1][ik], frfile[i1][ik])
        rlis.append(ra)
    return rlis


def change_QR(pnptm2, q_peak):
    '''Q时刻点修正'''
    pnptm = pnptm2[5:]
    tim = 0
    for j1 in range(len(pnptm)):
        if pnptm[j1][1] >= 1:
            tim = j1  # 最后一个<1的序号
            break
    tim1 = 0
    if tim > 0:  # 初始<1.0
        stabs = statdppn_QR(tim, pnptm)
        for j1 in range(tim):
            jend = min(j1+3, tim)
            if max(stabs[j1:jend]) == 0:  # q_peak开始第一次稳定, 更新
                tim1 = j1
                break
    elif tim == 0:  # 初始>=1.0
        stabs = statdppn_QR(8, pnptm2)  # 5+q_peak3=8个值=[1, 9]
        for j2 in range(6):
            j1 = 5 - j2  # 逆序选取=5, 4, 3, 2, 1, 0
            if max(stabs[j1:j1+3]) == 0:  # q_peak逆序第一次稳定, 更新
                tim1 = -j2
                break
    if tim1 == 0:
        tim1 = tim
    return q_peak + tim1


def change_QS(timelis0, matrixes, mf):
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    pnptm2 = cal_pnptm(matrixes, mf, range(q_peak-5, r_peak+1))
    q_peak = change_QR(pnptm2, q_peak)  # Q异常修正
    pnptm2 = cal_pnptm(matrixes, mf, range(r_peak, s_peak+6))
    s_peak = change_RS(pnptm2, s_peak)  # S异常修正
    timelis0[0], timelis0[2] = q_peak, s_peak
    return timelis0


def change_RS(pnptm2, s_peak):
    '''S时刻点修正'''
    pnptm = pnptm2[:-5]
    tim = 1
    for j1 in range(len(pnptm)):
        if pnptm[len(pnptm)-1-j1][1] >= 1:
            tim = j1+1  # 最后一个<1的序号
            break
    tim1 = 0
    if tim > 1:  # 初始<1.0
        stabs = statdppn_RS(tim, pnptm)
        for j1 in range(tim-1):
            jend = min(j1+3, tim-1)
            if max(stabs[j1:jend]) == 0:  # q_peak开始第一次稳定, 更新
                tim1 = j1 + 1
                break
    elif tim == 1:  # 初始>=1.0
        stabs = statdppn_RS(9, pnptm2)  # 5+q_peak3=8个值=[1, 9]
        for j2 in range(6):
            j1 = 5 - j2  # 逆序选取=5, 4, 3, 2, 1, 0
            if max(stabs[j1:j1+3]) == 0:  # q_peak逆序第一次稳定, 更新
                tim1 = -j2 + 1
                break
    if tim1 == 0:
        tim1 = tim
    return s_peak - tim1 + 1


def comb_diag1(perlis):
    '''综合诊断-202310'''
    if type(perlis) == list:  # 最靠近0/100的一个或者多个值中的最大值
        for i4 in range(len(perlis)):
            perlis[i4] = comb_diag1(perlis[i4])
        if len(perlis) == 0:  # 返回空值
            return 50
        else:
            max_p = [max(x, 100-x) for x in perlis]
            max_p_id = [ik for ik, x in enumerate(max_p) if x == max(max_p)]
            perlis_max = [perlis[ik] for ik in max_p_id]
            return max(perlis_max)
    else:
        return perlis


def convex_hull(p):
    p = list(set(p))
    k = 0
    for i1 in range(1, len(p)):
        if p[i1][1] < p[k][1] or (p[i1][1] == p[k][1] and p[i1][0] < p[k][0]):
            k = i1
    pk = p[k]
    p.remove(p[k])
    p_sort = sort_points_tan(p, pk)  # 按与基准点连线和x轴正向的夹角排序后的点坐标
    p_result = [pk, p_sort[0]]
    for i1 in range(1, len(p_sort)):  # 叉乘为正删点;叉乘为负加点
        while (multiply(p_result[-2], p_sort[i1], p_result[-1]) > 0):
            p_result.pop()
        p_result.append(p_sort[i1])
    return p_result


def cv_interpolate(mfm_file, Qp, Te):
    '''计算100*100cv插值, 返回矩阵和最大幅值列表'''
    mlines = get_lines(mfm_file)
    Te = min(len(mlines), Te)
    matrixes, mf = [], []
    for ik in range(len(mlines)):
        if ik < Qp - 10 or ik >= Te + 10:
            matrixes.append(np.zeros((100, 100)))
            mf.append(0)
        else:
            mcgdata = mcg_float(mlines, ik)
            mf.append(max(mcgdata.max(), -1 * mcgdata.min()))
            matrix = cv2.resize(mcgdata, (100, 100), interpolation=cv2.INTER_CUBIC)
            matrixes.append(matrix)
    return matrixes, mf


def extractmt(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    Z = np.zeros((100, 100))
    for i1 in range(len(lines)):
        line = re.split(' |,', lines[i1].strip())
        line = rm_null(line)
        for j1 in range(1, len(line)):
            Z[i1][j1 - 1] = int(line[j1])
    return Z


def gen_isdir_list(dir_name):
    files = os.listdir(dir_name)
    isdir_list = []
    for f in files:
        if os.path.isdir(dir_name + '/' + f):
            isdir_list.append(True)
        else:
            isdir_list.append(False)
    return isdir_list


def getAreaOfPolyGonbyVector(points):  # 基于向量叉乘计算多边形面积
    area = 0
    if (len(points) < 3):
        return 0
        # raise Exception("error")
    for i1 in range(0, len(points) - 1):
        p1 = points[i1]
        p2 = points[i1 + 1]
        triArea = (p1[0] * p2[1] - p2[0] * p1[1]) / 2
        area += triArea
    fn = (points[-1][0] * points[0][1] - points[0][0] * points[-1][1]) / 2
    return abs(area + fn)


def get_arc(p1, p0):  # 反正切极角
    if (p1[0] - p0[0]) == 0:
        if ((p1[1] - p0[1])) == 0:
            return -1
        else:
            return math.pi / 2
    tan = float((p1[1] - p0[1])) / float((p1[0] - p0[0]))
    arc = math.atan(tan)
    if arc >= 0:
        return arc
    else:
        return math.pi + arc


def get_dipole(matrix):
    '''计算偶极子坐标'''
    px, py = np.unravel_index(np.argmax(matrix), matrix.shape)
    nx, ny = np.unravel_index(np.argmin(matrix), matrix.shape)
    return px, py, nx, ny


def get_dipole_ct(cx, cy):
    '''偶极子中心统计'''
    cors = []
    for x1 in [int(cx), round(cx)]:
        for y1 in [int(cy), round(cy)]:
            x1 = max(0, min(99, x1))  # 端点控制
            y1 = max(0, min(99, y1))
            if [x1, y1] not in cors:
                cors.append([x1, y1])
    return cors


def get_ht(frfile, ik=1):
    '''获取首尾值'''
    return frfile[0][ik], frfile[-1][ik]


def get_files(rootdir):
    f11 = '%s/QR1.txt' % rootdir
    f12 = '%s/QR2.txt' % rootdir
    f13 = '%s/QR3.txt' % rootdir
    f14 = '%s/QR4.txt' % rootdir
    f15 = '%s/QR5.txt' % rootdir
    f21 = '%s/RS1.txt' % rootdir
    f22 = '%s/RS2.txt' % rootdir
    f23 = '%s/RS3.txt' % rootdir
    f24 = '%s/RS4.txt' % rootdir
    f25 = '%s/RS5.txt' % rootdir
    f31 = '%s/TT1.txt' % rootdir
    f32 = '%s/TT2.txt' % rootdir
    f33 = '%s/TT3.txt' % rootdir
    f34 = '%s/TT4.txt' % rootdir
    f35 = '%s/TT5.txt' % rootdir
    f40 = '%s/all.txt' % rootdir
    write_str('id, qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread', f11)
    write_str('id, ra, sa, nq1, nq2, nq3, nq4, rsa, q, m1, m2, a1, a2, areaf, aread', f21)
    write_str('id, ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread', f31)
    write_str('qrdma, qrdav, qrdmse, qrcd, qrcar, qrcre9', f12)
    write_str('rsdma, rsdav, rsdmse, rscd, rscar, rscre9', f22)
    write_str('ttdma, ttdav, ttdmse, ttcd, ttcar, ttcre9', f32)
    for f0 in [f13, f23, f33]:
        write_str('id, dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu', f0)
    for f0 in [f14, f24, f34]:
        write_str('id, epma, epav, epmse, enma, enav, enmse', f0)
    for f0 in [f15, f25, f35]:
        write_str('id, wg, m, yn, pm, nm, r, v, mse, nqs0, nqs1, nqs2, nqs3, vqs0, vqs1, vqs2, vqs3, af, ad, v1, mse1, saf, sad', f0)
    write_str('id, QR, RS, TT, 综合per', f40)
    files = [f11, f12, f13, f14, f15, f21, f22, f23, f24, f25, f31, f32, f33, f34, f35, f40]
    return files


def mts459():
    '''20250120最新459个参数'''
    l1 = ['mfm_disp1_Q_p', 'mfm_disp1_Q_n', 'mfm_disp1_Q_pn', 'mfm_disp1_R_p', 'mfm_disp1_R_n', 'mfm_disp1_R_pn', 'mfm_disp1_S_p', 'mfm_disp1_S_n', 'mfm_disp1_S_pn', 'mfm_disp1_T_p', 'mfm_disp1_T_n', 'mfm_disp1_T_pn', 'mfm_QR_center', 'mfm_QR_mt2', 'mfm_QR_didma', 'mfm_QR_didav', 'mfm_QR_didmse', 'mfm_QR_dicd', 'mfm_QR_dicar', 'mfm_QR_dicre9', 'mfm_QR_wg', 'mfm_QR_nums', 'mfm_QR_pm', 'mfm_QR_nm', 'mfm_QR_r', 'mfm_QR_v', 'mfm_QR_mse', 'mfm_QR_af', 'mfm_QR_ad', 'mfm_QR_v1', 'mfm_QR_mse1', 'mfm_QR_saf', 'mfm_QR_sad', 'mfm_QR_dppma', 'mfm_QR_dppav', 'mfm_QR_dppmse', 'mfm_QR_dpnma', 'mfm_QR_dpnav', 'mfm_QR_dpnmse', 'mfm_QR_pnpma', 'mfm_QR_pnpflu', 'mfm_QR_pnnma', 'mfm_QR_pnnflu', 'mfm_QR_qa', 'mfm_QR_ra', 'mfm_QR_qra', 'mfm_QR_q', 'mfm_QR_areaf', 'mfm_QR_aread', 'mfm_QR_epma', 'mfm_QR_epav', 'mfm_QR_epmse', 'mfm_QR_enma', 'mfm_QR_enav', 'mfm_QR_enmse', 'mfm_RS_center', 'mfm_RS_mt2', 'mfm_RS_didma', 'mfm_RS_didav', 'mfm_RS_didmse', 'mfm_RS_dicd', 'mfm_RS_dicar', 'mfm_RS_dicre9', 'mfm_RS_wg', 'mfm_RS_nums', 'mfm_RS_pm', 'mfm_RS_nm', 'mfm_RS_r', 'mfm_RS_v', 'mfm_RS_mse', 'mfm_RS_af', 'mfm_RS_ad', 'mfm_RS_v1', 'mfm_RS_mse1', 'mfm_RS_saf', 'mfm_RS_sad', 'mfm_RS_dppma', 'mfm_RS_dppav', 'mfm_RS_dppmse', 'mfm_RS_dpnma', 'mfm_RS_dpnav', 'mfm_RS_dpnmse', 'mfm_RS_pnpma', 'mfm_RS_pnpflu', 'mfm_RS_pnnma', 'mfm_RS_pnnflu', 'mfm_RS_sa', 'mfm_RS_rsa', 'mfm_RS_q', 'mfm_RS_areaf', 'mfm_RS_aread', 'mfm_RS_epma', 'mfm_RS_epav', 'mfm_RS_epmse', 'mfm_RS_enma', 'mfm_RS_enav', 'mfm_RS_enmse', 'mfm_TT_center', 'mfm_TT_mt2', 'mfm_TT_didma', 'mfm_TT_didav', 'mfm_TT_didmse', 'mfm_TT_dicd', 'mfm_TT_dicar', 'mfm_TT_dicre9', 'mfm_TT_wg', 'mfm_TT_nums', 'mfm_TT_pm', 'mfm_TT_nm', 'mfm_TT_r', 'mfm_TT_v', 'mfm_TT_mse', 'mfm_TT_af', 'mfm_TT_ad', 'mfm_TT_v1', 'mfm_TT_mse1', 'mfm_TT_saf', 'mfm_TT_sad', 'mfm_TT_dppma', 'mfm_TT_dppav', 'mfm_TT_dppmse', 'mfm_TT_dpnma', 'mfm_TT_dpnav', 'mfm_TT_dpnmse', 'mfm_TT_pnpma', 'mfm_TT_pnpflu', 'mfm_TT_pnnma', 'mfm_TT_pnnflu', 'mfm_TT_ta', 'mfm_TT_tt50', 'mfm_TT_tt75', 'mfm_TT_tta', 'mfm_TT_q', 'mfm_TT_areaf', 'mfm_TT_aread', 'mfm_TT_epma', 'mfm_TT_epav', 'mfm_TT_epmse', 'mfm_TT_enma', 'mfm_TT_enav', 'mfm_TT_enmse', 'mfm_TTtj_p_melg', 'mfm_TTtj_p_mmlg', 'mfm_TTtj_p_mnbd', 'mfm_TTtj_p_atjpa', 'mfm_TTtj_p_atjsp', 'mfm_TTtj_p_atjec', 'mfm_TTtj_p_mtjpa', 'mfm_TTtj_p_mtjsp', 'mfm_TTtj_p_mtjec', 'mfm_TTtj_p_htjpa', 'mfm_TTtj_p_htjsp', 'mfm_TTtj_p_htjec', 'mfm_TTtj_p_rtjpa', 'mfm_TTtj_p_rtjsp', 'mfm_TTtj_p_rtjec', 'mfm_TTtj_n_melg', 'mfm_TTtj_n_mmlg', 'mfm_TTtj_n_mnbd', 'mfm_TTtj_n_atjpa', 'mfm_TTtj_n_atjsp', 'mfm_TTtj_n_atjec', 'mfm_TTtj_n_mtjpa', 'mfm_TTtj_n_mtjsp', 'mfm_TTtj_n_mtjec', 'mfm_TTtj_n_htjpa', 'mfm_TTtj_n_htjsp', 'mfm_TTtj_n_htjec', 'mfm_TTtj_n_rtjpa', 'mfm_TTtj_n_rtjsp', 'mfm_TTtj_n_rtjec', 'mfm_TTtj_br_melg0', 'mfm_TTtj_br_mndb0', 'mfm_TTtj_br_qra', 'mfm_TTtj_br_q', 'mfm_TTtj_br_areaf', 'mfm_TTtj_br_aread', 'mfm_TTtj_br_didma', 'mfm_TTtj_br_didmi', 'mfm_TTtj_br_didav', 'mfm_TTtj_br_didms', 'mfm_TTtj_br_ctjpa', 'mfm_TTtj_br_ctjsp', 'mfm_TTtj_br_ctjec', 'mfm_TTtj_sg_melg0', 'mfm_TTtj_sg_mndb0', 'mfm_TTtj_sg_qra', 'mfm_TTtj_sg_q', 'mfm_TTtj_sg_areaf', 'mfm_TTtj_sg_aread', 'mfm_TTtj_sg_didma', 'mfm_TTtj_sg_didmi', 'mfm_TTtj_sg_didav', 'mfm_TTtj_sg_didms', 'mfm_TTtj_sg_ctjpa', 'mfm_TTtj_sg_ctjsp', 'mfm_TTtj_sg_ctjec', 'mfm_TTtj_df_melg0', 'mfm_TTtj_df_mndb0', 'mfm_TTtj_df_qra', 'mfm_TTtj_df_q', 'mfm_TTtj_df_areaf', 'mfm_TTtj_df_aread', 'mfm_TTtj_df_didma', 'mfm_TTtj_df_didmi', 'mfm_TTtj_df_didav', 'mfm_TTtj_df_didms', 'mfm_TTtj_df_ctjpa', 'mfm_TTtj_df_ctjsp', 'mfm_TTtj_df_ctjec', 'mfm_TTtj_p_snbd', 'mfm_TTtj_p_selg', 'mfm_TTtj_p_sapa', 'mfm_TTtj_p_sasp', 'mfm_TTtj_p_saec', 'mfm_TTtj_p_saep', 'mfm_TTtj_n_snbd', 'mfm_TTtj_n_selg', 'mfm_TTtj_n_sapa', 'mfm_TTtj_n_sasp', 'mfm_TTtj_n_saec', 'mfm_TTtj_n_saep', 'pcdm_QR_center', 'pcdm_QR_mt2', 'pcdm_QR_mctd', 'pcdm_QR_mcar', 'pcdm_QR_mcre9', 'pcdm_QR_areac', 'pcdm_QR_aread', 'pcdm_QR_wg', 'pcdm_QR_nums', 'pcdm_QR_pm', 'pcdm_QR_nm', 'pcdm_QR_r', 'pcdm_QR_v', 'pcdm_QR_mse', 'pcdm_QR_af', 'pcdm_QR_ad', 'pcdm_QR_v1', 'pcdm_QR_mse1', 'pcdm_QR_saf', 'pcdm_QR_sad', 'pcdm_QR_dpma', 'pcdm_QR_dpav', 'pcdm_QR_dpmse', 'pcdm_QR_dvma', 'pcdm_QR_dvav', 'pcdm_QR_dvmse', 'pcdm_QR_mdama', 'pcdm_QR_mdaav', 'pcdm_QR_mdamse', 'pcdm_QR_qca', 'pcdm_QR_qia', 'pcdm_QR_rca', 'pcdm_QR_ria', 'pcdm_QR_iama', 'pcdm_QR_iami', 'pcdm_QR_iaav', 'pcdm_QR_iamse', 'pcdm_QR_qra', 'pcdm_QR_q', 'pcdm_RS_center', 'pcdm_RS_mt2', 'pcdm_RS_mctd', 'pcdm_RS_mcar', 'pcdm_RS_mcre9', 'pcdm_RS_areac', 'pcdm_RS_aread', 'pcdm_RS_wg', 'pcdm_RS_nums', 'pcdm_RS_pm', 'pcdm_RS_nm', 'pcdm_RS_r', 'pcdm_RS_v', 'pcdm_RS_mse', 'pcdm_RS_af', 'pcdm_RS_ad', 'pcdm_RS_v1', 'pcdm_RS_mse1', 'pcdm_RS_saf', 'pcdm_RS_sad', 'pcdm_RS_dpma', 'pcdm_RS_dpav', 'pcdm_RS_dpmse', 'pcdm_RS_dvma', 'pcdm_RS_dvav', 'pcdm_RS_dvmse', 'pcdm_RS_mdama', 'pcdm_RS_mdaav', 'pcdm_RS_mdamse', 'pcdm_RS_rca', 'pcdm_RS_ria', 'pcdm_RS_iama', 'pcdm_RS_iami', 'pcdm_RS_iaav', 'pcdm_RS_iamse', 'pcdm_RS_qra', 'pcdm_RS_q', 'pcdm_TT_center', 'pcdm_TT_mt2', 'pcdm_TT_mctd', 'pcdm_TT_mcar', 'pcdm_TT_mcre9', 'pcdm_TT_areac', 'pcdm_TT_aread', 'pcdm_TT_wg', 'pcdm_TT_nums', 'pcdm_TT_pm', 'pcdm_TT_nm', 'pcdm_TT_r', 'pcdm_TT_v', 'pcdm_TT_mse', 'pcdm_TT_af', 'pcdm_TT_ad', 'pcdm_TT_v1', 'pcdm_TT_mse1', 'pcdm_TT_saf', 'pcdm_TT_sad', 'pcdm_TT_dpma', 'pcdm_TT_dpav', 'pcdm_TT_dpmse', 'pcdm_TT_dvma', 'pcdm_TT_dvav', 'pcdm_TT_dvmse', 'pcdm_TT_mdama', 'pcdm_TT_mdaav', 'pcdm_TT_mdamse', 'pcdm_TT_qca', 'pcdm_TT_qia', 'pcdm_TT_rca', 'pcdm_TT_ria', 'pcdm_TT_iama', 'pcdm_TT_iami', 'pcdm_TT_iaav', 'pcdm_TT_iamse', 'pcdm_TT_qra', 'pcdm_TT_q', 'space_rtsgnum', 'space_rtsgnum2', 'space_zeroR', 'space_zeroRTrot', 'space_zeroRTrot1', 'space_zeroT', 'space_zeroqrs', 'time_areaqj', 'time_qrsttrt', 'time_rtrt', 'time_rtrt3', 'time_stfs', 'time_strs', 'time_stsl', 'time_stsl1', 'time_tmdf', 'time_tmdf1', 'time_tnirt', 'time_tnirt1', 'time_tpnrt1', 'time_tpnrt2', 'time_tsm', 'time_tsm1', 'time_ttflt', 'time_ttflt1', 'time_ttstpn', 'time_ttstpp', 'time_ttstprt', 'time_htrt', 'timephase_QR', 'timephase_QRS', 'timephase_ST', 'timephase_STr', 'timephase_TT', 'timephase_QT', 'time_noq', 'amplitude_qrsmai', 'amplitude_qrsma', 'amplitude_qrs16maavi', 'amplitude_Rmai', 'amplitude_20a10i', 'timearea_qrsmai', 'timearea_ttmai', 'timearea_qrstt', 'timearea_qrsmai1', 'timearea_qrstt1', 'timearea_tmam', 'timearea_tmam1', 'timearea_16absav', 'timearea_16absav1', 'timearea_16absma', 'timearea_16absma1', 'timeitg_ttabsn', 'timeitg_ttsasn', 'timeitg_ttssn', 'timeitg_qrsabsn', 'timeitg_qrsttabsn', 'timeitg_qrsttdabsn', 'timeitg_qrsttsasn', 'timeitg_qrsttdsasn', 'timeitg_qrsttssn', 'timeitg_qrsttdssn', 'timeitg_qrsabsn1', 'timeitg_qrsttabsn1', 'timeitg_qrsttdabsn1', 'timeitg_qrsttsasn1', 'timeitg_qrsttdsasn1', 'timeitg_qrsttssn1', 'timeitg_qrsttdssn1', 'time_stph', 'time_twivls_0', 'time_twivls_1', 'time_twivls_2', 'time_twivls_3', 'time_twivls_3_1', 'time_twivls_4', 'time_stcgls_0', 'time_stcgls_1', 'time_stcgls_2', 'time_stcgls_3', 'time_stcgls_4', 'pcdm_dpmm0', 'pcdm_dpmm10', 'pcdm_dpmm20', 'pcdm_dpmm30', 'pcdm_dpmm40', 'pcdm_dpmm50', 'pcdm_dpmm60', 'pcdm_dpmm70', 'pcdm_dpmmth10', 'pcdm_dpmmth', 'mfm_ag_qrsa', 'mfm_ag_tta', 'mfm_ag_qrstta', 'mfm_ag_tta1', 'mfm_ag_qrsa1', 'mfm_ag_qrstta1', 'mfm_ag_qrsa2', 'mfm_ag_qrstta2', 'mfm_ag_qrsa3', 'mfm_ag_qrstta3', 'mfm_ag_rdz', 'mfm_ag_rdz1', 'mfm_ag_rdz2', 'mfm_ag_rdz3', 'mfm_ag_rdz4', 'space_qrswv_neg', 'space_qrswv_pos', 'space_qrswv_Qr', 'space_qrswv_rS', 'space_qrswv_qrsyc', 'space_twavedt_ur', 'space_twavedt_iv', 'space_twavedt_ttyc', 'amplitude_QRS', 'amplitude_RT', 'mfm_com_QRS_center', 'mfm_com_T_pos_trajectory', 'mfm_com_T_center', 'mfm_com_QRS_ave_angle', 'mfm_com_QR_rot_angle', 'mfm_com_mfm_QR_cwa']
    return l1


def mts451():
    '''原451个参数'''
    l1 = ['mfm_disp_Q', 'mfm_disp_R', 'mfm_disp_S', 'mfm_disp_T', 'mfm_disp_QRS', 'mfm_disp_TT', 'mfm_disp1_Q_p', 'mfm_disp1_Q_n', 'mfm_disp1_Q_pn', 'mfm_disp1_R_p', 'mfm_disp1_R_n', 'mfm_disp1_R_pn', 'mfm_disp1_S_p', 'mfm_disp1_S_n', 'mfm_disp1_S_pn', 'mfm_disp1_T_p', 'mfm_disp1_T_n', 'mfm_disp1_T_pn', 'mfm_QR_center', 'mfm_QR_mt2', 'mfm_QR_didma', 'mfm_QR_didav', 'mfm_QR_didmse', 'mfm_QR_dicd', 'mfm_QR_dicar', 'mfm_QR_dicre9', 'mfm_QR_wg', 'mfm_QR_nums', 'mfm_QR_pm', 'mfm_QR_nm', 'mfm_QR_r', 'mfm_QR_v', 'mfm_QR_mse', 'mfm_QR_af', 'mfm_QR_ad', 'mfm_QR_v1', 'mfm_QR_mse1', 'mfm_QR_saf', 'mfm_QR_sad', 'mfm_QR_dppma', 'mfm_QR_dppav', 'mfm_QR_dppmse', 'mfm_QR_dpnma', 'mfm_QR_dpnav', 'mfm_QR_dpnmse', 'mfm_QR_pnpma', 'mfm_QR_pnpflu', 'mfm_QR_pnnma', 'mfm_QR_pnnflu', 'mfm_QR_qa', 'mfm_QR_ra', 'mfm_QR_qra', 'mfm_QR_q', 'mfm_QR_areaf', 'mfm_QR_aread', 'mfm_QR_epma', 'mfm_QR_epav', 'mfm_QR_epmse', 'mfm_QR_enma', 'mfm_QR_enav', 'mfm_QR_enmse', 'mfm_RS_center', 'mfm_RS_mt2', 'mfm_RS_didma', 'mfm_RS_didav', 'mfm_RS_didmse', 'mfm_RS_dicd', 'mfm_RS_dicar', 'mfm_RS_dicre9', 'mfm_RS_wg', 'mfm_RS_nums', 'mfm_RS_pm', 'mfm_RS_nm', 'mfm_RS_r', 'mfm_RS_v', 'mfm_RS_mse', 'mfm_RS_af', 'mfm_RS_ad', 'mfm_RS_v1', 'mfm_RS_mse1', 'mfm_RS_saf', 'mfm_RS_sad', 'mfm_RS_dppma', 'mfm_RS_dppav', 'mfm_RS_dppmse', 'mfm_RS_dpnma', 'mfm_RS_dpnav', 'mfm_RS_dpnmse', 'mfm_RS_pnpma', 'mfm_RS_pnpflu', 'mfm_RS_pnnma', 'mfm_RS_pnnflu', 'mfm_RS_sa', 'mfm_RS_rsa', 'mfm_RS_q', 'mfm_RS_areaf', 'mfm_RS_aread', 'mfm_RS_epma', 'mfm_RS_epav', 'mfm_RS_epmse', 'mfm_RS_enma', 'mfm_RS_enav', 'mfm_RS_enmse', 'mfm_TT_center', 'mfm_TT_mt2', 'mfm_TT_didma', 'mfm_TT_didav', 'mfm_TT_didmse', 'mfm_TT_dicd', 'mfm_TT_dicar', 'mfm_TT_dicre9', 'mfm_TT_wg', 'mfm_TT_nums', 'mfm_TT_pm', 'mfm_TT_nm', 'mfm_TT_r', 'mfm_TT_v', 'mfm_TT_mse', 'mfm_TT_af', 'mfm_TT_ad', 'mfm_TT_v1', 'mfm_TT_mse1', 'mfm_TT_saf', 'mfm_TT_sad', 'mfm_TT_dppma', 'mfm_TT_dppav', 'mfm_TT_dppmse', 'mfm_TT_dpnma', 'mfm_TT_dpnav', 'mfm_TT_dpnmse', 'mfm_TT_pnpma', 'mfm_TT_pnpflu', 'mfm_TT_pnnma', 'mfm_TT_pnnflu', 'mfm_TT_ta', 'mfm_TT_tt50', 'mfm_TT_tt75', 'mfm_TT_tta', 'mfm_TT_q', 'mfm_TT_areaf', 'mfm_TT_aread', 'mfm_TT_epma', 'mfm_TT_epav', 'mfm_TT_epmse', 'mfm_TT_enma', 'mfm_TT_enav', 'mfm_TT_enmse', 'mfm_TTtj_p_melg', 'mfm_TTtj_p_mmlg', 'mfm_TTtj_p_mnbd', 'mfm_TTtj_p_atjpa', 'mfm_TTtj_p_atjsp', 'mfm_TTtj_p_atjec', 'mfm_TTtj_p_mtjpa', 'mfm_TTtj_p_mtjsp', 'mfm_TTtj_p_mtjec', 'mfm_TTtj_p_htjpa', 'mfm_TTtj_p_htjsp', 'mfm_TTtj_p_htjec', 'mfm_TTtj_p_rtjpa', 'mfm_TTtj_p_rtjsp', 'mfm_TTtj_p_rtjec', 'mfm_TTtj_n_melg', 'mfm_TTtj_n_mmlg', 'mfm_TTtj_n_mnbd', 'mfm_TTtj_n_atjpa', 'mfm_TTtj_n_atjsp', 'mfm_TTtj_n_atjec', 'mfm_TTtj_n_mtjpa', 'mfm_TTtj_n_mtjsp', 'mfm_TTtj_n_mtjec', 'mfm_TTtj_n_htjpa', 'mfm_TTtj_n_htjsp', 'mfm_TTtj_n_htjec', 'mfm_TTtj_n_rtjpa', 'mfm_TTtj_n_rtjsp', 'mfm_TTtj_n_rtjec', 'mfm_TTtj_br_melg0', 'mfm_TTtj_br_mndb0', 'mfm_TTtj_br_qra', 'mfm_TTtj_br_q', 'mfm_TTtj_br_areaf', 'mfm_TTtj_br_aread', 'mfm_TTtj_br_didma', 'mfm_TTtj_br_didmi', 'mfm_TTtj_br_didav', 'mfm_TTtj_br_didms', 'mfm_TTtj_br_ctjpa', 'mfm_TTtj_br_ctjsp', 'mfm_TTtj_br_ctjec', 'mfm_TTtj_sg_melg0', 'mfm_TTtj_sg_mndb0', 'mfm_TTtj_sg_qra', 'mfm_TTtj_sg_q', 'mfm_TTtj_sg_areaf', 'mfm_TTtj_sg_aread', 'mfm_TTtj_sg_didma', 'mfm_TTtj_sg_didmi', 'mfm_TTtj_sg_didav', 'mfm_TTtj_sg_didms', 'mfm_TTtj_sg_ctjpa', 'mfm_TTtj_sg_ctjsp', 'mfm_TTtj_sg_ctjec', 'mfm_TTtj_df_melg0', 'mfm_TTtj_df_mndb0', 'mfm_TTtj_df_qra', 'mfm_TTtj_df_q', 'mfm_TTtj_df_areaf', 'mfm_TTtj_df_aread', 'mfm_TTtj_df_didma', 'mfm_TTtj_df_didmi', 'mfm_TTtj_df_didav', 'mfm_TTtj_df_didms', 'mfm_TTtj_df_ctjpa', 'mfm_TTtj_df_ctjsp', 'mfm_TTtj_df_ctjec', 'mfm_TTtj_p_snbd', 'mfm_TTtj_p_selg', 'mfm_TTtj_p_sapa', 'mfm_TTtj_p_sasp', 'mfm_TTtj_p_saec', 'mfm_TTtj_p_saep', 'mfm_TTtj_n_snbd', 'mfm_TTtj_n_selg', 'mfm_TTtj_n_sapa', 'mfm_TTtj_n_sasp', 'mfm_TTtj_n_saec', 'mfm_TTtj_n_saep', 'pcdm_QR_center', 'pcdm_QR_mt2', 'pcdm_QR_mctd', 'pcdm_QR_mcar', 'pcdm_QR_mcre9', 'pcdm_QR_areac', 'pcdm_QR_aread', 'pcdm_QR_wg', 'pcdm_QR_nums', 'pcdm_QR_pm', 'pcdm_QR_nm', 'pcdm_QR_r', 'pcdm_QR_v', 'pcdm_QR_mse', 'pcdm_QR_af', 'pcdm_QR_ad', 'pcdm_QR_v1', 'pcdm_QR_mse1', 'pcdm_QR_saf', 'pcdm_QR_sad', 'pcdm_QR_dpma', 'pcdm_QR_dpav', 'pcdm_QR_dpmse', 'pcdm_QR_dvma', 'pcdm_QR_dvav', 'pcdm_QR_dvmse', 'pcdm_QR_mdama', 'pcdm_QR_mdaav', 'pcdm_QR_mdamse', 'pcdm_QR_qca', 'pcdm_QR_qia', 'pcdm_QR_rca', 'pcdm_QR_ria', 'pcdm_QR_iama', 'pcdm_QR_iami', 'pcdm_QR_iaav', 'pcdm_QR_iamse', 'pcdm_QR_qra', 'pcdm_QR_q', 'pcdm_RS_center', 'pcdm_RS_mt2', 'pcdm_RS_mctd', 'pcdm_RS_mcar', 'pcdm_RS_mcre9', 'pcdm_RS_areac', 'pcdm_RS_aread', 'pcdm_RS_wg', 'pcdm_RS_nums', 'pcdm_RS_pm', 'pcdm_RS_nm', 'pcdm_RS_r', 'pcdm_RS_v', 'pcdm_RS_mse', 'pcdm_RS_af', 'pcdm_RS_ad', 'pcdm_RS_v1', 'pcdm_RS_mse1', 'pcdm_RS_saf', 'pcdm_RS_sad', 'pcdm_RS_dpma', 'pcdm_RS_dpav', 'pcdm_RS_dpmse', 'pcdm_RS_dvma', 'pcdm_RS_dvav', 'pcdm_RS_dvmse', 'pcdm_RS_mdama', 'pcdm_RS_mdaav', 'pcdm_RS_mdamse', 'pcdm_RS_rca', 'pcdm_RS_ria', 'pcdm_RS_iama', 'pcdm_RS_iami', 'pcdm_RS_iaav', 'pcdm_RS_iamse', 'pcdm_RS_qra', 'pcdm_RS_q', 'pcdm_TT_center', 'pcdm_TT_mt2', 'pcdm_TT_mctd', 'pcdm_TT_mcar', 'pcdm_TT_mcre9', 'pcdm_TT_areac', 'pcdm_TT_aread', 'pcdm_TT_wg', 'pcdm_TT_nums', 'pcdm_TT_pm', 'pcdm_TT_nm', 'pcdm_TT_r', 'pcdm_TT_v', 'pcdm_TT_mse', 'pcdm_TT_af', 'pcdm_TT_ad', 'pcdm_TT_v1', 'pcdm_TT_mse1', 'pcdm_TT_saf', 'pcdm_TT_sad', 'pcdm_TT_dpma', 'pcdm_TT_dpav', 'pcdm_TT_dpmse', 'pcdm_TT_dvma', 'pcdm_TT_dvav', 'pcdm_TT_dvmse', 'pcdm_TT_mdama', 'pcdm_TT_mdaav', 'pcdm_TT_mdamse', 'pcdm_TT_qca', 'pcdm_TT_qia', 'pcdm_TT_rca', 'pcdm_TT_ria', 'pcdm_TT_iama', 'pcdm_TT_iami', 'pcdm_TT_iaav', 'pcdm_TT_iamse', 'pcdm_TT_qra', 'pcdm_TT_q', 'space_rtsgnum', 'space_rtsgnum2', 'space_zeroR', 'space_zeroRTrot', 'space_zeroRTrot1', 'space_zeroT', 'space_zeroqrs', 'time_areaqj', 'time_qrsttrt', 'time_rtrt', 'time_rtrt3', 'time_stfs', 'time_strs', 'time_stsl', 'time_stsl1', 'time_tmdf', 'time_tmdf1', 'time_tnirt', 'time_tnirt1', 'time_tpnrt1', 'time_tpnrt2', 'time_tsm', 'time_tsm1', 'time_ttflt', 'time_ttflt1', 'time_ttstpn', 'time_ttstpp', 'time_ttstprt', 'time_qsitv', 'time_htrt', 'timephase_QR', 'timephase_QRS', 'timephase_ST', 'timephase_STr', 'timephase_TT', 'timephase_QT', 'time_noq', 'amplitude_qrsmai', 'amplitude_qrsma', 'amplitude_qrs16maavi', 'amplitude_Rmai', 'amplitude_20a10i', 'timearea_qrsmai', 'timearea_ttmai', 'timearea_qrstt', 'timearea_qrsmai1', 'timearea_qrstt1', 'timearea_tmam', 'timearea_tmam1', 'timearea_16absav', 'timearea_16absav1', 'timearea_16absma', 'timearea_16absma1', 'timeitg_ttabsn', 'timeitg_ttsasn', 'timeitg_ttssn', 'timeitg_qrsabsn', 'timeitg_qrsttabsn', 'timeitg_qrsttdabsn', 'timeitg_qrsttsasn', 'timeitg_qrsttdsasn', 'timeitg_qrsttssn', 'timeitg_qrsttdssn', 'timeitg_qrsabsn1', 'timeitg_qrsttabsn1', 'timeitg_qrsttdabsn1', 'timeitg_qrsttsasn1', 'timeitg_qrsttdsasn1', 'timeitg_qrsttssn1', 'timeitg_qrsttdssn1', 'time_stph', 'time_twivls_0', 'time_twivls_1', 'time_twivls_2', 'time_twivls_3', 'time_twivls_3_1', 'time_twivls_4', 'time_stcgls_0', 'time_stcgls_1', 'time_stcgls_2', 'time_stcgls_3', 'time_stcgls_4', 'pcdm_dpmm', 'pcdm_dpmm0', 'pcdm_dpmm10', 'pcdm_dpmm20', 'pcdm_dpmm30', 'pcdm_dpmm40', 'pcdm_dpmm50', 'pcdm_dpmm60', 'pcdm_dpmm70', 'pcdm_dpmmth10', 'pcdm_dpmmth', 'mfm_ag_qrsa', 'mfm_ag_tta', 'mfm_ag_qrstta', 'mfm_ag_tta1', 'mfm_ag_qrsa1', 'mfm_ag_qrstta1', 'mfm_ag_qrsa2', 'mfm_ag_qrstta2', 'mfm_ag_qrsa3', 'mfm_ag_qrstta3', 'mfm_ag_rdz', 'mfm_ag_rdz1', 'mfm_ag_rdz2', 'mfm_ag_rdz3', 'mfm_ag_rdz4']
    return l1


def mts343():
    l1 = ['mfm_TTtj_n_snbd', 'mfm_RS_epmse', 'mfm_TTtj_n_melg', 'pcdm_RS_nm', 'pcdm_TT_mse1', 'pcdm_RS_mdaav', 'mfm_TTtj_p_mtjec', 'mfm_TTtj_p_htjsp', 'pcdm_QR_mcar', 'mfm_disp_TT', 'mfm_TTtj_sg_didma', 'mfm_TT_enmse', 'pcdm_TT_mcre9', 'mfm_QR_dpnmse', 'mfm_TTtj_n_atjpa', 'mfm_TTtj_n_rtjpa', 'mfm_RS_dpnmse', 'pcdm_QR_pm', 'pcdm_RS_iamse', 'mfm_TTtj_sg_ctjec', 'pcdm_RS_dpav', 'mfm_QR_pm', 'pcdm_QR_dpmse', 'mfm_RS_didma', 'mfm_TTtj_n_selg', 'pcdm_TT_saf', 'mfm_TTtj_df_aread', 'mfm_TT_dicd', 'mfm_TTtj_n_mtjpa', 'pcdm_QR_mdamse', 'pcdm_QR_iaav', 'pcdm_QR_wg', 'mfm_QR_saf', 'mfm_TTtj_sg_qra', 'mfm_QR_dicre9', 'mfm_QR_mse1', 'mfm_TTtj_n_htjec', 'pcdm_TT_sad', 'pcdm_RS_ria', 'mfm_RS_pnpflu', 'mfm_TTtj_p_rtjpa', 'pcdm_TT_q', 'mfm_QR_enav', 'mfm_TTtj_sg_mndb0', 'pcdm_TT_wg', 'mfm_TTtj_br_q', 'pcdm_RS_af', 'pcdm_QR_iami', 'mfm_RS_wg', 'mfm_QR_qa', 'mfm_RS_rsa', 'mfm_TTtj_sg_ctjsp', 'pcdm_TT_af', 'mfm_RS_v', 'mfm_TT_ad', 'mfm_TT_aread', 'mfm_TT_epma', 'mfm_QR_enmse', 'mfm_disp1_S_pn', 'mfm_QR_dppmse', 'mfm_TTtj_p_mmlg', 'mfm_TT_epmse', 'mfm_RS_dppma', 'mfm_RS_sad', 'mfm_TTtj_n_mtjsp', 'pcdm_QR_v', 'mfm_TT_wg', 'mfm_TT_center', 'pcdm_QR_sad', 'mfm_TTtj_n_htjsp', 'pcdm_RS_dpma', 'mfm_QR_nums', 'mfm_TTtj_n_saep', 'pcdm_QR_center', 'pcdm_RS_ad', 'pcdm_RS_aread', 'mfm_QR_sad', 'mfm_TTtj_sg_melg0', 'mfm_RS_areaf', 'mfm_RS_nums', 'mfm_QR_didav', 'mfm_TTtj_n_sasp', 'pcdm_TT_mdama', 'mfm_TTtj_df_melg0', 'pcdm_RS_mse', 'mfm_QR_dicar', 'mfm_TT_pnpma', 'mfm_TTtj_sg_ctjpa', 'mfm_TTtj_p_melg', 'mfm_TTtj_n_atjsp', 'pcdm_RS_iama', 'pcdm_TT_qia', 'pcdm_TT_rca', 'mfm_TT_mt2', 'pcdm_QR_ad', 'mfm_QR_v', 'pcdm_QR_dpma', 'mfm_TTtj_br_ctjec', 'mfm_QR_r', 'mfm_disp1_Q_pn', 'mfm_TT_mse', 'mfm_QR_dppav', 'mfm_TTtj_br_ctjpa', 'pcdm_TT_pm', 'mfm_TTtj_n_saec', 'mfm_QR_dppma', 'pcdm_TT_r', 'pcdm_QR_q', 'mfm_disp1_S_n', 'mfm_TTtj_br_didms', 'mfm_QR_mt2', 'pcdm_RS_mt2', 'pcdm_TT_iami', 'mfm_TTtj_br_melg0', 'pcdm_TT_mdamse', 'mfm_RS_sa', 'pcdm_RS_q', 'mfm_TTtj_p_mtjsp', 'mfm_TT_ta', 'pcdm_QR_iamse', 'mfm_TTtj_p_sapa', 'pcdm_QR_rca', 'mfm_disp1_R_n', 'pcdm_QR_iama', 'pcdm_TT_dpma', 'pcdm_RS_r', 'mfm_TTtj_df_qra', 'mfm_TTtj_df_areaf', 'pcdm_QR_v1', 'mfm_TTtj_br_qra', 'mfm_disp1_T_pn', 'mfm_QR_v1', 'pcdm_TT_mctd', 'pcdm_RS_mctd', 'mfm_QR_pnnma', 'pcdm_RS_mdamse', 'pcdm_TT_v1', 'mfm_RS_ad', 'mfm_RS_enma', 'mfm_RS_pm', 'pcdm_QR_af', 'mfm_RS_enav', 'mfm_RS_pnnma', 'pcdm_QR_mdama', 'pcdm_TT_dpav', 'mfm_QR_didmse', 'pcdm_QR_mdaav', 'mfm_TTtj_sg_didmi', 'mfm_TT_q', 'mfm_TT_dppmse', 'pcdm_RS_v1', 'mfm_QR_mse', 'pcdm_TT_mt2', 'pcdm_RS_sad', 'pcdm_RS_iaav', 'mfm_TTtj_p_atjsp', 'mfm_RS_af', 'mfm_TT_mse1', 'mfm_QR_pnpma', 'mfm_TT_nums', 'mfm_disp1_S_p', 'mfm_TTtj_sg_areaf', 'mfm_disp1_R_p', 'mfm_RS_saf', 'pcdm_QR_nm', 'mfm_TTtj_p_mtjpa', 'mfm_disp1_T_n', 'mfm_TTtj_p_saec', 'mfm_TTtj_df_didmi', 'mfm_QR_dicd', 'mfm_TT_pnpflu', 'mfm_TTtj_df_didms', 'mfm_QR_pnnflu', 'pcdm_TT_iamse', 'pcdm_RS_dvav', 'mfm_TT_sad', 'mfm_TTtj_df_didma', 'mfm_RS_dpnma', 'pcdm_TT_iama', 'mfm_TT_tt75', 'mfm_TT_dpnav', 'pcdm_TT_qca', 'mfm_TT_v', 'mfm_TTtj_sg_aread', 'pcdm_QR_qia', 'mfm_disp1_Q_p', 'pcdm_RS_saf', 'pcdm_TT_v', 'mfm_TTtj_n_mnbd', 'pcdm_RS_nums', 'mfm_TT_tt50', 'mfm_RS_mt2', 'mfm_TT_af', 'pcdm_TT_areac', 'mfm_QR_dpnma', 'pcdm_TT_mse', 'pcdm_QR_qra', 'mfm_disp_S', 'mfm_TTtj_df_ctjsp', 'pcdm_QR_qca', 'mfm_TTtj_br_mndb0', 'pcdm_TT_dvmse', 'mfm_RS_v1', 'mfm_RS_center', 'mfm_TTtj_p_mnbd', 'pcdm_QR_mse1', 'mfm_RS_aread', 'mfm_disp_T', 'mfm_TT_didmse', 'mfm_TTtj_df_mndb0', 'mfm_TTtj_n_htjpa', 'pcdm_TT_nums', 'mfm_TTtj_br_ctjsp', 'pcdm_QR_dpav', 'pcdm_RS_wg', 'mfm_QR_center', 'mfm_QR_epav', 'mfm_TTtj_n_atjec', 'mfm_TT_areaf', 'pcdm_QR_ria', 'mfm_TTtj_p_rtjec', 'pcdm_TT_ria', 'mfm_TTtj_p_htjec', 'mfm_TT_dpnma', 'mfm_disp_QRS', 'mfm_RS_nm', 'pcdm_QR_dvmse', 'pcdm_QR_saf', 'pcdm_RS_rca', 'mfm_TT_didav', 'mfm_disp1_T_p', 'mfm_TTtj_br_areaf', 'pcdm_TT_dpmse', 'mfm_RS_dicd', 'pcdm_QR_aread', 'mfm_TT_tta', 'mfm_TTtj_br_didav', 'mfm_RS_epma', 'mfm_RS_q', 'mfm_RS_pnpma', 'mfm_TT_pnnflu', 'mfm_TTtj_p_selg', 'mfm_RS_mse1', 'mfm_TTtj_p_snbd', 'mfm_TT_pm', 'pcdm_RS_mcar', 'mfm_QR_nm', 'pcdm_QR_areac', 'mfm_TTtj_br_didmi', 'mfm_TT_nm', 'mfm_QR_dpnav', 'pcdm_QR_nums', 'mfm_RS_mse', 'mfm_disp1_Q_n', 'pcdm_QR_mse', 'mfm_QR_didma', 'pcdm_RS_mse1', 'pcdm_RS_v', 'mfm_QR_areaf', 'mfm_TTtj_br_didma', 'mfm_TT_dpnmse', 'pcdm_RS_qra', 'mfm_TT_dppma', 'mfm_RS_dpnav', 'mfm_QR_pnpflu', 'mfm_TT_saf', 'mfm_TT_v1', 'pcdm_TT_iaav', 'mfm_TTtj_p_htjpa', 'mfm_TT_dppav', 'mfm_QR_epma', 'pcdm_TT_nm', 'mfm_TT_r', 'mfm_QR_wg', 'mfm_TTtj_sg_didav', 'pcdm_RS_mdama', 'mfm_TT_dicre9', 'pcdm_QR_mctd', 'mfm_TTtj_p_sasp', 'mfm_RS_epav', 'mfm_TT_didma', 'mfm_TTtj_n_sapa', 'mfm_TTtj_df_didav', 'mfm_RS_didmse', 'mfm_TT_enma', 'mfm_RS_enmse', 'pcdm_RS_dvma', 'mfm_QR_enma', 'mfm_TTtj_br_aread', 'pcdm_TT_dvma', 'pcdm_QR_r', 'mfm_TTtj_n_mtjec', 'mfm_disp_Q', 'pcdm_RS_areac', 'mfm_RS_dppav', 'mfm_RS_dppmse', 'mfm_RS_dicre9', 'mfm_TT_epav', 'pcdm_TT_dvav', 'pcdm_RS_dvmse', 'mfm_QR_qra', 'mfm_RS_r', 'pcdm_TT_center', 'mfm_TTtj_n_mmlg', 'mfm_QR_ra', 'pcdm_TT_mdaav', 'mfm_TT_enav', 'pcdm_RS_pm', 'mfm_TTtj_df_ctjec', 'mfm_TTtj_p_rtjsp', 'pcdm_TT_mcar', 'mfm_disp_R', 'pcdm_RS_iami', 'pcdm_QR_mt2', 'pcdm_QR_dvav', 'pcdm_TT_aread', 'pcdm_RS_center', 'mfm_TTtj_p_saep', 'mfm_QR_aread', 'pcdm_QR_mcre9', 'mfm_disp1_R_pn', 'mfm_RS_pnnflu', 'pcdm_RS_dpmse', 'pcdm_TT_ad', 'mfm_QR_ad', 'mfm_QR_af', 'mfm_RS_dicar', 'mfm_QR_q', 'pcdm_QR_dvma', 'mfm_TT_pnnma', 'mfm_RS_didav', 'mfm_TTtj_p_atjpa', 'mfm_TT_dicar', 'mfm_TTtj_n_rtjec', 'mfm_TTtj_sg_didms', 'mfm_TTtj_n_rtjsp', 'mfm_TTtj_sg_q', 'mfm_QR_epmse', 'mfm_TTtj_df_q', 'pcdm_TT_qra', 'mfm_TTtj_p_atjec', 'pcdm_RS_mcre9', 'mfm_TTtj_df_ctjpa']
    return l1


def write_xlsx0(d0, l1, ids, f0):
    '''参数写入xlsx'''
    l2 = [['ID']+l1]
    l = [txt_to_list('%s/%s.txt' % (d0, l1[i])) for i in range(len(l1))]
    for i in range(len(ids)):
        l2.append([ids[i]]+[l[j][i] for j in range(len(l1))])
    from openpyxl import Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = 'Sheet1'
    for row in l2:
        ws.append(row)
    wb.save(f0)
    return f0


def list_to_xlsx(l2, f0):
    '''列表写入表格: l2[0]=标签列表, l2[i]=内容列表'''
    from openpyxl import Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = 'Sheet1'
    for row in l2:
        ws.append(row)
    wb.save(f0)


def get_metr1(d0, ids, d1, d2):
    '''计算mfm_pcdm_time_space指标&保存: d0数据集, d1指标, d2信息, ids样本名'''
    Zqr = extractmt('%s/ischemic_mfm_Z/rkdb_qr22.txt' % d2)
    Zrs = extractmt('%s/ischemic_mfm_Z/rkdb_rs22.txt' % d2)
    Ztt = extractmt('%s/ischemic_mfm_Z/rkdb_tt22.txt' % d2)
    a1 = [['mfm_disp.txt'], ['mfm_QR1.txt', 'mfm_QR2.txt', 'mfm_QR3.txt', 'mfm_QR4.txt', 'mfm_QR5.txt', 'mfm_QR6.txt'], ['mfm_RS1.txt', 'mfm_RS2.txt', 'mfm_RS3.txt', 'mfm_RS4.txt', 'mfm_RS5.txt', 'mfm_RS6.txt'], ['mfm_TT1.txt', 'mfm_TT2.txt', 'mfm_TT3.txt', 'mfm_TT4.txt', 'mfm_TT5.txt', 'mfm_TT6.txt']]
    mfmls = [[['Q', 'R', 'S', 'T', 'QRS', 'TT', 'noqrs']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['didma', 'didav', 'didmse', 'dicd', 'dicar', 'dicre9'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dppma', 'dppav', 'dppmse', 'dpnma', 'dpnav', 'dpnmse', 'pnpma', 'pnpflu', 'pnnma', 'pnnflu'], ['qa', 'ra', 'nq1', 'nq2', 'nq3', 'nq4', 'qra', 'q', 'm1', 'm2', 'a1', 'a2', 'areaf', 'aread'], ['epma', 'epav', 'epmse', 'enma', 'enav', 'enmse']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['didma', 'didav', 'didmse', 'dicd', 'dicar', 'dicre9'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dppma', 'dppav', 'dppmse', 'dpnma', 'dpnav', 'dpnmse', 'pnpma', 'pnpflu', 'pnnma', 'pnnflu'], ['ra', 'sa', 'nq1', 'nq2', 'nq3', 'nq4', 'rsa', 'q', 'm1', 'm2', 'a1', 'a2', 'areaf', 'aread'], ['epma', 'epav', 'epmse', 'enma', 'enav', 'enmse']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['didma', 'didav', 'didmse', 'dicd', 'dicar', 'dicre9'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dppma', 'dppav', 'dppmse', 'dpnma', 'dpnav', 'dpnmse', 'pnpma', 'pnpflu', 'pnnma', 'pnnflu'], ['ta', 'tt50', 'tt75', 'nq1', 'nq2', 'nq3', 'nq4', 'tta', 'q', 'm1', 'm2', 'a1', 'a2', 'areaf', 'aread'], ['epma', 'epav', 'epmse', 'enma', 'enav', 'enmse']]]
    a2 = [['pcdm_QR1.txt', 'pcdm_QR2.txt', 'pcdm_QR3.txt', 'pcdm_QR4.txt', 'pcdm_QR5.txt'], ['pcdm_RS1.txt', 'pcdm_RS2.txt', 'pcdm_RS3.txt', 'pcdm_RS4.txt', 'pcdm_RS5.txt'], ['pcdm_TT1.txt', 'pcdm_TT2.txt', 'pcdm_TT3.txt', 'pcdm_TT4.txt', 'pcdm_TT5.txt']]
    pcdmls = [[['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['mctd', 'mcar', 'mcre9', 'areac', 'aread'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dpma', 'dpav', 'dpmse', 'dvma', 'dvav', 'dvmse', 'mdama', 'mdaav', 'mdamse'], ['qca', 'qia', 'rca', 'ria', 'iama', 'iami', 'iaav', 'iamse', 'nq1', 'nq2', 'nq3', 'nq4', 'qra', 'q', 'm1', 'm2', 'a1', 'a2']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['mctd', 'mcar', 'mcre9', 'areac', 'aread'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dpma', 'dpav', 'dpmse', 'dvma', 'dvav', 'dvmse', 'mdama', 'mdaav', 'mdamse'], ['qca', 'qia', 'rca', 'ria', 'iama', 'iami', 'iaav', 'iamse', 'nq1', 'nq2', 'nq3', 'nq4', 'RSa', 'q', 'm1', 'm2', 'a1', 'a2']], [['qs', 'n0', 'n1', 'n2', 'n3', 'n4', 'center', 'mt2'], ['mctd', 'mcar', 'mcre9', 'areac', 'aread'], ['wg', 'nums', 'pm', 'nm', 'r', 'v', 'mse', 'jnq1', 'jnq2', 'jnq3', 'jnq4', 'jvq1', 'jvq2', 'jvq3', 'jvq4', 'af', 'ad', 'v1', 'mse1', 'saf', 'sad'], ['dpma', 'dpav', 'dpmse', 'dvma', 'dvav', 'dvmse', 'mdama', 'mdaav', 'mdamse'], ['qca', 'qia', 'rca', 'ria', 'iama', 'iami', 'iaav', 'iamse', 'nq1', 'nq2', 'nq3', 'nq4', 'TTa', 'q', 'm1', 'm2', 'a1', 'a2']]]
    dfs = pcdm_default()
    Zqr1 = extractmt('%s/ischemic_pcdm_Z/rkdb_qr31.txt' % d2)
    Zrs1 = extractmt('%s/ischemic_pcdm_Z/rkdb_rs31.txt' % d2)
    Ztt1 = extractmt('%s/ischemic_pcdm_Z/rkdb_tt31.txt' % d2)
    a3, a4 = 'time.txt', 'space.txt'
    tmls = ['areaqj', 'qrsttrt', 'rtrt', 'rtrt3', 'stfs', 'strs', 'stsl', 'stsl1', 'tmdf', 'tmdf1', 'tnirt', 'tnirt1', 'tpnrt1', 'tpnrt2', 'tsm', 'tsm1', 'ttflt', 'ttflt1', 'ttstpn', 'ttstpp', 'ttstprt']
    spls = ['rtsgnum', 'rtsgnum2', 'zeroR', 'zeroRTrot', 'zeroRTrot1', 'zeroT', 'zeroqrs']
    tmrd = [3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
    sprd = [0, 0, 0, 3, 3, 0, 0]
    l0, l1, l2, l3 = [], [], [], []
    for i in range(len(ids)):
        mfm_file = '%s/%s.txt' % (d0, ids[i])
        # mfm&pcdm
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file)
        # print([type(matrixes[i]) for i in range(len(matrixes))])
        mf = get_mfs(get_lines(mfm_file))
        timelis0 = change_QS(timelis0, matrixes, mf)
        l0.append(get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt))
        l1.append(get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1))
        # time&space
        data, time_dic, rms = Data(mfm_file).get()
        tmmts = TimeWave(data, rms, time_dic).get_indicators()
        l2.append(get_round([tmmts[nm] for nm in tmls], tmrd))
        spmts = SpaceWave(data, time_dic).get_indicators()
        l3.append(get_round([spmts[nm] for nm in spls], sprd))
    d00 = write_lis1(l0, mfmls, d1, a1, ids)
    d00 = write_lis1(l1, pcdmls, d1, a2, ids)
    d00 = write_lis2(l2, tmls, d1, a3, ids)
    d00 = write_lis2(l3, spls, d1, a4, ids)
    return d1


def def_mts_intp_pre(tardir):
    '''插值前指标的文件列表和标题字符串列表'''
    mflis, mslis = [[], [], [], [], []], [[], [], [], [], []]
    mflis[0].append('%s/dispersion_metrics.txt' % tardir)
    mflis[0].append('%s/dispersion_ranks.txt' % tardir)
    mslis[0].extend(['id, Q, R, S, T, QRS, TT, noq, nor, nos', 'id, Q, R, S, T, QRS, TT'])
    txs1 = ['QR_', 'RS_', 'QRS_', 'TT_']
    txs2 = ['centerdistrib', 'dipole', 'jumprot', 'mulripole', 'pointto', 'poleform']
    titles2 = ['id, qs, n0, n1, n2, n3, na', 'id, didma, didav, didmse, dicd, dicar, dicre9', 'id, wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad', 'id, dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu', '', 'id, epma, epav, epmse, enma, enav, enmse']
    for i2 in range(len(txs1)):
        for j2 in range(len(txs2)):
            mflis[i2+1].append('%s/%s%s.txt' % (tardir, txs1[i2], txs2[j2]))
            if j2 != 4:
                mslis[i2+1].append(titles2[j2])
    mslis[1].insert(-1, 'id, qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread')
    mslis[2].insert(-1, 'id, ra, sa, nq1, nq2, nq3, nq4, rsa, q, m1, m2, a1, a2, areaf, aread')
    mslis[3].insert(-1, 'id, qa, sa, nq1, nq2, nq3, nq4, qsa, q, m1, m2, a1, a2, areaf, aread')
    mslis[4].insert(-1, 'id, ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread')
    return mflis, mslis


def def_mts_mfm(tardir):
    '''插值后指标的文件列表和标题字符串列表'''
    subfolds = ['ischemic_mfm_disp', 'ischemic_mfm_QR', 'ischemic_mfm_RS', 'ischemic_mfm_TT']
    new_folder(['%s/%s' % (tardir, subfolds[i2]) for i2 in range(len(subfolds))])
    mflis, mslis = [[], [], [], []], [[], [], [], []]
    mflis[0].append('%s/%s/metrics.txt' % (tardir, subfolds[0]))
    mslis[0].append('id, Q, R, S, T, QRS, TT, noqrs')
    txs2 = ['metrics_ctdistrib', 'metrics_dipole', 'metrics_jumprot', 'metrics_mulripole', 'metrics_pointto', 'metrics_poleform']
    titles2 = ['id, qs, n0, n1, n2, n3, n4, center, mt2', 'id, didma, didav, didmse, dicd, dicar, dicre9', 'id, wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad', 'id, dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu', '', 'id, epma, epav, epmse, enma, enav, enmse']
    for i2 in range(1, len(subfolds)):
        for j2 in range(len(txs2)):
            mflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs2[j2]))
            if j2 != 4:
                mslis[i2].append(titles2[j2])
    mslis[1].insert(-1, 'id, qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread')
    mslis[2].insert(-1, 'id, ra, sa, nq1, nq2, nq3, nq4, rsa, q, m1, m2, a1, a2, areaf, aread')
    mslis[3].insert(-1, 'id, ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread')
    dflis, dslis = [[], [], [], []], [[], [], [], []]
    dflis[0].append('%s/%s/percents.txt' % (tardir, subfolds[0]))
    dflis[0].append('%s/%s/diagnosis_all.txt' % (tardir, subfolds[0]))
    dslis[0].extend(['id, R, T, QRS, TT, noqrs', 'id, percent'])
    txs1 = ['diagnosis_center', 'diagnosis_better', 'diagnosis_available', 'diagnosis_auxiliary', 'diagnosis_subdivision']
    txs2 = [['id, center', 'id, enmse', 'id, qa, ra, qra, qrdmse, qrdma, dpnav, dpnmse, dppav, pnnflu, dppmse, pnnma, enma, nm', 'id, areaf, aread, qrcar, qrcd, qrdav, dpnma, dppma, epmse, epma, epav, wg, pm, saf, af, mse', 'id, subdivision'], ['id, center', 'id, ', 'id, ra, sa, rsa, rsdma, rsdav', 'id, aread, rscar, rscd, dpnma, dppav, epmse, epav, pm, v1, mse', 'id, subdivision'], ['id, center', 'id, ta, tt75, tta, areaf, aread, ttcd, v, ad, af, v1, mse', 'id, tt50, ttdmse, ttdma, dppav, dppmse, dppma, enmse, enma, epmse, epma, wg, nums, pm, nm, saf, sad, r, mse1', 'id, ttcar, dpnav, dpnma, pnpflu, pnnflu, enav, epav', 'id, subdivision']]
    for i2 in range(1, len(subfolds)):
        for j2 in range(len(txs1)):
            dflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs1[j2]))
            dslis[i2].append(txs2[i2-1][j2])
        dflis[i2].append('%s/%s/combinediagnosis.txt' % (tardir, subfolds[i2]))
        dslis[i2].append('id, ct, av, au, al')
    return mflis, mslis, dflis, dslis


def def_mts_pcdm(tardir):
    '''电流密度图指标的文件列表和标题字符串列表'''
    subfolds = ['ischemic_pcdm_QR', 'ischemic_pcdm_RS', 'ischemic_pcdm_TT']
    new_folder(['%s/%s' % (tardir, subfolds[i2]) for i2 in range(len(subfolds))])
    mflis, mslis = [[], [], []], [[], [], []]
    txs2 = ['metrics_ctdistrib', 'metrics_posi', 'metrics_jumprot', 'metrics_red', 'metrics_angle']
    titles2 = ['id, qs, n0, n1, n2, n3, n4, center, mt2', 'id, mctd, mcar, mcre9, areac, aread', 'id, wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad', 'id, dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse', 'id, qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2']
    for i2 in range(len(subfolds)):
        for j2 in range(len(txs2)):
            mflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs2[j2]))
            mslis[i2].append(titles2[j2])
    return mflis, mslis


def get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt):
    '''
    插值后指标: disp, QR, RS, TT
    timelis: 时刻点
    mf: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    mlfile = writedisp(matrixes, timelis0, mcglines)[0]
    mlfile = mlfile[:-3] + [cal_noqrs(mlfile[-3:])]
    mtsall = [[mlfile]]
    frfile = cal_frrt(matrixes, q_peak, r_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zqr, nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, r_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zrs, nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, t_onset, t_end)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Ztt, t_idx=t_peak-t_onset, mode='tt', nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    return mtsall


def get_mfm_mts_intp_pre(timelis0, matrixes, mcglines, Zqr, Zrs, Zqrs, Ztt):
    '''
    插值前指标: disp, QR, RS, QRS, TT
    timelis: 时刻点
    mflis: 最大幅值
    ctrdfile: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/QRS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    mlfile, rksfile = writedisp(matrixes, timelis0, mcglines)
    mtsall = [[mlfile, rksfile]]
    frfile = cal_frrt(matrixes, q_peak, r_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zqr)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, r_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zrs)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, q_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Zqrs)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile = cal_frrt(matrixes, t_onset, t_end)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, Ztt, t_idx=t_peak-t_onset, mode='tt')
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    return mtsall


def get_pcdm_mts(timelis0, matrixes, dfs, Zqr, Zrs, Ztt):
    '''
    等磁图和电流密度图指标: QR, RS, TT
    timelis: 时刻点
    mf: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    frfile = cal_fr(matrixes, dfs, q_peak, r_peak)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Zqr, nsik=5)
    mtsall = [[cdfile, mpfile, mjfile, mrfile, mafile]]
    frfile = cal_fr(matrixes, dfs, r_peak, s_peak)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Zrs, nsik=5)
    mtsall.append([cdfile, mpfile, mjfile, mrfile, mafile])
    frfile = cal_fr(matrixes, dfs, t_onset, t_end)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Ztt, nsik=5)
    mtsall.append([cdfile, mpfile, mjfile, mrfile, mafile])
    return mtsall


def write_lis1(l0, nms, d0, f1, ids):
    new_folder(d0)
    for i in range(len(f1)):
        for j in range(len(f1[i])):
            f2 = '%s/%s' % (d0, f1[i][j])
            strs = 'id, %s' % ', '.join(nms[i][j])
            for k in range(len(l0)):
                strs += '\n%s, %s' % (ids[k], ', '.join([str(l0[k][i][j][j1]) for j1 in range(len(l0[k][i][j]))]))
            write_str(strs, f2)
    return d0


def write_lis2(l0, nms, d0, f1, ids):
    new_folder(d0)
    f2 = '%s/%s' % (d0, f1)
    strs = 'id, %s' % ', '.join(nms)
    for i in range(len(l0)):
        strs += '\n%s, %s' % (ids[i], ', '.join([str(l0[i][j]) for j in range(len(l0[i]))]))
    write_str(strs, f2)
    return d0


def get_idstms(biao1, biaodan, mode='idtm'):
    '''
    提取ids和时刻点(广东南海), 1+6(有标题)
    mode=id时只取ids
    '''
    data = pd.read_excel(biao1, sheet_name=biaodan).values
    data_raw = data[:, :7]
    ids, timelis = [], []
    for i in range(data_raw.shape[0]):
        ids.append(str(data_raw[i, 0]))
        if mode == 'idtm':
            tms = []
            for j in range(1, 7):
                tms.append(int(str(data_raw[i, j])))
            timelis.append(tms)
    if mode == 'id':
        return ids
    else:
        return ids, timelis


def gen_isdir_list(dir_name):
    files = os.listdir(dir_name)
    isdir_list = []
    for f in files:
        if os.path.isdir(dir_name + '/' + f):
            isdir_list.append(True)
        else:
            isdir_list.append(False)
    return isdir_list


def get_lines(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    return lines


def get_mfm_mts_QR(timelis, matrixes_list, mflis, Zqr):
    '''
    等磁图指标QR
    timelis: 时刻点
    mflis: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics QR...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        q_peak, r_peak = timelis[i1][0], timelis[i1][1]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        pnptm2 = cal_pnptm(matrixes, mflis0, range(q_peak-5, r_peak+1))
        q_peak = change_QR(pnptm2, q_peak)  # Q异常修正
        timelis[i1][0] = q_peak
        frfile, rtfile = cal_frrt(matrixes, q_peak, r_peak)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zqr)
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_RS(timelis, matrixes_list, mflis, Zrs):
    '''
    等磁图指标RS
    timelis: 时刻点
    mflis: 最大幅值
    Zrs: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics RS...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        r_peak, s_peak = timelis[i1][1], timelis[i1][2]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        pnptm2 = cal_pnptm(matrixes, mflis0, range(r_peak, s_peak+6))
        s_peak = change_RS(pnptm2, s_peak)  # S异常修正
        timelis[i1][2] = s_peak
        frfile, rtfile = cal_frrt(matrixes, r_peak, s_peak)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zrs)
        print(cdfile)
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_TT(timelis, matrixes_list, mflis, Ztt):
    '''
    等磁图指标TT
    timelis: 时刻点
    mflis: 最大幅值
    Ztt: 中心轨迹等级分布图谱
    输出指标列表:
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    print('metrics TT...')
    mpfiles, mjfiles, mdfiles, mefiles, mffiles,cdfiles = [], [], [], [], [], []
    for i1 in range(len(matrixes_list)):
        t_onset, t_peak, t_end = timelis[i1][3], timelis[i1][4], timelis[i1][5]
        matrixes, mflis0 = matrixes_list[i1], mflis[i1]
        frfile, rtfile = cal_frrt(matrixes, t_onset, t_end)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt)
        mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt, t_idx=t_peak-t_onset, mode='tt')
        mpfiles.append(mpfile)
        mjfiles.append(mjfile)
        mdfiles.append(mdfile)
        mefiles.append(mefile)
        mffiles.append(mffile)
        cdfiles.append(cdfile)
    return timelis, mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfiles


def get_mfm_mts_TT0(timelis, matrixes_list, mcglines_list):
    '''
    等磁图指标TT-初版
    timelis: 时刻点
    mflis: 最大幅值
    输出指标列表:
        area, rotation, quadrant, m1 m2 a1 a2, TT50 TT75 Tangle
    '''
    mpfiles = []
    for i1 in range(len(matrixes_list)):
        t_onset, t_peak, t_end = timelis[i1][3], timelis[i1][4], timelis[i1][5]
        matrixes, mcglines = matrixes_list[i1], mcglines_list[i1]
        mpfile = writemetric_tt0(matrixes, mcglines, t_onset, t_peak, t_end)
        mpfiles.append(mpfile)
    return mpfiles


def get_mfs(mlines, md=0):
    mf = []
    mf1 = []
    for ik in range(len(mlines)):
        mcgdata = mcg_float(mlines, ik)
        mf.append(max(mcgdata.max(), -1 * mcgdata.min()))
        mf1.append([mcgdata.max(), mcgdata.min()])
    if md == 0:
        return mf
    else:
        return mf1


def get_offxy_mat(mfm_fine, dfs):
    '''获取初始信息: 最大强度、方向、坐标、矩阵'''
    diff_xx, diff_yy = dfs
    diff_mfm_x = np.diff(mfm_fine, axis=1)
    diff_mfm_x = np.hstack((diff_mfm_x, diff_mfm_x[:, -1].reshape(-1, 1)))
    diff_mfm_y = np.diff(mfm_fine, axis=0)
    diff_mfm_y = np.vstack((diff_mfm_y, diff_mfm_y[-1, :]))
    current_x = diff_mfm_y / diff_yy
    current_y = -diff_mfm_x / diff_xx
    mat_magni = np.sqrt(current_x**2 + current_y**2)
    v = np.max(mat_magni) * 0.232515 / 1.162577  # 等比例匹配NI的强度值
    px, py = np.unravel_index(np.argmax(mat_magni), mat_magni.shape)
    mat_angle = np.arctan2(current_y, current_x) * (180 / np.pi)
    max_magni = np.max(mat_magni)
    mat_magni1 = mat_magni / max_magni
    offset_x = mat_magni1 * np.cos(np.deg2rad(mat_angle))
    offset_y = mat_magni1 * np.sin(np.deg2rad(mat_angle))
    dx = offset_x[px, py]
    dy = offset_y[px, py]
    ca = calangle(dx, -dy)  # 电流角度
    offset_x = offset_x[::8, ::8]
    offset_y = offset_y[::8, ::8]
    mat_magni = mat_magni[::8, ::8]
    return v, ca, px, py, offset_x, offset_y, mat_magni


def get_pts(matrixes, q_peak, r_peak):
    '''从矩阵计算点集'''
    cjpts = []
    for j in range(q_peak, r_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        px, py, nx, ny = get_dipole(matrix)
        cx = (px + nx) / 2  # 中心点坐标和坐标
        cy = (py + ny) / 2
        cors = get_dipole_ct(cx, cy)
        for cor in cors:
            if cor not in cjpts:
                cjpts.append(cor)
    return cjpts


def get_pts1(matrixes, dfs, q_peak, r_peak):
    '''从矩阵计算点集-主电流位置'''
    cjpts = []
    for j in range(q_peak, r_peak+1):
        matrix = matrixes[j-1]  # 扩张心磁
        v, ca, cx, cy, offset_x, offset_y, mat_magni = get_offxy_mat(matrix, dfs)
        if [cx, cy] not in cjpts:
            cjpts.append([cx, cy])
    return cjpts


def get_qrs_t12(mcglines, locp, nl, vf):
    '''Q/R/S有效波段获取'''
    mcgdata = mcg_float(mcglines, locp)
    max_t = max(mcgdata.ravel()) * vf  # TT的1/3数值，有效波段——
    max_t2 = -1 * min(mcgdata.ravel()) * vf
    if max_t >= max_t2:
        t1, t2 = get_qrs_valid_p(mcglines, locp, nl, max_t)
    else:
        t1, t2 = get_qrs_valid_n(mcglines, locp, nl, -1*max_t2)
    return t1, t2


def get_qrs_valid_n(mcglines, locp, nl, max_t):
    '''QRS有效波段, 以负向波判断'''
    t1, t2 = locp, locp + 1
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp-jj)
        if min(mcgdata.ravel()) > max_t:
            t1 = locp - jj  # 有效波段起点
            break
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp+jj+1)
        if min(mcgdata.ravel()) > max_t:
            t2 = locp+jj+1  # 有效波段终点
            break
    return t1, t2


def get_qrs_valid_p(mcglines, locp, nl, max_t):
    '''QRS有效波段, 以正向波判断'''
    t1, t2 = locp, locp + 1
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp-jj)
        if max(mcgdata.ravel()) < max_t:
            t1 = locp - jj  # 有效波段起点
            break
    for jj in range(nl):
        mcgdata = mcg_float(mcglines, locp+jj+1)
        if max(mcgdata.ravel()) < max_t:
            t2 = locp+jj+1  # 有效波段终点
            break
    return t1, t2


def get_round(lis0, ik=3):
    '''精确到ik位小数, ik为list时按照列表'''
    if type(lis0) == list:
        return [get_round(lis0[i3], ik[i3]) for i3 in range(len(ik))]
    else:  # 单个
        if ik == 0:
            return round(lis0)
        else:
            return round(lis0, ik)


def get_stabmgs(tim, lis0, mode='QR'):
    '''获取稳定性信息: 幅值, 正负离散度, 转角'''
    dpp0, dpps, dpns, pntts = [], [], [], []
    if mode == 'QR':
        for j1 in range(tim):
            dpp0.append(lis0[j1][2])
            dpps.append(abs(lis0[j1][2] - lis0[j1+1][2]))
            dpns.append(abs(lis0[j1][3] - lis0[j1+1][3]))
            pntts.append(abs(calrot(lis0[j1][4], lis0[j1+1][4])))
    else:  # 'RS'
        for j2 in range(1, tim):
            j1 = tim - j2
            dpp0.append(lis0[j1][2])
            dpps.append(abs(lis0[j1][2] - lis0[j1-1][2]))
            dpns.append(abs(lis0[j1][3] - lis0[j1-1][3]))
            pntts.append(abs(calrot(lis0[j1][4], lis0[j1-1][4])))
    return dpp0, dpps, dpns, pntts


def get_tt12(mcglines, t_onset, t_peak, t_end, mode='p'):
    '''TT有效波段获取, mode=pn时以正负向波较大值判断'''
    mcgdata = mcg_float(mcglines, t_peak)
    max_t = max(mcgdata.ravel()) / 3  # TT的1/3数值，有效波段——
    if mode == 'p':
        tt1, tt2 = get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t)
    else:  # mode='pn'
        max_t2 = -1 * min(mcgdata.ravel()) / 3
        if max_t >= max_t2:
            tt1, tt2 = get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t)
        else:
            tt1, tt2 = get_tt_valid_n(mcglines, t_onset, t_peak, t_end, -1*max_t2)
    return tt1, tt2


def get_tt_valid_n(mcglines, t_onset, t_peak, t_end, max_t):
    '''TT有效波段, 以负向波判断'''
    tt1, tt2 = t_onset, t_end
    for jj in range(t_onset, t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if min(mcgdata.ravel()) < max_t:
            tt1 = jj  # 有效波段起点
            break
    for jj in range(0, t_end-t_peak):
        mcgdata = mcg_float(mcglines, t_end-jj)
        if min(mcgdata.ravel()) < max_t:
            tt2 = t_end - jj  # 有效波段终点
            break
    return tt1, tt2


def get_tt_valid_p(mcglines, t_onset, t_peak, t_end, max_t):
    '''TT有效波段, 以正向波判断'''
    tt1, tt2 = t_onset, t_end
    for jj in range(t_onset, t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if max(mcgdata.ravel()) > max_t:
            tt1 = jj  # 有效波段起点
            break
    for jj in range(0, t_end - t_peak):
        mcgdata = mcg_float(mcglines, jj)
        if max(mcgdata.ravel()) > max_t:
            tt2 = t_end - jj  # 有效波段终点
            break
    return tt1, tt2


def getava(jline):
    alis = []
    for i1 in range(jline[2]):
        alis.append(jline[7 + jline[1] + 8 * i1])
    return alis


def getts(tline):
    ts = []
    for i1 in range(tline[0]):
        if i1 < tline[0] - 1:
            ts.append([tline[1+2*i1], tline[2+2*i1]])
        else:  # 最后一个取t+1=r_peak+1
            ts.append([tline[1+2*i1], tline[2+2*i1]+1])
    return ts


def getxys(frfile, seq, ts):
    lis, xss, yss = [], [], []
    for i1 in range(len(ts)):
        lis.append([[], []])
    for i1 in range(len(frfile)):
        line = frfile[i1]
        xss.append(line[seq[0]])
        yss.append(line[seq[1]])
        for j1 in range(len(ts)):
            if line[0] in range(ts[j1][0], ts[j1][1]):
                lis[j1][0].append(line[seq[0]])
                lis[j1][1].append(line[seq[1]])
                break
    return lis, xss, yss


def mcg_float(mcglines, loct):
    '''36心磁数值'''
    mcgdata = np.array(mcglines[loct-1].split()[1:]).reshape((6, 6))
    mcgdata = mcgdata.astype(float)
    return mcgdata


def multiply(p1, p2, p0):  # 叉乘计算
    return (p1[0] - p0[0]) * (p2[1] - p0[1]) - (p2[0] - p0[0]) * (p1[1] - p0[1])


def new_folder(root_dir):
    if type(root_dir) == list:
        for item in root_dir:
            new_folder(item)
    else:  # 单个
        try:
            os.mkdir(root_dir)
        except:
            pass
    return root_dir


def noqrs(itv):
    if itv == 1:
        return 1
    else:
        return 0


def norms0(Z0):
    '''正负分别归一化'''
    Z = Z0.copy()
    max_n = float('%4.6f' % np.max(Z))
    min_n = float('%4.6f' % np.min(Z))
    for i1 in range(Z.shape[0]):
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] < 0:
                Z[i1, j1] = -1 * Z[i1, j1] / min_n
            else:
                Z[i1, j1] = Z[i1, j1] / max_n
    return Z


def pcdm_default():
    '''电流密度图指标的初始化参数, 不输入参数'''
    xx, yy = np.meshgrid(np.arange(0, 20, 0.2), np.arange(0, 20, 0.2))
    diff_xx = np.diff(xx, axis=1)
    diff_xx = np.hstack((diff_xx, diff_xx[:, -1].reshape(-1, 1)))
    diff_yy = np.diff(yy, axis=0)
    diff_yy = np.vstack((diff_yy, diff_yy[-1, :]))
    return [diff_xx, diff_yy]


def plot_pic(mfm_fine, dfs):
    '''统计电流信息'''
    v, ca, x, y, offset_x, offset_y, mat_magni = get_offxy_mat(mfm_fine, dfs)
    dp, dv, mda = statred(offset_x, offset_y, mat_magni)
    return v, ca, x, y, dp, dv, mda


def rkdp(disp):
    '''波离散度等级'''
    if disp > 24.0:
        rdp = 3
    elif disp > 10.0:
        rdp = 2
    elif disp > 2.0:
        rdp = 1
    else:
        rdp = 0
    return rdp


def rkdpqrs(degree):
    '''qrs离散度等级'''
    if degree < 4.1:
        return 0
    elif degree < 20.0:
        return 1
    elif degree < 30.0:
        return 2
    else:
        return 3


def rkdptt(degree):
    '''tt离散度等级'''
    if degree < 4.3:
        return 0
    elif degree < 13.1:
        return 1
    elif degree < 24.6:
        return 2
    else:
        return 3


def rm_null(liebiao):
    '''去除列表中的空字符'''
    while "" in liebiao:
        liebiao.remove("")
    return liebiao


def save_txt(messages, mode='list', files=None):
    '''保存为txt'''
    doc = open(files, 'w')
    if mode == 'list':  # tuple
        for ii1 in range(len(messages)):
            doc.write(str(messages[ii1]))
            if ii1 < len(messages) - 1:
                doc.write('\n')
    elif mode == 'lists':  # tuple
        for ii1 in range(len(messages[0])):
            for jj1 in range(len(messages)):
                doc.write(str(messages[jj1][ii1]))
                if jj1 < len(messages) - 1:
                    doc.write(', ')
            if ii1 < len(messages[0]) - 1:
                doc.write('\n')
    elif mode == 'array':
        for ii1 in range(messages.shape[0]):
            doc.write(str(messages[ii1, :]))
            if ii1 < messages.shape[0] - 1:
                doc.write('\n')
    elif mode == 'dict':
        cnt = 0
        for key, value in messages.items():
            cnt += 1
            doc.write('%s %s' % (str(key), str(value)))
            if cnt < len(messages):
                doc.write('\n')
    else:  # int/str
        doc.write(str(messages))
    doc.close()
    return files


def sort_points_tan(p, pk):  # 极角排序, 不包含基准点
    p2 = []
    for i1 in range(0, len(p)):
        p2.append({"index": i1, "arc": get_arc(p[i1], pk)})
    p2.sort(key=lambda k: (k.get('arc')))
    p_out = []
    for i1 in range(0, len(p2)):
        p_out.append(p[p2[i1]["index"]])
    return p_out


def stat_Z(Z, clis):  # 中心点集label叠加
    Z0 = Z.copy()
    for c1 in clis:  # 单人中心点集
        if c1[0] in range(Z.shape[0]) and c1[1] in range(Z.shape[1]):
            Z0[c1[0], c1[1]] += 1  # 数值——
    return Z0


def stataf(ags):
    m1, m2, a1, a2 = 0, 0, 0, 360  # 角度范围m1, m2, a1, a2
    for j1 in range(len(ags) - 1):
        difag = abs(ags[j1] - ags[j1 + 1])
        p1 = min(ags[j1], ags[j1 + 1])
        p2 = max(ags[j1], ags[j1 + 1])
        if difag <= 180:  # 更新角度范围
            if j1 == 0:
                m1, m2 = p1, p2
            elif a1 == 0 and a2 == 360:
                m1, m2 = min(m1, p1), max(m2, p2)
            elif p1 > a1:
                a2 = min(a2, p1)
            elif p2 < a2:
                a1 = max(a1, p2)
            else:
                m1, m2, a1, a2 = 0, 0, 360, 0
                break
        else:
            if m1 == 0 and m2 == 0:
                a1, a2 = max(a1, p1), min(a2, p2)
                if a1 > a2:
                    m1, m2, a1, a2 = 0, 0, 360, 0
                    break
                else:
                    continue
            elif m1 > p2 or m2 < p1:
                m1, m2, a1, a2 = 0, 0, p1, p2
                continue
            elif m1 > p1:
                a1, a2 = p1, min(m1, p2)
            elif m2 < p2:
                a1, a2 = max(m2, p1), p2
            else:
                m1, m2, a1, a2 = 0, 0, 360, 0
                break
            m1 = 0
            m2 = 0
    return m1, m2, a1, a2


def statcor(x0, y0):
    lis, liss = [], []
    lis.append([int(x0), int(y0)])
    lis.append([int(x0), round(y0)])
    lis.append([round(x0), int(y0)])
    lis.append([round(x0), round(y0)])
    for a in lis:
        if a not in liss:
            liss.append(a)
    return liss


def statctp(corf, ij, ik):
    '''点集生成20240204'''
    cjpts = []
    for i1 in range(len(corf)):
        cors = get_dipole_ct(corf[i1][ij], corf[i1][ik])
        for cor in cors:
            if cor not in cjpts:
                cjpts.append(cor)
    return cjpts


def statctp_raw(corf, ij, ik):
    '''点集生成-原始2023'''
    Z = np.zeros((100, 100))
    clis = []
    for i1 in range(len(corf)):
        liss = statcor(corf[i1][ij], corf[i1][ik])  # 当前帧中心点集
        for a in liss:
            if a not in clis:
                clis.append(a)
    for c1 in clis:  # 个人全部中心点集
        if c1[0] in range(Z.shape[0]) and c1[1] in range(Z.shape[1]):
            Z[c1[0], c1[1]] = 1  # 数值——
    return clis


def statdppn_QR(tim, lis0):
    '''统计QR稳定性列表stabs=[0/1, ...]'''
    dpp0, dpps, dpns, pntts = get_stabmgs(tim, lis0, 'QR')
    return cal_stabs(dpp0, dpps, dpns, pntts, range(tim))


def statdppn_RS(tim, lis0):
    '''统计RS稳定性列表stabs=[0/1, ...]'''
    dpp0, dpps, dpns, pntts = get_stabmgs(tim, lis0, 'RS')
    return cal_stabs(dpp0, dpps, dpns, pntts, range(tim-1))


def statnums(Z, lis, nsik=4):
    '''统计中心轨迹等级分布数量, nsik为等级数量'''
    ns = [0, 0, 0, 0]
    for ik in range(4, nsik):
        ns.append(0)
    for a in lis:
        k1 = int(Z[a[0]][a[1]])
        ns[k1] += 1
    ns.append(sum(ns))
    return ns


def statpf(Z):
    cors = [[], []]
    mcgs = [[], []]
    pap, pan = 0, 0
    for i1 in range(Z.shape[0]):
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] > 0.8:
                cors[0].append([i1, j1])
                mcgs[0].append(Z[i1, j1])
                pap += 1
            elif Z[i1, j1] < -0.8:
                cors[1].append([i1, j1])
                mcgs[1].append(Z[i1, j1])
                pan += 1
    return cors, pap, pan, mcgs


def statpnpt(matrix, Mf):
    '''计算初始信息'''
    Nf = matrix.max() - matrix.min()  # 幅值
    px, py, nx, ny = get_dipole(matrix)
    dx = ny - py  # 坐标和角度指向
    dy = px - nx
    a = calangle(dx, dy)  # 角度
    q = calquad(a)  # 象限
    qrd = pow(dx * dx + dy * dy, 1 / 2)  # 偶极子距离
    V1 = Nf / Mf  # 幅值归一化
    fx, fy = calptcors(V1, dx, dy)  # 面积-心磁坐标
    # V1 = 1 / pow(50 * 50 + 50 * 50, 1 / 2)  # 距离归一化
    V1 = qrd / pow(50 * 50 + 50 * 50, 1 / 2)  # 距离归一化
    mx, my = calptcors(V1, dx, dy)  # 面积-距离坐标
    cx = (px + nx) / 2  # 中心点坐标和坐标
    cy = (py + ny) / 2
    return a, q, qrd, fx, fy, mx, my, cx, cy


def statqs(qs):
    alist = []
    for j1 in range(len(qs)):
        alist.append(int(qs[j1]))
    alist = sorted(alist)
    slist = ''
    for j1 in range(len(qs)):
        slist = slist + str(alist[j1])
    return slist


def statquad(frfile, ik=2):
    '''q象限统计'''
    nq1, nq2, nq3, nq4 = 0, 0, 0, 0
    for i1 in range(len(frfile)):
        line = frfile[i1]
        if line[ik] == '1':
            nq1 += 1
        elif line[ik] == '2':
            nq2 += 1
        elif line[ik] == '3':
            nq3 += 1
        elif line[ik] == '4':
            nq4 += 1
    return nq1, nq2, nq3, nq4


def statred(offset_x, offset_y, mat_magni):
    '''红色电流信息'''
    redlis, dalis, dp = [], [], 0
    vmax = np.max(mat_magni)
    for i1 in range(mat_magni.shape[0]):
        for j1 in range(mat_magni.shape[1]):
            if mat_magni[i1, j1] >= vmax * 0.8:
                v1 = mat_magni[i1, j1] * 0.232515 / 1.162577
                a1 = calangle(offset_x[i1, j1], -offset_y[i1, j1])
                redlis.append([v1, a1, i1, j1])
                if mat_magni[i1, j1] == vmax:
                    mv, ma, mx, my = v1, a1, i1, j1
    for i1 in range(len(redlis)):
        dk = pow((redlis[i1][2]-mx)**2+(redlis[i1][3]-my)**2, 1/2) / 1.3
        dp += 2 * dk * redlis[i1][0] / mv
        v1 = abs(redlis[i1][1] - ma)
        v1 = min(v1, 360-v1)
        dalis.append(v1)
    dv = sum(dalis) / len(redlis)
    mda = max(dalis)
    return dp, dv, mda


def txt_to_array(mfm_file, time0=None):
    '''心磁txt文件转array格式[N, 36], 可选地返回一帧[6, 6]'''
    data0 = pd.read_csv(mfm_file, header=None, sep='\t')
    data0 = data0.iloc[:, 1:]
    data0 = np.array(data0)
    if time0:
        data0 = data0[time0-1, :].reshape(6, 6)
    return data0


def txt_to_list(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    lists = [ast.literal_eval(line) for line in lines]  # 读取为list
    return lists


def unzip_file(zip_filepath, dest_path):
    '''解压缩zip'''
    with zipfile.ZipFile(zip_filepath, 'r') as zip_ref:
        zip_ref.extractall(dest_path)
    return dest_path


def write_mt(idlis, mtlis, files):
    '''写入指标'''
    strs = ''
    for i1 in range(len(idlis)):
        strs += '\n%s' % idlis[i1]
        for j1 in range(len(mtlis)):
            strs += ', %s' % str(mtlis[j1][i1])
    write_str(strs, files)
    return idlis


def write_str(strs, files, mode='w'):
    '''写入字符串, mode=a时添加字符串'''
    if mode == 'a':
        doc = open(files, 'a')
    else:
        doc = open(files, 'w')
    doc.write(strs)
    doc.close()
    return files


def write_strlis(mslis, mflis):
    if type(mslis) != str:
        for i2 in range(len(mflis)):
            write_strlis(mslis[i2], mflis[i2])
    else:
        write_str(mslis, mflis)
    return mflis


def writecvpts(cvpts):
    cvfile = [len(cvpts)]
    for i1 in range(len(cvpts)):
        ri = cvpts[i1]
        cvfile.extend([ri[0], ri[1]])
    return cvfile


def writedisp(matrixes, timelis0, mcglines, mode='qrs'):
    '''离散度指标和等级计算'''
    mlfile, rksfile = [], []
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    rkslis = []
    for j2 in range(q_peak-5):
        rkslis.append(0)
    for j2 in range(q_peak-5, t_end+1):
        matrix = matrixes[j2-1]
        Z = norms0(matrix)  # 正负归一化
        pn = statpn(Z, vmin=0.3)  # 多极子
        dpp, dpn = caldp(pn)  # 离散度
        disp = max(dpp, dpn)
        rdp = rkdp(disp)
        rkslis.append(rdp)
        if j2 in [q_peak, r_peak, s_peak, t_peak]:
            mlfile.append(disp)
            rksfile.append(rdp)
    tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end, mode='pn')
    t1, t2 = get_qrs_t12(mcglines, q_peak, 5, 0.975)  # 截断、阈值
    t3, t4 = get_qrs_t12(mcglines, r_peak, 10, 0.95)
    t5, t6 = get_qrs_t12(mcglines, s_peak, 5, 0.975)
    dpq = sum(rkslis[j2] for j2 in range(t1, t2) if rkslis[j2] >= 2)
    dpr = sum(rkslis[j2] for j2 in range(t3, t4) if rkslis[j2] >= 2)
    dps = sum(rkslis[j2] for j2 in range(t5, t6) if rkslis[j2] >= 2)
    dpt = sum(rkslis[j2] for j2 in range(tt1, tt2) if rkslis[j2] >= 2)
    if mode == 'newqrs':  # 修正后
        dpqrs = round(10 * (dpq / (t2-t1) + dpr / (t4-t3) + dps / (t6-t5)), 1)
    else:  # 'qrs'->代码错误
        dpqrs = round(10 * (dpq / (t2-t1) + dpq / (t4-t3) + dpq / (t6-t5)), 1)
    dptt = round(20 * dpt / (tt2-tt1), 1)
    rkqrs, rktt = rkdpqrs(dpqrs), rkdptt(dptt)
    noq, nor, nos = noqrs(t2-t1), noqrs(t4-t3), noqrs(t6-t5)
    mlfile.extend([dpqrs, dptt, noq, nor, nos])
    rksfile.extend([rkqrs, rktt])
    return mlfile, rksfile


def writejr(wg, m, n, js, ts, ns):
    mjfile0 = [get_round(wg, 1), m, n]
    for i1 in range(len(js)):
        mjfile0.append(js[i1])
    for i1 in range(len(ns)):
        ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][6], ns[i1][7] = get_round([ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][6], ns[i1][7]], [1, 2, 2, 1, 3, 3])
        mjfile0.extend([ns[i1][0], ns[i1][1], ns[i1][2], ns[i1][3], ns[i1][4], ns[i1][5], ns[i1][6], ns[i1][7]])
    tjfile = [len(ts)]
    for i1 in range(len(ts)):
        tjfile.extend([ts[i1][0], ts[i1][1]])
    return mjfile0, tjfile


def writemetric(frfile, Z0, t_idx=150, mode='qr', nsik=4):
    '''
    计算等磁图指标
    单帧&转角信息->指标mpfile, mjfile, mdfile, mefile, mffile, cdfile
    '''
    lis = statctp(frfile, 8, 9)
    ns = statnums(Z0, lis, nsik)
    cdfile = calns(ns, nsik)
    nq1, nq2, nq3, nq4 = statquad(frfile)
    areaf = calarea(frfile, 4, 5)  # 幅值面积
    aread = calarea(frfile, 6, 7)  # 距离面积
    if mode == 'tt':
        ta, tt50, tt75, tta, q, m1, m2, a1, a2 = calalis_tt(frfile, t_idx)
        ta, tt50, tt75, tta, m1, m2, a1, a2, areaf, aread = get_round([ta, tt50, tt75, tta, m1, m2, a1, a2, areaf, aread], [1, 1, 1, 1, 1, 1, 1, 1, 3, 3])
        mpfile = [ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread]
    else:
        qa, ra, qra, q, m1, m2, a1, a2 = calalis(frfile)
        qa, ra, qra, m1, m2, a1, a2, areaf, aread = get_round([qa, ra, qra, m1, m2, a1, a2, areaf, aread], [1, 1, 1, 1, 1, 1, 1, 3, 3])
        mpfile = [qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread]
    rlis = calrt(frfile, 1)
    wg, m, n, js, ts, ns = caljr(frfile, rlis, 1, [4, 5], [6, 7])
    mjfile0, tjfile = writejr(wg, m, n, js, ts, ns)
    mjfile1 = writemj1(mjfile0)
    saf, sad = writesarea(tjfile, mjfile0, frfile, [4, 5], [6, 7])
    mjfile = mjfile1 + [saf, sad]
    qrdma, qrdav, qrdmse = calmas(frfile, 3)  # 偶极子间距最大/平均/均方差
    qrcd, qrcar, qrcre9 = calqrc(frfile, 8, 9)  # QR中心距离、面积、区域9
    dppma, dppav, dppmse = calmas(frfile, 10)  # 正离散度
    dpnma, dpnav, dpnmse = calmas(frfile, 11)  # 负离散度
    pnpma, pnpflu = calmfl(frfile, 12)  # 正主极子数量最大、波动次数
    pnnma, pnnflu = calmfl(frfile, 13)  # 负主极子数量
    epma, epav, epmse = calmas(frfile, 14)  # 正离心率
    enma, enav, enmse = calmas(frfile, 15)  # 负离心率
    qrdma, qrdav, qrdmse, qrcd, qrcar = get_round([qrdma, qrdav, qrdmse, qrcd, qrcar], [1, 1, 1, 1, 3])
    mdfile = [qrdma, qrdav, qrdmse, qrcd, qrcar, qrcre9]
    dppma, dppav, dppmse, dpnma, dpnav, dpnmse = get_round([dppma, dppav, dppmse, dpnma, dpnav, dpnmse], [1, 1, 1, 1, 1, 1])
    mefile = [dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu]
    epma, epav, epmse, enma, enav, enmse = get_round([epma, epav, epmse, enma, enav, enmse], [2, 2, 1, 2, 2, 1])
    mffile = [epma, epav, epmse, enma, enav, enmse]
    return mpfile, mjfile, mdfile, mefile, mffile, cdfile


def writemetric1(frfile, Z0, nsik=4):
    '''
    计算电流密度图指标
    单帧信息->指标mpfile, mjfile, mdfile, mefile, cdfile
    '''
    lis = statctp(frfile, 3, 4)
    ns = statnums(Z0, lis, nsik)
    cdfile = calns(ns, nsik)
    qca, rca, qra, q, m1, m2, a1, a2 = calalis(frfile, ik=2)
    qia, ria = get_ht(frfile, ik=10)
    nq1, nq2, nq3, nq4 = statquad(frfile, ik=8)
    iama, iami, iaav, iamse = calmas(frfile, 10, mode='min')
    qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, m1, m2, a1, a2 = get_round([qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, m1, m2, a1, a2], [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1])
    mafile = [qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2]
    mctd, mcar, mcre9 = calqrc(frfile, 3, 4)
    areac = calarea(frfile, 11, 12)
    aread = calarea(frfile, 13, 14)
    mctd, mcar, areac, aread = get_round([mctd, mcar, areac, aread], [1, 3, 3, 3])
    mpfile = [mctd, mcar, mcre9, areac, aread]
    dpma, dpav, dpmse = calmas(frfile, 5)
    dvma, dvav, dvmse = calmas(frfile, 6)
    mdama, mdaav, mdamse = calmas(frfile, 7)
    dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse = get_round([dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse], [1, 1, 1, 1, 1, 1, 1, 1, 1])
    mrfile = [dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse]
    rlis = calrt(frfile, 2)
    wg, m, n, js, ts, ns = caljr(frfile, rlis, 2, [11, 12], [13, 14])
    mjfile0, tjfile = writejr(wg, m, n, js, ts, ns)
    mjfile1 = writemj1(mjfile0)
    saf, sad = writesarea(tjfile, mjfile0, frfile, [11, 12], [13, 14])
    mjfile = mjfile1 + [saf, sad]
    return mafile, mjfile, mpfile, mrfile, cdfile


def writemetric_tt0(matrixes, mcglines, t_onset, t_peak, t_end):
    '''生成等磁图指标TT-初版top5指标'''
    tt1, tt2 = get_tt12(mcglines, t_onset, t_peak, t_end)
    Mf = calmf(matrixes, tt1, tt2)
    flist = []
    for jj in range(tt1, tt2 + 1):
        matrix = matrixes[jj-1]
        Nf = matrix.max() - matrix.min()  # 幅值
        px, py, nx, ny = get_dipole(matrix)
        fx, fy = calptcors(Nf/Mf, ny-py, px-nx)  # 面积-心磁坐标
        flist.append([fx, fy])
    area = calarea(flist, 0, 1)
    mpfile = [get_round(area, 3)]
    return mpfile


def writemj1(line):
    wg, m, n = line[0], line[1], line[2]
    ms, fs, rs, vs, mses, qs, afs, ads = [], [], [], [], [], [], [], []
    for j1 in range(m):
        ms.append(line[3 + j1])
    pm, nm = 0, 0
    if len(ms) > 0:
        pm = max(0, max(ms))  # 顺时针跳转最大值
        nm = min(0, min(ms))  # 逆时针跳转最大值
    for j1 in range(int(n)):
        fs.append(line[3 + m + 8 * j1])  # 帧
        rs.append(line[4 + m + 8 * j1])  # 转角
        vs.append(line[5 + m + 8 * j1])  # 角速度
        mses.append(line[6 + m + 8 * j1])  # 均方差
        qs.append(line[8 + m + 8 * j1])  # 象限
        afs.append(line[9 + m + 8 * j1])  # 面积-幅值
        ads.append(line[10 + m + 8 * j1])  # 面积-距离
    r = sum(rs)  # 转角和
    v, mse = 0, 0
    for j1 in range(n):
        v += fs[j1] * vs[j1]
        mse += fs[j1] * mses[j1]
    v = v / sum(fs)  # 平均角速度
    mse = mse / sum(fs)  # 平均均方差（扰动性）
    v1 = np.average(vs)
    mse1 = np.average(mses)
    nqs, vqs = [0, 0, 0, 0], [0, 0, 0, 0]
    for j1 in range(int(n)):
        nqs[int(qs[j1]) - 1] += 1  # 平均跳转象限
        vqs[int(qs[j1]) - 1] += fs[j1]  # 累计跳转象限
    af = sum(afs)  # 面积-幅值和
    ad = sum(ads)  # 面积-距离和
    pm, nm, r, v, mse, af, ad, v1, mse1 = get_round([pm, nm, r, v, mse, af, ad, v1, mse1], [3, 3, 2, 2, 2, 3, 3, 2, 2])
    mjfile1 = [wg, m, pm, nm, r, v, mse, nqs[0], nqs[1], nqs[2], nqs[3], vqs[0], vqs[1], vqs[2], vqs[3], af, ad, v1, mse1]
    return mjfile1


def writesarea(tjfile, jline, frfile, seqs1, seqs2):
    '''2种平均角度正负指向图'''
    ts = getts(tjfile)  # 获取时间段
    alis = getava(jline)  # 平均角度
    lis, xss, yss = getxys(frfile, [seqs1[0], seqs1[1]], ts)
    xss, yss = calavacors(alis, lis)  # 平均角度坐标
    af = calarea1(xss, yss)
    lis, xss, yss = getxys(frfile, [seqs2[0], seqs2[1]], ts)
    xss, yss = calavacors(alis, lis)
    ad = calarea1(xss, yss)
    af, ad = get_round([af, ad], [3, 3])
    return af, ad


def zipfolder(zip_path, folder_path):
    if type(zip_path) == list:
        for i4 in range(len(zip_path)):
            zipfolder(zip_path[i4], folder_path[i4])
    else:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:  
            for root, dirs, files in os.walk(folder_path):  
                for file in files:  
                    file_path = os.path.join(root, file)  
                    zipf.write(file_path, os.path.relpath(file_path, folder_path))
    return folder_path


def get_split(strs0):
    '''获取分割后的字符串列表'''
    strlis00 = re.split(' |,|\t', strs0.strip())
    strlis00 = rm_null(strlis00)
    return strlis00


def get_item(f2ls, ik=0, lis0=[], jd=3):
    '''提取文档列表的第ik个元素'''
    lis0 = []
    for i4 in range(len(f2ls)):
        row0 = get_split(f2ls[i4])
        lis0.append(get_item_real(row0[ik], jd=jd))
    return lis0


def get_item_real(item, jd=3):
    try:
        return int(item)
    except:
        try:
            item = float(item)
            return get_round(item, jd)
        except:
            if '\'' in item:  # 去除所有字符串符号''
                return get_item_real(item[1:-1])
            else:
                return str(item)


def get_lines(files):
    doc = open(files, 'r')
    lines = doc.readlines()
    doc.close()
    return lines


def get_lines1(files):
    '''获取文档内容, 去除换行府'''
    lines = get_lines(files)
    lines0 = []
    for i3 in range(len(lines)):
        lines0.append(lines[i3].replace('\n', ''))
    return lines0


def get_mcg36(f0, md=1):
    '''获取36条心磁信息: N*36, 精度%.6f'''
    l1 = [get_item(get_lines1(f0), i+1, [], jd=6) for i in range(36)]
    if md:  # N*36
        l1 = [[l1[i][j] for i in range(len(l1))] for j in range(len(l1[0]))]
    return l1


def get_rms0(f0):
    mcgdata, lrms = get_lines1(f0), []
    for i in range(len(mcgdata)):
        asimg = np.array(mcgdata[i].split()[1:])
        asimg = asimg.astype(float)  # str转float
        vrms = np.sqrt(np.sum(asimg**2, axis=0) / asimg.shape[0])
        lrms.append(vrms)
    return lrms


def cal_lsrms10(ls36, i1, i2, nm=10):
    '''计算ls10, rms10'''
    lis1 = [[i, max([abs(ls36[i][j]) for j in range(i1,i2)])] for i in range(len(ls36))]
    data = np.array(lis1)
    idex = np.lexsort((data[:, 0], -1*data[:, 1]))
    sorted_data = data[idex]
    lis1 = sorted_data.tolist()
    idx = [int(lis1[i][0]) for i in range(nm)]  # 筛选nm=10*个通道
    ls10 = [ls36[i] for i in idx]  # 10通道波
    return idx, ls10


def shaixuan_10channel(rms, ls36, rg, bl1=0.3, bl2=0.55, th=10, bz=10, nm=10):
    '''筛选较大通道: rms最大位置R0, 前后rg帧取绝对值最大值, 取最大的nm通道'''
    rg = max(rg, round(len(ls36[0])*rg/1000))  # 前后rg=5**帧内
    th = max(th, round(len(ls36[0])*th/1000))  # 前后rg=5**帧内
    ik1, ik2 = round(len(rms)*bl1), round(len(rms)*bl2)  # 定位区间
    rms0 = rms[ik1:ik2+1]
    rpeaks, _ = signal.find_peaks(np.array(rms0), distance=th)
    vs = [rms0[i] for i in rpeaks]
    if vs == []:
        t0 = rms0.index(max(rms0))+ik1  # 最大波位置R0
    else:
        t0 = rpeaks[np.argmax(vs)]+ik1
    rms0 = [v0*bz/max(rms) for v0 in rms]  # RMS的bz=10*值规范化
    # 较大通道筛选
    i1, i2 = max(t0-rg,0), min(t0+rg+1,len(ls36[0]))
    idx, ls10 = cal_lsrms10(ls36, i1, i2, nm=10)
    return [t0, rms0, idx, ls10]


def cal_std(rms0, M0, rg2, i1=0, i2=0, i3=0, i4=0, bz=10, vv=5):
    '''计算信号波的规范化std: bz规范化+拟合, i1234两段取std, 当前帧为均值, rg2步长'''
    # rms = [abs(v0*bz/M0) for v0 in rms0]  # bz=10*值规范化
    rms = [v0*bz/M0 for v0 in rms0]  # bz=10*值规范化
    y_smooth = gaussian_filter1d(np.array(rms), sigma=vv)  # sigma5*高斯拟合
    # print(y_smooth.shape)
    rks, _ = signal.find_peaks(y_smooth)
    std = [0 for _ in range(len(rms))]  # 定义0
    for i in range(i1, i2):
        x0 = y_smooth[i]
        xs = y_smooth[max(0, i-rg2):i]
        std[i] = np.sqrt(np.sum((xs - x0) ** 2) / (len(xs) - 1 + 0.000001))
    for i in range(i3, i4):
        x0 = y_smooth[i]
        xs = y_smooth[i+1:min(len(rms), i+rg2+1)]
        std[i] = np.sqrt(np.sum((xs - x0) ** 2) / (len(xs) - 1 + 0.000001))
    std = [min(bz, v0) for v0 in std]
    QhSrs, _ = signal.find_peaks(-np.array(std))  # 标准差波谷——
    QhSrs = [i for i in QhSrs if i not in rks]
    return [std, QhSrs]


def cal_wendingdian(rms0, t0, rg1=100, rg2=20, rg3=20, rg4=30, bz=10, vv=5, td=[0.2,0.5,1]):
    '''计算QRS复合的最远稳定点QhSr: rms10值规范化, 高斯拟合, 最大值前后的20帧标准差
    std<0.5,10帧<0.5, 定位后Qh-20/Sr+30
    若无标准差波谷, 可能报错, 无Qh/Sr'''
    # 1.统计稳定点
    rg1 = max(rg1, round(len(rms0)*rg1/1000))
    rg2 = max(rg2, round(len(rms0)*rg2/1000))
    std, QhSrs = cal_std(rms0, rms0[t0], rg2, max(0,t0-rg1), t0, t0, min(len(rms0),t0+rg1+1))
    # 2.筛选稳定点
    Qh = []  # 逆序稳定点
    Sr = []
    td0, td1, td2 = td
    for i in range(len(QhSrs)):
        j = len(QhSrs)-1-i
        if QhSrs[j]<t0:
            i1 = QhSrs[j]
            if std[i1]<td1 and max(std[max(0,round(i1-rg2/2)):i1])<td1:
                Qh.append(i1)
        if QhSrs[i]>t0:
            i1 = QhSrs[i]
            if std[i1]<td1 and max(std[i1:min(len(std),round(i1+rg2/2+1))])<td1:
                Sr.append(i1)
    if Qh == []:
        # print('no < 0.5')
        Qh = [i for i in QhSrs if i<t0 and std[i]<td2]
        if Qh == []:
            # print('no < 1')
            Qh = [i for i in QhSrs if i<t0]
        Qh.reverse()
    if Sr == []:
        # print('no < 0.5')
        Sr = [i for i in QhSrs if i>t0 and std[i]<td2]
        if Sr == []:
            # print('no < 1')
            Sr = [i for i in QhSrs if i>t0]
    # 3.固定稳定点
    rg3 = max(rg3, round(len(rms0)*rg3/1000))
    rg4 = max(rg4, round(len(rms0)*rg4/1000))
    if Qh == []:
        Qh0 = max(0,t0-rg1)
    else:
        Qh00 = Qh[0]-rg3
        Qh0 = Qh00
        for i in Qh:
            if i<=Qh00:
                break
            if max(std[max(0,i-rg2):i])<td0:
                Qh0 = i
                break
    if Sr == []:
        Sr0 = min(len(rms0),t0+rg1+1)
    else:
        Sr00 = Sr[0]+rg4
        Sr0 = Sr00
        for i in Sr:
            if i>=Sr00:
                break
            if max(std[i:min(len(std),i+rg2+1)])<td0:
                Sr0=i
                break
    return [Qh0, Sr0, Qh, Sr, QhSrs]


def cal_jzd0(numbers, reverse=0, nm=3, bl=1):
    '''计算集中点: nm最小数量'''
    jzd, p0 = -1, numbers
    if len(numbers) < nm:
        return [jzd, numbers]
    # 数据排序
    sorted_numbers = np.sort(numbers)
    if sorted_numbers[-1]-sorted_numbers[0]<=nm+2:
        filtered_numbers = sorted_numbers
    else:
        # 计算四分位数
        # position_Q1 = (len(sorted_numbers) + 1) / 4
        # Q1 = sorted_numbers[math.ceil(position_Q1) - 1]
        # Q3 = sorted_numbers[math.floor(3*position_Q1) - 1]
        Q1 = np.percentile(sorted_numbers, 25)
        Q3 = np.percentile(sorted_numbers, 75)
        # 计算四分位距
        IQR = Q3 - Q1
        # 确定离散点
        outliers = sorted_numbers[(sorted_numbers < Q1 - bl * IQR) | (sorted_numbers > Q3 + bl * IQR)]
        # 排除离散点
        filtered_numbers = sorted_numbers[~np.isin(sorted_numbers, outliers)]
        # print(numbers, filtered_numbers)
    if len(filtered_numbers) >= nm:
        if reverse:
            filtered_numbers = filtered_numbers.tolist()
            filtered_numbers.reverse()
        values, counts = np.unique(filtered_numbers, return_counts=True)
        if max(counts) >= nm:  # 中位数-向下
            # modes = values[counts == np.max(counts)]
            # jzd = modes[math.ceil((len(modes)+1)/2) - 1]
            jzd = values[np.argmax(counts)]
            p0 = [i for i in filtered_numbers if i== jzd]
        else:
            frequent_value = [sum(1 for v in filtered_numbers if abs(v-v0)<nm) for v0 in filtered_numbers]
            # print(frequent_value)
            if max(frequent_value) >= nm:
                filtered_numbers = np.array(filtered_numbers)
                modes = filtered_numbers[frequent_value == np.max(frequent_value)]
                jzd = modes[math.floor((len(modes)+1)/2) - 1]
                p0 = [i for i in filtered_numbers if abs(i-jzd)<nm]
                # print('now', filtered_numbers, frequent_value, modes, jzd)
                # print(numbers, frequent_value, jzd)
                # jzd = filtered_numbers[np.argmax(frequent_value)]
    return [jzd, p0]


def cal_suyuanS(Sps, Spts, Sp0s, QRS0, nm1=3):
    '''计算S波的溯源筛选'''
    for i in range(1, len(Sps)):  # 循环剔除n-1次
        Sps1, Spts1 = Sps.copy(), Spts.copy()
        for j in range(1, len(Sps1)):  # 遍历第2-波是否剔除
            p0 = Spts1[j-1]
            p1 = list(set(Spts1[j]))
            p2 = []
            for k1 in p1:
                for ps in QRS0:
                    if k1 in ps:
                        k2 = ps.index(k1)
                        p2 += ps[max(0,k2-1):k2]
            if len([i1 for i1 in p2 if i1 in p0])<nm1:  # 至少溯源nm1=3个点
                Sps1.pop(j)
                Spts1.pop(j)
                Sp0s.pop(j)
                break
        if Sps1==Sps:
            break
        else:
            Sps, Spts = Sps1.copy(), Spts1.copy()
    return [Sps, Spts, Sp0s]


def cal_suyuanQ(Qps, Qpts, Qp0s, QRS0, nm1=3):
    '''计算Q波的溯源筛选'''
    for i in range(1, len(Qps)):  # 循环剔除n-1次
        Qps1, Qpts1 = Qps.copy(), Qpts.copy()
        for j in range(1, len(Qps1)):  # 遍历第2-波是否剔除
            p0 = Qpts1[j-1]
            p1 = list(set(Qpts1[j]))
            # print(p0, p1)
            p2 = []
            for k1 in p1:
                for ps in QRS0:
                    if k1 in ps:
                        k2 = ps.index(k1)
                        p2 += ps[k2+1:min(len(ps),k2+2)]
            if len([i1 for i1 in p2 if i1 in p0])<nm1:  # 至少溯源nm1=3个点
                Qps1.pop(j)
                Qpts1.pop(j)
                Qp0s.pop(j)
                break
        if Qps1==Qps:
            break
        else:
            Qps, Qpts = Qps1.copy(), Qpts1.copy()
    return [Qps, Qpts, Qp0s]


def hb_lis0(l0):
    '''合并多维列表的全部元素为一维'''
    if type(l0) == list:
        l1 = []
        for i in range(len(l0)):
            l1 += hb_lis0(l0[i])
        return l1
    else:
        return [l0]


def cal_beixuanbo(ls10, Qh0, Sr0, t0, th=10, th2=70, nm=2, bls=[3.2,0.01,0.01,0.015,0.014,0.02,0.04,0.07]):
    '''计算9通道备选QRS波: 截取QRS段, 9通道绝对值的最大波峰, 前后最多2个峰'''
    # 1.统计abs波峰点集
    th = max(th, round(len(ls10[0])*th/1000))
    nm = max(nm, round(len(ls10[0])*nm/1000))
    QRS0 = []
    for l0 in ls10:
        l0 = l0[Qh0:Sr0+1]
        pks, _ = signal.find_peaks(np.abs(l0), distance=th)  # 最小距离
        if len(pks) == 0:
            l0 = list(np.abs(l0))
            QRS0.append([l0.index(max(l0))])
        else:
            vs = [abs(l0[j]) for j in pks]
            i0 = np.argmax(vs)
            ps = [pks[j] for j in range(max(0,i0-nm), min(i0+nm+1, len(vs)))]
            QRS0.append(ps)
    # 2.1 R0集中点
    i0 = t0-Qh0
    p0 = []
    for i in range(len(QRS0)):
        cz = [abs(j-i0) for j in QRS0[i]]
        if min(cz)<th:
            t = QRS0[i][np.argmin(cz)]
            p0.append(t)
    Rpts = p0
    R0, p0 = cal_jzd0(p0)
    # Rp0s = p0
    # 2.2 Sp/Qp集中点
    pts = sorted(set(hb_lis0(QRS0)))
    pts = [i for i in pts if i not in p0]
    Sps, i1, Spts, Sp0s = [], R0+th, [], []
    for i in pts:
        if i > i1:  # i0后逐步添加备选S波
            p0 = []
            for j in range(len(QRS0)):
                p0 += [k for k in QRS0[j] if k>=i and k-i<th][:1]
            S0, p0 = cal_jzd0(p0)
            if S0 != -1:
                p0 = []
                for j in range(len(QRS0)):
                    l0 = [k for k in QRS0[j] if k>i1]
                    cz = [abs(k-S0) for k in l0]
                    if cz!=[]:
                        if min(cz)<th:
                            t = l0[np.argmin(cz)]
                            p0.append(t)
                Spts.append(p0)  # 记录集中点
                S1, p0 = cal_jzd0(p0)
                Sp0s.append(p0)
                i1 = S1+th/5
                if S1 == -1:
                    S1 = S0
                    print('Error!')
                Sps.append(S0)  # 添加S波
    Qps, i1, Qpts, Qp0s = [], R0-th, [], []
    pts.reverse()
    for i in pts:
        if i < i1:  # i0前逐步添加备选Q波
            p0 = []
            for j in range(len(QRS0)):
                p0 += [k for k in QRS0[j] if k<=i and i-k<th][-1:]
            Q0, p0 = cal_jzd0(p0, 1)  # 逆序最集中点
            if Q0 != -1:
                # p1 = p0.copy()
                p0 = []
                for j in range(len(QRS0)):
                    l0 = [k for k in QRS0[j] if k<i1]
                    cz = [abs(k-Q0) for k in l0]
                    if cz!=[]:
                        if min(cz)<th:
                            cz.reverse()
                            t = l0[len(cz)-1-np.argmin(cz)]
                            p0.append(t)
                Qpts.append(p0)  # 记录集中点
                Q1, p0 = cal_jzd0(p0, 1)
                Qp0s.append(p0)
                i1 = Q1-th/5
                if Q1== -1:
                    Q1=Q0
                    print('Error!')
                Qps.append(Q0)  # 添加Q波
    # 3.集中点溯源筛选
    Qps = sorted(set(Qps))
    Sps = sorted(set(Sps))
    Qps.reverse()
    Sps1, Qps1 = Sps.copy(), Qps.copy()
    Sps, Spts, Sp0s = cal_suyuanS(Sps1, Spts, Sp0s, QRS0)
    Qps, Qpts, Qp0s = cal_suyuanQ(Qps1, Qpts, Qp0s, QRS0)
    Qps = Qps[:min(len(Qps),2)]
    Sps = Sps[:min(len(Sps),2)]
    # 4.确定Q/S单波
    t00, t11 = R0-th, R0+th  # 最近Qh/Sr
    M0 = max(np.abs(hb_lis0([[ls10[i][j] for j in range(Qh0,Sr0+1)] for i in range(len(ls10))])))
    Qps2, Sps2 = [], []
    fg = [[0, 0], [0, 0]]
    bl1, bl2, bl3, bl4, bl5, bl6, bl7, bl8=bls
    if len(Qps)>0:
        v0 = cal_fuzhi(Qps[0], Qh0, Qp0s[0], QRS0, ls10, M0)
        Qps2 = [Qps[0]]
        if len(Qps)==2:
            v1 = cal_fuzhi(Qps[1], Qh0, Qp0s[1], QRS0, ls10, M0)
            if v1/v0>bl1:
                Qps2 = [Qps[1]]
                v0 = v1
                Qp0s[0] = Qp0s[1]
            if v0<bl2:
                Qps2 = []
            else:
                t00 = min([t00]+Qp0s[0])
        else:
            if v0<bl5:
                Qps2 = []
            else:
                t00 = min([t00]+Qp0s[0])
    if len(Sps)>0:
        v0 = cal_fuzhi(Sps[0], Qh0, Sp0s[0], QRS0, ls10, M0)
        Sps2 = [Sps[0]]
        if len(Sps)==2:
            v1 = cal_fuzhi(Sps[1], Qh0, Sp0s[1], QRS0, ls10, M0)
            if v1/v0>bl1:
                Sps2 = [Sps[1]]
                v0=v0
                Sp0s[0] = Sp0s[1]
            if v0<bl3:
                Sps2 = []
            else:
                t11 = max([t11]+Sp0s[0])
        else:
            if v0<bl4:
                Sps2 = []
            else:
                t11 = max([t11]+Sp0s[0])
    # 5.补充单Q/S波
    M0 = max(np.abs(hb_lis0([[ls10[i][j] for j in range(Qh0,Sr0+1)] for i in range(len(ls10))])))
    pts = sorted(set(Rpts))
    i1 = R0-th
    if Qps2 == []:
        p0 = []
        for i in range(len(QRS0)):
            l0 = [j for j in QRS0[i] if j in pts]  # 溯源R0单通道波峰点
            if l0 != []:
                l1 = [j for j in QRS0[i] if j<i1 and j<l0[0]][-1:]
                if l1 != []:  # 溯源点前的波峰点
                    v0 = round(abs(ls10[i][l1[0]+Qh0]/M0),3)
                    if v0>=bl5:
                        p0.append(l1[0])
        if len(p0) in [1,2]:
            Qps2 = [max(p0)]
            # t00 = min(t00, max(p0))
        elif len(p0)>2:
            a, b = find_peak_point(p0)
            Qps2 = [a]
            # t00 = min([t00]+b)
        t00 = min([t00]+p0)
    th2 = max(th2, round(len(ls10[0])*th2/1000))  # 最远S控制
    i1 = R0+th
    if Sps2 == []:
        p0 = []
        for i in range(len(QRS0)):
            l0 = [j for j in QRS0[i] if j in pts]
            if l0 != []:
                l1 = [j for j in QRS0[i] if j>i1 and j>l0[-1]][:1]
                if l1 != []:
                    v0 = round(abs(ls10[i][l1[0]+Qh0]/M0),3)
                    if v0>=bl6 and l1[0]-R0<th2:
                        p0.append(l1[0])
        if len(p0) in [1,2]:
            Sps2 = [min(p0)]
            # t11 = max(t11, min(p0))
        elif len(p0)>2:
            a, b = find_peak_point(p0, 1)
            Sps2 = [a]
            # t11 = max([t11]+b)
        t11 = max([t11]+p0)
    # 6.无Q/S波时,截断幅值
    M0 = max(np.abs(hb_lis0([[ls10[i][j] for j in range(Qh0,Sr0+1)] for i in range(len(ls10))])))
    if Qps2 == []:
        v0s = []
        for i in range(R0-th):
            j = R0-th-1-i
            v0 = max([round(abs(ls10[k][j+Qh0]/M0),3) for k in range(len(ls10))])
            v0s.append(v0)
            if v0<bl7:
                Qps2 = [j]
                t00 = min(t00, j)
                break
        if Qps2==[]:
            i = v0s.index(min(v0s))
            Qps2 = [R0-th-1-i]
            # print('no Q')
    if Sps2 == []:
        v0s = []
        for j in range(R0+th+1, Sr0):
            v0 = max([round(abs(ls10[k][j+Qh0]/M0),3) for k in range(len(ls10))])
            v0s.append(v0)
            if v0<bl8:
                Sps2 = [j]
                t11 = max(t11, j)
                break
        if Sps2==[]:
            i = v0s.index(min(v0s))
            Sps2 = [R0+th+1+i]
            # print('no S')
    t00 = round(t00-th/2)
    t11 = round(t11+th/2)
    
    Qps = [i+Qh0 for i in Qps2]
    Sps = [i+Qh0 for i in Sps2]
    Qps1 = [i+Qh0 for i in Qps1]
    Sps1 = [i+Qh0 for i in Sps1]
    # return [R0+Qh0, Sps, Qps, Sps1, Qps1, fg, t00+Qh0, t11+Qh0]
    return [R0+Qh0, Sps, Qps, t00+Qh0, t11+Qh0]


def find_peak_point(numbers, md=0):
    # 从大到小排序
    numbers.sort(reverse=True)
    # 计算相邻数字的差值
    differences = [numbers[i] - numbers[i + 1] for i in range(len(numbers) - 1)]
    # 将差值从小到大排序
    differences.sort()
    # 选择最小差值作为近邻距离
    min_diff = differences[0] if differences else 0
    # 统计每个数值的近邻数量
    near_count = {num: 0 for num in numbers}
    for num in numbers:
        for neighbor in [num - min_diff, num + min_diff]:
            if neighbor in numbers:
                near_count[num] += 1
    # 选择近邻数量最多的数值作为聚点
    max_near_count = max(near_count.values())
    peak_points = [num for num, count in near_count.items() if count == max_near_count]
    # 如果有多个数值的近邻数量相同，选择其中最大的数值
    peak_point = max(peak_points)
    if md:
        peak_point = min(peak_points)
    nums = [i for i in [peak_point-min_diff,peak_point+min_diff] if i in numbers]
    return [peak_point, nums]


def cal_fuzhi(Q0, Qh0, p0, QRS0, ls10, M0):
    '''多Q波时, 计算单个Q集中点的所在通道的最大幅值'''
    vs = []
    for i in range(len(QRS0)):
        if [j for j in p0 if j in QRS0[i]] != []:
            vs.append(round(abs(ls10[i][Q0+Qh0]/M0),3))
    v0 = max(vs)
    return v0


def cal_QhSr(rms, R0, i2, i3, Qh0, Sr0, ls10, rg0=20, rg1=8, rg2=8, th=10, bz=10, vv=5, nm=4):
    '''计算Qh/Sr'''
    rg0 = max(rg0, round(len(ls10[0])*rg0/1000))
    rg1 = max(rg1, round(len(ls10[0])*rg1/1000))
    rg2 = max(rg2, round(len(ls10[0])*rg2/1000))
    th = max(th, round(len(ls10[0])*th/1000))
    i1 = Qh0-rg1
    i4 = Sr0+rg2
    pts1, pts2 = [], []
    for i in range(len(ls10)):
        l0 = ls10[i].copy()
        M0 = max([abs(l0[j]) for j in range(Qh0, Sr0)])
        # std, QhSrs = cal_std(l0, M0, rg0, i1, i2, i3, i4)
        std, QhSrs = cal_std(l0, M0, rg0, i1, i2, i2, i2)
        p1 = [j for j in QhSrs if j in range(i1, round(i2+th/2))]
        pts1.append(p1)
    pts11 = hb_lis0(pts1)
    if len(pts11)==0:
        Qh = Qh0
    elif len(pts11)<=nm:
        Qh = find_peak_point(pts11)[0]
    else:
        ls = sorted(set(pts11))
        ls.reverse()
        jzd = []
        for i in range(len(ls)):
            jld = hb_lis0([[j for j in l0 if j<=ls[i] and j>=ls[i]-10][-1:] for l0 in pts1])
            if len(jld)>nm:  # 多于nm=4个近邻点
                jzd = [find_peak_point(jld)[0]]
                break
        if jzd == []:
            Qh = find_peak_point(pts11)[0]
        else:
            for i in range(3):
                jld = []
                for l0 in pts1:
                    l0 = [j for j in l0 if abs(j-jzd[0])<th]
                    ds = [abs(j-jzd[0]) for j in l0]
                    if len(ds)>0:
                        if sum(1 for j in ds if j==min(ds))==1:
                            jld.append(l0[np.argmin(ds)])
                jzd2 = find_peak_point(jld)[0]
                if jzd2==jzd[0]:
                    break
                else:
                    get_rms0
                    jzd = [jzd2]
            Qh = jzd[0]
    # 定位Sr
    rms10 = cal_rms(ls10)
    rks, _ = signal.find_peaks(-np.array(rms10))
    Srs = [j for j in rks if j in range(round(i3-th/2), i4)]
    Sr = Sr0
    if Srs:
        Sr = Srs[0]
    return [Qh, Sr]


def cal_PpTp(ls36, rms, R0, Qh, Sr, th1=100, th2=80, th=10, bl1=0.05, bl2=0.95, vv=5):
    '''计算Pp/Tp'''
    th1 = max(th1, round(len(rms)*th1/1000))
    th2 = max(th2, round(len(rms)*th2/1000))  # P波前后最大80
    th = max(th, round(len(rms)*th/1000))
    # P波定位
    i1 = round(len(rms)*bl1)
    k2 = max(i1, min(R0-th1,Qh-th))
    if len(rms)<500:
        k2 = max(i1, Qh-th)
    rms0 = rms[i1:k2+1]
    y_smooth = gaussian_filter1d(np.array(rms0), sigma=vv)
    rpeaks, _ = signal.find_peaks(y_smooth, distance=th)
    vs = [rms0[i] for i in rpeaks]
    if vs == []:
        Pp = rms0.index(max(rms0))+i1  # 最大波位置R0
    else:
        Pp = rpeaks[np.argmax(vs)]+i1
    # PhPr
    Ph0, Pr0 = max(0, Pp-th2), min([Pp+th2, R0-th1, Qh-th])  # 最远
    if len(rms)<500:
        Pr0 = min(Pp+th2, Qh-th)
    l0 = [ls36[i][Pp] for i in range(len(ls36))]
    M0 = max(np.abs(l0))
    if max(l0)>-min(l0):
        l1 = [10*max([ls36[i][j] for i in range(len(ls36))])/M0 for j in range(len(ls36[0]))]
    else:
        l1 = [-10*min([ls36[i][j] for i in range(len(ls36))])/M0 for j in range(len(ls36[0]))]
    y_smooth = gaussian_filter1d(np.array(l1), sigma=vv)
    ds = [0.5, 1]
    std, PhPrs = cal_std(l1, M0, 20, Ph0, Pp, Pp, Pr0)
    Ph, Pr = Ph0, Pr0
    l1 = [i for i in PhPrs if i>Ph0 and i<=Pp-th]
    if l1:
        l2 = [i for i in l1 if std[i]<ds[0]]
        if l2:
            Ph = l2[-1]
        else:
            l3 = [i for i in l1 if std[i]<ds[1]]
            if l3:
                Ph = l3[-1]
            else:
                Ph = l1[-1]
    l1 = [i for i in PhPrs if i<Pr0 and i>=Pp+th]
    if l1:
        l2 = [i for i in l1 if std[i]<ds[0]]
        if l2:
            Pr = l2[0]
        else:
            l3 = [i for i in l1 if std[i]<ds[1]]
            if l3:
                Pr = l3[0]
            else:
                Pr = l1[0]
    # T波定位
    i2 = round(len(rms)*bl2)
    k1 = min(i2, max(R0+th1,Sr+th))
    rms0 = rms[k1:i2+1]
    rpeaks, _ = signal.find_peaks(np.array(rms0), distance=th)
    vs = [rms0[i] for i in rpeaks]
    if vs == []:
        Tp = rms0.index(max(rms0))+k1  # 最大波位置R0
    else:
        Tp = rpeaks[np.argmax(vs)]+k1
    return [Pp, Tp, Ph, Pr]


def cal_rms(ls10):
    '''计算RMS'''
    rms = []
    for i in range(len(ls10[0])):
        asimg = np.array([ls10[j][i] for j in range(len(ls10))])
        rms.append(np.sqrt(np.sum(asimg**2, axis=0) / asimg.shape[0]))
    return rms


def cal_ToTe(ls36, rms0, Tp, Sr, rg=5, nm=10, th1=100, th2=80, th=10, bl=[0.5,0.2], ds=[120,140,60,130,80]):
    '''计算ToTe'''
    # 较大通道筛选
    rg = max(rg, round(len(ls36[0])*rg/1000))  # 前后rg=5**帧内
    i1, i2 = max(Tp-rg,0), min(Tp+rg+1,len(ls36[0]))
    idx, ls10 = cal_lsrms10(ls36, i1, i2, nm=10)
    rms = cal_rms(ls10)
    # 计算ToTe
    th1 = max(th1, round(len(rms)*th1/1000))
    th = max(th, round(len(rms)*th/1000))
    bl1, bl2 = bl
    dis1, dis2, dis3, dis4, dis5 = ds
    dis1 = min(dis1, round(len(rms)*dis1/1000))
    i1, i2 = min(Tp, max(Tp-th1,Sr+th)), min(Tp+th1,len(rms)-1)
    l0 = hb_lis0([l0[i1:i2+1] for l0 in ls36])
    M0 = max(np.abs(l0))
    M1 = max(rms[i1:i2+1])
    To1, To2, Te1, Te2 = [], [], [], []
    i1 = max(i1,Tp-dis1)
    k2 = max(Tp-th,i1)+1
    for i in range(i1, k2):
        j = k2-1+i1-i
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl1:
            To1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for i in range(i1, k2):
        j = k2-1+i1-i
        if rms[j]/M1>bl1:
            To2 = [[j, rms[j]]]
        else:
            break
    To = max([i1]+[i for i,_ in To1+To2])
    i2 = min(i2,Tp+dis1)+1
    k1 = min(Tp+th,i2)
    for j in range(k1, i2):
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl1:
            Te1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for j in range(k1, i2):
        if rms[j]/M1>bl1:
            Te2 = [[j, rms[j]]]
        else:
            break
    Te = min([i2]+[i for i,_ in Te1+Te2])
    # 计算ThTr
    th2 = max(th2, round(len(rms)*th2/1000))
    dis2 = max(dis2, round(len(rms)*dis2/1000))
    dis3 = max(dis3, round(len(rms)*dis3/1000))
    dis4 = max(dis4, round(len(rms)*dis4/1000))
    dis5 = max(dis5, round(len(rms)*dis5/1000))
    Th1, Th2, Tr1, Tr2 = [], [], [], []
    i1 = min(To, max([Sr+th2, Tp-dis2, To-dis3]))
    k2 = max(To, i1)+1
    for i in range(i1, k2):
        j = k2-1+i1-i
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl2:
            Th1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for i in range(i1, k2):
        j = k2-1+i1-i
        if rms[j]/M1>bl2:
            Th2 = [[j, rms[j]]]
        else:
            break
    Th = max([i1]+[i for i,_ in Th1+Th2])
    i2 = max(Te, min([len(rms)-1, Tp+dis4, Te+dis5]))+1
    k1 = min(Te, i2-1)
    for j in range(k1, i2):
        l1 = [ls36[k][j] for k in range(len(ls36))]
        if max(np.abs(l1))/M0>bl2:
            Tr1 = [[j, max(np.abs(l1))/M0]]
        else:
            break
    for j in range(k1, i2):
        if rms[j]/M1>bl2:
            Tr2 = [[j, rms[j]]]
        else:
            break
    Tr = min([i2]+[i for i,_ in Tr1+Tr2])
    stds = [0.5, 1]
    std, ThTrs = cal_std(rms, rms[Tp], 20, i1, Tp, Tp, i2)
    Ths1 = [i for i in ThTrs if i<k2 and std[i]<stds[0]]
    if Ths1:
        Th = max(Th, Ths1[-1])
    else:
        Th = max([Th]+[i for i in ThTrs if i<k2 and std[i]<stds[1]][-1:])
    Trs1 = [i for i in ThTrs if i>k1 and std[i]<stds[0]]
    if Trs1:
        Tr = min(Tr, Trs1[0])
    else:
        Tr = min([Tr]+[i for i in ThTrs if i>k1 and std[i]<stds[1]][:1])
    return [To, Te, Th, Tr]


def cal_times1(f0, g1=''):
    '''计算原squid时刻点'''
    try:
        rt = gettime(f0)
        return rt
    except:
        return ['', '', '', '', '', '', '', '', '', '', '', '', '', '']


def cal_times0(f0, g1=''):
    '''计算时刻点20250121'''
    try:
        ls36 = get_mcg36(f0, 0)
        rms = get_rms0(f0)
        t0, rms0, idx, ls10 = shaixuan_10channel(rms, ls36, 5)  # 10通道
        Qh0, Sr0, _, _, _ = cal_wendingdian(rms0, t0, 100, 20, 20, 30)
        Rp, Sps, Qps, t00, t11 = cal_beixuanbo(ls10, Qh0, Sr0, t0)
        Qh, Sr = cal_QhSr(rms0, Rp, t00, t11, Qh0, Sr0, ls10)
        Pp, Tp, Ph, Pr = cal_PpTp(ls36, rms0, Rp, Qh, Sr)
        To, Te, Th, Tr = cal_ToTe(ls36, rms, Tp, Sr)
        tms = [Ph, Pp, Pr, Qh, Qps[0], Rp, Sps[0], Sr, Th, To, Tp, Te, Tr, len(rms)]
        return tms
    except:
        return ['', '', '', '', '', '', '', '', '', '', '', '', '', '']
    # # 绘制
    # M0, M1 = get_mai0(ls36)
    # N = len(ls36[0])
    # x = np.arange(N)
    # clis = np.linspace(0, 1, 38)  # 36 去除首尾
    # plt.figure()
    # plt.style.use('bmh')
    # plt.rcParams['savefig.dpi'] = 512  # 保存像素640*480*dpi/100
    # plt.xticks(tms, tms, fontsize=4)  # x刻度
    # # plt.yticks(ytks, ytks, fontsize=4)
    # for i1 in range(len(ls36)):  # 时间波组图
    #     c = plt.get_cmap('gist_rainbow')(clis[i1])  # 36
    #     plt.plot(x, np.array(ls36[i1]), color=c, linewidth=0.5)  # 36
    # plt.plot([0, N-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    # if rms:
    #     plt.plot(x, rms, color='r', linewidth=1)
    # for i in tms:
    #     plt.plot([i, i], [-M1, M0], linewidth=0.5, alpha=0.5, c='r')
    # if g1:
    #     plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    # # plt.show()
    # plt.close()


def cal_times00(f0, g1=''):
    '''计算时刻点_原始'''
    ls36 = get_mcg36(f0, 0)
    rms = get_rms0(f0)
    tms = gettime(f0)
    # 绘制
    M0, M1 = get_mai0(ls36)
    N = len(ls36[0])
    x = np.arange(N)
    clis = np.linspace(0, 1, 38)  # 36 去除首尾
    plt.figure()
    plt.style.use('bmh')
    plt.rcParams['savefig.dpi'] = 512  # 保存像素640*480*dpi/100
    plt.xticks(tms, tms, fontsize=4)  # x刻度
    # plt.yticks(ytks, ytks, fontsize=4)
    for i1 in range(len(ls36)):  # 时间波组图
        c = plt.get_cmap('gist_rainbow')(clis[i1])  # 36
        plt.plot(x, np.array(ls36[i1]), color=c, linewidth=0.5)  # 36
    plt.plot([0, N-1], [0, 0], linewidth=1, alpha=0.5, c='k')
    if rms:
        plt.plot(x, rms, color='r', linewidth=1)
    for i in tms:
        plt.plot([i, i], [-M1, M0], linewidth=0.5, alpha=0.5, c='r')
    if g1:
        plt.savefig(g1, bbox_inches='tight', pad_inches=0)
    # plt.show()
    plt.close()
    return tms


def get_mai0(ls36):
    '''周期最大幅值'''
    M0 = max([max(ls36[i]) for i in range(len(ls36))])
    M1 = -min([min(ls36[i]) for i in range(len(ls36))])
    return [M0, M1]

