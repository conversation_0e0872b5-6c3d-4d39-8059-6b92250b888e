"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: stats_utils
date: 2025/5/12 下午2:46
desc: 
"""
import pywt

from feature_generator.utils.get_args import *
import pandas as pd



def format_feature_name(segment: str, datasource: str, metric: str, statistic_or_peakinfo: str = None, channel_idx: int = None, group_idx: int = None) -> str:
    """
    Generates a standardized feature name.
    Example: STATS_QRS_CDM_MAX_MAGNITUDE_MEAN
             STATS_P_SIGNAL_MAX_MIN_CHANNEL_RATIO_CH0
             STATS_T_CDM_MAX_MAGNITUDE_AT_TPEAK
             STATS_EEG_HJORTH_ACTIVITY_GROUP1
    """
    parts = ["STATS"]
    if segment: parts.append(segment.upper())
    if datasource: parts.append(datasource.upper())
    if metric: parts.append(metric.upper())

    suffix_parts = []
    if statistic_or_peakinfo:
        suffix_parts.append(str(statistic_or_peakinfo).upper())
    if channel_idx is not None:
        suffix_parts.append(f"CH{channel_idx}")
    if group_idx is not None:
        suffix_parts.append(f"GROUP{group_idx}")

    if suffix_parts:
        parts.append("_".join(suffix_parts))

    return "_".join(parts)

# # --- Test cases for the name formatter ---
# print(format_feature_name("QRS", "CDM", "MAX_MAGNITUDE", statistic_or_peakinfo="MEAN"))
# print(format_feature_name("P", "SIGNAL", "MAX_MIN_CHANNEL_RATIO", channel_idx=0))
# print(format_feature_name("T", "CDM", "MAX_MAGNITUDE", statistic_or_peakinfo="AT_TPEAK"))
# print(format_feature_name("GLOBAL", "EEG", "HJORTH_ACTIVITY", group_idx=1))
# print(format_feature_name("RS", "PEAK", "DURATION_RATIO_VS_QT"))
# print(format_feature_name("P", "PEAK", "LOCATION", statistic_or_peakinfo="VALUE"))
#


# Potentially in feature_utils.py or at the top of get_QRSwave_features.py / get_Twave_features.py

# For pcd_rec model='e' (Current Density Map)
PCD_CDM_METRICS = {
    0: "MAX_MAGNITUDE",
    1: "MAX_ANGLE",
    2: "MAX_X_POSITION",
    3: "MAX_Y_POSITION",
}
PCD_CDM_NORM_STATUS = { # True if statistic_analysis, False if statistic_analysis_no_normaillize
    "MAX_MAGNITUDE": True,
    "MAX_ANGLE": False,
    "MAX_X_POSITION": False,
    "MAX_Y_POSITION": False,
}


# For pcd_rec model='m' (Magnetic Field Map)
PCD_MFM_METRICS = {
    0: "MINMAX_RATIO",        # original: ratio_minmax
    1: "MINMAX_ANGLE",        # original: angle_minmax
    2: "MINMAX_DISTANCE",     # original: dist_minmax
    3: "MAX_X_POLE",          # original: x_max_mfm
    4: "MAX_Y_POLE",          # original: y_max_mfm
    5: "MIN_X_POLE",          # original: x_min_mfm
    6: "MIN_Y_POLE",          # original: y_min_mfm
    7: "POSITIVE_POLE_AREA",  # original: area_pos_pole
    8: "NEGATIVE_POLE_AREA",  # original: area_neg_pole
}
PCD_MFM_NORM_STATUS = { # True if statistic_analysis, False if statistic_analysis_no_normaillize
    "MINMAX_RATIO": True, # Original used statistic_analysis for ratio_minmax in QRS, but not in T. Let's be consistent. Assuming True.
    "MINMAX_ANGLE": False,
    "MINMAX_DISTANCE": True,
    "MAX_X_POLE": False,
    "MAX_Y_POLE": False,
    "MIN_X_POLE": False,
    "MIN_Y_POLE": False,
    "POSITIVE_POLE_AREA": True,
    "NEGATIVE_POLE_AREA": True,
}


# This function would ideally go into a shared utils module for feature generation
# For now, let's imagine it's accessible by both get_Twave_features.py and get_QRSwave_features.py
# It would need imports: numpy as np, and your get_args: pcd_rec, statistic_analysis, statistic_analysis_no_normaillize, build_dict, max_distance
# And the format_feature_name, PCD_CDM_METRICS, etc. defined above.

def _extract_segment_pcd_stats(
        DATA_TEMP_Spline,  # Interpolated data
        loc_start: int,
        loc_end: int,
        gap_loc: int,
        pcd_model: str,  # 'e' or 'm'
        segment_name: str,  # e.g., "T", "QRS"
        specific_peak_loc: int = None,  # e.g., loc_t_peak, loc_r_peak
        specific_peak_suffix: str = None,  # e.g., "AT_TPEAK", "AT_RPEAK"
        # Optional args for pcd_rec if they vary beyond model
        save_path=None,
        filename=None,
        imShow=False,
        pcd_interval_name=None  # for pcd_rec's 'interval' arg if needed
):
    features = {}
    datasource_name = "CDM" if pcd_model == 'e' else "MFM"
    metric_map = PCD_CDM_METRICS if pcd_model == 'e' else PCD_MFM_METRICS
    norm_status_map = PCD_CDM_NORM_STATUS if pcd_model == 'e' else PCD_MFM_NORM_STATUS

    if loc_start is None or loc_end is None or loc_start > loc_end:
        print(
            f"Warning: Invalid segment for {segment_name} {datasource_name} features ({loc_start}-{loc_end}). Skipping.")
        return {}

    loop_set = np.arange(loc_start, loc_end + 1, gap_loc)
    if len(loop_set) == 0:
        print(f"Warning: Empty loop_set for {segment_name} {datasource_name} features. Skipping.")
        return {}

    # Store all results from pcd_rec for the segment
    # rec_all will be a list of dictionaries, each dict representing one time point's pcd_rec output
    rec_all_data_points = []
    for loc_idx, loc in enumerate(loop_set):
        mfm_at_loc = DATA_TEMP_Spline[loc]
        pcd_outputs = pcd_rec(mfm_at_loc, model=pcd_model, local=loc,  # Assuming 'local' is an arg for pcd_rec
                              save_path=save_path, filename=filename,
                              imShow=imShow, interval=pcd_interval_name)

        # Convert tuple output to a dictionary
        point_data = {metric_map[i]: pcd_outputs[i] for i in range(len(pcd_outputs))}
        rec_all_data_points.append(point_data)

    # --- Extract features at a specific peak (if provided) ---
    if specific_peak_loc is not None and specific_peak_suffix is not None:
        if loc_start <= specific_peak_loc <= loc_end:  # Ensure peak is within segment if relevant, or just valid
            mfm_at_peak = DATA_TEMP_Spline[specific_peak_loc]
            pcd_outputs_at_peak = pcd_rec(mfm_at_peak, model=pcd_model, local=specific_peak_loc,
                                          save_path=save_path, filename=filename,
                                          imShow=imShow,
                                          interval=pcd_interval_name)  # interval might be different for peak
            for i, val in enumerate(pcd_outputs_at_peak):
                metric_name = metric_map.get(i)
                if metric_name:
                    feat_name = format_feature_name(segment_name, datasource_name, metric_name,
                                                    statistic_or_peakinfo=specific_peak_suffix)
                    features[feat_name] = val
        else:
            print(
                f"Warning: specific_peak_loc {specific_peak_loc} out of bounds for {segment_name} or invalid. Skipping peak-specific metrics.")

    # --- Convert list of dicts to dict of lists (or numpy arrays) for easier statistical processing ---
    # e.g., {'MAX_MAGNITUDE': [v1, v2, ...], 'MAX_ANGLE': [a1, a2, ...]}
    segment_data_arrays = {}
    if rec_all_data_points:
        for key in rec_all_data_points[0].keys():
            segment_data_arrays[key] = np.array([dp[key] for dp in rec_all_data_points])

    # --- Calculate statistical features for each metric over the segment ---
    for metric_name, data_array in segment_data_arrays.items():
        if data_array.size == 0: continue

        use_normalized_stats = norm_status_map.get(metric_name, False)  # Default to no_normalize if not specified
        stat_func = statistic_analysis if use_normalized_stats else statistic_analysis_no_normaillize

        # `statistic_analysis` returns a dict like {'_Mean': val, '_Std': val}
        # We need to adapt these suffixes.
        # For now, assume statistic_analysis returns keys like `base_Mean`, `base_Std`
        # Or, we modify statistic_analysis to allow passing a full feature name prefix
        # Let's assume it returns a dict with fixed suffixes: _Mean, _Std, _Median, etc.

        # Temp assumption: statistic_analysis(data_array, base_name_for_stat_func)
        # Let's make base_name_for_stat_func an internal temporary name
        # and then remap its output
        temp_base_name = f"{segment_name}_{datasource_name}_{metric_name}"
        stats_dict_from_func = stat_func(data_array, temp_base_name)

        for key_from_stat_func, value in stats_dict_from_func.items():
            # Original keys are like 't_tt_max_magni_Mean'
            # We need to parse the statistic type from key_from_stat_func
            # Example: if key_from_stat_func is "someprefix_Mean", stat_type is "Mean"
            stat_type = key_from_stat_func.replace(temp_base_name + '_', '')  # Extracts "Mean", "Std"
            feat_name = format_feature_name(segment_name, datasource_name, metric_name,
                                            statistic_or_peakinfo=stat_type.upper())
            features[feat_name] = value

    # --- Calculate DERIVED features (specific to CDM or MFM) ---
    if pcd_model == 'e' and segment_data_arrays:  # CDM derived features
        x_pos = segment_data_arrays.get("MAX_X_POSITION")
        y_pos = segment_data_arrays.get("MAX_Y_POSITION")

        if x_pos is not None and y_pos is not None and x_pos.size > 1:
            # Relative displacement
            xy_coords = np.column_stack((x_pos, y_pos))
            rela_disp = np.sqrt(np.sum(np.diff(xy_coords, axis=0) ** 2, axis=1))
            if rela_disp.size > 0:
                # Apply stats to rela_disp
                derived_metric_name = "RELATIVE_DISPLACEMENT"
                stats_dict_from_func = statistic_analysis_no_normaillize(rela_disp,
                                                                         f"{segment_name}_{datasource_name}_{derived_metric_name}")
                for key, value in stats_dict_from_func.items():
                    stat_type = key.replace(f"{segment_name}_{datasource_name}_{derived_metric_name}_", '')
                    features[format_feature_name(segment_name, datasource_name, derived_metric_name,
                                                 statistic_or_peakinfo=stat_type.upper())] = value

            # Absolute magnitude and angle from origin
            abs_magni = np.sqrt(x_pos ** 2 + y_pos ** 2)
            derived_metric_name_mag = "ABSOLUTE_MAGNITUDE_ORIGIN"
            stats_dict_from_func_mag = statistic_analysis(abs_magni,
                                                          f"{segment_name}_{datasource_name}_{derived_metric_name_mag}")  # Normalized
            for key, value in stats_dict_from_func_mag.items():
                stat_type = key.replace(f"{segment_name}_{datasource_name}_{derived_metric_name_mag}_", '')
                features[format_feature_name(segment_name, datasource_name, derived_metric_name_mag,
                                             statistic_or_peakinfo=stat_type.upper())] = value

            abs_angle = np.arctan2(y_pos, x_pos) * (180 / np.pi)
            derived_metric_name_ang = "ABSOLUTE_ANGLE_ORIGIN"
            stats_dict_from_func_ang = statistic_analysis_no_normaillize(abs_angle,
                                                                         f"{segment_name}_{datasource_name}_{derived_metric_name_ang}")
            for key, value in stats_dict_from_func_ang.items():
                stat_type = key.replace(f"{segment_name}_{datasource_name}_{derived_metric_name_ang}_", '')
                features[format_feature_name(segment_name, datasource_name, derived_metric_name_ang,
                                             statistic_or_peakinfo=stat_type.upper())] = value

    elif pcd_model == 'm' and segment_data_arrays:  # MFM derived features
        x_max_pole = segment_data_arrays.get("MAX_X_POLE")
        y_max_pole = segment_data_arrays.get("MAX_Y_POLE")
        x_min_pole = segment_data_arrays.get("MIN_X_POLE")
        y_min_pole = segment_data_arrays.get("MIN_Y_POLE")
        area_pos_pole_arr = segment_data_arrays.get("POSITIVE_POLE_AREA")  # for ratio
        area_neg_pole_arr = segment_data_arrays.get("NEGATIVE_POLE_AREA")  # for ratio

        if all(arr is not None for arr in [x_max_pole, y_max_pole, x_min_pole, y_min_pole]):
            # Center minmax
            center_minmax_x = 0.5 * (x_max_pole + x_min_pole)
            center_minmax_y = 0.5 * (y_max_pole + y_min_pole)

            derived_metrics_center = {"CENTER_MINMAX_X": center_minmax_x, "CENTER_MINMAX_Y": center_minmax_y}
            for metric, data in derived_metrics_center.items():
                stats_dict_from_func = statistic_analysis_no_normaillize(data,
                                                                         f"{segment_name}_{datasource_name}_{metric}")
                for key, value in stats_dict_from_func.items():
                    stat_type = key.replace(f"{segment_name}_{datasource_name}_{metric}_", '')
                    features[format_feature_name(segment_name, datasource_name, metric,
                                                 statistic_or_peakinfo=stat_type.upper())] = value

            # Max distance of center_minmax points (if max_distance is available)
            if center_minmax_x.size > 0 and 'max_distance' in globals():  # Check if max_distance is imported
                center_coords = np.column_stack((center_minmax_x, center_minmax_y))
                max_dist_val = max_distance(center_coords)[0]  # Assuming max_distance returns a tuple/list
                features[format_feature_name(segment_name, datasource_name, "MAX_DISTANCE_CENTER_MINMAX_POINTS",
                                             statistic_or_peakinfo="VALUE")] = max_dist_val

            # Relative displacement of positive and negative poles
            pos_pole_coords = np.column_stack((x_max_pole, y_max_pole))
            neg_pole_coords = np.column_stack((x_min_pole, y_min_pole))

            if pos_pole_coords.shape[0] > 1:
                rela_dist_pos = np.sqrt(np.sum(np.diff(pos_pole_coords, axis=0) ** 2, axis=1))
                derived_metric_pos = "POSITIVE_POLE_RELATIVE_DISPLACEMENT"
                stats_dict_from_func = statistic_analysis_no_normaillize(rela_dist_pos,
                                                                         f"{segment_name}_{datasource_name}_{derived_metric_pos}")
                for key, value in stats_dict_from_func.items():
                    stat_type = key.replace(f"{segment_name}_{datasource_name}_{derived_metric_pos}_", '')
                    features[format_feature_name(segment_name, datasource_name, derived_metric_pos,
                                                 statistic_or_peakinfo=stat_type.upper())] = value

            if neg_pole_coords.shape[0] > 1:
                rela_dist_neg = np.sqrt(np.sum(np.diff(neg_pole_coords, axis=0) ** 2, axis=1))
                derived_metric_neg = "NEGATIVE_POLE_RELATIVE_DISPLACEMENT"
                stats_dict_from_func = statistic_analysis_no_normaillize(rela_dist_neg,
                                                                         f"{segment_name}_{datasource_name}_{derived_metric_neg}")
                for key, value in stats_dict_from_func.items():
                    stat_type = key.replace(f"{segment_name}_{datasource_name}_{derived_metric_neg}_", '')
                    features[format_feature_name(segment_name, datasource_name, derived_metric_neg,
                                                 statistic_or_peakinfo=stat_type.upper())] = value

            # Abs magnitude and angle for pos, neg, center poles
            pole_types_coords = {
                "POSITIVE_POLE": (pos_pole_coords, True),  # Name, coords, normalize magnitude
                "NEGATIVE_POLE": (neg_pole_coords, True),
                "CENTER_MINMAX_POLE": (np.column_stack((center_minmax_x, center_minmax_y)), True)
            }
            for pole_name, (coords, norm_mag) in pole_types_coords.items():
                if coords.shape[0] > 0:
                    abs_mag = np.sqrt(np.sum(coords ** 2, axis=1))
                    abs_ang = np.arctan2(coords[:, 1], coords[:, 0]) * (180 / np.pi)

                    mag_stat_func = statistic_analysis if norm_mag else statistic_analysis_no_normaillize

                    metric_mag = f"{pole_name}_ABS_MAGNITUDE_ORIGIN"
                    stats_mag = mag_stat_func(abs_mag, f"{segment_name}_{datasource_name}_{metric_mag}")
                    for key, value in stats_mag.items():
                        stat_type = key.replace(f"{segment_name}_{datasource_name}_{metric_mag}_", '')
                        features[format_feature_name(segment_name, datasource_name, metric_mag,
                                                     statistic_or_peakinfo=stat_type.upper())] = value

                    metric_ang = f"{pole_name}_ABS_ANGLE_ORIGIN"
                    stats_ang = statistic_analysis_no_normaillize(abs_ang,
                                                                  f"{segment_name}_{datasource_name}_{metric_ang}")  # Angles usually no_normaillize
                    for key, value in stats_ang.items():
                        stat_type = key.replace(f"{segment_name}_{datasource_name}_{metric_ang}_", '')
                        features[format_feature_name(segment_name, datasource_name, metric_ang,
                                                     statistic_or_peakinfo=stat_type.upper())] = value

            # Ratio of mean positive pole area to mean negative pole area (specific to T-wave in original)
            if segment_name == "T" and area_pos_pole_arr is not None and area_neg_pole_arr is not None and area_pos_pole_arr.size > 0 and area_neg_pole_arr.size > 0:
                mean_pos_area = np.mean(area_pos_pole_arr)
                mean_neg_area = np.mean(area_neg_pole_arr)
                ratio_val = np.nan
                if mean_neg_area != 0:
                    ratio_val = mean_pos_area / mean_neg_area
                elif mean_pos_area != 0:  # pos is non-zero, neg is zero
                    ratio_val = np.inf  # Or a large number
                # Add this specific feature
                features[
                    format_feature_name(segment_name, datasource_name, "MEAN_POSITIVE_AREA_TO_MEAN_NEGATIVE_AREA_RATIO",
                                        "VALUE")] = ratio_val

    return features


# Assuming imports for _extract_segment_pcd_stats, format_feature_name,
# PCD_CDM_METRICS, PCD_MFM_METRICS etc. are available.
# Also assuming build_dict from get_args is available.

def time_features_t(DATA_TEMP_Spline, signal_channels_normalized, loc_t_onset, loc_t_peak, loc_t_end, gap_loc,
                    save_path=None, filename=None, imShow=False):
    # signal_channels is not used in the original time_features_t, DATA_TEMP_Spline is.

    all_t_wave_time_features = {}

    # Electric features (CDM)
    cdm_features = _extract_segment_pcd_stats(
        DATA_TEMP_Spline=DATA_TEMP_Spline,
        loc_start=loc_t_onset,
        loc_end=loc_t_end,
        gap_loc=gap_loc,
        pcd_model='e',
        segment_name="T",
        specific_peak_loc=loc_t_peak,
        specific_peak_suffix="AT_TPEAK",
        save_path=save_path,
        filename=filename,
        imShow=imShow,
        pcd_interval_name='tt'  # as per original call in StatFeatures for T-wave
    )
    all_t_wave_time_features = build_dict(all_t_wave_time_features, cdm_features)

    # Magnetic features (MFM)
    mfm_features = _extract_segment_pcd_stats(
        DATA_TEMP_Spline=DATA_TEMP_Spline,
        loc_start=loc_t_onset,
        loc_end=loc_t_end,
        gap_loc=gap_loc,
        pcd_model='m',
        segment_name="T",
        specific_peak_loc=loc_t_peak,
        specific_peak_suffix="AT_TPEAK",
        save_path=save_path,  # MFM part in original didn't pass save_path/filename to pcd_rec
        filename=filename,  # but it's good practice if pcd_rec uses them.
        imShow=False,  # Original MFM calls had imShow=False explicitly for segment, peak
        pcd_interval_name='tt'  # Assuming 'tt' is also for MFM part of T-wave
    )
    all_t_wave_time_features = build_dict(all_t_wave_time_features, mfm_features)

    return all_t_wave_time_features

# The new time_features_t_new can be removed or also refactored if it had unique logic not covered.
# For now, focusing on the original functions.


# In get_QRSwave_features.py
# Assuming necessary imports

def time_features_qrs(DATA_TEMP_Spline, signal_channels_normalized, loc_q_peak, loc_r_peak, loc_s_peak,
                      gap_loc, band='qrs', park_name='rpeak', save_path=None, filename=None, imShow=False):
    # signal_channels_unused here too.

    all_qrs_wave_time_features = {}

    # Determine the specific peak location and suffix based on band/park_name
    # This mapping was implicit in StatFeatures' _extract_qrs_family_features
    # QRS -> R-peak, QR -> Q-peak (as the 'end' of QR for pcd_rec), RS -> S-peak (as 'end')
    # The `loc_r_peak` argument to time_features_qrs is the "reference peak" for the given band.
    current_specific_peak_loc = loc_r_peak
    current_specific_peak_suffix = f"AT_{park_name.upper()}"  # e.g., AT_RPEAK, AT_QPEAK

    # Electric features (CDM)
    cdm_features = _extract_segment_pcd_stats(
        DATA_TEMP_Spline=DATA_TEMP_Spline,
        loc_start=loc_q_peak,
        loc_end=loc_s_peak,  # loc_s_peak is the end of the segment for pcd_rec iteration
        gap_loc=gap_loc,
        pcd_model='e',
        segment_name=band.upper(),  # QRS, QR, RS
        specific_peak_loc=current_specific_peak_loc,
        specific_peak_suffix=current_specific_peak_suffix,
        save_path=save_path,
        filename=filename,
        imShow=imShow,
        pcd_interval_name='qrs'  # As per original call
    )
    all_qrs_wave_time_features = build_dict(all_qrs_wave_time_features, cdm_features)

    # Magnetic features (MFM)
    mfm_features = _extract_segment_pcd_stats(
        DATA_TEMP_Spline=DATA_TEMP_Spline,
        loc_start=loc_q_peak,
        loc_end=loc_s_peak,
        gap_loc=gap_loc,
        pcd_model='m',
        segment_name=band.upper(),
        specific_peak_loc=current_specific_peak_loc,
        specific_peak_suffix=current_specific_peak_suffix,
        save_path=save_path,  # As with T-wave, being explicit
        filename=filename,
        imShow=imShow,  # Original MFM calls had imShow=imShow for QRS
        pcd_interval_name='qrs'  # Assuming 'qrs' is also for MFM part
    )
    all_qrs_wave_time_features = build_dict(all_qrs_wave_time_features, mfm_features)

    return all_qrs_wave_time_features


def hjorth_parameters_util(data_series: np.ndarray):  # Renamed to avoid conflict if original is kept
    """
    Calculates Hjorth Parameters for a single data series (e.g., one EEG channel).
    Args:
        data_series: 1D numpy array.
    Returns:
        activity, mobility, complexity
    """
    if data_series.ndim != 1 or data_series.size < 3:  # Need at least 3 points for double diff
        return np.nan, np.nan, np.nan

    activity = np.var(data_series)

    diff1 = np.diff(data_series)
    std_data = np.std(data_series)
    if std_data == 0 or diff1.size == 0:  # Avoid division by zero or issues with very short series
        mobility = np.nan
    else:
        mobility = np.std(diff1) / std_data

    diff2 = np.diff(diff1)
    std_diff1 = np.std(diff1)
    if mobility == 0 or np.isnan(mobility) or std_diff1 == 0 or diff2.size == 0:
        complexity = np.nan
    else:
        complexity = (np.std(diff2) / std_diff1) / mobility

    return activity, mobility, complexity


def calculate_eeg_features_std_names(signal_channels_normalized: np.ndarray) -> dict:
    """
    Calculates EEG (Hjorth) features with standardized names.
    Args:
        signal_channels_normalized: 2D numpy array (channels x time), normalized.
    Returns:
        Dictionary of EEG features with standardized names.
    """
    if signal_channels_normalized.ndim != 2 or signal_channels_normalized.shape[0] != 36:
        # Or handle more gracefully, this is a hardcoded assumption from original
        print("Warning: EEG features expect 36 channels. Skipping or returning empty.")
        return {}

    group_indices_map = {  # 1-based indexing from original, converted to 0-based for iloc
        1: [idx - 1 for idx in [1, 2, 3, 7, 8, 9, 13, 14, 15]],
        2: [idx - 1 for idx in [4, 5, 6, 10, 11, 12, 16, 17, 18]],
        3: [idx - 1 for idx in [19, 20, 21, 25, 26, 27, 31, 32, 33]],
        4: [idx - 1 for idx in [22, 23, 24, 28, 29, 30, 34, 35, 36]]
    }

    # Calculate Hjorth for all 36 channels first
    all_channel_hjorth_params = []
    for i in range(signal_channels_normalized.shape[0]):  # Should be 36
        # Original EEG_features took signal_channels[i:i+1, :], implying it expected 2D.
        # hjorth_parameters_util should take a 1D series.
        activity, mobility, complexity = hjorth_parameters_util(signal_channels_normalized[i, :])
        all_channel_hjorth_params.append({'activity': activity, 'mobility': mobility, 'complexity': complexity})

    df_hjorth = pd.DataFrame(all_channel_hjorth_params)

    result_dict_std_names = {}
    hjorth_metrics = ['activity', 'mobility', 'complexity']

    # Calculate mean for each group
    for group_num, indices in group_indices_map.items():
        group_df = df_hjorth.iloc[indices, :]
        mean_group_params = group_df.mean()  # This is a pandas Series

        for metric_name in hjorth_metrics:
            formatted_metric_name = f"HJORTH_{metric_name.upper()}"
            # segment="EEGGROUP", datasource="SIGNAL" or "HJORTHPARAM", metric=metric_name, group_idx=group_num
            feat_name = format_feature_name("EEG", "SIGNAL", formatted_metric_name, group_idx=group_num)
            result_dict_std_names[feat_name] = mean_group_params[metric_name]

    return result_dict_std_names


def _calculate_segment_entropy_features(
        signal_segment_data: np.ndarray,  # (channels, time_points_in_segment)
        segment_name: str,  # "T", "QRS", "QR", "RS"
        gini_window_length: int = None,  # Only if gini_coef is calculated
        shannon_bins: int = 20,  # Default from original T-wave
        shannon_range_min: float = 0,
        shannon_range_max: float = 1
):
    features = {}
    datasource = "SIGNAL"  # Entropy is from the signal itself

    if signal_segment_data is None or signal_segment_data.size == 0 or signal_segment_data.shape[1] == 0:
        print(f"Warning: Empty signal segment for {segment_name} entropy. Skipping.")
        # Optionally, pre-fill with NaNs for expected feature names
        return {}

    num_channels = signal_segment_data.shape[0]

    # 1. Shannon Entropy (per channel, then aggregated)
    shannon_entropies_per_channel = []
    for i in range(num_channels):
        channel_data = signal_segment_data[i, :]
        # Original entropy_features_t used 0, 1, 20 for shannon_entropy args.
        # This implies normalized data in [0,1] or that shannon_entropy handles range.
        # Assuming shannon_entropy(data, min_val, max_val, num_bins)
        # If data is already normalized to [-1,1] by StatFeatures.unified_normalization,
        # then shannon_range_min/max might need to be -1 and 1, or shannon_entropy needs to be robust.
        # For now, sticking to original parameters. This implies the input signal_segment_data
        # should ideally be scaled to [0,1] for this specific Shannon calculation if shannon_entropy expects it.
        # This is a discrepancy: unified_normalization gives [-1,1].
        # Let's assume shannon_entropy can handle data range or this needs adjustment.
        # A common approach is to normalize the segment data specifically for Shannon if needed:
        #   norm_channel_data = (channel_data - np.min(channel_data)) / (np.max(channel_data) - np.min(channel_data) + 1e-9)
        #   sh_entropy = shannon_entropy(norm_channel_data, 0, 1, shannon_bins)
        # For now, directly passing:
        sh_entropy = shannon_entropy(channel_data, shannon_range_min, shannon_range_max, shannon_bins)
        shannon_entropies_per_channel.append(sh_entropy)

    if shannon_entropies_per_channel:
        shannon_arr = np.array(shannon_entropies_per_channel)
        features[format_feature_name(segment_name, datasource, "SHANNON_ENTROPY", "SUM")] = np.sum(shannon_arr)
        features[format_feature_name(segment_name, datasource, "SHANNON_ENTROPY", "MAX")] = np.max(shannon_arr)
        features[format_feature_name(segment_name, datasource, "SHANNON_ENTROPY", "MIN")] = np.min(shannon_arr)
        features[format_feature_name(segment_name, datasource, "SHANNON_ENTROPY", "MEAN")] = np.mean(shannon_arr)

    # 2. Gini Coefficient (calculated over channels for the segment)
    if gini_window_length is not None and 'gini_coef' in globals():
        # gini_coef returns: mean_gini, std_gini, gini_mat
        # Original code used statistic_analysis on gini_mat
        _, _, gini_mat_per_channel = gini_coef(signal_segment_data, gini_window_length)
        if gini_mat_per_channel is not None and gini_mat_per_channel.size > 0:
            # Assuming statistic_analysis takes (data_array, base_feature_name_prefix)
            # And returns dict like {base_prefix_Mean: val, base_prefix_Std: val, ...}
            temp_base_name = f"{segment_name}_{datasource}_GINI_COEFFICIENT"
            gini_stats = statistic_analysis(gini_mat_per_channel,
                                            temp_base_name)  # Applied to array of Gini coeffs (one per channel)
            for key_from_stat_func, value in gini_stats.items():
                stat_type = key_from_stat_func.replace(temp_base_name + '_', '')
                features[format_feature_name(segment_name, datasource, "GINI_COEFFICIENT", stat_type.upper())] = value

    # 3. SVD Entropy (single value for the segment_data matrix)
    if 'svd_entropy' in globals():
        svd_entr_val = svd_entropy(signal_segment_data)
        features[format_feature_name(segment_name, datasource, "SVD_ENTROPY", "VALUE")] = svd_entr_val

    return features


# New entropy functions in stats_utils.py, replacing old ones
def calculate_t_wave_entropy_features(
        signal_channels_normalized: np.ndarray,
        loc_t_onset: int, loc_t_peak: int, loc_t_end: int,
        t_gini_window_length: int):
    if loc_t_onset is None or loc_t_end is None or loc_t_onset >= loc_t_end:
        print("Warning: Invalid T-wave segment for entropy. Skipping.")
        return {}
    t_wave_segment = signal_channels_normalized[:, loc_t_onset: loc_t_end + 1]
    return _calculate_segment_entropy_features(
        signal_segment_data=t_wave_segment,
        segment_name="T",
        gini_window_length=t_gini_window_length
        # Shannon params use defaults of helper
    )


def calculate_qrs_wave_entropy_features(
        signal_channels_normalized: np.ndarray,
        loc_q_peak: int, loc_r_peak: int, loc_s_peak: int,
        qrs_gini_window_length: int,
        band: str = 'qrs'  # 'qrs', 'qr', 'rs'
):
    if loc_q_peak is None or loc_s_peak is None or loc_q_peak >= loc_s_peak:
        print(f"Warning: Invalid {band.upper()} segment for entropy. Skipping.")
        return {}
    qrs_wave_segment = signal_channels_normalized[:, loc_q_peak: loc_s_peak + 1]
    return _calculate_segment_entropy_features(
        signal_segment_data=qrs_wave_segment,
        segment_name=band.upper(),
        gini_window_length=qrs_gini_window_length
    )


def _extract_channel_wavelet_features(channel_data: np.ndarray, wavelet_name: str = 'db4', decomp_level=None):
    """ Helper to get wavelet coefficients and basic features for ONE channel """
    epsilon = 1e-10  # from original
    channel_data_proc = channel_data + epsilon

    # pywt.wavedec default level is chosen automatically if None
    coeffs = pywt.wavedec(channel_data_proc, wavelet_name, level=decomp_level)

    # Original code seems to focus on cA and first 4 cD levels if available
    # cA is coeffs[0], cDs are coeffs[1:]
    # Let's target cA (approx) and cD1, cD2, cD3, cD4

    processed_coeffs = {'cA': coeffs[0]}
    for i, cD in enumerate(coeffs[1:]):
        if i < 4:  # Max cD4
            processed_coeffs[f'cD{i + 1}'] = cD
        else:
            break

    chan_wavelet_features = {}

    for coeff_label, coeff_array in processed_coeffs.items():
        if coeff_array is None: continue  # Should not happen if decomp_level is reasonable

        # Reconstruct signal from this specific coefficient band to calculate power
        # To reconstruct from a specific detail level 'k', use [None]*k + [coeff_array] + [None]*(len(coeffs)-1-k)
        # For cA (coeffs[0]): [coeff_array] + [None]*(len(coeffs)-1)
        # For cD_k (coeffs[k]): [None]*k + [coeff_array] + [None]*(len(coeffs)-1-k)
        # This is tricky to generalize perfectly for xrec if not careful.
        # Original code: xrec = pywt.waverec([coeff] + [None] * (len(coeffs) - idx), 'db4')
        # This assumes 'coeff' is from a specific position 'idx' in the original list.
        # A simpler approach for power might be needed if xrec is too complex or if
        # the "power" was meant to be related to energy of the coefficient itself.
        # The original code is a bit ambiguous here.
        # Power = np.sqrt(np.sum(xrec**2))
        # Ratio = power / np.sqrt(np.sum(t_wave_channel**2))
        # For now, let's focus on energy, skew, kurtosis of coefficients directly, as power/ratio seem complex to replicate safely without exact xrec logic.
        # The original `extract_normalized_features` was applied to lists of these values across channels.

        energy = np.sum(coeff_array ** 2)
        # power_val = ... # Requires careful xrec, deferring for now or simplifying
        # ratio_val = ... # Requires power_val, deferring
        skew_val = skew(coeff_array) if coeff_array.size > 0 else np.nan
        kurt_val = kurtosis(coeff_array) if coeff_array.size > 0 else np.nan  # Fisher kurtosis (normal=0)

        chan_wavelet_features[f"{coeff_label}_ENERGY"] = energy
        chan_wavelet_features[f"{coeff_label}_SKEWNESS"] = skew_val
        chan_wavelet_features[f"{coeff_label}_KURTOSIS"] = kurt_val

    return chan_wavelet_features


def _calculate_segment_frequency_features(
        signal_segment_data: np.ndarray,  # (channels, time_points_in_segment)
        segment_name: str  # "T", "QRS", "QR", "RS"
):
    features = {}
    datasource = "SIGNAL_WAVELET_DB4"  # Be specific about wavelet

    if signal_segment_data is None or signal_segment_data.size == 0 or signal_segment_data.shape[1] == 0:
        print(f"Warning: Empty signal segment for {segment_name} frequency. Skipping.")
        return {}

    num_channels = signal_segment_data.shape[0]

    # Store features per channel, then aggregate:
    # e.g., all_cA_energies = [energy_ch1_cA, energy_ch2_cA, ...]
    # The original 'extract_normalized_features' took a list of values (one per channel for a given metric like 'energy')
    # and then calculated mean/max/min of the normalized version of that list.

    # Let's collect all per-channel metrics first
    # This will be a list of dicts, where each dict is from _extract_channel_wavelet_features
    all_channels_raw_wavelet_metrics = []
    for i in range(num_channels):
        chan_data = signal_segment_data[i, :]
        # Assuming decomp_level=4 (like A4/D4) based on original "a4" in feature names
        # pywt.wavedec with level=4 gives 5 sets of coeffs: cA4, cD4, cD3, cD2, cD1
        # The original func_frequency_feature_qrs processed cA, cD1, cD2, cD3, cD4.
        # If 'db4' and target is A4/D4, level should be 4.
        # coeffs = pywt.wavedec(t_wave_channel, 'db4', level=4) for A4, D4, D3, D2, D1
        # labels cA, cD1, cD2, cD3, cD4 in original might map to cA4, cD4, cD3, cD2, cD1
        chan_metrics = _extract_channel_wavelet_features(chan_data, wavelet_name='db4', decomp_level=4)
        all_channels_raw_wavelet_metrics.append(chan_metrics)

    if not all_channels_raw_wavelet_metrics: return {}

    # Now, aggregate these. For each metric (e.g., "cA_ENERGY"), gather values from all channels
    # Then apply the normalization and stats (mean, min, max of normalized values)

    # Get all unique metric keys from the first channel (assuming all channels produce same keys)
    sample_metric_keys = all_channels_raw_wavelet_metrics[0].keys()

    for metric_key_suffix in sample_metric_keys:  # e.g., "cA_ENERGY", "cD1_SKEWNESS"
        metric_values_across_channels = [chan_metrics[metric_key_suffix] for chan_metrics in
                                         all_channels_raw_wavelet_metrics]
        metric_values_arr = np.array(metric_values_across_channels)

        if metric_values_arr.size == 0: continue

        # Apply the normalization logic from the original 'extract_normalized_features'
        # data_sorted = np.sort(data)
        # min_value = np.percentile(data_sorted, 10)-0.000001 # Slight offset from original
        # max_value = np.percentile(data_sorted, 90)+0.000001 # Slight offset
        # normalized_data = (data - min_value) / (max_value - min_value)
        # This normalization seems specific. Let's encapsulate it.

        if np.all(np.isnan(metric_values_arr)):  # if all are nan, skip
            norm_data_stats = {'MEAN': np.nan, 'MAX': np.nan, 'MIN': np.nan}
        elif len(metric_values_arr) < 2:  # Percentile may fail or be meaningless
            # For single value, normalized value is tricky. Let's treat as unnormalized mean/max/min.
            norm_data_stats = {'MEAN': np.nanmean(metric_values_arr),
                               'MAX': np.nanmax(metric_values_arr) if not np.all(
                                   np.isnan(metric_values_arr)) else np.nan,
                               'MIN': np.nanmin(metric_values_arr) if not np.all(
                                   np.isnan(metric_values_arr)) else np.nan}
        else:
            data_no_nan = metric_values_arr[~np.isnan(metric_values_arr)]
            if data_no_nan.size < 2:  # Not enough non-NaN for percentile
                norm_data_stats = {'MEAN': np.nanmean(metric_values_arr),
                                   'MAX': np.nanmax(metric_values_arr) if not np.all(
                                       np.isnan(metric_values_arr)) else np.nan,
                                   'MIN': np.nanmin(metric_values_arr) if not np.all(
                                       np.isnan(metric_values_arr)) else np.nan}
            else:
                min_val_perc = np.percentile(data_no_nan, 10)
                max_val_perc = np.percentile(data_no_nan, 90)

                # Avoid division by zero if P10 and P90 are too close
                denominator = max_val_perc - min_val_perc
                if np.abs(denominator) < 1e-9:  # Effectively zero range for normalization
                    # if all values are same, normalized is 0 or 0.5. Here, maybe treat as unnormalized stats or fixed val.
                    # Let's output stats of the original data if range is zero
                    normalized_metric_values = np.full_like(metric_values_arr, 0.5,
                                                            dtype=float) if denominator == 0 else (
                                                                                                              metric_values_arr - min_val_perc) / (
                                                                                                              denominator + 1e-9)  # Fallback if still needed

                else:
                    # Apply original offset logic carefully
                    min_norm_bound = min_val_perc - 1e-6 if 'ENERGY' in metric_key_suffix.upper() or 'POWER' in metric_key_suffix.upper() else min_val_perc  # Original had epsilon only for QRS one
                    max_norm_bound = max_val_perc + 1e-6 if 'ENERGY' in metric_key_suffix.upper() or 'POWER' in metric_key_suffix.upper() else max_val_perc

                    # Recalculate denominator with bounds
                    denominator_eff = max_norm_bound - min_norm_bound
                    if np.abs(denominator_eff) < 1e-9:
                        normalized_metric_values = np.full_like(metric_values_arr, 0.5, dtype=float)
                    else:
                        normalized_metric_values = (metric_values_arr - min_norm_bound) / denominator_eff

                # Clip to handle outliers outside P10-P90 range if normalization expands them beyond [0,1]
                # This wasn't in original but good practice for percentile normalization.
                # normalized_metric_values = np.clip(normalized_metric_values, 0, 1) # Optional

                norm_data_stats = {
                    'MEAN': np.nanmean(normalized_metric_values),
                    'MAX': np.nanmax(normalized_metric_values) if not np.all(
                        np.isnan(normalized_metric_values)) else np.nan,
                    'MIN': np.nanmin(normalized_metric_values) if not np.all(
                        np.isnan(normalized_metric_values)) else np.nan,
                }

        # Store these stats with formatted names
        # metric_key_suffix is like "cA_ENERGY". We need to make it "CA_ENERGY" for format_feature_name
        formatted_metric_core = metric_key_suffix.upper()  # e.g. CA_ENERGY
        for stat_name, stat_val in norm_data_stats.items():
            # Example: STATS_T_SIGNAL_WAVELET_DB4_CA_ENERGY_MEAN
            features[format_feature_name(segment_name, datasource, formatted_metric_core, stat_name)] = stat_val

    return features


# New frequency functions in stats_utils.py
def calculate_t_wave_frequency_features(
        signal_channels_normalized: np.ndarray,
        loc_t_onset: int, loc_t_peak: int, loc_t_end: int
):
    if loc_t_onset is None or loc_t_end is None or loc_t_onset >= loc_t_end:
        print("Warning: Invalid T-wave segment for frequency. Skipping.")
        return {}
    t_wave_segment = signal_channels_normalized[:, loc_t_onset: loc_t_end + 1]
    return _calculate_segment_frequency_features(t_wave_segment, "T")


def calculate_qrs_wave_frequency_features(
        signal_channels_normalized: np.ndarray,
        loc_q_peak: int, loc_r_peak: int, loc_s_peak: int,
        band: str = 'qrs'
):
    if loc_q_peak is None or loc_s_peak is None or loc_q_peak >= loc_s_peak:
        print(f"Warning: Invalid {band.upper()} segment for frequency. Skipping.")
        return {}
    qrs_wave_segment = signal_channels_normalized[:, loc_q_peak: loc_s_peak + 1]
    return _calculate_segment_frequency_features(qrs_wave_segment, band.upper())