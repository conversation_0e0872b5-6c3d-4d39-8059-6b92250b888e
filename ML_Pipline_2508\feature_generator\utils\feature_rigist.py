'''
@Project ：ML_Pipline 
@File    ：feature_rigist.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/8/15 上午9:04 
@Discribe：
    特征注册器各测试版本存档
'''

import json
import time
from typing import List, Set, Dict, Callable

import pandas as pd


class FeatureRegistry:
    """
    特征注册器，复杂相应的特征与函数映射关系的管理
    """
    _features = {}
    _feature_groups = {}
    # todo
    _feature_subgroups = {}
    _feature_tags = {}

    is_loaded = False

    @classmethod
    def register(cls, name: str):
        cls._features[name] = None  # 初始时不知道具体特征

    @classmethod
    def update_features(cls, name: str, features: List[str]):
        cls._features[name] = features
        for feature in features:
            if feature not in cls._feature_groups:
                cls._feature_groups[feature] = set()
            cls._feature_groups[feature].add(name)

    @classmethod
    def get_groups_for_features(cls, features: List[str]) -> Set[str]:
        """根据特征获取生成这些特征的函数名"""
        return set.union(*(cls._feature_groups.get(feature, set()) for feature in features))

    @classmethod
    def get_density_for_features(cls, features: List[str]) -> Dict[str, int]:
        group_density = {}
        for feature in features:
            groups = cls._feature_groups.get(feature, set())
            for group in groups:
                if group in group_density:
                    group_density[group] += 1
                else:
                    group_density[group] = 1
        return group_density

    @classmethod
    def filter_features_for_function(cls, func_name: str) -> List[str]:
        """根据func_name从全特征集中筛选出特定列"""
        if not cls._features:
            raise ValueError("All features must be loaded before filtering.")
        if func_name not in cls._features:
            raise ValueError(f"No features registered for function: {func_name}")
        func_features = cls._features[func_name]
        return func_features

    @classmethod
    def save_to_file(cls, filename: str):
        data = {
            "features": cls._features,
            "feature_groups": {k: list(v) for k, v in cls._feature_groups.items()}
        }
        with open(filename, 'w') as f:
            json.dump(data, f)

    @classmethod
    def load_from_file(cls, filename: str):
        if not cls.is_loaded:
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                cls._features = data["features"]
                cls._feature_groups = {k: set(v) for k, v in data["feature_groups"].items()}
                cls.is_loaded = True

                print("加载特征注册器文件成功.")
                return True
            except FileNotFoundError as e:
                print("无法加载特征注册器文件，将直接创建特征注册器...")
                return False
    @classmethod
    def reload_from_file(cls, filename: str):
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
            cls._features = data["features"]
            cls._feature_groups = {k: set(v) for k, v in data["feature_groups"].items()}
            cls.is_loaded = True

            print("加载特征注册器文件成功.")
            return True
        except FileNotFoundError as e:
            print("无法加载特征注册器文件，将直接创建特征注册器...")
            return False
    @property
    def get_all_features(cls):
        return cls._features_groups

def feature_regis():
    def decorator(func):
        func_name = func.__name__
        FeatureRegistry.register(func_name)
        return func

    return decorator
# @feature_regis_v3(features=["feature1", "feature2"], parent="process_file")

class FeatureRegistry_v2:
    _features = {}
    _feature_hierarchy = {}
    _is_loaded = False

    @classmethod
    def register(cls, name: str, features: List[str], parent: str = None, tags: List[str] = None):
        cls._features[name] = {
            'features': set(features),
            'parent': parent,
            'tags': set(tags or [])
        }
        if parent:
            if parent not in cls._feature_hierarchy:
                cls._feature_hierarchy[parent] = set()
            cls._feature_hierarchy[parent].add(name)

    @classmethod
    def get_groups_for_features(cls, features: List[str]) -> Set[str]:
        needed_groups = set()
        for feature in features:
            for group, info in cls._features.items():
                if feature in info['features']:
                    needed_groups.add(group)
                    # 添加所有父级组
                    parent = info['parent']
                    while parent:
                        needed_groups.add(parent)
                        parent = cls._features.get(parent, {}).get('parent')
        return needed_groups

    @classmethod
    def get_features_by_tag(cls, tag: str) -> Set[str]:
        return {name for name, info in cls._features.items() if tag in info['tags']}

    @classmethod
    def save_to_file(cls, filename: str):
        data = {
            "features": {k: {**v, 'features': list(v['features']), 'tags': list(v['tags'])}
                         for k, v in cls._features.items()},
            "feature_hierarchy": {k: list(v) for k, v in cls._feature_hierarchy.items()}
        }
        with open(filename, 'w') as f:
            json.dump(data, f)

    @classmethod
    def load_from_file(cls, filename: str):
        if not cls._is_loaded:
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                cls._features = {k: {**v, 'features': set(v['features']), 'tags': set(v['tags'])}
                                 for k, v in data["features"].items()}
                cls._feature_hierarchy = {k: set(v) for k, v in data["feature_hierarchy"].items()}
                cls._is_loaded = True
                print("加载特征注册器文件成功.")
            except:
                print("无法加载特征注册器文件，将直接创建特征注册器...")


def feature_regis_v2(features: List[str], parent: str = None, tags: List[str] = None):
    def decorator(func):
        FeatureRegistry_v2.register(func.__name__, features, parent, tags)
        return func
    return decorator





class FeatureRegistry_v4:
    _features = {}
    _feature_hierarchy = {}
    _is_loaded = False

    @classmethod
    def register(cls, name: str, features: List[str], parent: str = None, tags: List[str] = None):
        cls._features[name] = {
            'features': set(features),
            'parent': parent,
            'tags': set(tags or [])
        }
        if parent:
            if parent not in cls._feature_hierarchy:
                cls._feature_hierarchy[parent] = set()
            cls._feature_hierarchy[parent].add(name)

    @classmethod
    def get_minimal_groups_for_features(cls, features: List[str]) -> Set[str]:
        needed_features = set(features)
        needed_groups = set()
        visited = set()

        def dfs(group):
            if group in visited:
                return set()
            visited.add(group)
            group_features = cls._features[group]['features']
            remaining_features = needed_features - group_features
            if not remaining_features:
                return {group}

            minimal_groups = {group}
            for child in cls._feature_hierarchy.get(group, []):
                minimal_groups.update(dfs(child))

            return minimal_groups

        # 从叶子节点开始搜索
        leaf_nodes = set(cls._features.keys()) - set(cls._feature_hierarchy.keys())
        for leaf in leaf_nodes:
            needed_groups.update(dfs(leaf))

        # 如果还有未满足的特征，继续向上搜索
        while needed_features:
            for group in list(needed_groups):
                parent = cls._features[group]['parent']
                if parent:
                    needed_groups.update(dfs(parent))
            # 更新未满足的特征
            satisfied_features = set().union(*(cls._features[group]['features'] for group in needed_groups))
            needed_features -= satisfied_features
            if not needed_features:
                break

        return needed_groups

    @classmethod
    def get_features_by_tag(cls, tag: str) -> Set[str]:
        return {name for name, info in cls._features.items() if tag in info['tags']}

    @classmethod
    def save_to_file(cls, filename: str):
        data = {
            "features": {k: {**v, 'features': list(v['features']), 'tags': list(v['tags'])}
                         for k, v in cls._features.items()},
            "feature_hierarchy": {k: list(v) for k, v in cls._feature_hierarchy.items()}
        }
        with open(filename, 'w') as f:
            json.dump(data, f)

    @classmethod
    def load_from_file(cls, filename: str):
        if not cls._is_loaded:
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                cls._features = {k: {**v, 'features': set(v['features']), 'tags': set(v['tags'])}
                                 for k, v in data["features"].items()}
                cls._feature_hierarchy = {k: set(v) for k, v in data["feature_hierarchy"].items()}
                cls._is_loaded = True
                print("加载特征注册器文件成功.")
            except:
                print("无法加载特征注册器文件，将直接创建特征注册器...")


def feature_regis_v4(features: List[str], parent: str = None, tags: List[str] = None):
    def decorator(func):
        FeatureRegistry_v4.register(func.__name__, features, parent, tags)
        return func

    return decorator
# # 示例用法
# @feature_regis_v4(features=["subfeature1_1", "subfeature1_2"])
# def extract_subfeature_1_1(basic_args_dict, label):
#     # 实现子特征提取逻辑
#     return pd.DataFrame(...)
#
# @feature_regis_v4(features=["feature1", "feature2"], parent="extract_subfeature_1_1")
# def extract_feature_group_1(basic_args_dict, label):
#     # 实现特征提取逻辑
#     return pd.DataFrame(...)

class FeatureGenerator_v2:
    def __init__(self):
        self.file_label_tuple = None
        self.basic_args_dict = None
        self.feature_cache = pd.DataFrame()
        self.generated_features = set()
        self.feature_functions: Dict[str, Callable] = {}

    def register_function(self, func: Callable):
        self.feature_functions[func.__name__] = func

    @feature_regis_v4(features=["basic_feature1", "basic_feature2"])
    def process_file(self, file_label_tuple):
        # 实现文件处理逻辑
        pass

    def get_needed_v2(self, feature_names: List[str], file_name=None) :
        if file_name:
            self.file_label_tuple = (file_name, 0)

        # 获取基础参数
        if self.basic_args_dict is None:
            self.basic_args_dict, _ = self.process_file(self.file_label_tuple)
        _, label = self.file_label_tuple

        # 确定需要生成的特征
        needed_features = set(feature_names)
        missing_features = needed_features - self.generated_features

        if missing_features:
            groups_to_call = FeatureRegistry_v4.get_minimal_groups_for_features(list(missing_features))
            for group_name in groups_to_call:
                feature_func = self.feature_functions.get(group_name) or getattr(self, group_name, None)
                if feature_func:
                    t1 = time.time()
                    group_results = feature_func(self.basic_args_dict, label)
                    print(f"{group_name}：{time.time() - t1}")
                    # 更新缓存
                    new_features = [col for col in group_results.columns if col not in self.generated_features]
                    self.feature_cache = pd.concat([self.feature_cache, group_results[new_features]], axis=1)
                    self.generated_features.update(new_features)

        # 从缓存中获取所需特征
        supported_features = list(needed_features.intersection(self.generated_features))
        unsupported_features = list(needed_features - set(supported_features))

        if supported_features:
            final_df = self.feature_cache[supported_features].copy()
            if 'MCG_ID' in final_df.columns:
                final_df['mcg_file'] = final_df['MCG_ID']
                final_df = final_df.drop(columns=['MCG_ID'])
            final_df['mcg_file'] = self.file_label_tuple[0]
            final_df['label'] = label
            print("已完成处理文件:", self.basic_args_dict['file_name'])
            return final_df, unsupported_features
        else:
            print("提取器不支持所需特征：", feature_names)
            return pd.DataFrame(), feature_names

# # 使用示例
# fg = FeatureGenerator_v2()
# fg.register_function(extract_subfeature_1_1)
# fg.register_function(extract_feature_group_1)
#
# result_df, unsupported = fg.get_needed_v2(["feature1", "subfeature1_1"], "example_file.txt")