import numpy as np
from scipy.linalg import solve_banded

import feature_generator.utils.time_locs1 as time_locs1


def cubic_splines(x, ys):
    """
    三次样条插值函数
    :param x: 输入数组 X
    :param ys: 输入数组 Y
    :return z: 数据点处的二阶导数
    """
    ys = np.reshape(ys, (-1, 6))
    dy = np.diff(ys, axis=-1)
    dx = np.diff(x)

    n = x.shape[0]
    # 创建一个三对角矩阵结构
    ab = np.zeros((3, n))  # ab为三对角矩阵，中、下、上三行
    ab[1, 0] = ab[1, -1] = 1
    ab[1, 1:-1] = 2 * (dx[:-1] + dx[1:])
    ab[0, 2:] = dx[1:]
    ab[2, :-2] = dx[:-1]

    B = np.zeros((ys.shape[0], n))
    B[:, 1:-1] = 6 * ((dy[:, 1:] / dx[1:]) - (dy[:, :-1] / dx[:-1]))

    zs = solve_banded((1, 1), ab, B.T).T  # 使用专门的三对角矩阵求解器
    return zs


def interpolates(x, y, z, x_new):
    """
    插值函数
    :param x: 输入数组 X
    :param y: 输入数组 Y
    :param z: 数据点处的二阶导数
    :param x_new: 要求插值的点
    :return y_new : 对应的插值值
    """
    n = x.shape[0]
    h = np.diff(x)
    idx = np.searchsorted(x, x_new) - 1
    idx = np.clip(idx, 0, n - 2)

    A = (x[idx + 1] - x_new) / h[idx]
    B = 1 - A
    C = (1 / 6) * (A ** 3 - A) * h[idx] ** 2
    D = (1 / 6) * (B ** 3 - B) * h[idx] ** 2
    y_new = A * y[:, idx] + B * y[:, idx + 1] + C * z[:, idx] + D * z[:, idx + 1]
    return y_new


def mcg_interpolate(d_ori):
    X = np.array([0, 4, 8, 12, 16, 20])
    Y = np.array([0, 4, 8, 12, 16, 20])
    values = np.linspace(0, 20, 100 + 1)[1:]
    Xi = np.tile(values, (100, 1))
    Yi = Xi.T
    Bs = d_ori.reshape((-1, 6, 6))

    Zi = np.zeros((Bs.shape[0], Xi.shape[0], Xi.shape[1]))
    z_x = np.array([cubic_splines(X, Bs[:, i, :]) for i in range(Bs.shape[1])])
    z_y = np.zeros((Bs.shape[0], 6, 100))

    for i in range(Xi.shape[0]):
        for k in range(Bs.shape[1]):
            z_y[:, k, :] = interpolates(X, Bs[:, k, :], z_x[k], Xi[i, :])

        for j in range(Xi.shape[1]):
            Zi[:, i, j] = interpolates(Y, z_y[:, :, j], cubic_splines(Y, z_y[:, :, j]), Yi[i, j])

    return Zi


def calculate_interpolation(file_p, signal_df, peak_locs=None):
    if not peak_locs:
        peak_locs = time_locs1.time_point(file_p)
    start = peak_locs['p-head']
    end = max(peak_locs['t-end'],peak_locs['t-r'])

    inspect_data = [np.zeros((100,100)) for _ in range(signal_df.shape[0])]

    signal_df = np.array(signal_df.iloc[start:end, 1:])
    Zi = mcg_interpolate(signal_df)

    # inspect_data = [0] * signal_df.shape[0]
    for x in range(len(Zi)):
        inspect_data[x + start] = Zi[x]
        # len(inspect_data)

    return inspect_data


if __name__ == '__main__':
    # profiler = cProfile.Profile()
    #
    pass