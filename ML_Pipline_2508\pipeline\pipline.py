"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_deployment_test
date: 2024/11/26 下午4:53
desc:
"""

import pandas as pd
import numpy as np
import pickle
from typing import Tuple, Dict, Union, Optional
from feature_generator.main_feature_generator import FeatureGenerator
from model_trainer.models import run_test_model
import sys
from contextlib import contextmanager
import os
import io


class MLPipeline:
    """
    Machine Learning Pipeline for feature processing and model inference.
    """

    HOSPITAL_MAPPING = {
        '上海中山': 0,
        '上海六院': 1,
        '上海十院': 2,
        '北京301': 3,
        '安医': 4,
        '广东南海': 5,
    }

    def __init__(self):
        self.columns_to_drop = [
            'filename_1', 'filename_2', 'filename_3',
            'filename_4', 'label_x', 'label_y'
        ]

    def load_model(self, model_path: str, fold: int) -> Tuple[object, float]:
        """
        Load the trained model and its threshold from a pickle file.

        Args:
            model_path: Path to the saved model file
            fold: Fold number to select from the model

        Returns:
            Tuple of (model, threshold)
        """
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)

        model = model_data['models_trained'][fold]
        threshold = model_data['results'][fold]['valid_result_threshold']['threshold']
        return model, threshold

    @staticmethod
    def _sigmoid(x: np.ndarray) -> np.ndarray:
        """
        Apply sigmoid function to numpy array with numerical stability.
        """
        mask = x >= 0
        result = np.zeros_like(x, dtype=np.float64)
        exp_nx = np.exp(-x[mask])
        result[mask] = 1 / (1 + exp_nx)
        exp_x = np.exp(x[~mask])
        result[~mask] = exp_x / (1 + exp_x)
        return result

    def predict(self, model_path: str, fold: int, test_data: pd.DataFrame) -> pd.DataFrame:
        """
        Make predictions using the loaded model(s).

        Args:
            model_path: Path to the saved model file
            fold: Fold number to use (-1 for ensemble prediction using all folds)
            test_data: DataFrame containing test features

        Returns:
            DataFrame with predictions
        """
        # Prepare features
        x_test = test_data.drop(columns=['mcg_file', 'label'], inplace=False)
        y_test = test_data['label']
        if model_path is None:
            print("No model path provided. Returning empty predictions.")
            results = test_data[['mcg_file', 'label']]
            results['pred'] = 0
            results['prob'] = 0
            return results.drop(columns=['label'], inplace=False)
        if fold != -1:
            # Single model prediction (original functionality)
            model, threshold = self.load_model(model_path, fold)
            y_prob, _, _ = run_test_model(
                model, x_test, y_test,
                verbose=0, dataset='test',
                threshold=threshold
            )

            # Convert probabilities to predictions
            y_pred = np.where(y_prob >= threshold, 1, 0)

            # Adjust probabilities if needed
            if (y_prob < 0).any() or (y_prob > 1).any():
                y_prob = self._sigmoid(y_prob)
                y_prob = np.clip(y_prob, 1e-6, 1 - 1e-6)

        else:
            # Ensemble prediction using all folds
            all_predictions = []
            all_probabilities = []

            # Get predictions from all 5 folds
            for fold_idx in range(5):
                model, threshold = self.load_model(model_path, fold_idx)
                y_prob, _, _ = run_test_model(
                    model, x_test, y_test,
                    verbose=0, dataset='test',
                    threshold=threshold
                )

                # Adjust probabilities if needed
                if (y_prob < 0).any() or (y_prob > 1).any():
                    y_prob = self._sigmoid(y_prob)
                    y_prob = np.clip(y_prob, 1e-6, 1 - 1e-6)

                # Convert to binary predictions
                y_pred = np.where(y_prob >= threshold, 1, 0)

                all_predictions.append(y_pred)
                all_probabilities.append(y_prob)

            # Stack predictions and probabilities
            stacked_preds = np.stack(all_predictions, axis=0)
            stacked_probs = np.stack(all_probabilities, axis=0)

            # Calculate votes (sum along fold axis)
            votes = np.sum(stacked_preds, axis=0)

            # Final prediction: 1 if majority (>2 votes), 0 otherwise
            y_pred = np.where(votes >= 2, 1, 0)

            # Average probabilities across folds
            y_prob = np.mean(stacked_probs, axis=0)

        # Prepare results
        results = test_data[['mcg_file', 'label']]
        results['pred'] = y_pred
        results['prob'] = y_prob

        return results.drop(columns=['label'], inplace=False)

    def select_features(self, features_df: pd.DataFrame, selected_features_path: str) -> pd.DataFrame:
        """
        Select and process features based on a saved feature set.

        Args:
            features_df: DataFrame containing all features
            selected_features_path: Path to saved selected features file

        Returns:
            DataFrame with selected features
        """
        with open(selected_features_path, "rb") as f:
            selected_features_df = pickle.load(f)

        # Get feature columns
        feature_columns = selected_features_df.columns.tolist()
        if 'mcg_file' not in selected_features_df.columns:
            feature_columns.append('mcg_file')

        features_df = features_df[feature_columns]

        # Convert numeric columns to float32
        features_df = features_df.drop(columns=self.columns_to_drop, errors='ignore')
        numeric_columns = features_df.drop(columns=['mcg_file', 'label'], errors='ignore')

        # Validate numeric columns
        non_numeric = numeric_columns.select_dtypes(exclude=[np.number])
        if not non_numeric.empty:
            raise ValueError(f'Feature columns {non_numeric.columns} are not numeric type')

        # Convert appropriate columns to float32
        columns_to_convert = numeric_columns.select_dtypes(
            include=[np.number],
            exclude=[np.float32]
        ).columns
        features_df[columns_to_convert] = features_df[columns_to_convert].astype(np.float32)

        return features_df

    @staticmethod
    def generate_features(
            data: Optional[np.ndarray] = None,
            file_label_tuple: Tuple[str, int] = ('GDNH_2023_000391', 0)
    ) -> pd.DataFrame:
        """
        Generate features using the FeatureGenerator.

        Args:
            data: Optional numpy array of raw data
            file_label_tuple: Tuple of (file_name, label)

        Returns:
            DataFrame containing generated features
        """
        generator = FeatureGenerator(file_label_tuple=file_label_tuple, remote=False)
        return generator.get_all(data)

    def process_clinic_features(
            self,
            features_df: pd.DataFrame,
            clinic_data: Dict[str, Union[str, int, float, None]]
    ) -> pd.DataFrame:
        """
        Process and add clinical features to the feature DataFrame.

        Args:
            features_df: DataFrame containing existing features
            clinic_data: Dictionary of clinical data

        Returns:
            DataFrame with added clinical features
        """
        default_values = {
            'clinic_height': np.nan,
            'clinic_weight': np.nan,
            'clinic_gender': np.nan,
            'clinic_age': np.nan,
            'clinic_smoking': np.nan,
            'clinic_drinking': np.nan,
            'clinic_hypertension': np.nan,
            'clinic_hyperlipidemia': np.nan,
            'clinic_intervention': np.nan,
            'clinic_hospital': np.nan,
            'clinic_diabetes': np.nan,
            'clinic_symp': np.nan
        }

        # Update with provided values
        for key, value in clinic_data.items():
            default_values[key] = value if value is not None else np.nan

        # Process height
        height = default_values['clinic_height']
        if pd.notna(height):
            if isinstance(height, (int, float, np.integer, np.floating)) and height > 0:
                default_values['clinic_height'] = float(height)
            else:
                default_values['clinic_height'] = -1

        # Process weight
        weight = default_values['clinic_weight']
        if pd.notna(weight):
            if isinstance(weight, (int, float, np.integer, np.floating)) and weight > 0:
                default_values['clinic_weight'] = float(weight)
            else:
                default_values['clinic_weight'] = -1

        # Process gender
        gender = default_values['clinic_gender']
        if pd.notna(gender):
            if isinstance(gender, str) and gender in ['男', '女']:
                default_values['clinic_gender'] = 1 if gender == '男' else 0
            elif gender in [0, 1]:
                default_values['clinic_gender'] = int(gender)
            else:
                default_values['clinic_gender'] = -1

        # Process age
        age = default_values['clinic_age']
        if pd.notna(age):
            if isinstance(age, (int, float, np.integer, np.floating)) and age > 0:
                default_values['clinic_age'] = float(age)
            else:
                default_values['clinic_age'] = -1

        # Process smoking
        smoking = default_values['clinic_smoking']
        if pd.notna(smoking):
            if isinstance(smoking, (int, float, np.integer, np.floating)):
                default_values['clinic_smoking'] = float(smoking)
            else:
                default_values['clinic_smoking'] = -1

        # Process drinking
        drinking = default_values['clinic_drinking']
        if pd.notna(drinking):
            if isinstance(drinking, (int, float, np.integer, np.floating)):
                default_values['clinic_drinking'] = float(drinking)
            else:
                default_values['clinic_drinking'] = -1

        # Process hypertension
        hypertension = default_values['clinic_hypertension']
        if pd.notna(hypertension):
            if isinstance(hypertension, (int, float, np.integer, np.floating)):
                default_values['clinic_hypertension'] = float(hypertension)
            else:
                default_values['clinic_hypertension'] = -1

        # Process hyperlipidemia
        hyperlipidemia = default_values['clinic_hyperlipidemia']
        if pd.notna(hyperlipidemia):
            if isinstance(hyperlipidemia, (int, float, np.integer, np.floating)):
                default_values['clinic_hyperlipidemia'] = float(hyperlipidemia)
            else:
                default_values['clinic_hyperlipidemia'] = -1

        # Process intervention
        intervention = default_values['clinic_intervention']
        if pd.notna(intervention):
            if isinstance(intervention, (int, float, np.integer, np.floating)):
                default_values['clinic_intervention'] = float(intervention)
            else:
                default_values['clinic_intervention'] = -1

        # Process hospital
        hospital = default_values['clinic_hospital']
        if pd.notna(hospital):
            if isinstance(hospital, str) and hospital in self.HOSPITAL_MAPPING:
                default_values['clinic_hospital'] = self.HOSPITAL_MAPPING[hospital]
            elif isinstance(hospital, int) and hospital in self.HOSPITAL_MAPPING.values():
                default_values['clinic_hospital'] = hospital
            else:
                default_values['clinic_hospital'] = -1

        # Process diabetes
        diabetes = default_values['clinic_diabetes']
        if pd.notna(diabetes):
            if isinstance(diabetes, (int, float, np.integer, np.floating)):
                default_values['clinic_diabetes'] = float(diabetes)
            else:
                default_values['clinic_diabetes'] = -1

        # Process symp
        symp = default_values['clinic_symp']
        if pd.notna(symp):
            if isinstance(symp, (int, float, np.integer, np.floating)):
                default_values['clinic_symp'] = float(symp)
            else:
                default_values['clinic_symp'] = -1

        # Add processed values to DataFrame
        for key, value in default_values.items():
            features_df[key] = value

        return features_df


def predict_integrated(
        data: Union[np.ndarray,None],
        clinic_data: Dict[str, Union[str, int, float, None]],
        model_path: str,
        selected_features_path: str,
        fold: int = 1,
        file_label_tuple: Tuple[str, int] = ('GDNH_2023_000391', 0),
        save_path: Optional[str] = None
) -> pd.DataFrame:
    """
    Integrated function to process data and get predictions in one step.

    Args:
        data: Raw input data array
        clinic_data: Dictionary of clinical data
        model_path: Path to the saved model file
        selected_features_path: Path to saved selected features file
        fold: Model fold to use (default: 1)
        file_label_tuple: Tuple of (file_name, label) (default: ('GDNH_2023_000391', 0))

    Returns:
        DataFrame containing predictions
    """
    pipeline = MLPipeline()

    # Generate features

    # Process clinical features
    # 判断如果 save_path 存在，features_df 从 save_path 中加载，否则运行后保存至 save_path
    if save_path:
        try:
            # 尝试从 save_path 加载 features_df
            with open(save_path, 'rb') as f:
                features_df = pickle.load(f)
            print("Features loaded from save_path.")
        except :
            print("Features not found in save_path, generating new features...")
            # 如果文件不存在，则生成新的 features_df 并保存到 save_path
            features_df0 = pipeline.generate_features(data=data, file_label_tuple=file_label_tuple)
            features_df = pipeline.process_clinic_features(features_df0, clinic_data)
            with open(save_path, 'wb') as f:
                pickle.dump(features_df, f)
            print("Features generated and saved to save_path.")
    else:
        # 如果没有 save_path，则直接处理特征并保存
        features_df0 = pipeline.generate_features(data=data, file_label_tuple=file_label_tuple)
        features_df = pipeline.process_clinic_features(features_df0, clinic_data)

    # Select features
    if selected_features_path is None:
        selected_features_df = features_df
    else:
        selected_features_df = pipeline.select_features(features_df, selected_features_path)

    # Make predictions
    return pipeline.predict(model_path, fold, selected_features_df)

@contextmanager
def suppress_print(enable_suppress=True, capture_output=False):
    """
    上下文管理器，用于控制是否屏蔽打印内容，并选择性捕获打印内容。

    :param enable_suppress: bool, 是否屏蔽打印内容（默认为 True）
    :param capture_output: bool, 是否捕获打印内容到变量（默认为 False）
    :yield: 捕获的输出内容（如果 capture_output=True），否则 None
    """
    original_stdout = sys.stdout  # 保存原始标准输出
    buffer = io.StringIO() if capture_output else None

    try:
        if enable_suppress:
            sys.stdout = buffer if capture_output else open(os.devnull, 'w')
        yield buffer
    finally:
        sys.stdout = original_stdout  # 恢复标准输出
        if buffer:
            # 不再直接关闭 buffer，让外部继续使用捕获内容
            pass


if __name__ == '__main__':
    ...

