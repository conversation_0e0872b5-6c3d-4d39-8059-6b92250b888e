"""
Author: b<PERSON>yuchen
email: <EMAIL>

file: 
date: 2024/10/29 16:46
desc: ST段的时刻生成Qh/Sr/Th/qhs/srs
"""

import os
from utils.utils import *
from utils.gettimes1 import gettime
from tqdm import tqdm

def draw_plot0(ys, g1='', xys=[], strs=''):
    '''绘制单个通道图: 心磁ys, 保存地址g1, 关键点xys=[[x1,y1],[x1,y2],..], 信息strs'''
    import matplotlib.pyplot as plt
    xs = list(range(len(ys)))
    plt.rcParams['savefig.dpi'] = 512
    plt.plot(xs, ys, c='k', linewidth=0.5)
    plt.plot([xs[0],xs[-1]], [0,0], linewidth=0.5)
    for x,y in xys:
        plt.scatter(x, y, c='r', s=2)
    plt.title(strs)  # 信息
    if g1:
        plt.savefig(g1)  # 保存位置
    # plt.show()  # 展示
    plt.close()


def get_mai0(ls36):
    '''周期最大幅值'''
    M0 = max([max(ls36[i]) for i in range(len(ls36))])
    M1 = -min([min(ls36[i]) for i in range(len(ls36))])
    return [M0, M1]


def draw_ls36(ls36, g1, ik=[]):
    '''绘制空间波组图: 36通道数据ls36, 保存地址g1, 关键点集ik=[x1,x2,..]'''
    import matplotlib.pyplot as plt
    # 1.创建数据
    M, N = max(get_mai0(ls36)), len(ls36[0])
    xs = np.arange(N)
    y0 = xs*0  # 0基线
    # 2.绘制
    fig, axs = plt.subplots(6, 6, figsize=(15, 15), facecolor='black', subplot_kw={'facecolor': 'black'})
    for ax in axs.flat:
        ax.set_xlim(0, N-1)
        ax.set_ylim(-M, M)
        ax.set_xticks([])  # 隐藏x轴刻度
        ax.set_yticks([])  # 隐藏y轴刻度
        ax.spines['top'].set_visible(False)    # 隐藏顶部轴线
        ax.spines['right'].set_visible(False)  # 隐藏右侧轴线
        ax.spines['bottom'].set_visible(False)  # 隐藏底部轴线
        ax.spines['left'].set_visible(False)   # 隐藏左侧轴线
    for i in range(6):
        for j in range(6):
            y = np.array(ls36[i*6+j])
            # axs[i, j].plot(x, y0, color='blue', linewidth=2, alpha=0.6)
            for i0 in ik:
                # axs[i, j].scatter(ik, [0, 0], c='r', s=50)
                axs[i, j].scatter(i0, 0, c='r', s=50)
            axs[i, j].plot(xs, y0, color='blue', linewidth=2)  # 基线
            axs[i, j].plot(xs, y, color='white', linewidth=2)  # 设置曲线颜色为白色
    plt.tight_layout()
    # plt.show()
    plt.savefig(g1)
    plt.close()


def cmpr_TPbase1(f0, f1, tms):
    '''TP段基线微调-分通道'''
    ls36 = get_mcg36(f0, 0)
    Tr, Ph = tms[12], tms[0]
    jxs, Ms, ls2 = [], [], []
    for j in range(len(ls36)):
        l0 = ls36[j]
        M = max(max(l0), -min(l0))
        l1, l2 = l0[Tr:], l0[:Ph]
        m0 = round(cal_iqr0(l1+l2), 6)
        Ms.append(m0)  # 基线

        jx = 0  # 基线TP差
        if l1!=[] and l2!=[]:
            m1, m2 = cal_iqr0(l1), cal_iqr0(l2)
            jx = round(abs(m1-m2)/M, 6)
        jxs.append(jx)
        ls2.append([l0[k]-m0 for k in range(len(l0))])
    ls2 = [[k]+[ls2[j][k] for j in range(len(ls2))] for k in range(len(ls2[0]))]
    l0 = ['\t'.join(format(ls2[k][j],'.6f') for j in range(len(ls2[k]))) for k in range(len(ls2))]
    write_str('\n'.join(l0), f1)  # 保存心磁
    return [jxs, Ms]


def Cal_stseg0(f0, f1):
    '''ST参数-Qh/Sr/Th时刻计算: 心磁f0, TP基线微调后心磁f1
    步骤: TP基线微调、生成综合Qh/Sr/Th时刻和每通道Qhs/Srs
    '''
    ts = gettime(f0)[:-1]  # 时刻点计算
    _, _ = cmpr_TPbase1(f0, f1, ts)  # 保存微调后心磁
    th0 = 20  # 前后延长20ms(心率60)
    th1 = 5  # 波峰波谷最小间距
    qhs0, qhs, srs, rss = [], [], [], []
    t0 = round(th0*len(get_lines1(f1))/600)
    ls36 = get_mcg36(f1, 0)
    ts = gettime(f1)  # 新时刻点
    Qh, Sr, Th = ts[3]-t0, ts[7]+t0, ts[8]
    for i0 in range(36):  # 统计信息
        l2 = ls36[i0][Qh:Sr+1]
        M0 = max(max(l2), -min(l2))
        ik0 = [0, len(l2)-1]
        ik, _ = signal.find_peaks(np.array(l2), distance=th1)
        ik1 = [j for j in ik]
        ik, _ = signal.find_peaks(-np.array(l2), distance=th1)
        ik2 = [j for j in ik]
        pt0 = sorted(set(ik0+ik1+ik2))
        l3 = [round(l2[i1]/M0,3) for i1 in range(len(l2))]
        Qh0, QRSs, Sr0, Rs = cmpr_qrspt1(l3, pt0, ik1, ik2)
        qhs.append(Qh0)
        srs.append(Sr0)
        rss.append(Rs)
        mg = [[l3[ii] for ii in jj] for jj in QRSs]
        if 1.0 in mg[0] or -1.0 in mg[0]:
            qhs0.append(Qh0)
    rss = hb_lis0(rss)
    if len(qhs0)>2:
        qh0 = cmpr_jzd0(qhs0, max(min(rss)-1,0), 0)[-1]  # 综合起止点
    else:
        qh0 = cmpr_jzd0(qhs, max(min(rss)-1,0), 0)[-1]
    t1, t2 = 62+qh0, 109+qh0
    sr0s = cmpr_jzd0(srs, min(max(rss)+1,Sr+1-Qh), 1)
    sr1s = [t00 for t00 in sr0s if t00>=t1 and t00<=t2]
    if sr1s:
        sr0 = sr1s[0]
    else:
        srs1 = [t00 for t00 in srs if t00>=t1 and t00<=t2]
        if srs1:
            sr0 = cmpr_jzd0(srs1, min(max(rss)+1,Sr+1-Qh), 1)[0]
        else:
            sr0 = sr0s[0]
    qh0 = qh0 + Qh
    sr0 = sr0 + Qh
    qhs = [num + Qh for num in qhs]
    srs = [num + Qh for num in srs]
    return [qh0, sr0, Th, qhs, srs]


if __name__ == '__main__':
    f0 = r'D:\ST_develop\datasets\000\000'  # 源心磁
    f1 = r'D:\ST_develop\datasets\000\000_2'  # TP基线微调后的心磁地址
    os.makedirs(f1, exist_ok=True)
    mcg_files = [f for f in os.listdir(f0) if f.endswith('.txt')]
    for filename in tqdm(mcg_files, desc="Processing MCG files"):
        input_file = os.path.join(f0, filename)
        output_file = os.path.join(f1, filename)

        Qh, Sr, Th, qhs, srs = Cal_stseg0(input_file, output_file)

