# -*- coding: utf-8 -*-
"""
平衡特征选择配置文件
定义各种平衡方法和参数
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class BalancedSelectionConfig:
    """
    平衡特征选择配置类
    """
    # 基本设置
    enable_balancing: bool = True
    imbalance_threshold: float = 2.0  # 不平衡比例阈值，超过此值才进行平衡处理
    
    # SMOTE相关参数
    smote_method: str = 'auto'  # 'smote', 'borderline', 'adasyn', 'auto'
    smote_k_neighbors: int = 5
    smote_sampling_strategy: str = 'auto'  # 'auto', 'minority', 'not majority', 'all'
    
    # Boruta相关参数
    boruta_iterations: int = 10
    boruta_sample_fraction: float = 0.8
    boruta_max_iter: int = 500
    boruta_alpha: float = 0.01
    boruta_perc: int = 100
    
    # 特征选择质量评估
    enable_quality_evaluation: bool = True
    quality_cv_folds: int = 5
    quality_scoring: str = 'f1_macro'
    
    # 稳定性相关
    stability_quantile: float = 0.5
    min_stability_score: float = 0.3
    
    # 随机森林参数
    rf_n_estimators: int = 500
    rf_max_depth: int = 15
    rf_min_samples_split: int = 3
    rf_min_samples_leaf: int = 5
    rf_class_weight: str = 'balanced'
    
    def get_smote_params(self) -> Dict[str, Any]:
        """获取SMOTE参数"""
        return {
            'k_neighbors': self.smote_k_neighbors,
            'sampling_strategy': self.smote_sampling_strategy,
            'random_state': 42
        }
    
    def get_boruta_params(self) -> Dict[str, Any]:
        """获取Boruta参数"""
        return {
            'n_estimators': 'auto',
            'max_iter': self.boruta_max_iter,
            'alpha': self.boruta_alpha,
            'perc': self.boruta_perc,
            'early_stopping': True,
            'verbose': 2
        }
    
    def get_rf_params(self) -> Dict[str, Any]:
        """获取随机森林参数"""
        return {
            'n_estimators': self.rf_n_estimators,
            'max_depth': self.rf_max_depth,
            'min_samples_split': self.rf_min_samples_split,
            'min_samples_leaf': self.rf_min_samples_leaf,
            'class_weight': self.rf_class_weight,
            'n_jobs': -1,
            'random_state': 42,
            'oob_score': False
        }


# 预定义配置
CONSERVATIVE_CONFIG = BalancedSelectionConfig(
    enable_balancing=True,
    imbalance_threshold=3.0,  # 更保守的阈值
    smote_method='smote',
    boruta_iterations=5,  # 较少的迭代次数
    boruta_sample_fraction=0.9,  # 更大的采样比例
    stability_quantile=0.7,  # 更严格的稳定性要求
)

AGGRESSIVE_CONFIG = BalancedSelectionConfig(
    enable_balancing=True,
    imbalance_threshold=1.5,  # 更激进的阈值
    smote_method='borderline',  # 使用BorderlineSMOTE
    boruta_iterations=15,  # 更多的迭代次数
    boruta_sample_fraction=0.7,  # 更小的采样比例
    stability_quantile=0.3,  # 更宽松的稳定性要求
)

FAST_CONFIG = BalancedSelectionConfig(
    enable_balancing=True,
    imbalance_threshold=2.0,
    smote_method='smote',
    boruta_iterations=3,  # 快速模式
    boruta_sample_fraction=1.0,  # 不进行采样
    boruta_max_iter=100,  # 较少的Boruta迭代
    rf_n_estimators=100,  # 较少的树
    enable_quality_evaluation=False,  # 跳过质量评估
)

HIGH_QUALITY_CONFIG = BalancedSelectionConfig(
    enable_balancing=True,
    imbalance_threshold=2.0,
    smote_method='adasyn',  # 使用ADASYN
    boruta_iterations=20,  # 更多迭代
    boruta_sample_fraction=0.8,
    boruta_max_iter=1000,  # 更多Boruta迭代
    rf_n_estimators=1000,  # 更多的树
    enable_quality_evaluation=True,
    quality_cv_folds=10,  # 更多的交叉验证折数
)


def get_config_by_name(config_name: str) -> BalancedSelectionConfig:
    """
    根据名称获取预定义配置
    
    Parameters:
    -----------
    config_name : str
        配置名称: 'conservative', 'aggressive', 'fast', 'high_quality', 'default'
        
    Returns:
    --------
    BalancedSelectionConfig: 配置对象
    """
    configs = {
        'conservative': CONSERVATIVE_CONFIG,
        'aggressive': AGGRESSIVE_CONFIG,
        'fast': FAST_CONFIG,
        'high_quality': HIGH_QUALITY_CONFIG,
        'default': BalancedSelectionConfig()
    }
    
    return configs.get(config_name.lower(), BalancedSelectionConfig())


def create_custom_config(**kwargs) -> BalancedSelectionConfig:
    """
    创建自定义配置
    
    Parameters:
    -----------
    **kwargs : 配置参数
        
    Returns:
    --------
    BalancedSelectionConfig: 自定义配置对象
    """
    config = BalancedSelectionConfig()
    
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            print(f"Warning: Unknown config parameter '{key}' ignored")
    
    return config


# 使用示例
if __name__ == "__main__":
    # 使用预定义配置
    config = get_config_by_name('aggressive')
    print("Aggressive配置:")
    print(f"  平衡阈值: {config.imbalance_threshold}")
    print(f"  SMOTE方法: {config.smote_method}")
    print(f"  Boruta迭代次数: {config.boruta_iterations}")
    
    # 创建自定义配置
    custom_config = create_custom_config(
        enable_balancing=True,
        imbalance_threshold=2.5,
        smote_method='borderline',
        boruta_iterations=8
    )
    print("\n自定义配置:")
    print(f"  平衡阈值: {custom_config.imbalance_threshold}")
    print(f"  SMOTE方法: {custom_config.smote_method}")
    print(f"  Boruta迭代次数: {custom_config.boruta_iterations}")
