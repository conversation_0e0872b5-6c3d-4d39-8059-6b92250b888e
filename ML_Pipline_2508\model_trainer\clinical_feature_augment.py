"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: clinical_feature_augment
date: 2025/7/3 15:48
desc: 
"""

import pandas as pd
import numpy as np
from itertools import combinations


def create_statistical_features(X: pd.DataFrame) -> pd.DataFrame:
    """
    根据输入的DataFrame创建统计衍生特征，并只返回新增的特征。
    """
    X_new = pd.DataFrame(index=X.index)

    numeric_cols = X.select_dtypes(include=np.number).columns.tolist()
    numeric_cols = [c for c in numeric_cols if X[c].nunique() > 2]

    if len(numeric_cols) >= 2:
        X_new['stat_numeric_mean'] = X[numeric_cols].mean(axis=1)
        X_new['stat_numeric_std'] = X[numeric_cols].std(axis=1)

    if 'clinic_age' in X.columns:
        X_new['stat_age_decade'] = (X['clinic_age'] // 10).astype(int)
        X_new['stat_is_elderly'] = (X['clinic_age'] >= 65).astype(int)

    if 'clinic_bmi' in X.columns:
        X_new['stat_is_obese'] = (X['clinic_bmi'] >= 30).astype(int)
        X_new['stat_is_overweight'] = ((X['clinic_bmi'] >= 25) & (X['clinic_bmi'] < 30)).astype(int)

    print(f"生成了 {X_new.shape[1]} 个统计衍生特征。")
    return X_new


def create_domain_specific_interactions(X: pd.DataFrame) -> pd.DataFrame:
    """
    (V3版) 根据详细的13个临床特征创建领域知识交互特征，并只返回新增的特征。
    此版本深度集成了“既往介入”和“典型症状”等高影响力指标。
    """
    X_new = pd.DataFrame(index=X.index)

    # 确保用于运算的列是数值类型，对于不存在的列，填充0以安全计算
    X_numeric = X.apply(pd.to_numeric, errors='coerce').fillna(0)

    # --- 1. 基础风险因子交互 (代谢综合征相关) ---
    metabolic_risk_factors = ['clinic_hypertension', 'clinic_hyperlipidemia', 'clinic_diabetes']
    for f1, f2 in combinations(metabolic_risk_factors, 2):
        if f1 in X.columns and f2 in X.columns:
            X_new[f'domain_meta_{f1.split("_")[1][:4]}_x_{f2.split("_")[1][:4]}'] = X_numeric[f1] * X_numeric[f2]

    # --- 2. 年龄与BMI的核心交互 ---
    if 'clinic_age' in X.columns:
        for feature in ['clinic_bmi', 'clinic_hypertension', 'clinic_diabetes']:
            if feature in X.columns:
                X_new[f'domain_age_x_{feature.split("_")[1]}'] = X_numeric['clinic_age'] * X_numeric[feature]

    if 'clinic_bmi' in X.columns:
        for feature in ['clinic_hypertension', 'clinic_diabetes']:
            if feature in X.columns:
                X_new[f'domain_bmi_x_{feature.split("_")[1]}'] = X_numeric['clinic_bmi'] * X_numeric[feature]

    # --- 3. 新增：基于高影响力指标的交互 (干预/症状) ---
    high_impact_indicators = ['clinic_intervention', 'clinic_symp']
    if all(f in X.columns for f in high_impact_indicators):
        # 有症状且有既往介入史，可能意味着再狭窄或新病变，风险极高
        X_new['domain_symp_and_intervention'] = X_numeric['clinic_symp'] * X_numeric['clinic_intervention']

    # 将高影响力指标与年龄/性别/BMI交互
    for indicator in high_impact_indicators:
        if indicator in X.columns:
            if 'clinic_age' in X.columns:
                X_new[f'domain_age_x_{indicator.split("_")[1]}'] = X_numeric['clinic_age'] * X_numeric[indicator]
            if 'clinic_gender' in X.columns:
                X_new[f'domain_gender_x_{indicator.split("_")[1]}'] = X_numeric['clinic_gender'] * X_numeric[indicator]
            if 'clinic_bmi' in X.columns:
                X_new[f'domain_bmi_x_{indicator.split("_")[1]}'] = X_numeric['clinic_bmi'] * X_numeric[indicator]

    # --- 4. 新增：生活方式交互 ---
    lifestyle_factors = ['clinic_smoking', 'clinic_drinking']
    if all(f in X.columns for f in lifestyle_factors):
        X_new['domain_smoke_and_drink'] = X_numeric['clinic_smoking'] * X_numeric['clinic_drinking']

    # --- 5. 综合风险评分 (更全面) ---
    all_risk_factors = [
        'clinic_smoking', 'clinic_drinking', 'clinic_hypertension',
        'clinic_hyperlipidemia', 'clinic_diabetes', 'clinic_symp', 'clinic_intervention'
    ]
    existing_risk_factors = [f for f in all_risk_factors if f in X.columns]

    if existing_risk_factors:
        X_new['domain_comprehensive_risk_score'] = X_numeric[existing_risk_factors].sum(axis=1)
        # 风险评分与核心指标的交互
        if 'clinic_age' in X.columns:
            X_new['domain_age_x_risk_score'] = X_numeric['clinic_age'] * X_new['domain_comprehensive_risk_score']
        if 'clinic_bmi' in X.columns:
            X_new['domain_bmi_x_risk_score'] = X_numeric['clinic_bmi'] * X_new['domain_comprehensive_risk_score']

    print(f"生成了 {X_new.shape[1]} 个V3版领域知识交互特征。")
    return X_new


def enrich_clinical_features(features_df: pd.DataFrame) -> pd.DataFrame:
    """
    接收一个特征DataFrame，对其临床特征进行增强，并返回包含新增特征的完整DataFrame。

    处理流程:
    1. 自动识别以 'clinic_' 开头的临床特征。
    2. 对这些临床特征进行基础的缺失值填充。
    3. 生成统计衍生特征。
    4. 生成扩充后的领域知识交互特征。
    5. 将新特征合并回原始DataFrame并返回。

    参数:
        features_df (pd.DataFrame): 包含原始特征的DataFrame。
        expand_domain (bool): 是否使用扩充版的领域知识特征，默认为True。

    返回:
        pd.DataFrame: 包含所有原始特征和新增临床增强特征的DataFrame。
    """
    print("开始执行临床特征增强流程...")

    # 创建副本以避免修改原始数据
    df_enriched = features_df.copy()

    # --- 步骤1: 识别并分离临床特征 ---
    clinic_cols = [col for col in df_enriched.columns if col.startswith('clinic_')]
    if not clinic_cols:
        print("警告: 未在DataFrame中找到任何以 'clinic_' 开头的特征，返回原始DataFrame。")
        return df_enriched

    X_clinic = df_enriched[clinic_cols].copy()
    print(f"成功识别 {len(clinic_cols)} 个临床特征进行处理。")

    # --- 步骤2: 基础预处理 (缺失值填充) ---
    # 这是一个管道无关的简单填充，用于确保特征生成时不会出错
    for col in X_clinic.columns:
        # 对数值型特征用中位数填充
        if pd.api.types.is_numeric_dtype(X_clinic[col]) and X_clinic[col].nunique() > 2:
            if X_clinic[col].isnull().any():
                median_val = X_clinic[col].median()
                X_clinic[col].fillna(median_val, inplace=True)
        # 对看起来像分类/二元的特征用-1填充
        else:
            if X_clinic[col].isnull().any():
                X_clinic[col].fillna(-1, inplace=True)

    print("已完成对临床特征的基础缺失值填充。")

    # --- 步骤3 & 4: 调用辅助函数生成新特征 ---
    # 注意：这些辅助函数现在被设计为只返回新生成的特征
    new_stat_features = create_statistical_features(X_clinic)
    new_domain_features = create_domain_specific_interactions(X_clinic)
    # new_stat_features = new_stat_features.add_prefix('clinic_')
    # new_domain_features = new_domain_features.add_prefix('clinic_')

    # --- 步骤5: 合并新特征到主DataFrame ---
    new_features_list = [new_stat_features, new_domain_features]

    for new_df in new_features_list:
        for col in new_df.columns:
            # 检查以避免重复添加 (虽然不太可能发生)
            if col not in df_enriched.columns:
                df_enriched[col] = new_df[col]

    print("\n临床特征增强流程执行完毕!")
    print(f"新增了 {df_enriched.shape[1] - features_df.shape[1]} 个特征。")

    return df_enriched
