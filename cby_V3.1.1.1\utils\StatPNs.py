"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/01/18 16:40
desc: 小工具
    解压缩
    提取id和时刻点
environment: pfafn36
run:
    python utils.py
option:
    结果目录、心磁文档、时刻点文档, plt.show()
"""

import numpy as np


def norms0(Z0):
    '''正负分别归一化'''
    Z = Z0.copy()
    max_n = float('%4.6f' % np.max(Z))
    min_n = float('%4.6f' % np.min(Z))
    for i1 in range(Z.shape[0]):
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] < 0:
                Z[i1, j1] = -1 * Z[i1, j1] / min_n
            else:
                Z[i1, j1] = Z[i1, j1] / max_n
    return Z


def fivepos(pn03, matrix):  # 8方位5近邻连续极值
    m = len(pn03[0])
    if m > 1:
        pnps = [pn03[0][0]]
        neighbor = [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1], [1, 0], [1, 1]]
        rnum, cnum = matrix.shape
        for i1 in range(1, m):
            corx, cory = pn03[0][i1][1] - 1, pn03[0][i1][2] - 1
            fiveflag = 1  # 噪声标识
            for nb in neighbor:
                for k1 in range(5):
                    xnew = int(corx + nb[0] * k1)
                    ynew = int(cory + nb[1] * k1)
                    if xnew + nb[0] < 0 or xnew + nb[0] > rnum - 1:
                        break
                    if ynew + nb[1] < 0 or ynew + nb[1] > cnum - 1:
                        break
                    if matrix[xnew, ynew] < matrix[xnew + nb[0], ynew + nb[1]]:
                        fiveflag = 0  # 曲率异常
                        break
                if fiveflag == 0:
                    break
            if fiveflag == 1:
                pnps.append(pn03[0][i1])
        pn03[0] = pnps
    return pn03


def ifpos(pn03, matrix):
    neighb = [-1, 0, 1]
    rnum, cnum = matrix.shape
    for corx in range(rnum):
        for cory in range(cnum):
            if matrix[corx, cory] <= 0:  # 正负筛选
                continue
            flag = 1  # 极值标识
            for j1 in neighb:
                if corx + j1 < 0 or corx + j1 > rnum - 1:
                    continue  # 维度内
                for j2 in neighb:
                    if cory + j2 < 0 or cory + j2 > cnum - 1:
                        continue  # 维度内
                    if j1 == 0 and j2 == 0:
                        continue  # 去除自己
                    if matrix[corx, cory] < matrix[corx+j1, cory+j2]:
                        flag = 0  # 严格极小
                        break
                if flag == 0:
                    break
            if flag == 1:  # 局部极值
                pn03[0].append([matrix[corx, cory], corx+1, cory+1])
    return pn03


def fiveneg(pn03, matrix):  # 8方位连续极值
    n = len(pn03[1])
    if n > 1:
        pnns = [pn03[1][0]]
        neighbor = [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1],
                    [1, 0], [1, 1]]
        rnum, cnum = matrix.shape
        for i1 in range(1, n):
            corx, cory = pn03[1][i1][1] - 1, pn03[1][i1][2] - 1
            fiveflag = 1  # 噪声标识
            for nb in neighbor:
                for k1 in range(5):
                    xnew = int(corx + nb[0] * k1)
                    ynew = int(cory + nb[1] * k1)
                    if xnew + nb[0] < 0 or xnew + nb[0] > rnum - 1:
                        break
                    if ynew + nb[1] < 0 or ynew + nb[1] > cnum - 1:
                        break
                    if matrix[xnew, ynew] > matrix[xnew + nb[0], ynew + nb[1]]:
                        fiveflag = 0  # 曲率异常
                        break
                if fiveflag == 0:
                    break
            if fiveflag == 1:
                pnns.append(pn03[1][i1])
        pn03[1] = pnns
    return pn03


def ifneg(pn03, matrix):
    neighb = [-1, 0, 1]
    rnum, cnum = matrix.shape
    # cnt = 0
    for corx in range(rnum):
        for cory in range(cnum):
            if matrix[corx, cory] >= 0:  # 正负筛选
                continue
            flag = 1  # 极值标识
            for j1 in neighb:
                if corx + j1 < 0 or corx + j1 > rnum - 1:
                    continue  # 维度内
                for j2 in neighb:
                    if cory + j2 < 0 or cory + j2 > cnum - 1:
                        continue  # 维度内
                    if j1 == 0 and j2 == 0:
                        continue  # 去除自己
                    if matrix[corx, cory] > matrix[corx + j1, cory + j2]:
                        flag = 0  # 严格极大
                        break
                if flag == 0:
                    break
            if flag == 1:  # 局部极值
                pn03[1].append([matrix[corx, cory], corx + 1, cory + 1])
    return pn03


def sortlis(pn03):
    m = len(pn03[0])
    n = len(pn03[1])
    if m > 1:  # 多正极子排序
        data = np.array(pn03[0])
        idex = np.lexsort((data[:, 2], data[:, 1], -1 * data[:, 0]))
        sorted_data = data[idex]
        pn03[0] = sorted_data.tolist()
    if n > 1:  # 多负极子排序
        data = np.array(pn03[1])
        idex = np.lexsort((data[:, 2], data[:, 1], data[:, 0]))
        sorted_data = data[idex]
        pn03[1] = sorted_data.tolist()
    return pn03


def truncate(pn03, vmin):
    if len(pn03[0]) > 1:
        pnps = [pn03[0][0]]
        for i1 in range(1, len(pn03[0])):
            if pn03[0][i1][0] > vmin:
                pnps.append(pn03[0][i1])
        pn03[0] = pnps
    if len(pn03[1]) > 1:
        pnns = [pn03[1][0]]
        for i1 in range(1, len(pn03[1])):
            if pn03[1][i1][0] < -1 * vmin:
                pnns.append(pn03[1][i1])
        pn03[1] = pnns
    return pn03


def merge(pndn):  # 按顺序合并5近邻极子
    pn = [[], []]
    for j1 in range(len(pndn[0])):  # 正极子列表
        if j1 > len(pndn[0]) - 1:
            break
        pn[0].append(pndn[0][j1])
        if j1 == len(pndn[0]) - 1:
            break
        pnleft = pndn[0][j1 + 1:].copy()
        for k1 in range(len(pnleft)):
            if abs(pndn[0][j1][1] - pnleft[k1][1]) <= 5 and abs(
                    pndn[0][j1][2] - pnleft[k1][2]) <= 5:
                pndn[0].remove(pnleft[k1])
    for j1 in range(len(pndn[1])):  # 负极子列表
        if j1 > len(pndn[1]) - 1:
            break
        pn[1].append(pndn[1][j1])
        if j1 == len(pndn[1]) - 1:
            break
        pnleft = pndn[1][j1 + 1:].copy()
        for k1 in range(len(pnleft)):
            if abs(pndn[1][j1][1] - pnleft[k1][1]) <= 5 and abs(
                    pndn[1][j1][2] - pnleft[k1][2]) <= 5:
                pndn[1].remove(pnleft[k1])
    return pn


def roundf2(pn0):
    pn = [[], []]
    for j in [0, 1]:
        for i in range(len(pn0[j])):
            l0 = pn0[j][i]
            pn[j].append([round(l0[0], 2), int(l0[1]), int(l0[2])])
    return pn


def statpn(matrix, vmin=0.3, f2=0):  # 去除列表中的空字符
    pn03 = [[], []]
    pn03 = ifpos(pn03, matrix)  # 统计+正负筛选
    pn03 = ifneg(pn03, matrix)
    pn03 = sortlis(pn03)  # 排序
    pn03 = fivepos(pn03, matrix)  # 5近邻曲率筛选
    pn03 = fiveneg(pn03, matrix)
    pn03 = truncate(pn03, vmin)  # 数值区间筛选
    pn03 = merge(pn03)  # 5近邻极子合并
    if f2:
        pn03 = roundf2(pn03)
    return pn03


# def caldp(pns):
#     disps = [0, 0]  # 无极子和单极子都是0
#     wk = 2  # 权重
#     for j1 in range(2):
#         if len(pns[j1]) != 0:
#             corx = pns[j1][0][1]
#             cory = pns[j1][0][2]
#             for j2 in range(1, len(pns[j1])):
#                 dx = abs(pns[j1][j2][1]-corx)
#                 dy = abs(pns[j1][j2][2]-cory)
#                 dk = pow(dx*dx+dy*dy, 1/2) / 10
#                 vk = pns[j1][j2][0]
#                 disps[j1] = disps[j1] + wk * dk * vk
#     disps[0] = round(disps[0], 1)
#     disps[1] = round(-1*disps[1], 1)
#     return disps[0], disps[1]


if __name__ == '__main__':
    matrix = './matrix'  # 心磁插值后矩阵
    Z = norms0(matrix)  # 正负归一化
    pn = statpn(Z, vmin=0.3)  # 多极子
    print(pn)
    # dpp, dpn = caldp(pn)  # 正/负离散度
    # disp = max(dpp, dpn)
    # print(disp)
