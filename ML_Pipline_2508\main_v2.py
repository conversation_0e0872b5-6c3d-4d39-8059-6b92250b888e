"""
Author: <PERSON><PERSON> (重构版本)
email: <EMAIL>
file: main_v2.py
date: 2025-01-XX
desc: 
    重构版本的部署脚本，支持新版本模型预测流程
    实现从txt文件到预测结果的完整部署管道
    集成基础特征、cur特征、ci特征生成和模型预测
"""

import argparse
import logging
import os
import sys
import tempfile
from pathlib import Path
from typing import Dict, Optional, Tuple, Any

import numpy as np
import pandas as pd
from tqdm import tqdm

# 导入新版本的核心组件
from model_trainer.train_manager import ModelEvaluator, ModelTrainer

# 尝试导入cur特征生成模块
try:
    # 添加cur_features目录到Python路径
    import sys
    cur_features_path = Path(__file__).parent / "outer_features" / "cur_features"
    if str(cur_features_path) not in sys.path:
        sys.path.insert(0, str(cur_features_path))

    from main import MCGAnalyzer
    CUR_AVAILABLE = True
    print("cur特征生成模块加载成功")
except ImportError as e:
    print(f"警告: cur特征生成模块导入失败: {e}")
    MCGAnalyzer = None
    CUR_AVAILABLE = False

# from outer_features.ci_features.index import gn_mts0  # 暂时注释，需要复杂配置
from feature_generator.main_feature_generator import FeatureGenerator


class DeploymentPipeline:
    """新版本部署管道类"""
    
    def __init__(self, model_version: str = None):
        """
        初始化部署管道
        Args:
            model_version: 指定模型版本，如果为None则使用最新版本
        """
        self.model_version = model_version
        self.model_evaluator = ModelEvaluator()

        # 初始化MCGAnalyzer，需要切换到正确的工作目录
        if CUR_AVAILABLE:
            try:
                # 保存当前工作目录
                original_cwd = os.getcwd()
                # 切换到cur_features目录
                cur_features_dir = Path(__file__).parent / "outer_features" / "cur_features"
                os.chdir(cur_features_dir)
                # 初始化MCGAnalyzer
                self.mcg_analyzer = MCGAnalyzer()
                # 恢复原工作目录
                os.chdir(original_cwd)
                print("MCGAnalyzer初始化成功")
            except Exception as e:
                print(f"MCGAnalyzer初始化失败: {e}")
                self.mcg_analyzer = None
        else:
            self.mcg_analyzer = None

        self.feature_generator = FeatureGenerator()

        # 临时文件夹用于存储中间特征文件
        self.temp_dir = tempfile.mkdtemp(prefix="ml_pipeline_")

    def _load_txt_data(self, txt_path: str):
        """从txt文件加载数据，使用与原版本相同的逻辑"""
        try:
            # 使用与原版本相同的数据加载方式
            data_frame = pd.read_csv(txt_path, sep='\t', header=None).iloc[:, :]
            return data_frame
        except Exception as e:
            print(f"Error loading txt file: {str(e)}")
            return None
        
    def __del__(self):
        """清理临时文件"""
        import shutil
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def generate_base_features(self, txt_file: str, clinical_data: Dict) -> str:
        """
        生成基础ML特征
        Args:
            txt_file: txt文件路径
            clinical_data: 临床数据字典
        Returns:
            str: 生成的特征文件路径
        """
        mcg_file = Path(txt_file).stem

        # 创建临时特征目录
        temp_feature_dir = os.path.join(self.temp_dir, "base_features")
        os.makedirs(temp_feature_dir, exist_ok=True)

        try:
            # 使用与原版本相同的方法生成基础特征
            from pipeline.pipline import MLPipeline

            # 读取txt数据 - 使用与原版本相同的load_txt_data函数
            data_array = self._load_txt_data(txt_file)
            if data_array is None:
                print("txt数据加载失败")
                features_df = pd.DataFrame({'mcg_file': [mcg_file], **clinical_data})
                feature_file = os.path.join(temp_feature_dir, f"{mcg_file}.pkl")
                features_df.to_pickle(feature_file)
                return feature_file

            print(f"数据形状: {data_array.shape}")

            # 使用MLPipeline生成基础特征
            pipeline = MLPipeline()
            file_label_tuple = (mcg_file, 0)

            # 生成基础特征
            features_df0 = pipeline.generate_features(data=data_array, file_label_tuple=file_label_tuple)

            # 处理临床特征
            features_df = pipeline.process_clinic_features(features_df0, clinical_data)

            # 保存特征文件
            feature_file = os.path.join(temp_feature_dir, f"{mcg_file}.pkl")
            features_df.to_pickle(feature_file)

            print(f"基础特征生成完成: {len(features_df.columns)}个特征")
            return feature_file

        except Exception as e:
            print(f"生成基础特征时出错: {e}")
            # 创建空的特征文件作为fallback
            empty_df = pd.DataFrame({'mcg_file': [mcg_file], **clinical_data})
            feature_file = os.path.join(temp_feature_dir, f"{mcg_file}.pkl")
            empty_df.to_pickle(feature_file)
            return feature_file
    
    def generate_cur_features(self, txt_file: str) -> pd.DataFrame:
        """
        生成cur电流源特征
        Args:
            txt_file: txt文件路径
        Returns:
            pd.DataFrame: cur特征DataFrame
        """
        try:
            mcg_file = Path(txt_file).stem
            print(f"正在生成cur特征: {mcg_file}")

            if not CUR_AVAILABLE or self.mcg_analyzer is None:
                print("cur特征生成模块不可用，跳过cur特征生成")
                return pd.DataFrame({'mcg_file': [mcg_file]})

            # 使用MCGAnalyzer生成电流源特征，需要在正确的工作目录下执行
            original_cwd = os.getcwd()
            try:
                # 切换到cur_features目录
                cur_features_dir = Path(__file__).parent / "outer_features" / "cur_features"
                os.chdir(cur_features_dir)
                # 转换为绝对路径
                abs_txt_file = os.path.abspath(os.path.join(original_cwd, txt_file))
                # 执行分析
                cur_result = self.mcg_analyzer.analyze_file(abs_txt_file)
            finally:
                # 恢复原工作目录
                os.chdir(original_cwd)

            # 转换为DataFrame格式，与参考文件格式一致
            if isinstance(cur_result, dict):
                # 如果返回的是字典，转换为DataFrame
                cur_features = {}
                for i, (key, value) in enumerate(cur_result.items()):
                    if i == 0:  # 第一个是文件名
                        cur_features['mcg_file'] = value
                    else:
                        cur_features[f'cur_{key}'] = value
                cur_df = pd.DataFrame([cur_features])
            elif isinstance(cur_result, list):
                # 如果返回的是列表，按照MCGAnalyzer的列名格式处理
                columns = [
                    "mcg_file", "cur_J_q_rotate_ang", 'cur_q_angle', "cur_J_q_move", "cur_J_r_rotate_ang",
                    "cur_J_r_max_angle", "cur_J_s_angle", "cur_J_s_movement", 'cur_J_q_r_loc', 'cur_J_r_move',
                    'cur_J_r_index', "cur_J_r_angle", "cur_J_front_q_rate", "cur_J_front_r_rate", "cur_J_front_s_rate",
                    "cur_J_long_q_rate", "cur_J_long_r_rate", "cur_J_long_s_rate",
                    "cur_J_up_q_rate", "cur_J_up_r_rate", "cur_J_up_s_rate",
                    "cur_J_front_q_r", "cur_J_front_r_s", "cur_J_front_q_s",
                    "cur_J_long_q_r", "cur_J_long_r_s", "cur_J_long_q_s",
                    "cur_J_up_q_r", "cur_J_up_r_s", "cur_J_up_q_s",
                    "cur_x_Q_x_R", "cur_x_R_x_S", "cur_x_Q_x_S", "cur_y_Q_y_R", "cur_y_R_y_S", "cur_y_Q_y_S",
                    "cur_z_Q_z_R", "cur_z_R_z_S", "cur_z_Q_z_S", "cur_x_S", "cur_y_S", "cur_z_S",
                    "cur_x_Q_x_R_rotation", "cur_x_R_x_S_rotation", "cur_x_Q_x_S_rotation",
                    "cur_y_Q_y_R_rotation", "cur_y_R_y_S_rotation", "cur_y_Q_y_S_rotation",
                    "cur_z_Q_z_R_rotation", "cur_z_R_z_S_rotation", "cur_z_Q_z_S_rotation",
                    "cur_x_S_rotation", "cur_y_S_rotation", "cur_z_S_rotation",
                    "cur_Q_strength", "cur_R_strength", "cur_S_strength",
                    "cur_Q_x_direct", "cur_Q_y_direct", "cur_R_x_direct", "cur_R_y_direct", "cur_S_x_direct",
                    "cur_S_y_direct", "cur_overall_std_dev_QR", "cur_TT最大和最小点源强度比值",
                    "cur_TT段最大夹角", 'cur_TT段最大夹角_1', "cur_TT总电流向量", "cur_TT方差电流向量", "cur_平坦度因子",
                    "cur_RT最大点源强度比", "cur_RT夹角",
                    "cur_TT段离散度_3d", 'cur_TT段离散度_3d_1', "cur_TT段离散度_2d", 'cur_TT段离散度_2d_1',
                    "cur_所处象限", 'cur_所处象限_1', "cur_旋转方向", 'cur_单偶极子误差', 'cur_Tp段位置'
                ]

                # 确保列数匹配
                if len(cur_result) == len(columns):
                    cur_features = dict(zip(columns, cur_result))
                    cur_features['mcg_file'] = mcg_file  # 确保mcg_file正确
                    cur_df = pd.DataFrame([cur_features])
                else:
                    print(f"cur特征数量不匹配: 期望{len(columns)}, 实际{len(cur_result)}")
                    cur_df = pd.DataFrame({'mcg_file': [mcg_file]})
            else:
                cur_df = pd.DataFrame({'mcg_file': [mcg_file]})

            print(f"cur特征生成完成: {len(cur_df.columns)}个特征")
            return cur_df

        except Exception as e:
            print(f"生成cur特征时出错: {e}")
            import traceback
            traceback.print_exc()
            mcg_file = Path(txt_file).stem
            return pd.DataFrame({'mcg_file': [mcg_file]})
    
    def generate_ci_features(self, txt_file: str) -> pd.DataFrame:
        """
        生成ci参数特征
        Args:
            txt_file: txt文件路径
        Returns:
            pd.DataFrame: ci特征DataFrame
        """
        try:
            mcg_file = Path(txt_file).stem
            print(f"正在生成ci特征: {mcg_file}")

            # 使用正确的ci特征生成逻辑（按照main.py示例）
            ci_main_dir = Path(__file__).parent / "outer_features" / "ci_features"

            original_cwd = os.getcwd()
            try:
                # 切换到ci_features目录
                os.chdir(ci_main_dir)

                # 添加ci_features目录到Python路径
                import sys
                if str(ci_main_dir) not in sys.path:
                    sys.path.insert(0, str(ci_main_dir))

                # 导入ci_features的main模块
                import importlib.util
                spec = importlib.util.spec_from_file_location("ci_main", ci_main_dir / "main.py")
                ci_main = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(ci_main)

                generate_ci_features_with_names = ci_main.generate_ci_features_with_names

                # 转换为绝对路径
                abs_txt_file = os.path.abspath(os.path.join(original_cwd, txt_file))

                print(f"正在从txt文件自动计算插值矩阵和时刻点...")
                # 使用新的函数生成带正确特征名的ci特征
                ci_features_dict = generate_ci_features_with_names(abs_txt_file)

                # 添加mcg_file
                ci_features = {'mcg_file': mcg_file}
                ci_features.update(ci_features_dict)

                ci_df = pd.DataFrame([ci_features])
                print(f"ci特征生成完成: {len(ci_df.columns)}个特征")
                return ci_df

            except Exception as e:
                print(f"ci特征生成失败: {e}")
                import traceback
                traceback.print_exc()
            finally:
                # 恢复原工作目录
                os.chdir(original_cwd)

            # 如果生成失败，返回空的DataFrame
            print("CI特征生成失败，返回空特征")
            ci_df = pd.DataFrame({'mcg_file': [mcg_file]})
            return ci_df

        except Exception as e:
            print(f"生成ci特征时出错: {e}")
            mcg_file = Path(txt_file).stem
            return pd.DataFrame({'mcg_file': [mcg_file]})
    
    def predict_single_file(self, txt_file: str, clinical_data: Dict) -> Tuple[float, float]:
        """
        对单个文件进行预测
        Args:
            txt_file: txt文件路径
            clinical_data: 临床数据字典
        Returns:
            Tuple[float, float]: (预测概率, 预测类别)
        """
        try:
            mcg_file = Path(txt_file).stem

            # 1. 生成基础特征
            print(f"正在生成基础特征: {mcg_file}")
            base_feature_file = self.generate_base_features(txt_file, clinical_data)

            # 2. 生成cur特征
            print(f"正在生成cur特征: {mcg_file}")
            cur_features_df = self.generate_cur_features(txt_file)

            # 3. 生成ci特征
            print(f"正在生成ci特征: {mcg_file}")
            ci_features_df = self.generate_ci_features(txt_file)

            # 4. 保存cur和ci特征到临时文件（使用ModelTrainer期望的文件名）
            temp_feature_dir = os.path.dirname(base_feature_file)
            temp_cur_file = os.path.join(temp_feature_dir, "cur_feature_df_3105.pkl")
            temp_ci_file = os.path.join(temp_feature_dir, "ci_feature_df_3105.pkl")

            cur_features_df.to_pickle(temp_cur_file)
            ci_features_df.to_pickle(temp_ci_file)

            # 5. 创建临时Excel文件用于预测
            temp_excel = os.path.join(self.temp_dir, "temp_prediction.xlsx")

            # 构建预测用的DataFrame，使用新版本的字段名
            prediction_df = pd.DataFrame([{
                '心磁号': mcg_file,
                '造影结论': 0,  # 临时标签
                '综合标签': 0,  # 添加综合标签列
                **{f'临床特征-{k.replace("clinic_", "")}': v for k, v in clinical_data.items()}
            }])
            prediction_df.to_excel(temp_excel, sheet_name='测试用', index=False)

            # 6. 使用ModelEvaluator进行预测
            temp_feature_dir = os.path.dirname(base_feature_file)

            print(f"正在进行模型预测: {mcg_file}")

            # 直接调用模型预测，绕过批量处理逻辑
            prob, pred = self._direct_model_predict(
                mcg_file=mcg_file,
                base_feature_file=base_feature_file,
                cur_features_df=cur_features_df,
                ci_features_df=ci_features_df,
                clinical_data=clinical_data
            )

            return prob, pred

        except Exception as e:
            print(f"预测过程中出错: {mcg_file}, 错误: {e}")
            import traceback
            traceback.print_exc()
            return 0.0, 0

    def _direct_model_predict(self, mcg_file: str, base_feature_file: str,
                             cur_features_df: pd.DataFrame, ci_features_df: pd.DataFrame,
                             clinical_data: Dict) -> Tuple[float, int]:
        """
        直接调用模型预测，绕过批量处理逻辑
        """
        try:
            import pickle
            import os
            from pathlib import Path

            # 1. 加载模型文件
            model_dir = Path("files/saved_models") / (self.model_version or "v2.5.03")

            with open(model_dir / "model.pkl", 'rb') as f:
                model = pickle.load(f)

            with open(model_dir / "features.pkl", 'rb') as f:
                model_features = pickle.load(f)

            with open(model_dir / "scaler.pkl", 'rb') as f:
                scaler = pickle.load(f)

            print(f"已加载模型: {type(model).__name__}, 特征数: {len(model_features)}")

            # 2. 加载基础特征
            try:
                base_features_df = pd.read_pickle(base_feature_file)
                print(f"基础特征数量: {len(base_features_df.columns)}")
            except:
                print("基础特征加载失败，使用空特征")
                base_features_df = pd.DataFrame({'mcg_file': [mcg_file]})

            # 3. 合并所有特征
            # 合并cur特征
            if len(cur_features_df.columns) > 1:  # 有实际特征
                features_df = pd.merge(base_features_df, cur_features_df, on="mcg_file", how="left")
                print(f"合并cur特征后: {len(features_df.columns)}列")
            else:
                features_df = base_features_df

            # 合并ci特征
            if len(ci_features_df.columns) > 1:  # 有实际特征
                features_df = pd.merge(features_df, ci_features_df, on="mcg_file", how="left")
                print(f"合并ci特征后: {len(features_df.columns)}列")

            # 4. 添加临床特征
            for key, value in clinical_data.items():
                features_df[key] = value

            # 5. 生成临床衍生特征（BMI等）
            features_df = self._generate_clinical_features(features_df)

            # 6. 特征对齐和选择
            # 只保留模型需要的特征
            available_features = [f for f in model_features if f in features_df.columns]
            missing_features = [f for f in model_features if f not in features_df.columns]

            print(f"可用特征: {len(available_features)}/{len(model_features)}")
            if missing_features:
                print(f"缺失特征数量: {len(missing_features)}")
                print("缺失特征详细列表:")
                for i, feature in enumerate(missing_features):
                    print(f"  {i+1:2d}. {feature}")

                # 分析缺失特征类型
                clinic_missing = [f for f in missing_features if 'clinic' in f]
                cur_missing = [f for f in missing_features if 'cur_' in f]
                ci_missing = [f for f in missing_features if 'ci_' in f]
                other_missing = [f for f in missing_features if not any(x in f for x in ['clinic', 'cur_', 'ci_'])]

                print(f"缺失特征分类:")
                print(f"  - 临床特征: {len(clinic_missing)}个")
                print(f"  - cur特征: {len(cur_missing)}个")
                print(f"  - ci特征: {len(ci_missing)}个")
                print(f"  - 其他特征: {len(other_missing)}个")

                if clinic_missing:
                    print("缺失的临床特征:", clinic_missing)
                if cur_missing:
                    print("缺失的cur特征:", cur_missing)
                if ci_missing:
                    print("缺失的ci特征:", ci_missing)
                if other_missing:
                    print("缺失的其他特征:", other_missing)

                # 为缺失特征填充默认值
                for feature in missing_features:
                    if 'clinic' in feature:
                        features_df[feature] = -1  # 临床特征用-1填充
                    else:
                        features_df[feature] = 0   # 其他特征用0填充

            # 按模型特征顺序选择特征
            X = features_df[model_features].values

            # 7. 数据缩放
            print(f"原始特征范围: min={X.min():.4f}, max={X.max():.4f}, mean={X.mean():.4f}")
            X_scaled = scaler.transform(X)
            print(f"缩放后特征范围: min={X_scaled.min():.4f}, max={X_scaled.max():.4f}, mean={X_scaled.mean():.4f}")

            # 8. 模型预测
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X_scaled)[0]
                prob = float(proba[1]) if len(proba) > 1 else float(proba[0])
            else:
                prob = float(model.predict(X_scaled)[0])

            pred = 1 if prob > 0.5 else 0

            print(f"预测完成: 概率={prob:.4f}, 类别={pred}")
            return prob, pred

        except Exception as e:
            print(f"直接模型预测失败: {e}")
            import traceback
            traceback.print_exc()
            return 0.0, 0

    def _generate_clinical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成临床衍生特征"""
        try:
            # 1. 计算BMI
            if 'clinic_height' in df.columns and 'clinic_weight' in df.columns:
                height_m = df['clinic_height'] / 100  # 转换为米
                df['clinic_bmi'] = df['clinic_weight'] / (height_m ** 2)

            # 2. 医院独热编码 (确保生成所有可能的医院独热编码列)
            if 'clinic_hospital' in df.columns:
                # 初始化所有可能的医院独热编码列为0
                for i in range(6):  # 0-5，共6个医院
                    df[f'clinic_hospital_onehot_{i}'] = 0

                # 设置对应医院为1
                hospital_value = df['clinic_hospital'].iloc[0]
                if hospital_value in [0, 1, 2, 3, 4, 5]:
                    df[f'clinic_hospital_onehot_{hospital_value}'] = 1
                elif hospital_value == 7:  # 其它医院映射到5
                    df[f'clinic_hospital_onehot_5'] = 1

            # 3. 统计特征
            if 'clinic_age' in df.columns:
                df['stat_age_decade'] = df['clinic_age'] // 10  # 年龄段
                df['stat_is_elderly'] = (df['clinic_age'] >= 65).astype(int)  # 是否老年人

            if 'clinic_bmi' in df.columns:
                df['stat_is_overweight'] = (df['clinic_bmi'] >= 25).astype(int)  # 是否超重

            # 4. 数值统计特征
            numeric_cols = ['clinic_age', 'clinic_height', 'clinic_weight']
            available_numeric = [col for col in numeric_cols if col in df.columns]
            if available_numeric:
                numeric_values = df[available_numeric].iloc[0].values
                df['stat_numeric_mean'] = numeric_values.mean()
                df['stat_numeric_std'] = numeric_values.std() if len(numeric_values) > 1 else 0

            # 5. 领域交互特征
            self._generate_domain_features(df)

            return df

        except Exception as e:
            print(f"生成临床特征时出错: {e}")
            import traceback
            traceback.print_exc()
            return df

    def _generate_domain_features(self, df: pd.DataFrame):
        """生成领域特征（交互特征和风险评分）"""
        try:
            # 获取基础特征值
            age = df.get('clinic_age', pd.Series([0])).iloc[0]
            bmi = df.get('clinic_bmi', pd.Series([0])).iloc[0]
            gender = df.get('clinic_gender', pd.Series([0])).iloc[0]
            hypertension = df.get('clinic_hypertension', pd.Series([0])).iloc[0]
            diabetes = df.get('clinic_diabetes', pd.Series([0])).iloc[0]
            smoking = df.get('clinic_smoking', pd.Series([0])).iloc[0]
            drinking = df.get('clinic_drinking', pd.Series([0])).iloc[0]
            intervention = df.get('clinic_intervention', pd.Series([0])).iloc[0]
            symp = df.get('clinic_symp', pd.Series([0])).iloc[0]

            # 年龄相关交互特征
            df['domain_age_x_bmi'] = age * bmi
            df['domain_age_x_hypertension'] = age * hypertension
            df['domain_age_x_diabetes'] = age * diabetes
            df['domain_age_x_intervention'] = age * intervention
            df['domain_age_x_symp'] = age * symp

            # BMI相关交互特征
            df['domain_bmi_x_hypertension'] = bmi * hypertension
            df['domain_bmi_x_diabetes'] = bmi * diabetes
            df['domain_bmi_x_intervention'] = bmi * intervention
            df['domain_bmi_x_symp'] = bmi * symp

            # 性别相关交互特征
            df['domain_gender_x_intervention'] = gender * intervention
            df['domain_gender_x_symp'] = gender * symp

            # 组合特征
            df['domain_smoke_and_drink'] = smoking * drinking

            # 风险评分特征
            # 基础风险评分（年龄 + BMI + 高血压 + 糖尿病）
            basic_risk = (age / 100) + (bmi / 50) + hypertension + diabetes
            df['domain_age_x_risk_score'] = age * basic_risk
            df['domain_bmi_x_risk_score'] = bmi * basic_risk

            # 综合风险评分
            comprehensive_risk = basic_risk + smoking + drinking + intervention + symp
            df['domain_comprehensive_risk_score'] = comprehensive_risk

        except Exception as e:
            print(f"生成领域特征时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def process_batch_files(self, txt_folder: str, excel_path: str, 
                          output_path: str = None) -> pd.DataFrame:
        """
        批量处理文件
        Args:
            txt_folder: txt文件夹路径
            excel_path: 临床信息Excel路径
            output_path: 输出结果路径
        Returns:
            pd.DataFrame: 预测结果
        """
        # 读取临床信息
        df_info = pd.read_excel(excel_path, sheet_name='测试用')
        
        if '心磁号' not in df_info.columns:
            raise ValueError("Excel文件中必须包含'心磁号'列")
        
        mcg_ids = df_info['心磁号'].astype(str).tolist()
        results = []
        
        # 批量处理
        for mcg_id in tqdm(mcg_ids, desc="处理文件"):
            txt_file = Path(txt_folder) / f"{mcg_id}.txt"
            
            if not txt_file.exists():
                logging.warning(f"未找到对应的txt文件：{txt_file}")
                continue
            
            # 获取临床信息
            info_row = df_info[df_info['心磁号'].astype(str) == mcg_id]
            
            # 构建临床数据字典（使用新版本的字段映射）
            clinical_data = self._extract_clinical_data(info_row, df_info.columns)
            
            try:
                # 执行预测
                prob, pred = self.predict_single_file(str(txt_file), clinical_data)
                
                # 构建结果
                result_dict = {
                    'mcg_file': mcg_id,
                    'predicted_prob': prob,
                    'predicted_class': pred,
                }
                
                # 添加原始临床信息
                for col in df_info.columns:
                    if col != '心磁号':
                        result_dict[col] = info_row[col].iloc[0]
                
                results.append(result_dict)
                
            except Exception as e:
                print(f"处理{txt_file}时出错: {e}")
                continue
        
        # 创建结果DataFrame
        results_df = pd.DataFrame(results)
        
        # 保存结果
        if output_path:
            results_df.to_excel(output_path, index=False)
            print(f"结果已保存到: {output_path}")
        
        return results_df
    
    def _extract_clinical_data(self, info_row: pd.DataFrame, columns: list) -> Dict:
        """提取临床数据"""
        clinical_mapping = {
            '临床特征-身高': 'clinic_height',
            '临床特征-体重': 'clinic_weight', 
            '临床特征-性别': 'clinic_gender',
            '临床特征-年龄': 'clinic_age',
            '临床特征-吸烟': 'clinic_smoking',
            '临床特征-饮酒': 'clinic_drinking',
            '临床特征-高血压': 'clinic_hypertension',
            '临床特征-高脂血症': 'clinic_hyperlipidemia',
            '临床特征-既往介入': 'clinic_intervention',
            '临床特征-所在医院': 'clinic_hospital',
            '临床特征-糖尿病': 'clinic_diabetes',
            '临床特征-典型症状': 'clinic_symp'
        }
        
        clinical_data = {}
        for excel_col, clinic_key in clinical_mapping.items():
            if excel_col in columns:
                value = info_row[excel_col].iloc[0]
                if pd.notna(value):
                    clinical_data[clinic_key] = value
        
        return clinical_data


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='新版本ML Pipeline部署脚本')
    
    # 模式选择
    parser.add_argument('--mode', choices=['single', 'batch'], default='single',
                       help='运行模式：single(单文件) 或 batch(批量处理)')
    
    # 单文件模式参数
    parser.add_argument('--txt_file', help='txt文件路径（单文件模式）')
    
    # 批量模式参数  
    parser.add_argument('--txt_folder', help='txt文件夹路径（批量模式）')
    parser.add_argument('--excel_path', help='临床信息Excel文件路径')
    parser.add_argument('--output_path', help='输出结果路径')
    
    # 模型参数
    parser.add_argument('--model_version', help='指定模型版本（可选）')
    
    # 临床参数（单文件模式）
    parser.add_argument('--clinic_gender', type=int, choices=[0, 1], help='性别')
    parser.add_argument('--clinic_age', type=int, help='年龄')
    parser.add_argument('--clinic_hospital', type=int, help='医院编码')
    parser.add_argument('--clinic_diabetes', type=int, choices=[0, 1], help='糖尿病')
    parser.add_argument('--clinic_symp', type=int, choices=[0, 1], help='典型症状')
    parser.add_argument('--clinic_height', type=float, help='身高')
    parser.add_argument('--clinic_weight', type=float, help='体重')
    parser.add_argument('--clinic_smoking', type=int, choices=[0, 1], help='吸烟')
    parser.add_argument('--clinic_drinking', type=int, choices=[0, 1], help='饮酒')
    parser.add_argument('--clinic_hypertension', type=int, choices=[0, 1], help='高血压')
    parser.add_argument('--clinic_hyperlipidemia', type=int, choices=[0, 1], help='高脂血症')
    parser.add_argument('--clinic_intervention', type=int, choices=[0, 1], help='既往介入')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()
    
    # 创建部署管道
    pipeline = DeploymentPipeline(model_version=args.model_version)
    
    try:
        if args.mode == 'single':
            # 单文件模式
            if not args.txt_file:
                print("单文件模式需要指定--txt_file参数")
                sys.exit(1)
            
            # 构建临床数据
            clinical_data = {k: v for k, v in {
                'clinic_gender': args.clinic_gender,
                'clinic_age': args.clinic_age,
                'clinic_hospital': args.clinic_hospital,
                'clinic_diabetes': args.clinic_diabetes,
                'clinic_symp': args.clinic_symp,
                'clinic_height': args.clinic_height,
                'clinic_weight': args.clinic_weight,
                'clinic_smoking': args.clinic_smoking,
                'clinic_drinking': args.clinic_drinking,
                'clinic_hypertension': args.clinic_hypertension,
                'clinic_hyperlipidemia': args.clinic_hyperlipidemia,
                'clinic_intervention': args.clinic_intervention,
            }.items() if v is not None}
            
            # 执行预测
            prob, pred = pipeline.predict_single_file(args.txt_file, clinical_data)
            print(f"预测结果: 概率={prob:.4f}, 类别={pred}")
            
        elif args.mode == 'batch':
            # 批量模式
            if not all([args.txt_folder, args.excel_path]):
                print("批量模式需要指定--txt_folder和--excel_path参数")
                sys.exit(1)
            
            # 执行批量预测
            results_df = pipeline.process_batch_files(
                txt_folder=args.txt_folder,
                excel_path=args.excel_path,
                output_path=args.output_path
            )
            
            print(f"批量处理完成，共处理{len(results_df)}个文件")
            
    except Exception as e:
        print(f"执行过程中出错: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
