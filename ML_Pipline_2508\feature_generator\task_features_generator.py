"""
@Project ：002_Code_envs
@File    ：task_features_generator.py
@IDE     ：PyCharm
<AUTHOR> Qu
@Date    ：2024/1/9 15:48
@Modified: <PERSON><PERSON>
@Mod_Date: 2024/7/16
@Discribe：
    提取特征主脚本
"""

import glob
import logging
import os
import pickle
import time
import warnings
import pandas as pd
import feature_generator.utils.time_locs1 as time_locs1
from constructed_indicator.cal_mts import get_constructed_indicator
from tqdm import tqdm
from feature_generator.utils.feature_rigist import FeatureRegistry, \
    feature_regis  # , FeatureRegistry_v2, feature_regis_v2
from feature_generator.main_feature_generator import FeatureGenerator

warnings.filterwarnings("ignore")
error_msg = []
logging.basicConfig(level=logging.CRITICAL)


class TaskDirector:
    """
    任务调度器，生成批次特征。
    """

    def __init__(self):
        self.file_path = None
        self.files_tobe_gen_features = None
        self.file_label_df = None
        try:
            self.get_all_McgID_lable_df()
            print("成功获取标签数据")
        except:
            print("无法获取标签数据")

    def get_file(self, file_path):
        self.file_path = file_path

    @staticmethod
    def get_McgID_list_from_txt_dir(txt_dir_path='D:/wyh/Data/所有心磁数据2024-3-14'):
        """
        从所有数据根目录 获取txt文件列表
        :param txt_dir_path: 所有数据的根目录 目录内都是txt文件
        :return: 目录内文件名列表，不含后缀
        """
        # txt_dir_path = '/home/<USER>/data_raw/所有心磁数据2024-3-14'
        txt_files = glob.glob(os.path.join(txt_dir_path, "*.txt"))
        print(txt_files)
        # 获取所有txt文件名列表
        txt_filenames = [os.path.basename(file[:-4]) for file in txt_files]
        files_all_data = txt_filenames

        return files_all_data

    @staticmethod
    def get_McgID_list_from_excel(excel_dir_path='D:/wyh/Data/6月新增数据20240620/一致性建模数据.xlsx'):
        """
        从excel文件中获取McgID列表
        :param excel_dir_path:
        :return:
        """
        # excel_dir_path = r"/home/<USER>/data_raw/一致性建模数据.xlsx"
        excel_data = pd.ExcelFile(excel_dir_path)

        # 初始化一个空的DataFrame用于合并所有sheet的数据
        combined_data_from_sheets = pd.DataFrame()

        # 遍历所有sheet并合并数据
        for sheet in excel_data.sheet_names:
            sheet_data = pd.read_excel(excel_dir_path, sheet_name=sheet)
            combined_data_from_sheets = pd.concat([combined_data_from_sheets, sheet_data], ignore_index=True)

        # 仅保留列头为‘x’和‘y’的列数据
        combined_data = combined_data_from_sheets[['ID', '标签']]
        return combined_data

    @staticmethod
    def get_McgID_list_from_dataPkl(id_pkl="./files/data/data_index/data_V0624_consistency/Outer_Valid.pkl"):
        """
        从dataPkl文件中获取McgID列表
        :param id_pkl: 整理好的5折数据
        :return: mcg列表
        """
        # todo 注意feature应以数据版本以及抽取版本命名，每个pkl只应保留一个总集。
        with open(id_pkl, "rb") as f:
            id_df = pickle.load(f)
        # 初始化一个空集合，用于存储去重的 ID
        unique_ids = set()
        # 检查 'test', 'train_valid', 和 'train' 是否存在于数据中，并收集 IDs
        keys_to_check = ['test', 'train_valid', 'train', 'valid']
        for key in keys_to_check:
            if key in id_df[0]:
                # 将当前 key 的所有 ID 添加到集合中，自动去重
                unique_ids.update(id_df[0][key][:, 0])
        # 将集合转换为列表
        return list(unique_ids)

    @staticmethod
    def get_MCGID_list_from_featurePkl(
            feature_pkl="./files/saved_features/June_integrated_dataset_feature_all_inall.pkl"):
        with open(feature_pkl, "rb") as f:
            features_df = pickle.load(f)
        return features_df['mcg_file'].tolist()

    def get_task_McgID_list(self, id_pkl="./files/data/data_index/data_V0624_consistency/Outer_Valid.pkl",
                            feature_pkl="./files/saved_features/June_integrated_CI_feature_all_inall.pkl"):
        all_files = set(self.get_McgID_list_from_dataPkl(id_pkl))
        generated_files = set(self.get_MCGID_list_from_featurePkl(feature_pkl))
        missing_files_list = list(all_files - generated_files)
        self.files_tobe_gen_features = missing_files_list
        return missing_files_list

    def run_feature_gen_v1(self, files_list=None, save_flag=False, save_name='', remote=False):
        """
        负责批量数据的特征生成。 版本为44757列的特征。
        :param files_list: 待生成特征的文件列表
        :param save_flag: 是否保存
        :param save_name: 保存的名称
        :return:
        """
        if not files_list:
            files_list = self.files_tobe_gen_features
            # 生成数据集的时候建议 labels设置为真实标签，只用特征的话，可以全部设为0
        labels = self.get_labels_for_files(files_list)
        batch_size = 1
        w_list = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4]
        error_msg = []
        # i = 0
        results = []
        res_df = pd.DataFrame()
        # ------------------------------------  循环处理获取特征 --------------------------------------------
        for i in tqdm(range(0, len(files_list), batch_size)):
            try:
                num = int(i / batch_size)
                # 获取当前批次的文件和标签
                batch_files = files_list[i]
                batch_labels = labels[i]
                file_label_pairs = (batch_files, batch_labels)
                # 运行特征提取;
                # results = list(map(process_file_wrapper, file_label_pairs))
                # feature_generator方法--------------------------
                fg = FeatureGenerator(file_label_tuple=file_label_pairs, w_list=w_list, remote=remote)
                results = fg.get_all()

                if save_flag:
                    with open(r'./files/saved_features/uncombined/all_dataset'+str(num)+'.pkl', 'wb') as f:
                        pickle.dump(results, f)
                res_df = pd.concat([res_df, results])
            # except Exception as e:
            #     print(e)
            except:
                error_msg.append(files_list[i:i + batch_size])
                print("error files:", error_msg)

        return results, res_df

    def run_feature_gen_v2(self, tobe_generated_files=None, save_flag=False,
                           save_name='June_integrated_CI_feature_all_add', stats_Flag=True):
        """参数特征"""
        if not tobe_generated_files:
            tobe_generated_files = self.get_task_McgID_list()
        cons_features_df = []
        labels = self.get_labels_for_files(tobe_generated_files)
        e_file_list = []
        for file in tqdm(tobe_generated_files):
            try:
                # file_path =  r'/home/<USER>/data_raw/所有心磁数据2024-3-14/{}.txt'.replace('{}', file)
                file_path = r'D:/wyh/Data/所有心磁数据2024-3-14/{}.txt'.replace('{}', file)  # 测试用
                messages_path = './constructed_indicator/messages'
                result_peak = time_locs1.time_point(file_path)
                idxs = [result_peak['q-peak'], result_peak['r-peak'], result_peak['s-peak'],
                        result_peak['t-onset'], result_peak['t-peak'], result_peak['t-end']]
                t1 = time.time()
                cons_features_df.append(get_constructed_indicator(file_path, messages_path,
                                                                  idxs, stats_Flag))
                print("completed {} in {}s".format(file, time.time() - t1))
            except Exception as e:
                print(e)
                e_file_list.append(file)
                continue

        # 给结果加上全0label列
        # 保存特征
        cons_features_df_all = pd.concat(cons_features_df)
        cons_features_df_all['label'] = labels  # 添加全0的 label 列
        cons_features_df_all.rename(columns={'MCG_ID': 'mcg_file'}, inplace=True)  # 修改列名
        if save_flag:
            with open('./files/saved_features/uncombined/' + save_name + '.pkl', 'wb') as f:
                pickle.dump(cons_features_df_all, f)
        return cons_features_df_all

    def update_McgID_label_df_from_dataPkl(self,
                                           id_pkl="./files/data/data_index/data_V0624_consistency/Outer_Valid.pkl"):
        """
        从dataPkl文件中获取McgID和label的DataFrame，并更新self.file_label_df
        :param id_pkl: 整理好的5折数据
        """
        # todo 注意feature应以数据版本以及抽取版本命名，每个pkl只应保留一个总集。
        with open(id_pkl, "rb") as f:
            id_df = pickle.load(f)

        # 初始化一个空列表，用于存储所有的McgID和label
        all_data = []

        # 检查 'test', 'train_valid', 'train', 和 'valid' 是否存在于数据中，并收集数据
        keys_to_check = ['test', 'train_valid', 'train', 'valid']
        for key in keys_to_check:
            if key in id_df[0]:
                # 将当前 key 的所有 McgID 和 label 添加到列表中
                all_data.extend(id_df[0][key][:, :2].tolist())

        # 创建DataFrame
        new_df = pd.DataFrame(all_data, columns=['MCG_ID', 'label'])

        # 去除重复行
        new_df = new_df.drop_duplicates()

        # 更新 self.file_label_df
        if self.file_label_df is None:
            self.file_label_df = new_df
        else:
            # 合并新旧DataFrame，保留所有行，并去重
            self.file_label_df = pd.concat([self.file_label_df, new_df]).drop_duplicates().reset_index(drop=True)

        print(f"Updated self.file_label_df. Current shape: {self.file_label_df.shape}")

    def get_all_McgID_lable_df(self):
        self.update_McgID_label_df_from_dataPkl("./files/data/data_index/data_V0624_consistency/S42-F5.pkl")
        self.update_McgID_label_df_from_dataPkl("./files/data/data_index/data_V0624_consistency/Outer_Valid.pkl")
        self.update_McgID_label_df_from_dataPkl("./files/data/data_index/data_V0624_integrated/S42-F5.pkl")
        self.update_McgID_label_df_from_dataPkl("./files/data/data_index/data_V0624_integrated/Outer_Valid.pkl")

    def get_labels_for_files(self, files_list):
        """
        为给定的文件列表获取对应的标签
        :param files_list: MCG_ID列表
        :return: 对应的标签列表
        """
        if self.file_label_df is None:
            print("Warning: file_label_df is not initialized. All labels will be 0.")
            return [0] * len(files_list)

        labels = []
        for file in files_list:
            # 在file_label_df中查找对应的label
            label_row = self.file_label_df[self.file_label_df['MCG_ID'] == file]
            if not label_row.empty:
                labels.append(label_row['label'].values[0])
            else:
                # 如果在file_label_df中找不到对应的MCG_ID，使用0代替
                labels.append(0)
                print(f"Warning: MCG_ID {file} not found in file_label_df. Using 0 as label.")

        return labels


if __name__ == '__main__':
    # ---------------------------------------- 生成特征任务测试 ---------------------------------------------
    task = TaskDirector()
    # # 全新的特征生成
    all_files1 = task.get_McgID_list_from_dataPkl("./files/data/data_index/data_V0624_integrated/S42-F5.pkl")
    all_files2 = task.get_McgID_list_from_dataPkl("./files/data/data_index/data_V0624_consistency/S42-F5.pkl")
    all_files3 = task.get_McgID_list_from_dataPkl("./files/data/data_index/data_V0624_integrated/Outer_Valid.pkl")
    all_files4 = task.get_McgID_list_from_dataPkl("./files/data/data_index/data_V0624_consistency/Outer_Valid.pkl")
    tobe_gen = set(all_files1).union(set(all_files2)).union(set(all_files3)).union(set(all_files4))
    len(tobe_gen) #2534
    _, res_df = task.run_feature_gen_v1(files_list=['SHLY_2024_001041'], save_flag=False, remote=False)
    res_df.shape

    task.run_feature_gen_v2(tobe_generated_files=tobe_gen, save_flag=False,
                            save_name='June_integrated_CI_nonstats_feature_all', stats_Flag=False)

    # <editor-fold desc="1114数据特征检查与生成">
    # 1114数据特征检查与生成---------------------------------------------------------------------------------
    excel_path = "./files/data/data_index/data_V1114/诊断模型数据整理列20241114.xlsx"
    sheet_data = pd.read_excel(excel_path, sheet_name='20241114诊断建模名单含时刻点')
    all_files = sheet_data['心磁号']
    # 第二批数据
    excel_path = "./files/data/data_index/data_V1114/cons_plus/诊断模型数据12.3新增.xlsx"
    sheet_data = pd.read_excel(excel_path, sheet_name='总表')
    all_files = sheet_data['心磁号']
    tobe_gen = res_df['mcg_file'].values
    print(all_files.shape,tobe_gen.shape)

    # 外验数据
    excel_path =  'E:\个人临时文件夹\王月霞\新增六院验证数据2024-12-13\验证1213.xlsx'
    sheet_data = pd.read_excel(excel_path, sheet_name='Sheet1')
    all_files = sheet_data['心磁号']

    # 获得tobegen里没有但存在于all_files里的所有数据  两个都是list
    new_tobe_gen = list(set(all_files)-set(tobe_gen))
    len(new_tobe_gen)
    _, res_df = task.run_feature_gen_v1(files_list=list(set(all_files)), save_flag=False, remote=False)
    print(res_df.shape)
    # 从原特征表中选择  1104excel里包含的样本(根据all_files)，并剔除多余特征列(只保留res_df里存在的)
    # 从sheet_data中取标签和临床信息并入特征 clinec_+   lables


    # 将res_df的label标签置为sheet_data的"造影结果"列值，两dataframe根据res_df['mcg_file']和sheet_data的"心磁号"列对齐
    mapping_dict = dict(zip(sheet_data['心磁号'], sheet_data['造影结果']))
    res_df.loc[:, 'label'] = res_df['mcg_file'].map(mapping_dict)
    res_df.shape
    save_path = "./files/saved_features/feature_303tmp.pkl"
    with open(save_path, 'wb') as f:
        pickle.dump(res_df, f)

    # 读取原特征表
    feature_df_path = "./files/saved_features/features_V0821_all_tspt_2534.pkl"
    with open(feature_df_path, "rb") as f:
        features_df = pickle.load(f)

    # 选出原特征表里所有有关数据,即features_df["mcg_file"]在sheet_data['心磁号']中有的数据
    filtered_features_df = features_df[features_df['mcg_file'].isin(sheet_data['心磁号'])]

    # filtered_features_df的列中选出ml相关列，即剔除CI参数
    filtered_features_selected = filtered_features_df[res_df.columns]
    print(filtered_features_selected.shape)  # 应为 (1387, 44807)
    final_df = pd.concat([res_df, filtered_features_selected], axis=0, ignore_index=True)
    print(final_df.shape)  # 应为 (1690, 44807)

    # 保存一个feature_V241119_all_1690+?，用它选择特征 然后和临床特征合并，以及feature_V241119_all_clinic_1690+?
    save_path = "./files/saved_features/feature_V241119_all_1860.pkl"
    with open(save_path, 'wb') as f:
        pickle.dump(final_df, f)

    # with open(save_path, "rb") as f:
    #     old_res_df = pickle.load(f)
    # print(old_res_df.shape)
    # </editor-fold>

    # <editor-fold desc="1114外部医学验证集特征生成与整备">

    # 1.读取生成特征
    excel_path = "./files/data/data_index/data_V1114/临床外部验证3.xlsx"
    sheet_data = pd.read_excel(excel_path, sheet_name='广东2')
    test_files = sheet_data['ID']

    _, res_df = task.run_feature_gen_v1(files_list=list(test_files), save_flag=False, remote=False)
    print(res_df.shape)
    save_path = "./files/saved_features/feature_V241119_outer_gdnh_valid_50.pkl"
    with open(save_path, 'wb') as f:
        pickle.dump(res_df, f)
    # 2.加上临床特征生成总集
    clinic_sheet = sheet_data[['心磁号', '性别', '年龄', '典型症状', '糖尿病', '医院']]
    # 重命名列并进行特征编码
    feature_mapping = {
        '心磁号': 'mcg_file',
        '性别': 'clinic_gender',
        '年龄': 'clinic_age',
        '典型症状': 'clinic_symp',
        '糖尿病': 'clinic_diabetes',
        '医院': 'clinic_hospital'
    }

    # 重命名列
    clinic_sheet = clinic_sheet.rename(columns=feature_mapping)

    # 性别编码 (通常 1-男, 0-女)
    clinic_sheet['clinic_gender'] = (clinic_sheet['clinic_gender'] == '男').astype(int)

    # 医院编码 ，广东南海为5，上海六院为1,北京301为3
    clinic_sheet['clinic_hospital'] = clinic_sheet['clinic_hospital'].map({'广东南海': 5, '上海六院': 1
                                                                           ,'北京301': 3})
    # 处理异常列
    feature_cols = [col for col in clinic_sheet.columns if col != 'mcg_file']
    object_tobe_chan_cols = [col for col in clinic_sheet.columns
                             if col != 'mcg_file' and
                             (clinic_sheet[col].dtype == 'object' or
                              clinic_sheet[col].dtype == 'string')]
    # 对每个特征列进行处理
    from sklearn.preprocessing import LabelEncoder
    le = LabelEncoder()
    for col in object_tobe_chan_cols:
        # 先转换为字符串
        clinic_sheet[col] = clinic_sheet[col].astype(str)
        # 使用LabelEncoder编码
        clinic_sheet[col] = le.fit_transform(clinic_sheet[col])
        # 转换为float类型
        clinic_sheet[col] = clinic_sheet[col].astype(float)

    # 将处理后的特征列并入feature_V241114_all_1690
    merged_df = res_df.merge(
        clinic_sheet,
        on='mcg_file',
        how='left'
    )
    print(merged_df.shape)

    with open('./files/saved_features/feature_V241119_outer_shly_clinic_112.pkl', 'wb') as f:
        pickle.dump(merged_df, f)

    # 给clinic_sheet 添加一列全0的label列在第2列位置，然后保存为pickle
    clinic_sheet.insert(loc=1, column='label', value=0)
    with open('./files/data/data_index/data_V1114/Clinic_Outer_shly_Valid.pkl', 'wb') as f:
        pickle.dump(clinic_sheet.to_numpy(), f)

    # 新来的数据合并进来 ----------
    with open('./files/saved_features/feature_V241119_outer_clinic_178.pkl', 'rb') as f:
        old_df=pickle.load(f)

    all_merged_df = pd.concat([merged_df, old_df], axis=0, ignore_index=True)
    all_merged_df.shape
    with open('./files/saved_features/feature_V241119_outer_clinic_178.pkl', 'wb') as f:
        pickle.dump(all_merged_df, f)

    with open('./files/data/data_index/data_V1114/Clinic_Outer_Valid.pkl', 'rb') as f:
        old_clinic_sheet = pickle.load(f)

    import numpy as np
    all_clinic_sheet =  np.vstack((clinic_sheet.to_numpy(), old_clinic_sheet))
    all_clinic_sheet.shape
    with open('./files/data/data_index/data_V1114/Clinic_Outer_all_Valid_228.pkl', 'wb') as f:
        pickle.dump(all_clinic_sheet, f)

    # </editor-fold>

    # <editor-fold desc="微循环数据集特征检查与生成">
    # 微循环数据集特征检查与生成 ---------------------------------------------------------------------------------
    task = TaskDirector()

    excel_path = "./files/data/data_index/data_V1125_微循环/微循环1120建模.xlsx"  # 替换为你的Excel文件路径
    sheet_data = pd.read_excel(excel_path, sheet_name='Sheet1')
    all_files = sheet_data['心磁记录号']
    print(len(all_files))
    _, res_df = task.run_feature_gen_v1(files_list=list(all_files)[:3], save_flag=False, remote=False)
    print(res_df.shape)
    # test = res_df[['mcg_file','label']]
    save_path = "./files/saved_features/feature_V241119_mc_675.pkl"
    with open(save_path, 'wb') as f:
        pickle.dump(res_df, f)
    with open(save_path, 'rb') as f:
        res_df=pickle.load(f)
    # 处理临床特征
    clinic_sheet = sheet_data[['心磁记录号', '性别', '年龄']]
    # 重命名列并进行特征编码
    feature_mapping = {
        '心磁记录号': 'mcg_file',
        '性别': 'clinic_gender',
        '年龄': 'clinic_age',
    }

    # 重命名列
    clinic_sheet = clinic_sheet.rename(columns=feature_mapping)
    # 性别编码 (通常 1-男, 0-女)
    clinic_sheet['clinic_gender'] = (clinic_sheet['clinic_gender'] == '男').astype(int)

    # 并入临床特征
    merged_df = res_df.merge(
        clinic_sheet,
        on='mcg_file',
        how='left'
    )
    print(merged_df.shape)
    # 保存总特征+临床特征
    with open('./files/saved_features/feature_V241119_mc_clinic_675.pkl', 'wb') as f:
        pickle.dump(merged_df, f)

    # 处理选择后特征+临床特征
    final_features_cols = final_features_df.columns.tolist()
    if 'mcg_file' in final_features_cols:
        final_features_cols.remove('mcg_file')
    if 'label' in final_features_cols:
        final_features_cols.remove('label')
    print(len(final_features_cols))
    # 获取clinic_sheet的特征列
    feature_cols = [col for col in clinic_sheet.columns if col != 'mcg_file']

    clinic_features = feature_cols

    # 创建子表，包含所需的特征列
    subset_cols = ['mcg_file', 'label'] + final_features_cols + clinic_features
    subset_df = merged_df[subset_cols].copy()
    print(subset_df.shape)

    with open('./files/saved_features/selected_features/features_V1119F5S42_mc_boruta_top196_clinic5.pkl', 'wb') as f:
        pickle.dump(subset_df, f)

    # </editor-fold>
