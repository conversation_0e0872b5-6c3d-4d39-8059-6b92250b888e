import inspect
from typing import Any, Dict, List


class RecursiveParameterGetter:
    def __init__(self):
        self.cache: Dict[str, Any] = {}
        self.raw_inputs: Dict[str, Any] = {}

    def set_raw_input(self, name: str, value: Any):
        self.raw_inputs[name] = value

    def get_parameter(self, param_name: str) -> Any:
        if param_name in self.cache:
            return self.cache[param_name]

        if param_name in self.raw_inputs:
            self.cache[param_name] = self.raw_inputs[param_name]
            return self.cache[param_name]

        method_name = f"fn_{param_name}"
        if hasattr(self, method_name):
            method = getattr(self, method_name)
            params = inspect.signature(method).parameters
            args = {}
            for param in params:
                if param != 'self':
                    args[param] = self.get_parameter(param)
            result = method(**args)
            self.cache[param_name] = result
            return result
        else:
            raise ValueError(f"No method found to generate parameter: {param_name}")

    def get_multiple_parameters(self, param_names: List[str]) -> Dict[str, Any]:
        return {name: self.get_parameter(name) for name in param_names}

    def fn_bulk_params(self) -> Dict[str, Any]:
        result = {}
        for i in range(5):  # 假设生成5个参数
            result[f"bulk_param_{i}"] = f"Bulk value {i}"
        return result

    def fn_a(self, raw_input: str) -> str:
        return f"A derived from {raw_input}"

    def fn_b(self, a: str) -> str:
        return f"B derived from {a}"

    def fn_c(self, a: str, b: str) -> str:
        return f"C derived from {a} and {b}"

    def fn_d(self, a: str, c: str) -> str:
        return f"D derived from {a} and {c}"

    def fn_intermediate(self, b: str, c: str) -> str:
        return f"Intermediate value from {b} and {c}"

# 使用示例
getter = RecursiveParameterGetter()

# 设置原始输入
getter.set_raw_input("raw_input", "Initial Value")

# 获取单个参数
print(getter.get_parameter("d"))

# 获取多个参数
print(getter.get_multiple_parameters(["a", "b", "c", "d"]))

# 获取中间参数
print(getter.get_parameter("intermediate"))

# 获取批量生成的参数
bulk_params = getter.get_parameter("bulk_params")
print(bulk_params)

# 获取批量参数中的特定参数
print(getter.get_parameter("bulk_param_3"))