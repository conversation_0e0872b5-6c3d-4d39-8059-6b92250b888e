"""
@Project：ML_pipline/model_trainer
@File   ：train_model_on_folds.py
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2023/7/24 10:35
@Discribe：
    改自qdg
"""

import pickle

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from model_trainer.models import (load_and_prepare_data, process_safe_features, Models,
                                  get_model_predict_results, plot_folds_results, load_config,
                                  CustomXGBClassifier, FocalXGBClassifier, CRFXGBClassifier, run_test_model, compute_metric)
from model_trainer.data_quality_selector import DataQualitySelector
import optuna
import copy
from imblearn.over_sampling import SMOTE
# plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号问题
from collections import Counter
import warnings
warnings.filterwarnings('ignore')


class OptunaModelTrainer:
    def __init__(
            self,
            n_trials: int = 100,
            timeout: int = None,
            study_name: str = "xgboost_optimization",
            metric_type: str = 'acc'
    ):
        self.n_trials = n_trials
        self.timeout = timeout
        self.study_name = study_name
        self.metric_type = metric_type
        self.best_trial_model = None  # 添加属性存储搜索过程中的最佳模型
        self.best_score = 0


    def optimize(self, train_data, valid_data, test_data):
        study = optuna.create_study(
            direction="maximize",
            study_name=self.study_name
        )

        def objective(trial):
            # 1. 选择损失函数类型
            loss_type = trial.suggest_categorical("loss_type", ["focal", "crf", "ghm", "ohem"])

            # 2. XGBoost基础参数优化
            xgb_params = {
                "max_depth": trial.suggest_int("max_depth", 3, 8),
                "learning_rate": trial.suggest_float("learning_rate", 1e-3, 0.1, log=True),
                "min_child_weight": trial.suggest_int("min_child_weight", 1, 7),
                "subsample": trial.suggest_float("subsample", 0.6, 1.0),
                "colsample_bytree": trial.suggest_float("colsample_bytree", 0.6, 1.0),
                "gamma": trial.suggest_float("gamma", 1e-8, 1.0, log=True),
                "lambda": trial.suggest_float("lambda", 1e-8, 1.0, log=True),
                "alpha": trial.suggest_float("alpha", 1e-8, 1.0, log=True)
            }

            # 3. 添加num_boost_round优化
            num_boost_round = trial.suggest_int("num_boost_round", 20, 1000)

            # 4. 损失函数特定参数优化
            loss_params = {}
            if loss_type == "focal":
                loss_params = {
                    "focal_alpha": trial.suggest_float("focal_alpha", 0.1, 0.9),
                    "focal_gamma": trial.suggest_float("focal_gamma", 0.5, 5.0)
                }
                model = FocalXGBClassifier(**xgb_params, **loss_params)
            elif loss_type == "crf":
                loss_params = {
                    "sigma": trial.suggest_float("crf_sigma", 0.5, 5.0),
                    "epsilon": trial.suggest_float("crf_epsilon", 1e-8, 1e-4, log=True)
                }
                model = CRFXGBClassifier(**xgb_params, **loss_params)
            elif loss_type in ["ghm", "ohem"]:
                if loss_type == "ghm":
                    loss_params = {
                        "bins": trial.suggest_int("ghm_bins", 10, 50),
                        "momentum": trial.suggest_float("ghm_momentum", 0.6, 0.9)
                    }
                else:  # ohem
                    loss_params = {
                        "rate": trial.suggest_float("ohem_rate", 0.7, 0.95)
                    }
                model = CustomXGBClassifier(
                    loss_type=loss_type,
                    loss_params=loss_params,
                    **xgb_params
                )

            # 5. 数据选择比例优化
            keep_train_rate = trial.suggest_float("keep_train_rate", 0.7, 1.0)

            # 6. 数据质量选择
            selector = DataQualitySelector(
                task_type='classification',
                final_keep_ratio=keep_train_rate
            )
            X_selected, y_selected = selector.select_quality_data(
                train_data[0],
                train_data[1]
            )

            # 7. 模型训练
            try:
                model.fit(
                    X_selected,
                    y_selected,
                    eval_set=[(valid_data[0], valid_data[1])],
                    verbose=False,
                    num_boost_round=num_boost_round  # 使用优化的num_boost_round
                )

                # 8. 验证集评估
                valid_pred = model.predict_proba(valid_data[0])[:, 1]
                valid_score = compute_metric(valid_data[1], valid_pred, self.metric_type)
                if self.best_score is None or valid_score > self.best_score:
                    self.best_score = valid_score
                    self.best_trial_model = copy.deepcopy(model)  # 保存模型副本
                return valid_score

            except Exception as e:
                print(f"Trial failed with error: {str(e)}")  # 打印具体错误信息
                print(f"Parameters: {trial.params}")
                return float('-inf')

        # 执行优化
        study.optimize(objective, n_trials=self.n_trials, timeout=self.timeout)

        # 使用最佳参数训练最终模型
        best_params = study.best_params
        final_model = self._train_final_model(best_params, train_data, valid_data)

        print("Best trial score:", self.best_score)
        valid_pred = final_model.predict_proba(valid_data[0])[:, 1]
        valid_score = compute_metric(valid_data[1], valid_pred, self.metric_type)
        print("Retrained model score:", valid_score)

        return final_model, best_params, study

    def _train_final_model(self, best_params, train_data, valid_data):
        # 从最佳参数中提取各组参数
        loss_type = best_params.pop("loss_type")
        keep_train_rate = best_params.pop("keep_train_rate")
        num_boost_round = best_params.pop("num_boost_round")  # 提取num_boost_round

        # 分离XGBoost参数和损失函数参数
        xgb_params = {}
        loss_params = {}

        for key, value in best_params.items():
            if any(key.startswith(prefix) for prefix in ["focal_", "crf_", "ghm_", "ohem_"]):
                clean_key = key.split("_", 1)[1]
                loss_params[clean_key] = value
            else:
                xgb_params[key] = value

        # 创建最终模型
        if loss_type == "focal":
            final_model = FocalXGBClassifier(**xgb_params, **loss_params)
        elif loss_type == "crf":
            final_model = CRFXGBClassifier(**xgb_params, **loss_params)
        else:
            final_model = CustomXGBClassifier(
                loss_type=loss_type,
                loss_params=loss_params,
                **xgb_params
            )

        # 数据选择
        selector = DataQualitySelector(
            task_type='classification',
            final_keep_ratio=keep_train_rate
        )
        X_selected, y_selected = selector.select_quality_data(train_data[0], train_data[1])

        # 训练最终模型
        final_model.fit(
            X_selected,
            y_selected,
            eval_set=[(valid_data[0], valid_data[1])],
            verbose=False,
            num_boost_round=num_boost_round  # 使用优化的num_boost_round
        )

        return final_model


def hospital_wise_smote(train_valid_data, excel_path, use_consistency=True,
                        selected_hospitals=None, sampling_ratios=None, mode='xgb',
                        use_any_positive=False):  # 新增参数
    """
    按医院分组进行SMOTE过采样
    对选定医院进行SMOTE处理，可配置每家医院的过采样程度

    Args:
        train_valid_data: 原始训练验证数据，包含mcg_file和label
        excel_path: Excel文件路径，包含造影结果和人工标注信息
        use_consistency: 是否只使用造影结果和人工标注一致的数据
        selected_hospitals: 指定要处理的医院列表，None表示处理所有医院
        sampling_ratios: 每个医院对应的采样比例列表，需与selected_hospitals长度相同
                        None表示所有医院使用1.0的采样比例
        mode: process_safe_features的模式参数
        use_any_positive: 是否使用任意一个标注为阳性时即判定为阳性的标签策略

    Returns:
        X_balanced: 平衡后的安全特征DataFrame
        y_balanced: 平衡后的标签数组
    """
    # 验证采样比例参数
    if selected_hospitals is not None and sampling_ratios is not None:
        if len(selected_hospitals) != len(sampling_ratios):
            raise ValueError("selected_hospitals 和 sampling_ratios 的长度必须相同")
        hospital_ratio_map = dict(zip(selected_hospitals, sampling_ratios))
    else:
        hospital_ratio_map = {}

    def get_sampling_ratio(hospital):
        """获取指定医院的采样比例"""
        if selected_hospitals is None:
            return 1.0
        elif hospital in hospital_ratio_map:
            return hospital_ratio_map[hospital]
        return None

    # 读取医院信息
    sheet_data = pd.read_excel(excel_path, sheet_name='总表2')
    cons_sheet = sheet_data[['心磁号', '造影结果', '人工', '医院']]

    if use_any_positive and not use_consistency:
        # 当任意一个为1时，新标签为1
        cons_sheet['new_label'] = ((cons_sheet['造影结果'] == 1) |
                                   (cons_sheet['人工'] == 1)).astype(int)
        # 创建mcg_file到标签的映射
        mcg_to_label = dict(zip(cons_sheet['心磁号'], cons_sheet['new_label']))
        # 创建mcg_file到医院的映射
        mcg_to_hospital = dict(zip(cons_sheet['心磁号'], cons_sheet['医院']))
        print("使用任意标注为阳性即判定为阳性的标签策略")
    else:
        # 根据参数决定是否使用一致性数据
        if use_consistency:
            cons_sheet = cons_sheet.dropna(subset=['人工'])
            cons_sheet = cons_sheet[cons_sheet['造影结果'] == cons_sheet['人工']]
            print("使用造影结果和人工标注一致的数据")

        # 创建mcg_file到医院的映射
        mcg_to_hospital = dict(zip(cons_sheet['心磁号'], cons_sheet['医院']))

    # 筛选数据并添加医院信息
    filtered_data = train_valid_data[train_valid_data['mcg_file'].isin(mcg_to_hospital.keys())].copy()
    filtered_data['hospital'] = filtered_data['mcg_file'].map(mcg_to_hospital)

    # 获取或更新标签
    if use_any_positive and not use_consistency:
        # 使用新的标签
        filtered_data['label'] = filtered_data['mcg_file'].map(mcg_to_label)

    y = filtered_data['label'].values

    # 处理安全特征
    safe_features = process_safe_features(filtered_data, mode=mode)
    # 在进行SMOTE之前，从safe_features中去除hospital字段
    if 'hospital' in safe_features.columns:
        safe_features = safe_features.drop('hospital', axis=1)

    # 初始化存储结果的列表
    balanced_features = []
    balanced_labels = []

    # 获取所有医院
    all_hospitals = filtered_data['hospital'].unique()

    # 如果没有指定医院，则处理所有医院
    if selected_hospitals is None:
        hospitals_to_process = all_hospitals
    else:
        hospitals_to_process = selected_hospitals

    print("\n开始按医院进行SMOTE过采样:")

    for hospital in all_hospitals:
        if pd.isna(hospital):
            continue

        # 获取该医院的数据
        hospital_mask = filtered_data['hospital'] == hospital
        hospital_features = safe_features[hospital_mask]
        hospital_labels = y[hospital_mask]

        # 如果是选定的医院，进行SMOTE处理
        sampling_ratio = get_sampling_ratio(hospital)
        if hospital in (selected_hospitals or all_hospitals):
            print(f"\n处理医院 {hospital}")
            print(f"采样比例: {sampling_ratio}")
            print(f"原始样本分布: {Counter(hospital_labels)}")

            if len(np.unique(hospital_labels)) < 2:
                print(f"警告：医院 {hospital} 只有一个类别，跳过SMOTE")
                balanced_features.append(hospital_features)
                balanced_labels.append(hospital_labels)
                continue

            try:
                # 计算采样策略
                class_counts = Counter(hospital_labels)
                min_samples = min(class_counts.values())
                max_samples = max(class_counts.values())

                # 根据采样比例计算目标样本数
                target_samples = int(min_samples + (max_samples - min_samples) * sampling_ratio)

                # 设置采样策略
                sampling_strategy = {}
                for label, count in class_counts.items():
                    if count < max_samples:
                        sampling_strategy[label] = target_samples
                    else:
                        sampling_strategy[label] = count

                # 对该医院数据进行SMOTE
                smote = SMOTE(random_state=42, sampling_strategy=sampling_strategy)
                X_hospital_balanced, y_hospital_balanced = smote.fit_resample(
                    hospital_features, hospital_labels)

                print(f"平衡后样本分布: {Counter(y_hospital_balanced)}")

                balanced_features.append(X_hospital_balanced)
                balanced_labels.append(y_hospital_balanced)

            except ValueError as e:
                print(f"警告：医院 {hospital} SMOTE失败 ({e})，使用原始数据")
                balanced_features.append(hospital_features)
                balanced_labels.append(hospital_labels)
        else:
            # 如果不是选定的医院，直接使用原始数据
            balanced_features.append(hospital_features)
            balanced_labels.append(hospital_labels)
    # 合并所有医院的结果
    X_balanced = pd.concat(balanced_features, axis=0, ignore_index=True) \
        if isinstance(balanced_features[0], pd.DataFrame) \
        else np.vstack(balanced_features)
    y_balanced = np.concatenate(balanced_labels)

    print("\n总体样本分布:")
    print(f"原始: {Counter(y)}")
    print(f"平衡后: {Counter(y_balanced)}")
    if selected_hospitals:
        print(f"处理医院数量: {len(selected_hospitals)}")
        print(f"保留原始数据的医院数量: {len(all_hospitals) - len(selected_hospitals)}")
    else:
        print(f"处理医院数量: {len(all_hospitals)}")

    return X_balanced, y_balanced


def func_feature_test(func_name='get_ts_features'):
    """对特定特征建模测试"""
    # 读取想要的特征
    from feature_generator.utils.feature_rigist import FeatureRegistry
    import numpy as np

    fg = FeatureRegistry()
    fg.reload_from_file("./files/configs/feature_registry_V2.json")
    selected_features_ts = FeatureRegistry.filter_features_for_function(func_name)

    params_ = {
        "feature_pkl": "./files/saved_features/features_V0821_all_tstt_2534.pkl",
        "id_pkl": "./files/data/data_index/data_V0820/S42-F5.pkl",
        "selected_feature_pkl": "./files/saved_features/integrated_all_features_stable_F5_s42_fold_all.pkl",
        "save_res_path": "./files/saved_models/xxx_test0820.pkl",
        "selected_feature_list": np.array(selected_features_ts)
    }
    save_res, avg = train_model_on_stable_folds(**params_, save_flag=False, mode='valid_result_threshold')
    return save_res, avg


def valid_best_model(best_res_path, best_fold,all_feature="./files/saved_features/features_V0821_all_tspt_2534.pkl",
                    selected_feature = "./files/saved_features/selected_features/features_V0826F5S42_tspt_boruta_top1369.pkl",
                     version='20240820_1754_351',id_pkl = "./files/data/data_index/data_V0826_cons/Outer_Valid.pkl"
                     ):
    # S42 fold 2
    from model_trainer.models import load_and_prepare_outer_data, run_test_model
    import pandas as pd
    import requests
    with open(best_res_path, 'rb') as f:
        best_res = pickle.load(f)
    best_model, best_threshold = best_res['models_trained'][best_fold], \
        best_res['results'][best_fold]['valid_result_threshold']['threshold']
    print(best_threshold)
    test_data = load_and_prepare_outer_data(
        id_pkl=id_pkl,
        features_pkl=all_feature,
        selected_features_pkl=selected_feature)
    # "selected_feature_pkl": "./files/saved_features/features_V0826F5S42_boruta_top1369_embed47.pkl",

    x_test, y_test = test_data.drop(columns=['mcg_file', 'label'], inplace=False), test_data['label']
    result_dict_outer_test = get_model_predict_results(model=best_model, x_test=x_test, y_test=y_test,
                                                       best_threshold=best_threshold, valid=True)
    y_prob, acc, result = run_test_model(best_model, x_test, y_test, dataset='test', threshold=best_threshold)
    y_pred = np.where(y_prob >= best_threshold, 1, 0)
    res = test_data[['mcg_file', 'label']]
    res['pred'] = list(y_pred)
    res['prob'] = list(y_prob)

    with open(id_pkl, 'rb') as f:
        outer_valid = pd.DataFrame(pickle.load(f))

    # 创建一个从文件名到标签的映射
    prob_map = dict(zip(res['mcg_file'], res['prob']))
    label_map = dict(zip(res['mcg_file'], res['pred']))

    # 使用 map 方法更新 df1 的标签列
    outer_valid.iloc[:, 1] = outer_valid.iloc[:, 0].map(prob_map)
    outer_valid.iloc[:, 2] = outer_valid.iloc[:, 0].map(label_map)
    outer_valid = np.array(outer_valid)

    # 保存
    with open('./files/results/tmptest.pkl', 'wb') as f:
        pickle.dump(outer_valid, f)
    # 上传测试
    predict_pickle = r'./files/results/tmptest.pkl'  # 修改此处为待评估模型结果pickle文件地址即可
    file_handle = open(predict_pickle, 'rb')
    # reponse = requests.post('http://**************:8042/calculate', files={'file': open(predict_pickle, 'rb')})
    response = requests.post('http://**************:8042/calculate',
                             files={'file': open(predict_pickle, 'rb')},
                             data={'version': version})
    file_handle.close()
    print(response.json())
    return response.json()


def train_model_on_folds(feature_pkl, id_pkl, selected_feature_pkl, save_res_path, save_flag=False):
    """
    Train and test the model on multiple folds of data.
    :param feature_pkl: Path to the feature pickle file.
    :param id_pkl: Path to the ID pickle file.
    :param selected_feature_pkl: Path to the selected feature pickle file.
    :param save_res_path: Path to the saved results file.
    :param save_flag: Whether to save the results.
    :return: Tuple containing the results of model training and testing on outer test data and the average result.
    """
    # 在训练-测试集预测-------综合模型/一致性模型    -- 不含调参直接训练 粗调模型训练 -- 每个fold使用不同的特征
    results = []
    models_trained = []
    for fold in range(5):
        models = Models()
        models.xgb_model()
        model, model_name = models.model, models.model_name

        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl=id_pkl,
            features_pkl=feature_pkl,
            selected_features_pkl=selected_feature_pkl + str(fold) + '_top70.pkl',
            fold=fold)

        # 获取无异常字符的安全特征 (为lgbm准备)
        safe_train_data, safe_test_data, safe_valid_data, safe_train_valid_data = [
            process_safe_features(x, mode=model_name) for x in [train_data, test_data, valid_data, train_valid_data]]
        # 作为一个修正，train_data 实际使用 test以外的所有数据，因为还有外部验证集合
        safe_train_data = safe_train_valid_data

        model.fit(safe_train_data, train_valid_data['label'])

        result_dict = get_model_predict_results(model=model,
                                                x_train=safe_train_data,
                                                y_train=train_valid_data['label'],
                                                x_test=safe_test_data,
                                                y_test=test_data['label'],
                                                x_valid=safe_valid_data,
                                                y_valid=valid_data['label'],
                                                )
        results.append(result_dict)
        models_trained.append(model)

    # 可视化训练结果
    avg = plot_folds_results(results, mode='test_result')
    # 保存模型
    save_res = {'models_trained': models_trained, 'results': results}
    if save_flag:
        with open(save_res_path, 'wb') as f:
            pickle.dump(save_res, f)

    return results, avg


def train_single_model(id_pkl, feature_pkl, selected_feature_pkl, selected_feature_list, fold):
    models = Models()
    models.xgb_model()
    model, model_name = models.model, models.model_name
    train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
        id_pkl=id_pkl,
        features_pkl=feature_pkl,
        selected_features_pkl=selected_feature_pkl,
        selected_feature_list=selected_feature_list,
        fold=fold)

    # 获取无异常字符的安全特征
    safe_train_data, safe_test_data, safe_valid_data, safe_train_valid_data = [
        process_safe_features(x, mode=model_name) for x in [train_data, test_data, valid_data, train_valid_data]]

    # ld = LowerDimension(safe_train_valid_data)
    # ld.lower_dimension()
    # final_train_data, final_test_data, final_valid_data, final_train_valid_data = [
    #     ld.lower_dimension(x) for x in [safe_train_data, safe_test_data, safe_valid_data, safe_train_valid_data]]

    # 作为一个修正，train_data 实际使用 test以外的所有数据，因为还有外部验证集合
    print(safe_train_valid_data.shape)
    model.fit(safe_train_valid_data, train_valid_data['label'])

    result_dict = get_model_predict_results(model=model,
                                            x_train=safe_train_valid_data,
                                            y_train=train_valid_data['label'],
                                            x_test=safe_test_data,
                                            y_test=test_data['label'],
                                            x_valid=safe_valid_data,
                                            y_valid=valid_data['label'],
                                            )
    return model, result_dict


def train_model_on_stable_folds(feature_pkl, id_pkl, selected_feature_pkl, save_res_path, save_flag=False,
                                selected_feature_list=None,keep_train_rate = 1.0,
                                mode='valid_result_threshold'):
    """
    Train and test the model on multiple folds of data.
    :param feature_pkl: Path to the feature pickle file.
    :param id_pkl: Path to the ID pickle file.
    :param selected_feature_pkl: Path to the selected feature pickle file.
    :param save_res_path: Path to the saved results file.
    :param save_flag: Whether to save the results.
    :return: Tuple containing the results of model training and testing on outer test data and the average result.
    """
    # 每个fold 使用相同特征
    results = []
    models_trained = []
    importanceframe = []
    for fold in range(5):
        models = Models()
        models.xgb_model()
        model, model_name = models.model, models.model_name
        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl=id_pkl,
            features_pkl=feature_pkl,
            selected_features_pkl=selected_feature_pkl,
            selected_feature_list=selected_feature_list,
            fold=fold)
        # 按人工造影一致性重新赋予标签训练  —————————————————————————————————————————————————————————————————————————
        # 读取Excel数据
        # excel_path = "./files/data/data_index/data_V1114/cons/诊断模型数据11.29新修.xlsx"
        # sheet_data = pd.read_excel(excel_path, sheet_name='Sheet1')
        # cons_sheet = sheet_data[['心磁号', '造影结果', '人工','医院']]
        # # cons_sheet = cons_sheet[cons_sheet['医院']!='广东南海']#.isin(['上海六院', '上海十院','北京301'])]
        #
        # # 根据新逻辑计算标签
        # def calculate_new_label(row):
        #     # 如果人工或造影结果任意一个为1，则返回1
        #     if row['造影结果'] == 1 or row['人工'] == 1:
        #         return 1
        #     return 0
        #
        # cons_sheet['new_label'] = cons_sheet.apply(calculate_new_label, axis=1)
        # labels_dict = dict(zip(cons_sheet['心磁号'], cons_sheet['new_label']))
        #
        # # 定义更新label的函数
        # def update_data_labels(dataset):
        #     # 更新label列
        #     dataset['label'] = dataset['mcg_file'].map(labels_dict)
        #     # 对于未在labels_dict中的数据，保持原label不变
        #     dataset['label'] = dataset['label'].fillna(dataset['label'])
        #     dataset = dataset[dataset['mcg_file'].isin(cons_sheet['心磁号'])]
        #
        #     return dataset
        #
        # # 更新所有数据集的label
        # train_data = update_data_labels(train_data)
        # test_data = update_data_labels(test_data)
        # valid_data = update_data_labels(valid_data)
        # train_valid_data = update_data_labels(train_valid_data)
        #
        # # 打印统计信息
        # def print_dataset_stats(name, data):
        #     print(f"\n{name}标签统计:")
        #     print(f"总样本数: {len(data)}")
        #     print(f"标签为1的样本数: {(data['label'] == 1).sum()}")
        #     print(f"标签为0的样本数: {(data['label'] == 0).sum()}")
        #     print(f"标签1占比: {(data['label'] == 1).sum() / len(data) * 100:.2f}%")
        #
        # # 打印所有数据集的统计信息
        # print_dataset_stats("训练集", train_data)
        # print_dataset_stats("测试集", test_data)
        # print_dataset_stats("验证集", valid_data)
        # print_dataset_stats("训练验证集", train_valid_data)
        # 根据是否一致加权 ——————————————————————————————————————————————————————————————————————————————————————————
        # 首先读取数据表
        excel_path = "./files/data/data_index/data_V1114/cons/诊断模型数据11.29新修.xlsx"
        # excel_path ="./files/data/data_index/data_V1114/cons_plus/诊断模型数据12.3新增.xlsx"
        sheet_data = pd.read_excel(excel_path, sheet_name='Sheet1')
        cons_sheet = sheet_data[['心磁号', '造影结果', '人工','医院']]
        # 在计算权重之前，先剔除指定数据
        # exclude_condition = (cons_sheet['医院'] == '广东南海') & (cons_sheet['人工'] == 1) & (
        #             cons_sheet['造影结果'] == 0)
        # excluded_mcg = cons_sheet[exclude_condition]['心磁号'].tolist()
        # print(f"被剔除的广东南海样本数量: {len(excluded_mcg)}")
        #
        # # 移除这些样本
        # cons_sheet = cons_sheet[~exclude_condition]
        # 创建一个字典来存储每个心磁号的权重
        def calculate_weights(row):
            # 如果人工标注为空，给予默认权重1
            if pd.isna(row['人工']):
                return 1.0

            # 一致性数据权重为1
            if row['造影结果'] == row['人工']:
                return 1.0

            # 不一致数据中：
            # 人工阴性(0)，造影阳性(1) - 加强权重
            if row['人工'] == 0 and row['造影结果'] == 1:
                return 1.5  # 可以调整这个权重值

            # 人工阳性(1)，造影阴性(0) - 减弱权重
            if row['人工'] == 1 and row['造影结果'] == 0:
                return 1.5  # 可以调整这个权重值

            return 1.0  # 其他情况默认权重为1

        # 计算每个心磁号对应的权重
        cons_sheet['weight'] = cons_sheet.apply(calculate_weights, axis=1)
        weights_dict = dict(zip(cons_sheet['心磁号'], cons_sheet['weight']))

        # 定义更新label和权重的函数
        def update_data_with_weights(input_dataset):
            # 给数据集添加标签和权重
            dataset = input_dataset.copy()
            dataset['consistency_label'] = dataset['mcg_file'].apply(
                lambda x: 1 if x in cons_sheet[cons_sheet['造影结果'] == cons_sheet['人工']]['心磁号'].values else 0
            )

            # 添加权重列
            dataset['weight'] = dataset['mcg_file'].map(weights_dict)
            # 对于未在权重字典中的数据，设置默认权重为1
            dataset['weight'] = dataset['weight'].fillna(1.0)

            return dataset

        # 更新所有数据集
        w_train_data = update_data_with_weights(train_data)
        w_test_data = update_data_with_weights(test_data)
        w_valid_data = update_data_with_weights(valid_data)
        w_train_valid_data = update_data_with_weights(train_valid_data)

        # 打印统计信息
        def print_dataset_stats(name, data):
            print(f"\n{name}统计信息:")
            print(f"总样本数: {len(data)}")
            print("一致性样本数:", len(data[data['consistency_label'] == 1]))
            print("不一致样本数:", len(data[data['consistency_label'] == 0]))
            print("权重分布:")
            print(data['weight'].value_counts().sort_index())

        # 打印所有数据集的统计信息
        print_dataset_stats("训练集", w_train_data)
        print_dataset_stats("测试集", w_test_data)
        print_dataset_stats("验证集", w_valid_data)
        print_dataset_stats("训练验证集", w_train_valid_data)
        # 仅使用一致性数据 ——————————————————————————————————————————————————————————————————————————————————————————
        # excel_path = "./files/data/data_index/data_V1114/cons/诊断模型数据11.29新修.xlsx"
        # sheet_data = pd.read_excel(excel_path, sheet_name='Sheet1')
        # cons_sheet = sheet_data[['心磁号', '造影结果', '人工', '医院']]
        # # 选出cons_sheet中 造影结果和人工一致的数据
        # cons_sheet = cons_sheet.dropna(subset=['人工'])
        # cons_sheet = cons_sheet[cons_sheet['造影结果'] == cons_sheet['人工']]
        # cons_sheet = cons_sheet[cons_sheet['医院'] .isin(['上海六院', '上海十院','北京301'])]

        # print(cons_sheet.shape)
        # 选出train_valid_data中心磁号在cons_sheet中的数据
        # train_valid_data = train_valid_data[train_valid_data['mcg_file'].isin(cons_sheet['心磁号'])]
        # ——————————————————————————————————————————————————————————————————————————————————————————————————————————
        # 分医院建模 ——————————————————————————————————————————————————————————————————————————————————————————
        # excel_path = "./files/data/data_index/data_V1114/cons/诊断模型数据11.29新修.xlsx"
        # sheet_data = pd.read_excel(excel_path, sheet_name='Sheet1')
        # cons_sheet = sheet_data[['心磁号', '造影结果', '人工', '医院']]
        # # 选出cons_sheet中 造影结果和人工一致的数据
        # cons_sheet = cons_sheet[cons_sheet['医院'].isin(['广东南海', '上海十院'])]
        # # 选出train_valid_data中心磁号在cons_sheet中的数据
        # train_valid_data = train_valid_data[train_valid_data['mcg_file'].isin(cons_sheet['心磁号'])]
        # ——————————————————————————————————————————————————————————————————————————————————————————————————————————
        # 获取无异常字符的安全特征
        print(f"using {model_name} features for test(standard scaling) mode")
        safe_train_data, safe_test_data, safe_valid_data, safe_train_valid_data = [
            process_safe_features(x, mode='test') for x in [train_data, test_data, valid_data, train_valid_data]]

        # 直接过采样测试 —————————————————————————————————————————————————————————————————————————————————————————————————
        # smote = SMOTE(random_state=42)
        # X_balanced, y_balanced = smote.fit_resample(safe_train_valid_data, train_valid_data['label'])

        # 分医院过采样测试 ——————————————————————————————————————————————————————————————————————————————————————————————
        excel_path = "./files/data/data_index/data_V1114/cons_plus/诊断模型数据12.3新增.xlsx"

        # 使用一致性数据，处理所有医院
        # X_balanced, y_balanced = hospital_wise_smote(
        #     train_valid_data=train_valid_data,
        #     excel_path=excel_path,
        #     use_consistency=False,
        #     use_any_positive=False,
        #     selected_hospitals=None, #['北京301','安医','广东南海','上海六院'],
        #     sampling_ratios = None #[0,1,1,0]
        # )
        X_balanced, y_balanced =safe_train_valid_data, train_valid_data['label']
        # 查看样本数量
        print("Original dataset shape:", dict(zip(*np.unique(train_valid_data['label'], return_counts=True))))
        print("Balanced dataset shape:", dict(zip(*np.unique(y_balanced, return_counts=True))))

        # # 数据选择 --------------------------------------------------------------------------
        # selector = DataQualitySelector(
        #     task_type='classification',
        #     final_keep_ratio=keep_train_rate
        # )
        # print("Start to select quality data ...")
        # X_selected, y_selected = selector.select_quality_data(safe_train_data, train_data['label'])
        # print("Finish to select quality data ...")
        # print(safe_train_valid_data.shape,"-->",X_selected.shape)

        # 数据加权 --------------------------------------------------------------------------
        # optimizer = EnhancedMetaWeightOptimizer(
        #     task_type='classification',
        #     meta_iterations=3,
        #     meta_lr=0.2,
        #     n_weight_groups=3,
        #     n_splits=8
        # )
        # weights = optimizer.get_suggested_weights(X.values, y.values)

        # optimizer = ImprovedHessianOptimizer(n_splits=30, test_size=0.1)

        # 损失函数调节 ------------------------------------------------------------------------
        # 搜索准备
        X, y, X_val, y_val = safe_train_valid_data, train_valid_data['label'], safe_test_data, test_data['label']
        eval_set = [(X_val, y_val)]

        # # focal loss
        # zero_ratio = (train_valid_data['label'] == 0).mean()
        # gamma_value = [1,1.5,2,2.5,3,3.5,4,4.5]
        # gamma_results = {}
        # for gamma in gamma_value:
        #     model = FocalXGBClassifier(**models.params, focal_alpha=1 / zero_ratio,
        #                                focal_gamma=gamma)  # ,sample_weight=weights)
        #     model.fit(X, y, eval_set=eval_set, verbose=False,sample_weight=weights)
        #     # 计算验证集得分
        #     _,_,res_list = run_test_model(model, X_val, y_val, dataset='train ')
        #     print(gamma,":",res_list['auc'])
        #     gamma_results[gamma] = res_list['auc']
        # best_gamma = max(gamma_results.items(), key=lambda x: x[1])[0]
        #
        # model = FocalXGBClassifier(**models.params,focal_alpha=1/zero_ratio,focal_gamma=best_gamma)
        # ghm loss
        # model = CustomXGBClassifier(**models.params,loss_type='ghm',loss_params={'bins':30, 'momentum':0.75})
        # # CRF
        # # 数据加权
        # # weights = optimizer.get_suggested_weights(X.values, y.values,conflict_percentile=20)
        # sigma_values = [0.5, 0.8,1.0,1.5, 2.0,3.0,4.0, 5.0]
        # # sigma_values从0.5到5.0，步长为0.2
        # # sigma_values = np.arange(0.5, 5.1, 0.2).tolist()
        #
        # sigma_results = {}
        # print('损失函数参数搜索...')
        # for sigma in sigma_values:
        #     model = CRFXGBClassifier(
        #         **models.params, sigma=sigma,
        #     )
        #     model.fit(X, y, eval_set=eval_set, verbose=False,sample_weight=w_train_valid_data['weight'].values)
        #     # 计算验证集得分
        #     _,_,res_list = run_test_model(model, X_val, y_val, dataset='train ')
        #     # print(sigma,":",res_list['auc'])
        #     sigma_results[sigma] = res_list['auc']
        # best_sigma = max(sigma_results.items(), key=lambda x: x[1])[0]
        # print(f'最佳损失函数参数{best_sigma}.')
        #
        # model = CRFXGBClassifier(**models.params,sigma=best_sigma)
        # 训练模型获取结果
        # weights = optimizer.get_suggested_weights(safe_train_valid_data.values, train_valid_data['label'].values)

        model.fit(X_balanced, y_balanced)#,sample_weight=w_train_valid_data['weight'].values)
        result_dict = get_model_predict_results(model=model,
                                                x_train=safe_train_valid_data,
                                                y_train=train_valid_data['label'],
                                                x_test=safe_test_data,
                                                y_test=test_data['label'],
                                                x_valid=safe_valid_data,
                                                y_valid=valid_data['label'],
                                                search_method='acc')  # g-mean # auc # rec

        # ++++++++++++++++++++++++++++++++++
        # 假设你的X_balanced是一个DataFrame，列名就是特征名

        feature_names = train_data.iloc[:,2:].columns.tolist()


        # 获取特征重要性
        importance = model.feature_importances_

        # 将特征名和重要性组合成DataFrame便于查看
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance
        })

        # 按重要性降序排序
        importance_df = importance_df.sort_values('importance', ascending=False)

        # 打印结果
        print(importance_df)
        importanceframe.append(importance_df)
        # ++++++++++++++++++++++++++++++++++


        results.append(result_dict)
        models_trained.append(model)

    # 可视化训练结果
    avg = plot_folds_results(results, mode=mode)
    save_res = {'models_trained': models_trained, 'results': results}
    if save_flag:
        with open(save_res_path, 'wb') as f:
            pickle.dump(save_res, f)

    return save_res, avg,importanceframe


def train_model_by_optuna(
        feature_pkl,
        id_pkl,
        selected_feature_pkl,
        save_res_path,
        save_flag=False,
        selected_feature_list=None,
        n_trials=50,
        timeout=None,
        mode='valid_result_threshold'
):
    results = []
    models_trained = []

    for fold in range(5):
        # 数据加载和预处理（保持不变）
        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl=id_pkl,
            features_pkl=feature_pkl,
            selected_features_pkl=selected_feature_pkl,
            selected_feature_list=selected_feature_list,
            fold=fold
        )

        # 获取安全特征
        safe_train_data, safe_test_data, safe_valid_data, safe_train_valid_data = [
            process_safe_features(x, mode='xgb') for x in
            [train_data, test_data, valid_data, train_valid_data]
        ]

        # 创建Optuna优化器
        trainer = OptunaModelTrainer(
            n_trials=n_trials,
            timeout=timeout,
            study_name=f"fold_{fold}_optimization"
        )

        # 执行优化和训练
        best_model, best_params, study = trainer.optimize(
            train_data=(safe_train_data, train_data['label']),
            valid_data=(safe_valid_data, valid_data['label']),
            test_data=(safe_test_data, test_data['label'])
        )

        # 获取预测结果
        result_dict = get_model_predict_results(
            model=best_model,
            x_train=safe_train_valid_data,
            y_train=train_valid_data['label'],
            x_test=safe_test_data,
            y_test=test_data['label'],
            x_valid=safe_valid_data,
            y_valid=valid_data['label'],
            search_method='acc'
        )

        results.append(result_dict)
        models_trained.append(best_model)

    # 可视化和保存结果
    avg = plot_folds_results(results, mode=mode)
    save_res = {
        'models_trained': models_trained,
        'results': results
    }

    if save_flag:
        with open(save_res_path, 'wb') as f:
            pickle.dump(save_res, f)

    return save_res, avg



if __name__ == '__main__':

    # 一般性模型训练测试 (折间稳定特征)----------------------------------------
    # config_file = './files/configs/test_config_V1129.json'
    # params = load_config(config_file, "train_loss_test")
    # results, avg0 = train_model_on_stable_folds(**params, save_flag=False, mode='valid_result_threshold',keep_train_rate=0.93)
    # print(avg0)
    # results, avg0 = train_model_by_optuna(**params, save_flag=True, mode='valid_result_threshold')

    # 一致性测试 -- 从一致性数据中选择的特征
    config_file = './files/configs/test_config_V1211.json'
    params = load_config(config_file, "train_loss_test")
    results, avg0 = train_model_on_stable_folds(**params, save_flag=False, mode='valid_result_threshold',keep_train_rate=0.93)
    print(avg0)



    # <editor-fold desc="加载一个最强模型，去外部测试">

    # 加载一个最强模型，去外部测试。 -------------------------------------------------------------------------------
    # best_res_path = "./files/saved_models/V1119_xgb_S42F5_loss_test.pkl"
    # best_res_path = "./files/saved_models/tobe_outer_test/V1119_xgb_S42F5_loss_test_0.796acc.pkl"
    best_res_path = params['save_res_path']
    selected_feature = params['selected_feature_pkl']
    best_fold =4
    ress = []
    for best_fold in range(5):
        print(f'--------------f{best_fold}----------------')
        tmpres =valid_best_model(best_res_path=best_res_path,
                                 best_fold=best_fold,
                         all_feature="./files/saved_features/feature_V241119_all_clinic_1860.pkl",
                         selected_feature=selected_feature,
                         version='20241114_1589_177',
                         id_pkl = "./files/data/data_index/data_V1114/cons_plus/Outer_Valid.pkl")
        ress.append(tmpres)

    # </editor-fold>

    # <editor-fold desc="微循环测试">
    params = {
        "feature_pkl": "./files/saved_features/old_version_features/feature_V241119_mc_clinic_675.pkl",
        "id_pkl": "./files/data/data_index/data_V1125_微循环/S42-F5.pkl",
        "selected_feature_pkl": './files/saved_features/selected_features/features_V1119F5S42_mc_boruta_top92_clinic5.pkl',
        "save_res_path": "./files/saved_models/xgb_V1119F5S42_mc_boruta_top196_clinic5.pkl"
    }
    results, avg0, importance_df = train_model_on_stable_folds(**params, save_flag=False, mode='valid_result_threshold',
                                                keep_train_rate=1)
    print(avg0)
    for i in range(5):
        print('fold'+str(i))
        print(results['results'][i]['test_result'])


        print(results['models_trained'][i])

    importance_df[2].to_excel(
        'feature_importance_mw.xlsx',
        index=False,  # 不保存行索引
        sheet_name='Feature Importance',  # 自定义 sheet 名称
        encoding='utf-8',  # 确保中文等特殊字符正常保存
        float_format='%.4f'  # 控制浮点数精度（保留4位小数）
    )





    # </editor-fold>

    # <editor-fold desc="绘制ROC">
    probs,y = [],[]
    for i in range(5):
        probs.append(results['results'][i]['test_prob'])
        y.append(results['results'][i]['test_y'])
    from sklearn.metrics import roc_curve, auc


    def plot_multiple_roc_curves(probs, labels, names):
        """
        绘制多条ROC曲线及其平均曲线.

        Args:
            probs: 预测概率列表，每个元素对应一组预测概率.
            labels: 真实标签列表，每个元素对应一组真实标签.
            names: 每组数据的名称列表，用于图例的标签.
        """
        plt.figure(figsize=(8, 6))

        tprs = []
        aucs = []
        mean_fpr = np.linspace(0, 1, 100)

        for i, (prob, label, name) in enumerate(zip(probs, labels, names)):
            fpr, tpr, _ = roc_curve(label, prob)
            roc_auc = auc(fpr, tpr)
            tprs.append(np.interp(mean_fpr, fpr, tpr))
            tprs[-1][0] = 0.0
            aucs.append(roc_auc)
            plt.plot(fpr, tpr, linestyle='--', lw=2, alpha=0.6, label=f'{name} (AUC = {roc_auc:.2f})')

        mean_tpr = np.mean(tprs, axis=0)
        mean_tpr[-1] = 1.0
        mean_auc = auc(mean_fpr, mean_tpr)

        plt.plot(mean_fpr, mean_tpr, color='b',
                 label=r'Mean ROC (AUC = %0.2f)' % mean_auc, lw=2, alpha=.8)

        plt.plot([0, 1], [0, 1], linestyle='--', color='gray', lw=2, label='Chance')
        plt.xlim([-0.05, 1.05])
        plt.ylim([-0.05, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('Receiver Operating Characteristic')
        plt.legend(loc="lower right")
        plt.grid(alpha=0.3)
        plt.show()

    plot_multiple_roc_curves(probs, y,
                             ['F0', 'F1','F2', 'F3','F4'])

    # </editor-fold>

    # <editor-fold desc="数据精选效果测试">
    # 数据精选效果测试 ------------------------------------------------------------
    avgs = []
    for rate in [0.5, 0.6, 0.7, 0.8, 0.9, 0.95,1]:
        results, avg0 = train_model_on_stable_folds(**params, save_flag=False, mode='valid_result_threshold',keep_train_rate=rate)
        avgs.append(avg0)
        print(avg0)
    print(avgs)
    # 绘制柱状图
    plt.figure(figsize=(10, 6))
    plt.bar(['0.5', '0.6', '0.7', '0.8', '0.9', '0.95','1'], pd.DataFrame(avgs).iloc[:,0],width=0.3)
    plt.plot(['0.5', '0.6', '0.7', '0.8', '0.9', '0.95','1'], pd.DataFrame(avgs).iloc[:,0],color ='r')

    plt.xlabel('Keep Rate')
    plt.ylabel('Average')
    plt.ylim(0.92,0.94)
    plt.title('AUC')
    plt.show()
    # </editor-fold>

    # <editor-fold desc="0820 新数据模式外部测试准备">
    # 0820 新数据模式外部测试准备 -----------------------------------------
    # 检查一下0820的outer_valid数据特征有没有被全部生成 如果没有则要补充
    id_pkl = "./files/data/data_index/data_V0820/Outer_Valid.pkl"
    with open(id_pkl, 'rb') as f:
        outer_valid = pickle.load(f)
    feature_pkl = "./files/saved_features/features_V0821_all_tspt_2534.pkl"
    with open(feature_pkl, 'rb') as f:
        features = pickle.load(f)

    outer_column = outer_valid[:, 0]
    feature_column = np.array(features['mcg_file'])  # features['mcg_file']
    # 检查outer_valid中的mcg_file是否都在features中
    missed = []
    for i in range(len(outer_column)):
        if outer_column[i] not in feature_column:
            missed.append(outer_column[i])
    # missed 为0 表示全部存在。 可以进行下一步测试。
    # </editor-fold>

    # <editor-fold desc="0820 新数据所有seed特征测试">
    # 0820 新数据所有seed特征测试 ----------------------------------------
    from model_trainer.train_scripts.valid_model_on_outer import run_model_valid_on_folds_and_vote
    import pandas as pd

    seeds = [7, 21, 42, 63, 84]
    valid_results = []
    valid_results2 = []
    tsf = 'pt'  # 'tt' #
    for seed in seeds:
        params = {
            "feature_pkl": "./files/saved_features/features_V0821_all_ts" + tsf + "_2534.pkl",
            "id_pkl": "./files/data/data_index/data_V0820/S" + str(seed) + "-F5.pkl",
            "selected_feature_pkl": "./files/saved_features/selected_features/features_V0821F5S" + str(
                seed) + "_ts" + tsf + "_stable_top150.pkl",  # 暂时先做了tt
            "save_res_path": "./files/saved_models/xgb_V0821F5S" + str(seed) + "_ts" + tsf + "_stable_top150.pkl"
        }

        results, avg = train_model_on_stable_folds(**params, save_flag=True, mode='valid_result_threshold')
        print(avg)

        # 直接测试
        valid_params = {
            "feature_pkl": "./files/saved_features/features_V0821_all_ts" + tsf + "_2534.pkl",
            "id_pkl": "./files/data/data_index/data_V0820/Outer_Valid.pkl",
            "selected_feature_pkl": "./files/saved_features/selected_features/features_V0821F5S" + str(
                seed) + "_ts" + tsf + "_stable_top150.pkl",  # 暂时先做了tt
            "save_res_path": "./files/saved_models/xgb_V0821F5S" + str(seed) + "_ts" + tsf + "_stable_top150.pkl"
        }
        _, _, _, _, _, res = run_model_valid_on_folds_and_vote(**valid_params)

        id_pkl = "./files/data/data_index/data_V0820/Outer_Valid.pkl"
        with open(id_pkl, 'rb') as f:
            outer_valid = pd.DataFrame(pickle.load(f))

        # 创建一个从文件名到标签的映射
        prob_map = dict(zip(res['mcg_file'], res['prob']))
        label_map = dict(zip(res['mcg_file'], res['pred']))

        # 使用 map 方法更新 df1 的标签列
        outer_valid.iloc[:, 1] = outer_valid.iloc[:, 0].map(prob_map)
        outer_valid.iloc[:, 2] = outer_valid.iloc[:, 0].map(label_map)
        outer_valid = np.array(outer_valid)

        # 保存
        with open('./files/results/xgb_S' + str(seed) + '_top_' + tsf + '150.pkl', 'wb') as f:
            pickle.dump(outer_valid, f)
        # 上传测试
        import requests

        predict_pickle = r'./files/results/xgb_S' + str(seed) + '_top_' + tsf + '150.pkl'  # 修改此处为待评估模型结果pickle文件地址即可
        file_handle = open(predict_pickle, 'rb')
        reponse = requests.post('http://**************:8042/calculate', files={'file': open(predict_pickle, 'rb')})
        file_handle.close()
        print(seed, reponse.json())
        valid_results2.append(reponse.json())
    # </editor-fold>

    # <editor-fold desc="纯不一致数据集筛选建模检查">

    # 纯不一致数据集筛选建模检查 ----------------------------------------
    import pandas as pd

    id_inte_path = "./files/data/data_index/data_V0624_integrated/S42-F5.pkl"
    id_cons_path = "./files/data/data_index/data_V0624_consistency/S42-F5.pkl"
    features_path = "./files/saved_features/integrated_all_features_stable_F5_s42_fold_all.pkl"  # 旧版的所有 新版也可以 为了对比用旧版
    selected_features_path = "./files/saved_features/selected_features/integrated_all_features_stable_F5_s42_fold_all_top300.pkl"

    with open(id_inte_path, 'rb') as f:
        id_inte = pickle.load(f)

    with open(id_cons_path, 'rb') as f:
        id_cons = pickle.load(f)
    # 获取id文件中0-5折数据中所有字段的所有不重复数据,id包含0-5 五个字段，每折内有不同字段
    id_inte_arr = []
    # 便利id_inte[0]字典
    for k, v in id_inte[0].items():
        print(v)
        id_inte_arr.append(v[:, 0])
    # 对id_inte_pd进行拼接
    id_inte_arr = np.concatenate(id_inte_arr, axis=0)
    # 去重
    id_inte_arr = np.unique(id_inte_arr)

    # 对id_cons进行拼接
    id_cons_arr = []
    for k, v in id_cons[0].items():
        print(v)
        id_cons_arr.append(v[:, 0])
    id_cons_arr = np.concatenate(id_cons_arr, axis=0)
    # 去重
    id_cons_arr = np.unique(id_cons_arr)

    # 选择不一致数据 在inte中但不在cons中的数据
    id_inc = id_inte_arr[~np.in1d(id_inte_arr, id_cons_arr)]
    # 处理id_inte,只保留不一致数据
    for i in range(len(id_inte)):
        id_inte[i] = {k: v[np.in1d(v[:, 0], id_inc), :] for k, v in id_inte[i].items()}

    # 保存
    with open("./files/data/data_index/data_V0624_consistency/incons.pkl", 'wb') as f:
        pickle.dump(id_inte, f)

    #
    id_pkl = "./files/data/data_index/data_V0624_consistency/incons.pkl"
    res, avg = train_model_on_stable_folds(features_path, id_cons_path, selected_features_path
                                           , save_res_path="./files/saved_models/tmp.pkl", save_flag=True)

    # res[0]['models_trained']

    _ = run_model_valid_on_folds_and_vote(features_path, id_inte_path, save_res_path="./files/saved_models/tmp.pkl",
                                          selected_feature_pkl=selected_features_path)
    # </editor-fold>

    # <editor-fold desc="嵌入特征的模型训练">

    # 嵌入特征的模型训练 ---------------------------------------------------------
    params_embed = {
        "feature_pkl": "./files/saved_features/features_V0826F5S42_boruta_top1369_embed96.pkl",
        "id_pkl": "./files/data/data_index/data_V0826_cons/S42-F5.pkl",
        "selected_feature_pkl": "./files/saved_features/features_V0826F5S42_boruta_top1369_embed96.pkl",
        "save_res_path": "./files/saved_models/xgb_V0821F5S42_boruta_top1369_embed96.pkl"
    }

    res, avg = train_model_on_stable_folds(**params_embed, save_flag=True)
    # </editor-fold>
