"""
ML_Pipeline_2508 使用示例
演示如何使用重构后的部署管道进行预测
"""

import os
import sys
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pipeline.main_v2 import DeploymentPipeline


def example_single_file_prediction():
    """单文件预测示例"""
    print("=== 单文件预测示例 ===")
    
    # 创建部署管道（使用最新模型版本）
    pipeline = DeploymentPipeline(model_version='v2.5.03')
    
    # 示例txt文件路径（需要替换为实际文件路径）
    txt_file = "path/to/your/mcg_file.txt"
    
    # 示例临床数据
    clinical_data = {
        'clinic_gender': 1,      # 男性
        'clinic_age': 65,        # 65岁
        'clinic_height': 170.0,  # 身高170cm
        'clinic_weight': 70.0,   # 体重70kg
        'clinic_hospital': 3,    # 北京301医院
        'clinic_diabetes': 0,    # 无糖尿病
        'clinic_hypertension': 1, # 有高血压
        'clinic_smoking': 0,     # 不吸烟
        'clinic_drinking': 0,    # 不饮酒
        'clinic_intervention': 0, # 无既往介入
        'clinic_symp': 1         # 有典型症状
    }
    
    try:
        # 执行预测
        prob, pred = pipeline.predict_single_file(txt_file, clinical_data)
        
        print(f"预测结果:")
        print(f"  - 预测概率: {prob:.4f}")
        print(f"  - 预测类别: {pred}")
        print(f"  - 风险评估: {'高风险' if prob > 0.5 else '低风险'}")
        
    except Exception as e:
        print(f"预测失败: {e}")


def example_batch_prediction():
    """批量预测示例"""
    print("\n=== 批量预测示例 ===")
    
    # 创建部署管道
    pipeline = DeploymentPipeline(model_version='v2.5.03')
    
    # 示例路径（需要替换为实际路径）
    txt_folder = "path/to/txt_files_folder"
    excel_path = "path/to/clinical_info.xlsx"
    output_path = "path/to/results.xlsx"
    
    try:
        # 执行批量预测
        results_df = pipeline.process_batch_files(
            txt_folder=txt_folder,
            excel_path=excel_path,
            output_path=output_path
        )
        
        print(f"批量预测完成:")
        print(f"  - 处理文件数: {len(results_df)}")
        print(f"  - 结果保存至: {output_path}")
        
        # 显示预测统计
        if len(results_df) > 0:
            high_risk_count = (results_df['predicted_prob'] > 0.5).sum()
            print(f"  - 高风险病例: {high_risk_count}")
            print(f"  - 低风险病例: {len(results_df) - high_risk_count}")
            print(f"  - 平均预测概率: {results_df['predicted_prob'].mean():.4f}")
        
    except Exception as e:
        print(f"批量预测失败: {e}")


def example_command_line_usage():
    """命令行使用示例"""
    print("\n=== 命令行使用示例 ===")
    
    print("1. 单文件预测:")
    print("python pipeline/main_v2.py --mode single \\")
    print("    --txt_file path/to/file.txt \\")
    print("    --clinic_gender 1 \\")
    print("    --clinic_age 65 \\")
    print("    --clinic_hospital 3 \\")
    print("    --model_version v2.5.03")
    
    print("\n2. 批量预测:")
    print("python pipeline/main_v2.py --mode batch \\")
    print("    --txt_folder path/to/txt_files \\")
    print("    --excel_path path/to/clinical_info.xlsx \\")
    print("    --output_path path/to/results.xlsx \\")
    print("    --model_version v2.5.03")


def example_clinical_data_format():
    """临床数据格式示例"""
    print("\n=== 临床数据格式说明 ===")
    
    print("Excel文件应包含以下列（批量模式）:")
    clinical_fields = [
        "心磁号",
        "临床特征-身高",
        "临床特征-体重", 
        "临床特征-性别",
        "临床特征-年龄",
        "临床特征-吸烟",
        "临床特征-饮酒",
        "临床特征-高血压",
        "临床特征-高脂血症",
        "临床特征-既往介入",
        "临床特征-所在医院",
        "临床特征-糖尿病",
        "临床特征-典型症状"
    ]
    
    for field in clinical_fields:
        print(f"  - {field}")
    
    print("\n字段值说明:")
    print("  - 性别: 0=女, 1=男")
    print("  - 年龄: 数值（岁）")
    print("  - 身高: 数值（cm）")
    print("  - 体重: 数值（kg）")
    print("  - 医院: 0=上海中山, 1=上海六院, 2=上海十院, 3=北京301, 4=安医, 5=广东南海, 7=其它")
    print("  - 其他二分类特征: 0=否, 1=是")


def example_model_versions():
    """模型版本说明"""
    print("\n=== 可用模型版本 ===")
    
    print("当前可用的模型版本:")
    versions = [
        "v2.5.03 (推荐) - 最新版本，601个特征，AUC=0.9973",
        "v2.5.02 - 稳定版本",
        "v2.5.01 - 早期版本",
        "v2.5.0 - 基础版本"
    ]
    
    for version in versions:
        print(f"  - {version}")
    
    print("\n使用方法:")
    print("  - 不指定版本: 自动使用最新版本")
    print("  - 指定版本: --model_version v2.5.03")


def main():
    """主函数 - 运行所有示例"""
    print("ML_Pipeline_2508 重构版本使用指南")
    print("=" * 50)
    
    # 运行示例（注意：需要实际的文件路径才能运行）
    example_single_file_prediction()
    example_batch_prediction()
    example_command_line_usage()
    example_clinical_data_format()
    example_model_versions()
    
    print("\n" + "=" * 50)
    print("使用指南完成！")
    print("请根据实际情况修改文件路径后运行。")


if __name__ == '__main__':
    main()
