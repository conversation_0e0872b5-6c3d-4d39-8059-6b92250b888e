"""
Author: b<PERSON><PERSON>chen
email: <EMAIL>

date: 2024/09/19 16:20
desc: 时刻点定位算法——T波识别不准, 尝试修改
environment: pfafn36
run:
    # 第一个id的小S波现象——搁置
    python gettimes.py ./datasets/北京301/SY_TT_002407.txt
option:
    心磁文档、时刻点
"""


import sys
import matplotlib.pyplot as plt
from scipy import signal
from scipy.ndimage import gaussian_filter1d
import numpy as np
# from utils import *
# import os
# import re


def drawqhsrrp(ls36, lrms, lma, lmi, ma1, mi1, xss1, yss1, xs, savedir, tm=[]):
    d1 = ma1 - mi1
    # xss = np.arange(len(ls36[0]))
    plt.figure()
    plt.style.use('bmh')
    plt.rcParams['savefig.dpi'] = 512  # 保存像素640*480*dpi/100
    clis = np.linspace(0, 1, 38)  # 36 去除首尾
    ys = [d1 / 4 - mi1, d1 * 3 / 2 - mi1]  # y刻度
    ytick = ['ls36', 'ls']
    xtick = []
    for i1 in range(len(xs)):
        xtick.append(str(xs[i1]))
    plt.xticks(xs, xtick, fontsize=4)  # x刻度
    plt.yticks(ys[:1], ytick[:1], fontsize=10)
    plt.title("Time_vision_qhsrrp")
    for i1 in range(len(ls36)):  # 时间波组图
        c = plt.get_cmap('gist_rainbow')(clis[i1])  # 36
        plt.plot(np.arange(len(ls36[i1])), np.array(ls36[i1]) + ys[0], color=c, linewidth=0.5)  # 36
    if tm:
        for t in tm:
            plt.plot([t, t], [d1/4, d1*5/4], linewidth=0.5, alpha=0.5, c='r')
    # plt.plot(xss, np.array(lma) + ys[1], color='g', linewidth=0.5)  # 最大值曲线2
    # plt.plot(xss, np.array(lrms) + ys[1], color='r', linewidth=0.5)  # RMS曲线
    # plt.plot(xss, np.array(lmi) + ys[1], color='c', linewidth=0.5)  # 最小值曲线2
    # for i1 in range(len(xss1)):
    #     plt.scatter(xss1[i1], yss1[i1] + ys[1], c='k', marker='o', alpha=1, s=4)
    if savedir:
        plt.savefig(savedir, bbox_inches='tight', pad_inches=0)
    # plt.show()
    plt.close()


def statwaves(mcgdata):
    ls36, lrms, lma, lmi = [], [], [], []
    ma1, mi1 = 0, 0
    for i in range(36):
        ls36.append([])
    for i in range(len(mcgdata)):
        asimg = np.array(mcgdata[i].split()[1:])
        asimg = asimg.astype(float)  # str转float
        vrms = np.sqrt(np.sum(asimg**2, axis=0) / asimg.shape[0])
        lrms.append(vrms)
        ma1 = max(ma1, np.max(asimg))
        mi1 = min(mi1, np.min(asimg))
        lma.append(np.max(asimg))
        lmi.append(np.min(asimg))
        asimg = asimg.reshape((6, 6))
        for j in range(6):
            for k in range(6):  # 空间波
                if j < 3 and k < 3:
                    t = j * 3 + k
                    ls36[t].append(asimg[j, k])
                elif j < 3 and k > 2:
                    t = j * 3 + k + 6
                    ls36[t].append(asimg[j, k])
                elif j > 2 and k < 3:
                    t = (j - 3) * 3 + k + 18
                    ls36[t].append(asimg[j, k])
                elif j > 2 and k > 2:
                    t = (j - 3) * 3 + k + 24
                    ls36[t].append(asimg[j, k])
    return ls36, lrms, lma, lmi, ma1, mi1


def selectQh(tlis1, qhsr1, qhsr2, qhsr3, qhsr4):  # 初始定位Qh，<qhsr4剩余极小值点
    '''
    qhsr1=最小极值截断1=0.03
    qhsr2=最小极值截断2=0.1
    qhsr3=最小极值波动阈值=0.02
    qhsr4=备选qhsr阈值=0.1
    '''
    tlis1.reverse()  # 时刻点逆序
    lis = []
    k3 = 0  # 默认第一个值
    # 筛选1-阈值0.03
    for i2 in range(len(tlis1)):
        lis.append(tlis1[i2][1])
        if lis[-1] <= qhsr1:
            break
    if min(lis) > qhsr1:
        k3 = lis.index(min(lis))  # 截取到最小值
        lis = lis[:k3 + 1]
    # 筛选2-稳定极小值
    lis.reverse()  # 逆序-从小到大
    for i2 in range(len(lis) - 1):
        if round(abs(lis[i2 + 1] - lis[i2]), 2) > qhsr3:
            k3 = i2
            break
        else:
            k3 = i2 + 1
    lis = lis[k3:]
    # 计算Qh, Qhrest
    lis.reverse()  # 顺序-从大到小
    Qh = tlis1[len(lis) - 1][0]
    Qhrest = []  # []表示Qh不需要微调
    if min(lis) <= qhsr4:
        for i2 in range(len(lis) - 1):
            if lis[i2] > qhsr4:
                continue
            else:
                Qhrest.append(tlis1[i2])
    Qhrest.reverse()  # 顺序-从小到大
    tlis1.reverse()
    return Qh, Qhrest


def selectSr(tlis2, qhsr1, qhsr2, qhsr3, qhsr4):  # 初始定位Sr，<qhsr4剩余极小值点
    '''
    qhsr1=第一阶极小值=0.03
    qhsr2=第二阶极小值=0.1
    qhsr3=极小值波动幅度=0.02
    qhsr4=备选位置阈值=0.1
    '''
    lis = []
    k3 = 0
    # 筛选1-阈值qhsr1
    for i2 in range(len(tlis2)):
        lis.append(tlis2[i2][1])
        if lis[-1] <= qhsr1:
            break
    if min(lis) > qhsr1:
        k3 = lis.index(min(lis))  # 取最小值
        lis = lis[:k3 + 1]
    # 筛选2-稳定极小值
    lis.reverse()  # 逆序-从小到大
    for i2 in range(len(lis) - 1):
        if round(abs(lis[i2 + 1] - lis[i2]), 2) > qhsr3:
            k3 = i2
            break
        else:
            k3 = i2 + 1
    lis = lis[k3:]
    # 计算Qh, Qhrest
    lis.reverse()  # 顺序-从大到小
    Sr = tlis2[len(lis) - 1][0]
    Srrest = []  # []表示Sr不需要微调*
    if min(lis) <= qhsr4:
        for i2 in range(len(lis) - 1):
            if lis[i2] > qhsr4:
                continue
            else:
                Srrest.append(tlis2[i2])
    Srrest.reverse()  # 顺序-从大到小
    return Sr, Srrest


def getturn(lis, flag, rf0, rf1):
    if len(lis) == 0:  # 返回端点
        return -1
    elif len(lis) == 1:
        return 0
    else:
        if flag:
            lis.reverse()
        data = np.array(lis)
        data1 = np.diff(data, n=1)
        data1 = np.insert(data1, 0, data[0])
        data2 = np.diff(data1, n=1)
        data2 = np.insert(data2, 0, data[0])
        lis1 = data1.tolist()
        lis2 = data2.tolist()
        ik = len(lis) - 1
        for i2 in range(len(lis)):
            if lis[i2] > rf0:  # 截断0.1
                ik0 = min(i2 + 1, len(lis) - 1)
                continue
            if lis1[i2] < rf1 and lis2[i2] < rf1:
                ik = i2
                break
        if ik == len(lis) - 1:
            ik = ik0
        return ik


def cal_nums01(data):
    '''计算数列的每一段的长度'''
    # 初始化变量
    lengths = []
    current_value = None
    current_length = 0
    # 遍历数组
    for value in data:
        if value == current_value:
            current_length += 1
        else:
            if current_value is not None:
                lengths.append(current_length)
            current_value = value
            current_length = 1
    # 添加最后一段的长度
    if current_value is not None:
        lengths.append(current_length)
    return lengths


def get_hxd0(l2):
    '''统计候选点: 最低点，极小点, 鞍点'''
    l3 = [l2.index(min(l2))]  # 最低点
    l1 = np.array(l2)
    th = 20
    iks, _ = signal.find_peaks(-l1, distance=th)
    for i in iks:  # 极小值-波谷
        if abs(l3[0]-i)>th:
            l3.append(i)
    data1 = np.diff(l1, n=1)
    data1 = np.insert(data1, 0, l1[0])
    iks, _ = signal.find_peaks(-np.abs(data1), distance=th)  # 斜率极小值-平缓
    N1 = len(l3)
    for i in iks:
        sy = [1 for j in range(N1) if abs(l3[j]-i)<=th]
        if sy == []:  # 不在近邻中
            l3.append(i)
    l3.sort()
    return l3


def hb_lis0(l0):
    '''合并多维列表的全部元素为一维'''
    if type(l0) == list:
        l1 = []
        for i in range(len(l0)):
            l1 += hb_lis0(l0[i])
        return l1
    else:
        return [l0]


def cal_iqr0(l0):
    '''IQR去除奇异值后均值'''
    data = np.array(l0)
    Q1 = np.percentile(data, 25)
    Q3 = np.percentile(data, 75)
    IQR = Q3 - Q1
    v1 = Q1 - 1.5 * IQR
    v2 = Q3 + 1.5 * IQR
    data1 = data[(data >= v1) & (data <= v2)]
    m2 = np.mean(data1)
    return m2


def getthtr(ls36, Sr, Tp, Te, N1, Th0, Tr0, Rp):
    '''重新计算Th/Tr:
        Th<Tp, Th>=Sr+50, To>=Th
        Tr>Tp, Tr<=Rp+500, Tr<=len-1, Te<=Tr
    '''
    l00, l01 = [], []
    yss, yss1 = [], []
    vv, th = 15, 20
    r0, r1 = 0.203, 0.188
    for j in range(len(ls36)):
        ys = ls36[j][Sr:Tp+1]  # 可靠波通道-Th
        xs = list(range(len(ys)))
        x_data = np.array(xs)
        A = np.vstack([x_data, np.ones(len(x_data))]).T  # 直线型
        m, _ = np.linalg.lstsq(A, np.array(ys), rcond=None)[0]
        if m > 0:
            ys = [v-min(ys) for v in ys]
        else:
            ys = [max(ys)-v for v in ys]
        v1, v2 = ys[-1], max(ys)
        ysm = gaussian_filter1d(ys, sigma=vv)
        if ys.index(v2)>len(ys)/2 and v1>0.6*v2:
            data1 = np.diff(np.array(ysm), n=1)
            lss = []
            lss1 = data1.tolist()
            for k0 in lss1:
                if k0 < 0:
                    lss.append(0)
                else:
                    lss.append(1)
            lns = cal_nums01(lss)
            if len(lns) > 3:
                v0 = max(lns)
                ik = lns.index(v0)
                if sum(lns[ik+1:])<0.3 and ik > 0:
                    if ik == len(lns)-1:
                        if sum(lns[-2:])>0.55:
                            l00.append(lns)
                            yss.append(ys)
                    else:
                        if v0 + max(lns[ik+1], lns[ik-1])>0.55:
                            l00.append(lns)
                            yss.append(ys)
            else:
                l00.append(lns)
                yss.append(ys)
        ys = ls36[j][Tp:N1]  # 可靠波通道-Tr
        xs = list(range(len(ys)))
        x_data = np.array(list(range(Te-Tp)))
        A = np.vstack([x_data, np.ones(len(x_data))]).T  # 直线型
        m, _ = np.linalg.lstsq(A, np.array(ls36[j][Tp:Te]), rcond=None)[0]
        if m > 0:
            ys = [max(ys)-v for v in ys]
        else:
            ys = [v-min(ys) for v in ys]
        v1, v2 = ys[0], max(ys)
        ysm = gaussian_filter1d(ys, sigma=vv)
        if ys.index(v2)<len(ys)/2 and v1>0.6*v2:
            data1 = np.diff(np.array(ysm), n=1)
            lss = []
            lss1 = data1.tolist()
            for k0 in lss1:
                if k0 < 0:
                    lss.append(0)
                else:
                    lss.append(1)
            lns = cal_nums01(lss)
            if len(lns) > 3:
                v0 = max(lns)
                ik = lns.index(v0)
                if sum(lns[:ik])<0.3 and ik < len(lns)-1:
                    if ik == 0:
                        if sum(lns[:2])>0.5:
                            l01.append(lns)
                            yss1.append(ys)
                    else:
                        if v0 + max(lns[ik+1], lns[ik-1])>0.5:
                            l01.append(lns)
                            yss1.append(ys)
            else:
                l01.append(lns)
                yss1.append(ys)
    if yss == []:
        Th = max(Th0, Sr+50)
    else:
        ys0 = [np.sqrt(sum([yss[k][j]**2 for k in range(len(yss))])/len(yss)) for j in range(len(yss[0]))]
        pts = get_hxd0(ys0)
        M1 = max(hb_lis0(yss))
        rts = [round(ys0[i1]/M1,3) for i1 in pts]
        k0 = pts[len(rts)-1]  # 初始化
        for i1 in range(len(rts)):
            if k0 < 50:  # Th>=Sr+50截断
                break
            k0 = pts[len(rts)-1-i1]
            if rts[len(rts)-1-i1]<r0:
                break
        ks = []
        for ys in yss:  # 多个有效通道的最近极小点
            pt = signal.find_peaks(-np.array(ys), distance=th)[0]
            ds = [abs(i1-k0) for i1 in pt]
            ik = [pt[i1] for i1 in range(len(pt)) if ds[i1]==min(ds) and pt[i1]>=50]
            # if ik >= 50:
            ks += ik
        if ks == []:
            Th = max(Th0, Sr+50)
        else:
            k1 = cal_iqr0(ks)
            ks = sorted(set(ks))
            ds = [abs(i1-k1) for i1 in ks]
            ik = [ks[i1] for i1 in range(len(ks)) if ds[i1]==min(ds)]
            Th = ik[-1]+Sr  # Th确定
    if yss1 == []:
        Tr = max(Tr0, Te)
    else:
        yss = yss1
        ys0 = [np.sqrt(sum([yss[k][j]**2 for k in range(len(yss))])/len(yss)) for j in range(len(yss[0]))]
        pts = get_hxd0(ys0)
        M1 = max(hb_lis0(yss))
        rts = [round(ys0[i1]/M1,3) for i1 in pts]
        k0 = pts[0]  # 初始化
        for i1 in range(len(rts)):
            if pts[i1]>Rp+500-Sr:
                break
            k0 = pts[i1]
            if rts[i1]<r1:
                break
        ks = []
        for ys in yss:  # 多个有效通道的最近极小点
            pt = signal.find_peaks(-np.array(ys), distance=th)[0]
            ds = [abs(i1-k0) for i1 in pt]
            ik = [pt[i1] for i1 in range(len(pt)) if ds[i1]==min(ds) and pt[i1]<=Rp+500-Sr]
            # if ik <= Rp+500-Sr:
            ks += ik
        if ks == []:
            Tr = min(Tr0, Rp+500)
        else:
            k1 = cal_iqr0(ks)
            ks = sorted(set(ks))
            ds = [abs(i1-k1) for i1 in ks]
            ik = [ks[i1] for i1 in range(len(ks)) if ds[i1]==min(ds)]
            Tr = ik[0]+Tp
    return Th, Tr


def getthtr1(l0, Sr, Tp, N):
    'ThTr定位重做: 最小/波谷/平缓, 能量平滑阈值2'
    # th, n0, n1 = 20, 0.092, 0.076
    th, n0, n1 = 20, 0.18, 0.15
    M1 = l0[Tp]  # Tp最大值
    # Th
    l2 = l0[Sr: Tp]
    l3 = [l2.index(min(l2))]  # 最低点
    M0 = l2[l3[0]]  # 最小值
    t0 = l3[0] + Sr  # 最小时刻————
    l1 = np.array(l2)
    iks, _ = signal.find_peaks(-l1, distance=th)
    for i in iks:  # 极小值-波谷
        if abs(l3[0]-i)>th:
            l3.append(i)
    data1 = np.diff(l1, n=1)
    data1 = np.insert(data1, 0, l1[0])
    iks, _ = signal.find_peaks(-data1, distance=th)  # 斜率极小值-平缓
    N1 = len(l3)
    for i in iks:
        sy = [1 for j in range(N1) if abs(l3[j]-i)<=th]
        if sy == []:  # 不在近邻中
            l3.append(i)
    l3.sort()
    l3 = [l3[i]+Sr for i in range(len(l3))]  # 全部候选时刻————
    l1 = np.array(l0)
    l2 = []
    for i in l3:
        if i < t0:
            l2.append((M0-l1[i])/(M1-M0))
        else:
            l2.append((l1[i]-M0)/(M1-M0))
    for i in range(len(l3)):  # 修改时刻点-step2展示——————
        if l2[len(l3)-1-i] < n0:
            Th = l3[len(l3)-1-i]
            break
    # Tr选择
    l2 = l0[Tp:]
    l3 = [l2.index(min(l2))]  # 最低点
    M0 = l2[l3[0]]  # 最小值
    t0 = l3[0] + Tp  # 最小时刻
    l1 = np.array(l2)
    iks, _ = signal.find_peaks(-l1, distance=th)
    for i in iks:  # 极小值-波谷
        if abs(l3[0]-i)>th:
            l3.append(i)
    data1 = np.diff(l1, n=1)
    data1 = np.insert(data1, 0, l1[0])
    iks, _ = signal.find_peaks(data1, distance=th)  # 斜率极大值-平缓————
    N1 = len(l3)
    for i in iks:
        sy = [1 for j in range(N1) if abs(l3[j]-i)<=th]
        if sy == []:  # 不在近邻中
            l3.append(i)
    l3.sort()
    l3 = [l3[i]+Tp for i in range(len(l3))]  # 全部候选时刻
    l1 = np.array(l0)
    l2 = []
    for i in l3:
        if i < t0:
            l2.append((l1[i]-M0)/(M1-M0))
        else:
            l2.append((M0-l1[i])/(M1-M0))
    for i in range(len(l3)):  # 修改时刻点-step2展示——————
        if l2[i] < n1:
            Tr = l3[i]
            break
    return Th, Tr


def get_Rpt0(l0):
    l1 = np.array(l0)
    h0 = np.max(l1)/20
    th = 20
    rpeaks, _ = signal.find_peaks(l1, height=h0, distance=th)
    k1 = rpeaks[0]
    if rpeaks.shape[0] > 1:
        l2 = [[i, l0[i]] for i in rpeaks]
        data = np.array(l2)
        idex = np.lexsort((data[:, 0], -1*data[:, 1]))
        sorted_data = data[idex]
        l2 = sorted_data.tolist()
        k1, M0 = l2[0]
        for i in range(1, len(l2)):
            i1, m = l2[i]
            if m < M0*2/3:
                break
            elif i1-k1>150:
                break
            elif k1-i1>150:
                k1 = i1
                break
    return int(k1)


def statrmsmi(lrms):
    '''
    Rp前后截取5个RMS极小值作为备选Qh/Sr
    Qh-Rp最大阈值=150
    Rp-Sr最大阈值=150
    '''
    # 定位最大信号处
    # k1 = lrms.index(max(lrms[:600]))  # R波位置限制——
    k1 = get_Rpt0(lrms)
    # print(k1)
    k3 = max(0, k1 - 150)
    k4 = min(k1 + 151, len(lrms))
    # Qh斜率稳定/rms<0.1的最近点
    Qh0 = getturn(lrms[k3:k1], 1, 0.1, 0.01)  # 逆序查找
    Qh0 = k1 - 1 - Qh0
    Sr0 = getturn(lrms[k1 + 1:k4], 0, 0.1, 0.01)  # 逆序查找
    Sr0 = k1 + 1 + Sr0
    # Qh定位
    tlis1 = calexm(lrms[k3:k1], 0)  # 极小值
    k2 = min(5, len(tlis1))
    tlis1 = tlis1[-1 * k2:]  # 截取<=5个
    for i1 in range(len(tlis1)):  # 规范化
        tlis1[i1][0] = tlis1[i1][0] + k3
        tlis1[i1][1] = round(tlis1[i1][1] / max(lrms), 2)
    if len(tlis1) == 0:
        tlis1 = [[k3, round(lrms[k3] / max(lrms), 2)]]  # 无极小值，取最左端
    Qh, Qhrest0 = selectQh(tlis1, 0.03, 0.1, 0.02, 0.1)  # QhSr阈值设定/顺序————
    Qh = max(Qh0, Qh)
    Qhrest = []
    for i1 in range(len(Qhrest0)):
        if Qhrest0[i1][0] > Qh:
            Qhrest.append(Qhrest0[i1])
    # Sr定位
    tlis2 = calexm(lrms[k1 + 1:k4], 0)
    k2 = min(5, len(tlis2))
    tlis2 = tlis2[:k2]  # 截取<=5个
    for i1 in range(len(tlis2)):
        tlis2[i1][0] = tlis2[i1][0] + k1
        tlis2[i1][1] = round(tlis2[i1][1] / max(lrms), 2)
    if len(tlis2) == 0:
        tlis2 = [[k4, round(lrms[k4] / max(lrms), 2)]]  # 无极大值，取最右端
    Sr, Srrest0 = selectSr(tlis2, 0.03, 0.1, 0.02, 0.1)  # 逆序
    Sr = min(Sr0, Sr)
    Srrest = []
    for i1 in range(len(Srrest0)):
        if Srrest0[i1][0] < Sr:
            Srrest.append(Srrest0[i1])
    return Qh, Qhrest, Sr, Srrest


def noiserms(lrms, Qh, rmsmin):  # 筛选噪声rms范围
    # Qh前后噪声阈值=50——
    lis = calexm(lrms[Qh - 50:Qh + 51], 1)  # 极大值
    lis1 = []
    Nrms = 0
    for i1 in range(len(lis)):
        if lis[i1][1] < rmsmin:
            lis1.append(lis[i1][1])
            Nrms = max(Nrms, lis1[-1])
    return Nrms


def denoisemw(xss, yss, abss, lma, lmi, Qh, Sr, rf0, rf1, rf2):
    '''
    rf0=R波小于S波的最小比例=0.5
    rf1=Q小波阈值=1.8
    rf2=S小波阈值=3
    '''
    Rprest, mainwaves, abss1, xss0, yss0 = [], [], [], [], []
    ik = abss.index(max(abss))
    Rp = xss[ik]
    xss0.append(xss[ik])
    yss0.append(yss[ik])
    mainwaves.append([xss[ik], yss[ik]])
    if len(xss) > 1:  # 有候选
        if ik > 0:  # 非第一个波
            maxv0 = rf1 * (lma[Rp] - lmi[Rp])
            maxv = rf1 * (lma[Qh] - lmi[Qh])
            maxv = min(maxv0 / 20, maxv)
            for i2 in range(ik):  # 最大值之前的波, 满足阈值条件, 添加为候选
                ij = xss[i2]
                if lma[ij] - lmi[ij] > maxv:
                    mainwaves.insert(-1, [xss[i2], yss[i2]])
                    xss0.insert(-1, xss[i2])
                    yss0.insert(-1, yss[i2])
                    abss1.append(abss[i2])  # R前的极值
        cnt = 0
        if len(xss) - 1 - ik > 0:
            maxv0 = rf2 * (lma[Rp] - lmi[Rp])
            maxv = rf2 * (lma[Sr] - lmi[Sr])
            maxv = min(maxv0 / 20, maxv)
            for i2 in range(ik + 1, len(xss)):  # 最大值之后的波, 满足阈值条件, 添加为候选
                ij = xss[i2]
                if lma[ij] - lmi[ij] > maxv:
                    cnt += 1  # R后的极值
                    xss0.append(xss[i2])
                    yss0.append(yss[i2])
                    mainwaves.append([xss[i2], yss[i2]])
        # 计算Rp, Rprest
        if len(abss1) > 0 and cnt == 0:  # 备选次大-最大波之前存在候选波
            ik0 = abss1.index(max(abss1))
            # if abs(mainwaves[ik0][1]) > abs(mainwaves[ik][1]) * rf0:
            if abss1[ik0] > abss[ik] * rf0:
                Rprest = [xss[ik], yss[ik]]
                Rp = mainwaves[ik0][0]
    return Rp, Rprest, mainwaves, xss0, yss0


def statpeaks(lrms, ls36, lma, lmi, Qh, Sr, Nrms, mcgmin, neib0, rf1, rf2,
              rf3):
    '''
    Nrms=RMS阈值
    mcgmin=MCG阈值=0.5pT
    neib0=近邻波阈值=10
    rf1=R波小于S波的最小比例=0.5
    rf2=最小可靠波:最大可靠波的最小比例=1/14
    rf3=非集中极值的波/包络噪声波=10
    '''
    lma0 = lma[Qh:Sr]
    lmi0 = lmi[Qh:Sr]
    tlis1 = calexm(lma0, 1)  # 极大值
    tlis2 = calexm(lmi0, 0)  # 极小值
    max1, max2 = 0, 0  # 截断小波
    for i1 in range(len(tlis1)):
        max1 = max(max1, tlis1[i1][1])
    for i1 in range(len(tlis2)):
        max2 = max(max2, -tlis2[i1][1])
    max12 = max(max1, max2)
    max0 = max(max12 * rf2, mcgmin)
    # 筛选可靠波
    tlis11, tlis22 = [], []
    for i1 in range(len(tlis1)):
        j1 = tlis1[i1][0]
        if tlis1[i1][1] == max1:
            tlis11.append([j1 + Qh, tlis1[i1][1], tlis1[i1][1]])
        if tlis1[i1][1] > max0 and lrms[j1 + Qh] > Nrms:
            lis0 = calexnum(ls36, j1 + Qh, 2)
            if sum(lis0) > rf3:
                tlis11.append([j1 + Qh, tlis1[i1][1], tlis1[i1][1]])
    for i1 in range(len(tlis2)):
        j1 = tlis2[i1][0]
        if -tlis2[i1][1] == max2:
            tlis22.append([j1 + Qh, -tlis2[i1][1], tlis2[i1][1]])
        if -tlis2[i1][1] > max0 and lrms[j1 + Qh] > Nrms:
            lis0 = calexnum(ls36, j1 + Qh, 2)
            if sum(lis0) > rf3:
                tlis22.append([j1 + Qh, -tlis2[i1][1], tlis2[i1][1]])
    # 融合排序-时间顺序
    tlis = tlis11 + tlis22
    # print(tlis)
    data = np.array(tlis)
    idex = np.lexsort((-1 * data[:, 2], -1 * data[:, 1], data[:, 0]))  # -1从大到小
    sorted_data = data[idex]
    tlis = sorted_data.tolist()
    tlis0 = [tlis[0]]
    abslis = [tlis[0][1]]
    for i1 in range(1, len(tlis)):  # 相同时刻点融合
        if tlis[i1][0] == tlis0[-1][0]:
            continue
        else:
            tlis0.append(tlis[i1])
            abslis.append(tlis[i1][1])
    # 10近邻整合可靠波、最大值固定
    k1 = abslis.index(max(abslis))
    k2 = int(tlis0[k1][0])  # 最大值时刻点
    xss, yss, abss = [k2], [tlis0[k1][2]], [tlis0[k1][1]]  # 最大值点
    lis1, lis2, lis3 = [], [], []  # 缓存-时刻、绝对值、心磁值
    for i1 in range(k1 + 1):
        if k2 - tlis0[i1][0] > neib0:
            if len(lis1) > 0:  # 排除起始点
                if tlis0[i1][0] - lis1[0] <= neib0:
                    lis1.append(tlis0[i1][0])
                    lis2.append(tlis0[i1][1])
                    lis3.append(tlis0[i1][2])
                else:
                    ik0 = lis2.index(max(lis2))
                    if tlis0[i1][0] - lis1[ik0] > neib0:
                        xss.insert(-1, int(lis1[ik0]))
                        abss.insert(-1, lis2[ik0])
                        yss.insert(-1, lis3[ik0])
                        lis1 = [tlis0[i1][0]]  # 新起点
                        lis2 = [tlis0[i1][1]]
                        lis3 = [tlis0[i1][2]]
                    else:
                        lis1 = lis1[ik0:]  # 截断、添加
                        lis2 = lis2[ik0:]
                        lis3 = lis3[ik0:]
                        lis1.append(tlis0[i1][0])
                        lis2.append(tlis0[i1][1])
                        lis3.append(tlis0[i1][2])
            else:
                lis1.append(tlis0[i1][0])
                lis2.append(tlis0[i1][1])
                lis3.append(tlis0[i1][2])
        else:
            if len(lis1) > 0:
                ik0 = lis2.index(max(lis2))
                xss.insert(-1, int(lis1[ik0]))
                abss.insert(-1, lis2[ik0])
                yss.insert(-1, lis3[ik0])
            break
    xss1, yss1, abss1 = [], [], []  # 逆序-从大到小
    lis1, lis2, lis3 = [], [], []  # 缓存-时刻、绝对值、心磁值
    for i1 in range(len(tlis0) - k1):
        j1 = len(tlis0) - 1 - i1  # len(tlis0) - 1, k1
        if tlis0[j1][0] - k2 > neib0:
            if len(lis1) > 0:  # 排除起始点
                if lis1[0] - tlis0[j1][0] <= neib0:
                    lis1.append(tlis0[j1][0])
                    lis2.append(tlis0[j1][1])
                    lis3.append(tlis0[j1][2])
                else:
                    ik0 = lis2.index(max(lis2))
                    if lis1[ik0] - tlis0[j1][0] > neib0:
                        xss1.append(int(lis1[ik0]))
                        abss1.append(lis2[ik0])
                        yss1.append(lis3[ik0])
                        lis1 = [tlis0[j1][0]]  # 新起点
                        lis2 = [tlis0[j1][1]]
                        lis3 = [tlis0[j1][2]]
                    else:
                        lis1 = lis1[ik0:]  # 截断、添加
                        lis2 = lis2[ik0:]
                        lis3 = lis3[ik0:]
                        lis1.append(tlis0[j1][0])
                        lis2.append(tlis0[j1][1])
                        lis3.append(tlis0[j1][2])
            else:
                lis1.append(tlis0[j1][0])
                lis2.append(tlis0[j1][1])
                lis3.append(tlis0[j1][2])
        else:
            if len(lis1) > 0:
                ik0 = lis2.index(max(lis2))
                xss1.append(int(lis1[ik0]))
                abss1.append(lis2[ik0])
                yss1.append(lis3[ik0])
            break
    xss1.reverse()
    yss1.reverse()
    abss1.reverse()
    xss.extend(xss1)
    yss.extend(yss1)
    abss.extend(abss1)
    Rp, Rprest, mainwaves, xss0, yss0 = denoisemw(xss, yss, abss, lma, lmi, Qh,
                                                  Sr, rf1, 1.8,
                                                  3)  # 筛选可靠波，前后小波去除
    return Rp, Rprest, mainwaves, xss0, yss0


def writemainw(mainwf, item, xss, yss):
    doc = open(mainwf, 'a')
    doc.write('\n%s' % item)
    for i1 in range(len(xss)):
        doc.write(', %d %.1f' % (xss[i1], yss[i1]))
    doc.close()


def calexm(lis, flag):  # flag=0极小/1
    neighb = [-2, -1, 1, 2]
    tlis = []
    for i3 in range(1, len(lis) - 1):  # 两端不算极值
        flag0 = 0
        for j3 in neighb:
            if i3 + j3 in range(len(lis)):
                if flag:
                    if lis[i3] < lis[i3 + j3]:
                        flag0 = 1
                else:
                    if lis[i3] > lis[i3 + j3]:
                        flag0 = 1
            if flag0:
                break
        if flag0 == 0:
            tlis.append([i3, lis[i3]])
    return tlis


def statpts(lis1, lis2):  # 只取1个最大极值1个最小极值
    lisall = []
    if len(lis1) > 0:
        b1 = np.array(lis1)
        b2 = b1[:, 1]
        b3 = b2.tolist()
        ik = b3.index(max(b3))
        lisall.append(lis1[ik][0])
    if len(lis2) > 0:
        b1 = np.array(lis2)
        b2 = b1[:, 1]
        b3 = b2.tolist()
        ik = b3.index(min(b3))
        lisall.append(lis2[ik][0])
    return lisall


def calrp(ls36, k1, Rp, k2):
    '''
    rf1=波形微调范围=10
    '''
    Rplis = []
    for i1 in range(k2 - k1 - 2):
        Rplis.append(0)
    if len(Rplis) == 0:
        return Rp
    else:
        for j1 in range(len(ls36)):
            ls1 = ls36[j1]
            lis1 = calexm(ls1[k1:k2], 1)  # 极大值(两端不取)
            lis2 = calexm(ls1[k1:k2], 0)  # 极小值(两端不取)
            lisall = statpts(lis1, lis2)
            for i1 in lisall:
                Rplis[i1 - 1] += 1  # -10到10，数量加1
        # 根据Rplis进行位置微调
        lis0, lis1, lis2 = [], [], []  # 最大值，次大值列表
        max1 = max(Rplis)  # 最大值
        for i1 in range(len(Rplis)):
            if Rplis[i1] == max1:
                lis1.append([i1, abs(Rp - k1 - 1 - i1)])  # loc/差值
            else:
                lis0.append(Rplis[i1])  # 去除最大值的列表
        if len(lis0) == 0:  # 每个点极值数量相同
            return Rp
        else:
            max2 = max(lis0)
            for i1 in range(len(Rplis)):
                if Rplis[i1] == max2:
                    lis2.append([i1, abs(Rp - k1 - 1 - i1)])  # loc/差值
            data = np.array(lis1)
            idex = np.lexsort((data[:, 0], data[:, 1]))  # -1从大到小
            sorted_data = data[idex]
            lis1 = sorted_data.tolist()
            data = np.array(lis2)
            idex = np.lexsort((data[:, 0], data[:, 1]))  # -1从大到小
            sorted_data = data[idex]
            lis2 = sorted_data.tolist()
            # 判断
            if int(lis1[0][1]) > 5:  # 最大值较远>5
                k11 = max(0, Rp - k1 - 1 - 3)  # 3近邻>8
                k22 = min(len(Rplis), Rp - k1 - 1 + 4)
                if max(Rplis[k11:k22]) >= 8:
                    lis0 = Rplis[k11:k22]
                    Rpnew = lis0.index(max(lis0))
                    Rpnew += k11  # 3以内>=8的位置
                elif max2 < 3:  # 次大值<3
                    Rpnew = int(lis1[0][0])  # 最大值
                else:  # 次大值>=3
                    df = int(lis1[0][1]) - int(lis2[0][1])  # 最大值和次大值的位置差
                    if df > 4:
                        Rpnew = int(lis2[0][0])  # 次大
                    elif df == 4:
                        ik = int(lis2[0][0])  # 次大值相邻也有次大
                        Rpnew = int(lis1[0][0])  # 最大值
                        if ik + 1 in range(
                                len(Rplis)) and Rplis[ik - 1] == max2:
                            Rpnew = int(lis2[0][0])  # 次大
                        if ik + 1 in range(
                                len(Rplis)) and Rplis[ik + 1] == max2:
                            Rpnew = int(lis2[0][0])  # 次大
                    else:
                        Rpnew = int(lis1[0][0])  # 最大值
            else:
                Rpnew = int(lis1[0][0])  # 最大值
            Rpnew += k1 + 1
            return Rpnew


def statedges(lrms, lma, lmi, ls36, RSp0, Sr0, Nrms, mcgmin, rf0, rf1, rf2,
              rf3, flag):
    '''
    Nrms=RMS噪声阈值<0.35
    mcgmin=心磁噪声阈值=0.5
    rf0=与Rp的最小间隔=10
    rf1=顺序备选波数量=3
    rf2=最小极值数量=8
    rf3=噪声波:Sr的正负差的阈值=3/1.8
    flag=Sp/Qp标识=1/0
    '''
    maxv0 = rf3 * (lma[RSp0[0]] - lmi[RSp0[0]])
    maxv = rf3 * (lma[Sr0[0]] - lmi[Sr0[0]])
    maxv = min(maxv0 / 20, maxv)
    if flag:  # Sp
        RSp = RSp0[0] + rf0
        Sr = Sr0[0]
    else:  # Qp
        RSp = RSp0[0]
        Sr = Sr0[0] - rf0
    lis0, Splis, Spnew = [], [], []  # 备选位置/极值点/查找Sp
    if Sr <= RSp:
        return Splis, Spnew
    else:
        lrms0 = lrms[RSp:Sr]
        lma0 = lma[RSp:Sr]
        lmi0 = lmi[RSp:Sr]
        if flag:
            lrms0.reverse()
            lma0.reverse()
            lmi0.reverse()
        for i1 in range(len(lrms0)):
            if lrms0[i1] > Nrms and max(
                    lma0[i1],
                    -lmi0[i1]) > mcgmin and lma0[i1] - lmi0[i1] > maxv:
                lis0.append(i1)  # 备选位置
            Splis.append(0)  # 极值数量统计
        for i1 in range(len(ls36)):
            ls0 = ls36[i1][RSp:Sr]
            if flag:
                ls0.reverse()
            lis1 = calexm(ls0, 1)  # 极大值、两端不取
            lis2 = calexm(ls0, 0)
            lis11, lis22 = [], []
            for j1 in range(len(lis1)):  # 噪声筛选
                if lis1[j1][0] in lis0:
                    lis11.append([lis1[j1][0], lis1[j1][1]])
            for j1 in range(len(lis2)):
                if lis2[j1][0] in lis0:
                    lis22.append([lis2[j1][0], -lis2[j1][1]])
            lisall = lis11 + lis22
            if len(lisall) > 0:
                data = np.array(lisall)  # 按次序
                idex = np.lexsort((-1 * data[:, 1], data[:, 0]))  # -1从大到小
                sorted_data = data[idex]
                lisall = sorted_data.tolist()
                if len(lisall) > rf1:  # 截取不多于3个
                    lisall = lisall[:rf1]
            for j1 in range(len(lisall)):  # 添加极值点
                ik = int(lisall[j1][0])
                Splis[ik] += 1
        if max(Splis) >= rf2:  # 最密集数量不小于8
            ik = Splis.index(max(Splis))
            if flag:  # Sp逆序-Sr-1初始
                ik = Sr - 1 - ik
            else:  # Qp顺序-RSp初始
                ik = RSp + ik
            Spnew.append(ik)
        return Splis, Spnew


def getpeak(mainwaves, Rpnew, ls36, lma, lmi, Sr, rf0, rf1, flag):
    '''
    rf0=噪声波:Sr的正负差的阈值=3/1.8
    rf1=多个波权衡的极值集中数量=12
    '''
    Spnew0, Spnew = [], []
    if len(mainwaves) == 0:
        return Spnew
    else:
        maxv0 = rf0 * (lma[Rpnew] - lmi[Rpnew])
        maxv = rf0 * (lma[Sr] - lmi[Sr])
        maxv = min(maxv0 / 20, maxv)
        if flag:
            for i1 in range(len(mainwaves)):
                ij = len(mainwaves) - 1 - i1
                ik = mainwaves[ij][0]
                if ik > Rpnew and lma[ik] - lmi[ik] > maxv:
                    Spnew0.append(ik)  # Sp
                    Spnew = [ik]  # Sp
            if len(Spnew0) > 1:
                Spnew = []  # 重新选择
                for i1 in range(len(Spnew0)):
                    lis0 = calexnum(ls36, Spnew0[i1], 2)
                    if sum(lis0) > rf1:
                        Spnew = [Spnew0[i1]]
                        break
                if len(Spnew) == 0:
                    Spnew = [Spnew0[-1]]
        else:
            for i1 in range(len(mainwaves)):
                ik = mainwaves[i1][0]
                if ik < Rpnew and lma[ik] - lmi[ik] > maxv:
                    Spnew0.append(ik)  # Qp
                    Spnew = [ik]  # Qp
                    break
        return Spnew


def calexnum(ls36, Sp0, rf0):
    '''
    rf0=截取极子范围=2
    '''
    k1 = Sp0 - 1 - rf0
    k2 = Sp0 + 2 + rf0
    numlis = []
    for i2 in range(k2 - k1 - 2):
        numlis.append(0)
    for i2 in range(len(ls36)):
        ls0 = ls36[i2][k1:k2]
        lis1 = calexm(ls0, 1)  # 极大值、两端不取
        lis2 = calexm(ls0, 0)
        lisall = statpts(lis1, lis2)
        for j2 in lisall:
            numlis[j2 - 1] += 1
    return numlis


def get05(lma, lmi, RSp, Sr, mcgmin, flag):
    lma0 = lma[RSp:Sr + 1]
    lmi0 = lmi[RSp:Sr + 1]
    if flag:
        lma0.reverse()
        lmi0.reverse()
    Sp = 0  # 初始点
    for i1 in range(1, len(lma0)):
        if max(lma0[i1], -lmi0[i1]) > mcgmin:
            Sp = i1
        else:  # 可能取到端点
            break
    if flag:  # Qp逆序-Sr初始
        Sp = Sr - Sp
    else:  # Sp顺序-RSp初始
        Sp = RSp + Sp
    return Sp


def tuningedges(lma, lmi, k1, k2, rf1, flag):
    '''
    rf1=端点微调比例=1.2
    '''
    lma0 = lma[k1:k2 + 1]
    lmi0 = lmi[k1:k2 + 1]
    lmami = []
    for i1 in range(len(lma0)):
        lmami.append(lma0[i1] - lmi0[i1])
    if flag:
        lmami.reverse()
    ik = 0
    if len(lmami) > 0:
        maxv = lmami[0] * rf1
        for i1 in range(len(lmami)):
            if lmami[i1] <= maxv:
                ik = i1
            else:
                break
    if flag:
        Sr = k2 - ik
    else:
        Sr = k1 + ik
    return Sr


def maindl(lis0, Rp):
    lis1 = []
    for i1 in range(len(lis0)):
        if lis0[i1][0] != Rp:
            lis1.append(lis0[i1])
    return lis1


def getabsmax(lma, lmi, k1, k2):
    lma0 = lma[k1:k2]
    lmi0 = lmi[k1:k2]
    Tp = 0
    if lma0[0] > -lmi0[0]:
        Tpv = lma0[0]
    else:
        Tpv = lmi0[0]
    if len(lma0) > 0:
        lmami = []
        for i1 in range(len(lma0)):
            lmami.append(lma0[i1] - lmi0[i1])
        maxv = max(lmami) / 1.2
        for i1 in range(len(lma0)):
            if lmami[i1] < maxv:  # 筛除正负差略小的噪声波
                lma0[i1] = 0
                lmi0[i1] = 0
        if max(lma0) > -min(lmi0):
            Tp = lma0.index(max(lma0))
            Tpv = max(lma0)
        else:
            Tp = lmi0.index(min(lmi0))
            Tpv = min(lmi0)
    Tp += k1
    return Tp, Tpv


def getrmsmi(lrms, k1, Pp, k2):
    lrms0 = lrms[k1:Pp]
    Ph, Pr = k1, k2
    if len(lrms0) > 2:
        maxv = max(lrms0)
        lrms0.reverse()
        lis0 = calexm(lrms0, 0)  # 极小值
        if len(lis0) > 0:
            for i1 in range(len(lis0)):
                if lis0[i1][1] < maxv / 2:
                    Ph = lis0[i1][0]
                    Ph = Pp - 1 - Ph
                    break
    lrms0 = lrms[Pp:k2]
    if len(lrms0) > 2:
        maxv = max(lrms0)
        lis0 = calexm(lrms0, 0)  # 极小值
        if len(lis0) > 0:
            for i1 in range(len(lis0)):
                if lis0[i1][1] < maxv / 2:
                    Pr = lis0[i1][0]
                    Pr = Pp + Pr
    return Ph, Pr


def caltt(lma, lmi, k1, Tp, k2, Tpv0):
    To, Te = k1, k2
    lma0 = lma[k1:Tp]
    lmi0 = lmi[k1:Tp]
    if len(lma0) > 0:
        lmami = []
        for i1 in range(len(lma0)):
            lmami.append(lma0[i1] - lmi0[i1])
        lma0.reverse()
        lmi0.reverse()
        lmami.reverse()
        Tpv1 = lmami[0] / 2
        if Tpv0 > 0:
            for i1 in range(len(lma0)):
                if lmami[i1] < Tpv1 or lma0[i1] < Tpv0:
                    break
                To = Tp - i1 - 1
        else:
            for i1 in range(len(lmi0)):
                if lmami[i1] < Tpv1 or lmi0[i1] > Tpv0:
                    break
                To = Tp - i1 - 1
    lma0 = lma[Tp + 1:k2]
    lmi0 = lmi[Tp + 1:k2]
    if len(lma0) > 0:
        lmami = []
        for i1 in range(len(lma0)):
            lmami.append(lma0[i1] - lmi0[i1])
        Tpv1 = lmami[0] / 2
        if Tpv0 > 0:
            for i1 in range(len(lma0)):
                if lmami[i1] < Tpv1 or lma0[i1] < Tpv0:
                    break
                Te = Tp + 1 + i1
        else:
            for i1 in range(len(lmi0)):
                if lmami[i1] < Tpv1 or lmi0[i1] > Tpv0:
                    break
                Te = Tp + 1 + i1
        Te0 = Tp + 1.5 * (Tp - To)  # 间期截断
        Te = min(Te, Te0)
    return To, int(Te)


def get36s(l1):
    l0 = []
    l2 = [0 for i in range(len(l1[0].split()[1:]))]
    for i in range(len(l1)):
        l0.append(l1[i].split()[:])
        vs = l1[i].split()[1:]
        l2 = [max(abs(float(vs[j])), l2[j]) for j in range(len(l2))]
    a = sorted(enumerate(l2), key=lambda x: x[1], reverse=True)
    b = [a[i][0]+1 for i in range(len(a))]
    b = [0]+b[:36]
    l0 = [[l0[i][j] for j in range(len(l0[i])) if j in b] for i in range(len(l0))]
    l2 = [' '.join(l0[i]) for i in range(len(l0))]
    return l2


# def gettime(mcgfile, f0):
def gettime(mcgfile, noqs=0, f0='', tm=[]):
    noq, nos = 0, 0
    if type(mcgfile)==str:
        doc = open(mcgfile, 'r')
        mcgdata = doc.readlines()
        doc.close()
    elif type(mcgfile)==list:
        mcgdata = mcgfile
    RR = len(mcgdata) - 1
    # # 61通道提取最大信号的36通道
    # mcgdata = get36s(mcgdata)
    # Qh/Sr初步定位
    ls36, lrms, lma, lmi, ma1, mi1 = statwaves(mcgdata)
    Qh, Qhrest, Sr, Srrest = statrmsmi(lrms)  # 定位Qh/Sr, 剩余备选
    # Rp初步定位
    Nrms = noiserms(lrms, Qh, 0.35)  # 计算Qh最大噪声rms
    Rp, Rprest, mainwaves, xss, yss = statpeaks(lrms, ls36, lma, lmi, Qh, Sr,
                                                Nrms, 0.5, 10, 0.5, 1 / 14,
                                                5)  # 定位Rp/备选Rp/可靠波
    # writemainw(mainwf, item, xss, yss)  # QRS可靠波
    # Rp/Sp定位
    if len(Rprest) > 0:  # Rp为次大值
        Splis, Spnew = statedges(lrms, lma, lmi, ls36, Rprest, [Sr, 0], Nrms,
                                 0.5, 10, 3, 4, 3, 1)  # 逆序查找波形
        if len(Spnew) > 0:
            Sp = Spnew[0]  # 查找到Sp
            Rp = Rprest[0]  # Rp替换为最大值*
            mainwaves = maindl(mainwaves, Rp)
            k1 = max(Rp - 10 - 1, Qh)
            k2 = min(Rp + 10 + 2, Sp - 8)
            Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
        else:
            Sp = Rprest[0]  # 查找不到，Sp为最大值
            mainwaves = maindl(mainwaves, Rp)
            mainwaves = maindl(mainwaves, Sp)
            k1 = max(Rp - 10 - 1, Qh)
            k2 = min(Rp + 10 + 2, Sp - 8)
            Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
    else:  # Rp为最大值
        mainwaves = maindl(mainwaves, Rp)
        Spnew = getpeak(mainwaves, Rp, ls36, lma, lmi, Sr, 3, 6, 1)  # 1逆序取Sp
        if len(Spnew) > 0:
            Sp = Spnew[0]  # 有可靠波
            mainwaves = maindl(mainwaves, Sp)
            k1 = max(Rp - 10 - 1, Qh)
            k2 = min(Rp + 10 + 2, Sp - 8)
            Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
        else:
            Splis, Spnew = statedges(lrms, lma, lmi, ls36, [Rp, 0], [Sr, 0],
                                     Nrms, 0.5, 10, 3, 4, 3, 1)  # 逆序查找波形
            if len(Spnew) > 0:
                Sp = Spnew[0]  # 查找到Sp
                k1 = max(Rp - 10 - 1, Qh)
                k2 = min(Rp + 10 + 2, Sp - 8)
                Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
            else:
                nos = 1
                k1 = max(Rp - 10 - 1, Qh)
                k2 = min(Rp + 10 + 2, Sr - 8)
                Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sr
                Sp = get05(lma, lmi, Rp, Sr, 0.5, 0)  # 查找不到顺序取0.5pT
    if Sp - Rp < 1:  # RS间期控制
        Rp = Sp - 1
    # Qp定位
    Qpnew = getpeak(mainwaves, Rp, ls36, lma, lmi, Qh, 1.8, 12, 0)  # 0顺序取Qp
    if len(Qpnew) > 0:
        Qp = Qpnew[0]  # 有可靠波
        mainwaves = maindl(mainwaves, Qp)
    else:
        Qplis, Qpnew = statedges(lrms, lma, lmi, ls36, [Qh, 0], [Rp, 0], Nrms,
                                 0.5, 10, 3, 4, 1.8, 0)  # 顺序查找波形
        if len(Qpnew) > 0:
            Qp = Qpnew[0]  # 查找到Sp
        else:
            noq = 1
            Qp = get05(lma, lmi, Qh, Rp, 0.5, 1)  # 查找不到逆序取0.5pT
    if Rp - Qp < 1:  # QR间期控制
        Rp = Qp + 1
    # Sr修正
    Srrest.reverse()  # 改成顺序
    for j in range(len(Srrest)):
        if Srrest[j][0] > Sp:  # Sp后第一个备选
            Sr = Srrest[j][0]  # 默认为Sr
            break
    # print(Sr)
    Sr = min(Sr, Sp + 60)  # 间期截断
    # print(Sr)
    Sr = tuningedges(lma, lmi, Sp, Sr, 1.2, 1)  # 逆序微调Sr
    # Qh修正
    Qhrest.reverse()
    for j in range(len(Qhrest)):
        if Qhrest[j][0] < Qp:  # Qp前第一个备选
            Qh = Qhrest[j][0]
            break
    Qh = max(Qh, Qp - 35)  # 间期截断
    Qh = tuningedges(lma, lmi, Qh, Qp, 1.2, 0)  # 顺序微调Qh
    # Pp/Tp定位
    # k1 = max(Sr + 1, Rp + 100)
    k1 = min(max(Sr + 1, Rp + 150), len(lma)-1)
    # print(k1, len(lma))
    Tp, Tpv = getabsmax(lma, lmi, k1, len(lma))
    # lrms0 = lrms[k1:]
    # k2 = min(Qh - 1, Rp - 100)
    k2 = max(min(Qh - 1, Rp - 100), 1)
    # print(0, k2)
    Pp, Ppv = getabsmax(lma, lmi, 0, k2)
    # Ph/Pr、Th/Tr、To/Te
    k1 = max(0, Pp - 30)
    k2 = min(Pp + 30, Qh)
    Ph, Pr = getrmsmi(lrms, k1, Pp, k2)
    Th0, Tr0 = getthtr1(lrms, Sr, Tp, len(lrms))
    To, Te = caltt(lma, lmi, Th0, Tp, Tr0, Tpv / 2)
    Th, Tr = getthtr(ls36, Sr, Tp, Te, len(lrms), Th0, Tr0, Rp)
    Th = min(Tp, max(Sr+50, Th))
    To = max(To, Th)
    Tr = max(Tp, min([RR, Rp+500, Tr]))
    Te = min(Te, Tr)
    # Th = max(Th, 2 * To - Tp)  # 过长截断
    # Tr = min(Tr, 2 * Te - Tp)
    # print(Th, Tr)
    # # 绘制
    # drawqhsrrp(ls36, lrms, lma, lmi, ma1, mi1, xss, yss, [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr], savedir)
    if f0:
        drawqhsrrp(ls36, lrms, lma, lmi, ma1, mi1, xss, yss, [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr], f0, tm=tm)
    if noqs:
        # return [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR, noq, nos]
        return [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR, 0, 0]  # 不看单独无波指标, 默认0——有Q/S波
    else:
        return [Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR]


if __name__ == '__main__':
    # # 725例时刻点生成
    # f1 = './root_dir/liuyuan725/MCGID.txt'
    # d1 = './datasets/liuyuan'
    # doc = open(f1, 'r')
    # lines = doc.readlines()
    # doc.close()
    # cnt = 0
    # for i in range(len(lines)):
    #     mcgfile = '%s/%s.txt' % (d1, lines[i][:-1])
    #     print(i)
    #     try:
    #         Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettime(mcgfile)
    #     except:
    #         cnt += 1
    #         print(mcgfile)
    # print(cnt)
    mcgfile = sys.argv[1]  # id文档
    Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettime(mcgfile)
    print('Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR:')
    print(Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR)
    # ### 批量生成
    # d0 = sys.argv[1]  # id文档
    # d1 = sys.argv[2]  # mcg目录
    # d2 = sys.argv[3]  # mcg目录
    # ln = get_lines1(d0)
    # for item in ln:
    #     mcgfile = '%s/%s.txt' % (d1, item)
    #     f0 = '%s/%s.png' % (d2, item)
    #     Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettime(mcgfile, f0)
