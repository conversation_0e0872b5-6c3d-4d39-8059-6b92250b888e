'''
@Project ：pythonProject 
@File    ：get_args.py
@IDE     ：PyCharm 
<AUTHOR> Qu
@Date    ：2023/4/20 9:09 
'''
import os
import threading
import warnings

import matplotlib as mpl
import numpy as np
from matplotlib import pyplot as plt
# from mayavi import mlab
from scipy.stats import entropy
from scipy.stats import stats, skew, kurtosis

plt.rcParams['font.family'] = 'DejaVu Sans'
plot_lock = threading.Lock()
mpl.rcParams['axes.unicode_minus'] = False


def shannon_entropy(t_wave_channel, bot_limit, top_limit, num_bins):
    t_wave_channel = (t_wave_channel - bot_limit) / (top_limit - bot_limit)
    counts, bin_edges = np.histogram(t_wave_channel, bins=num_bins)
    probs = counts / np.sum(counts)
    shannon_entropy = entropy(probs)
    return shannon_entropy


def gini_coef(t_waves, window_length):
    row_channel, col_time = t_waves.shape
    counts = col_time // window_length
    gini_mat = np.zeros((counts, 1))

    for ii in range(counts):
        wave_window = np.abs(t_waves[:, ii * window_length:(ii + 1) * window_length])

        mean_template = np.tile(np.mean(wave_window, axis=0), (row_channel, 1))
        wave_window_norm = wave_window / mean_template

        U, S, V = np.linalg.svd(wave_window_norm.T @ wave_window_norm)

        vector_max_eigen = U[:, 0]
        vector_max_eigen = vector_max_eigen / np.sum(vector_max_eigen)

        yp_first_var = wave_window_norm @ vector_max_eigen

        index = np.argsort(yp_first_var)[::-1]

        rp_second_var = np.zeros_like(index)
        rp_second_var[index] = np.arange(1, row_channel + 1)

        gini_mat[ii, 0] = 1 - np.sum((2 * rp_second_var - 1) / (row_channel ** 2) * yp_first_var)

    mean_gini = np.mean(gini_mat)
    std_gini = np.std(gini_mat)

    return mean_gini, std_gini, gini_mat


def statistic_analysis(single_line, name_of):
    # if 't_max_angle' in name_of:
    #     pdb.set_trace()
    temp = single_line
    data_sorted = np.sort(single_line)
    # 找到第10百分位和第90百分位的值
    min_value = np.percentile(data_sorted, 10)
    max_value = np.percentile(data_sorted, 90)
    build_dict = {}
    if max_value != min_value:
        single_line = (single_line - min_value) / (max_value - min_value + 1)
    else:
        single_line = (single_line - min_value) / (max_value - min_value + 1)

    funcs = ['mean', 'sum', 'std', 'mode', 'median', 'min', 'max', 'length']
    for func in funcs:
        if func == 'mode':
            mode_value = stats.mode(single_line, keepdims=False)
            if not isinstance(mode_value.mode, np.ndarray):
                value = mode_value.mode
            else:
                value = list(mode_value.mode)[0]

        elif func == 'length':
            value = len(single_line)
        else:
            value = getattr(np, func)(single_line)

        build_dict[f'{name_of}_{func}'] = value
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", category=RuntimeWarning)

        skew_value = float(skew(single_line))
        if np.isnan(skew_value):
            skew_value = 0

        kurtosis_value = float(kurtosis(single_line, bias=False, fisher=True))
        if np.isnan(kurtosis_value):
            kurtosis_value = 0

    build_dict[f'{name_of}_skewness'] = skew_value
    build_dict[f'{name_of}_kurtosis'] = kurtosis_value
    build_dict[f'{name_of}_range'] = np.ptp(single_line)

    return build_dict


def statistic_analysis_no_normaillize(single_line, name_of):
    build_dict = {}
    funcs = ['mean', 'sum', 'std', 'mode', 'median', 'min', 'max', 'length']
    for func in funcs:
        if func == 'mode':
            mode_value = stats.mode(single_line, keepdims=False)
            if not isinstance(mode_value.mode, np.ndarray):
                value = mode_value.mode
            else:
                value = list(mode_value.mode)[0]

        elif func == 'length':
            value = len(single_line)
        else:
            value = getattr(np, func)(single_line)

        build_dict[f'{name_of}_{func}'] = value
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", category=RuntimeWarning)

        skew_value = float(skew(single_line))
        if np.isnan(skew_value):
            skew_value = 0

        kurtosis_value = float(kurtosis(single_line, bias=False, fisher=True))
        if np.isnan(kurtosis_value):
            kurtosis_value = 0

    build_dict[f'{name_of}_skewness'] = skew_value
    build_dict[f'{name_of}_kurtosis'] = kurtosis_value
    build_dict[f'{name_of}_range'] = np.ptp(single_line)

    return build_dict


def svd_entropy(t_waves):
    _, S, _ = np.linalg.svd(t_waves @ t_waves.T)
    S_norm = S / np.sum(S)
    svd_entropy = -np.sum(S_norm * np.log(S_norm))
    return svd_entropy


def max_distance(points):
    num_points = points.shape[0]
    dist_all = np.zeros((num_points, 5))

    for ii in range(num_points):
        max_dist_tempt, max_loc_tempt = np.max(np.linalg.norm(points[ii] - points, axis=1)), np.argmax(
            np.linalg.norm(points[ii] - points, axis=1))
        dist_all[ii, :] = [max_dist_tempt, *points[ii], *points[max_loc_tempt]]

    max_loc = np.argmax(dist_all[:, 0])
    max_dist = dist_all[max_loc, 0]
    max_points = dist_all[max_loc, 1:]

    return max_dist, max_points


def build_dict(*args):
    _dict = {}

    for arg in args:
        if not arg:
            continue
        for field_name, value in arg.items():
            _dict[field_name] = value

    return _dict


def pcd_rec(mfm, model, local=None, save_path=None, filename=None, imShow=False, interval=None):
    '''
    :param mfm:
    :param imShow:
    :param model: 'e' or 'm'   e 是电流相关计算    m 是磁场相关
    :return:
    '''
    if model == 'e' or model == 'm':
        pass
    else:
        return 'ERROR'

    # 第一种方法
    # x, y = np.meshgri d(np.arange(0, 30, 5), np.arange(0, 30, 5))
    # xx, yy = np.meshgrid(np.arange(0, 25.01, 0.1), np.arange(0, 25.01, 0.1))
    # 心磁矩阵塑性
    # mfm = mfm.reshape(6, 6)
    # mfm旋转
    # mfm = mfm.T
    # 样条插值
    # mfm_fine = interp2(x, y, mfm, xx, yy, 'linear')

    # 第二种方法
    x, y = np.meshgrid(np.arange(0, 10.1, 2), np.arange(0, 10.1, 2))
    xx, yy = np.meshgrid(np.arange(0, 10.0, 0.1), np.arange(0, 10.0, 0.1))
    # mfm = mfm.reshape(6, 6)
    # mfm = mfm.T
    # interp_func = RectBivariateSpline(x[0], y[:, 0], mfm, kx=3, ky=3)
    # mfm_fine = interp_func(xx[0], yy[:, 0])
    mfm_fine = mfm

    if imShow:
        plt.figure()
        plt.imshow(mfm_fine, cmap='jet')
        path = os.path.join(save_path, '插值图', filename.replace('.txt', ''), interval)
        if not os.path.exists(path):
            os.makedirs(path)
        plt.savefig(os.path.join(path, str(local) + '.png'))
        # 显示图像
        # plt.show()

    if model == 'e':
        # pdb.set_trace()
        # 伪电流密度计算 c=(dB/dy)*dx-(dB/dx)*dy

        diff_xx = np.diff(xx, axis=1)
        diff_xx = np.round(np.hstack((diff_xx, diff_xx[:, -1].reshape(-1, 1))), 2)

        diff_yy = np.diff(yy, axis=0)
        diff_yy = np.round(np.vstack((diff_yy, diff_yy[-1, :])), 2)

        try:
            diff_mfm_x = np.diff(mfm_fine, axis=1)
        except:
            print('Error ', filename)


        diff_mfm_x = np.hstack((diff_mfm_x, diff_mfm_x[:, -1].reshape(-1, 1)))

        diff_mfm_y = np.diff(mfm_fine, axis=0)
        diff_mfm_y = np.vstack((diff_mfm_y, diff_mfm_y[-1, :]))

        current_x = diff_mfm_y / diff_yy
        current_y = -diff_mfm_x / diff_xx

        mat_magni = np.sqrt(current_x ** 2 + current_y ** 2)
        mat_angle = np.arctan2(current_y, current_x) * (180 / np.pi)

        # 最大电流密度的点
        max_magni = np.max(mat_magni)
        temp_mat_magni = np.sort(mat_magni)
        min_value = np.percentile(temp_mat_magni, 10)
        max_value = np.percentile(temp_mat_magni, 90)
        row_max_magni, col_max_magni = np.where(mat_magni == max_magni)
        max_magni = (max_magni - min_value) / (max_value - min_value + 1)

        max_angle = mat_angle[row_max_magni, col_max_magni][0]
        max_x_position = xx[row_max_magni, col_max_magni][0]
        max_y_position = yy[row_max_magni, col_max_magni][0]

        return max_magni, max_angle, max_x_position, max_y_position

    elif model == 'm':

        # 显示图像
        max_mfm = np.max(mfm_fine)
        row_max_mfm, col_max_mfm = np.where(mfm_fine == max_mfm)
        x_max_mfm = xx[row_max_mfm, col_max_mfm]
        y_max_mfm = yy[row_max_mfm, col_max_mfm]

        min_mfm = np.min(mfm_fine)
        row_min_mfm, col_min_mfm = np.where(mfm_fine == min_mfm)
        x_min_mfm = xx[row_min_mfm, col_min_mfm]
        y_min_mfm = yy[row_min_mfm, col_min_mfm]

        if imShow:
            fig = plt.figure(figsize=(5, 4))
            ax2 = fig.add_subplot(1, 1, 1)
            # 在子图2中绘制mfm_fine
            ax2.imshow(mfm_fine, cmap='jet')
            ax2.set_title('mfm_fine')
            x1, y1 = row_max_mfm, col_max_mfm
            x2, y2 = row_min_mfm, col_min_mfm
            plt.annotate("", xy=(y2, x2), xytext=(y1, x1), arrowprops=dict(arrowstyle="->", lw=2, color="g"))
            plt.show()

        # 几个重要的参数输出
        # 计算最小值和最大值的绝对比率（ratio_minmax）
        ratio_minmax = abs(min_mfm) / max_mfm
        # 计算最小值和最大值之间的角度（angle_minmax），这里使用了反正切函数和弧度到角度的转换。
        angle_minmax = np.arctan2((y_min_mfm - y_max_mfm), (x_min_mfm - x_max_mfm)) * (180 / np.pi)
        # 计算最小值和最大值之间的距离（dist_minmax），这里使用了欧式距离公式。
        dist_minmax = np.sqrt((y_min_mfm - y_max_mfm) ** 2 + (x_min_mfm - x_max_mfm) ** 2)

        # 正负磁极的面积

        # 计算正磁极的点数（points_pos_pole），即mfm_fine中大于等于0.8 * max_mfm的点数。
        points_pos_pole = np.sum(mfm_fine >= 0.8 * max_mfm)
        # 计算正磁极的面积占比（area_pos_pole），即正磁极点数除以mfm_fin的总点数。
        area_pos_pole = points_pos_pole / (mfm_fine.shape[0] * mfm_fine.shape[1])

        # 负磁极
        points_neg_pole = np.sum(mfm_fine <= 0.8 * min_mfm)
        area_neg_pole = points_neg_pole / mfm_fine.shape[0] / mfm_fine.shape[1]

        try:
            return tuple(map(float, (
                ratio_minmax, angle_minmax, dist_minmax, x_max_mfm, y_max_mfm, x_min_mfm, y_min_mfm, area_pos_pole,
                area_neg_pole)))
        except:
            print("计算错误。")
        return


    else:
        return 'ERROR'


def plot_timewaves(imgs, save=False, save_path=None, filename=None):
    plt.figure()
    if imgs.shape[-1] == 6:
        new_imgs = np.einsum('kij->ijk', imgs)
    else:
        new_imgs = imgs

    with plot_lock:  # 获取锁
        for i in range(6):
            for j in range(6):
                plt.plot(new_imgs[i, j])
        if save:
            if not os.path.exists(os.path.join(save_path, "_time")):
                os.mkdir(os.path.join(save_path, "_time"))
            plt.savefig(os.path.join(save_path, "_time", filename.replace('.txt', '') + ".png"), dpi=300)
        else:
            plt.show()  # 释放锁


def plot_spatialwaves(imgs, save=False, save_path=None, filename=None):
    plt.figure()
    with plot_lock:  # 获取锁
        fig, ax = plt.subplots(6, 6, sharey=True)
        for i in range(6):
            for j in range(6):
                ax[i, j].plot(imgs[i, j])
                ax[i, j].axis('off')
                ax[i, j].set_title(f"({i + 1},{j + 1})")

        fig.tight_layout()
        if save:
            if not os.path.exists(os.path.join(save_path, "_spaces")):
                os.mkdir(os.path.join(save_path, "_spaces"))
            plt.savefig(os.path.join(save_path, "_spaces", filename.replace('.txt', '') + ".png"), dpi=300)
        else:
            plt.show()  # 释放锁


def plot_pic(imgs, loc_p_peak, loc_t_end, s=False, t=False, display_3D=False, save=True, save_path=None, filename=None):
    imgs = imgs.reshape(6, 6, -1)
    imgs = imgs[:, :, loc_p_peak:loc_t_end]
    if t or save:
        plot_timewaves(imgs, save, save_path, filename)
    if s or save:
        plot_spatialwaves(imgs, save, save_path, filename)

    # if display_3D:
    #     new_imgs = np.einsum('hwc->chw', imgs)
    #     interp_imgs = [cv2.resize(img, (150, 150), interpolation=cv2.INTER_CUBIC) for img in new_imgs]
    #     show_imgs = np.einsum('kij->ijk', interp_imgs)
    #     mlab.contour3d(show_imgs, contours=45, transparent=True, vmin=-1, vmax=1)
    #     mlab.show()
