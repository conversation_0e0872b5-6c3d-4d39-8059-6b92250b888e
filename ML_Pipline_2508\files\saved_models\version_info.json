{"versions": {"v1.0": {"version": "v1.0", "model_name": "Stacking-Strategy0", "description": "初始版本，使用SMOTE平衡", "created_at": "2025-02-06 14:42:17", "feature_count": 152, "model_type": "StackingClassifier"}, "v1.1": {"version": "v1.1", "model_name": "Stacking-Strategy0", "description": "测试版本，使用SMOTE平衡", "created_at": "2025-02-06 16:21:39", "feature_count": 152, "model_type": "StackingClassifier", "train_metrics": {"accuracy": 0.9858732876712328, "precision": 0.99833748960931, "recall": 0.9748376623376623, "specificity": 0.9981884057971014, "f1": 0.9864476386036961, "auc": 0.9999073616600791}, "sample_count": 2336, "class_distribution": {"1": 1232, "0": 1104}, "error_rate": 0.014126712328767123}, "v1.2": {"version": "v1.2", "model_name": "CRFXGB_S2.5", "description": "测试版本300特征，使用SMOTE平衡", "created_at": "2025-02-06 21:20:44", "feature_count": 300, "model_type": "CRFXGBClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 2336, "class_distribution": {"1": 1237, "0": 1099}, "error_rate": 0.0}, "v1.3": {"version": "v1.3", "model_name": "Stacking-Strategy1", "description": "300特征，使用adasyn分医院平衡0.8/0.1", "created_at": "2025-02-07 13:53:58", "feature_count": 301, "model_type": "StackingClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 2777, "class_distribution": {"0": 1395, "1": 1382}, "error_rate": 0.0}, "v1.4": {"version": "v1.4", "model_name": "XGBoost-Deep", "description": "600新版特征,使用adasyn全数据平衡0.9,按临床特征分层和中值填补", "created_at": "2025-02-13 10:57:44", "feature_count": 601, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 2142, "class_distribution": {"1": 1118, "0": 1024}, "error_rate": 0.0}, "v1.31": {"version": "v1.31", "model_name": "Stacking-Strategy1", "description": "300特征，使用adasyn分医院平衡0.7/0.1", "created_at": "2025-02-07 14:02:18", "feature_count": 301, "model_type": "StackingClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 2777, "class_distribution": {"0": 1395, "1": 1382}, "error_rate": 0.0}, "v1.32": {"version": "v1.32", "model_name": "Stacking-Strategy1", "description": "300特征，使用adasyn分医院平衡0.5/0.1", "created_at": "2025-02-07 14:10:14", "feature_count": 301, "model_type": "StackingClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 2777, "class_distribution": {"0": 1395, "1": 1382}, "error_rate": 0.0}, "v1.33": {"version": "v1.33", "model_name": "XGBoost-Deep", "description": "300特征，使用adasyn全数据平衡0.9,按临床特征分层和中值填补", "created_at": "2025-02-11 11:31:32", "feature_count": 304, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9995305164319249, "precision": 1.0, "recall": 0.9991055456171736, "specificity": 1.0, "f1": 0.9995525727069351, "auc": 1.0}, "sample_count": 2130, "class_distribution": {"1": 1118, "0": 1012}, "error_rate": 0.00046948356807511736}, "v1.35": {"version": "v1.35", "model_name": "XGBoost-Deep", "description": "600新版特征,使用adasyn全数据平衡0.9,仅用3家验证医院数据训练得到按临床特征分层和中值填补", "created_at": "2025-02-13 13:08:00", "feature_count": 304, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 1578, "class_distribution": {"1": 853, "0": 725}, "error_rate": 0.0}, "v1.36": {"version": "v1.36", "model_name": "XGBoost-Deep", "description": "600新版特征,使用adasyn全数据平衡1+0.2,仅用3家验证医院数据训练得到按临床特征分层和中值填补", "created_at": "2025-02-13 13:14:06", "feature_count": 304, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9995213020584012, "precision": 1.0, "recall": 0.999001996007984, "specificity": 1.0, "f1": 0.999500748876685, "auc": 1.0}, "sample_count": 2089, "class_distribution": {"0": 1087, "1": 1002}, "error_rate": 0.0004786979415988511}, "v1.41": {"version": "v1.41", "model_name": "XGBoost-Deep", "description": "300新版特征,0.9/0,全部医院训练", "created_at": "2025-02-13 14:15:21", "feature_count": 303, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 1564, "class_distribution": {"1": 871, "0": 693}, "error_rate": 0.0}, "v1.42": {"version": "v1.42", "model_name": "XGBoost-Deep", "description": "300新版特征,0.9/0,全部医院训练，原1589训练数据", "created_at": "2025-02-13 14:52:43", "feature_count": 303, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 1922, "class_distribution": {"1": 1006, "0": 916}, "error_rate": 0.0}, "v1.43": {"version": "v1.43", "model_name": "XGBoost-Deep", "description": "300新版特征,不增强(考虑外部验证与内部阴阳分布一致),全部医院训练，1589训练数据", "created_at": "2025-02-14 13:32:30", "feature_count": 303, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 1589, "class_distribution": {"1": 1006, "0": 583}, "error_rate": 0.0}, "v1.44": {"version": "v1.44", "model_name": "XGBoost-Deep", "description": "300新版特征,不增强(考虑外部验证与内部阴阳分布一致),全部医院训练，1589训练数据，早停防过拟合", "created_at": "2025-02-14 13:56:20", "feature_count": 303, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9729389553178099, "precision": 0.9743842364532019, "recall": 0.9831013916500994, "specificity": 0.9554030874785592, "f1": 0.9787234042553191, "auc": 0.9933793465621367}, "sample_count": 1589, "class_distribution": {"1": 1006, "0": 583}, "error_rate": 0.027061044682190057}, "v1.45": {"version": "v1.45", "model_name": "XGBoost-Deep", "description": "300新版特征,不增强(考虑外部验证与内部阴阳分布一致),全部医院训练，全数据，早停防过拟合", "created_at": "2025-02-14 14:03:47", "feature_count": 303, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9688561721404304, "precision": 0.9749776586237712, "recall": 0.9758497316636852, "specificity": 0.9567901234567902, "f1": 0.9754135002235136, "auc": 0.9926980498685926}, "sample_count": 1766, "class_distribution": {"1": 1118, "0": 648}, "error_rate": 0.03114382785956965}, "v1.47": {"version": "v1.47", "model_name": "XGBoost-Deep", "description": "300新版特征,不增强,全部医院训练，全数据，早停防过拟合,院所独热编码", "created_at": "2025-02-19 13:54:27", "feature_count": 305, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9552661381653454, "precision": 0.9642537980339589, "recall": 0.9651162790697675, "specificity": 0.9382716049382716, "f1": 0.9646848457755923, "auc": 0.986601128558493}, "sample_count": 1766, "class_distribution": {"1": 1118, "0": 648}, "error_rate": 0.04473386183465459}, "v1.48": {"version": "v1.48", "model_name": "XGBoost-Deep", "description": "300新版特征,不增强,全部医院训练，全数据，早停100防过拟合,院所独热编码", "created_at": "2025-02-19 14:05:41", "feature_count": 305, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9739524348810872, "precision": 0.9820143884892086, "recall": 0.9767441860465116, "specificity": 0.9691358024691358, "f1": 0.979372197309417, "auc": 0.9918394840875462}, "sample_count": 1766, "class_distribution": {"1": 1118, "0": 648}, "error_rate": 0.026047565118912798}, "v1.49": {"version": "v1.49", "model_name": "XGBoost-Deep", "description": "300新版特征,不增强,全部医院训练，全数据，早停100,略微调参,院所独热编码", "created_at": "2025-02-19 14:15:02", "feature_count": 305, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9722536806342016, "precision": 0.9810981098109811, "recall": 0.9749552772808586, "specificity": 0.9675925925925926, "f1": 0.978017048003589, "auc": 0.9916669427328342}, "sample_count": 1766, "class_distribution": {"1": 1118, "0": 648}, "error_rate": 0.027746319365798414}, "v1.50": {"version": "v1.50", "model_name": "XGBoost-Deep", "description": "300/0211版特征,smote增强,全数据全医院独热编码，早停100,", "created_at": "2025-02-19 14:25:45", "feature_count": 305, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9844632768361582, "precision": 0.9874213836477987, "recall": 0.983005366726297, "specificity": 0.9860834990059643, "f1": 0.9852084267144778, "auc": 0.9965528830594254}, "sample_count": 2124, "class_distribution": {"1": 1118, "0": 1006}, "error_rate": 0.015536723163841809}, "v1.60": {"version": "v1.60", "model_name": "XGBoost-Deep", "description": "300/0214版特征,不增强,全数据全医院独热编码，早停100,", "created_at": "2025-02-19 14:56:26", "feature_count": 308, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9750849377123443, "precision": 0.9811827956989247, "recall": 0.9794275491949911, "specificity": 0.9675925925925926, "f1": 0.9803043867502239, "auc": 0.9929589323969168}, "sample_count": 1766, "class_distribution": {"1": 1118, "0": 648}, "error_rate": 0.02491506228765572}, "v1.61": {"version": "v1.61", "model_name": "XGBoost-Deep", "description": "300/0214版特征,不增强,全数据全医院独热编码，早停200,树深1000", "created_at": "2025-02-19 16:05:47", "feature_count": 308, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9688561721404304, "precision": 0.9809954751131221, "recall": 0.9695885509838998, "specificity": 0.9675925925925926, "f1": 0.9752586594691858, "auc": 0.9921445372026767}, "sample_count": 1766, "class_distribution": {"1": 1118, "0": 648}, "error_rate": 0.03114382785956965}, "v1.62": {"version": "v1.62", "model_name": "XGBoost-Deep", "description": "300/0214版特征,<PERSON><PERSON><PERSON><PERSON>,不增强,全数据全医院独热编码，早停200，", "created_at": "2025-02-20 14:19:16", "feature_count": 305, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9728199320498301, "precision": 0.9828519855595668, "recall": 0.9740608228980322, "specificity": 0.970679012345679, "f1": 0.9784366576819407, "auc": 0.9929106208175976}, "sample_count": 1766, "class_distribution": {"1": 1118, "0": 648}, "error_rate": 0.027180067950169876}, "v1.63": {"version": "v1.63", "model_name": "XGBoost-Deep", "description": "300/0214版特征,deepB<PERSON><PERSON>,bj301-0.8增强,0.1noise,,全数据全医院独热编码，早停200，", "created_at": "2025-02-20 14:31:58", "feature_count": 305, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9910865874363328, "precision": 0.9950535861500412, "recall": 0.9877250409165302, "specificity": 0.9947089947089947, "f1": 0.9913757700205338, "auc": 0.9995872265375813}, "sample_count": 2356, "class_distribution": {"1": 1222, "0": 1134}, "error_rate": 0.008913412563667233}, "v1.64": {"version": "v1.64", "model_name": "XGBoost-Deep", "description": "300/0214版特征,1129版数据,<PERSON><PERSON><PERSON><PERSON>,整体0.9增强,全数据全医院独热编码，早停200，", "created_at": "2025-02-20 15:33:25", "feature_count": 307, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9947916666666666, "precision": 0.993414863593603, "recall": 0.9971671388101983, "specificity": 0.991869918699187, "f1": 0.9952874646559848, "auc": 0.9991434515721119}, "sample_count": 1920, "class_distribution": {"1": 1059, "0": 861}, "error_rate": 0.005208333333333333}, "v1.65": {"version": "v1.65", "model_name": "XGBoost-Deep", "description": "300/0214版特征,1129版数据,<PERSON><PERSON><PERSON><PERSON>,不增强,全数据全医院独热编码，早停200，", "created_at": "2025-02-20 15:43:16", "feature_count": 307, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9912280701754386, "precision": 0.9906103286384976, "recall": 0.996222851746931, "specificity": 0.9813780260707635, "f1": 0.9934086629001883, "auc": 0.9972023077883461}, "sample_count": 1596, "class_distribution": {"1": 1059, "0": 537}, "error_rate": 0.008771929824561403}, "v2.0.0": {"version": "v2.0.0", "model_name": "XGBoost-Deep", "description": "300/0301版特征,0227版数据,基准版本，早停200", "created_at": "2025-03-03 14:37:11", "feature_count": 307, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9924242424242424, "precision": 0.9943582510578279, "recall": 0.9929577464788732, "specificity": 0.9916317991631799, "f1": 0.9936575052854123, "auc": 0.9996316813011963}, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.007575757575757576}, "v2.0.1": {"version": "v2.0.1", "model_name": "XGBoost-Deep", "description": "300/0301版特征,0227版数据,六院1.0十院0.8增强,0.1噪声,基准版本，早停200", "created_at": "2025-03-03 16:16:18", "feature_count": 307, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.9905797101449275, "precision": 0.9912935323383084, "recall": 0.9925280199252802, "specificity": 0.9878682842287695, "f1": 0.9919103920348475, "auc": 0.9993848889886496}, "sample_count": 1380, "class_distribution": {"1": 803, "0": 577}, "error_rate": 0.009420289855072464}, "v2.0.2": {"version": "v2.0.2", "model_name": "XGBoost-Deep", "description": "300/0301版特征,0227版数据,基准版本，SVM", "created_at": "2025-03-03 17:01:09", "feature_count": 307, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 0.8838383838383839, "precision": 0.8803191489361702, "recall": 0.9323943661971831, "specificity": 0.8117154811715481, "f1": 0.905608755129959, "auc": 0.9578171960634096}, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.11616161616161616}, "v2.0.3": {"version": "v2.0.3", "model_name": "XGBoost-Deep", "description": "数据:0227版数据,原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta,模型:XGB,600tree,lr0.05,不早停增强:无", "created_at": "2025-03-04 09:32:06", "feature_count": 307, "model_type": "XGBClassifier", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.0}, "v2.0.4": {"version": "v2.0.4", "model_name": "SVM", "description": "数据:0227版数据,训练集一致性数据,原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta,模型:XGB,600tree,lr0.05,不早停增强:无", "created_at": "2025-03-04 09:37:07", "feature_count": 311, "model_type": "SVC", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "sample_count": 809, "class_distribution": {"1": 525, "0": 284}, "error_rate": 0.0}, "v2.0.5": {"created_at": "2025-03-07 13:36:16", "model_type": "DoubleModel", "threshold1": 0.4, "threshold2": 0.6, "feature_version": ["feature0303_PureBoruta_0227data(1')", "feature0303_PureBoruta_0227data(1''3)"]}, "20250311_1": {"version": "20250311_1", "model_type": "ConflictWeightedXGBoost", "created_at": "2025-03-11 16:35:08", "description": "", "train_metrics": {"accuracy": 0.9966329966329966, "precision": 0.9971830985915493, "recall": 0.9971830985915493, "specificity": 0.99581589958159, "f1": 0.9971830985915493, "auc": 0.9998791914667924}, "feature_count": 606, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.003367003367003367, "cv_results": {"best_fold": 3, "best_score": 0.7647058823529411, "fold_metrics": [{"fold": 1, "base_accuracy": 0.680672268907563, "weighted_accuracy": 0.6890756302521008, "improvement": 0.008403361344537785, "correction_stats": {"improved": 9, "worsened": 7, "total_diff": 16}}, {"fold": 2, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7352941176470589, "improvement": 0.0, "correction_stats": {"improved": 11, "worsened": 11, "total_diff": 22}}, {"fold": 3, "base_accuracy": 0.7857142857142857, "weighted_accuracy": 0.7647058823529411, "improvement": -0.021008403361344574, "correction_stats": {"improved": 6, "worsened": 11, "total_diff": 17}}, {"fold": 4, "base_accuracy": 0.7721518987341772, "weighted_accuracy": 0.7552742616033755, "improvement": -0.016877637130801704, "correction_stats": {"improved": 4, "worsened": 8, "total_diff": 12}}, {"fold": 5, "base_accuracy": 0.7341772151898734, "weighted_accuracy": 0.7215189873417721, "improvement": -0.012658227848101333, "correction_stats": {"improved": 4, "worsened": 7, "total_diff": 11}}]}}, "v2.0.6": {"version": "v2.0.6", "model_type": "ConflictWeightedXGBoost", "created_at": "2025-03-12 12:02:51", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权增强:无", "train_metrics": {"accuracy": 0.9410774410774411, "precision": 0.9395604395604396, "recall": 0.9633802816901409, "specificity": 0.9079497907949791, "f1": 0.9513212795549374, "auc": 0.9857917378749486}, "feature_count": 307, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.058922558922558925, "cv_results": {"best_fold": 5, "best_score": 0.7637130801687764, "fold_metrics": [{"fold": 1, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7352941176470589, "improvement": 0.0, "correction_stats": {"improved": 4, "worsened": 4, "total_diff": 8}}, {"fold": 2, "base_accuracy": 0.7478991596638656, "weighted_accuracy": 0.7521008403361344, "improvement": 0.004201680672268893, "correction_stats": {"improved": 5, "worsened": 4, "total_diff": 9}}, {"fold": 3, "base_accuracy": 0.7100840336134454, "weighted_accuracy": 0.726890756302521, "improvement": 0.01680672268907557, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"fold": 4, "base_accuracy": 0.7426160337552743, "weighted_accuracy": 0.7468354430379747, "improvement": 0.00421940928270037, "correction_stats": {"improved": 7, "worsened": 6, "total_diff": 13}}, {"fold": 5, "base_accuracy": 0.7426160337552743, "weighted_accuracy": 0.7637130801687764, "improvement": 0.021097046413502074, "correction_stats": {"improved": 8, "worsened": 3, "total_diff": 11}}]}}, "v2.0.61": {"version": "v2.0.61", "model_type": "ConflictWeightedXGBoost", "created_at": "2025-03-12 13:01:29", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权增强:无", "train_metrics": {"accuracy": 0.9503367003367004, "precision": 0.9465020576131687, "recall": 0.971830985915493, "specificity": 0.9184100418410042, "f1": 0.9589993050729674, "auc": 0.9865077494254229}, "feature_count": 307, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.049663299663299666, "cv_results": {"best_fold": 4, "best_score": 0.7805907172995781, "fold_metrics": [{"fold": 1, "base_accuracy": 0.7394957983193278, "weighted_accuracy": 0.7436974789915967, "improvement": 0.004201680672268893, "correction_stats": {"improved": 4, "worsened": 3, "total_diff": 7}}, {"fold": 2, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7436974789915967, "improvement": 0.008403361344537785, "correction_stats": {"improved": 5, "worsened": 3, "total_diff": 8}}, {"fold": 3, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7352941176470589, "improvement": 0.0, "correction_stats": {"improved": 3, "worsened": 3, "total_diff": 6}}, {"fold": 4, "base_accuracy": 0.7679324894514767, "weighted_accuracy": 0.7805907172995781, "improvement": 0.012658227848101333, "correction_stats": {"improved": 5, "worsened": 2, "total_diff": 7}}, {"fold": 5, "base_accuracy": 0.7468354430379747, "weighted_accuracy": 0.7510548523206751, "improvement": 0.0042194092827004814, "correction_stats": {"improved": 6, "worsened": 5, "total_diff": 11}}]}}, "v2.0.62": {"version": "v2.0.62", "model_type": "ConflictWeightedXGBoost", "created_at": "2025-03-12 14:50:02", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权增强:无", "train_metrics": {"accuracy": 0.9427609427609428, "precision": 0.9421487603305785, "recall": 0.9633802816901409, "specificity": 0.9121338912133892, "f1": 0.9526462395543176, "auc": 0.9877688726501267}, "feature_count": 307, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.05723905723905724, "cv_results": {"best_fold": 2, "best_score": 0.7689075630252101, "fold_metrics": [{"fold": 1, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.7394957983193278, "improvement": 0.012605042016806789, "correction_stats": {"improved": 5, "worsened": 2, "total_diff": 7}}, {"fold": 2, "base_accuracy": 0.7689075630252101, "weighted_accuracy": 0.7689075630252101, "improvement": 0.0, "correction_stats": {"improved": 5, "worsened": 5, "total_diff": 10}}, {"fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7647058823529411, "improvement": 0.021008403361344463, "correction_stats": {"improved": 10, "worsened": 5, "total_diff": 15}}, {"fold": 4, "base_accuracy": 0.7257383966244726, "weighted_accuracy": 0.7215189873417721, "improvement": -0.0042194092827004814, "correction_stats": {"improved": 6, "worsened": 7, "total_diff": 13}}, {"fold": 5, "base_accuracy": 0.7426160337552743, "weighted_accuracy": 0.7637130801687764, "improvement": 0.021097046413502074, "correction_stats": {"improved": 10, "worsened": 5, "total_diff": 15}}]}}, "v2.0.63": {"version": "v2.0.63", "model_type": "OptunaWeightedXGBoost", "created_at": "2025-03-12 20:14:58", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权增强:无", "train_metrics": {"accuracy": 0.9587542087542088, "precision": 0.9571230982019364, "recall": 0.9746478873239437, "specificity": 0.9351464435146444, "f1": 0.9658060013956734, "auc": 0.9849225057457717}, "feature_count": 307, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.041245791245791245, "cv_results": {"best_fold": 4, "best_score": 0.8143459915611815, "fold_metrics": [{"fold": 1, "base_accuracy": 0.7394957983193278, "weighted_accuracy": 0.7605042016806722, "improvement": 0.021008403361344463, "correction_stats": {"improved": 13, "worsened": 8, "total_diff": 21}}, {"fold": 2, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7563025210084033, "improvement": 0.021008403361344463, "correction_stats": {"improved": 12, "worsened": 7, "total_diff": 19}}, {"fold": 3, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7689075630252101, "improvement": 0.03361344537815125, "correction_stats": {"improved": 15, "worsened": 7, "total_diff": 22}}, {"fold": 4, "base_accuracy": 0.7679324894514767, "weighted_accuracy": 0.8143459915611815, "improvement": 0.04641350210970474, "correction_stats": {"improved": 15, "worsened": 4, "total_diff": 19}}, {"fold": 5, "base_accuracy": 0.7468354430379747, "weighted_accuracy": 0.7848101265822784, "improvement": 0.03797468354430378, "correction_stats": {"improved": 12, "worsened": 3, "total_diff": 15}}]}, "optuna_results": {"best_params": {"use_conflict_weighting": false, "use_consistency_weight": true, "consistency_1_weight": 0.6264929949142469, "consistency_0_weight": 1.9075167882486423, "use_hospital_consistency_weight": false, "use_quality_weight": true, "quality_good_weight": 1.0553243405350685, "quality_poor_weight": 1.461431413073332, "use_score_range_weight": false, "use_hospital_weight": true, "hospital_sh6_weight": 0.5281260954030644, "hospital_sh10_weight": 2.188884073751515, "hospital_bj301_weight": 3.4824017200855457, "hospital_gdnh_weight": 0.5018586814669468, "use_combination_weight": false}, "best_value": 0.7769740807715492, "n_trials": 300}}, "v2.0.64": {"version": "v2.0.64", "model_type": "OptunaWeightedXGBoost", "created_at": "2025-03-13 09:20:33", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权增强:无", "train_metrics": {"accuracy": 0.****************, "precision": 0.9449035812672176, "recall": 0.9661971830985916, "specificity": 0.9163179916317992, "f1": 0.9554317548746519, "auc": 0.9817667511344216}, "feature_count": 307, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.05387205387205387, "cv_results": {"best_fold": 2, "best_score": 0.7521008403361344, "fold_metrics": [{"fold": 1, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.7394957983193278, "improvement": 0.012605042016806789, "correction_stats": {"improved": 9, "worsened": 6, "total_diff": 15}}, {"fold": 2, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7521008403361344, "improvement": 0.008403361344537785, "correction_stats": {"improved": 8, "worsened": 6, "total_diff": 14}}, {"fold": 3, "base_accuracy": 0.7394957983193278, "weighted_accuracy": 0.7394957983193278, "improvement": 0.0, "correction_stats": {"improved": 7, "worsened": 7, "total_diff": 14}}, {"fold": 4, "base_accuracy": 0.7510548523206751, "weighted_accuracy": 0.7510548523206751, "improvement": 0.0, "correction_stats": {"improved": 8, "worsened": 8, "total_diff": 16}}, {"fold": 5, "base_accuracy": 0.729957805907173, "weighted_accuracy": 0.7468354430379747, "improvement": 0.016877637130801704, "correction_stats": {"improved": 20, "worsened": 16, "total_diff": 36}}]}, "optuna_results": {"best_params": {"use_conflict_weighting": false, "use_consistency_weight": true, "consistency_1_weight": 0.7378569551713141, "consistency_0_weight": 1.4768862059976637, "use_hospital_consistency_weight": true, "sh6_consistency_1_weight": 0.7056266805824618, "sh6_consistency_0_weight": 2.1104732772160206, "sh10_consistency_1_weight": 1.4709894568345623, "sh10_consistency_0_weight": 0.8675852143250504, "bj301_consistency_1_weight": 1.9484888640228224, "bj301_consistency_0_weight": 1.0329169255570634, "gdnh_consistency_1_weight": 1.88412661846567, "gdnh_consistency_0_weight": 2.839867551246207, "use_quality_weight": true, "quality_good_weight": 0.5659536443074591, "quality_poor_weight": 1.175217486332374, "use_score_range_weight": false, "use_hospital_weight": true, "hospital_sh6_weight": 4.283591984801299, "hospital_sh10_weight": 0.9437667112902971, "hospital_bj301_weight": 3.1147910701736587, "hospital_gdnh_weight": 2.4929118132354797, "use_combination_weight": false}, "best_value": 0.7651668262241605, "n_trials": 1000}}, "v2.0.51": {"created_at": "2025-03-13 12:46:54", "model_type": "DoubleModel", "threshold1": 0.5, "threshold2": 0.5, "feature_version": ["feature0303_PureBoruta_0227data(1')", "feature0303_PureBoruta_0227data(1''3)"]}, "v2.0.7": {"version": "v2.0.7", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-14 18:51:57", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权增强:无", "train_metrics": {"accuracy": 0.9436026936026936, "precision": 0.962589928057554, "recall": 0.9422535211267605, "specificity": 0.9456066945606695, "f1": 0.9523131672597864, "auc": 0.983133950144381}, "feature_count": 308, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.0563973063973064, "cv_results": {"best_fold": 5, "best_score": 0.7805907172995781, "fold_metrics": [{"fold": 1, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.7605042016806722, "weighted_accuracy_xgb": 0.7605042016806722, "improvement": 0.03361344537815125, "correction_stats": {"improved": 18, "worsened": 10, "total_diff": 28}}, {"fold": 2, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.7436974789915967, "weighted_accuracy_xgb": 0.7436974789915967, "improvement": 0.01680672268907568, "correction_stats": {"improved": 15, "worsened": 11, "total_diff": 26}}, {"fold": 3, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7689075630252101, "weighted_accuracy_xgb": 0.7689075630252101, "improvement": 0.037815126050420256, "correction_stats": {"improved": 20, "worsened": 11, "total_diff": 31}}, {"fold": 4, "base_accuracy": 0.70042194092827, "weighted_accuracy": 0.7510548523206751, "weighted_accuracy_xgb": 0.7510548523206751, "improvement": 0.05063291139240511, "correction_stats": {"improved": 21, "worsened": 9, "total_diff": 30}}, {"fold": 5, "base_accuracy": 0.759493670886076, "weighted_accuracy": 0.7805907172995781, "weighted_accuracy_xgb": 0.7805907172995781, "improvement": 0.021097046413502074, "correction_stats": {"improved": 20, "worsened": 15, "total_diff": 35}}]}, "optuna_results": {"best_params": {"sh六院_pos_consistent": 0.5756018244801845, "sh六院_pos_inconsistent": 2.6270029093781755, "sh六院_neg_consistent": 1.909131406101853, "sh六院_neg_inconsistent": 3.4712468888393646, "sh十院_pos_consistent": 2.938873120636476, "sh十院_pos_inconsistent": 0.3368560588800724, "sh十院_neg_consistent": 4.219812017030103, "sh十院_neg_inconsistent": 1.9427640462137532, "bj301_pos_consistent": 3.112562983620491, "bj301_pos_inconsistent": 1.2403587363547515, "bj301_neg_consistent": 1.7077604959076977, "bj301_neg_inconsistent": 4.8780014941920005, "gd南海_pos_consistent": 1.3725861364443488, "gd南海_pos_inconsistent": 0.2880134190296945, "gd南海_neg_consistent": 0.39223910978060755, "gd南海_neg_inconsistent": 0.6660165593346588, "quality_good_weight": 0.5689184794502087, "quality_poor_weight": 1.070237407379435, "conflict_alpha": 6.150945502440983, "voting_weight": 2.604837112668607}, "best_value": 0.7609509626635464, "n_trials": 1000, "conflict_alpha": 6.150945502440983, "voting_weight": 2.604837112668607}}, "v2.0.72": {"version": "v2.0.72", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-16 19:48:52", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权,全面optuna增强:无", "train_metrics": {"accuracy": 0.9612794612794613, "precision": 0.9662921348314607, "recall": 0.9690140845070423, "specificity": 0.9497907949790795, "f1": 0.9676511954992968, "auc": 0.9892008957510755}, "feature_count": 308, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.03872053872053872, "cv_results": {"random_seeds": [870, 970, 1070], "best_seed": 970, "best_fold": 4, "best_score": 0.8059071729957806, "fold_metrics": [{"seed": 870, "fold": 1, "base_accuracy": 0.7689075630252101, "weighted_accuracy": 0.7857142857142857, "improvement": 0.01680672268907557, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"seed": 870, "fold": 2, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7394957983193278, "improvement": -0.004201680672268893, "correction_stats": {"improved": 5, "worsened": 6, "total_diff": 11}}, {"seed": 870, "fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.8025210084033614, "improvement": 0.05882352941176472, "correction_stats": {"improved": 16, "worsened": 2, "total_diff": 18}}, {"seed": 870, "fold": 4, "base_accuracy": 0.7721518987341772, "weighted_accuracy": 0.7763713080168776, "improvement": 0.00421940928270037, "correction_stats": {"improved": 8, "worsened": 7, "total_diff": 15}}, {"seed": 870, "fold": 5, "base_accuracy": 0.729957805907173, "weighted_accuracy": 0.7468354430379747, "improvement": 0.016877637130801704, "correction_stats": {"improved": 13, "worsened": 9, "total_diff": 22}}, {"seed": 970, "fold": 1, "base_accuracy": 0.7521008403361344, "weighted_accuracy": 0.7689075630252101, "improvement": 0.01680672268907568, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"seed": 970, "fold": 2, "base_accuracy": 0.7563025210084033, "weighted_accuracy": 0.7773109243697479, "improvement": 0.021008403361344574, "correction_stats": {"improved": 15, "worsened": 10, "total_diff": 25}}, {"seed": 970, "fold": 3, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7689075630252101, "improvement": 0.037815126050420256, "correction_stats": {"improved": 15, "worsened": 6, "total_diff": 21}}, {"seed": 970, "fold": 4, "base_accuracy": 0.7679324894514767, "weighted_accuracy": 0.8059071729957806, "improvement": 0.03797468354430389, "correction_stats": {"improved": 15, "worsened": 6, "total_diff": 21}}, {"seed": 970, "fold": 5, "base_accuracy": 0.7510548523206751, "weighted_accuracy": 0.7383966244725738, "improvement": -0.012658227848101333, "correction_stats": {"improved": 5, "worsened": 8, "total_diff": 13}}, {"seed": 1070, "fold": 1, "base_accuracy": 0.7142857142857143, "weighted_accuracy": 0.726890756302521, "improvement": 0.012605042016806678, "correction_stats": {"improved": 13, "worsened": 10, "total_diff": 23}}, {"seed": 1070, "fold": 2, "base_accuracy": 0.7773109243697479, "weighted_accuracy": 0.7773109243697479, "improvement": 0.0, "correction_stats": {"improved": 10, "worsened": 10, "total_diff": 20}}, {"seed": 1070, "fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7478991596638656, "improvement": 0.004201680672268893, "correction_stats": {"improved": 7, "worsened": 6, "total_diff": 13}}, {"seed": 1070, "fold": 4, "base_accuracy": 0.7679324894514767, "weighted_accuracy": 0.7721518987341772, "improvement": 0.0042194092827004814, "correction_stats": {"improved": 8, "worsened": 7, "total_diff": 15}}, {"seed": 1070, "fold": 5, "base_accuracy": 0.7257383966244726, "weighted_accuracy": 0.7679324894514767, "improvement": 0.04219409282700415, "correction_stats": {"improved": 15, "worsened": 5, "total_diff": 20}}]}, "optuna_results": {"best_params": {"n_estimators": 900, "learning_rate": 0.04618135570894504, "max_depth": 7, "subsample": 0.6243582300438759, "colsample_bytree": 0.7528151459496338, "sh六院_pos_consistent": 1.7659200666661719, "sh六院_pos_inconsistent": 1.4360308020170773, "sh六院_neg_consistent": 4.9397140179772165, "sh六院_neg_inconsistent": 4.022890887962563, "sh十院_pos_consistent": 1.3805687290476667, "sh十院_pos_inconsistent": 1.8094123332191379, "sh十院_neg_consistent": 1.9755369295522476, "sh十院_neg_inconsistent": 4.708698817829852, "bj301_pos_consistent": 1.8296728391485593, "bj301_pos_inconsistent": 4.484859107458527, "bj301_neg_consistent": 4.9664403625761695, "bj301_neg_inconsistent": 2.766798041709158, "gd南海_pos_consistent": 0.23761462738598826, "gd南海_pos_inconsistent": 2.760339166617062, "gd南海_neg_consistent": 2.929696086173247, "gd南海_neg_inconsistent": 3.0448837341589514, "quality_good_weight": 0.5869990845355126, "quality_poor_weight": 1.3866382683988492, "conflict_alpha": 1.0059862821454095}, "best_value": 0.7668368613268093, "n_trials": 1000, "conflict_alpha": 1.0059862821454095}, "xgb_params_optimized": true}, "v2.0.73": {"version": "v2.0.73", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-17 10:45:58", "description": "数据:0227版数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权,按提升optuna增强:无", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 308, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.0, "cv_results": {"random_seeds": [924, 1024, 1124], "best_seed": 924, "best_fold": 3, "best_score": 0.7899159663865546, "fold_metrics": [{"seed": 924, "fold": 1, "base_accuracy": 0.7142857142857143, "weighted_accuracy": 0.7184873949579832, "improvement": 0.004201680672268893, "correction_stats": {"improved": 19, "worsened": 18, "total_diff": 37}}, {"seed": 924, "fold": 2, "base_accuracy": 0.7184873949579832, "weighted_accuracy": 0.7563025210084033, "improvement": 0.037815126050420145, "correction_stats": {"improved": 17, "worsened": 8, "total_diff": 25}}, {"seed": 924, "fold": 3, "base_accuracy": 0.7478991596638656, "weighted_accuracy": 0.7899159663865546, "improvement": 0.04201680672268904, "correction_stats": {"improved": 20, "worsened": 10, "total_diff": 30}}, {"seed": 924, "fold": 4, "base_accuracy": 0.7172995780590717, "weighted_accuracy": 0.7679324894514767, "improvement": 0.050632911392405, "correction_stats": {"improved": 22, "worsened": 10, "total_diff": 32}}, {"seed": 924, "fold": 5, "base_accuracy": 0.7215189873417721, "weighted_accuracy": 0.7426160337552743, "improvement": 0.021097046413502185, "correction_stats": {"improved": 17, "worsened": 12, "total_diff": 29}}, {"seed": 1024, "fold": 1, "base_accuracy": 0.7647058823529411, "weighted_accuracy": 0.7689075630252101, "improvement": 0.004201680672269004, "correction_stats": {"improved": 10, "worsened": 9, "total_diff": 19}}, {"seed": 1024, "fold": 2, "base_accuracy": 0.7941176470588235, "weighted_accuracy": 0.7857142857142857, "improvement": -0.008403361344537785, "correction_stats": {"improved": 9, "worsened": 11, "total_diff": 20}}, {"seed": 1024, "fold": 3, "base_accuracy": 0.773109243697479, "weighted_accuracy": 0.7647058823529411, "improvement": -0.008403361344537896, "correction_stats": {"improved": 12, "worsened": 14, "total_diff": 26}}, {"seed": 1024, "fold": 4, "base_accuracy": 0.759493670886076, "weighted_accuracy": 0.7510548523206751, "improvement": -0.008438818565400852, "correction_stats": {"improved": 15, "worsened": 17, "total_diff": 32}}, {"seed": 1024, "fold": 5, "base_accuracy": 0.****************, "weighted_accuracy": 0.6835443037974683, "improvement": 0.016877637130801704, "correction_stats": {"improved": 17, "worsened": 13, "total_diff": 30}}, {"seed": 1124, "fold": 1, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.726890756302521, "improvement": 0.0, "correction_stats": {"improved": 12, "worsened": 12, "total_diff": 24}}, {"seed": 1124, "fold": 2, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7521008403361344, "improvement": 0.008403361344537785, "correction_stats": {"improved": 13, "worsened": 11, "total_diff": 24}}, {"seed": 1124, "fold": 3, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7436974789915967, "improvement": 0.012605042016806789, "correction_stats": {"improved": 15, "worsened": 12, "total_diff": 27}}, {"seed": 1124, "fold": 4, "base_accuracy": 0.7383966244725738, "weighted_accuracy": 0.759493670886076, "improvement": 0.021097046413502185, "correction_stats": {"improved": 16, "worsened": 11, "total_diff": 27}}, {"seed": 1124, "fold": 5, "base_accuracy": 0.7383966244725738, "weighted_accuracy": 0.729957805907173, "improvement": -0.008438818565400852, "correction_stats": {"improved": 9, "worsened": 11, "total_diff": 20}}]}, "optuna_results": {"best_params": {"n_estimators": 400, "learning_rate": 0.25970744226675785, "max_depth": 8, "subsample": 0.8532837697553226, "colsample_bytree": 0.6128519970038657, "sh六院_pos_consistent": 0.2848666202730876, "sh六院_pos_inconsistent": 1.146595036396542, "sh六院_neg_consistent": 4.192302691937529, "sh六院_neg_inconsistent": 2.553252950008339, "sh十院_pos_consistent": 3.2445103144777594, "sh十院_pos_inconsistent": 0.7345452727971431, "sh十院_neg_consistent": 3.753684492010239, "sh十院_neg_inconsistent": 4.3584894788113, "bj301_pos_consistent": 2.32195273020693, "bj301_pos_inconsistent": 0.6891268375002055, "bj301_neg_consistent": 1.804697978854712, "bj301_neg_inconsistent": 1.635023035213847, "gd南海_pos_consistent": 2.141153372638483, "gd南海_pos_inconsistent": 1.0737641568191727, "gd南海_neg_consistent": 1.5165456723372, "gd南海_neg_inconsistent": 2.508486515523752, "quality_good_weight": 1.0163038469385013, "quality_poor_weight": 0.8413762427978667, "conflict_alpha": 3.911355663289494}, "best_value": 0.012350931933955023, "n_trials": 5, "conflict_alpha": 3.911355663289494}, "xgb_params_optimized": true}, "v2.0.74": {"version": "v2.0.74", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-17 12:28:44", "description": "数据:0227版数据,训练测试全量原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权,按提升optuna增强:无", "train_metrics": {"accuracy": 0.9535353535353536, "precision": 0.9616685456595265, "recall": 0.9605855855855856, "specificity": 0.9430485762144054, "f1": 0.9611267605633802, "auc": 0.9836645690917047}, "feature_count": 305, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "error_rate": 0.046464646464646465, "cv_results": {"random_seeds": [924, 1024, 1124], "best_seed": 924, "best_fold": 1, "best_score": 0.7676767676767676, "fold_metrics": [{"seed": 924, "fold": 1, "base_accuracy": 0.7474747474747475, "weighted_accuracy": 0.7676767676767676, "improvement": 0.02020202020202011, "correction_stats": {"improved": 21, "worsened": 15, "total_diff": 36}}, {"seed": 924, "fold": 2, "base_accuracy": 0.7171717171717171, "weighted_accuracy": 0.7138047138047138, "improvement": -0.0033670033670033517, "correction_stats": {"improved": 23, "worsened": 24, "total_diff": 47}}, {"seed": 924, "fold": 3, "base_accuracy": 0.7441077441077442, "weighted_accuracy": 0.7306397306397306, "improvement": -0.013468013468013518, "correction_stats": {"improved": 16, "worsened": 20, "total_diff": 36}}, {"seed": 924, "fold": 4, "base_accuracy": 0.6868686868686869, "weighted_accuracy": 0.7070707070707071, "improvement": 0.02020202020202022, "correction_stats": {"improved": 21, "worsened": 15, "total_diff": 36}}, {"seed": 924, "fold": 5, "base_accuracy": 0.7272727272727273, "weighted_accuracy": 0.7373737373737373, "improvement": 0.010101010101010055, "correction_stats": {"improved": 16, "worsened": 13, "total_diff": 29}}, {"seed": 1024, "fold": 1, "base_accuracy": 0.7643097643097643, "weighted_accuracy": 0.7441077441077442, "improvement": -0.02020202020202011, "correction_stats": {"improved": 12, "worsened": 18, "total_diff": 30}}, {"seed": 1024, "fold": 2, "base_accuracy": 0.7441077441077442, "weighted_accuracy": 0.7272727272727273, "improvement": -0.01683501683501687, "correction_stats": {"improved": 14, "worsened": 19, "total_diff": 33}}, {"seed": 1024, "fold": 3, "base_accuracy": 0.7272727272727273, "weighted_accuracy": 0.7070707070707071, "improvement": -0.02020202020202022, "correction_stats": {"improved": 17, "worsened": 23, "total_diff": 40}}, {"seed": 1024, "fold": 4, "base_accuracy": 0.7441077441077442, "weighted_accuracy": 0.7138047138047138, "improvement": -0.030303030303030387, "correction_stats": {"improved": 14, "worsened": 23, "total_diff": 37}}, {"seed": 1024, "fold": 5, "base_accuracy": 0.7676767676767676, "weighted_accuracy": 0.7474747474747475, "improvement": -0.02020202020202011, "correction_stats": {"improved": 12, "worsened": 18, "total_diff": 30}}, {"seed": 1124, "fold": 1, "base_accuracy": 0.7676767676767676, "weighted_accuracy": 0.7676767676767676, "improvement": 0.0, "correction_stats": {"improved": 18, "worsened": 18, "total_diff": 36}}, {"seed": 1124, "fold": 2, "base_accuracy": 0.7811447811447811, "weighted_accuracy": 0.7575757575757576, "improvement": -0.023569023569023573, "correction_stats": {"improved": 16, "worsened": 23, "total_diff": 39}}, {"seed": 1124, "fold": 3, "base_accuracy": 0.6936026936026936, "weighted_accuracy": 0.****************, "improvement": -0.026936026936026924, "correction_stats": {"improved": 10, "worsened": 18, "total_diff": 28}}, {"seed": 1124, "fold": 4, "base_accuracy": 0.7037037037037037, "weighted_accuracy": 0.7239057239057239, "improvement": 0.02020202020202022, "correction_stats": {"improved": 23, "worsened": 17, "total_diff": 40}}, {"seed": 1124, "fold": 5, "base_accuracy": 0.7710437710437711, "weighted_accuracy": 0.7508417508417509, "improvement": -0.02020202020202022, "correction_stats": {"improved": 14, "worsened": 20, "total_diff": 34}}]}, "optuna_results": {"best_params": {"n_estimators": 400, "learning_rate": 0.25970744226675785, "max_depth": 8, "subsample": 0.8532837697553226, "colsample_bytree": 0.6128519970038657, "sh六院_pos_consistent": 0.2848666202730876, "sh六院_pos_inconsistent": 1.146595036396542, "sh六院_neg_consistent": 4.192302691937529, "sh六院_neg_inconsistent": 2.553252950008339, "sh十院_pos_consistent": 3.2445103144777594, "sh十院_pos_inconsistent": 0.7345452727971431, "sh十院_neg_consistent": 3.753684492010239, "sh十院_neg_inconsistent": 4.3584894788113, "bj301_pos_consistent": 2.32195273020693, "bj301_pos_inconsistent": 0.6891268375002055, "bj301_neg_consistent": 1.804697978854712, "bj301_neg_inconsistent": 1.635023035213847, "gd南海_pos_consistent": 2.141153372638483, "gd南海_pos_inconsistent": 1.0737641568191727, "gd南海_neg_consistent": 1.5165456723372, "gd南海_neg_inconsistent": 2.508486515523752, "quality_good_weight": 1.0163038469385013, "quality_poor_weight": 0.8413762427978667, "conflict_alpha": 3.911355663289494}, "best_value": 0.012350931933955023, "n_trials": 0, "conflict_alpha": 3.911355663289494}, "xgb_params_optimized": true}, "v2.0.69": {"version": "v2.0.69", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-17 14:50:57", "description": "数据:0227版数据,训练测试全量原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权,按提升optuna增强:无", "train_metrics": {"accuracy": 0.9503367003367004, "precision": 0.950207468879668, "recall": 0.967605633802817, "specificity": 0.9246861924686193, "f1": 0.9588276343335659, "auc": 0.9863957805409865}, "feature_count": 789, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.049663299663299666, "cv_results": {"random_seeds": [924, 1024, 1124], "best_seed": 924, "best_fold": 3, "best_score": 0.7521008403361344, "fold_metrics": [{"seed": 924, "fold": 1, "base_accuracy": 0.7605042016806722, "weighted_accuracy": 0.7478991596638656, "improvement": -0.012605042016806678, "correction_stats": {"improved": 13, "worsened": 16, "total_diff": 29}}, {"seed": 924, "fold": 2, "base_accuracy": 0.7773109243697479, "weighted_accuracy": 0.7226890756302521, "improvement": -0.054621848739495826, "correction_stats": {"improved": 4, "worsened": 17, "total_diff": 21}}, {"seed": 924, "fold": 3, "base_accuracy": 0.7521008403361344, "weighted_accuracy": 0.7521008403361344, "improvement": 0.0, "correction_stats": {"improved": 13, "worsened": 13, "total_diff": 26}}, {"seed": 924, "fold": 4, "base_accuracy": 0.7468354430379747, "weighted_accuracy": 0.70042194092827, "improvement": -0.04641350210970463, "correction_stats": {"improved": 7, "worsened": 18, "total_diff": 25}}, {"seed": 924, "fold": 5, "base_accuracy": 0.7130801687763713, "weighted_accuracy": 0.7172995780590717, "improvement": 0.0042194092827004814, "correction_stats": {"improved": 11, "worsened": 10, "total_diff": 21}}, {"seed": 1024, "fold": 1, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7394957983193278, "improvement": 0.004201680672268893, "correction_stats": {"improved": 19, "worsened": 18, "total_diff": 37}}, {"seed": 1024, "fold": 2, "base_accuracy": 0.7605042016806722, "weighted_accuracy": 0.7394957983193278, "improvement": -0.021008403361344463, "correction_stats": {"improved": 14, "worsened": 19, "total_diff": 33}}, {"seed": 1024, "fold": 3, "base_accuracy": 0.7521008403361344, "weighted_accuracy": 0.7184873949579832, "improvement": -0.03361344537815125, "correction_stats": {"improved": 11, "worsened": 19, "total_diff": 30}}, {"seed": 1024, "fold": 4, "base_accuracy": 0.7257383966244726, "weighted_accuracy": 0.729957805907173, "improvement": 0.00421940928270037, "correction_stats": {"improved": 17, "worsened": 16, "total_diff": 33}}, {"seed": 1024, "fold": 5, "base_accuracy": 0.6919831223628692, "weighted_accuracy": 0.6497890295358649, "improvement": -0.04219409282700426, "correction_stats": {"improved": 9, "worsened": 19, "total_diff": 28}}, {"seed": 1124, "fold": 1, "base_accuracy": 0.7184873949579832, "weighted_accuracy": 0.7394957983193278, "improvement": 0.021008403361344574, "correction_stats": {"improved": 21, "worsened": 16, "total_diff": 37}}, {"seed": 1124, "fold": 2, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7436974789915967, "improvement": 0.012605042016806789, "correction_stats": {"improved": 19, "worsened": 16, "total_diff": 35}}, {"seed": 1124, "fold": 3, "base_accuracy": 0.6974789915966386, "weighted_accuracy": 0.7100840336134454, "improvement": 0.012605042016806789, "correction_stats": {"improved": 15, "worsened": 12, "total_diff": 27}}, {"seed": 1124, "fold": 4, "base_accuracy": 0.7383966244725738, "weighted_accuracy": 0.7341772151898734, "improvement": -0.00421940928270037, "correction_stats": {"improved": 10, "worsened": 11, "total_diff": 21}}, {"seed": 1124, "fold": 5, "base_accuracy": 0.679324894514768, "weighted_accuracy": 0.7215189873417721, "improvement": 0.04219409282700415, "correction_stats": {"improved": 18, "worsened": 8, "total_diff": 26}}]}, "optuna_results": {"best_params": {"n_estimators": 400, "learning_rate": 0.25970744226675785, "max_depth": 8, "subsample": 0.8532837697553226, "colsample_bytree": 0.6128519970038657, "sh六院_pos_consistent": 0.2848666202730876, "sh六院_pos_inconsistent": 1.146595036396542, "sh六院_neg_consistent": 4.192302691937529, "sh六院_neg_inconsistent": 2.553252950008339, "sh十院_pos_consistent": 3.2445103144777594, "sh十院_pos_inconsistent": 0.7345452727971431, "sh十院_neg_consistent": 3.753684492010239, "sh十院_neg_inconsistent": 4.3584894788113, "bj301_pos_consistent": 2.32195273020693, "bj301_pos_inconsistent": 0.6891268375002055, "bj301_neg_consistent": 1.804697978854712, "bj301_neg_inconsistent": 1.635023035213847, "gd南海_pos_consistent": 2.141153372638483, "gd南海_pos_inconsistent": 1.0737641568191727, "gd南海_neg_consistent": 1.5165456723372, "gd南海_neg_inconsistent": 2.508486515523752, "quality_good_weight": 1.0163038469385013, "quality_poor_weight": 0.8413762427978667, "conflict_alpha": 3.911355663289494}, "best_value": 0.012350931933955023, "n_trials": 0, "conflict_alpha": 3.911355663289494}, "xgb_params_optimized": true}, "v2.0.68": {"version": "v2.0.68", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-17 15:31:32", "description": "数据:0227版数据,训练测试全量原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权,按提升optuna增强:无", "train_metrics": {"accuracy": 0.9528619528619529, "precision": 0.9529085872576177, "recall": 0.9690140845070423, "specificity": 0.9288702928870293, "f1": 0.9608938547486033, "auc": 0.98978136602039}, "feature_count": 789, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.04713804713804714, "cv_results": {"random_seeds": [1243, 1343, 1443], "best_seed": 1443, "best_fold": 2, "best_score": 0.7647058823529411, "fold_metrics": [{"seed": 1243, "fold": 1, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7605042016806722, "improvement": 0.025210084033613356, "correction_stats": {"improved": 9, "worsened": 3, "total_diff": 12}}, {"seed": 1243, "fold": 2, "base_accuracy": 0.7352941176470589, "weighted_accuracy": 0.7226890756302521, "improvement": -0.012605042016806789, "correction_stats": {"improved": 6, "worsened": 9, "total_diff": 15}}, {"seed": 1243, "fold": 3, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7352941176470589, "improvement": 0.004201680672269004, "correction_stats": {"improved": 7, "worsened": 6, "total_diff": 13}}, {"seed": 1243, "fold": 4, "base_accuracy": 0.7383966244725738, "weighted_accuracy": 0.7426160337552743, "improvement": 0.0042194092827004814, "correction_stats": {"improved": 5, "worsened": 4, "total_diff": 9}}, {"seed": 1243, "fold": 5, "base_accuracy": 0.7510548523206751, "weighted_accuracy": 0.7383966244725738, "improvement": -0.012658227848101333, "correction_stats": {"improved": 5, "worsened": 8, "total_diff": 13}}, {"seed": 1343, "fold": 1, "base_accuracy": 0.7521008403361344, "weighted_accuracy": 0.7436974789915967, "improvement": -0.008403361344537785, "correction_stats": {"improved": 5, "worsened": 7, "total_diff": 12}}, {"seed": 1343, "fold": 2, "base_accuracy": 0.7563025210084033, "weighted_accuracy": 0.7605042016806722, "improvement": 0.004201680672268893, "correction_stats": {"improved": 6, "worsened": 5, "total_diff": 11}}, {"seed": 1343, "fold": 3, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.7058823529411765, "improvement": -0.021008403361344463, "correction_stats": {"improved": 3, "worsened": 8, "total_diff": 11}}, {"seed": 1343, "fold": 4, "base_accuracy": 0.729957805907173, "weighted_accuracy": 0.7341772151898734, "improvement": 0.0042194092827004814, "correction_stats": {"improved": 10, "worsened": 9, "total_diff": 19}}, {"seed": 1343, "fold": 5, "base_accuracy": 0.7130801687763713, "weighted_accuracy": 0.729957805907173, "improvement": 0.016877637130801704, "correction_stats": {"improved": 4, "worsened": 0, "total_diff": 4}}, {"seed": 1443, "fold": 1, "base_accuracy": 0.7184873949579832, "weighted_accuracy": 0.7310924369747899, "improvement": 0.012605042016806678, "correction_stats": {"improved": 10, "worsened": 7, "total_diff": 17}}, {"seed": 1443, "fold": 2, "base_accuracy": 0.7899159663865546, "weighted_accuracy": 0.7647058823529411, "improvement": -0.025210084033613467, "correction_stats": {"improved": 6, "worsened": 12, "total_diff": 18}}, {"seed": 1443, "fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7436974789915967, "improvement": 0.0, "correction_stats": {"improved": 9, "worsened": 9, "total_diff": 18}}, {"seed": 1443, "fold": 4, "base_accuracy": 0.7046413502109705, "weighted_accuracy": 0.729957805907173, "improvement": 0.025316455696202445, "correction_stats": {"improved": 9, "worsened": 3, "total_diff": 12}}, {"seed": 1443, "fold": 5, "base_accuracy": 0.7215189873417721, "weighted_accuracy": 0.7215189873417721, "improvement": 0.0, "correction_stats": {"improved": 8, "worsened": 8, "total_diff": 16}}]}, "optuna_results": {"best_params": {"n_estimators": 800, "learning_rate": 0.044571967545593595, "max_depth": 9, "subsample": 0.8834910369037949, "colsample_bytree": 0.6843215614047402, "sh六院_pos_consistent": 2.6889217250338406, "sh六院_pos_inconsistent": 2.5014281787762944, "sh六院_neg_consistent": 2.5662031792445963, "sh六院_neg_inconsistent": 4.969660313164862, "sh十院_pos_consistent": 0.773611741176298, "sh十院_pos_inconsistent": 0.3373163887359733, "sh十院_neg_consistent": 0.2826850954782675, "sh十院_neg_inconsistent": 2.324199879483485, "bj301_pos_consistent": 3.8079112871469243, "bj301_pos_inconsistent": 3.173607688171226, "bj301_neg_consistent": 3.3947779552781823, "bj301_neg_inconsistent": 3.3210122216941778, "gd南海_pos_consistent": 1.1112179420481971, "gd南海_pos_inconsistent": 2.994894884157586, "gd南海_neg_consistent": 4.78172740014165, "gd南海_neg_inconsistent": 0.9641050004619246, "quality_good_weight": 0.9856213825214999, "quality_poor_weight": 0.9855745433946441, "conflict_alpha": 2.5557170548561094}, "best_value": 0.7631859494852794, "n_trials": 0, "conflict_alpha": 2.5557170548561094}, "xgb_params_optimized": true}, "v2.0.67": {"version": "v2.0.67", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-19 02:58:53", "description": "数据:0227版数据,训练测试全量原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta模型:XGB,矛盾数据加权,按提升optuna增强:无", "train_metrics": {"accuracy": 0.9570707070707071, "precision": 0.957004160887656, "recall": 0.971830985915493, "specificity": 0.9351464435146444, "f1": 0.9643605870020965, "auc": 0.9900907537273852}, "feature_count": 789, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.04292929292929293, "cv_results": {"random_seeds": [1121, 1221, 1321], "best_seed": 1221, "best_fold": 2, "best_score": 0.7857142857142857, "fold_metrics": [{"seed": 1121, "fold": 1, "base_accuracy": 0.7058823529411765, "weighted_accuracy": 0.7226890756302521, "improvement": 0.01680672268907557, "correction_stats": {"improved": 14, "worsened": 10, "total_diff": 24}}, {"seed": 1121, "fold": 2, "base_accuracy": 0.7563025210084033, "weighted_accuracy": 0.7605042016806722, "improvement": 0.004201680672268893, "correction_stats": {"improved": 5, "worsened": 4, "total_diff": 9}}, {"seed": 1121, "fold": 3, "base_accuracy": 0.7100840336134454, "weighted_accuracy": 0.7352941176470589, "improvement": 0.025210084033613467, "correction_stats": {"improved": 12, "worsened": 6, "total_diff": 18}}, {"seed": 1121, "fold": 4, "base_accuracy": 0.7552742616033755, "weighted_accuracy": 0.7552742616033755, "improvement": 0.0, "correction_stats": {"improved": 6, "worsened": 6, "total_diff": 12}}, {"seed": 1121, "fold": 5, "base_accuracy": 0.7172995780590717, "weighted_accuracy": 0.7215189873417721, "improvement": 0.00421940928270037, "correction_stats": {"improved": 8, "worsened": 7, "total_diff": 15}}, {"seed": 1221, "fold": 1, "base_accuracy": 0.7478991596638656, "weighted_accuracy": 0.7647058823529411, "improvement": 0.01680672268907557, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"seed": 1221, "fold": 2, "base_accuracy": 0.7941176470588235, "weighted_accuracy": 0.7857142857142857, "improvement": -0.008403361344537785, "correction_stats": {"improved": 2, "worsened": 4, "total_diff": 6}}, {"seed": 1221, "fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7436974789915967, "improvement": 0.0, "correction_stats": {"improved": 6, "worsened": 6, "total_diff": 12}}, {"seed": 1221, "fold": 4, "base_accuracy": 0.729957805907173, "weighted_accuracy": 0.7468354430379747, "improvement": 0.016877637130801704, "correction_stats": {"improved": 9, "worsened": 5, "total_diff": 14}}, {"seed": 1221, "fold": 5, "base_accuracy": 0.70042194092827, "weighted_accuracy": 0.6962025316455697, "improvement": -0.00421940928270037, "correction_stats": {"improved": 5, "worsened": 6, "total_diff": 11}}, {"seed": 1321, "fold": 1, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7394957983193278, "improvement": -0.004201680672268893, "correction_stats": {"improved": 6, "worsened": 7, "total_diff": 13}}, {"seed": 1321, "fold": 2, "base_accuracy": 0.7647058823529411, "weighted_accuracy": 0.7436974789915967, "improvement": -0.021008403361344463, "correction_stats": {"improved": 4, "worsened": 9, "total_diff": 13}}, {"seed": 1321, "fold": 3, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.7605042016806722, "improvement": 0.03361344537815125, "correction_stats": {"improved": 11, "worsened": 3, "total_diff": 14}}, {"seed": 1321, "fold": 4, "base_accuracy": 0.7637130801687764, "weighted_accuracy": 0.7637130801687764, "improvement": 0.0, "correction_stats": {"improved": 6, "worsened": 6, "total_diff": 12}}, {"seed": 1321, "fold": 5, "base_accuracy": 0.7341772151898734, "weighted_accuracy": 0.7383966244725738, "improvement": 0.00421940928270037, "correction_stats": {"improved": 9, "worsened": 8, "total_diff": 17}}]}, "optuna_results": {"best_params": {"n_estimators": 1000, "learning_rate": 0.010147028856594525, "max_depth": 8, "subsample": 0.9260806072304738, "colsample_bytree": 0.8046723200063755, "sh六院_pos_consistent": 1.3948899289546572, "sh六院_pos_inconsistent": 2.785058094279518, "sh六院_neg_consistent": 2.713316063998297, "sh六院_neg_inconsistent": 1.537621909457364, "sh十院_pos_consistent": 0.24961617837618605, "sh十院_pos_inconsistent": 0.7638586786942252, "sh十院_neg_consistent": 0.2715980752490603, "sh十院_neg_inconsistent": 0.39920030038217996, "bj301_pos_consistent": 1.4706184787445886, "bj301_pos_inconsistent": 2.215978805058312, "bj301_neg_consistent": 0.9193479455615955, "bj301_neg_inconsistent": 1.****************, "gd南海_pos_consistent": 1.****************, "gd南海_pos_inconsistent": 0.****************, "gd南海_neg_consistent": 2.**************, "gd南海_neg_inconsistent": 1.****************, "quality_good_weight": 0.****************, "quality_poor_weight": 1.***************, "conflict_alpha": 1.****************}, "best_value": 0.****************, "n_trials": 500, "conflict_alpha": 1.****************}, "xgb_params_optimized": true}, "v2.1.0": {"version": "v2.1.0", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-18 09:07:24", "description": "数据:0227版数据,训练数据原始特征:0303版特征(自动时刻点0.5)选择特征:PureBoruta,追加cur/ci特征 789模型:XGB,optuna后增强:无", "train_metrics": {"accuracy": 0.9553872053872053, "precision": 0.9581589958158996, "recall": 0.967605633802817, "specificity": 0.9372384937238494, "f1": 0.9628591450595655, "auc": 0.9879662914726854}, "feature_count": 789, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.04461279461279461, "cv_results": {"random_seeds": [1243, 1343, 1443], "best_seed": 870, "best_fold": 1, "best_score": 0.7773109243697479, "fold_metrics": [{"seed": 870, "fold": 1, "base_accuracy": 0.7899159663865546, "weighted_accuracy": 0.7773109243697479, "improvement": -0.012605042016806678, "correction_stats": {"improved": 4, "worsened": 7, "total_diff": 11}}, {"seed": 870, "fold": 2, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7226890756302521, "improvement": -0.021008403361344574, "correction_stats": {"improved": 4, "worsened": 9, "total_diff": 13}}, {"seed": 870, "fold": 3, "base_accuracy": 0.7394957983193278, "weighted_accuracy": 0.7436974789915967, "improvement": 0.004201680672268893, "correction_stats": {"improved": 5, "worsened": 4, "total_diff": 9}}, {"seed": 870, "fold": 4, "base_accuracy": 0.7848101265822784, "weighted_accuracy": 0.7763713080168776, "improvement": -0.008438818565400852, "correction_stats": {"improved": 5, "worsened": 7, "total_diff": 12}}, {"seed": 870, "fold": 5, "base_accuracy": 0.7257383966244726, "weighted_accuracy": 0.7172995780590717, "improvement": -0.008438818565400852, "correction_stats": {"improved": 5, "worsened": 7, "total_diff": 12}}, {"seed": 970, "fold": 1, "base_accuracy": 0.7647058823529411, "weighted_accuracy": 0.7647058823529411, "improvement": 0.0, "correction_stats": {"improved": 8, "worsened": 8, "total_diff": 16}}, {"seed": 970, "fold": 2, "base_accuracy": 0.7226890756302521, "weighted_accuracy": 0.7310924369747899, "improvement": 0.008403361344537785, "correction_stats": {"improved": 4, "worsened": 2, "total_diff": 6}}, {"seed": 970, "fold": 3, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7563025210084033, "improvement": 0.025210084033613467, "correction_stats": {"improved": 9, "worsened": 3, "total_diff": 12}}, {"seed": 970, "fold": 4, "base_accuracy": 0.7172995780590717, "weighted_accuracy": 0.7172995780590717, "improvement": 0.0, "correction_stats": {"improved": 6, "worsened": 6, "total_diff": 12}}, {"seed": 970, "fold": 5, "base_accuracy": 0.7468354430379747, "weighted_accuracy": 0.7341772151898734, "improvement": -0.012658227848101222, "correction_stats": {"improved": 5, "worsened": 8, "total_diff": 13}}, {"seed": 1070, "fold": 1, "base_accuracy": 0.7647058823529411, "weighted_accuracy": 0.7521008403361344, "improvement": -0.012605042016806678, "correction_stats": {"improved": 5, "worsened": 8, "total_diff": 13}}, {"seed": 1070, "fold": 2, "base_accuracy": 0.7563025210084033, "weighted_accuracy": 0.7563025210084033, "improvement": 0.0, "correction_stats": {"improved": 6, "worsened": 6, "total_diff": 12}}, {"seed": 1070, "fold": 3, "base_accuracy": 0.7521008403361344, "weighted_accuracy": 0.7478991596638656, "improvement": -0.004201680672268893, "correction_stats": {"improved": 7, "worsened": 8, "total_diff": 15}}, {"seed": 1070, "fold": 4, "base_accuracy": 0.7046413502109705, "weighted_accuracy": 0.7172995780590717, "improvement": 0.012658227848101222, "correction_stats": {"improved": 13, "worsened": 10, "total_diff": 23}}, {"seed": 1070, "fold": 5, "base_accuracy": 0.7130801687763713, "weighted_accuracy": 0.7257383966244726, "improvement": 0.012658227848101333, "correction_stats": {"improved": 9, "worsened": 6, "total_diff": 15}}]}, "optuna_results": {"best_params": {"n_estimators": 800, "learning_rate": 0.044571967545593595, "max_depth": 9, "subsample": 0.8834910369037949, "colsample_bytree": 0.6843215614047402, "sh六院_pos_consistent": 2.6889217250338406, "sh六院_pos_inconsistent": 2.5014281787762944, "sh六院_neg_consistent": 2.5662031792445963, "sh六院_neg_inconsistent": 4.969660313164862, "sh十院_pos_consistent": 0.773611741176298, "sh十院_pos_inconsistent": 0.3373163887359733, "sh十院_neg_consistent": 0.2826850954782675, "sh十院_neg_inconsistent": 2.324199879483485, "bj301_pos_consistent": 3.8079112871469243, "bj301_pos_inconsistent": 3.173607688171226, "bj301_neg_consistent": 3.3947779552781823, "bj301_neg_inconsistent": 3.3210122216941778, "gd南海_pos_consistent": 1.1112179420481971, "gd南海_pos_inconsistent": 2.994894884157586, "gd南海_neg_consistent": 4.78172740014165, "gd南海_neg_inconsistent": 0.9641050004619246, "quality_good_weight": 0.9856213825214999, "quality_poor_weight": 0.9855745433946441, "conflict_alpha": 2.5557170548561094}, "best_value": 0.7631859494852794, "n_trials": 0, "conflict_alpha": 2.5557170548561094}, "xgb_params_optimized": true}, "v2.1.01": {"version": "v2.1.01", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-18 09:45:10", "description": "数据:0227版数据,训练数据原始特征:0303版特征(自动时刻点0.5)选择特征:训练PureBoruta,追加cur/ci特征 789模型:XGB,optuna后增强:无", "train_metrics": {"accuracy": 0.9570707070707071, "precision": 0.9595536959553695, "recall": 0.9690140845070423, "specificity": 0.9393305439330544, "f1": 0.9642606867554311, "auc": 0.987757086451765}, "feature_count": 792, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.04292929292929293, "cv_results": {"random_seeds": [1243, 1343, 1443], "best_seed": 870, "best_fold": 1, "best_score": 0.7857142857142857, "fold_metrics": [{"seed": 870, "fold": 1, "base_accuracy": 0.7815126050420168, "weighted_accuracy": 0.7857142857142857, "improvement": 0.004201680672268893, "correction_stats": {"improved": 7, "worsened": 6, "total_diff": 13}}, {"seed": 870, "fold": 2, "base_accuracy": 0.7478991596638656, "weighted_accuracy": 0.7478991596638656, "improvement": 0.0, "correction_stats": {"improved": 6, "worsened": 6, "total_diff": 12}}, {"seed": 870, "fold": 3, "base_accuracy": 0.7521008403361344, "weighted_accuracy": 0.726890756302521, "improvement": -0.025210084033613467, "correction_stats": {"improved": 5, "worsened": 11, "total_diff": 16}}, {"seed": 870, "fold": 4, "base_accuracy": 0.7721518987341772, "weighted_accuracy": 0.7468354430379747, "improvement": -0.025316455696202556, "correction_stats": {"improved": 4, "worsened": 10, "total_diff": 14}}, {"seed": 870, "fold": 5, "base_accuracy": 0.7215189873417721, "weighted_accuracy": 0.7130801687763713, "improvement": -0.008438818565400852, "correction_stats": {"improved": 6, "worsened": 8, "total_diff": 14}}, {"seed": 970, "fold": 1, "base_accuracy": 0.773109243697479, "weighted_accuracy": 0.7521008403361344, "improvement": -0.021008403361344574, "correction_stats": {"improved": 5, "worsened": 10, "total_diff": 15}}, {"seed": 970, "fold": 2, "base_accuracy": 0.7226890756302521, "weighted_accuracy": 0.7310924369747899, "improvement": 0.008403361344537785, "correction_stats": {"improved": 10, "worsened": 8, "total_diff": 18}}, {"seed": 970, "fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7521008403361344, "improvement": 0.008403361344537785, "correction_stats": {"improved": 9, "worsened": 7, "total_diff": 16}}, {"seed": 970, "fold": 4, "base_accuracy": 0.7341772151898734, "weighted_accuracy": 0.7383966244725738, "improvement": 0.00421940928270037, "correction_stats": {"improved": 3, "worsened": 2, "total_diff": 5}}, {"seed": 970, "fold": 5, "base_accuracy": 0.7679324894514767, "weighted_accuracy": 0.7468354430379747, "improvement": -0.021097046413502074, "correction_stats": {"improved": 6, "worsened": 11, "total_diff": 17}}, {"seed": 1070, "fold": 1, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7478991596638656, "improvement": 0.01680672268907568, "correction_stats": {"improved": 9, "worsened": 5, "total_diff": 14}}, {"seed": 1070, "fold": 2, "base_accuracy": 0.7563025210084033, "weighted_accuracy": 0.7815126050420168, "improvement": 0.025210084033613467, "correction_stats": {"improved": 13, "worsened": 7, "total_diff": 20}}, {"seed": 1070, "fold": 3, "base_accuracy": 0.726890756302521, "weighted_accuracy": 0.7352941176470589, "improvement": 0.008403361344537896, "correction_stats": {"improved": 8, "worsened": 6, "total_diff": 14}}, {"seed": 1070, "fold": 4, "base_accuracy": 0.7088607594936709, "weighted_accuracy": 0.7426160337552743, "improvement": 0.03375527426160341, "correction_stats": {"improved": 13, "worsened": 5, "total_diff": 18}}, {"seed": 1070, "fold": 5, "base_accuracy": 0.7383966244725738, "weighted_accuracy": 0.7341772151898734, "improvement": -0.00421940928270037, "correction_stats": {"improved": 6, "worsened": 7, "total_diff": 13}}]}, "optuna_results": {"best_params": {"n_estimators": 800, "learning_rate": 0.044571967545593595, "max_depth": 9, "subsample": 0.8834910369037949, "colsample_bytree": 0.6843215614047402, "sh六院_pos_consistent": 2.6889217250338406, "sh六院_pos_inconsistent": 2.5014281787762944, "sh六院_neg_consistent": 2.5662031792445963, "sh六院_neg_inconsistent": 4.969660313164862, "sh十院_pos_consistent": 0.773611741176298, "sh十院_pos_inconsistent": 0.3373163887359733, "sh十院_neg_consistent": 0.2826850954782675, "sh十院_neg_inconsistent": 2.324199879483485, "bj301_pos_consistent": 3.8079112871469243, "bj301_pos_inconsistent": 3.173607688171226, "bj301_neg_consistent": 3.3947779552781823, "bj301_neg_inconsistent": 3.3210122216941778, "gd南海_pos_consistent": 1.1112179420481971, "gd南海_pos_inconsistent": 2.994894884157586, "gd南海_neg_consistent": 4.78172740014165, "gd南海_neg_inconsistent": 0.9641050004619246, "quality_good_weight": 0.9856213825214999, "quality_poor_weight": 0.9855745433946441, "conflict_alpha": 2.5557170548561094}, "best_value": 0.7631859494852794, "n_trials": 0, "conflict_alpha": 2.5557170548561094}, "xgb_params_optimized": true}, "v2.1.02": {"version": "v2.1.02", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-18 10:34:55", "description": "数据:0227版数据,训练数据原始特征:0303版特征(自动时刻点0.5)选择特征:训测PureBoruta,追加cur/ci特征 789模型:XGB,optuna后增强:无", "train_metrics": {"accuracy": 0.9537037037037037, "precision": 0.9542302357836339, "recall": 0.9690140845070423, "specificity": 0.****************, "f1": 0.9615653389238296, "auc": 0.9875095762861689}, "feature_count": 789, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.046296296296296294, "cv_results": {"random_seeds": [1121, 1221, 1321], "best_seed": 870, "best_fold": 4, "best_score": 0.7679324894514767, "fold_metrics": [{"seed": 870, "fold": 1, "base_accuracy": 0.773109243697479, "weighted_accuracy": 0.7605042016806722, "improvement": -0.012605042016806789, "correction_stats": {"improved": 3, "worsened": 6, "total_diff": 9}}, {"seed": 870, "fold": 2, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7352941176470589, "improvement": -0.008403361344537785, "correction_stats": {"improved": 4, "worsened": 6, "total_diff": 10}}, {"seed": 870, "fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7352941176470589, "improvement": -0.008403361344537785, "correction_stats": {"improved": 6, "worsened": 8, "total_diff": 14}}, {"seed": 870, "fold": 4, "base_accuracy": 0.7721518987341772, "weighted_accuracy": 0.7679324894514767, "improvement": -0.0042194092827004814, "correction_stats": {"improved": 6, "worsened": 7, "total_diff": 13}}, {"seed": 870, "fold": 5, "base_accuracy": 0.7257383966244726, "weighted_accuracy": 0.7257383966244726, "improvement": 0.0, "correction_stats": {"improved": 7, "worsened": 7, "total_diff": 14}}, {"seed": 970, "fold": 1, "base_accuracy": 0.7605042016806722, "weighted_accuracy": 0.7478991596638656, "improvement": -0.012605042016806678, "correction_stats": {"improved": 8, "worsened": 11, "total_diff": 19}}, {"seed": 970, "fold": 2, "base_accuracy": 0.7394957983193278, "weighted_accuracy": 0.726890756302521, "improvement": -0.012605042016806789, "correction_stats": {"improved": 4, "worsened": 7, "total_diff": 11}}, {"seed": 970, "fold": 3, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7394957983193278, "improvement": 0.008403361344537896, "correction_stats": {"improved": 9, "worsened": 7, "total_diff": 16}}, {"seed": 970, "fold": 4, "base_accuracy": 0.7088607594936709, "weighted_accuracy": 0.7257383966244726, "improvement": 0.016877637130801704, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"seed": 970, "fold": 5, "base_accuracy": 0.7257383966244726, "weighted_accuracy": 0.7215189873417721, "improvement": -0.0042194092827004814, "correction_stats": {"improved": 9, "worsened": 10, "total_diff": 19}}, {"seed": 1070, "fold": 1, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7394957983193278, "improvement": 0.008403361344537896, "correction_stats": {"improved": 10, "worsened": 8, "total_diff": 18}}, {"seed": 1070, "fold": 2, "base_accuracy": 0.7605042016806722, "weighted_accuracy": 0.7352941176470589, "improvement": -0.025210084033613356, "correction_stats": {"improved": 5, "worsened": 11, "total_diff": 16}}, {"seed": 1070, "fold": 3, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7605042016806722, "improvement": 0.01680672268907557, "correction_stats": {"improved": 11, "worsened": 7, "total_diff": 18}}, {"seed": 1070, "fold": 4, "base_accuracy": 0.7341772151898734, "weighted_accuracy": 0.7130801687763713, "improvement": -0.021097046413502185, "correction_stats": {"improved": 4, "worsened": 9, "total_diff": 13}}, {"seed": 1070, "fold": 5, "base_accuracy": 0.7130801687763713, "weighted_accuracy": 0.7172995780590717, "improvement": 0.0042194092827004814, "correction_stats": {"improved": 8, "worsened": 7, "total_diff": 15}}]}, "optuna_results": {"best_params": {"n_estimators": 1000, "learning_rate": 0.010147028856594525, "max_depth": 8, "subsample": 0.9260806072304738, "colsample_bytree": 0.8046723200063755, "sh六院_pos_consistent": 1.3948899289546572, "sh六院_pos_inconsistent": 2.785058094279518, "sh六院_neg_consistent": 2.713316063998297, "sh六院_neg_inconsistent": 1.537621909457364, "sh十院_pos_consistent": 0.24961617837618605, "sh十院_pos_inconsistent": 0.7638586786942252, "sh十院_neg_consistent": 0.2715980752490603, "sh十院_neg_inconsistent": 0.39920030038217996, "bj301_pos_consistent": 1.4706184787445886, "bj301_pos_inconsistent": 2.215978805058312, "bj301_neg_consistent": 0.9193479455615955, "bj301_neg_inconsistent": 1.****************, "gd南海_pos_consistent": 1.****************, "gd南海_pos_inconsistent": 0.****************, "gd南海_neg_consistent": 2.**************, "gd南海_neg_inconsistent": 1.****************, "quality_good_weight": 0.****************, "quality_poor_weight": 1.***************, "conflict_alpha": 1.****************}, "best_value": 0.****************, "n_trials": 0, "conflict_alpha": 1.****************}, "xgb_params_optimized": true}, "20250319_1": {"version": "20250319_1", "model_type": "LatentRoutingMOE", "created_at": "2025-03-19 14:45:18", "description": "潜在路由MOE模型 - 训练时利用consistency，推理时不依赖", "feature_count": 305, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "train_metrics": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "hospital_count": 4, "expert_count": 8, "expert_info": [{"id": 0, "hospital": 1, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.****************, "sample_count": 84, "reverse_predictions": false}, {"id": 1, "hospital": 1, "consistency_target": 1, "model_type": "LogisticRegression", "performance": 0.****************, "sample_count": 148, "reverse_predictions": false}, {"id": 2, "hospital": 2, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.****************, "sample_count": 60, "reverse_predictions": false}, {"id": 3, "hospital": 2, "consistency_target": 1, "model_type": "SVM", "performance": 0.8799105607155142, "sample_count": 159, "reverse_predictions": false}, {"id": 4, "hospital": 3, "consistency_target": 0, "model_type": "SVM", "performance": 0.8272666932165139, "sample_count": 173, "reverse_predictions": false}, {"id": 5, "hospital": 3, "consistency_target": 1, "model_type": "MLP", "performance": 0.8308944886531093, "sample_count": 400, "reverse_predictions": false}, {"id": 6, "hospital": 5, "consistency_target": 0, "model_type": "LogisticRegression", "performance": 0.885989010989011, "sample_count": 62, "reverse_predictions": false}, {"id": 7, "hospital": 5, "consistency_target": 1, "model_type": "ExtraTrees", "performance": 0.966919191919192, "sample_count": 102, "reverse_predictions": false}], "consistency_reverse_prediction": false, "top_router_features": [{"feature": "clinic_hospital", "importance": 0.05593546230766604}, {"feature": "clinic_hospital_onehot_3", "importance": 0.05501876529827157}, {"feature": "clinic_hospital_onehot_1", "importance": 0.041006793832495045}, {"feature": "clinic_bmi", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.029023051311041638}, {"feature": "clinic_symp", "importance": 0.028350151616827968}, {"feature": "clinic_weight", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_2", "importance": 0.****************}]}, "20250319_2": {"version": "20250319_2", "model_type": "LatentRoutingMOE", "created_at": "2025-03-19 15:15:57", "description": "潜在路由MOE模型 - 训练时利用consistency，推理时不依赖", "feature_count": 305, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "train_metrics": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.*****************, "specificity": 0.****************, "f1": 0.*****************, "auc": 0.****************}, "cv_metrics": {"accuracy": {"mean": 0.****************, "std": 0.*****************}, "precision": {"mean": 0.****************, "std": 0.***************}, "recall": {"mean": 0.****************, "std": 0.018520214101919737}, "specificity": {"mean": 0.****************, "std": 0.029424493777466424}, "f1": {"mean": 0.****************, "std": 0.014941640017343851}, "auc": {"mean": 0.****************, "std": 0.021307345081485116}}, "hospital_count": 4, "expert_count": 8, "expert_info": [{"id": 0, "hospital": 1, "consistency_target": 0, "model_type": "XGBoost-Deep", "performance": 0.8010416666666668, "sample_count": 84, "reverse_predictions": false}, {"id": 1, "hospital": 1, "consistency_target": 1, "model_type": "CRFXGB_S1.5", "performance": 0.9438725490196079, "sample_count": 148, "reverse_predictions": false}, {"id": 2, "hospital": 2, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.7650793650793651, "sample_count": 60, "reverse_predictions": false}, {"id": 3, "hospital": 2, "consistency_target": 1, "model_type": "SVM", "performance": 0.8905303030303031, "sample_count": 159, "reverse_predictions": false}, {"id": 4, "hospital": 3, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.8447518919848642, "sample_count": 173, "reverse_predictions": false}, {"id": 5, "hospital": 3, "consistency_target": 1, "model_type": "RandomForest-Deep", "performance": 0.8820065660672871, "sample_count": 400, "reverse_predictions": false}, {"id": 6, "hospital": 5, "consistency_target": 0, "model_type": "MLP", "performance": 0.8239285714285713, "sample_count": 62, "reverse_predictions": false}, {"id": 7, "hospital": 5, "consistency_target": 1, "model_type": "ExtraTrees", "performance": 0.9523809523809523, "sample_count": 102, "reverse_predictions": false}], "consistency_reverse_prediction": false, "top_router_features": [{"feature": "clinic_hospital", "importance": 0.05593546230766604}, {"feature": "clinic_hospital_onehot_3", "importance": 0.05501876529827157}, {"feature": "clinic_hospital_onehot_1", "importance": 0.041006793832495045}, {"feature": "clinic_bmi", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.029023051311041638}, {"feature": "clinic_symp", "importance": 0.028350151616827968}, {"feature": "clinic_weight", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_2", "importance": 0.****************}]}, "20250319_3": {"version": "20250319_3", "model_type": "LatentRoutingMOE", "created_at": "2025-03-19 15:37:49", "description": "潜在路由MOE模型 - 训练时利用consistency，推理时不依赖", "feature_count": 305, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "train_metrics": {"accuracy": 0.***************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "cv_metrics": {"accuracy": {"mean": 0.****************, "std": 0.*****************}, "precision": {"mean": 0.****************, "std": 0.***************}, "recall": {"mean": 0.****************, "std": 0.018520214101919737}, "specificity": {"mean": 0.****************, "std": 0.029424493777466424}, "f1": {"mean": 0.****************, "std": 0.014941640017343851}, "auc": {"mean": 0.****************, "std": 0.021307345081485116}}, "hospital_count": 4, "expert_count": 8, "expert_info": [{"id": 0, "hospital": 1, "consistency_target": 0, "model_type": "XGBoost-Deep", "performance": 0.8010416666666668, "sample_count": 84, "reverse_predictions": false}, {"id": 1, "hospital": 1, "consistency_target": 1, "model_type": "CRFXGB_S1.5", "performance": 0.9438725490196079, "sample_count": 148, "reverse_predictions": false}, {"id": 2, "hospital": 2, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.7650793650793651, "sample_count": 60, "reverse_predictions": false}, {"id": 3, "hospital": 2, "consistency_target": 1, "model_type": "SVM", "performance": 0.8905303030303031, "sample_count": 159, "reverse_predictions": false}, {"id": 4, "hospital": 3, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.8447518919848642, "sample_count": 173, "reverse_predictions": false}, {"id": 5, "hospital": 3, "consistency_target": 1, "model_type": "RandomForest-Deep", "performance": 0.8820065660672871, "sample_count": 400, "reverse_predictions": false}, {"id": 6, "hospital": 5, "consistency_target": 0, "model_type": "MLP", "performance": 0.8239285714285713, "sample_count": 62, "reverse_predictions": false}, {"id": 7, "hospital": 5, "consistency_target": 1, "model_type": "ExtraTrees", "performance": 0.9523809523809523, "sample_count": 102, "reverse_predictions": false}], "consistency_reverse_prediction": false, "top_router_features": [{"feature": "clinic_hospital", "importance": 0.05593546230766604}, {"feature": "clinic_hospital_onehot_3", "importance": 0.05501876529827157}, {"feature": "clinic_hospital_onehot_1", "importance": 0.041006793832495045}, {"feature": "clinic_bmi", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.029023051311041638}, {"feature": "clinic_symp", "importance": 0.028350151616827968}, {"feature": "clinic_weight", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_2", "importance": 0.****************}]}, "20250319_4": {"version": "20250319_4", "model_type": "LatentRoutingMOE", "created_at": "2025-03-19 15:53:45", "description": "潜在路由MOE模型 - 训练时利用consistency，推理时不依赖", "feature_count": 305, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "train_metrics": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "cv_metrics": {"accuracy": {"mean": 0.****************, "std": 0.*****************}, "precision": {"mean": 0.****************, "std": 0.***************}, "recall": {"mean": 0.****************, "std": 0.018520214101919737}, "specificity": {"mean": 0.****************, "std": 0.029424493777466424}, "f1": {"mean": 0.****************, "std": 0.014941640017343851}, "auc": {"mean": 0.****************, "std": 0.021307345081485116}}, "hospital_count": 4, "expert_count": 8, "expert_info": [{"id": 0, "hospital": 1, "consistency_target": 0, "model_type": "XGBoost-Deep", "performance": 0.8010416666666668, "sample_count": 84, "reverse_predictions": false}, {"id": 1, "hospital": 1, "consistency_target": 1, "model_type": "CRFXGB_S1.5", "performance": 0.9438725490196079, "sample_count": 148, "reverse_predictions": false}, {"id": 2, "hospital": 2, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.7650793650793651, "sample_count": 60, "reverse_predictions": false}, {"id": 3, "hospital": 2, "consistency_target": 1, "model_type": "SVM", "performance": 0.8905303030303031, "sample_count": 159, "reverse_predictions": false}, {"id": 4, "hospital": 3, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.8447518919848642, "sample_count": 173, "reverse_predictions": false}, {"id": 5, "hospital": 3, "consistency_target": 1, "model_type": "RandomForest-Deep", "performance": 0.8820065660672871, "sample_count": 400, "reverse_predictions": false}, {"id": 6, "hospital": 5, "consistency_target": 0, "model_type": "MLP", "performance": 0.8239285714285713, "sample_count": 62, "reverse_predictions": false}, {"id": 7, "hospital": 5, "consistency_target": 1, "model_type": "ExtraTrees", "performance": 0.9523809523809523, "sample_count": 102, "reverse_predictions": false}], "consistency_reverse_prediction": false, "top_router_features": [{"feature": "clinic_hospital", "importance": 0.05593546230766604}, {"feature": "clinic_hospital_onehot_3", "importance": 0.05501876529827157}, {"feature": "clinic_hospital_onehot_1", "importance": 0.041006793832495045}, {"feature": "clinic_bmi", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.029023051311041638}, {"feature": "clinic_symp", "importance": 0.028350151616827968}, {"feature": "clinic_weight", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_2", "importance": 0.****************}]}, "20250319_5": {"version": "20250319_5", "model_type": "LatentRoutingMOE", "created_at": "2025-03-19 20:36:34", "description": "潜在路由MOE模型 - 训练时利用consistency，推理时不依赖", "feature_count": 305, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "train_metrics": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "cv_metrics": {"accuracy": {"mean": 0.****************, "std": 0.013866168540049848}, "precision": {"mean": 0.****************, "std": 0.016416095520745005}, "recall": {"mean": 0.****************, "std": 0.019248423600999525}, "specificity": {"mean": 0.****************, "std": 0.028617974155173184}, "f1": {"mean": 0.****************, "std": 0.011891419878678321}, "auc": {"mean": 0.****************, "std": 0.*****************}}, "hospital_count": 4, "expert_count": 8, "expert_info": [{"id": 0, "hospital": 1, "consistency_target": 0, "model_type": "RandomForest-Deep", "performance": 0.8590909090909091, "sample_count": 105, "reverse_predictions": false}, {"id": 1, "hospital": 1, "consistency_target": 1, "model_type": "SVM", "performance": 0.9157142857142857, "sample_count": 184, "reverse_predictions": false}, {"id": 2, "hospital": 2, "consistency_target": 0, "model_type": "RandomForest-Default", "performance": 0.7357142857142858, "sample_count": 75, "reverse_predictions": false}, {"id": 3, "hospital": 2, "consistency_target": 1, "model_type": "SVM", "performance": 0.8964190476190476, "sample_count": 199, "reverse_predictions": false}, {"id": 4, "hospital": 3, "consistency_target": 0, "model_type": "SVM", "performance": 0.8812215320910972, "sample_count": 216, "reverse_predictions": false}, {"id": 5, "hospital": 3, "consistency_target": 1, "model_type": "RandomForest-Deep", "performance": 0.****************, "sample_count": 501, "reverse_predictions": false}, {"id": 6, "hospital": 5, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.****************, "sample_count": 78, "reverse_predictions": false}, {"id": 7, "hospital": 5, "consistency_target": 1, "model_type": "XGBoost-Deep", "performance": 0.****************, "sample_count": 127, "reverse_predictions": false}], "consistency_reverse_prediction": false, "top_router_features": [{"feature": "clinic_hospital", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_3", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_1", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.035209855696190596}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_weight", "importance": 0.*****************}, {"feature": "clinic_bmi", "importance": 0.030226967812122058}, {"feature": "clinic_hospital_onehot_2", "importance": 0.030153032702239714}, {"feature": "clinic_symp", "importance": 0.027818945492758082}]}, "20250320_1": {"version": "20250320_1", "model_type": "LatentRoutingMOE", "created_at": "2025-03-20 09:26:07", "description": "潜在路由MOE模型 - 每家医院一个专家", "feature_count": 305, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "train_metrics": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "cv_metrics": {"accuracy": {"mean": 0.***************, "std": 0.025783420497072933}, "precision": {"mean": 0.****************, "std": 0.020960265086732852}, "recall": {"mean": 0.****************, "std": 0.027400952665235324}, "specificity": {"mean": 0.****************, "std": 0.*****************}, "f1": {"mean": 0.****************, "std": 0.019742880984796055}, "auc": {"mean": 0.****************, "std": 0.017143573885209797}}, "hospital_count": 4, "expert_count": 4, "expert_info": [{"id": 0, "hospital": 1, "model_type": "CRFXGB_S1.5", "performance": 0.****************, "sample_count": 289, "consistency_0_count": 105, "consistency_1_count": 184, "reverse_predictions": false}, {"id": 1, "hospital": 2, "model_type": "SVM", "performance": 0.7598743112947659, "sample_count": 274, "consistency_0_count": 75, "consistency_1_count": 199, "reverse_predictions": false}, {"id": 2, "hospital": 3, "model_type": "XGBoost-Deep", "performance": 0.7920378113637042, "sample_count": 717, "consistency_0_count": 216, "consistency_1_count": 501, "reverse_predictions": false}, {"id": 3, "hospital": 5, "model_type": "ExtraTrees", "performance": 0.743393009377664, "sample_count": 205, "consistency_0_count": 78, "consistency_1_count": 127, "reverse_predictions": false}], "top_router_features": [{"feature": "clinic_hospital_onehot_3", "importance": 0.11785357814084733}, {"feature": "clinic_hospital", "importance": 0.10349679540629582}, {"feature": "clinic_hospital_onehot_1", "importance": 0.07610291370647607}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.*****************}, {"feature": "clinic_bmi", "importance": 0.053168508341536816}, {"feature": "clinic_hospital_onehot_2", "importance": 0.*****************}, {"feature": "clinic_symp", "importance": 0.*****************}, {"feature": "clinic_weight", "importance": 0.*****************}]}, "20250320_2": {"version": "20250320_2", "model_type": "LatentRoutingMOE", "expert_mode": "hospital_consistency", "created_at": "2025-03-20 10:31:11", "description": "潜在路由MOE模型 - 每家医院每种一致性一个专家", "feature_count": 305, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "train_metrics": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "cv_metrics": {"accuracy": {"mean": 0.****************, "std": 0.013866168540049848}, "precision": {"mean": 0.****************, "std": 0.016416095520745005}, "recall": {"mean": 0.****************, "std": 0.019248423600999525}, "specificity": {"mean": 0.****************, "std": 0.028617974155173184}, "f1": {"mean": 0.****************, "std": 0.011891419878678321}, "auc": {"mean": 0.****************, "std": 0.*****************}}, "hospital_count": 4, "expert_count": 8, "expert_info": [{"id": 0, "hospital": 1, "consistency_target": 0, "model_type": "RandomForest-Deep", "performance": 0.8590909090909091, "sample_count": 105, "reverse_predictions": false}, {"id": 1, "hospital": 1, "consistency_target": 1, "model_type": "SVM", "performance": 0.9157142857142857, "sample_count": 184, "reverse_predictions": false}, {"id": 2, "hospital": 2, "consistency_target": 0, "model_type": "RandomForest-Default", "performance": 0.7357142857142858, "sample_count": 75, "reverse_predictions": false}, {"id": 3, "hospital": 2, "consistency_target": 1, "model_type": "SVM", "performance": 0.8964190476190476, "sample_count": 199, "reverse_predictions": false}, {"id": 4, "hospital": 3, "consistency_target": 0, "model_type": "SVM", "performance": 0.8812215320910972, "sample_count": 216, "reverse_predictions": false}, {"id": 5, "hospital": 3, "consistency_target": 1, "model_type": "RandomForest-Deep", "performance": 0.****************, "sample_count": 501, "reverse_predictions": false}, {"id": 6, "hospital": 5, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.****************, "sample_count": 78, "reverse_predictions": false}, {"id": 7, "hospital": 5, "consistency_target": 1, "model_type": "XGBoost-Deep", "performance": 0.****************, "sample_count": 127, "reverse_predictions": false}], "router_model": "RandomForest", "router_performance": {"accuracy": 0.****************, "vs_direct_routing": -0.002693602693602748}, "comparative_performance": {"feature_routing_moe": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "direct_routing_moe": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.***************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "global_model": {"accuracy": 0.****************, "precision": 0.***************, "recall": 0.875, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}}, "top_router_features": [{"feature": "clinic_hospital", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_3", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_1", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.035209855696190596}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_weight", "importance": 0.*****************}, {"feature": "clinic_bmi", "importance": 0.030226967812122058}, {"feature": "clinic_hospital_onehot_2", "importance": 0.030153032702239714}, {"feature": "clinic_symp", "importance": 0.027818945492758082}]}, "20250320_3": {"version": "20250320_3", "model_type": "LatentRoutingMOE", "expert_mode": "hospital_consistency", "created_at": "2025-03-20 10:56:30", "description": "潜在路由MOE模型 - 每家医院每种一致性一个专家", "feature_count": 789, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "train_metrics": {"accuracy": 0.****************, "precision": 0.**************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.**************}, "cv_metrics": {"accuracy": {"mean": 0.****************, "std": 0.****************}, "precision": {"mean": 0.***************, "std": 0.*****************}, "recall": {"mean": 0.****************, "std": 0.*****************}, "specificity": {"mean": 0.****************, "std": 0.043281289607351996}, "f1": {"mean": 0.****************, "std": 0.013446654540391704}, "auc": {"mean": 0.****************, "std": 0.*****************}}, "hospital_count": 4, "expert_count": 8, "expert_info": [{"id": 0, "hospital": 1, "consistency_target": 0, "model_type": "XGBoost-Deep", "performance": 0.8872727272727271, "sample_count": 105, "reverse_predictions": false}, {"id": 1, "hospital": 1, "consistency_target": 1, "model_type": "ExtraTrees", "performance": 0.9174107142857142, "sample_count": 184, "reverse_predictions": false}, {"id": 2, "hospital": 2, "consistency_target": 0, "model_type": "ExtraTrees", "performance": 0.9107142857142858, "sample_count": 75, "reverse_predictions": false}, {"id": 3, "hospital": 2, "consistency_target": 1, "model_type": "RandomForest-Deep", "performance": 0.8993142857142857, "sample_count": 199, "reverse_predictions": false}, {"id": 4, "hospital": 3, "consistency_target": 0, "model_type": "RandomForest-Deep", "performance": 0.9426708074534161, "sample_count": 216, "reverse_predictions": false}, {"id": 5, "hospital": 3, "consistency_target": 1, "model_type": "ExtraTrees", "performance": 0.****************, "sample_count": 501, "reverse_predictions": false}, {"id": 6, "hospital": 5, "consistency_target": 0, "model_type": "SVM", "performance": 0.****************, "sample_count": 78, "reverse_predictions": false}, {"id": 7, "hospital": 5, "consistency_target": 1, "model_type": "MLP", "performance": 0.****************, "sample_count": 127, "reverse_predictions": false}], "router_model": "RandomForest", "router_performance": {"accuracy": 0.****************, "vs_direct_routing": -0.0006734006734007147}, "comparative_performance": {"feature_routing_moe": {"accuracy": 0.****************, "precision": 0.**************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.**************}, "direct_routing_moe": {"accuracy": 0.****************, "precision": 0.****************, "recall": 0.****************, "specificity": 0.****************, "f1": 0.****************, "auc": 0.****************}, "global_model": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}}, "top_router_features": [{"feature": "clinic_hospital_onehot_3", "importance": 0.*****************}, {"feature": "clinic_hospital", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.032116968168028386}, {"feature": "clinic_hospital_onehot_1", "importance": 0.031718959207466556}, {"feature": "clinic_weight", "importance": 0.031064571918956556}, {"feature": "clinic_hospital_onehot_2", "importance": 0.*****************}, {"feature": "clinic_drinking", "importance": 0.026939722406823505}, {"feature": "clinic_bmi", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_5", "importance": 0.024192683951326174}, {"feature": "clinic_symp", "importance": 0.018643845870511697}]}, "20250320_4": {"version": "20250320_4", "model_type": "LatentRoutingMOE", "expert_mode": "hospital_only", "created_at": "2025-03-20 11:42:51", "description": "潜在路由MOE模型 - 每家医院一个专家", "feature_count": 789, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "train_metrics": {"accuracy": 0.****************, "precision": 1.0, "recall": 0.****************, "specificity": 1.0, "f1": 0.****************, "auc": 0.****************}, "cv_metrics": {"accuracy": {"mean": 0.***************, "std": 0.018637511788684242}, "precision": {"mean": 0.****************, "std": 0.018700721642904223}, "recall": {"mean": 0.****************, "std": 0.028075282772368575}, "specificity": {"mean": 0.****************, "std": 0.*****************}, "f1": {"mean": 0.****************, "std": 0.014635004014200982}, "auc": {"mean": 0.****************, "std": 0.014972495418997558}}, "hospital_count": 4, "expert_count": 4, "expert_info": [{"id": 0, "hospital": 1, "model_type": "ExtraTrees", "performance": 0.****************, "sample_count": 289, "consistency_0_count": 105, "consistency_1_count": 184, "reverse_predictions": false}, {"id": 1, "hospital": 2, "model_type": "SVM", "performance": 0.7141442837465565, "sample_count": 274, "consistency_0_count": 75, "consistency_1_count": 199, "reverse_predictions": false}, {"id": 2, "hospital": 3, "model_type": "XGBoost-Deep", "performance": 0.****************, "sample_count": 717, "consistency_0_count": 216, "consistency_1_count": 501, "reverse_predictions": false}, {"id": 3, "hospital": 5, "model_type": "ExtraTrees", "performance": 0.****************, "sample_count": 205, "consistency_0_count": 78, "consistency_1_count": 127, "reverse_predictions": false}], "router_model": "RandomForest", "router_performance": {"accuracy": 1.0, "vs_direct_routing": 0.0}, "comparative_performance": {"feature_routing_moe": {"accuracy": 0.****************, "precision": 1.0, "recall": 0.****************, "specificity": 1.0, "f1": 0.****************, "auc": 0.****************}, "direct_routing_moe": {"accuracy": 0.****************, "precision": 1.0, "recall": 0.****************, "specificity": 1.0, "f1": 0.****************, "auc": 0.****************}, "global_model": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}}, "top_router_features": [{"feature": "clinic_hospital_onehot_3", "importance": 0.*****************}, {"feature": "clinic_hospital", "importance": 0.*****************}, {"feature": "clinic_hospital_onehot_1", "importance": 0.*****************}, {"feature": "clinic_height", "importance": 0.*****************}, {"feature": "clinic_bmi", "importance": 0.053331240712798905}, {"feature": "clinic_hospital_onehot_2", "importance": 0.049317320183003575}, {"feature": "clinic_hospital_onehot_5", "importance": 0.*****************}, {"feature": "clinic_weight", "importance": 0.046159506119280186}, {"feature": "clinic_drinking", "importance": 0.*****************}, {"feature": "clinic_symp", "importance": 0.034264027025372894}]}, "v2.1.2": {"version": "v2.1.2", "model_type": "MetaLearningEnsembleModel", "created_at": "2025-03-22 06:49:54", "description": "数据:0227版数据,训测数据原始特征:0303版特征(自动时刻点0.5)选择特征:训测PureBoruta,追加cur/ci特征 模型:XGB,meta增强:无", "train_metrics": {"accuracy": 0.9511784511784511, "precision": 0.9490358126721763, "recall": 0.9704225352112676, "specificity": 0.9225941422594143, "f1": 0.9596100278551533, "auc": 0.9867199009959338}, "feature_count": 790, "sample_count": 1188, "class_distribution": {"1": 710, "0": 478}, "error_rate": 0.04882154882154882, "cv_results": {"random_seeds": [42, 142, 242], "best_seed": 242, "best_fold": 2, "best_score": 0.7563025210084033, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.7226890756302521, "weighted_accuracy": 0.7352941176470589, "improvement": 0.012605042016806789, "correction_stats": {"improved": 10, "worsened": 7, "total_diff": 17}}, {"seed": 42, "fold": 2, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7352941176470589, "improvement": 0.004201680672269004, "correction_stats": {"improved": 2, "worsened": 1, "total_diff": 3}}, {"seed": 42, "fold": 3, "base_accuracy": 0.7478991596638656, "weighted_accuracy": 0.7226890756302521, "improvement": -0.025210084033613467, "correction_stats": {"improved": 4, "worsened": 10, "total_diff": 14}}, {"seed": 42, "fold": 4, "base_accuracy": 0.7215189873417721, "weighted_accuracy": 0.729957805907173, "improvement": 0.008438818565400852, "correction_stats": {"improved": 5, "worsened": 3, "total_diff": 8}}, {"seed": 42, "fold": 5, "base_accuracy": 0.7215189873417721, "weighted_accuracy": 0.7088607594936709, "improvement": -0.012658227848101222, "correction_stats": {"improved": 2, "worsened": 5, "total_diff": 7}}, {"seed": 142, "fold": 1, "base_accuracy": 0.7436974789915967, "weighted_accuracy": 0.7352941176470589, "improvement": -0.008403361344537785, "correction_stats": {"improved": 5, "worsened": 7, "total_diff": 12}}, {"seed": 142, "fold": 2, "base_accuracy": 0.7226890756302521, "weighted_accuracy": 0.7394957983193278, "improvement": 0.01680672268907568, "correction_stats": {"improved": 9, "worsened": 5, "total_diff": 14}}, {"seed": 142, "fold": 3, "base_accuracy": 0.7478991596638656, "weighted_accuracy": 0.7394957983193278, "improvement": -0.008403361344537785, "correction_stats": {"improved": 1, "worsened": 3, "total_diff": 4}}, {"seed": 142, "fold": 4, "base_accuracy": 0.7257383966244726, "weighted_accuracy": 0.7172995780590717, "improvement": -0.008438818565400852, "correction_stats": {"improved": 6, "worsened": 8, "total_diff": 14}}, {"seed": 142, "fold": 5, "base_accuracy": 0.7341772151898734, "weighted_accuracy": 0.7257383966244726, "improvement": -0.008438818565400852, "correction_stats": {"improved": 4, "worsened": 6, "total_diff": 10}}, {"seed": 242, "fold": 1, "base_accuracy": 0.7310924369747899, "weighted_accuracy": 0.7184873949579832, "improvement": -0.012605042016806678, "correction_stats": {"improved": 3, "worsened": 6, "total_diff": 9}}, {"seed": 242, "fold": 2, "base_accuracy": 0.7478991596638656, "weighted_accuracy": 0.7563025210084033, "improvement": 0.008403361344537785, "correction_stats": {"improved": 6, "worsened": 4, "total_diff": 10}}, {"seed": 242, "fold": 3, "base_accuracy": 0.7647058823529411, "weighted_accuracy": 0.7478991596638656, "improvement": -0.01680672268907557, "correction_stats": {"improved": 2, "worsened": 6, "total_diff": 8}}, {"seed": 242, "fold": 4, "base_accuracy": 0.70042194092827, "weighted_accuracy": 0.7130801687763713, "improvement": 0.012658227848101222, "correction_stats": {"improved": 8, "worsened": 5, "total_diff": 13}}, {"seed": 242, "fold": 5, "base_accuracy": 0.7130801687763713, "weighted_accuracy": 0.729957805907173, "improvement": 0.016877637130801704, "correction_stats": {"improved": 8, "worsened": 4, "total_diff": 12}}]}, "meta_learning_results": {"best_params": {"sh六院_pos_consistent": 2.637197703248855, "sh六院_pos_inconsistent": 3.1661865964438474, "sh六院_neg_consistent": 2.946594559153735, "sh六院_neg_inconsistent": 2.237872240450139, "sh十院_pos_consistent": 2.6410810697810536, "sh十院_pos_inconsistent": 2.2559516819880225, "sh十院_neg_consistent": 2.5444569652444384, "sh十院_neg_inconsistent": 2.853232772339005, "bj301_pos_consistent": 2.6667764499404214, "bj301_pos_inconsistent": 2.6287491283578803, "bj301_neg_consistent": 2.5399929581510943, "bj301_neg_inconsistent": 2.510708301457844, "gd南海_pos_consistent": 2.7359948172743462, "gd南海_pos_inconsistent": 2.2941130901351343, "gd南海_neg_consistent": 2.7974573959065094, "gd南海_neg_inconsistent": 2.2854110048539624, "quality_good_weight": 1.7787734626121048, "quality_poor_weight": 1.9860999180251637, "conflict_alpha": 4.212621956059752, "extremely_inconsistent_weight": 2.8016454914612794}, "best_value": 0.7732493392309143, "n_iterations": 1000, "meta_learning_rate": 1, "conflict_alpha": 4.212621956059752, "subtask_count": 5000, "training_time": 49689.603407621384}, "xgb_params_optimized": false}, "v2.1.1": {"version": "v2.1.1", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-24 21:23:18", "description": "数据:0227版数据,训测数据原始特征:0303版特征(自动时刻点0.5)选择特征:训测PureBoruta,追加cur/ci特征 模型:XGB,optuna增强:无", "train_metrics": {"accuracy": 0.9528619528619529, "precision": 0.9679633867276888, "recall": 0.9527027027027027, "specificity": 0.9530988274706867, "f1": 0.960272417707151, "auc": 0.9876880649493716}, "feature_count": 790, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "error_rate": 0.04713804713804714, "cv_results": {"random_seeds": [145, 245, 345], "best_seed": 142, "best_fold": 5, "best_score": 0.7744107744107744, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.7306397306397306, "weighted_accuracy": 0.7441077441077442, "improvement": 0.013468013468013518, "correction_stats": {"improved": 12, "worsened": 8, "total_diff": 20}}, {"seed": 42, "fold": 2, "base_accuracy": 0.734006734006734, "weighted_accuracy": 0.7407407407407407, "improvement": 0.006734006734006703, "correction_stats": {"improved": 10, "worsened": 8, "total_diff": 18}}, {"seed": 42, "fold": 3, "base_accuracy": 0.734006734006734, "weighted_accuracy": 0.7474747474747475, "improvement": 0.013468013468013518, "correction_stats": {"improved": 11, "worsened": 7, "total_diff": 18}}, {"seed": 42, "fold": 4, "base_accuracy": 0.7003367003367004, "weighted_accuracy": 0.7070707070707071, "improvement": 0.006734006734006703, "correction_stats": {"improved": 6, "worsened": 4, "total_diff": 10}}, {"seed": 42, "fold": 5, "base_accuracy": 0.7777777777777778, "weighted_accuracy": 0.7643097643097643, "improvement": -0.013468013468013518, "correction_stats": {"improved": 11, "worsened": 15, "total_diff": 26}}, {"seed": 142, "fold": 1, "base_accuracy": 0.7609427609427609, "weighted_accuracy": 0.7508417508417509, "improvement": -0.010101010101010055, "correction_stats": {"improved": 9, "worsened": 12, "total_diff": 21}}, {"seed": 142, "fold": 2, "base_accuracy": 0.7575757575757576, "weighted_accuracy": 0.7542087542087542, "improvement": -0.0033670033670033517, "correction_stats": {"improved": 9, "worsened": 10, "total_diff": 19}}, {"seed": 142, "fold": 3, "base_accuracy": 0.7373737373737373, "weighted_accuracy": 0.7441077441077442, "improvement": 0.006734006734006814, "correction_stats": {"improved": 11, "worsened": 9, "total_diff": 20}}, {"seed": 142, "fold": 4, "base_accuracy": 0.7070707070707071, "weighted_accuracy": 0.7239057239057239, "improvement": 0.01683501683501687, "correction_stats": {"improved": 11, "worsened": 6, "total_diff": 17}}, {"seed": 142, "fold": 5, "base_accuracy": 0.7508417508417509, "weighted_accuracy": 0.7744107744107744, "improvement": 0.023569023569023573, "correction_stats": {"improved": 14, "worsened": 7, "total_diff": 21}}, {"seed": 242, "fold": 1, "base_accuracy": 0.734006734006734, "weighted_accuracy": 0.734006734006734, "improvement": 0.0, "correction_stats": {"improved": 8, "worsened": 8, "total_diff": 16}}, {"seed": 242, "fold": 2, "base_accuracy": 0.7609427609427609, "weighted_accuracy": 0.7744107744107744, "improvement": 0.013468013468013518, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"seed": 242, "fold": 3, "base_accuracy": 0.7239057239057239, "weighted_accuracy": 0.7575757575757576, "improvement": 0.03367003367003363, "correction_stats": {"improved": 16, "worsened": 6, "total_diff": 22}}, {"seed": 242, "fold": 4, "base_accuracy": 0.7441077441077442, "weighted_accuracy": 0.7373737373737373, "improvement": -0.006734006734006814, "correction_stats": {"improved": 6, "worsened": 8, "total_diff": 14}}, {"seed": 242, "fold": 5, "base_accuracy": 0.7407407407407407, "weighted_accuracy": 0.7306397306397306, "improvement": -0.010101010101010055, "correction_stats": {"improved": 7, "worsened": 10, "total_diff": 17}}]}, "optuna_results": {"best_params": {"sh六院_pos_consistent": 1.539358960284019, "sh六院_pos_inconsistent": 4.941251280315054, "sh六院_neg_consistent": 4.152570697871693, "sh六院_neg_inconsistent": 4.787860023001081, "sh十院_pos_consistent": 0.693506617843247, "sh十院_pos_inconsistent": 1.1621927441138589, "sh十院_neg_consistent": 1.835542433693015, "sh十院_neg_inconsistent": 3.137032353761269, "bj301_pos_consistent": 1.826654045333343, "bj301_pos_inconsistent": 3.2961183401158074, "bj301_neg_consistent": 3.7551245925468635, "bj301_neg_inconsistent": 1.4005815149156298, "gd南海_pos_consistent": 2.107411343064542, "gd南海_pos_inconsistent": 3.791375423021977, "gd南海_neg_consistent": 1.8930312140985812, "gd南海_neg_inconsistent": 3.9850663929472785, "quality_good_weight": 1.7674279842300658, "quality_poor_weight": 1.3275358316225467, "conflict_alpha": 0.8619732410987747, "extremely_inconsistent_weight": 1.9670393609967027}, "best_value": 0.745679012345679, "n_trials": 0, "conflict_alpha": 0.8619732410987747}, "xgb_params_optimized": false}, "v2.1.11": {"version": "v2.1.11", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-25 20:21:27", "description": "数据:0227版数据,训测数据原始特征:0303版特征(自动时刻点0.5)选择特征:训测PureBoruta,追加cur/ci特征 模型:XGB,optuna增强:无", "train_metrics": {"accuracy": 0.9569023569023569, "precision": 0.9517543859649122, "recall": 0.9774774774774775, "specificity": 0.9262981574539364, "f1": 0.9644444444444443, "auc": 0.9867317065809529}, "feature_count": 790, "sample_count": 1485, "class_distribution": {"1": 888, "0": 597}, "error_rate": 0.0430976430976431, "cv_results": {"random_seeds": [240, 340, 440], "best_seed": 242, "best_fold": 2, "best_score": 0.7845117845117845, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.7306397306397306, "weighted_accuracy": 0.734006734006734, "improvement": 0.0033670033670033517, "correction_stats": {"improved": 8, "worsened": 7, "total_diff": 15}}, {"seed": 42, "fold": 2, "base_accuracy": 0.734006734006734, "weighted_accuracy": 0.7508417508417509, "improvement": 0.01683501683501687, "correction_stats": {"improved": 12, "worsened": 7, "total_diff": 19}}, {"seed": 42, "fold": 3, "base_accuracy": 0.734006734006734, "weighted_accuracy": 0.7407407407407407, "improvement": 0.006734006734006703, "correction_stats": {"improved": 8, "worsened": 6, "total_diff": 14}}, {"seed": 42, "fold": 4, "base_accuracy": 0.7003367003367004, "weighted_accuracy": 0.696969696969697, "improvement": -0.0033670033670033517, "correction_stats": {"improved": 5, "worsened": 6, "total_diff": 11}}, {"seed": 42, "fold": 5, "base_accuracy": 0.7777777777777778, "weighted_accuracy": 0.7676767676767676, "improvement": -0.010101010101010166, "correction_stats": {"improved": 8, "worsened": 11, "total_diff": 19}}, {"seed": 142, "fold": 1, "base_accuracy": 0.7609427609427609, "weighted_accuracy": 0.7609427609427609, "improvement": 0.0, "correction_stats": {"improved": 9, "worsened": 9, "total_diff": 18}}, {"seed": 142, "fold": 2, "base_accuracy": 0.7575757575757576, "weighted_accuracy": 0.7542087542087542, "improvement": -0.0033670033670033517, "correction_stats": {"improved": 8, "worsened": 9, "total_diff": 17}}, {"seed": 142, "fold": 3, "base_accuracy": 0.7373737373737373, "weighted_accuracy": 0.7542087542087542, "improvement": 0.01683501683501687, "correction_stats": {"improved": 9, "worsened": 4, "total_diff": 13}}, {"seed": 142, "fold": 4, "base_accuracy": 0.7070707070707071, "weighted_accuracy": 0.7239057239057239, "improvement": 0.01683501683501687, "correction_stats": {"improved": 13, "worsened": 8, "total_diff": 21}}, {"seed": 142, "fold": 5, "base_accuracy": 0.7508417508417509, "weighted_accuracy": 0.7542087542087542, "improvement": 0.0033670033670033517, "correction_stats": {"improved": 10, "worsened": 9, "total_diff": 19}}, {"seed": 242, "fold": 1, "base_accuracy": 0.734006734006734, "weighted_accuracy": 0.7474747474747475, "improvement": 0.013468013468013518, "correction_stats": {"improved": 11, "worsened": 7, "total_diff": 18}}, {"seed": 242, "fold": 2, "base_accuracy": 0.7609427609427609, "weighted_accuracy": 0.7845117845117845, "improvement": 0.023569023569023573, "correction_stats": {"improved": 10, "worsened": 3, "total_diff": 13}}, {"seed": 242, "fold": 3, "base_accuracy": 0.7239057239057239, "weighted_accuracy": 0.7542087542087542, "improvement": 0.030303030303030276, "correction_stats": {"improved": 13, "worsened": 4, "total_diff": 17}}, {"seed": 242, "fold": 4, "base_accuracy": 0.7441077441077442, "weighted_accuracy": 0.7441077441077442, "improvement": 0.0, "correction_stats": {"improved": 7, "worsened": 7, "total_diff": 14}}, {"seed": 242, "fold": 5, "base_accuracy": 0.7407407407407407, "weighted_accuracy": 0.7441077441077442, "improvement": 0.0033670033670034627, "correction_stats": {"improved": 8, "worsened": 7, "total_diff": 15}}]}, "optuna_results": {"best_params": {"sh六院_pos_consistent": 2.4080413670208456, "sh六院_pos_inconsistent": 3.4912983062781984, "sh六院_neg_consistent": 4.472422174517153, "sh六院_neg_inconsistent": 3.4683351303803267, "sh十院_pos_consistent": 1.1838230263927152, "sh十院_pos_inconsistent": 2.454949498240786, "sh十院_neg_consistent": 1.917647447182797, "sh十院_neg_inconsistent": 4.805654098069839, "bj301_pos_consistent": 2.6330695265380006, "bj301_pos_inconsistent": 4.739022049346803, "bj301_neg_consistent": 3.836919656754561, "bj301_neg_inconsistent": 4.671620495141962, "gd南海_pos_consistent": 3.2327077477950557, "gd南海_pos_inconsistent": 3.640773216029312, "gd南海_neg_consistent": 3.6941617699572893, "gd南海_neg_inconsistent": 3.959319824853937, "quality_good_weight": 1.9255992272616782, "quality_poor_weight": 1.1550978119230062, "conflict_alpha": 0.5887205213184451, "extremely_inconsistent_weight": 1.9293197945577787}, "best_value": 0.7474747474747475, "n_trials": 200, "conflict_alpha": 0.5887205213184451}, "xgb_params_optimized": false}, "肥厚v0.1": {"version": "肥厚v0.1", "model_type": "SVC", "created_at": "2025-03-26 16:01:27", "description": "DeepBoruta150基线版本", "train_metrics": {"accuracy": 0.9643916913946587, "precision": 0.9775280898876404, "recall": 0.8969072164948454, "specificity": 0.9916666666666667, "f1": 0.935483870967742, "auc": 0.9971219931271478}, "feature_count": 150, "sample_count": 337, "class_distribution": {"0": 240, "1": 97}, "error_rate": 0.03560830860534125, "optimization": {"optimized": false, "model_params": {"C": 1.0, "kernel": "rbf", "gamma": "scale"}, "aug_params": {"use_augmentation": false}}}, "肥厚v0.2": {"version": "肥厚v0.2", "model_type": "XGBClassifier", "created_at": "2025-03-26 16:23:46", "description": "DeepBoruta150基线版本xgb模型", "train_metrics": {"accuracy": 0.9910979228486647, "precision": 0.9795918367346939, "recall": 0.9896907216494846, "specificity": 0.9916666666666667, "f1": 0.9846153846153847, "auc": 0.9990120274914089}, "feature_count": 150, "sample_count": 337, "class_distribution": {"0": 240, "1": 97}, "error_rate": 0.008902077151335312, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "肥厚v0.3": {"version": "肥厚v0.3", "model_type": "XGBClassifier", "created_at": "2025-03-26 16:52:23", "description": "DeepBoruta150基线版本xgb模型optuna", "train_metrics": {"accuracy": 0.9910979228486647, "precision": 0.9795918367346939, "recall": 0.9896907216494846, "specificity": 0.9916666666666667, "f1": 0.9846153846153847, "auc": 0.9984106529209622}, "feature_count": 150, "sample_count": 337, "class_distribution": {"0": 240, "1": 97}, "error_rate": 0.008902077151335312, "optimization": {"optimized": true, "best_score": 0.931479160167271, "model_params": {"learning_rate": 0.09994310374703232, "n_estimators": 307, "max_depth": 4, "subsample": 0.6750282901946115, "colsample_bytree": 0.8568991273814798, "min_child_weight": 2, "gamma": 0.3975696978177813}, "aug_params": {"use_augmentation": true, "balance_method": "<PERSON><PERSON><PERSON>", "noise_method": "gaussian_noise", "noise_ratio": 0.1871831219510517, "balance_by_center": true, "center_target_ratio": [0.638948937968668, 0.8145645984125401, 0.9015947067725983, 0.5467893967979074]}}}, "肥厚v0.4": {"version": "肥厚v0.4", "model_type": "XGBClassifier", "created_at": "2025-03-26 17:12:12", "description": "DeepBoruta50基线版本xgb模型optuna", "train_metrics": {"accuracy": 0.9881305637982196, "precision": 0.9894736842105263, "recall": 0.9690721649484536, "specificity": 0.9958333333333333, "f1": 0.9791666666666666, "auc": 0.9971649484536083}, "feature_count": 50, "sample_count": 337, "class_distribution": {"0": 240, "1": 97}, "error_rate": 0.011869436201780416, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.2.0": {"version": "v2.2.0", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-03-27 14:08:25", "description": "数据:0227版数据,训练+补充数据原始特征:0303版特征(自动时刻点0.5)选择特征:训测PureBoruta,追加cur/ci特征(原训练数据) 模型:XGB,optuna增强:无", "train_metrics": {"accuracy": 0.9523578026251823, "precision": 0.9559294871794872, "recall": 0.9652103559870551, "specificity": 0.9330085261875761, "f1": 0.9605475040257649, "auc": 0.9849550039615435}, "feature_count": 791, "sample_count": 2057, "class_distribution": {"1": 1236, "0": 821}, "error_rate": 0.047642197374817695, "cv_results": {"random_seeds": [240, 340, 440], "best_seed": 42, "best_fold": 5, "best_score": 0.7956204379562044, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.7669902912621359, "weighted_accuracy": 0.7815533980582524, "improvement": 0.014563106796116498, "correction_stats": {"improved": 17, "worsened": 11, "total_diff": 28}}, {"seed": 42, "fold": 2, "base_accuracy": 0.779126213592233, "weighted_accuracy": 0.7766990291262136, "improvement": -0.0024271844660194164, "correction_stats": {"improved": 14, "worsened": 15, "total_diff": 29}}, {"seed": 42, "fold": 3, "base_accuracy": 0.7737226277372263, "weighted_accuracy": 0.7615571776155717, "improvement": -0.012165450121654597, "correction_stats": {"improved": 11, "worsened": 16, "total_diff": 27}}, {"seed": 42, "fold": 4, "base_accuracy": 0.7664233576642335, "weighted_accuracy": 0.7858880778588808, "improvement": 0.01946472019464729, "correction_stats": {"improved": 19, "worsened": 11, "total_diff": 30}}, {"seed": 42, "fold": 5, "base_accuracy": 0.7834549878345499, "weighted_accuracy": 0.7956204379562044, "improvement": 0.012165450121654486, "correction_stats": {"improved": 19, "worsened": 14, "total_diff": 33}}, {"seed": 142, "fold": 1, "base_accuracy": 0.7839805825242718, "weighted_accuracy": 0.7669902912621359, "improvement": -0.016990291262135915, "correction_stats": {"improved": 11, "worsened": 18, "total_diff": 29}}, {"seed": 142, "fold": 2, "base_accuracy": 0.7718446601941747, "weighted_accuracy": 0.7742718446601942, "improvement": 0.0024271844660194164, "correction_stats": {"improved": 20, "worsened": 19, "total_diff": 39}}, {"seed": 142, "fold": 3, "base_accuracy": 0.7639902676399026, "weighted_accuracy": 0.7761557177615572, "improvement": 0.012165450121654597, "correction_stats": {"improved": 13, "worsened": 8, "total_diff": 21}}, {"seed": 142, "fold": 4, "base_accuracy": 0.7761557177615572, "weighted_accuracy": 0.7493917274939172, "improvement": -0.02676399026763998, "correction_stats": {"improved": 9, "worsened": 20, "total_diff": 29}}, {"seed": 142, "fold": 5, "base_accuracy": 0.781021897810219, "weighted_accuracy": 0.7834549878345499, "improvement": 0.0024330900243308973, "correction_stats": {"improved": 12, "worsened": 11, "total_diff": 23}}, {"seed": 242, "fold": 1, "base_accuracy": 0.7354368932038835, "weighted_accuracy": 0.7572815533980582, "improvement": 0.021844660194174748, "correction_stats": {"improved": 24, "worsened": 15, "total_diff": 39}}, {"seed": 242, "fold": 2, "base_accuracy": 0.7694174757281553, "weighted_accuracy": 0.7645631067961165, "improvement": -0.004854368932038833, "correction_stats": {"improved": 13, "worsened": 15, "total_diff": 28}}, {"seed": 242, "fold": 3, "base_accuracy": 0.7858880778588808, "weighted_accuracy": 0.7761557177615572, "improvement": -0.009732360097323589, "correction_stats": {"improved": 12, "worsened": 16, "total_diff": 28}}, {"seed": 242, "fold": 4, "base_accuracy": 0.7591240875912408, "weighted_accuracy": 0.7615571776155717, "improvement": 0.0024330900243308973, "correction_stats": {"improved": 14, "worsened": 13, "total_diff": 27}}, {"seed": 242, "fold": 5, "base_accuracy": 0.7931873479318735, "weighted_accuracy": 0.781021897810219, "improvement": -0.012165450121654486, "correction_stats": {"improved": 16, "worsened": 21, "total_diff": 37}}]}, "optuna_results": {"best_params": {"sh六院_pos_consistent": 2.4080413670208456, "sh六院_pos_inconsistent": 3.4912983062781984, "sh六院_neg_consistent": 4.472422174517153, "sh六院_neg_inconsistent": 3.4683351303803267, "sh十院_pos_consistent": 1.1838230263927152, "sh十院_pos_inconsistent": 2.454949498240786, "sh十院_neg_consistent": 1.917647447182797, "sh十院_neg_inconsistent": 4.805654098069839, "bj301_pos_consistent": 2.6330695265380006, "bj301_pos_inconsistent": 4.739022049346803, "bj301_neg_consistent": 3.836919656754561, "bj301_neg_inconsistent": 4.671620495141962, "gd南海_pos_consistent": 3.2327077477950557, "gd南海_pos_inconsistent": 3.640773216029312, "gd南海_neg_consistent": 3.6941617699572893, "gd南海_neg_inconsistent": 3.959319824853937, "quality_good_weight": 1.9255992272616782, "quality_poor_weight": 1.1550978119230062, "conflict_alpha": 0.5887205213184451, "extremely_inconsistent_weight": 1.9293197945577787}, "best_value": 0.7474747474747475, "n_trials": 0, "conflict_alpha": 0.5887205213184451}, "xgb_params_optimized": false}, "肥厚v0.5": {"version": "肥厚v0.5", "model_type": "MLPClassifier", "created_at": "2025-03-31 10:06:36", "description": "DeepBoruta50基线版本mlp", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 300, "sample_count": 337, "class_distribution": {"0": 240, "1": 97}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {"hidden_layer_sizes": [100, 50], "activation": "relu", "solver": "adam", "alpha": 0.0001, "learning_rate": "adaptive", "learning_rate_init": 0.001, "max_iter": 800}, "aug_params": {"use_augmentation": false}}}, "肥厚v0.6": {"version": "肥厚v0.6", "model_type": "MLPClassifier", "created_at": "2025-03-31 10:46:07", "description": "DeepBoruta300基线版本SVM", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 300, "sample_count": 337, "class_distribution": {"0": 240, "1": 97}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {"hidden_layer_sizes": [100, 50], "activation": "relu", "solver": "adam", "alpha": 0.0001, "learning_rate": "adaptive", "learning_rate_init": 0.001, "max_iter": 800}, "aug_params": {"use_augmentation": false}}}, "全量v0.1": {"version": "全量v0.1", "model_type": "XGBClassifier", "created_at": "2025-04-07 16:42:58", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9597069597069597, "precision": 0.9719789842381786, "recall": 0.961038961038961, "specificity": 0.9576719576719577, "f1": 0.9664780148019155, "auc": 0.9895485466914038}, "feature_count": 858, "sample_count": 1911, "class_distribution": {"1": 1155, "0": 756}, "error_rate": 0.040293040293040296, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.1-去广东-": {"version": "全量v0.1-去广东-", "model_type": "XGBClassifier", "created_at": "2025-04-07 16:50:27", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9762484774665042, "precision": 0.979381443298969, "recall": 0.9839924670433146, "specificity": 0.9620689655172414, "f1": 0.9816815406294035, "auc": 0.9950613676212741}, "feature_count": 858, "sample_count": 1642, "class_distribution": {"1": 1062, "0": 580}, "error_rate": 0.023751522533495738, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.2": {"version": "全量v0.2", "model_type": "XGBClassifier", "created_at": "2025-04-07 09:57:32", "description": "DeepBoruta300基线版本(无电流源)XGB", "train_metrics": {"accuracy": 0.9722658294086866, "precision": 0.9808027923211169, "recall": 0.9731601731601731, "specificity": 0.9708994708994709, "f1": 0.9769665362885702, "auc": 0.9965848965848967}, "feature_count": 307, "sample_count": 1911, "class_distribution": {"1": 1155, "0": 756}, "error_rate": 0.02773417059131345, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.2-去广东-": {"version": "全量v0.2-去广东-", "model_type": "XGBClassifier", "created_at": "2025-04-07 13:14:16", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9774665042630938, "precision": 0.9812206572769953, "recall": 0.9839924670433146, "specificity": 0.9655172413793104, "f1": 0.9826046074283027, "auc": 0.9944687966751088}, "feature_count": 307, "sample_count": 1642, "class_distribution": {"1": 1062, "0": 580}, "error_rate": 0.02253349573690621, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.2-去十院-": {"version": "全量v0.2-去十院-", "model_type": "XGBClassifier", "created_at": "2025-04-07 13:21:16", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9818291944276196, "precision": 0.9770431588613406, "recall": 0.9953227315247896, "specificity": 0.9570446735395189, "f1": 0.9860982391102873, "auc": 0.9955815082342427}, "feature_count": 307, "sample_count": 1651, "class_distribution": {"1": 1069, "0": 582}, "error_rate": 0.018170805572380374, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.2-去六院-": {"version": "全量v0.2-去六院-", "model_type": "XGBClassifier", "created_at": "2025-04-07 13:27:31", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9769230769230769, "precision": 0.9832285115303984, "recall": 0.9791231732776617, "specificity": 0.973421926910299, "f1": 0.9811715481171549, "auc": 0.9969638435555802}, "feature_count": 307, "sample_count": 1560, "class_distribution": {"1": 958, "0": 602}, "error_rate": 0.023076923076923078, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.2-去安医-": {"version": "全量v0.2-去安医-", "model_type": "XGBClassifier", "created_at": "2025-04-07 13:39:12", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9737274220032841, "precision": 0.979223125564589, "recall": 0.9774571686203787, "specificity": 0.967966573816156, "f1": 0.9783393501805054, "auc": 0.9934481364174104}, "feature_count": 307, "sample_count": 1827, "class_distribution": {"1": 1109, "0": 718}, "error_rate": 0.026272577996715927, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.1-去十院-": {"version": "全量v0.1-去十院-", "model_type": "XGBClassifier", "created_at": "2025-04-07 16:57:53", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.982434887946699, "precision": 0.9770642201834863, "recall": 0.9962581852198317, "specificity": 0.9570446735395189, "f1": 0.9865678554886521, "auc": 0.9967918117262817}, "feature_count": 858, "sample_count": 1651, "class_distribution": {"1": 1069, "0": 582}, "error_rate": 0.01756511205330103, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.1-去六院-": {"version": "全量v0.1-去六院-", "model_type": "XGBClassifier", "created_at": "2025-04-07 17:05:06", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9762820512820513, "precision": 0.9821989528795811, "recall": 0.9791231732776617, "specificity": 0.9717607973421927, "f1": 0.9806586513329848, "auc": 0.9975221772935032}, "feature_count": 858, "sample_count": 1560, "class_distribution": {"1": 958, "0": 602}, "error_rate": 0.023717948717948717, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.1-去安医-": {"version": "全量v0.1-去安医-", "model_type": "XGBClassifier", "created_at": "2025-04-07 17:14:04", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9731800766283525, "precision": 0.9792043399638336, "recall": 0.9765554553651938, "specificity": 0.967966573816156, "f1": 0.9778781038374718, "auc": 0.9940195563771723}, "feature_count": 858, "sample_count": 1827, "class_distribution": {"1": 1109, "0": 718}, "error_rate": 0.02681992337164751, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.3": {"version": "全量v0.3", "model_type": "XGBClassifier", "created_at": "2025-04-07 18:32:38", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB", "train_metrics": {"accuracy": 0.9738356881214024, "precision": 0.9808529155787642, "recall": 0.9757575757575757, "specificity": 0.9708994708994709, "f1": 0.978298611111111, "auc": 0.9973556425937379}, "feature_count": 307, "sample_count": 1911, "class_distribution": {"1": 1155, "0": 756}, "error_rate": 0.026164311878597593, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.3-去广东-": {"version": "全量v0.3-去广东-", "model_type": "XGBClassifier", "created_at": "2025-04-07 18:39:40", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9786845310596833, "precision": 0.980355472404116, "recall": 0.9868173258003766, "specificity": 0.9637931034482758, "f1": 0.983575786015955, "auc": 0.9937431002013117}, "feature_count": 813, "sample_count": 1642, "class_distribution": {"1": 1062, "0": 580}, "error_rate": 0.021315468940316686, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.3-去十院-": {"version": "全量v0.3-去十院-", "model_type": "XGBClassifier", "created_at": "2025-04-07 18:46:37", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9836462749848577, "precision": 0.9788602941176471, "recall": 0.9962581852198317, "specificity": 0.9604810996563574, "f1": 0.9874826147426983, "auc": 0.9971229173296816}, "feature_count": 813, "sample_count": 1651, "class_distribution": {"1": 1069, "0": 582}, "error_rate": 0.01635372501514234, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.3-去六院-": {"version": "全量v0.3-去六院-", "model_type": "XGBClassifier", "created_at": "2025-04-07 18:53:13", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9698717948717949, "precision": 0.9749739311783108, "recall": 0.975991649269311, "specificity": 0.9601328903654485, "f1": 0.9754825247782994, "auc": 0.9941877804673358}, "feature_count": 813, "sample_count": 1560, "class_distribution": {"1": 958, "0": 602}, "error_rate": 0.03012820512820513, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.3-去安医-": {"version": "全量v0.3-去安医-", "model_type": "XGBClassifier", "created_at": "2025-04-07 19:00:58", "description": "DeepBoruta300基线版本XGB", "train_metrics": {"accuracy": 0.9770114942528736, "precision": 0.9836808703535811, "recall": 0.9783588818755635, "specificity": 0.9749303621169917, "f1": 0.9810126582278481, "auc": 0.9972157405477091}, "feature_count": 813, "sample_count": 1827, "class_distribution": {"1": 1109, "0": 718}, "error_rate": 0.022988505747126436, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.4": {"version": "全量v0.4", "model_type": "XGBClassifier", "created_at": "2025-04-08 14:45:26", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB去除偏差数据/综合标签", "train_metrics": {"accuracy": 0.9785752544188537, "precision": 0.9832635983263598, "recall": 0.9832635983263598, "specificity": 0.9702380952380952, "f1": 0.9832635983263598, "auc": 0.9966128710898586}, "feature_count": 308, "sample_count": 1867, "class_distribution": {"1": 1195, "0": 672}, "error_rate": 0.021424745581146223, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.4-仅综合标签": {"version": "全量v0.4-仅综合标签", "model_type": "XGBClassifier", "created_at": "2025-04-08 15:48:41", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9764521193092621, "precision": 0.9821138211382113, "recall": 0.9813160032493907, "specificity": 0.9676470588235294, "f1": 0.9817147501015846, "auc": 0.9980288622353897}, "feature_count": 812, "sample_count": 1911, "class_distribution": {"1": 1231, "0": 680}, "error_rate": 0.023547880690737835, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.4-仅综合标签-非叠加": {"version": "全量v0.4-仅综合标签-非叠加", "model_type": "XGBClassifier", "created_at": "2025-04-08 15:59:44", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9754055468341183, "precision": 0.9813008130081301, "recall": 0.9805036555645816, "specificity": 0.9661764705882353, "f1": 0.9809020723283218, "auc": 0.9970994409136522}, "feature_count": 308, "sample_count": 1911, "class_distribution": {"1": 1231, "0": 680}, "error_rate": 0.024594453165881738, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.4-仅综合标签-重选特征": {"version": "全量v0.4-仅综合标签-重选特征", "model_type": "XGBClassifier", "created_at": "2025-04-08 17:25:12", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.97854526425955, "precision": 0.9829545454545454, "recall": 0.983753046303818, "specificity": 0.9691176470588235, "f1": 0.9833536337799431, "auc": 0.99579132221532}, "feature_count": 307, "sample_count": 1911, "class_distribution": {"1": 1231, "0": 680}, "error_rate": 0.021454735740450027, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合1": {"version": "全量v0.5-综合1", "model_type": "XGBClassifier", "created_at": "2025-04-11 17:46:56", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9460932768019382, "precision": 0.9365609348914858, "recall": 0.9885462555066079, "specificity": 0.8527131782945736, "f1": 0.9618516930990141, "auc": 0.9844978315063346}, "feature_count": 310, "sample_count": 1651, "class_distribution": {"1": 1135, "0": 516}, "error_rate": 0.05390672319806178, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合2": {"version": "全量v0.5-综合2", "model_type": "XGBClassifier", "created_at": "2025-04-12 11:05:59", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9769836462749849, "precision": 0.9740708729472775, "recall": 0.9929515418502203, "specificity": 0.9418604651162791, "f1": 0.9834205933682374, "auc": 0.9895382986715842}, "feature_count": 309, "sample_count": 1651, "class_distribution": {"1": 1135, "0": 516}, "error_rate": 0.02301635372501514, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合3": {"version": "全量v0.5-综合3", "model_type": "XGBClassifier", "created_at": "2025-04-12 12:51:18", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9837451235370611, "precision": 0.9826086956521739, "recall": 0.9955947136563876, "specificity": 0.9503722084367245, "f1": 0.9890590809628009, "auc": 0.9939987538395951}, "feature_count": 309, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.016254876462938883, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合4": {"version": "全量v0.5-综合4", "model_type": "XGBClassifier", "created_at": "2025-04-14 09:52:40", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9720416124837451, "precision": 0.9698795180722891, "recall": 0.9929515418502203, "specificity": 0.913151364764268, "f1": 0.9812799303439269, "auc": 0.9891955706649469}, "feature_count": 310, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.027958387516254877, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合5": {"version": "全量v0.5-综合5", "model_type": "XGBClassifier", "created_at": "2025-04-14 14:50:59", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9660811629315567, "precision": 0.9591489361702128, "recall": 0.9929515418502203, "specificity": 0.9069767441860465, "f1": 0.9757575757575757, "auc": 0.9872724789126797}, "feature_count": 312, "sample_count": 1651, "class_distribution": {"1": 1135, "0": 516}, "error_rate": 0.03391883706844337, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合11": {"version": "全量v0.5-综合11", "model_type": "XGBClassifier", "created_at": "2025-04-14 15:08:23", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9636583888552392, "precision": 0.9543533389687235, "recall": 0.9947136563876652, "specificity": 0.8953488372093024, "f1": 0.9741156169111304, "auc": 0.9883413584673703}, "feature_count": 310, "sample_count": 1651, "class_distribution": {"1": 1135, "0": 516}, "error_rate": 0.03634161114476075, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合51": {"version": "全量v0.5-综合51", "model_type": "XGBClassifier", "created_at": "2025-04-14 15:39:55", "description": "DeepBoruta300基线版本(混合电流源特征联合选择)XGB综合标签", "train_metrics": {"accuracy": 0.9745608721986675, "precision": 0.970714900947459, "recall": 0.9929515418502203, "specificity": 0.9341085271317829, "f1": 0.9817073170731707, "auc": 0.990023221664447}, "feature_count": 312, "sample_count": 1651, "class_distribution": {"1": 1135, "0": 516}, "error_rate": 0.025439127801332527, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合6": {"version": "全量v0.5-综合6", "model_type": "XGBClassifier", "created_at": "2025-04-15 16:46:59", "description": "DeepBoruta300基线版本XGB综合标签", "train_metrics": {"accuracy": 0.9791937581274383, "precision": 0.9766637856525497, "recall": 0.9955947136563876, "specificity": 0.9330024813895782, "f1": 0.9860383944153578, "auc": 0.994543129174364}, "feature_count": 313, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.02080624187256177, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合7": {"version": "全量v0.5-综合7", "model_type": "XGBClassifier", "created_at": "2025-04-15 17:52:45", "description": "DeepBoruta300基线版本XGB综合标签", "train_metrics": {"accuracy": 0.9791937581274383, "precision": 0.9774891774891775, "recall": 0.9947136563876652, "specificity": 0.9354838709677419, "f1": 0.9860262008733625, "auc": 0.9932073326701719}, "feature_count": 310, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.02080624187256177, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合6-训测optuna": {"version": "全量v0.5-综合6-训测optuna", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-04-17 07:14:52", "description": "DeepBoruta300基线版本XGB综合标签", "train_metrics": {"accuracy": 0.9629817444219066, "precision": 0.9547244094488189, "recall": 0.9972583961617546, "specificity": 0.8654970760233918, "f1": 0.9755279919544083, "auc": 0.9883241345309813}, "feature_count": 313, "sample_count": 1972, "class_distribution": {"1": 1459, "0": 513}, "error_rate": 0.03701825557809331, "cv_results": {"random_seeds": [388, 488, 588], "best_seed": 142, "best_fold": 1, "best_score": 0.830379746835443, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.8202531645569621, "weighted_accuracy": 0.8075949367088607, "improvement": -0.012658227848101333, "correction_stats": {"improved": 5, "worsened": 10, "total_diff": 15}}, {"seed": 42, "fold": 2, "base_accuracy": 0.8202531645569621, "weighted_accuracy": 0.8202531645569621, "improvement": 0.0, "correction_stats": {"improved": 5, "worsened": 5, "total_diff": 10}}, {"seed": 42, "fold": 3, "base_accuracy": 0.7944162436548223, "weighted_accuracy": 0.8096446700507615, "improvement": 0.015228426395939132, "correction_stats": {"improved": 9, "worsened": 3, "total_diff": 12}}, {"seed": 42, "fold": 4, "base_accuracy": 0.7893401015228426, "weighted_accuracy": 0.817258883248731, "improvement": 0.027918781725888353, "correction_stats": {"improved": 15, "worsened": 4, "total_diff": 19}}, {"seed": 42, "fold": 5, "base_accuracy": 0.8020304568527918, "weighted_accuracy": 0.8147208121827412, "improvement": 0.012690355329949332, "correction_stats": {"improved": 11, "worsened": 6, "total_diff": 17}}, {"seed": 142, "fold": 1, "base_accuracy": 0.8202531645569621, "weighted_accuracy": 0.830379746835443, "improvement": 0.010126582278480956, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"seed": 142, "fold": 2, "base_accuracy": 0.810126582278481, "weighted_accuracy": 0.8151898734177215, "improvement": 0.005063291139240533, "correction_stats": {"improved": 9, "worsened": 7, "total_diff": 16}}, {"seed": 142, "fold": 3, "base_accuracy": 0.8147208121827412, "weighted_accuracy": 0.8071065989847716, "improvement": -0.0076142131979696215, "correction_stats": {"improved": 6, "worsened": 9, "total_diff": 15}}, {"seed": 142, "fold": 4, "base_accuracy": 0.7817258883248731, "weighted_accuracy": 0.7918781725888325, "improvement": 0.010152284263959421, "correction_stats": {"improved": 10, "worsened": 6, "total_diff": 16}}, {"seed": 142, "fold": 5, "base_accuracy": 0.7969543147208121, "weighted_accuracy": 0.7791878172588832, "improvement": -0.017766497461928932, "correction_stats": {"improved": 3, "worsened": 10, "total_diff": 13}}, {"seed": 242, "fold": 1, "base_accuracy": 0.8025316455696202, "weighted_accuracy": 0.8177215189873418, "improvement": 0.0151898734177216, "correction_stats": {"improved": 11, "worsened": 5, "total_diff": 16}}, {"seed": 242, "fold": 2, "base_accuracy": 0.810126582278481, "weighted_accuracy": 0.8050632911392405, "improvement": -0.005063291139240533, "correction_stats": {"improved": 8, "worsened": 10, "total_diff": 18}}, {"seed": 242, "fold": 3, "base_accuracy": 0.8071065989847716, "weighted_accuracy": 0.8147208121827412, "improvement": 0.0076142131979696215, "correction_stats": {"improved": 7, "worsened": 4, "total_diff": 11}}, {"seed": 242, "fold": 4, "base_accuracy": 0.7893401015228426, "weighted_accuracy": 0.7944162436548223, "improvement": 0.005076142131979711, "correction_stats": {"improved": 11, "worsened": 9, "total_diff": 20}}, {"seed": 242, "fold": 5, "base_accuracy": 0.7918781725888325, "weighted_accuracy": 0.8096446700507615, "improvement": 0.017766497461928932, "correction_stats": {"improved": 10, "worsened": 3, "total_diff": 13}}]}, "optuna_results": {"best_params": {"n_estimators": 500, "learning_rate": 0.01494094869442187, "max_depth": 10, "subsample": 0.9472754953675799, "colsample_bytree": 0.7685512701504291, "sh六院_pos_consistent": 1.0340747926638427, "sh六院_pos_inconsistent": 2.5268440874047715, "sh六院_neg_consistent": 1.2878494765264787, "sh六院_neg_inconsistent": 2.250215072303141, "sh中山_pos_consistent": 4.762276824572181, "sh中山_pos_inconsistent": 1.0403717401363477, "sh中山_neg_consistent": 2.414992953795863, "sh中山_neg_inconsistent": 3.2692927374905674, "bj301_pos_consistent": 2.44457001150787, "bj301_pos_inconsistent": 3.7521827498380484, "bj301_neg_consistent": 1.5064021360908688, "bj301_neg_inconsistent": 3.6608831080672197, "gd南海_pos_consistent": 0.5177375235556033, "gd南海_pos_inconsistent": 1.3369664123101375, "gd南海_neg_consistent": 1.8079877271314455, "gd南海_neg_inconsistent": 4.227497398953839, "安医_pos_consistent": 2.2784062033883123, "安医_pos_inconsistent": 1.5881210094622735, "安医_neg_consistent": 2.8953571263163314, "安医_neg_inconsistent": 0.17785018730034763, "quality_good_weight": 1.0847802105241835, "quality_poor_weight": 1.8491081188323302, "hypertension_yes_weight": 1.3853314248962816, "hypertension_no_weight": 1.6061955264076295, "conflict_alpha": 1.145105419764125}, "best_value": 0.8125352866842299, "n_trials": 500, "conflict_alpha": 1.145105419764125}, "xgb_params_optimized": true}, "全量v0.5-综合6-训测optuna-1": {"version": "全量v0.5-综合6-训测optuna-1", "model_type": "OptunaEnsembleModelV2", "created_at": "2025-04-17 11:43:40", "description": "DeepBoruta300基线版本XGB综合标签", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 314, "sample_count": 1972, "class_distribution": {"1": 1459, "0": 513}, "error_rate": 0.0, "cv_results": {"random_seeds": [388, 488, 588], "best_seed": 42, "best_fold": 2, "best_score": 0.8405063291139241, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.7949367088607595, "weighted_accuracy": 0.8075949367088607, "improvement": 0.012658227848101222, "correction_stats": {"improved": 9, "worsened": 4, "total_diff": 13}}, {"seed": 42, "fold": 2, "base_accuracy": 0.8329113924050633, "weighted_accuracy": 0.8405063291139241, "improvement": 0.0075949367088608, "correction_stats": {"improved": 9, "worsened": 6, "total_diff": 15}}, {"seed": 42, "fold": 3, "base_accuracy": 0.8121827411167513, "weighted_accuracy": 0.8096446700507615, "improvement": -0.0025380710659898, "correction_stats": {"improved": 7, "worsened": 8, "total_diff": 15}}, {"seed": 42, "fold": 4, "base_accuracy": 0.8071065989847716, "weighted_accuracy": 0.8045685279187818, "improvement": -0.0025380710659898, "correction_stats": {"improved": 6, "worsened": 7, "total_diff": 13}}, {"seed": 42, "fold": 5, "base_accuracy": 0.7868020304568528, "weighted_accuracy": 0.7944162436548223, "improvement": 0.0076142131979695105, "correction_stats": {"improved": 11, "worsened": 8, "total_diff": 19}}, {"seed": 142, "fold": 1, "base_accuracy": 0.8025316455696202, "weighted_accuracy": 0.8126582278481013, "improvement": 0.010126582278481067, "correction_stats": {"improved": 11, "worsened": 7, "total_diff": 18}}, {"seed": 142, "fold": 2, "base_accuracy": 0.8126582278481013, "weighted_accuracy": 0.8202531645569621, "improvement": 0.0075949367088608, "correction_stats": {"improved": 11, "worsened": 8, "total_diff": 19}}, {"seed": 142, "fold": 3, "base_accuracy": 0.799492385786802, "weighted_accuracy": 0.799492385786802, "improvement": 0.0, "correction_stats": {"improved": 7, "worsened": 7, "total_diff": 14}}, {"seed": 142, "fold": 4, "base_accuracy": 0.8197969543147208, "weighted_accuracy": 0.8197969543147208, "improvement": 0.0, "correction_stats": {"improved": 8, "worsened": 8, "total_diff": 16}}, {"seed": 142, "fold": 5, "base_accuracy": 0.8020304568527918, "weighted_accuracy": 0.8020304568527918, "improvement": 0.0, "correction_stats": {"improved": 8, "worsened": 8, "total_diff": 16}}, {"seed": 242, "fold": 1, "base_accuracy": 0.7949367088607595, "weighted_accuracy": 0.7974683544303798, "improvement": 0.0025316455696202667, "correction_stats": {"improved": 10, "worsened": 9, "total_diff": 19}}, {"seed": 242, "fold": 2, "base_accuracy": 0.8151898734177215, "weighted_accuracy": 0.8278481012658228, "improvement": 0.012658227848101222, "correction_stats": {"improved": 10, "worsened": 5, "total_diff": 15}}, {"seed": 242, "fold": 3, "base_accuracy": 0.8274111675126904, "weighted_accuracy": 0.8223350253807107, "improvement": -0.005076142131979711, "correction_stats": {"improved": 3, "worsened": 5, "total_diff": 8}}, {"seed": 242, "fold": 4, "base_accuracy": 0.7944162436548223, "weighted_accuracy": 0.7918781725888325, "improvement": -0.0025380710659898, "correction_stats": {"improved": 4, "worsened": 5, "total_diff": 9}}, {"seed": 242, "fold": 5, "base_accuracy": 0.7944162436548223, "weighted_accuracy": 0.7918781725888325, "improvement": -0.0025380710659898, "correction_stats": {"improved": 6, "worsened": 7, "total_diff": 13}}]}, "optuna_results": {"best_params": {"n_estimators": 500, "learning_rate": 0.01494094869442187, "max_depth": 10, "subsample": 0.9472754953675799, "colsample_bytree": 0.7685512701504291, "sh六院_pos_consistent": 1.0340747926638427, "sh六院_pos_inconsistent": 2.5268440874047715, "sh六院_neg_consistent": 1.2878494765264787, "sh六院_neg_inconsistent": 2.250215072303141, "sh中山_pos_consistent": 4.762276824572181, "sh中山_pos_inconsistent": 1.0403717401363477, "sh中山_neg_consistent": 2.414992953795863, "sh中山_neg_inconsistent": 3.2692927374905674, "bj301_pos_consistent": 2.44457001150787, "bj301_pos_inconsistent": 3.7521827498380484, "bj301_neg_consistent": 1.5064021360908688, "bj301_neg_inconsistent": 3.6608831080672197, "gd南海_pos_consistent": 0.5177375235556033, "gd南海_pos_inconsistent": 1.3369664123101375, "gd南海_neg_consistent": 1.8079877271314455, "gd南海_neg_inconsistent": 4.227497398953839, "安医_pos_consistent": 2.2784062033883123, "安医_pos_inconsistent": 1.5881210094622735, "安医_neg_consistent": 2.8953571263163314, "安医_neg_inconsistent": 0.17785018730034763, "quality_good_weight": 1.0847802105241835, "quality_poor_weight": 1.8491081188323302, "hypertension_yes_weight": 1.3853314248962816, "hypertension_no_weight": 1.6061955264076295, "conflict_alpha": 1.145105419764125}, "best_value": 0.8125352866842299, "n_trials": 10, "conflict_alpha": 1.145105419764125}, "xgb_params_optimized": true}, "全量v0.5-综合6-训测meta": {"version": "全量v0.5-综合6-训测meta", "model_type": "MetaLearningEnsembleModel", "created_at": "2025-04-17 16:15:46", "description": "DeepBoruta300基线版本XGB综合标签", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 314, "sample_count": 1972, "class_distribution": {"1": 1459, "0": 513}, "error_rate": 0.0, "cv_results": {"random_seeds": [42, 142, 242], "best_seed": 242, "best_fold": 3, "best_score": 0.8197969543147208, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.789873417721519, "weighted_accuracy": 0.7873417721518987, "improvement": -0.0025316455696202667, "correction_stats": {"improved": 2, "worsened": 3, "total_diff": 5}}, {"seed": 42, "fold": 2, "base_accuracy": 0.830379746835443, "weighted_accuracy": 0.8177215189873418, "improvement": -0.012658227848101222, "correction_stats": {"improved": 3, "worsened": 8, "total_diff": 11}}, {"seed": 42, "fold": 3, "base_accuracy": 0.8121827411167513, "weighted_accuracy": 0.8071065989847716, "improvement": -0.005076142131979711, "correction_stats": {"improved": 3, "worsened": 5, "total_diff": 8}}, {"seed": 42, "fold": 4, "base_accuracy": 0.799492385786802, "weighted_accuracy": 0.799492385786802, "improvement": 0.0, "correction_stats": {"improved": 6, "worsened": 6, "total_diff": 12}}, {"seed": 42, "fold": 5, "base_accuracy": 0.7893401015228426, "weighted_accuracy": 0.7918781725888325, "improvement": 0.002538071065989911, "correction_stats": {"improved": 3, "worsened": 2, "total_diff": 5}}, {"seed": 142, "fold": 1, "base_accuracy": 0.8075949367088607, "weighted_accuracy": 0.7949367088607595, "improvement": -0.012658227848101222, "correction_stats": {"improved": 2, "worsened": 7, "total_diff": 9}}, {"seed": 142, "fold": 2, "base_accuracy": 0.8227848101265823, "weighted_accuracy": 0.8177215189873418, "improvement": -0.005063291139240533, "correction_stats": {"improved": 1, "worsened": 3, "total_diff": 4}}, {"seed": 142, "fold": 3, "base_accuracy": 0.7868020304568528, "weighted_accuracy": 0.7868020304568528, "improvement": 0.0, "correction_stats": {"improved": 2, "worsened": 2, "total_diff": 4}}, {"seed": 142, "fold": 4, "base_accuracy": 0.8045685279187818, "weighted_accuracy": 0.8071065989847716, "improvement": 0.0025380710659898, "correction_stats": {"improved": 4, "worsened": 3, "total_diff": 7}}, {"seed": 142, "fold": 5, "base_accuracy": 0.8096446700507615, "weighted_accuracy": 0.7893401015228426, "improvement": -0.020304568527918843, "correction_stats": {"improved": 1, "worsened": 9, "total_diff": 10}}, {"seed": 242, "fold": 1, "base_accuracy": 0.7924050632911392, "weighted_accuracy": 0.7924050632911392, "improvement": 0.0, "correction_stats": {"improved": 4, "worsened": 4, "total_diff": 8}}, {"seed": 242, "fold": 2, "base_accuracy": 0.8126582278481013, "weighted_accuracy": 0.810126582278481, "improvement": -0.0025316455696202667, "correction_stats": {"improved": 3, "worsened": 4, "total_diff": 7}}, {"seed": 242, "fold": 3, "base_accuracy": 0.8223350253807107, "weighted_accuracy": 0.8197969543147208, "improvement": -0.002538071065989911, "correction_stats": {"improved": 4, "worsened": 5, "total_diff": 9}}, {"seed": 242, "fold": 4, "base_accuracy": 0.7969543147208121, "weighted_accuracy": 0.8096446700507615, "improvement": 0.012690355329949332, "correction_stats": {"improved": 6, "worsened": 1, "total_diff": 7}}, {"seed": 242, "fold": 5, "base_accuracy": 0.7969543147208121, "weighted_accuracy": 0.7969543147208121, "improvement": 0.0, "correction_stats": {"improved": 4, "worsened": 4, "total_diff": 8}}]}, "meta_learning_results": {"best_params": {"sh六院_pos_consistent": 1.0, "sh六院_pos_inconsistent": 1.0, "sh六院_neg_consistent": 1.0, "sh六院_neg_inconsistent": 1.0, "sh中山_pos_consistent": 1.0, "sh中山_pos_inconsistent": 1.0, "sh中山_neg_consistent": 1.0, "sh中山_neg_inconsistent": 1.0, "bj301_pos_consistent": 1.0, "bj301_pos_inconsistent": 1.0, "bj301_neg_consistent": 1.0, "bj301_neg_inconsistent": 1.0, "gd南海_pos_consistent": 1.0, "gd南海_pos_inconsistent": 1.0, "gd南海_neg_consistent": 1.0, "gd南海_neg_inconsistent": 1.0, "安医_pos_consistent": 1.0, "安医_pos_inconsistent": 1.0, "安医_neg_consistent": 1.0, "安医_neg_inconsistent": 1.0, "quality_good_weight": 1.0, "quality_poor_weight": 1.0, "conflict_alpha": 1.0, "hypertension_yes_weight": 1.0, "hypertension_no_weight": 1.0}, "best_value": 0, "n_iterations": 0, "meta_learning_rate": 0.1, "conflict_alpha": 1.0, "subtask_count": 5000, "training_time": 0.0}, "xgb_params_optimized": false}, "全量v0.5-综合62": {"version": "全量v0.5-综合62", "model_type": "XGBClassifier", "created_at": "2025-04-18 17:53:52", "description": "DeepBoruta300基线版本XGB综合标签", "train_metrics": {"accuracy": 0.9876462938881665, "precision": 0.9869109947643979, "recall": 0.9964757709251101, "specificity": 0.9627791563275434, "f1": 0.9916703200350723, "auc": 0.999033679124627}, "feature_count": 313, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.01235370611183355, "optimization": {"optimized": true, "best_score": 0.8289406124053559, "model_params": {"learning_rate": 0.011219188662182425, "n_estimators": 877, "max_depth": 10, "subsample": 0.8160560791415381, "colsample_bytree": 0.7377223685751669, "min_child_weight": 8, "gamma": 1.9435469143216455}, "aug_params": {"use_augmentation": true, "balance_method": "smote", "noise_method": "gaussian_noise", "noise_ratio": 0.1419255409483006, "balance_by_center": true, "center_target_ratio": [0.8879554562841507, 0.830959407731753, 0.8040822669058657, 0.7177512079173799, 0.6215224082583429]}}}, "('全量v0.5-综合63',)": {"version": "('全量v0.5-综合63',)", "model_type": "StackingClassifier", "created_at": "2025-04-21 09:35:09", "description": "DeepBoruta300基线版本", "train_metrics": {"accuracy": 0.9863459037711313, "precision": 0.9818339100346021, "recall": 1.0, "specificity": 0.9478908188585607, "f1": 0.9908336970755128, "auc": 1.0}, "feature_count": 313, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.013654096228868661, "optimization": {"optimized": false, "model_params": {}, "aug_params": {"use_augmentation": false}}}, "全量v0.5-综合63": {"version": "全量v0.5-综合63", "model_type": "StackingClassifier", "created_at": "2025-04-21 10:07:54", "description": "DeepBoruta300基线版本", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 313, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {}, "aug_params": {"use_augmentation": false}}}, "十院微循环v0.1": {"version": "十院微循环v0.1", "model_type": "StackingClassifier", "created_at": "2025-04-22 15:46:14", "description": "DeepBoruta300基线版本default model", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 300, "sample_count": 237, "class_distribution": {"1": 143, "0": 94}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {}, "aug_params": {"use_augmentation": false}}}, "十院微循环v0.2": {"version": "十院微循环v0.2", "model_type": "StackingClassifier", "created_at": "2025-04-22 15:55:11", "description": "DeepBoruta300基线版本default model", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 300, "sample_count": 237, "class_distribution": {"1": 143, "0": 94}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {}, "aug_params": {"use_augmentation": false}}}, "十院微循环v0.3": {"version": "十院微循环v0.3", "model_type": "SVC", "created_at": "2025-04-23 09:31:52", "description": "DeepBoruta150基线版本", "train_metrics": {"accuracy": 0.9831223628691983, "precision": 0.986013986013986, "recall": 0.986013986013986, "specificity": 0.9787234042553191, "f1": 0.986013986013986, "auc": 0.9988097009373604}, "feature_count": 150, "sample_count": 237, "class_distribution": {"1": 143, "0": 94}, "error_rate": 0.016877637130801686, "optimization": {"optimized": false, "model_params": {"C": 1.0, "kernel": "rbf", "gamma": "scale"}, "aug_params": {"use_augmentation": false}}}, "十院微循环v0.4": {"version": "十院微循环v0.4", "model_type": "MLPClassifier", "created_at": "2025-04-23 10:57:37", "description": "DeepBoruta20基线版本default model", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 300, "sample_count": 237, "class_distribution": {"1": 143, "0": 94}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {"hidden_layer_sizes": [100, 50], "activation": "relu", "solver": "adam", "alpha": 0.0001, "learning_rate": "adaptive", "learning_rate_init": 0.001, "max_iter": 800}, "aug_params": {"use_augmentation": false}}}, "feature0324_十院微循环_训练_DeepBoruta_20": {"version": "feature0324_十院微循环_训练_DeepBoruta_20", "model_type": "StackingClassifier", "created_at": "2025-04-23 10:38:43", "description": "DeepBoruta20基线版本", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 150, "sample_count": 237, "class_distribution": {"1": 143, "0": 94}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {}, "aug_params": {"use_augmentation": false}}}, "全量v0.6-综合1": {"version": "全量v0.6-综合1", "model_type": "XGBClassifier", "created_at": "2025-05-13 16:32:53", "description": "DeepBoruta300基线版本Stacking综合标签", "train_metrics": {"accuracy": 0.9811443433029909, "precision": 0.9759036144578314, "recall": 0.9991189427312775, "specificity": 0.9305210918114144, "f1": 0.9873748367435786, "auc": 0.9890053672347263}, "feature_count": 312, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.0188556566970091, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "中山微循环v0.1": {"version": "中山微循环v0.1", "model_type": "XGBClassifier", "created_at": "2025-05-14 10:11:15", "description": "DeepBoruta20基线版本default model", "train_metrics": {"accuracy": 0.9634146341463414, "precision": 0.9583333333333334, "recall": 0.9787234042553191, "specificity": 0.9428571428571428, "f1": 0.968421052631579, "auc": 0.989935832489024}, "feature_count": 30, "sample_count": 246, "class_distribution": {"1": 141, "0": 105}, "error_rate": 0.036585365853658534, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "中山微循环v0.2": {"version": "中山微循环v0.2", "model_type": "XGBClassifier", "created_at": "2025-05-14 10:17:44", "description": "DeepBoruta20基线版本default model", "train_metrics": {"accuracy": 0.967479674796748, "precision": 0.965034965034965, "recall": 0.9787234042553191, "specificity": 0.9523809523809523, "f1": 0.971830985915493, "auc": 0.9881121242823371}, "feature_count": 30, "sample_count": 246, "class_distribution": {"1": 141, "0": 105}, "error_rate": 0.032520325203252036, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "中山微循环v0.3": {"version": "中山微循环v0.3", "model_type": "SVC", "created_at": "2025-05-15 10:57:56", "description": "DeepBoruta基线版本default model", "train_metrics": {"accuracy": 0.9715447154471545, "precision": 0.9652777777777778, "recall": 0.9858156028368794, "specificity": 0.9523809523809523, "f1": 0.9754385964912281, "auc": 0.9947990543735225}, "feature_count": 150, "sample_count": 246, "class_distribution": {"1": 141, "0": 105}, "error_rate": 0.028455284552845527, "optimization": {"optimized": false, "model_params": {"C": 1.0, "kernel": "rbf", "gamma": "scale"}, "aug_params": {"use_augmentation": false}}}, "全量v0.6-综合2": {"version": "全量v0.6-综合2", "model_type": "XGBClassifier", "created_at": "2025-05-14 15:31:55", "description": "DeepBoruta300基线版本Stacking综合标签", "train_metrics": {"accuracy": 0.9798439531859557, "precision": 0.9750430292598967, "recall": 0.9982378854625551, "specificity": 0.9280397022332506, "f1": 0.9865041358293426, "auc": 0.9900722554410206}, "feature_count": 312, "sample_count": 1538, "class_distribution": {"1": 1135, "0": 403}, "error_rate": 0.020156046814044214, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "中山微循环v0.4": {"version": "中山微循环v0.4", "model_type": "SVC", "created_at": "2025-05-26 16:50:23", "description": "DeepBoruta基线版本default model", "train_metrics": {"accuracy": 0.8859934853420195, "precision": 0.8770053475935828, "recall": 0.9318181818181818, "specificity": 0.8244274809160306, "f1": 0.9035812672176309, "auc": 0.957971894517696}, "feature_count": 30, "sample_count": 307, "class_distribution": {"1": 176, "0": 131}, "error_rate": 0.11400651465798045, "optimization": {"optimized": false, "model_params": {"C": 1.0, "kernel": "rbf", "gamma": "scale"}, "aug_params": {"use_augmentation": false}}}, "北京微循环v0.1": {"version": "北京微循环v0.1", "model_type": "SVC", "created_at": "2025-05-23 15:44:16", "description": "DeepBoruta基线版本default model", "train_metrics": {"accuracy": 0.9007633587786259, "precision": 0.8854166666666666, "recall": 0.9770114942528736, "specificity": 0.75, "f1": 0.9289617486338797, "auc": 0.9798197492163009}, "feature_count": 62, "sample_count": 262, "class_distribution": {"1": 174, "0": 88}, "error_rate": 0.09923664122137404, "optimization": {"optimized": false, "model_params": {"C": 1.0, "kernel": "rbf", "gamma": "scale"}, "aug_params": {"use_augmentation": false}}}, "v2.3.0": {"version": "v2.3.0", "model_type": "XGBClassifier", "created_at": "2025-05-26 14:40:52", "description": "DeepBoruta300基线版本0303版特征造影标签", "train_metrics": {"accuracy": 0.9731820526044352, "precision": 0.9723538704581358, "recall": 0.9863782051282052, "specificity": 0.9493487698986975, "f1": 0.979315831344471, "auc": 0.9870183030910238}, "feature_count": 308, "sample_count": 1939, "class_distribution": {"1": 1248, "0": 691}, "error_rate": 0.026817947395564725, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.3.1": {"version": "v2.3.1", "model_type": "XGBClassifier", "created_at": "2025-05-26 15:03:50", "description": "DeepBoruta300基线版本0303版特征-造影选择特征，综合标签训练综合标签", "train_metrics": {"accuracy": 0.9453326456936565, "precision": 0.9334195216548158, "recall": 0.9979267449896337, "specificity": 0.790650406504065, "f1": 0.9645958583834335, "auc": 0.9868412920480276}, "feature_count": 308, "sample_count": 1939, "class_distribution": {"1": 1447, "0": 492}, "error_rate": 0.05466735430634347, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.3.2": {"version": "v2.3.2", "model_type": "XGBClassifier", "created_at": "2025-05-27 08:31:03", "description": "DeepBoruta300基线版本0303版特征-综合选择特征，综合标签训练综合标签", "train_metrics": {"accuracy": 0.9824651882413615, "precision": 0.9789830508474576, "recall": 0.9979267449896337, "specificity": 0.9369918699186992, "f1": 0.9883641341546886, "auc": 0.9953492226698355}, "feature_count": 308, "sample_count": 1939, "class_distribution": {"1": 1447, "0": 492}, "error_rate": 0.017534811758638472, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.3.3": {"version": "v2.3.3", "model_type": "XGBClassifier", "created_at": "2025-05-27 14:18:34", "description": "DeepBoruta300基线版本", "train_metrics": {"accuracy": 0.9871067560598247, "precision": 0.986986301369863, "recall": 0.9958534899792675, "specificity": 0.9613821138211383, "f1": 0.9914000687994495, "auc": 0.9972426832077581}, "feature_count": 308, "sample_count": 1939, "class_distribution": {"1": 1447, "0": 492}, "error_rate": 0.012893243940175348, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.3.4": {"version": "v2.3.4", "model_type": "OptunaEnsembleModelV3_Simplified", "created_at": "2025-05-29 13:45:20", "description": "数据:0523训测集原始特征:0303版特征(自动时刻点0.5)选择特征:训测PureBoruta,追加cur/ci特征(原训练数据) 模型:XGB,optuna,综合标签搜索增强:无", "train_metrics": {"accuracy": 0.9682212133718531, "precision": 0.9651847884306374, "recall": 0.9933847850055126, "specificity": 0.8932676518883416, "f1": 0.9790817712578105, "auc": 0.9888895527035664}, "feature_count": 308, "sample_count": 2423, "class_distribution": {"1": 1814, "0": 609}, "error_rate": 0.03177878662814693, "cv_results": {"random_seeds": [275, 375, 475], "best_seed": 242, "best_fold": 1, "best_score": 0.8474226804123711, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.8309278350515464, "weighted_accuracy": 0.8412371134020619, "improvement": 0.010309278350515427, "correction_stats": {"improved": 10, "worsened": 5, "total_diff": 15}}, {"seed": 42, "fold": 2, "base_accuracy": 0.8536082474226804, "weighted_accuracy": 0.845360824742268, "improvement": -0.008247422680412342, "correction_stats": {"improved": 3, "worsened": 7, "total_diff": 10}}, {"seed": 42, "fold": 3, "base_accuracy": 0.8268041237113402, "weighted_accuracy": 0.8309278350515464, "improvement": 0.004123711340206282, "correction_stats": {"improved": 8, "worsened": 6, "total_diff": 14}}, {"seed": 42, "fold": 4, "base_accuracy": 0.8367768595041323, "weighted_accuracy": 0.8367768595041323, "improvement": 0.0, "correction_stats": {"improved": 5, "worsened": 5, "total_diff": 10}}, {"seed": 42, "fold": 5, "base_accuracy": 0.8202479338842975, "weighted_accuracy": 0.8367768595041323, "improvement": 0.016528925619834767, "correction_stats": {"improved": 13, "worsened": 5, "total_diff": 18}}, {"seed": 142, "fold": 1, "base_accuracy": 0.8391752577319588, "weighted_accuracy": 0.8412371134020619, "improvement": 0.0020618556701030855, "correction_stats": {"improved": 3, "worsened": 2, "total_diff": 5}}, {"seed": 142, "fold": 2, "base_accuracy": 0.8185567010309278, "weighted_accuracy": 0.8371134020618557, "improvement": 0.01855670103092788, "correction_stats": {"improved": 13, "worsened": 4, "total_diff": 17}}, {"seed": 142, "fold": 3, "base_accuracy": 0.8185567010309278, "weighted_accuracy": 0.8371134020618557, "improvement": 0.01855670103092788, "correction_stats": {"improved": 15, "worsened": 6, "total_diff": 21}}, {"seed": 142, "fold": 4, "base_accuracy": 0.8202479338842975, "weighted_accuracy": 0.8243801652892562, "improvement": 0.004132231404958664, "correction_stats": {"improved": 9, "worsened": 7, "total_diff": 16}}, {"seed": 142, "fold": 5, "base_accuracy": 0.8367768595041323, "weighted_accuracy": 0.8326446280991735, "improvement": -0.004132231404958775, "correction_stats": {"improved": 7, "worsened": 9, "total_diff": 16}}, {"seed": 242, "fold": 1, "base_accuracy": 0.8412371134020619, "weighted_accuracy": 0.8474226804123711, "improvement": 0.006185567010309256, "correction_stats": {"improved": 7, "worsened": 4, "total_diff": 11}}, {"seed": 242, "fold": 2, "base_accuracy": 0.8288659793814434, "weighted_accuracy": 0.8412371134020619, "improvement": 0.012371134020618513, "correction_stats": {"improved": 11, "worsened": 5, "total_diff": 16}}, {"seed": 242, "fold": 3, "base_accuracy": 0.8103092783505155, "weighted_accuracy": 0.8247422680412371, "improvement": 0.014432989690721598, "correction_stats": {"improved": 11, "worsened": 4, "total_diff": 15}}, {"seed": 242, "fold": 4, "base_accuracy": 0.8223140495867769, "weighted_accuracy": 0.8264462809917356, "improvement": 0.004132231404958664, "correction_stats": {"improved": 10, "worsened": 8, "total_diff": 18}}, {"seed": 242, "fold": 5, "base_accuracy": 0.8285123966942148, "weighted_accuracy": 0.8305785123966942, "improvement": 0.0020661157024793875, "correction_stats": {"improved": 4, "worsened": 3, "total_diff": 7}}]}, "optuna_results": {"best_params": {"n_estimators": 500, "learning_rate": 0.0339898965861571, "max_depth": 9, "subsample": 0.9844641418755025, "colsample_bytree": 0.9393789548141428, "hospital_sh六院_weight": 2.3202458110584696, "hospital_sh中山_weight": 0.47811975898825065, "hospital_bj301_weight": 2.525390098875148, "hospital_gd南海_weight": 2.418934159586107, "hospital_安医_weight": 0.34347453926712523, "consistent_weight": 0.9410952260417078, "inconsistent_weight": 2.0570113443185787, "hypertension_weight": 1.7302435951676582, "comprehensive_label_1_weight": 2.895612668887437, "comprehensive_label_2_weight": 1.2191543301106882, "comprehensive_label_3_weight": 1.320575918925005, "comprehensive_label_4_weight": 2.954454826028702, "comprehensive_label_5_weight": 2.309196165021745, "comprehensive_label_0_weight": 2.448194013175042}, "best_value": 0.8355996705574963, "n_trials": 100, "conflict_alpha": 1.0, "parameter_count": 14, "optimization_strategy": "simplified_hospital_consistency_weights"}, "xgb_params_optimized": true, "weight_structure": {"hospital_weights": {"上海六院": 2.3202458110584696, "上海中山": 0.47811975898825065, "北京301": 2.525390098875148, "广东南海": 2.418934159586107, "安医": 0.34347453926712523}, "consistency_weights": {"consistent": 0.9410952260417078, "inconsistent": 2.0570113443185787}, "hypertension_weight": 1.7302435951676582, "comprehensive_label_weights": {"0": 2.448194013175042, "1": 2.895612668887437, "2": 1.2191543301106882, "3": 1.320575918925005, "4": 2.954454826028702, "5": 2.309196165021745}, "conflict_alpha_fixed": true}}, "v2.3.5": {"version": "v2.3.5", "model_type": "XGBClassifier", "created_at": "2025-05-29 09:43:30", "description": "DeepBoruta300基线版本0527版特征-造影选择特征，综合标签训练综合标签", "train_metrics": {"accuracy": 0.9748245976062733, "precision": 0.9725067385444744, "recall": 0.9944873208379272, "specificity": 0.916256157635468, "f1": 0.9833742164077406, "auc": 0.9902401138381824}, "feature_count": 307, "sample_count": 2423, "class_distribution": {"1": 1814, "0": 609}, "error_rate": 0.025175402393726783, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.3.6": {"version": "v2.3.6", "model_type": "XGBClassifier", "created_at": "2025-05-29 11:24:19", "description": "DeepBoruta300基线版本0303版特征-综合训练集选择特征，综合标签训测集训练综合标签", "train_metrics": {"accuracy": 0.9806025588113908, "precision": 0.9804241435562806, "recall": 0.9939360529217199, "specificity": 0.9408866995073891, "f1": 0.9871338625787025, "auc": 0.9907678465067357}, "feature_count": 308, "sample_count": 2423, "class_distribution": {"1": 1814, "0": 609}, "error_rate": 0.01939744118860916, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.3.41": {"version": "v2.3.41", "model_type": "OptunaEnsembleModelV3_Simplified", "created_at": "2025-05-29 18:32:19", "description": "数据:0523训测集原始特征:0303版特征(自动时刻点0.5)选择特征:训测PureBoruta,追加cur/ci特征(原训练数据) 模型:XGB,optuna,综合标签搜索增强:无", "train_metrics": {"accuracy": 0.9645068097399917, "precision": 0.9576271186440678, "recall": 0.9966923925027563, "specificity": 0.8686371100164204, "f1": 0.9767693138843867, "auc": 0.9831062181934705}, "feature_count": 308, "sample_count": 2423, "class_distribution": {"1": 1814, "0": 609}, "error_rate": 0.03549319026000825, "cv_results": {"random_seeds": [329, 429, 529], "best_seed": 242, "best_fold": 2, "best_score": 0.8494845360824742, "fold_metrics": [{"seed": 42, "fold": 1, "base_accuracy": 0.8350515463917526, "weighted_accuracy": 0.8474226804123711, "improvement": 0.012371134020618513, "correction_stats": {"improved": 15, "worsened": 9, "total_diff": 24}}, {"seed": 42, "fold": 2, "base_accuracy": 0.8515463917525773, "weighted_accuracy": 0.845360824742268, "improvement": -0.006185567010309256, "correction_stats": {"improved": 8, "worsened": 11, "total_diff": 19}}, {"seed": 42, "fold": 3, "base_accuracy": 0.8329896907216495, "weighted_accuracy": 0.8412371134020619, "improvement": 0.008247422680412342, "correction_stats": {"improved": 11, "worsened": 7, "total_diff": 18}}, {"seed": 42, "fold": 4, "base_accuracy": 0.8347107438016529, "weighted_accuracy": 0.8409090909090909, "improvement": 0.006198347107438051, "correction_stats": {"improved": 8, "worsened": 5, "total_diff": 13}}, {"seed": 42, "fold": 5, "base_accuracy": 0.8285123966942148, "weighted_accuracy": 0.8305785123966942, "improvement": 0.0020661157024793875, "correction_stats": {"improved": 10, "worsened": 9, "total_diff": 19}}, {"seed": 142, "fold": 1, "base_accuracy": 0.8391752577319588, "weighted_accuracy": 0.8391752577319588, "improvement": 0.0, "correction_stats": {"improved": 3, "worsened": 3, "total_diff": 6}}, {"seed": 142, "fold": 2, "base_accuracy": 0.8288659793814434, "weighted_accuracy": 0.8412371134020619, "improvement": 0.012371134020618513, "correction_stats": {"improved": 13, "worsened": 7, "total_diff": 20}}, {"seed": 142, "fold": 3, "base_accuracy": 0.8206185567010309, "weighted_accuracy": 0.8288659793814434, "improvement": 0.008247422680412453, "correction_stats": {"improved": 15, "worsened": 11, "total_diff": 26}}, {"seed": 142, "fold": 4, "base_accuracy": 0.8181818181818182, "weighted_accuracy": 0.8223140495867769, "improvement": 0.004132231404958664, "correction_stats": {"improved": 8, "worsened": 6, "total_diff": 14}}, {"seed": 142, "fold": 5, "base_accuracy": 0.8305785123966942, "weighted_accuracy": 0.8471074380165289, "improvement": 0.016528925619834656, "correction_stats": {"improved": 17, "worsened": 9, "total_diff": 26}}, {"seed": 242, "fold": 1, "base_accuracy": 0.8391752577319588, "weighted_accuracy": 0.8391752577319588, "improvement": 0.0, "correction_stats": {"improved": 8, "worsened": 8, "total_diff": 16}}, {"seed": 242, "fold": 2, "base_accuracy": 0.8268041237113402, "weighted_accuracy": 0.8494845360824742, "improvement": 0.02268041237113405, "correction_stats": {"improved": 16, "worsened": 5, "total_diff": 21}}, {"seed": 242, "fold": 3, "base_accuracy": 0.8082474226804124, "weighted_accuracy": 0.8206185567010309, "improvement": 0.012371134020618513, "correction_stats": {"improved": 13, "worsened": 7, "total_diff": 20}}, {"seed": 242, "fold": 4, "base_accuracy": 0.8243801652892562, "weighted_accuracy": 0.8347107438016529, "improvement": 0.010330578512396715, "correction_stats": {"improved": 14, "worsened": 9, "total_diff": 23}}, {"seed": 242, "fold": 5, "base_accuracy": 0.8285123966942148, "weighted_accuracy": 0.8243801652892562, "improvement": -0.004132231404958664, "correction_stats": {"improved": 7, "worsened": 9, "total_diff": 16}}]}, "optuna_results": {"best_params": {"n_estimators": 1000, "learning_rate": 0.05478172301004213, "max_depth": 8, "subsample": 0.902800195074704, "colsample_bytree": 0.9423248421589838, "hospital_sh六院_weight": 0.6127095116149459, "hospital_sh中山_weight": 0.2637761573335474, "hospital_bj301_weight": 2.070478333707775, "hospital_gd南海_weight": 2.9329426756858634, "hospital_安医_weight": 0.43089390156847146, "consistent_weight": 0.5247802260780448, "inconsistent_weight": 1.5793289088391673, "hypertension_weight": 1.0268700834713245, "comprehensive_label_1_weight": 2.9728011809326054, "comprehensive_label_2_weight": 0.3228082239965122, "comprehensive_label_3_weight": 0.3474214926977246, "comprehensive_label_4_weight": 2.0414017541929113, "comprehensive_label_5_weight": 2.0183015161327997, "comprehensive_label_0_weight": 1.3608347822890885}, "best_value": 0.8368384879725086, "n_trials": 100, "conflict_alpha": 1.0, "parameter_count": 14, "optimization_strategy": "simplified_hospital_consistency_weights"}, "xgb_params_optimized": true, "weight_structure": {"hospital_weights": {"上海六院": 0.6127095116149459, "上海中山": 0.2637761573335474, "北京301": 2.070478333707775, "广东南海": 2.9329426756858634, "安医": 0.43089390156847146}, "consistency_weights": {"consistent": 0.5247802260780448, "inconsistent": 1.5793289088391673}, "hypertension_weight": 1.0268700834713245, "comprehensive_label_weights": {"0": 1.3608347822890885, "1": 2.9728011809326054, "2": 0.3228082239965122, "3": 0.3474214926977246, "4": 2.0414017541929113, "5": 2.0183015161327997}, "conflict_alpha_fixed": true}}, "v2.4.0": {"version": "v2.4.0", "model_type": "XGBClassifier", "created_at": "2025-06-11 08:27:06", "description": "DeepBoruta300基线版本0303版特征--加十院2136训练集综合标签", "train_metrics": {"accuracy": 0.9611241217798595, "precision": 0.9545171339563863, "recall": 0.993514915693904, "specificity": 0.8768971332209107, "f1": 0.9736256752462663, "auc": 0.9868559480143394}, "feature_count": 308, "sample_count": 2135, "class_distribution": {"1": 1542, "0": 593}, "error_rate": 0.03887587822014051, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.4.1": {"version": "v2.4.1", "model_type": "SVC", "created_at": "2025-06-11 10:25:33", "description": "DeepBoruta50基线版本0303版特征--仅十院197训练集综合标签", "train_metrics": {"accuracy": 0.9846938775510204, "precision": 0.9893617021276596, "recall": 0.9789473684210527, "specificity": 0.9900990099009901, "f1": 0.9841269841269842, "auc": 0.9974986972381448}, "feature_count": 64, "sample_count": 196, "class_distribution": {"0": 101, "1": 95}, "error_rate": 0.015306122448979591, "optimization": {"optimized": false, "model_params": {"C": 1.0, "kernel": "rbf", "gamma": "scale"}, "aug_params": {"use_augmentation": false}}}, "v2.4.3": {"version": "v2.4.3", "model_type": "XGBClassifier", "created_at": "2025-06-12 13:03:08", "description": "DeepBoruta300基线版本0303版特征--2668训测集综合标签", "train_metrics": {"accuracy": 0.910761154855643, "precision": 0.8939747781410555, "recall": 0.9942857142857143, "specificity": 0.6940700808625337, "f1": 0.94146581406788, "auc": 0.9826688136661182}, "feature_count": 307, "sample_count": 2667, "class_distribution": {"1": 1925, "0": 742}, "error_rate": 0.08923884514435695, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.4.11": {"version": "v2.4.11", "model_type": "SVC", "created_at": "2025-06-16 15:11:21", "description": "DeepBoruta300基线版本0303版特征--十院高血压综合标签", "train_metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1": 1.0, "auc": 1.0}, "feature_count": 64, "sample_count": 73, "class_distribution": {"0": 50, "1": 23}, "error_rate": 0.0, "optimization": {"optimized": false, "model_params": {"C": 1.0, "kernel": "rbf", "gamma": "scale"}, "aug_params": {"use_augmentation": false}}}, "v2.4.01": {"version": "v2.4.01", "model_type": "XGBClassifier", "created_at": "2025-06-16 15:25:51", "description": "DeepBoruta300基线版本0303版特征--非高血压综合标签", "train_metrics": {"accuracy": 0.9778481012658228, "precision": 0.9693548387096774, "recall": 0.9966832504145937, "specificity": 0.9449275362318841, "f1": 0.9828291087489779, "auc": 0.9969043670536207}, "feature_count": 308, "sample_count": 948, "class_distribution": {"1": 603, "0": 345}, "error_rate": 0.022151898734177215, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.4.02": {"version": "v2.4.02", "model_type": "XGBClassifier", "created_at": "2025-06-16 15:30:18", "description": "DeepBoruta300基线版本0303版特征--仅高血压综合标签", "train_metrics": {"accuracy": 0.981638418079096, "precision": 0.977818853974122, "recall": 0.9981132075471698, "specificity": 0.9325842696629213, "f1": 0.9878618113912232, "auc": 0.9926939792240831}, "feature_count": 308, "sample_count": 1416, "class_distribution": {"1": 1060, "0": 356}, "error_rate": 0.018361581920903956, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量-删去十院3类高血压": {"version": "全量-删去十院3类高血压", "model_type": "XGBClassifier", "created_at": "2025-06-18 16:18:42", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9750479846449136, "precision": 0.9715189873417721, "recall": 0.9954604409857328, "specificity": 0.9169741697416974, "f1": 0.9833440102498399, "auc": 0.9898667566442201}, "feature_count": 308, "sample_count": 2084, "class_distribution": {"1": 1542, "0": 542}, "error_rate": 0.02495201535508637, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量-控制十院3类比例": {"version": "全量-控制十院3类比例", "model_type": "XGBClassifier", "created_at": "2025-06-18 16:27:26", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9710703953712633, "precision": 0.9654522613065326, "recall": 0.996757457846952, "specificity": 0.8966165413533834, "f1": 0.9808551372048501, "auc": 0.9891289507816233}, "feature_count": 308, "sample_count": 2074, "class_distribution": {"1": 1542, "0": 532}, "error_rate": 0.02892960462873674, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "仅十院-控制十院3类比例": {"version": "仅十院-控制十院3类比例", "model_type": "XGBClassifier", "created_at": "2025-06-19 10:53:02", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9777777777777777, "precision": 0.9693877551020408, "recall": 1.0, "specificity": 0.925, "f1": 0.9844559585492229, "auc": 0.9963157894736842}, "feature_count": 308, "sample_count": 135, "class_distribution": {"1": 95, "0": 40}, "error_rate": 0.022222222222222223, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "仅十院-删除3类高血压": {"version": "仅十院-删除3类高血压", "model_type": "XGBClassifier", "created_at": "2025-06-19 10:55:39", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9724137931034482, "precision": 0.9690721649484536, "recall": 0.9894736842105263, "specificity": 0.94, "f1": 0.9791666666666666, "auc": 0.9985263157894737}, "feature_count": 308, "sample_count": 145, "class_distribution": {"1": 95, "0": 50}, "error_rate": 0.027586206896551724, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量训练仅非高血压": {"version": "全量训练仅非高血压", "model_type": "XGBClassifier", "created_at": "2025-06-24 17:39:02", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9860917941585535, "precision": 0.98559670781893, "recall": 0.9937759336099585, "specificity": 0.9704641350210971, "f1": 0.9896694214876034, "auc": 0.9970674230089115}, "feature_count": 310, "sample_count": 719, "class_distribution": {"1": 482, "0": 237}, "error_rate": 0.013908205841446454, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "全量训练仅高血压": {"version": "全量训练仅高血压", "model_type": "XGBClassifier", "created_at": "2025-06-24 17:44:37", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.97893850042123, "precision": 0.9740663900414938, "recall": 1.0, "specificity": 0.8991935483870968, "f1": 0.9868628481345244, "auc": 0.9921673709162114}, "feature_count": 315, "sample_count": 1187, "class_distribution": {"1": 939, "0": 248}, "error_rate": 0.02106149957877001, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "非高血压特征全量训练": {"version": "非高血压特征全量训练", "model_type": "XGBClassifier", "created_at": "2025-06-26 09:03:17", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9686182669789227, "precision": 0.9641283826305853, "recall": 0.993514915693904, "specificity": 0.9038785834738617, "f1": 0.9786010859150432, "auc": 0.9868843817735229}, "feature_count": 310, "sample_count": 2135, "class_distribution": {"1": 1542, "0": 593}, "error_rate": 0.03138173302107728, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "高血压特征全量训练": {"version": "高血压特征全量训练", "model_type": "XGBClassifier", "created_at": "2025-06-26 09:10:11", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9587822014051522, "precision": 0.9504337050805453, "recall": 0.9948119325551232, "specificity": 0.8650927487352446, "f1": 0.9721166032953105, "auc": 0.984744194591899}, "feature_count": 315, "sample_count": 2135, "class_distribution": {"1": 1542, "0": 593}, "error_rate": 0.041217798594847775, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "temptest": {"version": "temptest", "model_type": "XGBClassifier", "created_at": "2025-07-08 15:47:42", "description": "DeepBoruta300基线版本0303版特征综合标签", "train_metrics": {"accuracy": 0.9803023362345397, "precision": 0.9826086956521739, "recall": 0.9885598923283984, "specificity": 0.9626972740315638, "f1": 0.9855753102985575, "auc": 0.9939666442028999}, "feature_count": 502, "sample_count": 2183, "class_distribution": {"1": 1486, "0": 697}, "error_rate": 0.019697663765460376, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.5.0": {"version": "v2.5.0", "model_type": "XGBClassifier", "created_at": "2025-07-10 09:09:26", "description": "BalanceBoruta6000623版特征训测集，删除偏差综合标签", "train_metrics": {"accuracy": 0.9773805180591025, "precision": 0.9798835362625727, "recall": 0.9872, "specificity": 0.9561200923787528, "f1": 0.9835281615302869, "auc": 0.9942688221709006}, "feature_count": 601, "sample_count": 2741, "class_distribution": {"1": 1875, "0": 866}, "error_rate": 0.022619481940897482, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.5.01": {"version": "v2.5.01", "model_type": "XGBClassifier", "created_at": "2025-07-11 15:05:58", "description": "BalanceBoruta6000623版特征训测集，删除偏差综合标签", "train_metrics": {"accuracy": 0.9872309376140095, "precision": 0.9878048780487805, "recall": 0.9936, "specificity": 0.9734411085450346, "f1": 0.9906939643711778, "auc": 0.9966331023864511}, "feature_count": 601, "sample_count": 2741, "class_distribution": {"1": 1875, "0": 866}, "error_rate": 0.012769062385990515, "optimization": {"optimized": false, "model_params": {"learning_rate": 0.05, "n_estimators": 600, "max_depth": 6, "subsample": 0.8, "colsample_bytree": 0.8}, "aug_params": {"use_augmentation": false}}}, "v2.5.02": {"version": "v2.5.02", "model_type": "XGBClassifier", "created_at": "2025-07-14 10:01:46", "description": "BalanceBoruta6000623版特征训测集，删除偏差综合标签", "train_metrics": {"accuracy": 0.990149580445093, "precision": 0.989406779661017, "recall": 0.9962666666666666, "specificity": 0.976905311778291, "f1": 0.9928248737709274, "auc": 0.9972803695150115}, "feature_count": 601, "sample_count": 2741, "class_distribution": {"1": 1875, "0": 866}, "error_rate": 0.009850419554906968, "optimization": {"optimized": false, "model_params": {"n_estimators": 600, "learning_rate": 0.01, "max_depth": 6, "min_child_weight": 1, "subsample": 0.9, "colsample_bytree": 0.9, "gamma": 0.05}, "aug_params": {"use_augmentation": false}}}, "v2.5.03": {"version": "v2.5.03", "model_type": "XGBClassifier", "created_at": "2025-07-14 14:23:54", "description": "BalanceBoruta6000623版特征训测集，删除偏差综合标签", "train_metrics": {"accuracy": 0.9897847500912076, "precision": 0.9894011658717541, "recall": 0.9957333333333334, "specificity": 0.976905311778291, "f1": 0.9925571504518873, "auc": 0.9973364126250962}, "feature_count": 601, "sample_count": 2741, "class_distribution": {"1": 1875, "0": 866}, "error_rate": 0.010215249908792412, "optimization": {"optimized": false, "model_params": {"n_estimators": 600, "learning_rate": 0.01, "max_depth": 6, "min_child_weight": 1, "subsample": 0.9, "colsample_bytree": 0.9, "gamma": 0.05}, "aug_params": {"use_augmentation": false}}}}, "latest_version": "v2.5.03"}