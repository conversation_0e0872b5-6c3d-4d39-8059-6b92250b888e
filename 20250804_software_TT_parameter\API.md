
### 概述
1. 文件目录说明：
   1. main.py：主功能函数，包含一个全部算法接口的类MCGAnalyzer
   2. utils文件夹：算法背后的全部功能函数，是一个package，实现具体功能的所有代码都存放在里面；
   3. mcg文件夹：存放心磁文件

2. 运行环境:
   conda install -y numpy=1.26.4 scipy=1.10.0 pandas=2.2.3 scikit-learn=1.6.0 scikit-image=0.19.3 matplotlib=3.7.0 shapely=2.0.7 pillow=9.4.0 openpyxl=3.1.5 joblib=1.1.1

3. 输入：100*100*N 的插值后心磁数据， To, Te, Tp时刻点
   输出：TT_Stability，TT_UnipolarIndex，TT_MultipolarIndex， T_PeakType
       例：输入：  AHYK_2023_000037.pkl
                  时刻点：To, Te, Tp = 553, 662, 613
           输出：  稳定 0.26365168899065533 0.44972647815189903 正常偶极子

4. 描述：
     1. 集成单极子和多极子分析功能，对To-Te时间段内的MCG数据进行单极化/多极子的判断
     2. 计算TpT波峰时刻的磁图类型：（负连通哑铃型、正/负连通狭长形  这三种情况纳入正常现象，即正常范围是[8,9,10,11]）
            1/2：正/负单极子
            3/4：正/负单极化趋势
            5/6：正/负分化多极子
            7/8：正/负连通哑铃型
            9/10：正/负连通狭长形
            11：正常偶极子
     3. TT波段稳定性的描述：0：稳定  1：不稳定  2：不予判断  

5. 代码运行时间：0.9567 秒





