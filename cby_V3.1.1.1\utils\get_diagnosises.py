"""
Author: b<PERSON>yuchen
email: <EMAIL>

file: 
date: 2024/01/30 16:46
desc: 诊断和审查汇总
"""


from utils.utils import *


def cal_prop_disp1(mlfile):
    '''计算诊断概率-离散度-心肌缺血202310'''
    q, r, s, t, qrs, tt, noqrs = mlfile
    noqrs = int(noqrs)
    if r < 7.1:
        pr = 51
    elif r < 16.3:
        pr = 40
    else:
        pr = 80
    if t < 5.8:
        pt = 47
    else:
        pt = 60
    if qrs < 31.4:
        pqrs = 49
    else:
        pqrs = 60
    if tt < 1.6:
        ptt = 44
    else:
        ptt = 60
    if noqrs in [32, 321]:
        pno = 100
    elif noqrs == 31:
        pno = 75
    elif noqrs == 21:
        pno = 50
    elif noqrs == 2:
        pno = 33
    elif noqrs == 1:
        if q < 2.6:
            pno = 50
        else:
            pno = 61
    elif noqrs == 3:
        if s < 11.3:
            pno = 63
        else:
            pno = 0
    else:  # 0
        if qrs < 16.2:
            pno = 45
        else:
            pno = 60
    return [pr, pt, pqrs, ptt, pno]


def cal_prop_QR1(mtsall1):
    '''计算诊断概率-QR-心肌缺血202310'''
    cdfile, mdfile, mjfile, mefile, mpfile, mffile = mtsall1
    center = cdfile[6]
    qrdma, qrdav, qrdmse, qrcd, qrcar, qrcre9 = mdfile
    wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad = mjfile
    dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu = mefile
    qa, ra, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2, areaf, aread = mpfile
    epma, epav, epmse, enma, enav, enmse = mffile
    qrcre9, q = int(qrcre9), int(q)
    if q in [3, 124, 234]:
        psubdivision = 100
    elif q == 23:
        if qra < 130:
            psubdivision = 90
        else:
            psubdivision = 100
    elif q == 123:
        if qra < 175:
            psubdivision = 100
        else:
            psubdivision = 90
    elif q == 2:
        if nq2 < 25:
            psubdivision = 67
        elif nq2 < 27:
            psubdivision = 83
        else:
            psubdivision = 100
    elif q == 12:
        if nq2 < 5:
            psubdivision = 67
        elif nq2 < 28:
            psubdivision = 80 + (100 - 80) * (nq2 - 5) / (28 - 5)
        else:
            psubdivision = 100
    elif q == 1:
        if nq1 < 14:
            psubdivision = 100
        elif nq1 < 30:
            psubdivision = 80
        else:
            psubdivision = 50
    elif q == 34:
        if qra < 64:
            psubdivision = 100
        elif qra < 79:
            psubdivision = 29
        elif qra < 140:
            psubdivision = 83
        else:
            psubdivision = 100
    elif q == 1234:
        if jvq2 < 6:
            psubdivision = 64
        elif jvq2 < 10:
            psubdivision = 33
        else:
            psubdivision = 64
    elif q == 4:
        if qra < 26:
            psubdivision = 62
        elif qra < 41:
            psubdivision = 25
        else:
            psubdivision = 65
    elif q == 134:
        if jvq3 < 7:
            psubdivision = 38
        else:
            psubdivision = 62
    else:  # 14
        if jvq4 < 14:
            psubdivision = 40
        elif jvq4 < 33:
            psubdivision = 46
        else:
            psubdivision = 64
    if center < -1.07:
        pcenter = 0
    elif center < -0.1:
        pcenter = (10 - 0) * (center + 1.07) / (-0.1 + 1.07)
    elif center < 0:
        pcenter = 20 + (37 - 20) * (center + 0.1) / (0.1)
    elif center < 0.13:
        pcenter = 40 + (46 - 40) * (center - 0) / (0.13 - 0)
    elif center < 0.4:
        pcenter = 62 + (80 - 62) * (center - 0.13) / (0.4 - 0.13)
    elif center < 1.66:
        pcenter = 90 + (100 - 90) * (center - 0.4) / (1.66 - 0.4)
    else:
        pcenter = 100
    if 0 <= qa < 90:
        pqa = 62 + (80 - 62) * (qa - 0) / (90 - 0)
    elif 90 <= qa < 150:
        pqa = 81
    elif 150 <= qa < 266:
        pqa = 80 - (80 - 61) * (qa - 150) / (266 - 150)
    else:
        pqa = 45
    if 0 <= ra < 32:
        pra = 61
    elif 32 <= ra < 90:
        pra = 45
    elif 90 <= ra < 113:
        pra = 81
    else:
        pra = 60
    if 0 <= qra < 92:
        pqra = 60
    elif 92 <= qra < 176:
        pqra = 44
    elif 176 <= qra < 328:
        pqra = 60 + (80 - 60) * (qra - 176) / (328 - 176)
    else:
        pqra = 100
    if 0 <= areaf < 0.09:
        pareaf = 61
    else:
        pareaf = 47
    if 0 <= aread < 0.273:
        paread = 61
    elif 0.273 <= aread < 0.874:
        paread = 48
    else:
        paread = 60
    if 0 <= qrcar < 0.605:
        pqrcar = 49
    elif 0.605 <= qrcar < 1.016:
        pqrcar = 60
    elif 1.016 <= qrcar < 1.447:
        pqrcar = 80 + (100 - 80) * (qrcar - 1.016) / (1.447 - 1.016)
    else:
        pqrcar = 100
    if 0 <= qrcd < 100:
        pqrcd = 49
    elif 100 <= qrcd < 187.8:
        pqrcd = 60
    elif 187.8 <= qrcd < 192.4:
        pqrcd = 83 + (100 - 83) * (qrcd - 187.8) / (192.4 - 187.8)
    else:
        pqrcd = 100
    if 0 <= qrdmse < 2.1:
        pqrdmse = 62
    elif 2.1 <= qrdmse < 8.1:
        pqrdmse = 47
    elif 8.1 <= qrdmse < 14.7:
        pqrdmse = 61
    elif 14.7 <= qrdmse < 19.4:
        pqrdmse = 83 + (100 - 83) * (qrdmse - 14.7) / (19.4 - 14.7)
    else:
        pqrdmse = 100
    if 0 <= qrdma < 44.1:
        pqrdma = 0
    elif 44.1 <= qrdma < 48.8:
        pqrdma = 0 + (19 - 0) * (qrdma - 44.1) / (48.8 - 44.1)
    elif 48.8 <= qrdma < 59.7:
        pqrdma = 40
    elif 59.7 <= qrdma < 77.1:
        pqrdma = 53
    elif 77.1 <= qrdma < 96.9:
        pqrdma = 60
    elif 96.9 <= qrdma < 101.6:
        pqrdma = 80 + (100 - 80) * (qrdma - 96.9) / (101.6 - 96.9)
    else:
        pqrdma = 100
    if 0 <= qrdav < 39.2:
        pqrdav = 0
    elif 39.2 <= qrdav < 46.8:
        pqrdav = 0 + (20 - 0) * (qrdav - 39.2) / (46.8 - 39.2)
    elif 46.8 <= qrdav < 74.5:
        pqrdav = 52
    elif 74.5 <= qrdav < 75.4:
        pqrdav = 86 + (100 - 86) * (qrdav - 74.5) / (75.4 - 74.5)
    else:
        pqrdav = 100
    if 0 <= dpnav < 0.4:
        pdpnav = 60
    elif 0.4 <= dpnav < 6.6:
        pdpnav = 46
    elif 6.6 <= dpnav < 13.8:
        pdpnav = 61
    else:
        pdpnav = 83
    if 0 <= dpnmse < 1.5:
        pdpnmse = 61
    elif 1.5 <= dpnmse < 10.6:
        pdpnmse = 47
    elif 10.6 <= dpnmse < 18.3:
        pdpnmse = 80 + (100 - 80) * (dpnmse - 10.6) / (18.3 - 10.6)
    else:
        pdpnmse = 100
    if 0 <= dpnma < 5.4:
        pdpnma = 60
    elif 5.4 <= dpnma < 37.3:
        pdpnma = 47
    elif 37.3 <= dpnma < 78.8:
        pdpnma = 80 + (100 - 80) * (dpnma - 37.3) / (78.8 - 37.3)
    else:
        pdpnma = 100
    if 0 <= dppav < 0.5:
        pdppav = 46
    else:
        pdppav = 61
    if 0 <= pnnflu < 1:
        ppnnflu = 61
    elif 1 <= pnnflu < 8:
        ppnnflu = 47
    else:
        ppnnflu = 100
    if 0 <= dppmse < 2.2:
        pdppmse = 47
    else:
        pdppmse = 60
    if 1 <= pnnma < 2:
        ppnnma = 61
    elif 2 <= pnnma < 4:
        ppnnma = 47
    elif 4 <= pnnma < 5:
        ppnnma = 60
    else:
        ppnnma = 100
    if 0 <= dppma < 7.2:
        pdppma = 41
    else:
        pdppma = 65
    if 0 <= enmse < 2:
        penmse = 62
    elif 2 <= enmse < 3.8:
        penmse = 50
    elif 3.8 <= enmse < 13.2:
        penmse = 40
    else:
        penmse = 0
    if 0 <= enma < 14.87:
        penma = 60
    elif 14.87 <= enma < 37.31:
        penma = 44
    else:
        penma = 61
    if 0 <= epmse < 1.9:
        pepmse = 48
    elif 1.9 <= epmse < 11.7:
        pepmse = 61
    else:
        pepmse = 100
    if 0 <= epma < 7.56:
        pepma = 0
    elif 7.56 <= epma < 8.26:
        pepma = 0 + (18 - 0) * (epma - 7.56) / (8.26 - 7.56)
    elif 8.26 <= epma < 26.53:
        pepma = 50
    elif 26.53 <= epma < 40.44:
        pepma = 60
    elif 40.44 <= epma < 44.71:
        pepma = 80 + (100 - 80) * (epma - 40.44) / (44.71 - 40.44)
    else:
        pepma = 100
    if 0 <= epav < 6.78:
        pepav = 0
    elif 6.78 <= epav < 7.07:
        pepav = 0 + (19 - 0) * (epav - 6.78) / (7.07 - 6.78)
    elif 7.07 <= epav < 12.5:
        pepav = 50
    else:
        pepav = 83
    if 0 <= wg < 1.6:
        pwg = 17
    elif 1.6 <= wg < 4.2:
        pwg = 61
    elif 4.2 <= wg < 11.8:
        pwg = 49
    else:
        pwg = 40
    if 0 <= pm < 8:
        ppm = 47
    elif 8 <= pm < 70:
        ppm = 64 + (80 - 64) * (pm - 8) / (70 - 8)
    elif 70 <= pm < 167:
        ppm = 80 + (100 - 80) * (pm - 70) / (167 - 70)
    else:
        ppm = 100
    if -180 <= nm < -143:
        pnm = 60
    elif -143 <= nm < -33:
        pnm = 44
    else:
        pnm = 60
    if 0 <= saf < 0.031:
        psaf = 60
    elif 0.031 <= saf < 0.591:
        psaf = 47
    elif 0.591 <= saf < 0.654:
        psaf = 80 + (100 - 80) * (saf - 0.591) / (0.654 - 0.591)
    else:
        psaf = 100
    if 0 <= af < 0.036:
        paf = 61
    elif 0.036 <= af < 0.408:
        paf = 60 - (60 - 49) * (af - 0.036) / (0.408 - 0.036)
    else:
        paf = 39
    if 0 <= mse < 0.5:
        pmse = 31
    elif 0.5 <= mse < 0.7:
        pmse = 65
    elif 0.7 <= mse < 3.1:
        pmse = 60 - (60 - 50) * (mse - 0.7) / (3.1 - 0.7)
    else:
        pmse = 36
    pcenter, penmse, pqa, pra, pqra, pqrdmse, pqrdma, pdpnav, pdpnmse, pdppav, ppnnflu,  pdppmse, ppnnma, penma, pnm, pareaf, paread, pqrcar, pqrcd, pqrdav, pdpnma, pdppma, pepmse, pepma, pepav, pwg, ppm, psaf, paf, pmse, psubdivision = get_round([pcenter, penmse, pqa, pra, pqra, pqrdmse, pqrdma, pdpnav, pdpnmse, pdppav, ppnnflu,  pdppmse, ppnnma, penma, pnm, pareaf, paread, pqrcar, pqrcd, pqrdav, pdpnma, pdppma, pepmse, pepma, pepav, pwg, ppm, psaf, paf, pmse, psubdivision], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
    return [[pcenter], [penmse], [pqa, pra, pqra, pqrdmse, pqrdma, pdpnav, pdpnmse, pdppav, ppnnflu,  pdppmse, ppnnma, penma, pnm], [pareaf, paread, pqrcar, pqrcd, pqrdav, pdpnma, pdppma, pepmse, pepma, pepav, pwg, ppm, psaf, paf, pmse], [psubdivision]]


def cal_prop_RS1(mtsall2):
    '''计算诊断概率-RS-心肌缺血202310'''
    cdfile, mdfile, mjfile, mefile, mpfile, mffile = mtsall2
    center = cdfile[6]
    rsdma, rsdav, rsdmse, rscd, rscar, rscre9 = mdfile
    wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad = mjfile
    dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu = mefile
    ra, sa, nq1, nq2, nq3, nq4, rsa, q, m1, m2, a1, a2, areaf, aread = mpfile
    epma, epav, epmse, enma, enav, enmse = mffile
    rscre9, q = int(rscre9), int(q)
    if q == 124:
        psubdivision = 100
    elif q == 2:
        if rsa < 51:
            psubdivision = 90 + (100 - 90) * (rsa - 0) / (51 - 0)
        else:
            psubdivision = 100
    elif q == 234:
        if rsa < 200:
            psubdivision = 100
        else:
            psubdivision = 100 - (100 - 90) * (rsa - 200) / (270 - 200)
    elif q == 23:
        if rsa < 144:
            psubdivision = 80 + (100 - 80) * (rsa - 0) / (144 - 0)
        else:
            psubdivision = 100
    elif q == 4:
        if rsa < 53:
            psubdivision = 100
        elif rsa < 64:
            psubdivision = 100 - (100 - 80) * (rsa - 53) / (64 - 53)
        else:
            psubdivision = 50
    elif q == 12:
        if jvq1 < 3:
            psubdivision = 40
        elif jvq1 < 24:
            psubdivision = 62
        else:
            psubdivision = 80
    elif q == 34:
        if jvq3 < 15:
            psubdivision = 80
        else:
            psubdivision = 36
    elif q == 1234:
        if nq1 < 9:
            psubdivision = 64
        elif nq1 < 16:
            psubdivision = 36
        else:
            psubdivision = 64
    elif q == 14:
        if rsa < 104:
            psubdivision = 60
        elif rsa < 154:
            psubdivision = 45
        else:
            psubdivision = 60
    elif q == 3:
        if nq3 < 17:
            psubdivision = 29
        else:
            psubdivision = 100
    elif q == 123:
        if jvq2 < 9:
            psubdivision = 40
        else:
            psubdivision = 64
    elif q == 1:
        if nq1 < 29:
            psubdivision = 32
        else:
            psubdivision = 60
    else:  # 134
        if nq3 < 16:
            psubdivision = 40
        else:
            psubdivision = 16
    if center < -1.67:
        pcenter = 0
    elif center < 0:
        pcenter = (8 - 0) * (center + 1.67) / (0 + 1.67)
    elif center < 0.1:
        pcenter = 52 + (60 - 52) * (center - 0) / (0.1 - 0)
    elif center < 1.35:
        pcenter = 90 + (100 - 90) * (center - 0.1) / (1.35 - 0.1)
    else:
        pcenter = 100
    if 0 <= ra < 31:
        pra = 64
    elif 31 <= ra < 59:
        pra = 40
    elif 59 <= ra < 91:
        pra = 50
    elif 91 <= ra < 133:
        pra = 80
    elif 133 <= ra < 342:
        pra = 61
    else:
        pra = 48
    if 0 <= sa < 18:
        psa = 20 + (37 - 20) * (sa - 0) / (18 - 0)
    elif 18 <= sa < 107:
        psa = 51
    elif 107 <= sa < 137:
        psa = 80
    elif 137 <= sa < 196:
        psa = 80 - (80 - 60) * (sa - 137) / (196 - 137)
    elif 196 <= sa < 233:
        psa = 40
    elif 233 <= sa < 328:
        psa = 49
    else:
        psa = 40 - (40 - 20) * (sa - 328) / (360 - 328)
    if 0 <= rsa < 10:
        prsa = 20 + (39 - 20) * (rsa - 0) / (10 - 0)
    elif 10 <= rsa < 104:
        prsa = 60
    elif 104 <= rsa < 213:
        prsa = 48
    else:
        prsa = 39
    if aread < 0.089:
        paread = 39
    elif aread < 1.154:
        paread = 51
    else:
        paread = 40
    if 0 <= rscar < 0.056:
        prscar = 60
    elif 0.056 <= rscar < 1.143:
        prscar = 49
    else:
        prscar = 61
    if 0 <= rscd < 23:
        prscd = 62
    elif 23 <= rscd < 118:
        prscd = 51
    else:
        prscd = 39
    if 0 <= rsdma < 46:
        prsdma = 0
    elif 46 <= rsdma < 53:
        prsdma = 0 + (17 - 0) * (rsdma - 46) / (53 - 46)
    elif 53 <= rsdma < 63:
        prsdma = 20 + (40 - 20) * (rsdma - 53) / (63 - 53)
    elif 63 <= rsdma < 94:
        prsdma = 40 + (52 - 40) * (rsdma - 63) / (94 - 63)
    else:
        prsdma = 63
    if 0 <= rsdav < 40:
        prsdav = 0
    elif 40 <= rsdav < 50:
        prsdav = 0 + (18 - 0) * (rsdav - 40) / (50 - 40)
    elif 50 <= rsdav < 55:
        prsdav = 20 + (41 - 20) * (rsdav - 50) / (55 - 50)
    elif 55 <= rsdav < 77:
        prsdav = 53
    elif 77 <= rsdav < 86:
        prsdav = 85 + (100 - 85) * (rsdav - 77) / (86 - 77)
    else:
        prsdav = 100
    if 0 <= dpnma < 22.6:
        pdpnma = 51
    else:
        pdpnma = 40
    # dppav
    if 0 <= dppav < 5.2:
        pdppav = 49
    elif 5.2 <= dppav < 11.2:
        pdppav = 60
    elif 11.2 <= dppav < 13:
        pdppav = 83 + (100 - 83) * (dppav - 11.2) / (13 - 11.2)
    else:
        pdppav = 100
    if 0 <= epmse < 0.4:
        pepmse = 61
    elif 0.4 <= epmse < 5.8:
        pepmse = 48
    else:
        pepmse = 60
    if 0 <= epav < 8.64:
        pepav = 40
    elif 8.64 <= epav < 14.28:
        pepav = 51
    elif 14.28 <= epav < 17.62:
        pepav = 82 + (100 - 82) * (epav - 14.28) / (17.62 - 14.28)
    else:
        pepav = 100
    if 0 <= pm < 48:
        ppm = 52
    else:
        ppm = 40
    if 0 <= v1 < 0.62:
        pv1 = 60
    elif 0.62 <= v1 < 4.57:
        pv1 = 50
    elif 4.57 <= v1 < 5.44:
        pv1 = 40
    else:
        pv1 = 60
    if 0 <= mse < 0.55:
        pmse = 61
    elif 0.55 <= mse < 2.71:
        pmse = 49
    else:
        pmse = 60
    pcenter, pra, psa, prsa, prsdma, prsdav, paread, prscar, prscd, pdpnma, pdppav, pepmse, pepav, ppm, pv1, pmse, psubdivision = get_round([pcenter, pra, psa, prsa, prsdma, prsdav, paread, prscar, prscd, pdpnma, pdppav, pepmse, pepav, ppm, pv1, pmse, psubdivision], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
    return [[pcenter], [], [pra, psa, prsa, prsdma, prsdav], [paread, prscar, prscd, pdpnma, pdppav, pepmse, pepav, ppm, pv1, pmse], [psubdivision]]


def cal_prop_TT1(mtsall2):
    '''计算诊断概率-TT-心肌缺血202310'''
    cdfile, mdfile, mjfile, mefile, mpfile, mffile = mtsall2
    center = cdfile[6]
    ttdma, ttdav, ttdmse, ttcd, ttcar, ttcre9 = mdfile
    wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad = mjfile
    dppma, dppav, dppmse, dpnma, dpnav, dpnmse, pnpma, pnpflu, pnnma, pnnflu = mefile
    ta, tt50, tt75, nq1, nq2, nq3, nq4, tta, q, m1, m2, a1, a2, areaf, aread = mpfile
    epma, epav, epmse, enma, enav, enmse = mffile
    ttcre9, q = int(ttcre9), int(q)
    if q in [23, 124, 234]:
        psubdivision = 100
    elif q == 1234:
        psubdivision = 75
    elif q == 134:
        psubdivision = 71
    elif q == 123:
        psubdivision = 67
    elif q == 3:
        psubdivision = 63
    elif q == 34:
        if tta < 100:
            psubdivision = 80 + (100 - 80) * (tta - 0) / (100 - 0)
        else:
            psubdivision = 100
    elif q == 4:
        if nq4 < 78:
            psubdivision = 100
        elif nq4 < 115:
            psubdivision = 100 - (100 - 80) * (nq4 - 78) / (115 - 78)
        else:
            psubdivision = 68
    elif q == 14:
        if a1 < 29:
            psubdivision = 80
        else:
            psubdivision = 58
    elif q == 12:
        if nq1 < 17:
            psubdivision = 83
        elif nq1 < 95:
            psubdivision = 40
        else:
            psubdivision = 67
    elif q == 2:
        if jvq2 < 84:
            psubdivision = 100
        elif jvq2 < 91:
            psubdivision = 80
        elif jvq2 < 106:
            psubdivision = 50
        elif jvq2 < 111:
            psubdivision = 14
        else:
            psubdivision = 0
    else:  # 1
        if tta < 5.6:
            psubdivision = 19
        elif tta < 36:
            psubdivision = 40
        elif tta < 50:
            psubdivision = 51
        else:
            psubdivision = 81
    if center < -1.6:
        pcenter = 0
    elif center < 0:
        pcenter = (19 - 0) * (center + 1.6) / (0 + 1.6)
    elif center < 0.37:
        pcenter = 20 + (36 - 20) * (center - 0) / (0.37 - 0)
    elif center < 0.63:
        pcenter = 61 + (80 - 61) * (center - 0.37) / (0.63 - 0.37)
    elif center < 3:
        pcenter = 90 + (100 - 90) * (center - 0.63) / (3 - 0.63)
    else:
        pcenter = 100
    if 0 <= ta < 39:
        pta = 60 - (60 - 40) * (ta - 0) / (39 - 0)
    elif 39 <= ta < 76:
        pta = 40
    elif 76 <= ta < 127:
        pta = 40 + (51 - 40) * (ta - 76) / (127 - 76)
    elif 127 <= ta < 334:
        pta = 80
    else:
        pta = 66
    if 0 <= tt50 < 0.1:
        ptt50 = 45
    elif 0.1 <= tt50 < 1:
        ptt50 = 62
    else:
        ptt50 = 88
    if 0 <= tt75 < 0.6:
        ptt75 = 37
    elif 0.6 <= tt75 < 0.8:
        ptt75 = 47
    elif 0.8 <= tt75 < 1.4:
        ptt75 = 87 + (100 - 87) * (tt75 - 0.8) / (1.4 - 0.8)
    else:
        ptt75 = 100
    if 0 <= tta < 15:
        ptta = 39
    elif 15 <= tta < 26:
        ptta = 47
    else:
        ptta = 60
    if 0 <= areaf < 0.9:
        pareaf = 40
    elif 0.9 <= areaf < 0.162:
        pareaf = 40 + (46 - 40) * (areaf - 0.9) / (0.162 - 0.9)
    elif 0.162 <= areaf < 0.681:
        pareaf = 60
    elif 0.681 <= areaf < 0.939:
        pareaf = 80 + (100 - 80) * (areaf - 0.681) / (0.939 - 0.681)
    else:
        pareaf = 100
    if 0 <= aread < 0.032:
        paread = 0 + (19 - 0) * (aread - 0) / (0.032 - 0)
    elif 0.032 <= aread < 0.108:
        paread = 20 + (40 - 20) * (aread - 0.032) / (0.108 - 0.032)
    elif 0.108 <= aread < 0.193:
        paread = 49
    elif 0.193 <= aread < 0.963:
        paread = 60
    elif 0.963 <= aread < 1.161:
        paread = 82 + (100 - 82) * (aread - 0.963) / (1.161 - 0.963)
    else:
        paread = 100
    if 0 <= ttcar < 0.056:
        pttcar = 60
    elif 0.056 <= ttcar < 1.143:
        pttcar = 49
    else:
        pttcar = 61
    if 0 <= ttcd < 7.4:
        pttcd = 0
    elif 7.4 <= ttcd < 12:
        pttcd = 0 + (20 - 0) * (ttcd - 7.4) / (12 - 7.4)
    elif 12 <= ttcd < 18.4:
        pttcd = 39
    elif 18.4 <= ttcd < 35.8:
        pttcd = 49
    else:
        pttcd = 60
    if 0 <= ttdmse < 2:
        pttdmse = 39
    elif 2 <= ttdmse < 7.9:
        pttdmse = 55
    else:
        pttdmse = 61
    if 0 <= ttdma < 47.7:
        pttdma = 0
    elif 47.7 <= ttdma < 52.8:
        pttdma = 0 + (19 - 0) * (ttdma - 47.7) / (52.8 - 47.7)
    elif 52.8 <= ttdma < 79.5:
        pttdma = 40 + (50 - 40) * (ttdma - 52.8) / (79.5 - 52.8)
    else:
        pttdma = 61 + (80 - 61) * (ttdma - 79.5) / (- 79.5)
    if 0 <= dpnav < 7:
        pdpnav = 49
    elif 7 <= dpnav < 16.4:
        pdpnav = 62
    else:
        pdpnav = 39
    if 0 <= dpnma < 22.6:
        pdpnma = 51
    else:
        pdpnma = 40
    if 0 <= pnpflu < 2:
        ppnpflu = 47
    elif 2 <= pnpflu < 8:
        ppnpflu = 60
    elif 8 <= pnpflu < 19:
        ppnpflu = 83 + (100 - 83) * (pnpflu - 8) / (19 - 8)
    else:
        ppnpflu = 100
    if 0 <= dppav < 0.4:
        pdppav = 46
    else:
        pdppav = 61
    if 0 <= pnnflu < 4:
        ppnnflu = 49
    else:
        ppnnflu = 60
    if 0 <= dppmse < 1.3:
        pdppmse = 46
    else:
        pdppmse = 61
    if 0 <= dppma < 6.2:
        pdppma = 46
    elif 6.2 <= dppma < 42.9:
        pdppma = 61
    elif 42.9 <= dppma < 48.2:
        pdppma = 80 + (100 - 80) * (dppma - 42.9) / (48.2 - 42.9)
    else:
        pdppma = 100
    if 0 <= enmse < 0.4:
        penmse = 38
    elif 0.4 <= enmse < 4.8:
        penmse = 53
    else:
        penmse = 60
    if 0 <= enma < 7.2:
        penma = 0 + (20 - 0) * (enma - 0) / (7.2 - 0)
    elif 7.2 <= enma < 9.04:
        penma = 39
    elif 9.04 <= enma < 14.93:
        penma = 49
    else:
        penma = 60
    if 0 <= enav < 7.14:
        penav = 40
    elif 7.14 <= enav < 12.57:
        penav = 50
    else:
        penav = 60
    if 0 <= epmse < 0.3:
        pepmse = 34
    elif 0.3 <= epmse < 1.1:
        pepmse = 47
    else:
        pepmse = 61
    if 0 <= epma < 9.4:
        pepma = 40
    elif 9.4 <= epma < 12.92:
        pepma = 40 + (48 - 40) * (epma - 9.4) / (12.92 - 9.4)
    elif 12.92 <= epma < 47.37:
        pepma = 60
    elif 47.37 <= epma < 48.01:
        pepma = 80 + (100 - 80) * (epma - 47.37) / (48.01 - 47.37)
    else:
        pepma = 100
    if 0 <= epav < 6.8:
        pepav = 80 - (80 - 62) * (epav - 0) / (6.8 - 0)
    elif 6.8 <= epav < 11.39:
        pepav = 49
    else:
        pepav = 60
    if 0 <= wg < 2.4:
        pwg = 44
    else:
        pwg = 61
    if 0 <= nums < 1:
        pnums = 44
    else:
        pnums = 62
    if 0 <= pm < 8:
        ppm = 47
    elif 8 <= pm < 64:
        ppm = 60
    elif 64 <= pm < 137:
        ppm = 82 + (100 - 82) * (pm - 64) / (137 - 64)
    else:
        ppm = 100
    if -180 <= nm < 0:
        pnm = 63
    else:
        pnm = 45
    if 0 <= saf < 0.11:
        psaf = 44
    else:
        psaf = 62
    if 0 <= sad < 0.013:
        psad = 44
    elif 0.013 <= sad < 0.821:
        psad = 61
    elif 0.821 <= sad < 0.924:
        psad = 82 + (100 - 82) * (sad - 0.821) / (0.924 - 0.821)
    else:
        psad = 100
    if 0 <= r < 10:
        pr = 39
    elif 10 <= r < 23:
        pr = 46
    else:
        pr = 60
    if 0 <= v < 0.34:
        pv = 39
    elif 0.34 <= v < 0.36:
        pv = 56
    else:
        pv = 60
    if 0 <= ad < 0.026:
        pad = 19
    elif 0.026 <= ad < 0.074:
        pad = 20 + (39 - 20) * (ad - 0.026) / (0.074 - 0.026)
    elif 0.074 <= ad < 0.155:
        pad = 48
    else:
        pad = 60
    if 0 <= af < 0.074:
        paf = 20 + (39 - 20) * (af - 0) / (0.074 - 0)
    elif 0.074 <= af < 0.147:
        paf = 48
    elif 0.147 <= af < 0.389:
        paf = 60 + (80 - 60) * (af - 0.147) / (0.389 - 0.147)
    else:
        paf = 80
    if 0 <= v1 < 0.29:
        pv1 = 40
    elif 0.29 <= v1 < 0.37:
        pv1 = 44
    else:
        pv1 = 61
    if 0 <= mse < 0.52:
        pmse = 40
    elif 0.52 <= mse < 0.62:
        pmse = 50
    else:
        pmse = 60
    if 0 <= mse1 < 0.65:
        pmse1 = 45
    else:
        pmse1 = 61
    pcenter, pta, ptt75, ptta, pareaf, paread, pttcd, pv, pad, paf, pv1, pmse, ptt50, pttdmse, pttdma, pdppav, pdppmse, pdppma, penmse, penma, pepmse, pepma, pwg, pnums, ppm, pnm, psaf, psad, pr, pmse1, pttcar, pdpnav, pdpnma, ppnpflu, ppnnflu, penav, pepav, psubdivision = get_round([pcenter, pta, ptt75, ptta, pareaf, paread, pttcd, pv, pad, paf, pv1, pmse, ptt50, pttdmse, pttdma, pdppav, pdppmse, pdppma, penmse, penma, pepmse, pepma, pwg, pnums, ppm, pnm, psaf, psad, pr, pmse1, pttcar, pdpnav, pdpnma, ppnpflu, ppnnflu, penav, pepav, psubdivision], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
    return [[pcenter], [pta, ptt75, ptta, pareaf, paread, pttcd, pv, pad, paf, pv1, pmse], [ptt50, pttdmse, pttdma, pdppav, pdppmse, pdppma, penmse, penma, pepmse, pepma, pwg, pnums, ppm, pnm, psaf, psad, pr, pmse1], [pttcar, pdpnav, pdpnma, ppnpflu, ppnnflu, penav, pepav], [psubdivision]]


def combdg1(perlis):
    '''综合诊断-QR/RS/TT-心肌缺血202310'''
    cbct = comb_diag1(perlis[0])
    cbav = comb_diag1(perlis[1:3]+[perlis[4]])
    cbau = comb_diag1(perlis[1:5])
    cbal = comb_diag1(perlis)
    return [cbct, cbav, cbau, cbal]


def get_rks_prop_intp_post(mtsall):
    '''
    插值前指标诊断概率和综合诊断: disp, QR, RS, TT
    '''
    dgs0 = cal_prop_disp1(mtsall[0][0])
    cbdg0 = comb_diag1(dgs0.copy())
    dgs1 = cal_prop_QR1(mtsall[1])
    cbdgs1 = combdg1(dgs1.copy())
    dgs2 = cal_prop_RS1(mtsall[2])
    cbdgs2 = combdg1(dgs2.copy())
    dgs3 = cal_prop_TT1(mtsall[3])
    cbdgs3 = combdg1(dgs3.copy())
    return [[dgs0, [cbdg0]], dgs1+[cbdgs1], dgs2+[cbdgs2], dgs3+[cbdgs3]]
