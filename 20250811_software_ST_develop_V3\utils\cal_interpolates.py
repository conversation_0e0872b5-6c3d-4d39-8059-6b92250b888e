"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

file: 
date: 2024/01/29 13:46
desc: 获取插值后心磁
"""
import sys
import pandas as pd
import numpy as np
import requests
import pickle


def cal_interpolate0(f0, url='http://192.168.130.43:5002/getinterp'):
    '''flask获取和保存插值结果'''
    files = {'file': open(f0, 'rb')}
    response = requests.post(url, files=files)
    file_data = pickle.loads(response.content)
    return file_data


def cal_interpolate(file_p, Qp=0, Te=0):
    '''借用本地插值*'''
    data = pd.read_csv(file_p, header=None, sep='\t')
    inspect_data = [0] * data.shape[0]
    if Te:  # 区间
        t1, t2 = Qp, Te
    elif Qp:  # 时刻
        t1, t2 = Qp, Qp
    else:  # 全部
        t1, t2 = 0, data.shape[0]-1
    t1 = min(data.shape[0]-1, max(0, t1))
    t2 = min(data.shape[0]-1, max(0, t2))
    # t1, t2 = 0, 100
    data = np.array(data.iloc[t1:t2+1, 1:])
    Zi = mcg_interpolate(data)
    for x in range(len(Zi)):
        inspect_data[x+Qp] = Zi[x]
    return inspect_data


def cal_interpolate1(file_p, Qp=0, Te=0):
    data = pd.read_csv(file_p, header=None, sep='\t')
    inspect_data = [0] * data.shape[0]
    data = np.array(data.iloc[:, 1:])
    Zi = mcg_interpolate(data)
    for x in range(len(Zi)):
        inspect_data[x+Qp] = Zi[x]
        # inspect_data[x] = Zi[x]
    return inspect_data


def cubic_splines(x, ys):
    """
    三次样条插值函数
    :param x: 输入数组 X
    :param ys: 输入数组 Y
    :return z: 数据点处的二阶导数
    """
    ys = np.reshape(ys, (-1, 6))
    dy = np.diff(ys, axis=-1)
    dx = np.diff(x)
    n = x.shape[0]
    A = np.zeros((n, n))
    B = np.zeros(shape=(ys.shape[0], n))
    A[0, 0] = 1
    A[n - 1, n - 1] = 1
    for i in range(1, n - 1):
        A[i, i - 1] = dx[i - 1]
        A[i, i] = 2 * (dx[i - 1] + dx[i])
        A[i, i + 1] = dx[i]
        B[:, i] = 6 * ((dy[:, i] / dx[i]) - (dy[:, i - 1] / dx[i - 1]))
    As = np.tile(A, (B.shape[0], 1, 1))
    zs = np.linalg.solve(As, B)
    return zs


def interpolates(x, y, z, x_new):
    """
    插值函数
    :param x: 输入数组 X
    :param y: 输入数组 Y
    :param z: 数据点处的二阶导数
    :param x_new: 要求插值的点
    :return y_new : 对应的插值值
    """
    n = x.shape[0]
    h = np.diff(x)
    idx = np.searchsorted(x, x_new) - 1
    idx = np.clip(idx, 0, n-2)
    A = (x[idx + 1] - x_new) / h[idx]
    B = 1 - A
    C = (1 / 6) * (A ** 3 - A) * h[idx] ** 2
    D = (1 / 6) * (B ** 3 - B) * h[idx] ** 2
    y_new = A * y[:, idx] + B * y[:, idx + 1] + C * z[:, idx] + D * z[:, idx + 1]
    return y_new


def mcg_interpolate(d_ori):
    X = np.array([0, 4, 8, 12, 16, 20])
    Y = np.array([0, 4, 8, 12, 16, 20])
    values = np.linspace(0, 20, 100 + 1)[1:]
    Xi = np.tile(values, (100, 1))
    Yi = Xi.T
    Bs = d_ori.reshape((-1, 6, 6))
    Zi = np.zeros((Bs.shape[0], Xi.shape[0], Xi.shape[1]))
    z_x = np.array([cubic_splines(X, Bs[:, i, :]) for i in range(Bs.shape[1])])
    z_y = np.zeros((Bs.shape[0], 6, 100))
    for i in range(Xi.shape[0]):
        for k in range(Bs.shape[1]):
            z_y[:, k, :] = interpolates(X, Bs[:, k, :], z_x[k], Xi[i, :])
        for j in range(Xi.shape[1]):
            Zi[:, i, j] = interpolates(Y, z_y[:, :, j], cubic_splines(Y, z_y[:, :, j]), Yi[i, j])
    return Zi


def mcg_interpolate1(d_ori):
    '''焦博-电流密度图'''
    X = np.array([0, 4, 8, 12, 16, 20])
    Y = np.array([0, 4, 8, 12, 16, 20])
    values = np.linspace(0, 20, 100 + 1)  # [1:]
    Xi = np.tile(values, (101, 1))
    Yi = Xi.T
    Bs = d_ori.reshape((-1, 6, 6))

    Zi = np.zeros((Bs.shape[0], Xi.shape[0], Xi.shape[1]))
    z_x = np.array([cubic_splines(X, Bs[:, i, :]) for i in range(Bs.shape[1])])
    z_y = np.zeros((Bs.shape[0], 6, 101))

    for i in range(Xi.shape[0]):
        for k in range(Bs.shape[1]):
            z_y[:, k, :] = interpolates(X, Bs[:, k, :], z_x[k], Xi[i, :])

        for j in range(Xi.shape[1]):
            Zi[:, i, j] = interpolates(Y, z_y[:, :, j], cubic_splines(Y, z_y[:, :, j]), Yi[i, j])

    return Xi, Yi, Zi


if __name__ == '__main__':
    mcgfile = sys.argv[1]
    matrix = cal_interpolate(mcgfile)
    print(matrix)
    print(len(matrix))
