"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: losses
date: 2024/11/20 下午4:18
desc: 
"""

from typing import <PERSON>ple

import numpy as np


class Focal_Binary_Loss:
    def __init__(self, gamma_indct=2.0, alpha=0.25):
        self.gamma_indct = gamma_indct
        self.alpha = alpha

    def robust_pow(self, num_base, num_pow):
        return np.sign(num_base) * (np.abs(num_base)) ** (num_pow)

    def focal_binary_object(self, pred, label):
        gamma_indct = self.gamma_indct
        sigmoid_pred = 1.0 / (1.0 + np.exp(-pred))
        g1 = sigmoid_pred * (1 - sigmoid_pred)
        g2 = label + ((-1) ** label) * sigmoid_pred
        g3 = sigmoid_pred + label - 1
        g4 = 1 - label - ((-1) ** label) * sigmoid_pred
        g5 = label + ((-1) ** label) * sigmoid_pred

        grad = (self.alpha * (
                gamma_indct * g3 * self.robust_pow(g2, gamma_indct) * np.log(g4 + 1e-9) +
                ((-1) ** label) * self.robust_pow(g5, (gamma_indct + 1))
        ))

        hess_1 = self.robust_pow(g2, gamma_indct) + gamma_indct * ((-1) ** label) * g3 * self.robust_pow(g2, (
                    gamma_indct - 1))
        hess_2 = ((-1) ** label) * g3 * self.robust_pow(g2, gamma_indct) / g4

        hess = ((hess_1 * np.log(g4 + 1e-9) - hess_2) * gamma_indct + (gamma_indct + 1) * self.robust_pow(g5,
                                                                                                          gamma_indct)) * g1

        return grad, hess


class CRF_Binary_Loss:
    """
    Correntropy Loss实现
    基于信息理论的Maximum Correntropy Criterion (MCC)
    对异常值和噪声更加鲁棒
    """

    def __init__(self, sigma: float = 1.0, epsilon: float = 1e-6):
        """
        参数:
        sigma: float, 高斯核的带宽参数，控制对异常值的敏感程度
        epsilon: float, 数值稳定性参数
        """
        self.sigma = sigma
        self.epsilon = epsilon
        self._sigma_square_2 = 2.0 * (sigma ** 2)

    def crf_binary_object(self, pred: np.ndarray, label: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算Correntropy Loss的梯度和二阶导

        pred: 模型原始输出（未经过sigmoid）
        label: 真实标签 (0 或 1)
        """

        # 数值稳定的sigmoid
        def stable_sigmoid(x: np.ndarray) -> np.ndarray:
            mask = x >= 0
            result = np.zeros_like(x, dtype=np.float64)
            # x >= 0的情况
            exp_nx = np.exp(-x[mask])
            result[mask] = 1 / (1 + exp_nx)
            # x < 0的情况
            exp_x = np.exp(x[~mask])
            result[~mask] = exp_x / (1 + exp_x)
            return result

        # 计算sigmoid预测值
        sigmoid_pred = stable_sigmoid(pred)
        sigmoid_pred = np.clip(sigmoid_pred, self.epsilon, 1 - self.epsilon)

        # 计算预测误差
        error = sigmoid_pred - label

        # 计算高斯核
        gaussian_kernel = np.exp(-error ** 2 / self._sigma_square_2)

        # 计算一阶导数
        grad = 2.0 * error * gaussian_kernel * sigmoid_pred * (1 - sigmoid_pred)

        # 计算二阶导数
        # d2K/de2 * (de/dp)^2 + dK/de * d2e/dp2
        hess_1 = (-2.0 / self._sigma_square_2) * gaussian_kernel
        hess_2 = sigmoid_pred * (1 - sigmoid_pred)
        hess = (
                2.0 * (hess_1 * error ** 2 + gaussian_kernel) * hess_2 ** 2 +
                2.0 * error * gaussian_kernel * hess_2 * (1 - 2 * sigmoid_pred)
        )

        return grad, np.clip(hess, self.epsilon, 100)


class GHM_Binary_Loss:
    """
    Gradient Harmonizing Mechanism Loss
    Reference: https://arxiv.org/abs/1811.05181
    """

    def __init__(self, bins=30, momentum=0.75):
        self.bins = bins
        self.momentum = momentum
        self.edges = [float(x) / bins for x in range(bins + 1)]
        self.edges[-1] += 1e-6  # 防止最后一个bin为空
        self.acc_sum = None  # 累积梯度统计

    def calc_weights(self, g: np.ndarray) -> np.ndarray:
        # 计算梯度模长
        g_abs = np.abs(g)
        # 统计梯度直方图
        hist, _ = np.histogram(g_abs, bins=self.edges)
        # 计算每个bin的密度
        width = self.edges[1] - self.edges[0]
        hist = hist / width  # 密度

        # 使用动量更新累积和
        if self.acc_sum is None:
            self.acc_sum = hist
        else:
            self.acc_sum = self.momentum * self.acc_sum + (1 - self.momentum) * hist

        # 计算权重
        weights = np.zeros_like(g)
        for i in range(len(self.edges) - 1):
            mask = (g_abs >= self.edges[i]) & (g_abs < self.edges[i + 1])
            if self.acc_sum[i] != 0:
                weights[mask] = 1.0 / (self.acc_sum[i] + 1e-9)

        # 标准化权重
        weights = weights / np.mean(weights)
        return weights

    def ghm_binary_object(self, pred: np.ndarray, label: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算GHM loss的梯度和二阶导"""
        # sigmoid
        eps = 1e-12
        sigmoid_pred = 1.0 / (1.0 + np.exp(-pred))
        sigmoid_pred = np.clip(sigmoid_pred, eps, 1 - eps)

        # 计算原始梯度
        grad = sigmoid_pred - label

        # 计算GHM权重
        weights = self.calc_weights(grad)

        # 应用权重到梯度和二阶导
        grad = weights * grad
        hess = weights * sigmoid_pred * (1 - sigmoid_pred)

        return grad, hess


class OHEM_Binary_Loss:
    """
    Online Hard Example Mining Loss
    只保留最难的top-k个样本
    """

    def __init__(self, rate=0.7):
        self.rate = rate  # 保留比例

    def ohem_binary_object(self, pred: np.ndarray, label: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        # sigmoid
        eps = 1e-12
        sigmoid_pred = 1.0 / (1.0 + np.exp(-pred))
        sigmoid_pred = np.clip(sigmoid_pred, eps, 1 - eps)

        # 计算损失
        loss = -label * np.log(sigmoid_pred) - (1 - label) * np.log(1 - sigmoid_pred)

        # 选择最难的样本
        num_keep = int(len(loss) * self.rate)
        indices = np.argsort(loss)[-num_keep:][::-1]  # 获取前k个最大值的索引，逆序排列
        values = loss[indices]  # 获取对应的值
        # values, indices = loss.topk(num_keep)

        # 计算梯度和二阶导
        grad = np.zeros_like(pred)
        hess = np.zeros_like(pred)

        grad[indices] = sigmoid_pred[indices] - label[indices]
        hess[indices] = sigmoid_pred[indices] * (1 - sigmoid_pred[indices])

        return grad, hess
