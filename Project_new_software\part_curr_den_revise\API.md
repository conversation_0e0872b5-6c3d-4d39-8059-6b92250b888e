
## 电流源轨迹可视化

### 概述

本程序主要是根据输出的磁场数据 1*36的向量输出对应的电流密度图

### 修改说明
在原有的part_curr_den_revise基础上:
(1)将电流的向量全部修改为黑色。然后背景根据电流密度的强度进行着色，红色代表强度最大，黄色代表强度最小
(2)将电流密度图变成正方向，并且增大分辨率
### 主函数输入及输出

直接调用 main.py文件中的函数 main(mcg_data)即可
输入：mcg_data
其中 mcg_data是一个 1*36的向量，代表某一时刻36通道的磁场值


输出：目录下的cur_den.png, 代表根据输入的心磁数据生成的电流密度图

运行时间为0.586s(包括文件的读取和结果的生成)


### 例子

```
例1
    mcg_data = np.array([0.592578, 1.393776, 2.199441, 3.099023, 1.816884, 0.801566,
                         0.997516, 2.898056, 5.087041, 4.513909, 2.251532, 1.014558,
                         0.907813, 2.807073, 3.351294, -2.161837, -1.087883, 0.176905,
                         0.188084, -0.340515, -2.789730, -7.080690,	-5.155373, -1.606127,
                         0.191104, -0.859270, -3.062833, -4.420899,	-3.380085, -1.273779,
                         0.197325, -0.462841, -1.110405, -1.428688,	-1.260064, -0.569730])
    main(mcg_data)

