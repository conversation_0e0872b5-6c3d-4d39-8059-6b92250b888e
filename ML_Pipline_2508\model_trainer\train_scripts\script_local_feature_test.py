"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_local_acc_test
date: 2024/11/13 下午1:05
desc: 局部特征的建模精度测试 脚本
"""


import pickle

import matplotlib.pyplot as plt
import pandas as pd

from model_trainer.train_scripts.train_model_on_folds import train_model_on_stable_folds, func_feature_test

# plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号问题



if __name__ == '__main__':

    # 对给定list特征的局部精度测试: 电流源特征测试 --------------------------------------------
    features_pkl = "./files/saved_features/features_all_cur_V0905_2534.pkl"
    with open(features_pkl, "rb") as f:
        df_comb_feature = pickle.load(f)
    cur_features_list = df_comb_feature.columns[df_comb_feature.columns.str.startswith(('xh_', 'J_'))]
    xh_features_list = df_comb_feature.columns[df_comb_feature.columns.str.startswith(('xh_'))]
    j_features_list = df_comb_feature.columns[df_comb_feature.columns.str.startswith(('J_'))]
    params = {
        "feature_pkl": features_pkl,
        "id_pkl": "./files/data/data_index/data_V0826_cons/S42-F5.pkl",
        "selected_feature_pkl": features_pkl,
        "save_res_path": "./files/saved_models/cur_S42F5.pkl",
        'selected_feature_list': cur_features_list,
    }
    results, avg0 = train_model_on_stable_folds(**params, save_flag=False, mode='valid_result_threshold')
    print(avg0)

    # 对给定list特征的局部精度测试: w_list对比测试 --------------------------------------------
    features_pkl = "./files/saved_features/features_all_cur_V0905_2534.pkl"
    with open(features_pkl, "rb") as f:
        df_comb_feature = pickle.load(f)
    avgs = []
    for weight in ['0.9', '0.8', '0.7', '0.6', '0.5', '0.4']:
        features_list = df_comb_feature.columns[df_comb_feature.columns.str.contains(weight)]
        params = {
            "feature_pkl": features_pkl,
            "id_pkl": "./files/data/data_index/data_V0826_cons/S42-F5.pkl",
            "selected_feature_pkl": features_pkl,
            "save_res_path": "./files/saved_models/cur_S42F5.pkl",
            'selected_feature_list': features_list,
        }
        results, avg0 = train_model_on_stable_folds(**params, save_flag=False, mode='valid_result_threshold')
        avgs.append(avg0)
        print(avg0)

    # 绘制柱状图
    plt.figure(figsize=(10, 6))
    plt.bar(['0.9', '0.8', '0.7', '0.6', '0.5', '0.4'], pd.DataFrame(avgs).iloc[:,1])
    plt.xlabel('Weight')
    plt.ylabel('Average')
    plt.ylim(0.8,0.9)
    plt.title('Average by Weight')
    plt.show()



    # 根据特征注册信息选择特定范围的特征局部精度测试 ------------------------------------------

    save_res_ts, avg_ts = func_feature_test('get_ts_features')
    save_res_ti, avg_ti = func_feature_test('get_ti_features')
    save_res_stat, avg_stat = func_feature_test('get_stat_features')