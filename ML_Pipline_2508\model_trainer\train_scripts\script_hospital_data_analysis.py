"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_hospital_data_analysis
date: 2024/12/9 上午11:11
desc: 
"""

import json
from collections import Counter

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import xgboost as xgb
from imblearn.over_sampling import SMOTE
from scipy.stats import gaussian_kde
from sklearn.decomposition import PCA
from sklearn.metrics import roc_auc_score, precision_score, recall_score, accuracy_score
from sklearn.preprocessing import StandardScaler

from model_trainer.models import (load_and_prepare_data, process_safe_features)

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号问题
class HospitalAnalysis:
    def __init__(self, excel_path, feature_pkl, id_pkl, selected_feature_pkl=None, selected_feature_list=None,**kwargs):
        """初始化设置"""
        self.excel_path = excel_path
        self.feature_pkl = feature_pkl
        self.id_pkl = id_pkl
        self.selected_feature_pkl = selected_feature_pkl
        self.selected_feature_list = selected_feature_list
        self.hospitals = None
        self.hospital_models = {}
        self.hospital_results = {}
        self.excluded_hospitals = []# ['上海中山']  # 排除的医院

    def clean_data(self, data):
        """清理数据中的无效值"""
        # 替换无穷大值
        data = data.replace([np.inf, -np.inf], np.nan)
        # 对于数值列，使用中位数填充缺失值
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        data[numeric_columns] = data[numeric_columns].fillna(data[numeric_columns].median())
        return data

    def load_and_prepare_hospital_data(self, fold, enable_sampling=True,
                                       sampling_hospitals=['北京301','上海六院'],
                                       sampling_ratios=[1.4,1.5]):
        """
        加载并准备医院数据，对指定医院的训练数据进行过采样平衡

        Args:
            fold: 折数
            enable_sampling: 是否启用过采样
            sampling_hospitals: 参与过采样的医院列表，None表示所有医院
            sampling_ratio: 过采样程度，1.0表示完全平衡，小于1表示部分平衡
        """
        # 验证参数
        if sampling_hospitals is not None and sampling_ratios is not None:
            if len(sampling_hospitals) != len(sampling_ratios):
                raise ValueError("sampling_hospitals 和 sampling_ratios 的长度必须相同")
            # 创建医院->比例的映射
            hospital_ratio_map = dict(zip(sampling_hospitals, sampling_ratios))
        else:
            hospital_ratio_map = {}
        # 加载基础数据集
        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl=self.id_pkl,
            features_pkl=self.feature_pkl,
            selected_features_pkl=self.selected_feature_pkl,
            selected_feature_list=self.selected_feature_list,
            fold=fold
        )

        # 读取医院信息
        sheet_data = pd.read_excel(self.excel_path, sheet_name='总表')
        hospital_info = sheet_data[['心磁号', '造影结果', '人工', '医院']]

        # 排除指定医院
        hospital_info = hospital_info[~hospital_info['医院'].isin(self.excluded_hospitals)]

        # 获取唯一医院列表
        self.hospitals = hospital_info['医院'].unique()

        def get_sampling_ratio(hospital):
            """获取指定医院的采样比例"""
            if sampling_hospitals is None:
                return 1.0
            elif hospital in hospital_ratio_map:
                return hospital_ratio_map[hospital]
            return None  # 该医院不参与采样
        def apply_smote(X, y, hospital):
            """对数据应用SMOTE过采样"""
            # 如果不启用过采样或该医院不在过采样列表中，直接返回原始数据
            sampling_ratio = get_sampling_ratio(hospital)
            if not enable_sampling or sampling_ratio is None:
                return X, y

            print(f"医院 {hospital} 的采样比例: {sampling_ratio}")

            print(f"数据形状: {X.shape}")
            print(f"标签分布: {Counter(y)}")

            if len(np.unique(y)) < 2:
                print("只有一个类别，返回原始数据")
                return X, y

            class_counts = Counter(y)
            min_samples = min(class_counts.values())
            max_samples = max(class_counts.values())

            # 计算目标样本数：在最小样本数和最大样本数之间，根据sampling_ratio确定
            # sampling_ratio=1.0时会平衡到最大类的数量，小于1时会部分平衡
            target_samples = int(min_samples + (max_samples - min_samples) * sampling_ratio)

            # 确定每个类别的目标样本数
            sampling_strategy = {}
            for label, count in class_counts.items():
                if count < max_samples:
                    # 对于少数类，根据sampling_ratio增加样本
                    sampling_strategy[label] = target_samples
                else:
                    # 对于多数类，保持原样
                    sampling_strategy[label] = count

            if min_samples < 5:
                print(f"少数类样本数量过少: {min_samples}")
                minority_class = min(class_counts, key=class_counts.get)
                minority_indices = np.where(y == minority_class)[0]

                # 计算需要采样的数量
                n_samples_needed = target_samples - min_samples
                print(f"需要生成的样本数量: {n_samples_needed}")

                try:
                    additional_indices = np.random.choice(
                        minority_indices,
                        size=n_samples_needed,
                        replace=True
                    )

                    if isinstance(X, pd.DataFrame):
                        additional_samples = X.loc[X.index[additional_indices]]
                        X_resampled = pd.concat([X, additional_samples], axis=0, ignore_index=True)
                        y_resampled = pd.Series(np.hstack([y, y[additional_indices]]))
                    else:
                        X_resampled = np.vstack([X, X[additional_indices]])
                        y_resampled = np.hstack([y, y[additional_indices]])

                    print(f"重采样后的形状: {X_resampled.shape}")
                    print(f"重采样后的标签分布: {Counter(y_resampled)}")

                    return X_resampled, y_resampled

                except Exception as e:
                    print(f"在处理极不平衡数据时发生错误: {e}")
                    return X, y

            try:
                print(f"采样策略: {sampling_strategy}")
                smote = SMOTE(
                    random_state=42,
                    k_neighbors=min(5, min_samples - 1),
                    sampling_strategy=sampling_strategy
                )

                if isinstance(X, pd.DataFrame):
                    X_numpy = X.to_numpy()
                    X_resampled, y_resampled = smote.fit_resample(X_numpy, y)
                    X_resampled = pd.DataFrame(X_resampled, columns=X.columns)
                else:
                    X_resampled, y_resampled = smote.fit_resample(X, y)

                print(f"重采样后的形状: {X_resampled.shape}")
                print(f"重采样后的标签分布: {Counter(y_resampled)}")

                return X_resampled, y_resampled

            except ValueError as e:
                print(f"SMOTE失败，使用原始数据: {e}")
                return X, y

        hospital_data = {}
        for hospital in self.hospitals:
            # 获取该医院的数据
            hospital_mcg = hospital_info[
                (hospital_info['医院'] == hospital)
                & (hospital_info['造影结果'] == hospital_info['人工'])
                ]['心磁号']

            # 为每个数据集筛选该医院的数据
            hospital_train = train_data[train_data['mcg_file'].isin(hospital_mcg)]
            hospital_test = test_data[test_data['mcg_file'].isin(hospital_mcg)]
            hospital_valid = valid_data[valid_data['mcg_file'].isin(hospital_mcg)]
            hospital_train_valid = train_valid_data[train_valid_data['mcg_file'].isin(hospital_mcg)]

            # 检查数据极度不平衡的情况
            if len(hospital_train) < 5 or Counter(hospital_train['label']).most_common()[-1][1] < 2:
                print(f"警告：医院 {hospital} 的数据极度不平衡：{Counter(hospital_train['label'])}")
                continue

            # 更新标签
            def update_labels(data, hospital_info):
                mcg_files = data['mcg_file']
                new_labels = []
                for mcg in mcg_files:
                    info = hospital_info[hospital_info['心磁号'] == mcg]
                    if len(info) > 0:
                        label = info['造影结果'].iloc[0]
                        new_labels.append(label)
                    else:
                        new_labels.append(data[data['mcg_file'] == mcg]['label'].iloc[0])
                return new_labels

            # 更新标签
            hospital_train['label'] = update_labels(hospital_train, hospital_info)
            hospital_test['label'] = update_labels(hospital_test, hospital_info)
            hospital_valid['label'] = update_labels(hospital_valid, hospital_info)
            hospital_train_valid['label'] = update_labels(hospital_train_valid, hospital_info)

            # 获取安全特征并清理数据
            safe_train = self.clean_data(process_safe_features(hospital_train))
            safe_test = self.clean_data(process_safe_features(hospital_test))
            safe_valid = self.clean_data(process_safe_features(hospital_valid))
            safe_train_valid = self.clean_data(process_safe_features(hospital_train_valid))

            # 对训练集和训练验证集进行过采样
            X_train_resampled, y_train_resampled = apply_smote(safe_train, hospital_train['label'], hospital)
            X_train_valid_resampled, y_train_valid_resampled = apply_smote(safe_train_valid,
                                                                           hospital_train_valid['label'],
                                                                           hospital)

            # 保存原始数据的mcg_file信息
            train_mcg_files = hospital_train['mcg_file'].values
            train_valid_mcg_files = hospital_train_valid['mcg_file'].values

            # 为过采样的数据添加mcg_file信息
            if len(X_train_resampled) > len(train_mcg_files):
                train_mcg_files_resampled = np.concatenate([
                    train_mcg_files,
                    train_mcg_files[np.random.choice(len(train_mcg_files),
                                                     len(X_train_resampled) - len(train_mcg_files))]
                ])
            else:
                train_mcg_files_resampled = train_mcg_files

            if len(X_train_valid_resampled) > len(train_valid_mcg_files):
                train_valid_mcg_files_resampled = np.concatenate([
                    train_valid_mcg_files,
                    train_valid_mcg_files[np.random.choice(len(train_valid_mcg_files),
                                                           len(X_train_valid_resampled) - len(train_valid_mcg_files))]
                ])
            else:
                train_valid_mcg_files_resampled = train_valid_mcg_files

            hospital_data[hospital] = {
                'train': (X_train_resampled, y_train_resampled, train_mcg_files_resampled),
                'test': (safe_test, hospital_test['label'], hospital_test['mcg_file'].values),
                'valid': (safe_valid, hospital_valid['label'], hospital_valid['mcg_file'].values),
                'train_valid': (X_train_valid_resampled, y_train_valid_resampled, train_valid_mcg_files_resampled)
            }

            # 打印每个医院的样本分布情况
            print(f"\n医院 {hospital} 的样本分布:")
            print(f"训练集 - 原始: {Counter(hospital_train['label'])}")
            print(f"训练集 - 平衡后: {Counter(y_train_resampled)}")
            print(f"测试集: {Counter(hospital_test['label'])}")
            print(f"验证集: {Counter(hospital_valid['label'])}")

        return hospital_data
    def plot_feature_importance_comparison(self, feature_importance,top_n=10):
        """Plot feature importance comparison across hospitals and common features

        Args:
            feature_importance: Dictionary with hospital names as keys and their feature importance DataFrames as values

        Returns:
            matplotlib.figure.Figure: The generated plot
        """
        # 计算布局：增加一个子图用于显示共同特征
        num_hospitals = len(self.hospitals)
        total_plots = num_hospitals + 1  # +1 for common features
        cols = min(3, total_plots)
        rows = (total_plots + cols - 1) // cols

        plt.figure(figsize=(15, 4 * rows))

        # 先处理每个医院的单独特征重要性
        for i, hospital in enumerate(self.hospitals, 1):
            plt.subplot(rows, cols, i)
            importance = feature_importance[hospital]

            importance['feature'] = importance['feature'].apply(
                lambda x: x[:30] + '...' if len(x) > 30 else x
            )

            sns.barplot(
                data=importance[:top_n],
                x='importance',
                y='feature',
                palette='Blues_r'
            )

            plt.title(f'Top 10 Features - {hospital}')
            plt.xlabel('Importance Score')
            plt.ylabel('Features')

        # 计算共同特征及其平均重要性
        all_features = {}
        for hospital, imp_df in feature_importance.items():
            for _, row in imp_df.iterrows():
                feature = row['feature']
                if feature not in all_features:
                    all_features[feature] = {'count': 0, 'total_importance': 0.0}
                all_features[feature]['count'] += 1
                all_features[feature]['total_importance'] += row['importance']

        # 找出在所有医院中都出现的特征
        common_features = {
            feature: data for feature, data in all_features.items()
            if data['count'] == len(self.hospitals)
        }

        # 计算平均重要性
        common_features_df = pd.DataFrame([
            {
                'feature': feature,
                'importance': data['total_importance'] / len(self.hospitals)
            }
            for feature, data in common_features.items()
        ])

        # 按重要性排序并取前10
        common_features_df = common_features_df.sort_values('importance', ascending=False).head(10)

        # 绘制共同特征图
        plt.subplot(rows, cols, num_hospitals + 1)
        sns.barplot(
            data=common_features_df,
            x='importance',
            y='feature',
            palette='Reds_r'  # 使用不同的配色以区分
        )
        plt.title('Top 10 Common Features (Average Importance)')
        plt.xlabel('Average Importance Score')
        plt.ylabel('Features')

        plt.tight_layout()
        plt.show()
        return plt
    def find_optimal_threshold(self, y_true, y_prob):
        """找到最优阈值"""
        thresholds = np.arange(0.1, 0.9, 0.05)
        best_threshold = 0.5
        best_f1 = 0

        for threshold in thresholds:
            y_pred = (y_prob >= threshold).astype(int)
            sen = recall_score(y_true, y_pred)
            spe = precision_score(y_true, y_pred)
            f1 = 2 * (sen * spe) / (sen + spe) if (sen + spe) > 0 else 0

            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

        return best_threshold

    def evaluate_metrics(self, y_true, y_pred, y_prob):
        """评估所有指标"""
        optimal_threshold = self.find_optimal_threshold(y_true, y_prob)
        y_pred_optimal = (y_prob >= optimal_threshold).astype(int)

        return {
            'auc': roc_auc_score(y_true, y_prob),
            'accuracy': accuracy_score(y_true, y_pred),
            'sensitivity': recall_score(y_true, y_pred),
            'specificity': precision_score(y_true, y_pred),
            'optimal_threshold': optimal_threshold,
            'sensitivity_optimal': recall_score(y_true, y_pred_optimal),
            'specificity_optimal': precision_score(y_true, y_pred_optimal),
            'accuracy_optimal': accuracy_score(y_true, y_pred_optimal)
        }

    def train_and_evaluate(self, fold,n_features=50):
        """训练和评估模型"""
        hospital_data = self.load_and_prepare_hospital_data(fold)
        results = {}
        feature_importance = {}

        for hospital in self.hospitals:
            print(f"\nProcessing hospital: {hospital}")

            data = hospital_data[hospital]
            X_train, y_train,_ = data['train']
            X_test, y_test,_ = data['test']
            X_valid, y_valid,_ = data['valid']
            X_train_valid, y_train_valid,_ = data['train_valid']

            # 训练模型
            model = xgb.XGBClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=5
            )

            eval_set = [(X_valid, y_valid)]
            model.fit(X_train, y_train, eval_set=eval_set, verbose=False)

            # 评估
            y_pred = model.predict(X_test)
            y_prob = model.predict_proba(X_test)[:, 1]

            # 计算所有指标
            results[hospital] = self.evaluate_metrics(y_test, y_pred, y_prob)

            # 特征重要性
            importance = pd.DataFrame({
                'feature': X_train.columns,
                'importance': model.feature_importances_
            })
            feature_importance[hospital] = importance.nlargest(n_features, 'importance')

            self.hospital_models[hospital] = model

        return results, feature_importance

    def visualize_dimensions(self, hospital_data,s = 5,hospitals_to_show=None):
        """数据降维可视化"""
        # 创建带有边缘分布的布局
        fig = plt.figure(figsize=(15, 15))
        gs = fig.add_gridspec(3, 3)
        ax_main = fig.add_subplot(gs[1:, :-1])
        ax_right = fig.add_subplot(gs[1:, -1])
        ax_top = fig.add_subplot(gs[0, :-1])

        all_features = []
        all_labels = []
        hospital_sizes = []

        if hospitals_to_show is not None:
            pass#self.hospitals = hospitals_to_show
        else:
            hospitals_to_show = self.hospitals
        for hospital in hospitals_to_show:
            X_train_valid, y_train_valid,_ = hospital_data[hospital]['train_valid']
            # 确保数据已经清理
            X_train_valid = self.clean_data(X_train_valid)
            all_features.append(X_train_valid)
            all_labels.extend(y_train_valid)
            hospital_sizes.append(len(X_train_valid))

        all_features = pd.concat(all_features)

        # 标准化和PCA
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(all_features)

        pca = PCA(n_components=2)
        features_2d = pca.fit_transform(features_scaled)

        # 创建DataFrame以便更容易处理
        df_plot = pd.DataFrame(features_2d, columns=['PC1', 'PC2'])
        df_plot['Label'] = all_labels
        df_plot['Hospital'] = np.concatenate([[hospital] * size
                                              for hospital, size in zip(hospitals_to_show, hospital_sizes)])

        # 绘制主散点图
        colors = ['red', 'blue', 'green', 'purple', 'orange']
        colors = ['#FF5733', '#337DFF', '#33FF57', '#B033FF', '#FFD433', '#FF3384', '#33FFE6']
        ax_main.clear()
        ax_top.clear()
        ax_right.clear()
        start_idx = 0

        for i, (hospital, size) in enumerate(zip(hospitals_to_show, hospital_sizes)):
            end_idx = start_idx + size
            hospital_points = features_2d[start_idx:end_idx]
            hospital_labels = all_labels[start_idx:end_idx]

            pos_mask = np.array(hospital_labels) == 1
            neg_mask = np.array(hospital_labels) == 0

            # 主散点图
            ax_main.scatter(hospital_points[pos_mask, 0], hospital_points[pos_mask, 1],
                            c=colors[i], marker='o', s=s, label=f'{hospital} (+)',
                            alpha=0.7, edgecolors='white', linewidth=0.5)
            ax_main.scatter(hospital_points[neg_mask, 0], hospital_points[neg_mask, 1],
                            c=colors[i], marker='x', s=s, label=f'{hospital} (-)',
                            alpha=0.7, linewidth=2)

            # 为正负样本分别绘制密度图
            for label, marker, line_style in [(1, 'o', '-'), (0, 'x', '--')]:
                mask = (df_plot['Hospital'] == hospital) & (df_plot['Label'] == label)

                # PC1 密度图（顶部）
                sns.kdeplot(
                    data=df_plot[mask]['PC1'],
                    ax=ax_top,
                    color=colors[i],
                    linestyle=line_style,
                    alpha=0.4,
                    fill=True,
                    linewidth=2,
                    label=f'{hospital} ({"+/-"[label == 0]})'
                )

                # PC2 密度图（右侧）
                # 注意：这里不使用vertical=True，而是交换xy轴
                density = sns.kdeplot(
                    data=df_plot[mask]['PC2'],
                    ax=ax_right,
                    color=colors[i],
                    linestyle=line_style,
                    alpha=0.4,
                    fill=True,
                    linewidth=2,
                    label=f'{hospital} ({"+/-"[label == 0]})'
                )

            start_idx = end_idx

            # 设置主图
        ax_main.set_title('2D PCA Visualization', fontsize=14, pad=20)
        ax_main.set_xlabel(f'PC1 (Variance: {pca.explained_variance_ratio_[0]:.2f})', fontsize=12)
        ax_main.set_ylabel(f'PC2 (Variance: {pca.explained_variance_ratio_[1]:.2f})', fontsize=12)
        ax_main.grid(True, alpha=0.3)

        # 设置边缘分布图
        ax_top.set_title('PC1 Distribution', fontsize=12)
        ax_right.set_title('PC2 Distribution', fontsize=12)

        # 修正PC2密度图的方向
        ax_right.clear()  # 清除之前的图
        for i, (hospital, size) in enumerate(zip(hospitals_to_show, hospital_sizes)):
            for label, marker, line_style in [(1, 'o', '-'), (0, 'x', '--')]:
                mask = (df_plot['Hospital'] == hospital) & (df_plot['Label'] == label)
                kde = gaussian_kde(df_plot[mask]['PC2'])
                x_range = np.linspace(df_plot['PC2'].min(), df_plot['PC2'].max(), 100)
                density = kde(x_range)
                ax_right.plot(density, x_range, color=colors[i], linestyle=line_style,
                              alpha=0.4, linewidth=2, label=f'{hospital} ({"+/-"[label == 0]})')
                ax_right.fill_betweenx(x_range, 0, density, color=colors[i], alpha=0.2)

        # 移除边缘图的刻度标签
        ax_top.set_xticks([])
        ax_right.set_xticks([])

        # 对齐轴限
        ax_top.set_xlim(ax_main.get_xlim())
        ax_right.set_ylim(ax_main.get_ylim())

        # 添加图例
        handles, labels = ax_main.get_legend_handles_labels()
        ax_main.legend(handles[:len(hospitals_to_show) * 2], labels[:len(hospitals_to_show) * 2],
                       bbox_to_anchor=(1.05, -0.1))  # 恢复原来的位置


        # 调整布局
        plt.tight_layout()
        plt.show()
        return plt

    def run_analysis(self, n_folds=5):
        """运行完整分析流程"""
        all_results = []
        all_feature_importance = []

        for fold in range(n_folds):
            print(f"\nProcessing fold {fold + 1}/{n_folds}")
            results, feature_importance = self.train_and_evaluate(fold)
            all_results.append(results)
            all_feature_importance.append(feature_importance)

            self.hospital_results[f'fold_{fold}'] = {
                'results': results,
                'feature_importance': feature_importance
            }

        # 计算平均结果
        avg_results = {}
        metrics = ['auc', 'accuracy', 'sensitivity', 'specificity',
                   'sensitivity_optimal', 'specificity_optimal', 'accuracy_optimal']

        for hospital in self.hospitals:
            avg_results[hospital] = {
                metric: np.mean([fold_results[hospital][metric]
                                 for fold_results in all_results])
                for metric in metrics
            }
            # 计算最优阈值的平均值
            avg_results[hospital]['avg_optimal_threshold'] = np.mean(
                [fold_results[hospital]['optimal_threshold'] for fold_results in all_results]
            )

        print("\nAverage Performance by Hospital:")
        for hospital, metrics in avg_results.items():
            print(f"\n{hospital}:")
            print(f"AUC: {metrics['auc']:.3f}")
            print(f"Accuracy: {metrics['accuracy']:.3f}")
            print(f"Sensitivity: {metrics['sensitivity']:.3f}")
            print(f"Specificity: {metrics['specificity']:.3f}")
            print("\nOptimal threshold metrics:")
            print(f"Average optimal threshold: {metrics['avg_optimal_threshold']:.3f}")
            print(f"Optimal Sensitivity: {metrics['sensitivity_optimal']:.3f}")
            print(f"Optimal Specificity: {metrics['specificity_optimal']:.3f}")
            print(f"Optimal Accuracy: {metrics['accuracy_optimal']:.3f}")

        # 生成可视化
        # hospital_data = self.load_and_prepare_hospital_data(n_folds - 1)

        # print("\nGenerating visualizations...")
        # dim_vis = self.visualize_dimensions(hospital_data)
        # dim_vis.savefig('hospital_data_2d.png')
        #
        # feat_vis = self.plot_feature_importance_comparison(all_feature_importance[-1])
        # feat_vis.savefig('feature_importance_comparison.png')

        return avg_results, all_feature_importance[-1]

    def plot_hospital_metrics(self, avg_results):
        """
        Create a comparison visualization for hospital metrics with density curves.

        Args:
            avg_results: Dictionary containing metrics for each hospital
        """
        metrics_to_plot = ['auc', 'accuracy', 'sensitivity', 'specificity',
                           'sensitivity_optimal', 'specificity_optimal', 'accuracy_optimal']

        plot_data = []
        for hospital, metrics in avg_results.items():
            for metric in metrics_to_plot:
                plot_data.append({
                    'Hospital': hospital,
                    'Metric': metric.replace('_', ' ').title(),
                    'Value': metrics[metric]
                })

        df_plot = pd.DataFrame(plot_data)

        # 创建Figure和GridSpec以便更灵活地控制子图布局
        fig = plt.figure(figsize=(15, 14))
        gs = fig.add_gridspec(4, 2, height_ratios=[0.5, 3, 0.5, 3])

        # 上半部分：默认阈值指标
        ax_top_density = fig.add_subplot(gs[0, :])
        ax1 = fig.add_subplot(gs[1, :])

        # 下半部分：最优阈值指标
        ax_bottom_density = fig.add_subplot(gs[2, :])
        ax2 = fig.add_subplot(gs[3, :])

        # 原始指标图（默认阈值）
        original_metrics = ['Auc', 'Accuracy', 'Sensitivity', 'Specificity']
        df_original = df_plot[df_plot['Metric'].isin(original_metrics)]

        # 绘制主图
        sns.barplot(
            data=df_original,
            x='Hospital',
            y='Value',
            hue='Metric',
            ax=ax1,
            palette='Blues'
        )
        ax1.set_title('Performance Metrics at Default Threshold (0.5)')
        ax1.set_ylim(0, 1)
        ax1.grid(True, axis='y')

        # 绘制密度图
        for i, metric in enumerate(original_metrics):
            metric_values = df_original[df_original['Metric'] == metric]['Value']
            sns.kdeplot(
                data=metric_values,
                ax=ax_top_density,
                fill=True,
                alpha=0.3,
                color=plt.cm.Blues((i + 1) / (len(original_metrics) + 1)),
                label=metric
            )
        ax_top_density.set_title('Density Distribution of Default Threshold Metrics')
        ax_top_density.set_xlabel('')
        ax_top_density.set_ylim(bottom=0)

        # 最优阈值指标图
        optimal_metrics = ['Sensitivity Optimal', 'Specificity Optimal', 'Accuracy Optimal']
        df_optimal = df_plot[df_plot['Metric'].isin(optimal_metrics)]

        # 绘制主图
        sns.barplot(
            data=df_optimal,
            x='Hospital',
            y='Value',
            hue='Metric',
            ax=ax2,
            palette='Greens'
        )
        ax2.set_title('Performance Metrics at Optimal Threshold')
        ax2.set_ylim(0, 1)
        ax2.grid(True, axis='y')

        # 绘制密度图
        for i, metric in enumerate(optimal_metrics):
            metric_values = df_optimal[df_optimal['Metric'] == metric]['Value']
            sns.kdeplot(
                data=metric_values,
                ax=ax_bottom_density,
                fill=True,
                alpha=0.3,
                color=plt.cm.Greens((i + 1) / (len(optimal_metrics) + 1)),
                label=metric
            )
        ax_bottom_density.set_title('Density Distribution of Optimal Threshold Metrics')
        ax_bottom_density.set_xlabel('')
        ax_bottom_density.set_ylim(bottom=0)

        # 添加最优阈值标注
        for i, hospital in enumerate(avg_results.keys()):
            threshold = avg_results[hospital]['avg_optimal_threshold']
            ax2.text(i, 0.05, f'Threshold: {threshold:.2f}',
                     ha='center', va='bottom')

        # 调整布局
        plt.tight_layout()
        plt.show()
        # 保存图像
        plt.savefig('hospital_metrics_comparison.png', dpi=300, bbox_inches='tight')
        return plt


def load_config(config_file, environment='default'):
    """
    Load configuration from a JSON file.
    :param config_file:
    :param environment:
    :return:
    """
    with open(config_file, 'r') as file:
        config = json.load(file)
        # 根据传入的环境参数获取相应的配置
        return config[environment]
if __name__ == "__main__":

    config_file = './files/configs/test_config_V1211.json'
    params = load_config(config_file, "train_loss_test")
    # 初始化分析器
    analyzer = HospitalAnalysis(
        # excel_path="./files/data/data_index/data_V1114/cons/诊断模型数据11.29新修.xlsx",
        excel_path="./files/data/data_index/data_V1114/cons_plus/诊断模型数据12.3新增.xlsx",

        **params
    )

    # 运行5折交叉验证的完整分析
    avg_results, feature_importance = analyzer.run_analysis(n_folds=3)

    hospital_data = analyzer.load_and_prepare_hospital_data(3 - 1,)

    dim_vis = analyzer.visualize_dimensions(hospital_data,s=50,hospitals_to_show=['北京301','上海六院'])#,hospitals_to_show=['北京301','广东南海','上海六院'])
    analyzer.plot_hospital_metrics(avg_results)
    feat_vis = analyzer.plot_feature_importance_comparison(feature_importance)
