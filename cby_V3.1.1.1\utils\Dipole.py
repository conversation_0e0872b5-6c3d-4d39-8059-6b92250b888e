"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

file: cneter_traject_301
date: 2024/01/02 13:46
desc: 偶极子相关函数
"""
import os
import re
import numpy as np
import random
import matplotlib.pyplot as plt
import matplotlib as mpl
import imageio
from skimage import img_as_ubyte
from utils.utils import *
from utils.cal_interpolates import cal_interpolate


class CenterTrajectPOINTS():
    def __init__(self, matrixes=[], seqs=[1, 2]):
        self.matrixes = matrixes  # 插值矩阵
        self.seqs = seqs  # 间期索引

    def get_dipole(self, matrix):
        '''计算偶极子坐标'''
        px, py = np.unravel_index(np.argmax(matrix), matrix.shape)
        nx, ny = np.unravel_index(np.argmin(matrix), matrix.shape)
        return px, py, nx, ny

    def get_dipole_ct(self, cx, cy) -> list:
        '''偶极子中心统计'''
        cors = []
        for x1 in [int(cx), round(cx)]:
            for y1 in [int(cy), round(cy)]:
                # x1 = max(0, min(99, x1))  # 端点控制
                # y1 = max(0, min(99, y1))
                if [x1, y1] not in cors:
                    cors.append([x1, y1])
        return cors

    def cal_cjpt(self, matrixes, i1):
        '''计算一帧的偶极子中心, 最多4个点'''
        matrix = matrixes[i1-1]
        px, py, nx, ny = self.get_dipole(matrix)
        cx, cy = (px + nx) / 2, (py + ny) / 2
        cors = self.get_dipole_ct(cx, cy)
        return cors

    def cal_cjpts(self, matrixes, time1, time2=None):
        '''计算QR中心轨迹点集[[x0, y0], [x1, y1], ..., [xN, yN]], 或返回一帧中心轨迹'''
        if time2:
            cjpts = []
            for i1 in range(time1, time2+1):
                cjpt = self.cal_cjpt(matrixes, i1)
                for cors in cjpt:
                    if cors not in cjpts:
                        cjpts.append(cors)
        else:
            cjpts = self.cal_cjpt(matrixes, time1)
        return cjpts

    def get_matrixes_list(self, mfm_dir, times_file,  display=10):
        '''
        返回插值矩阵list和时刻点list
        mfm_dir心磁地址
        times_file人名和QRSTTT时刻点
        display显示迭代次数, 如迭代10次显示一次
        '''
        doc = open(times_file, 'r')
        tlines = doc.readlines()
        doc.close()
        matrixes_list, times_list = [], []
        for i11 in range(1, len(tlines)):
            if i11 % display == 0:
                print('start:', i11, 'left:', len(tlines)-i11-1)
            times = re.split(' |,', tlines[i11].strip())
            times = rm_null(times)
            mfm_file = os.path.join(mfm_dir, times[0]+'.txt')
            Qp, Te = int(times[1]), int(times[6])
            matrixes = cal_interpolate(mfm_file, Qp, Te)
            matrixes_list.append(matrixes)
            times_list.append(times)
        return matrixes_list, times_list

    def cal_cettrj_pts(self, matrixes_list, times_list, seqs=[1, 2]) -> list:
        '''
        中心轨迹点集计算，返回全部人员的点集列表
        matrixes_list插值矩阵list
        times_list时刻点list
        seqs起止时刻点, 如QpRp
        '''
        cttjpts = []
        for i11 in range(len(matrixes_list)):
            matrixes, times = matrixes_list[i11], times_list[i11]
            time1, time2 = int(times[seqs[0]]), int(times[seqs[1]])
            cjpts = self.cal_cjpts(matrixes, time1, time2)  # 计算中心轨迹点集
            cttjpts.append(cjpts)
        return cttjpts

    def save_gif(self, names, save_path):
        imgs = []
        for imgname in names:
            # print(names)
            img = plt.imread(imgname)
            imgs.append(img)
        imgs = np.array(imgs)
        imageio.mimsave(save_path+'.gif', img_as_ubyte(imgs), 'GIF', duration=0.2)

    def save_txt(self, messages, mode='list', files=None):
        '''保存为txt'''
        doc = open(files, 'w')
        if mode == 'list':  # tuple
            for ii1 in range(len(messages)):
                doc.write(str(messages[ii1]))
                if ii1 < len(messages) - 1:
                    doc.write('\n')
        elif mode == 'lists':  # tuple
            for ii1 in range(len(messages[0])):
                for jj1 in range(len(messages)):
                    doc.write(str(messages[jj1][ii1]))
                    if jj1 < len(messages) - 1:
                        doc.write(', ')
                if ii1 < len(messages[0]) - 1:
                    doc.write('\n')
        elif mode == 'array':
            for ii1 in range(messages.shape[0]):
                doc.write(str(messages[ii1, :]))
                if ii1 < messages.shape[0] - 1:
                    doc.write('\n')
        elif mode == 'dict':
            cnt = 0
            for key, value in messages.items():
                cnt += 1
                doc.write('%s %s' % (str(key), str(value)))
                if cnt < len(messages):
                    doc.write('\n')
        else:  # int/str
            doc.write(str(messages))
        doc.close()

    def get_cors(self, cors):
        xss, yss = [], []
        for ik in range(len(cors)):
            xss.append(cors[ik][0])
            yss.append(cors[ik][1])
        return xss, yss

    def draw_cttj_pts(self, save_path, draw_ms, modec=False, modeg=False):
        '''
        绘制中心轨迹点集计算过程, 并合成动画
        save_path: 保存目录
        draw_ms: 绘制信息
        modec: 是否添加中心点
        modeg: 是否合成动画
        '''
        # 准备
        if not os.path.exists(save_path):
            os.mkdir(save_path)
        names, cors = [], []
        # for i1 in range(0, 2):
        for i1 in range(len(draw_ms)):
            ms0 = draw_ms[i1]
            saveimg = os.path.join(save_path, str(ms0[0])+'.png')
            plt.rcParams['figure.figsize'] = (8, 8)
            plt.rcParams['savefig.dpi'] = 256
            plt.matshow(ms0[1], cmap='jet')
            plt.xticks([])
            plt.yticks([])
            plt.axis('off')
            plt.margins(0, 0)
            plt.draw()
            px, py, dx, dy = ms0[2]
            plt.quiver(py, px, dx, dy, scale=1, color='w', scale_units='xy')
            cors.extend(ms0[4])
            xss, yss = self.get_cors(cors)
            plt.scatter(yss, xss, color='k', marker='s', s=13.4)  # xy反向绘制
            if modec:
                plt.scatter(ms0[3][1], ms0[3][0], color='w', marker='*', s=2)
            plt.savefig(saveimg, bbox_inches='tight', pad_inches=0)
            plt.close()
            names.append(saveimg)
        if modeg:
            self.save_gif(names, save_path)

    def stat_cttj_pts(self, mfm_dir, times_file, savedir, seqs=[1, 2], sel_list=10, mode='random'):
        '''
        统计[矩阵, 正负指向坐标, 中心点坐标, 偶极子中心], 用于绘制中心轨迹点集计算过程
        mode: random选择随机N个实现, list选择列表中的索引实现, id实现一个样本
        '''
        # 准备
        print(mfm_dir, times_file, savedir)
        if not os.path.exists(savedir):
            os.mkdir(savedir)
        doc = open(times_file, 'r')
        tlines = doc.readlines()
        doc.close()
        if mode == 'random':
            sel_list = random.sample(list(range(1, len(tlines))), sel_list)  # 10
        elif mode == 'list':
            pass
        else:  # 'id'
            for i11 in range(1, len(tlines)):
                times = re.split(' |,', tlines[i11].strip())
                times = rm_null(times)
                if sel_list == times[0]:
                    sel_list = [i11]
                    break
        # 统计和绘制
        for i11 in sel_list:
            times = re.split(' |,', tlines[i11].strip())
            times = rm_null(times)
            mfm_file = os.path.join(mfm_dir, times[0]+'.txt')
            Qp, Te = int(times[1]), int(times[6])
            matrixes = cal_interpolate(mfm_file, Qp, Te)
            time1, time2 = int(times[seqs[0]]), int(times[seqs[1]])
            # 统计信息
            draw_ms = []
            for i1 in range(time1, time2+1):
                ms0 = [i1]
                matrix = matrixes[i1-1]
                px, py, nx, ny = self.get_dipole(matrix)
                dx, dy = ny - py, px - nx  # 坐标和角度指向
                cx, cy = (px + nx) / 2, (py + ny) / 2
                cors = self.get_dipole_ct(cx, cy)
                ms0.append(matrix)
                ms0.append([px, py, dx, dy])
                ms0.append([cx, cy])
                ms0.append(cors)
                draw_ms.append(ms0)
            # 绘制图像和gif
            save_path = os.path.join(savedir, times[0])
            self.draw_cttj_pts(save_path, draw_ms, True, True)

    def matrix_render(self, Z0, savedir, cmaps, v1, v2):
        '''Z0矩阵渲染, cmaps颜色, v1v2正则化'''
        plt.rcParams['figure.figsize'] = (8, 8)
        plt.rcParams['savefig.dpi'] = 64
        norm = mpl.colors.Normalize(vmin=v1, vmax=v2)
        plt.matshow(Z0, cmap=cmaps, norm=norm)  # 4种渲染——
        plt.xticks([])
        plt.yticks([])
        plt.axis('off')
        plt.margins(0, 0)
        plt.draw()
        plt.savefig(savedir, bbox_inches='tight', pad_inches=0)
        plt.close()

    def stat_cors(self, A):
        '''统计坐标为矩阵'''
        Z = np.zeros((100, 100))
        for cors in A:
            for cor in cors:
                Z[cor[0], cor[1]] += 1  # xy反向绘制
        return Z

    def divide_norm(self, Z1, Z):
        '''相除和归一化, 0赋值-1, 正数归一化'''
        Z2 = Z.copy()
        Z2[Z == 0] = -1
        Z2 = np.divide(Z1, Z2)
        Z2 = Z2 / Z2.max()
        Z2[Z == 0] = -1
        return Z2

    def rank_assign(self, Z2, rlis):
        '''按rlis从0开始赋值, 如[0, 0.6, 0.7, 0.8, 1]赋值0/1/2/3'''
        Z3 = Z2.copy()
        for i3 in range(Z2.shape[0]):
            for j3 in range(Z2.shape[1]):
                for k3 in range(1, len(rlis-1)):
                    if k3 >= rlis[len(rlis)-1-k3]:
                        break
                Z3[i3, j3] = int(len(rlis)-1-k3)
        return Z3
        # if len(rlis) == 6:  # raw202312
        #     rf0, rf1, rf2, rf3 = rlis[1:5]
        #     for i3 in range(Z2.shape[0]):
        #         for j3 in range(Z2.shape[1]):
        #             if Z2[i3, j3] < rf0:
        #                 Z3[i3, j3] = 0
        #             elif Z2[i3, j3] < rf1:
        #                 Z3[i3, j3] = 1
        #             elif Z2[i3, j3] < rf2:
        #                 Z3[i3, j3] = 2
        #             elif Z2[i3, j3] < rf3:
        #                 Z3[i3, j3] = 3
        #             else:
        #                 Z3[i3, j3] = 4
        # if len(rlis) == 5:
        #     rf0, rf1, rf2 = rlis[1:4]
        #     for i3 in range(Z2.shape[0]):
        #         for j3 in range(Z2.shape[1]):
        #             if Z2[i3, j3] < rf0:
        #                 Z3[i3, j3] = 0
        #             elif Z2[i3, j3] < rf1:
        #                 Z3[i3, j3] = 1
        #             elif Z2[i3, j3] < rf2:
        #                 Z3[i3, j3] = 2
        #             else:
        #                 Z3[i3, j3] = 3
        # return Z3

    def draw_dpr_imgs(self, A, B, rlis, savedir=None, mode=[1, 1, 1]):
        '''
        绘制密度分布图/概率分布图/等级分布图
        A: 阴性样本的中心轨迹点集
        B: 阳性样本的中心轨迹点集
        rlis: 等级划分标准, 如[0, 0.6, 0.7, 0.8, 1]
        mode: 是否绘制
        '''
        Z0, Z1 = self.stat_cors(A), self.stat_cors(B)
        Z = Z0 + Z1
        Z2 = self.divide_norm(Z1, Z)
        Z3 = self.rank_assign(Z2, rlis)
        if mode[0]:
            saveimg = os.path.join(savedir, 'density_'+str(2*len(A))+'.png')
            self.matrix_render(Z, saveimg, 'binary', 0, Z.max())
        if mode[1]:
            saveimg = os.path.join(savedir, 'probability_'+str(2*len(A))+'.png')
            self.matrix_render(Z2, saveimg,'seismic', -1, 1)
        if mode[2]:
            saveimg = os.path.join(savedir, 'ranks_'+str(2*len(A))+'.png')
            self.matrix_render(Z3, saveimg, 'Reds', 0, 4)
            # self.matrix_render(Z3, saveimg, 'Reds', 0, len(rlis)-2)
        return Z, Z2, Z3

    def norm_pn(self, A):
        '''矩阵归一化,正负分别进行'''
        A1, A2 = A.copy(), A.copy()
        A1[A1 > 0] = 0
        A1 = A1 / min(-0.000001, A1.min())
        A2[A2 < 0] = 0
        A2 = A2 / max(0.000001, A2.max())
        A3 = A2 - A1
        return A3

    def loss_mse(self, A, A1):
        '''MSE均方误差'''
        size = A.shape[0] * A.shape[1]
        mse = np.sum((A-A1)**2) / size
        return mse

    def loss_ssim(self, img1, img2, L=1):
        """Calculate SSIM (structural similarity) for one channel images.
        Args:
            img1 (ndarray): Images with range [0, 255].
            img2 (ndarray): Images with range [0, 255].
        Returns:
            float: ssim result.
        Analysis:
            取值为-1到1, 1最相似, -1最不相似, 0无关
        """
        K1 = 0.01
        K2 = 0.03
        C1 = (K1 * L)**2
        C2 = (K2 * L)**2
        C3 = C2/2
        ux = img1.mean()
        uy = img2.mean()
        ux_sq = ux**2
        uy_sq = uy**2
        uxuy = ux * uy
        ox_sq = img1.var()
        oy_sq = img2.var()
        ox = np.sqrt(ox_sq)
        oy = np.sqrt(oy_sq)
        oxoy = ox * oy
        oxy = np.mean((img1 - ux) * (img2 - uy))
        L = (2 * uxuy + C1) / (ux_sq + uy_sq + C1)
        C = (2 * ox * oy + C2) / (ox_sq + oy_sq + C2)
        S = (oxy + C3) / (oxoy + C3)
        ssim = L * C * S
        return ssim

    def loss_psnr(self, img1, img2, L=1):
        '''PSNR峰值信噪比, L为灰度范围'''
        mse = np.mean((img1-img2)**2)
        if mse == 0:
            return 100
        else:
            return 20*np.log10(L/np.sqrt(mse))

    def loss_msp(self, A, B, C, A1, B1, C1):
        '''MSE, SSIM, PSNR'''
        A, A1 = self.norm_pn(A), self.norm_pn(A1)
        B, B1 = self.norm_pn(B), self.norm_pn(B1)
        C, C1 = self.norm_pn(C), self.norm_pn(C1)
        l11 = self.loss_mse(A, A1)
        l21 = self.loss_mse(B, B1)
        l31 = self.loss_mse(C, C1)
        l12 = self.loss_ssim(A, A1)
        l22 = self.loss_ssim(B, B1)
        l32 = self.loss_ssim(C, C1)
        l13 = self.loss_psnr(A, A1)
        l23 = self.loss_psnr(B, B1)
        l33 = self.loss_psnr(C, C1)
        return [l11, l12, l13, l21, l22, l23, l31, l32, l33]

    def draw_curve(self, yss, saveimg):
        plt.figure()
        plt.rcParams['savefig.dpi'] = 256
        plt.plot(list(range(len(yss))), yss, color='k', marker=',', linestyle='-', linewidth=1)
        plt.savefig(saveimg, bbox_inches='tight', pad_inches=0)
        plt.close()

    def draw_loss(self, loss, savedir):
        loss = np.array(loss)
        self.draw_curve(loss[:, 0], os.path.join(savedir, 'density_mse.png'))
        self.draw_curve(loss[:, 1], os.path.join(savedir, 'density_ssim.png'))
        self.draw_curve(loss[:, 2], os.path.join(savedir, 'density_psnr.png'))
        self.draw_curve(loss[:, 3], os.path.join(savedir, 'probability_mse.png'))
        self.draw_curve(loss[:, 4], os.path.join(savedir, 'probability_ssim.png'))
        self.draw_curve(loss[:, 5], os.path.join(savedir, 'probability_psnr.png'))
        self.draw_curve(loss[:, 6], os.path.join(savedir, 'ranks_mse.png'))
        self.draw_curve(loss[:, 7], os.path.join(savedir, 'ranks_ssim.png'))
        self.draw_curve(loss[:, 8], os.path.join(savedir, 'ranks_psnr.png'))

    def train_view(self, cttjpts, rlis, l1=[500, 500], l2=[100, 100], l3=[10, 10], savedir=None):
        '''
        训练中心轨迹点集的收敛过程
        cttjpts: QR中心轨迹点集列表
        rlis: 等级划分标准
        l1: 阴阳数量
        l2: 初始阴阳数据量
        l3: 每次迭代增加的阴阳数据量
        '''
        # 初始化
        N, P = cttjpts[:l1[0]], cttjpts[l1[0]:l1[0]+l1[1]]
        A = random.sample(N, l2[0])
        B = random.sample(P, l2[1])
        N = [x for x in N if x not in A]
        P = [x for x in P if x not in B]
        Z, Z2, Z3 = self.draw_dpr_imgs(A, B, rlis, savedir, [1, 1, 1])
        # 迭代
        cnt, loss0 = 0, []
        lenth = int((l1[0]-l2[0]) / l3[0])
        for ik in range(lenth):
            if len(N) > 0:
                cnt += 1
                print('——————save image', str(cnt))
                A0 = random.sample(N, l3[0])
                B0 = random.sample(P, l3[1])
                A = A + A0
                B = B + B0
                N = [x for x in N if x not in A]
                P = [x for x in P if x not in B]
                newZ, newZ2, newZ3 = self.draw_dpr_imgs(A, B, rlis, savedir, [1, 1, 1])
                loss = self.loss_msp(Z, Z2, Z3, newZ, newZ2, newZ3)
                loss0.append(loss)
                Z, Z2, Z3 = newZ, newZ2, newZ3
        self.draw_loss(loss0, savedir)
        return loss0

    def data_fold(self, allnum, fold=[100, 100]):
        '''
        返回几折分割list
        allnum总数
        foldsize每折阴阳数量比例
        '''
        n1 = int(allnum * fold[0] / sum(fold))
        cnt = int(n1/fold[0])
        Nnum, Pnum = list(range(n1)), list(range(n1, allnum))
        Ds = []
        for i1 in range(cnt):
            A1 = random.sample(Nnum, fold[0])
            B1 = random.sample(Pnum, fold[1])
            Ds.append(A1+B1)
            Nnum = [x for x in Nnum if x not in A1]
            Pnum = [x for x in Pnum if x not in B1]
        return Ds

    def cal_cttj_metrics(self, Z3, cttjpts, mode='06'):
        '''
        计算中心轨迹等级分布和指标
        Z3是等级矩阵图谱
        mode不同模式: 06计算dsrk和rkrt, 02计算dsws0和dsws1
        '''
        if mode == '06':
            nslis, dsrks, rkrts = [], [], []
            for i11 in range(len(cttjpts)):
                ns, dsrk, rkrt = [0, 0, 0, 0], '', 0
                ns = self.get_ns(ns, Z3, cttjpts[i11])
                for j11 in range(len(ns)):
                    k11 = len(ns) - 1 - j11
                    if ns[k11] > 0:
                        dsrk += str(k11)
                rkrt = sum(ns[1:]) / sum(ns)
                nslis.append(ns)
                dsrks.append(int(dsrk))
                rkrts.append(rkrt)
            return nslis, dsrks, rkrts
        else:  # '02'
            nslis, dswss0, dswss1 = [], [], []
            for i11 in range(len(cttjpts)):
                ns, dsws0, dsws1 = [0, 0, 0, 0, 0], 0, 0
                ns = self.get_ns(ns, Z3, cttjpts[i11])
                dsws0 = (2*ns[3]+4*ns[4]-2*ns[1]-4*ns[0]) / sum(ns)
                dsws1 = (3*ns[3]+5*ns[4]-3*ns[1]-5*ns[0]) / sum(ns)
                nslis.append(ns)
                dswss0.append(dsws0)
                dswss1.append(dsws1)
            return nslis, dswss0, dswss1

    def get_ns(self, ns, Z3, A0):
        '''获取等级分布, A0样本点集'''
        for cor in A0:
            ik = int(Z3[cor[0], cor[1]])
            ns[ik] += 1
        return ns

    def train_cont(self, ptsliss, rlis1, rlis2, segs, Ds, vsdir=None):
        '''
        训练不同折、不同等级划分、不同间期下的指标结果
        ids第几折
        cttjpts: 点集列表QR/RS/TT
        rlis1, rlis2: 不同等级划分标准
        segs: 不同间期['QR', 'RS', 'TT']
        foldsize: 每折数量
        trids: 训练集ids
        teids: 测试集ids
        savedir: 0.6版和0.2版文件夹
        '''
        for i1 in range(len(Ds)):  # 5折
            train_ids0, train_ids1, teids = [], [], Ds[i1]
            for j1 in range(len(Ds)):
                if j1 != i1:
                    train_ids0 += Ds[j1][:100]
                    train_ids1 += Ds[j1][100:]
            for j1 in range(len(ptsliss)):  # QR/RS/TT
                print('start:', [i1+1, j1+1], 'to:', [len(Ds), len(ptsliss)])
                cttjpts, segname = ptsliss[j1], segs[j1]
                rootpath1 = '%s/%s_fold%d_tr' % (vsdir[0], segname, i1+1)
                rootpath2 = '%s/%s_fold%d_tr' % (vsdir[1], segname, i1+1)
                cttjpts0 = [cttjpts[k1] for k1 in train_ids0]
                cttjpts1 = [cttjpts[k1] for k1 in train_ids1]
                Z0, Z1 = self.stat_cors(cttjpts0), self.stat_cors(cttjpts1)
                Z = Z0 + Z1
                Z2 = self.divide_norm(Z1, Z)
                Z31 = self.rank_assign(Z2, rlis1)  # 等级分布矩阵
                nslis, dsrks, rkrts = self.cal_cttj_metrics(Z31, cttjpts0+cttjpts1, mode='06')
                self.save_txt(Z31, 'array', rootpath1+'_Z3.txt')
                self.save_txt(nslis, 'list', rootpath1+'_nslis.txt')
                self.save_txt([dsrks, rkrts], 'lists', rootpath1+'_metrics.txt')
                Z32 = self.rank_assign(Z2, rlis2)
                nslis, dswss0, dswss1 = self.cal_cttj_metrics(Z32, cttjpts0+cttjpts1, mode='02')
                self.save_txt(Z32, 'array', rootpath2+'_Z3.txt')
                self.save_txt(nslis, 'list', rootpath2+'_nslis.txt')
                self.save_txt([dswss0, dswss1], 'lists', rootpath2+'_metrics.txt')

    def test_cont(self, ptsliss, rlis1, rlis2, segs, Ds, vsdir=None):
        '''
        测试不同折、不同等级划分、不同间期下的指标结果
        ids第几折
        cttjpts: 点集列表QR/RS/TT
        rlis1, rlis2: 不同等级划分标准
        segs: 不同间期['QR', 'RS', 'TT']
        foldsize: 每折数量
        trids: 训练集ids
        teids: 测试集ids
        savedir: 0.6版和0.2版文件夹
        '''
        for i1 in range(len(Ds)):  # 5折
            train_ids0, train_ids1, teids = [], [], Ds[i1]
            for j1 in range(len(Ds)):
                if j1 != i1:
                    train_ids0 += Ds[j1][:100]
                    train_ids1 += Ds[j1][100:]
            for j1 in range(len(ptsliss)):  # QR/RS/TT
                print('start:', [i1+1, j1+1], 'to:', [len(Ds), len(ptsliss)])
                cttjpts, segname = ptsliss[j1], segs[j1]
                rootpath1 = '%s/%s_fold%d_tr' % (vsdir[0], segname, i1+1)
                rootpath2 = '%s/%s_fold%d_tr' % (vsdir[1], segname, i1+1)
                cttjpts0 = [cttjpts[k1] for k1 in train_ids0]
                cttjpts1 = [cttjpts[k1] for k1 in train_ids1]
                # Z0, Z1 = self.stat_cors(cttjpts0), self.stat_cors(cttjpts1)
                # Z = Z0 + Z1
                # Z2 = self.divide_norm(Z1, Z)
                # Z31 = self.rank_assign(Z2, rlis1)  # 等级分布矩阵
                Z31 = self.rank_assign(Z2, rlis1)  # 等级分布矩阵
                nslis, dsrks, rkrts = self.cal_cttj_metrics(Z31, cttjpts0+cttjpts1, mode='06')
                self.save_txt(Z31, 'array', rootpath1+'_Z3.txt')
                self.save_txt(nslis, 'list', rootpath1+'_nslis.txt')
                self.save_txt([dsrks, rkrts], 'lists', rootpath1+'_metrics.txt')
                Z32 = self.rank_assign(Z2, rlis2)
                nslis, dswss0, dswss1 = self.cal_cttj_metrics(Z32, cttjpts0+cttjpts1, mode='02')
                self.save_txt(Z32, 'array', rootpath2+'_Z3.txt')
                self.save_txt(nslis, 'list', rootpath2+'_nslis.txt')
                self.save_txt([dswss0, dswss1], 'lists', rootpath2+'_metrics.txt')

    def stat_elem_num(self, my_list, element):
        '''统计列表中元素数量'''
        my_array = np.array(my_list)
        count = np.count_nonzero(my_array == element)
        return count

    def cal_dsrkbl(self, dsrkrts, num=400):
        '''计算等级分布比率'''
        data = np.array(dsrkrts)
        rks = data[:, 0]
        rks0, rks1 = rks[:num], rks[num:]
        rks = np.unique(rks)
        dsrkbl = []
        for i2 in range(len(rks)):
            num0 = self.stat_elem_num(rks0, rks[i2])
            num1 = self.stat_elem_num(rks1, rks[i2])
            num2 = num0 + num1
            rt = num1 / num2
            dsrkbl.append([rks[i2], num0, num1, num2, rt])
        return dsrkbl

    def ranks_cont(self, segs, vsdir=None):
        '''分级'''
        pathlis0, pathlis1 = [], []
        for i1 in range(5):  # 5折
            for j1 in range(3):  # QR/RS/TT
                pathlis0.append('%s/%s_fold%d_tr' % (vsdir[0], segs[j1], i1+1))
                pathlis1.append('%s/%s_fold%d_tr' % (vsdir[1], segs[j1], i1+1))
        # ### dsrk+rkrt指标分级
        i1 = 4*3+2  # 0-4, 0-2
        dsrkrts = txt_to_list(pathlis0[i1]+'_metrics.txt')
        # dsrkbl = self.cal_dsrkbl(dsrkrts, 400)  # 计算等级分布的比率
        # self.save_txt(dsrkbl, 'list', pathlis0[i1]+'_dsrk_rate.txt')
        dsrkrts = np.array(dsrkrts)
        dsrks, rkrts = list(dsrkrts[:, 0]), list(dsrkrts[:, 1])
        rklis = [10, 210, 3210]
        j1 = 2
        svf = '%s_ranks_%d.txt' % (pathlis0[i1], rklis[j1])
        n12m = [0, 0]
        # lis10 = [0, 0.045, 0.166, 0.501]  # QRf1
        # lis210 = [0, 0.105, 0.222, 0.428, 0.56, 0.609]
        # lis310 = [0, 0.172, 0.185, 0.428, 0.901]
        # lis3210 = [0, 0.181, 0.185, 0.303, 0.678, 0.892]
        # lis10 = [0, 0.041, 0.166, 0.5, 0.751]  # QRf2
        # lis210 = [0, 0.074, 0.15, 0.393, 0.458, 0.626]
        # lis310 = [0, 0.08, 0.187, 0.458, 0.969]
        # lis3210 = [0, 0.135, 0.756, 0.951]
        # lis10 = [0, 0.052, 0.2, 0.347, 0.412]  # QRf3
        # lis210 = [0, 0.076, 0.19, 0.222, 0.565, 0.706]
        # lis310 = [0, 0.076, 0.304, 0.762]
        # lis3210 = [0, 0.107, 0.187, 0.233, 0.514, 0.953]
        # lis10 = [0, 0.052, 0.304, 0.376]  # QRf4
        # lis210 = [0, 0.083, 0.166, 0.375, 0.52, 0.584]
        # lis310 = [0, 0.1, 0.235, 0.351, 0.851]
        # lis3210 = [0, 0.131, 0.2, 0.23, 0.666, 0.938]
        # lis10 = [0, 0.038, 0.171, 0.305]  # QRf5
        # lis210 = [0, 0.088, 0.464, 0.667]
        # lis310 = [0, 0.125, 0.181, 0.304, 0.81]
        # lis3210 = [0, 0.586, 0.953]
        # lis10 = [0, 0.068, 0.451]  # RSf1
        # lis210 = [0, 0.1, 0.421, 0.535, 0.601]
        # lis310 = [0, 0.166, 0.214, 0.478, 0.885]
        # lis3210 = [0, 0.142, 0.227, 0.48, 0.883]
        # lis10 = [0, 0.045, 0.429]  # RSf2
        # lis210 = [0, 0.068, 0.751]
        # lis310 = [0, 0.166, 0.23, 0.318, 0.701]
        # lis3210 = [0, 0.137, 0.565, 0.901]
        # lis10 = [0, 0.133, 0.429]  # RSf3
        # lis210 = [0, 0.128, 0.357, 0.529, 0.607]
        # lis310 = [0, 0.086, 0.206, 0.368, 0.686]
        # lis3210 = [0, 0.116, 0.204, 0.513, 0.885]
        # lis10 = [0, 0.055, 0.401]  # RSf4
        # lis210 = [0, 0.096, 0.333, 0.454, 0.648]
        # lis310 = [0, 0.135, 0.216, 0.409, 0.768]
        # lis3210 = [0, 0.151, 0.153, 0.512, 0.867]
        # lis10 = [0, 0.045, 0.371]  # RSf5
        # lis210 = [0, 0.161, 0.333, 0.481, 0.59, 0.765]
        # lis310 = [0, 0.148, 0.212, 0.29, 0.688]
        # lis3210 = [0, 0.128, 0.189, 0.565, 0.889]
        # lis210 = [0, 0.583, 0.867]  # TTf1
        # lis3210 = [0, 0.369, 0.966]
        # lis10 = [0, 0.08, 0.667]  # TTf2
        # lis210 = [0, 0.225, 0.411, 0.786]
        # lis3210 = [0, 0.318, 0.757, 0.979]
        # lis10 = [0, 0.125, 0.834]  # TTf3
        # lis210 = [0, 0.129, 0.29, 0.48, 0.858]
        # lis3210 = [0, 0.2, 0.333, 0.837, 0.979]
        # lis10 = [0, 0.041, 0.701]  # TTf4
        # lis210 = [0, 0.222, 0.666, 0.867]
        # lis310 = [0, 0.428, 0.966]
        # lis3210 = [0, 0.285, 0.342, 0.821, 0.963]
        # lis10 = [0, 0.225, 0.384, 0.584]  # TTf5
        # lis210 = [0, 0.13, 0.917]
        # lis3210 = [0, 0.306, 0.344, 0.937, 0.976]
        lis1, lis2, lis12 = self.get_lis12_dsrkrts(dsrks, rkrts, 400, rklis[j1])
        self.make_ranks(lis1, lis2, lis12, savefile=svf, n12m=n12m, mode=0, lis0=0)
        # # ### dsws指标分级
        # i1 = 0*3+0  # 0-4, 0-2
        # dswslis = txt_to_list(pathlis1[i1]+'_metrics.txt')
        # dswslis = np.array(dswslis)
        # svf=pathlis1[i1]+'_ranks_mt1.txt'
        # n12m=[0, 0]
        # # lis0 = [-3.406, -1.3, -0.036, 0.105, 0.363, 1.512, 3.201]  # 最后1个>
        # # lis3 = [-3.278, -0.728, -0.414, 0.035, 0.37, 1.047, 3.501]
        # # lis6 = [-2.701, -1.401, -0.286, -0.08, 0.444, 0.956, 3.62]
        # # lis9 = [-3.223, -1.091, -0.112, 0.142, 0.275, 1.096, 3.313]
        # # lis12 = [-3.223, -0.948, -0.073, 0.38, 1.346, 3.334]
        # # lis1 = [-2.8, -0.834, -0.149, 0.04, 2.444, 3.231]
        # # lis4 = [-2.757, -0.875, -0.1, 0.074, 2.416, 3.154]
        # # lis7 = [-2.8, -0.834, -0.052, 0.272, 1.058, 3.154]
        # # lis10 = [-2.5, -1, -0.034, 0.378, 0.88, 3.001]
        # # lis13 = [-2.6, -1.167, 0.051, 0.133, 2.801]
        # # lis2 = [-4, -1.125, -0.091, 0.476, 0.714, 2.875, 4.001]
        # # lis5 = [-4, -1.8, 0.045, 0.518, 1.022, 2.114, 4.001]
        # # lis8 = [-3.827, -0.8, 0, 0.5, 0.842, 1.871, 4.001]
        # # lis11 = [-4, -1, 0, 0.375, 0.666, 3.09, 4.001]
        # # lis14 = [-4, -1, -0.134, 0.553, 3.025, 4.001]
        # lis1, lis2, lis12 = self.get_lis12_dsws(list(dswslis[:, 0]), 400)
        # self.make_ranks(lis1, lis2, lis12, savefile=svf, n12m=n12m, mode=0, lis0=0)
        # # lis1, lis2, lis12 = self.get_lis12_dsws(list(dswslis[:, 1]), 400)
        # # self.make_ranks(lis1, lis2, lis12, pathlis1[i1]+'_ranks_mt2.txt')

    def get_lis12_dsrkrts(self, dsrks, rkrts, num, dj):
        '''
        截取综合指标dsrks+rkrts在dj等级下的指标列表
        '''
        lis01, lis02 = dsrks[:num], dsrks[num:]
        lis11, lis12 = rkrts[:num], rkrts[num:]
        lis1 = [lis11[ik] for ik in range(num) if lis01[ik] == dj]
        lis2 = [lis12[ik] for ik in range(num) if lis02[ik] == dj]
        lis1.sort()
        lis2.sort()
        lis12 = np.unique(lis1+lis2)
        lis12.sort()
        return lis1, lis2, lis12

    def get_lis12_dsws(self, lis, num):
        '''单个指标列表lis, 按阴性数量num, 分成lis1, lis2, lis12'''
        lis1, lis2 = lis[:num], lis[num:]
        lis1.sort()  # 阴性
        lis2.sort()  # 阳性
        lis12 = np.unique(lis)
        lis12.sort()  # 全部数值
        return lis1, lis2, lis12

    def make_ranks(self, lis1, lis2, lis12, savefile=None, mode=0, n12m=False, lis0=False, mode0=0):
        '''
        单个指标分级
        lis0为空:
            按lis12分级;
            mode: 0按<数量, 1按按>=数量;
            n12m: [n1m, n2m]时, n1和n2分别减去数量; 为空时不减去数量;
        lis0=列表:
            按列表分级;
            mode0: 0左闭右开, 1左开右闭;
            注意: mode0=0时, lis0[0]为lis12最小, lis0[-1]大于lis12最大;
        '''
        pertlis = []
        if lis0:
            for ik in range(len(lis0)-1):
                if mode0:
                    v0 = lis0[ik+1]
                    n1 = sum(1 for x in lis1 if x > lis0[ik] and x <= v0)
                    n2 = sum(1 for x in lis2 if x > lis0[ik] and x <= v0)
                else:
                    v0 = lis0[ik]
                    n1 = sum(1 for x in lis1 if x >= v0 and x < lis0[ik+1])
                    n2 = sum(1 for x in lis2 if x >= v0 and x < lis0[ik+1])
                pertlis.append([v0, n1, n2, n1+n2, n2/(n1+n2+0.00001)])
        else:
            for v0 in lis12:
                n1 = sum(1 for x in lis1 if x < v0)  # <数量
                n2 = sum(1 for x in lis2 if x < v0)
                if mode:
                    n1, n2 = len(lis1)-n1, len(lis2)-n2  # >=数量
                if n12m:
                    n1 -= n12m[0]
                    n2 -= n12m[1]
                pertlis.append([v0, n1, n2, n1+n2, n2/(n1+n2+0.00001)])
        self.save_txt(pertlis, 'list', savefile)
