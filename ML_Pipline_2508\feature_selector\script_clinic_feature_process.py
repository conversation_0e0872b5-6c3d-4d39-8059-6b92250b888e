"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_clinic_feature_process
date: 2024/11/15 上午10:12
desc: 
"""
import os
import pickle

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from feature_selector.select_utils import (execute_selection_on_folds, get_data, stable_res_extract,
                                           remove_zero_variance_features, remove_multicollinearity, DataScaler)
from feature_selector.stats_vis import DimensionalityReductionVisualizer
from tqdm import tqdm
from feature_selector.feature_select import FeatureSelector,get_feature_composition
from sklearn.model_selection import cross_val_score
from sklearn.model_selection import train_test_split
from xgboost import XGBClassifier

# ==================临床特征处理与建模测试==========================

# 1. 准备不同的特征处理方案
def prepare_features(df, strategy='all'):
    X = df.copy()

    # 只进行类型转换，保留缺失值
    object_cols = X.select_dtypes(include=['object']).columns
    for col in object_cols:
        mask = X[col].notna()
        le = LabelEncoder()
        X.loc[mask, col] = X.loc[mask, col].astype(str)
        X.loc[mask, col] = le.fit_transform(X.loc[mask, col])
        X[col] = X[col].astype(float)

    if strategy == 'drop':
        X = X.drop(['clinic_symp', 'clinic_diabetes'], axis=1)
    if strategy == 'drop_most':
        X = X.drop(['clinic_symp', 'clinic_diabetes','clinic_hospital'], axis=1)
    if strategy == 'drop_hospital':
        X = X.drop(['clinic_hospital'], axis=1)
    return X


# 2. 模型验证
def evaluate_strategy(X, y, strategy):
    X_processed = prepare_features(X, strategy)

    # 划分训练测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X_processed, y, test_size=0.2, random_state=42
    )

    # XGBoost模型
    model = XGBClassifier(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=4,
        random_state=42
    )

    # 5折交叉验证
    scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')

    # 在测试集上评估
    model.fit(X_train, y_train)
    test_score = model.score(X_test, y_test)

    return {
        'cv_scores': scores,
        'cv_mean': scores.mean(),
        'cv_std': scores.std(),
        'test_score': test_score,
        'feature_importance': dict(zip(X_processed.columns, model.feature_importances_))
    }


if __name__ == '__main__':
    # 常规特征选择 --------------------------------------------------------
    params = {
        'seed': 42,
        'feature_num': 200,
        'data_id_path': './files/data/data_index/data_V1114/S42-F5.pkl',
        'all_features_path': './files/saved_features/feature_V241114_all_1690.pkl',
    }

    train_data, test_data, valid_data, train_valid_data, df_features = get_data(id_pkl=params['data_id_path'],
                                                                                features_pkl=params[
                                                                                    'all_features_path'],
                                                                                fold=0)
    fg = FeatureSelector(**params)

    selected_features_results, record_results = fg.run_stable_select()

    # 确认df_feature
    final_features_df = stable_res_extract(record_results, df_features, top_n=1000, vis=True,
                                           mean_weights_threshold=0.2, std_weights_threshold=1)
    print(final_features_df.shape)
    print(df_features.shape)

    # 选择后特征的组成成分占比分析 /保存被选中特征  -----------------------------------------------------------------
    get_feature_composition(final_features_df)
    # se_features = df_features[freq_on_folds.columns]
    with open('./files/saved_features/selected_features/features_V1114F5S42_boruta_top294.pkl', 'wb') as f:
        pickle.dump(final_features_df, f)

    # 临床特征处理 ------------------------------------------------------------------------------------------
    # 获取临床特征，加入 feature_V241114_all_1690 和 features_V1114F5S42_boruta_top294
    excel_path = "./files/data/data_index/data_V1114/诊断模型数据整理列20241114.xlsx"
    sheet_data = pd.read_excel(excel_path, sheet_name='20241114诊断建模名单含时刻点')

    excel_path = "./files/data/data_index/data_V1114/cons_plus/诊断模型数据12.3新增.xlsx"
    sheet_data = pd.read_excel(excel_path, sheet_name='总表')

    clinic_sheet = sheet_data[['心磁号', '性别', '年龄', '典型症状', '糖尿病', '医院']]


    # 重命名列并进行特征编码
    feature_mapping = {
        '心磁号': 'mcg_file',
        '性别': 'clinic_gender',
        '年龄': 'clinic_age',
        '典型症状': 'clinic_symp',
        '糖尿病': 'clinic_diabetes',
        '医院': 'clinic_hospital'
    }

    # 重命名列
    clinic_sheet = clinic_sheet.rename(columns=feature_mapping)

    # 性别编码 (通常 1-男, 0-女)
    clinic_sheet['clinic_gender'] = (clinic_sheet['clinic_gender'] == '男').astype(int)

    # 医院编码 (可以用Label Encoding或One-hot,视医院数量而定)
    from sklearn.preprocessing import LabelEncoder

    le = LabelEncoder()
    clinic_sheet['clinic_hospital'] = le.fit_transform(clinic_sheet['clinic_hospital'])

    # 获取医院编码映射关系
    hospital_mapping = dict(zip(le.classes_, le.transform(le.classes_)))
    print("医院编码对应关系：")
    for hospital, code in hospital_mapping.items():
        print(f"{hospital}: {code}")

    # 1. 查看年龄的基本统计信息
    age_stats = clinic_sheet['clinic_age'].describe()
    print("年龄统计信息：")
    print(age_stats)

    # 2. 查看年龄分布
    # import matplotlib.pyplot as plt
    #
    # plt.figure(figsize=(10, 5))
    # clinic_sheet['clinic_age'].hist(bins=30)
    # plt.show()

    # 3. 测试精度
    # 准备数据
    X = clinic_sheet.drop(['mcg_file'], axis=1)  # 特征矩阵，保留所有特征列
    X = X.set_index(clinic_sheet['mcg_file'])  # 将mcg_file设为索引

    # 处理y，确保与X对应
    y = df_features[['mcg_file', 'label']].set_index('mcg_file')

    # 确保X和y的样本对应
    common_mcg_files = X.index.intersection(y.index)
    X = X.loc[common_mcg_files]
    y = y.loc[common_mcg_files]['label']

    print(f"最终样本数量: {len(X)}")
    strategies = ['drop', 'all','drop_most','drop_hospital']
    results = {}

    for strategy in strategies:
        results[strategy] = evaluate_strategy(X, y, strategy)
        print(f"\n策略: {strategy}")
        print(f"交叉验证平均分数: {results[strategy]['cv_mean']:.4f} ± {results[strategy]['cv_std']:.4f}")
        print(f"测试集分数: {results[strategy]['test_score']:.4f}")
        print("\n特征重要性:")
        sorted_importance = dict(sorted(
            results[strategy]['feature_importance'].items(),
            key=lambda x: x[1],
            reverse=True
        ))
        for feat, imp in sorted_importance.items():
            print(f"{feat}: {imp:.4f}")

    # 4. 处理clinicsheet 并保存
    # 获取除mcg_file外的所有列
    feature_cols = [col for col in clinic_sheet.columns if col != 'mcg_file']
    object_tobe_chan_cols = [col for col in clinic_sheet.columns
                   if col != 'mcg_file' and
                   (clinic_sheet[col].dtype == 'object' or
                    clinic_sheet[col].dtype == 'string')]
    # 对每个特征列进行处理
    le = LabelEncoder()
    for col in object_tobe_chan_cols:
        # 先转换为字符串
        clinic_sheet[col] = clinic_sheet[col].astype(str)
        # 使用LabelEncoder编码
        clinic_sheet[col] = le.fit_transform(clinic_sheet[col])
        # 转换为float类型
        clinic_sheet[col] = clinic_sheet[col].astype(float)

    # 将处理后的特征列并入feature_V241114_all_1690
    merged_df = df_features.merge(
        clinic_sheet,
        on='mcg_file',
        how='left'
    )
    print(merged_df.shape)

    with open('./files/saved_features/feature_V241119_all_clinic_1860.pkl', 'wb') as f:
        pickle.dump(merged_df, f)

    # 获取final_features_df中的特征列
    final_features_cols = final_features_df.columns.tolist()
    if 'mcg_file' in final_features_cols:
        final_features_cols.remove('mcg_file')
    if 'label' in final_features_cols:
        final_features_cols.remove('label')
    print(len(final_features_cols))
    # 获取clinic_sheet的特征列
    clinic_features = feature_cols

    # 创建子表，包含所需的特征列
    subset_cols = ['mcg_file', 'label'] + final_features_cols + clinic_features
    subset_df = merged_df[subset_cols].copy()
    print(subset_df.shape)

    with open('./files/saved_features/selected_features/features_V1211F5S42_boruta_cons_top1064_clinic5.pkl', 'wb') as f:
        pickle.dump(subset_df, f)



    # 纯粹clinic特征
    subset_clinic_cols = ['mcg_file', 'label']  + clinic_features
    subset_clinic_cols = merged_df[subset_clinic_cols].copy()
    print(subset_clinic_cols.shape)

    with open('./files/saved_features/selected_features/features_V1114F5S42_clinic5.pkl', 'wb') as f:
        pickle.dump(subset_clinic_cols, f)

