'''
@Project ：ML_Pipline 
@File    ：models.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/7/29 11:18 
@Discribe：
    改自qdg同学的模型代码; 定义模型类及相关工具

'''
import json
import math
import pickle

# 可视化
import matplotlib.pyplot as plt
import pandas as pd
from imblearn.metrics import specificity_score
from lightgbm import LGBMClassifier
from sklearn.metrics import balanced_accuracy_score
from xgboost import XGBClassifier

from model_trainer.losses import Focal_Binary_Loss, CRF_Binary_Loss, GHM_Binary_Loss, OHEM_Binary_Loss
# import shap
from model_trainer.metrics_self import *

columns_to_drop = ['filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x', 'label_y']


def load_config(config_file, environment='default'):
    """
    Load configuration from a JSON file.
    :param config_file:
    :param environment:
    :return:
    """
    with open(config_file, 'r') as file:
        config = json.load(file)
        # 根据传入的环境参数获取相应的配置
        return config[environment]


def print_metrics(metrics_default, metrics_best_threshold, best_threshold, model_name):
    '''
    Args:
        metrics_default: 模型初始结果（未调参）
        metrics_best_threshold:模型调整阈值后（最佳阈值）
        best_threshold:（最佳阈值）
        model_name:模型名称
    Returns:

    '''
    total_length = 40
    threshold_str = f"best_threshold ({round(best_threshold, 3)})"
    combined_string = f"{model_name}"
    centered_string = combined_string.center(total_length)
    out1 = f"AUC:{round(metrics_default['roc_auc'], 3)} , " \
           f"ACC:{round(metrics_default['accuracy'], 3)} , " \
           f"PRE:{round(metrics_default['precision'], 3)}"

    out2 = f"RCL:{round(metrics_default['recall'], 3)} , " \
           f"F_1:{round(metrics_default['f1_score'], 3)} , " \
           f"SPE:{round(metrics_default['specificity'], 3)}"

    out3 = f"AUC:{round(metrics_best_threshold['roc_auc'], 3)} , " \
           f"ACC:{round(metrics_best_threshold['accuracy'], 3)} , " \
           f"PRE:{round(metrics_best_threshold['precision'], 3)}"

    out4 = f"RCL:{round(metrics_best_threshold['recall'], 3)} , " \
           f"F_1:{round(metrics_best_threshold['f1_score'], 3)} , " \
           f"SPE:{round(metrics_best_threshold['specificity'], 3)}"

    print('|----------------------------------------|')
    print(f"|{centered_string}|")
    print('|----------------------------------------|')
    print(f"|{'default'.center(total_length)}|")
    print(f"|{out1.center(total_length)}|")
    print(f"|{out2.center(total_length)}|")
    print(f"|{threshold_str.center(total_length)}|")
    print(f"|{out3.center(total_length)}|")
    print(f"|{out4.center(total_length)}|")
    print('|----------------------------------------|')
    global ALL_metries
    ALL_metries = ALL_metries + f'''{centered_string},default,{round(metrics_default['roc_auc'], 3)},{round(metrics_default['accuracy'], 3)},{round(metrics_default['precision'], 3)},{round(metrics_default['recall'], 3)},{round(metrics_default['f1_score'], 3)},{round(metrics_default['specificity'], 3)},
    {threshold_str},{round(metrics_best_threshold['roc_auc'], 3)},{round(metrics_best_threshold['accuracy'], 3)},{round(metrics_best_threshold['precision'], 3)},{round(metrics_best_threshold['recall'], 3)},{round(metrics_best_threshold['f1_score'], 3)},{round(metrics_best_threshold['specificity'], 3)}\n'''


def get_metrics(y, pred, prob):
    try:
        if len(prob.shape) == 2:
            prob = prob[:, 1]
        # 1. 计算评估指标
        auc_caculate = format(roc_auc_score(y, prob), '.3f')
        acc_caculate = format(accuracy_score(y, pred), '.3f')
        rec_caculate = format(recall_score(y, pred), '.3f')
        spec_caculate = format(
            (confusion_matrix(y, pred)[0, 0] / (confusion_matrix(y, pred)[0, 0] + confusion_matrix(y, pred)[0, 1])),
            '.3f')
        gmean_caculate = format(geometric_mean_score(y, pred), '.3f')
    except:
        print("提供的标签仅包含一类,如果不是0820后版本数据，请检查！")
        return 0, 0, 0, 0, 0
    return auc_caculate, acc_caculate, rec_caculate, spec_caculate, gmean_caculate


def get_metric_value(y_true, y_pred, metric):
    if metric == 'roc_auc' or metric == 'auc':
        return roc_auc_score(y_true, y_pred)
    elif metric == 'prc_auc':
        return average_precision_score(y_true, y_pred)
    elif metric == 'accuracy' or metric == 'acc':
        return accuracy_score(y_true, y_pred)
    elif metric == 'precision':
        return precision_score(y_true, y_pred)
    elif metric == 'recall' or metric == 'rec':
        return recall_score(y_true, y_pred)
    elif metric == 'f1':
        return f1_score(y_true, y_pred)
    elif metric == 'fb':
        beta = 1.25
        # print("beta:",beta)
        return fbeta_score(y_true, y_pred, beta=beta)
    elif metric == 'mcc':
        return matthews_corrcoef(y_true, y_pred)
    elif metric == 'G-mean' or metric == 'g-mean':
        return geometric_mean_score(y_true, y_pred)
    elif metric == 'bacc':
        return balanced_accuracy_score(y_true, y_pred)
    elif metric == 'gcc':
        return math.sqrt(geometric_mean_score(y_true, y_pred) * accuracy_score(y_true, y_pred))
    elif metric == 'sen-spe':
        rec = recall_score(y_true, y_pred)
        spec = specificity_score(y_true, y_pred)
        metrics = rec + spec - abs(rec - spec)
        return metrics
    else:
        raise ValueError("Invalid metric specified.")


def search_best_threshold(y_true, y_prob, metric='g-mean'):
    '''
    根据不同的评估指标，搜索最佳阈值
    :param y_true: 真实标签
    :param y_prob: 预测概率
    :param metric: 评估指标
    :return: 最佳阈值
    '''
    thresholds = np.linspace(0, 1, 100)
    best_threshold = 0.5
    best_metric = 0
    for threshold in thresholds:
        y_pred = (y_prob >= threshold).astype(int)
        metric_value = get_metric_value(y_true, y_pred, metric)

        if metric_value > best_metric:
            best_metric = metric_value
            best_threshold = threshold
    return best_threshold


def remap_probabilities(y_pred_prob, best_threshold):
    """
    将预测概率重新映射到一个更均匀的区间。

    参数：
    y_pred_prob：预测概率值数组
    best_threshold：最佳阈值

    返回：
    remapped_prob：重新映射后的概率值数组
    """

    # 获取小于等于最佳阈值的一半部分
    lower_half = y_pred_prob[y_pred_prob <= best_threshold]
    lower_half = (lower_half / best_threshold) * 0.5

    # 获取大于最佳阈值的一半部分
    upper_half = y_pred_prob[y_pred_prob > best_threshold]
    upper_half = 0.5 + ((upper_half - best_threshold) / (1 - best_threshold) * 0.5)

    # 创建一个与y_pred_prob相同大小的全零数组
    remapped_prob = np.zeros_like(y_pred_prob)

    # 将小于等于最佳阈值的概率映射到较小的一半区域
    remapped_prob[y_pred_prob <= best_threshold] = lower_half

    # 将大于最佳阈值的概率映射到较大的一半区域
    remapped_prob[y_pred_prob > best_threshold] = upper_half

    return remapped_prob


def plot_probability_density(data):
    # 确保所有的数据都在0和1之间
    if np.any((data < 0) | (data > 1)):
        raise ValueError("所有数据点必须在0和1之间。")
    # 设置箱子的边界
    bins = np.arange(0, 1.1, 0.1)
    # 计算概率密度（而不是简单的计数），必须设置density=True
    plt.hist(data, bins=bins, density=True, alpha=0.75, color='blue', edgecolor='black')
    # 设置图的标题和坐标轴标签
    plt.title('Probability Density')
    plt.xlabel('Value')
    plt.ylabel('Density')
    # 显示网格
    plt.grid(True)
    # 显示图形
    plt.show()


def load_models_and_predict(input_data, models_thresholds):
    predictions = []
    # 假设 df 是你的 DataFrame
    data_input = input_data.copy()
    data_input['RT_cosine_similarity'] = data_input['RT_cosine_similarity'].astype(float)  # 将列转换为 float 类型
    for item in models_thresholds:
        model = item["model"]
        threshold = item["best_threshold"]
        proba = model.predict_proba(data_input)[:, 1]
        # print(proba)#
        proba = remap_probabilities(proba, threshold)

        # print(proba)#
        predictions.append(proba)

    np_array = np.array(predictions)
    average_list = np.mean(np_array, axis=0)

    return average_list


def plot_auc_curves(test, val):
    """

    :param test:
    :param val:
    """
    # Convert string values to floats
    test_floats = [float(i) for i in test]
    val_floats = [float(i) for i in val]

    # Generate epochs numbers (assuming continuous epochs)
    epochs = list(range(1, len(test) + 1))

    # Plotting the curves
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, test_floats, label='Test acc', color='blue')
    plt.plot(epochs, val_floats, label='Validation acc', color='red')

    # Adding titles and labels
    plt.title('Test vs Valid acc over Epochs')
    plt.xlabel('Epoch')
    plt.ylabel('AUC')
    plt.legend()

    # Show the plot
    plt.show()




def run_test_model(model_test, x, y, dataset=None, threshold=0.5, verbose=1):
    # 获取原始预测概率
    y_prob = model_test.predict_proba(x)[:, 1]
    y_pred = np.where(y_prob >= threshold, 1, 0)
    auc, acc, rec, spec, gmean = get_metrics(y, y_pred, y_prob)
    if verbose:
        print(
            f'{dataset} -> auc: {auc}, acc: {acc}, rec: {rec}, spec: {spec}, gmean: {gmean}, threshold: {np.round(threshold, 2)} 丨')
    result_list = {'auc': auc, 'acc': acc, 'rec': rec, 'spec': spec, 'gmean': gmean, 'threshold': threshold}

    return y_prob, acc, result_list


def load_and_prepare_data(id_pkl, features_pkl, selected_features_pkl, selected_feature_list=None, fold=0):
    with open(id_pkl, "rb") as f:
        id_df = pickle.load(f)
    with open(features_pkl, "rb") as f:
        features_df = pickle.load(f)
    with open(selected_features_pkl, "rb") as f:
        selected_features_df = pickle.load(f)

    # 如果没有列表传入 则去文件读取。
    if selected_feature_list is None:
        selected_columns = selected_features_df.columns
    else:
        selected_columns = selected_feature_list
    features_df = features_df[selected_columns.tolist() + [
        'mcg_file'] if 'mcg_file' not in selected_columns else selected_columns.tolist()]

    # 将指定列转换为float32
    features_df = features_df.drop(columns=columns_to_drop, errors='ignore')
    columns_to_convert = features_df.drop(columns=['mcg_file', 'label'], errors='ignore')
    coltmp = columns_to_convert.select_dtypes(exclude=[np.number])
    if len(coltmp.columns) > 0:
        raise Exception(f'feature column {coltmp.columns} is not number type')
    columns_to_convert = columns_to_convert.select_dtypes(include=[np.number]).select_dtypes(
        exclude=[np.float32]).columns
    features_df[columns_to_convert] = features_df[columns_to_convert].astype(np.float32)

    # 分析数据集结构
    folder_data = id_df[fold]
    datasets = {
        'train': folder_data.get('train', folder_data['train_valid']),
        'valid': folder_data.get('valid', folder_data.get('val', folder_data['test'])),
        'test': folder_data['test'],
        'train_valid': folder_data['train_valid']
    }

    # 获取数据和标签
    results = {}
    for key, data in datasets.items():
        ids = data[:, 0].tolist()
        dataset = features_df[features_df['mcg_file'].isin(ids)].copy()  # 创建明确的副本

        missing_ids = set(ids) - set(dataset['mcg_file'])
        if missing_ids:
            print(f"警告: 在key={key}中，以下ID在features中未找到: {missing_ids}")

        labels_dict = {row[0]: row[1] for row in data}
        dataset.loc[:, 'label'] = dataset['mcg_file'].map(labels_dict)  # 使用.loc
        results[key] = dataset

    return results['train'], results['test'], results['valid'], results['train_valid']


def load_and_prepare_tmp_outer_data(id_pkl, features_pkl, selected_features_pkl, fold=0):
    tmp_file = pd.read_excel(id_pkl)
    with open(features_pkl, "rb") as f:
        features_df = pickle.load(f)
    with open(selected_features_pkl, "rb") as f:
        selected_features_df = pickle.load(f)

    # # 如果没有列表传入 则去文件读取。
    # if selected_feature_list is None:
    #     selected_columns = selected_features_df.columns
    # else:
    #     selected_columns = selected_feature_list
    # features_df = features_df[selected_columns.tolist() + [
    #     'mcg_file'] if 'mcg_file' not in selected_columns else selected_columns.tolist()]
    features_df = features_df[selected_features_df.columns.tolist() + [
        'mcg_file'] if 'mcg_file' not in selected_features_df.columns else selected_features_df.columns.tolist()]

    # 将指定列转换为float32
    features_df = features_df.drop(columns=columns_to_drop, errors='ignore')
    columns_to_convert = features_df.drop(columns=['mcg_file', 'label'], errors='ignore')
    coltmp = columns_to_convert.select_dtypes(exclude=[np.number])
    if len(coltmp.columns) > 0:
        raise Exception(f'feature column {coltmp.columns} is not number type')
    columns_to_convert = columns_to_convert.select_dtypes(include=[np.number]).select_dtypes(
        exclude=[np.float32]).columns
    features_df[columns_to_convert] = features_df[columns_to_convert].astype(np.float32)

    # 获取数据和标签
    test_ids = tmp_file['ID'].tolist()  # 临时文件的ID列 注意根据实际情况修改
    test_df = features_df[features_df['mcg_file'].isin(test_ids)].copy()

    # 添加警告信息
    missing_test_ids = set(test_ids) - set(test_df['mcg_file'])
    if missing_test_ids:
        print(f"警告: 以下测试ID在features中未找到: {missing_test_ids}")

    test_df['label'] = 0

    # 检查是否所有的测试数据都被正确标记
    unlabeled_rows = test_df[test_df['label'].isna()]
    if not unlabeled_rows.empty:
        print(f"警告: {len(unlabeled_rows)}行测试数据未被标记。这可能是因为labels_dict中缺少相应的mcg_file。")
        print("未标记的mcg_file:", unlabeled_rows['mcg_file'].tolist())

    return test_df


def load_and_prepare_outer_data(id_pkl, features_pkl, selected_features_pkl, fold=0):
    with open(id_pkl, "rb") as f:
        id_df = pickle.load(f)
    with open(features_pkl, "rb") as f:
        features_df = pickle.load(f)
    with open(selected_features_pkl, "rb") as f:
        selected_features_df = pickle.load(f)

    # # 如果没有列表传入 则去文件读取。
    # if selected_feature_list is None:
    #     selected_columns = selected_features_df.columns
    # else:
    #     selected_columns = selected_feature_list
    # features_df = features_df[selected_columns.tolist() + [
    #     'mcg_file'] if 'mcg_file' not in selected_columns else selected_columns.tolist()]
    features_df = features_df[selected_features_df.columns.tolist() + [
        'mcg_file'] if 'mcg_file' not in selected_features_df.columns else selected_features_df.columns.tolist()]

    # 将指定列转换为float32
    features_df = features_df.drop(columns=columns_to_drop, errors='ignore')
    columns_to_convert = features_df.drop(columns=['mcg_file', 'label'], errors='ignore')
    coltmp = columns_to_convert.select_dtypes(exclude=[np.number])
    if len(coltmp.columns) > 0:
        raise Exception(f'feature column {coltmp.columns} is not number type')
    columns_to_convert = columns_to_convert.select_dtypes(include=[np.number]).select_dtypes(
        exclude=[np.float32]).columns
    features_df[columns_to_convert] = features_df[columns_to_convert].astype(np.float32)

    # 获取测试数据集
    try:
        folder_data = id_df[fold]
        test_key = 'train_test' if 'train_test' in folder_data else 'test'
        test_data = folder_data[test_key]
    except:  # 0820以后的valid数据 后期再改。
        test_data = id_df

    # 获取数据和标签
    test_ids = test_data[:, 0].tolist()
    test_df = features_df[features_df['mcg_file'].isin(test_ids)].copy()

    # 添加警告信息
    missing_test_ids = set(test_ids) - set(test_df['mcg_file'])
    if missing_test_ids:
        print(f"警告: 以下测试ID在features中未找到: {missing_test_ids}")

    labels_dict = {row[0]: row[1] for row in test_data}
    test_df['label'] = test_df['mcg_file'].map(labels_dict)

    # 检查是否所有的测试数据都被正确标记
    unlabeled_rows = test_df[test_df['label'].isna()]
    if not unlabeled_rows.empty:
        print(f"警告: {len(unlabeled_rows)}行测试数据未被标记。这可能是因为labels_dict中缺少相应的mcg_file。")
        print("未标记的mcg_file:", unlabeled_rows['mcg_file'].tolist())

    return test_df


def get_model_predict_results(model, x_train=None, x_valid=None, x_test=None, y_train=None, y_valid=None, y_test=None,
                              best_threshold=0.5, valid=False, search_method='g-mean'):
    train_result, valid_result, valid_result_threshold, result, best_threshold = None, None, None, None, best_threshold
    if x_train is not None:
        # 训练数据预测----------------------------------------------------------
        y_prob, acc, train_result = run_test_model(model, x_train, y_train, dataset='train ')
        y_pred = np.where(y_prob >= 0.5, 1, 0)

    if x_valid is not None:
        # 验证数据预测----------------------------------------------------------
        y_prob, acc, valid_result = run_test_model(model, x_valid, y_valid, dataset='val ')
        # 搜索最佳阈值后测试
        if not valid:
            search_metric = search_method  # 'auc'#'sen-spe'
            print('search best valid threshold use ' + search_metric + '...')
            best_threshold = search_best_threshold(y_valid, y_prob, metric=search_metric)
        y_prob, acc, valid_result_threshold = run_test_model(model, x_valid, y_valid, dataset='val ',
                                                             threshold=best_threshold)
        y_pred = np.where(y_prob >= best_threshold, 1, 0)

    if x_test is not None:
        # 测试数据预测-----------------------------------------------------------
        print('test model with best threshold searched by valid(or default 0.5)...')
        y_prob, acc, result = run_test_model(model, x_test, y_test, dataset='test ', threshold=best_threshold)
        if not valid:
            search_metric = search_method
            print('search best test threshold use ' + search_metric + '...')
            best_threshold = search_best_threshold(y_test, y_prob, metric=search_method)
            y_prob, acc, result = run_test_model(model, x_test, y_test, dataset='test ', threshold=best_threshold)
        y_pred = np.where(y_prob >= best_threshold, 1, 0)
        valid_result_threshold = result  # 如果存在test集合，则用test集合的结果

    result_dict = {'train_result': train_result, 'valid_result': valid_result,
                   'valid_result_threshold': valid_result_threshold, 'test_result': result,
                   'test_prob': y_prob, 'test_y': y_test}
    # 如果train_result/valid_result/valid_result_threshold全部都是none则返回result

    if not train_result and not valid_result and not valid_result_threshold:
        return {'test_result': result}

    return result_dict


def plot_folds_results(results, mode='valid_result_threshold'):
    # Parameters to plot
    params = ['auc', 'acc', 'rec', 'spec', 'gmean']

    # Organize data by fold, not by metric
    fold_data = [[] for _ in range(len(results))]  # List of lists for each fold

    # Extracting data for each fold
    for i, result in enumerate(results):
        for param in params:
            try:
                value = result[mode][param]
            except KeyError as e:
                # 打印可用的 mode 选项
                print("可用的 mode 选项有：", list(result.keys()))
                if mode in result:
                    # 如果 mode 是有效的，但 param 有误
                    print("模式 '{mode}' 下可用的 param 选项有：", list(result[mode].keys()))
                else:
                    # 如果 mode 无效
                    print("给定的模式 '{mode}' 不在结果字典中。")
                raise KeyError("无法访问 result[{mode}][{param}]: {e}")
            fold_data[i].append(float(value) if isinstance(value, str) else value)

    # Calculate averages for each parameter
    averages = []
    for i in range(len(params)):
        param_values = [fold[i] for fold in fold_data]  # Get all folds' values for parameter i
        averages.append(sum(param_values) / len(param_values))

    # Plotting
    plt.figure(figsize=(10, 6))
    for i, fold in enumerate(fold_data):
        plt.plot(params, fold, linestyle='-', label=f'Fold {i + 1}', linewidth=1, alpha=0.5)

    # Plot average values
    plt.plot(params, averages, marker='o', linestyle='-', linewidth=2, color='black', label='Average')

    plt.title('Performance Metrics Across Folds')
    plt.xlabel('Metric')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    plt.show()
    return averages


def process_safe_features(unsafe_data, mode='xgb'):
    """
    获取无异常字符的安全特征名以适配lgbm
    :param train_data:
    :return:
    """
    safe_data = unsafe_data.copy().drop(columns=['mcg_file', 'label'], inplace=False)
    if mode == 'xgb':
        return safe_data.fillna(-1) # todo 注意为了适配smote 加了对nan值的处理  请谨慎使用
    elif mode == 'test':
        from sklearn.preprocessing import StandardScaler
        safe_data = safe_data.fillna(safe_data.median())
        scaler = StandardScaler()
        safe_data = scaler.fit_transform(safe_data)
    elif mode == 'rf':
        safe_data = safe_data.fillna(-1)
    else:
        # 查看原始的特征名称
        print("原始特征名称:", safe_data.columns)
        # 替换掉特殊字符
        safe_data.columns = [
            col.replace('{', '').replace('}', '').replace('[', '').replace(']', '').replace(':', '').replace('"', '')
            for col in safe_data.columns]
        # 查看修改后的特征名称
        print("修改后的特征名称:", safe_data.columns)
    return safe_data




class Models:
    def __init__(self):
        self.model = None
        self.model_name = None
        self.params = None

    def xgb_model(self):
        if not self.params:
            print("使用默认超参数")
            self.params = {
                'n_estimators': 307,
                'learning_rate': 0.042971176572795665,
                'max_depth': 7,
                'min_child_weight': 4,
                'colsample_bytree': 0.7736469007483583,
                'subsample': 0.7223757991618465,
                'gamma': 0.29891410080707026,
                'lambda': 3.681497934850562,
                'alpha': 1.5776089667314617,
                'scale_pos_weight': 1.026574566398537,
                'max_delta_step': 11
            }

        model = XGBClassifier(**self.params)
        self.model = model
        self.model_name = 'xgb'

    def lgbm_model(self):
        if not self.params:
            self.params = {
                'n_estimators': 124,
                'learning_rate': 0.18832239476360674,
                'num_leaves': 10,
                'max_depth': 7,
                'min_data_in_leaf': 67,
                'lambda_l1': 0.15423704930691365,
                'lambda_l2': 0.9530470216588179,
                'min_gain_to_split': 0.5918118996328543,
                'subsample': 0.3372000359082652,
                'colsample_bytree': 0.6595258851650223,
                'feature_fraction': 0.5282285495478233,
                'min_child_weight': 8.861462388189988
            }

        model = LGBMClassifier(**self.params, verbose=-1)
        self.model = model
        self.model_name = 'lgbm'

    def rfc_model(self):
        params = {
            "n_estimators": 3,
            "max_depth": 4,
            "random_state": 2023,
        }
        model = RandomForestClassifier(**params)
        self.model = model
        self.model_name = 'rfc'

    # def tabpfn_model(self):
    #     model = TabPFNClassifier(device='cpu', N_ensemble_configurations=32)
    #     self.model = model
    #     self.model_name = 'TabPFN'

    def rf_model(self):
        params = {'n_estimators': 120, 'max_depth': 7, 'min_samples_split': 18, 'min_samples_leaf': 8}
        model = RandomForestClassifier(**params)
        self.model = model
        self.model_name = 'rf'


class FocalXGBClassifier:
    def __init__(self, focal_alpha=0.25, focal_gamma=2.0, **params):
        self.params = params
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        self.model = None
        self.classes_ = np.array([0, 1])  # Assuming binary classification
        self.use_sigmoid = True

    def fit(self, X, y, eval_set=None, verbose=True, sample_weight=None, num_boost_round=307):
        dtrain = xgb.DMatrix(X, label=y, weight=sample_weight)
        evals = []
        if eval_set:
            for i, (X_val, y_val) in enumerate(eval_set):
                evals.append((xgb.DMatrix(X_val, label=y_val), f'eval{i}'))

        def focal_loss_obj(preds, dtrain):
            labels = dtrain.get_label()
            focal_loss = Focal_Binary_Loss(gamma_indct=self.focal_gamma, alpha=self.focal_alpha)
            grad, hess = focal_loss.focal_binary_object(preds, labels)
            return grad, hess

        self.model = xgb.train(
            self.params, dtrain, num_boost_round=num_boost_round, evals=evals,
            obj=focal_loss_obj, verbose_eval=verbose
        )

    def predict_proba(self, X):
        dtest = xgb.DMatrix(X)
        raw_scores = self.model.predict(dtest)

        if self.use_sigmoid:
            def sigmoid(x):
                mask = x >= 0
                result = np.zeros_like(x, dtype=np.float64)
                exp_nx = np.exp(-x[mask])
                result[mask] = 1 / (1 + exp_nx)
                exp_x = np.exp(x[~mask])
                result[~mask] = exp_x / (1 + exp_x)
                return result

            proba = sigmoid(raw_scores)
            proba = np.clip(proba, 1e-6, 1 - 1e-6)
        else:
            proba = raw_scores

        return np.vstack((1 - proba, proba)).T

    def predict(self, X):
        proba = self.predict_proba(X)
        return np.argmax(proba, axis=1)

    def set_params(self, **params):
        self.params.update(params)
        return self

    def get_params(self, deep=True):
        return self.params


class CRFXGBClassifier_old:
    """使用CRF Loss的XGBoost分类器，支持样本权重调节"""

    def __init__(self, sigma: float = 1.0, epsilon: float = 1e-6, scale_pos_weight: float = None, **params):
        self.params = params
        self.sigma = sigma
        self.epsilon = epsilon
        self.scale_pos_weight = scale_pos_weight
        self.model = None
        self.classes_ = np.array([0, 1])
        self.loss_fn = CRF_Binary_Loss(sigma=sigma, epsilon=epsilon)
        self.use_sigmoid = True

    def _compute_sample_weight(self, y):
        """计算样本权重"""
        if self.scale_pos_weight is not None:
            # 使用指定的scale_pos_weight
            sample_weight = np.ones_like(y, dtype=np.float32)
            sample_weight[y == 1] = 1# self.scale_pos_weight 保留不使用权重
        else:
            class_weights = compute_class_weight('balanced', classes=np.unique(y), y=y)
            sample_weight = np.ones_like(y, dtype=np.float32)
            for i, weight in enumerate(class_weights):
                sample_weight[y == i] = weight
        return sample_weight

    def fit(self, X, y, eval_set=None, verbose=True, sample_weight=None, num_boost_round=307):
        # 如果没有提供sample_weight，则计算样本权重
        if sample_weight is None:
            pass
            # sample_weight = self._compute_sample_weight(y)

        # 更新XGBoost参数
        if self.scale_pos_weight is not None:
            self.params['scale_pos_weight'] = self.scale_pos_weight

        dtrain = xgb.DMatrix(X, label=y, weight=sample_weight)
        evals = []
        if eval_set:
            for i, (X_val, y_val) in enumerate(eval_set):
                # 对验证集也应用相同的权重计算
                val_weight = self._compute_sample_weight(y_val)
                evals.append((xgb.DMatrix(X_val, label=y_val, weight=val_weight), f'eval{i}'))

        def crf_obj(preds, dtrain):
            labels = dtrain.get_label()
            return self.loss_fn.crf_binary_object(preds, labels)

        self.model = xgb.train(
            self.params,
            dtrain,
            num_boost_round=num_boost_round,
            evals=evals,
            obj=crf_obj,
            verbose_eval=verbose,
        )

        return self

    def predict_proba(self, X):
        dtest = xgb.DMatrix(X)
        raw_scores = self.model.predict(dtest)

        if self.use_sigmoid:
            def sigmoid(x):
                mask = x >= 0
                result = np.zeros_like(x, dtype=np.float64)
                exp_nx = np.exp(-x[mask])
                result[mask] = 1 / (1 + exp_nx)
                exp_x = np.exp(x[~mask])
                result[~mask] = exp_x / (1 + exp_x)
                return result

            proba = sigmoid(raw_scores)
            proba = np.clip(proba, 1e-6, 1 - 1e-6)
        else:
            proba = raw_scores

        return np.vstack((1 - proba, proba)).T

    def predict(self, X):
        proba = self.predict_proba(X)
        return np.argmax(proba, axis=1)


from sklearn.base import BaseEstimator, ClassifierMixin
from sklearn.utils.validation import check_X_y, check_array, check_is_fitted
from sklearn.utils.class_weight import compute_class_weight


class CRFXGBClassifier(BaseEstimator, ClassifierMixin):
    """使用CRF Loss的XGBoost分类器，支持样本权重调节 暂时不支持nan输入 """
    def __init__(self, sigma: float = 1.0, epsilon: float = 1e-6,
                 scale_pos_weight: float = None, num_boost_round: int = 307,
                 **params):
        self.sigma = sigma
        self.epsilon = epsilon
        self.scale_pos_weight = scale_pos_weight
        self.params = params
        self.num_boost_round = num_boost_round
        self.classes_ = np.array([0, 1])
        self.loss_fn = CRF_Binary_Loss(sigma=sigma, epsilon=epsilon)
        self.use_sigmoid = True
        self.model = None

    def _compute_sample_weight(self, y):
        """计算样本权重"""
        if self.scale_pos_weight is not None:
            sample_weight = np.ones_like(y, dtype=np.float32)
            sample_weight[y == 1] = 1
        else:
            class_weights = compute_class_weight('balanced', classes=np.unique(y), y=y)
            sample_weight = np.ones_like(y, dtype=np.float32)
            for i, weight in enumerate(class_weights):
                sample_weight[y == i] = weight
        return sample_weight

    def fit(self, X, y, eval_set=None, verbose=True, sample_weight=None):
        # 检查输入数据
        X, y = check_X_y(X, y)

        # 设置classes_属性
        self.classes_ = np.unique(y)

        # 如果没有提供sample_weight，则计算样本权重
        if sample_weight is None:
            pass
            # sample_weight = self._compute_sample_weight(y)

        # 更新XGBoost参数
        if self.scale_pos_weight is not None:
            self.params['scale_pos_weight'] = self.scale_pos_weight

        dtrain = xgb.DMatrix(X, label=y, weight=sample_weight)
        evals = []
        if eval_set:
            for i, (X_val, y_val) in enumerate(eval_set):
                X_val = check_array(X_val)
                val_weight = self._compute_sample_weight(y_val)
                evals.append((xgb.DMatrix(X_val, label=y_val, weight=val_weight), f'eval{i}'))

        def crf_obj(preds, dtrain):
            labels = dtrain.get_label()
            return self.loss_fn.crf_binary_object(preds, labels)

        self.model = xgb.train(
            self.params,
            dtrain,
            num_boost_round=self.num_boost_round,
            evals=evals,
            obj=crf_obj,
            verbose_eval=verbose,
        )

        return self

    def predict_proba(self, X):
        # 检查模型是否已训练
        check_is_fitted(self)

        # 检查输入数据
        X = check_array(X)

        dtest = xgb.DMatrix(X)
        raw_scores = self.model.predict(dtest)

        if self.use_sigmoid:
            def sigmoid(x):
                mask = x >= 0
                result = np.zeros_like(x, dtype=np.float64)
                exp_nx = np.exp(-x[mask])
                result[mask] = 1 / (1 + exp_nx)
                exp_x = np.exp(x[~mask])
                result[~mask] = exp_x / (1 + exp_x)
                return result

            proba = sigmoid(raw_scores)
            proba = np.clip(proba, 1e-6, 1 - 1e-6)
        else:
            proba = raw_scores

        return np.vstack((1 - proba, proba)).T

    def predict(self, X):
        # 检查模型是否已训练
        check_is_fitted(self)

        # 检查输入数据
        X = check_array(X)

        proba = self.predict_proba(X)
        return np.argmax(proba, axis=1)

    def get_params(self, deep=True):
        """获取模型参数，scikit-learn接口要求"""
        params = {
            'sigma': self.sigma,
            'epsilon': self.epsilon,
            'scale_pos_weight': self.scale_pos_weight,
            'num_boost_round': self.num_boost_round,
        }
        params.update(self.params)
        return params

    def set_params(self, **parameters):
        """设置模型参数，scikit-learn接口要求"""
        for parameter, value in parameters.items():
            if parameter in ['sigma', 'epsilon', 'scale_pos_weight', 'num_boost_round']:
                setattr(self, parameter, value)
            else:
                self.params[parameter] = value
        return self


class CustomXGBClassifier:
    """
    支持多种损失函数的XGBoost分类器
    """

    def __init__(self, loss_type='ghm', loss_params=None, **params):
        self.params = params
        self.loss_type = loss_type
        self.loss_params = loss_params or {}
        self.model = None
        self.classes_ = np.array([0, 1])
        self.use_sigmoid = True

        # 初始化损失函数
        if loss_type == 'ghm':
            self.loss_fn = GHM_Binary_Loss(**self.loss_params)
        elif loss_type == 'ohem':
            self.loss_fn = OHEM_Binary_Loss(**self.loss_params)
        else:
            raise ValueError(f"Unknown loss type: {loss_type}")

    def fit(self, X, y, eval_set=None, verbose=True, num_boost_round=307):
        dtrain = xgb.DMatrix(X, label=y)
        evals = []
        if eval_set:
            for i, (X_val, y_val) in enumerate(eval_set):
                evals.append((xgb.DMatrix(X_val, label=y_val), f'eval{i}'))

        def custom_obj(preds, dtrain):
            labels = dtrain.get_label()
            if self.loss_type == 'ghm':
                return self.loss_fn.ghm_binary_object(preds, labels)
            else:  # ohem
                return self.loss_fn.ohem_binary_object(preds, labels)

        self.model = xgb.train(
            self.params, dtrain,
            num_boost_round=num_boost_round,
            evals=evals,
            obj=custom_obj,
            verbose_eval=verbose
        )

    def predict_proba(self, X):
        dtest = xgb.DMatrix(X)
        raw_scores = self.model.predict(dtest)

        if self.use_sigmoid:
            def sigmoid(x):
                mask = x >= 0
                result = np.zeros_like(x, dtype=np.float64)
                exp_nx = np.exp(-x[mask])
                result[mask] = 1 / (1 + exp_nx)
                exp_x = np.exp(x[~mask])
                result[~mask] = exp_x / (1 + exp_x)
                return result

            proba = sigmoid(raw_scores)
            proba = np.clip(proba, 1e-6, 1 - 1e-6)
        else:
            proba = raw_scores

        return np.vstack((1 - proba, proba)).T

    def predict(self, X):
        proba = self.predict_proba(X)
        return np.argmax(proba, axis=1)

import numpy as np
import xgboost as xgb
from sklearn.metrics import accuracy_score
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.base import clone, BaseEstimator, ClassifierMixin
from sklearn.model_selection import StratifiedKFold

class CustomStackingClassifier(BaseEstimator, ClassifierMixin):
    """
    自定义堆叠分类器，只对支持样本权重的基础分类器应用权重
    """

    def __init__(self, estimators, final_estimator=None, cv=5, n_jobs=-1, verbose=0):
        self.estimators = estimators
        self.final_estimator = final_estimator
        self.cv = cv
        self.n_jobs = n_jobs
        self.verbose = verbose
        self.named_estimators_ = dict(estimators)
        self.estimators_ = [clone(est) for _, est in estimators]
        self.final_estimator_ = clone(final_estimator) if final_estimator else LogisticRegression()
        self.classes_ = None

    def fit(self, X, y, sample_weight=None):
        # 记录类别
        self.classes_ = np.unique(y)
        n_classes = len(self.classes_)

        # 准备用于元学习器的数据
        cv = StratifiedKFold(n_splits=self.cv)
        meta_features = np.zeros((X.shape[0], len(self.estimators_) * n_classes))

        # 为每个基础模型生成预测
        for i, estimator in enumerate(self.estimators_):
            # 检查该模型是否支持样本权重
            supports_sample_weight = hasattr(estimator,
                                             'fit') and 'sample_weight' in estimator.fit.__code__.co_varnames

            # 预测集合
            pred_probs = np.zeros((X.shape[0], n_classes))

            # 对每个CV折训练模型并生成预测
            for train_idx, test_idx in cv.split(X, y):
                X_train_fold, X_test_fold = X[train_idx], X[test_idx]
                y_train_fold = y[train_idx]

                # 克隆估计器以避免修改原始估计器
                est = clone(estimator)

                # 根据是否支持样本权重选择不同的训练方式
                if supports_sample_weight and sample_weight is not None:
                    est.fit(X_train_fold, y_train_fold, sample_weight=sample_weight[train_idx])
                else:
                    est.fit(X_train_fold, y_train_fold)

                # 生成预测概率
                if hasattr(est, 'predict_proba'):
                    pred_probs[test_idx] = est.predict_proba(X_test_fold)
                else:
                    # 对于不支持predict_proba的分类器，转换为一热编码
                    y_pred = est.predict(X_test_fold)
                    pred_probs_temp = np.zeros((len(test_idx), n_classes))
                    for j, cls in enumerate(self.classes_):
                        pred_probs_temp[:, j] = (y_pred == cls).astype(int)
                    pred_probs[test_idx] = pred_probs_temp

            # 将该基础模型的预测添加到元特征
            meta_features[:, i * n_classes:(i + 1) * n_classes] = pred_probs

            # 在全量数据上重新训练该基础模型
            est = clone(estimator)
            if supports_sample_weight and sample_weight is not None:
                est.fit(X, y, sample_weight=sample_weight)
            else:
                est.fit(X, y)

            # 保存训练好的模型
            self.estimators_[i] = est

        # 训练元学习器
        if sample_weight is not None and hasattr(self.final_estimator_,
                                                 'fit') and 'sample_weight' in self.final_estimator_.fit.__code__.co_varnames:
            self.final_estimator_.fit(meta_features, y, sample_weight=sample_weight)
        else:
            self.final_estimator_.fit(meta_features, y)

        return self

    def predict(self, X):
        meta_features = self._generate_meta_features(X)
        return self.final_estimator_.predict(meta_features)

    def predict_proba(self, X):
        meta_features = self._generate_meta_features(X)
        if hasattr(self.final_estimator_, 'predict_proba'):
            return self.final_estimator_.predict_proba(meta_features)

        # 如果元学习器不支持predict_proba，转换预测结果
        y_pred = self.final_estimator_.predict(meta_features)
        pred_proba = np.zeros((X.shape[0], len(self.classes_)))
        for i, cls in enumerate(self.classes_):
            pred_proba[:, i] = (y_pred == cls).astype(float)
        return pred_proba

    def _generate_meta_features(self, X):
        n_classes = len(self.classes_)
        meta_features = np.zeros((X.shape[0], len(self.estimators_) * n_classes))

        for i, estimator in enumerate(self.estimators_):
            if hasattr(estimator, 'predict_proba'):
                meta_features[:, i * n_classes:(i + 1) * n_classes] = estimator.predict_proba(X)
            else:
                # 对于不支持predict_proba的分类器，转换为一热编码
                y_pred = estimator.predict(X)
                pred_probs = np.zeros((X.shape[0], n_classes))
                for j, cls in enumerate(self.classes_):
                    pred_probs[:, j] = (y_pred == cls).astype(int)
                meta_features[:, i * n_classes:(i + 1) * n_classes] = pred_probs

        return meta_features

def compute_metric(y_true, y_pred, metric_type='acc'):
    """
    计算各种评估指标

    Parameters:
    -----------
    y_true : array-like
        真实标签
    y_pred : array-like
        如果metric_type为'auc'，则为预测概率；否则为预测标签
    metric_type : str
        评估指标类型，可选：'acc', 'auc', 'precision', 'recall', 'f1'

    Returns:
    --------
    float : 评估指标值
    """
    if metric_type == 'acc':
        # 如果输入的是概率，转换为标签
        if len(y_pred.shape) == 2 or np.any(np.logical_and(y_pred > 0, y_pred < 1)):
            y_pred_labels = np.where(y_pred >= 0.5, 1, 0)
            return accuracy_score(y_true, y_pred_labels)
        return accuracy_score(y_true, y_pred)

    elif metric_type == 'auc':
        # 确保输入的是概率
        if len(y_pred.shape) == 2:
            y_pred = y_pred[:, 1]
        return roc_auc_score(y_true, y_pred)

    elif metric_type == 'precision':
        if len(y_pred.shape) == 2 or np.any(np.logical_and(y_pred > 0, y_pred < 1)):
            y_pred_labels = np.where(y_pred >= 0.5, 1, 0)
            return precision_score(y_true, y_pred_labels)
        return precision_score(y_true, y_pred)

    elif metric_type == 'recall':
        if len(y_pred.shape) == 2 or np.any(np.logical_and(y_pred > 0, y_pred < 1)):
            y_pred_labels = np.where(y_pred >= 0.5, 1, 0)
            return recall_score(y_true, y_pred_labels)
        return recall_score(y_true, y_pred)

    elif metric_type == 'f1':
        if len(y_pred.shape) == 2 or np.any(np.logical_and(y_pred > 0, y_pred < 1)):
            y_pred_labels = np.where(y_pred >= 0.5, 1, 0)
            return f1_score(y_true, y_pred_labels)
        return f1_score(y_true, y_pred)

    else:
        raise ValueError(f"Unsupported metric type: {metric_type}")


if __name__ == '__main__':
    ...
