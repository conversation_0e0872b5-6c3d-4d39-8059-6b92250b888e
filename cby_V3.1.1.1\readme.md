1. 文件目录说明：
   1. ParamsServer.py：主功能函数，包含一个全部算法接口的类Server，该类不实现任何具体的功能相关特性，只是负责调用utils package里面实现的类和函数；
   2. utils文件夹：算法背后的全部功能函数，是一个package，实现具体功能的所有代码都存放在里面；
   3. static文件夹：算法运行中的数据文件夹，包括心磁文件夹mcg10和ID文档mcg_rd10.txt、输出结果文件夹rts10和输出打印文档print_rts10.txt、运行时间统计表runtimes.xlsx；
   4. model文件夹：算法运行需要的信息矩阵；
   5. 对接说明_20241220.docx：上一版软件的对接文档（保留接口）；
   6. 依赖包文件：requirements.txt是必要的安装包、requirements_piplist.txt是导出的pip list结果，创建环境按下面步骤。

2. 运行环境说明，conda环境配置方法如下：

   ```
   # 创建和激活环境
   conda create -n py39_cby python=3.9
   conda activate py39_cby
   # 安装依赖-不指定版本(可选地)
   conda install -y numpy scipy pandas scikit-learn scikit-image matplotlib shapely pillow openpyxl joblib
   # 安装依赖-指定版本(可选地)
   conda install -y numpy=2.0.2 scipy=1.13.1 pandas=2.3.1 scikit-learn=1.6.1 scikit-image=0.24.0 matplotlib=3.9.4 shapely=2.0.7 pillow=11.3.0 openpyxl=3.1.5 joblib=1.5.1
   ```

3. Server调用说明（以SHLY_2024_002080为例）：

   1. 历史版本-时刻点生成get_times1，输入txt文件，输出13个时刻点的字典；

      ```
      {'Code': 1, 'loc_p_head': 39, 'loc_p_peak': 79, 'loc_p_rear': 156, 'loc_q_head': 251, 'loc_q_peak': 273, 'loc_r_peak': 290, 'loc_s_peak': 319, 'loc_s_rear': 349, 'loc_t_head': 430, 'loc_t_onset': 488, 'loc_t_peak': 546, 'loc_t_end': 608, 'loc_t_rear': 674}
      ```

   2. 历史版本-波形描述参数get_waveform，输入txt文件、6个时刻点的字典，输出波形相关参数;

      ```
      {'timephase_HR': 78.534, 'timephase_P': 117, 'timephase_PR': 212, 'timephase_QRS': 98, 'timephase_QT': 423, 'timephase_QTc': 483.942, 'amplitude_QRS': 13.502, 'amplitude_RT': 1.511, 'QRSyc': [0, 0, 0], 'QRSwv': [], 'qrsyc': 5.2, 'STTyc': [2, 0, 54], 'STwv': [['D3', 1], ['D4', 1], ['E2', 1], ['E3', 1], ['E4', 1], ['F2', 1], ['F3', 1], ['F4', 1], ['A5', 2], ['A6', 2], ['B6', 2], ['C6', 2]], 'STreg': [2, 4], 'Twv': [['D3', 5], ['D4', 5], ['E3', 5], ['E4', 4], ['F2', 5], ['F3', 5]], 'ttyc': 8.7, 'stt': [355, 430, 430, 674], 'qhsrs': [[238, 376], [239, 355], [240, 376], [245, 376], [245, 376], [230, 359], [253, 359], [238, 376], [240, 347], [247, 376], [245, 376], [246, 359], [251, 376], [250, 376], [242, 345], [247, 376], [243, 376], [246, 376], [250, 376], [247, 376], [244, 376], [244, 357], [246, 358], [248, 376], [249, 376], [246, 376], [246, 376], [244, 355], [243, 357], [246, 372], [247, 376], [246, 376], [245, 347], [244, 354], [241, 372], [263, 368]], 'Code': 1}
      ```

   3. 历史版本-磁图描述参数get_mcgpole，输入txt文件、6个时刻点的字典，输出磁图相关参数；

      ```
      {'QRS_p_disp': 9.8, 'QRS_p_disp_type': [], 'QRS_n_disp': 8.9, 'QRS_n_disp_type': [], 'T_p_disp': 10.3, 'T_p_disp_type': [], 'T_n_disp': 4.8, 'T_n_disp_type': [], 'QRS_center': 7.9, 'T_pos_trajectory': 4.1, 'T_center': 7.3, 'QRS_angle': [0, 0, 0, 0, 0, 0], 'QRS_ave_angle': 158.6, 'QR_rot_angle': 164.9, 'QR_rotation': 1, 'T_angle': 9.7, 'T_angle_type': [], 'T_ave_angle': -174.8, 'T_angle_stable': 7.0, 'QRS_T_ave_angle': -26.6, 'Code': 1}
      ```

   4. 历史版本-离散度参数get_disps1，输入txt文件、6个时刻点的字典，输出Q/R/S/T波正负离散度和离散类型；

      ```
      {'Q_disp_p_n': [2.266, [0.0, 1.0, 0.0], 1.806, [0.0, 1.0, 0.0]], 'R_disp_p_n': [1.458, [0.0, 0.326, 0.674], 3.234, [0.822, 0.178, 0.0]], 'S_disp_p_n': [0.833, [0.0, 1.0, 0.0], 0.0, []], 'T_disp_p_n': [2.24, [0.0, 1.0, 0.0], 0.0, []], 'Code': 1}
      ```

   5. 历史版本-心磁结论get_results，输入txt文件、电流参数和心肌缺血评分、6个时刻点的字典、波形描述和磁图描述结果，输出正常/轻中重，数字0/1/2/3。

      ```
      {'Result': 2, 'Code': 1}
      ```

   6. 新版本-时刻点算法get_tms250805，输入txt文件或行字符串的列表，输出13个时刻点的字典。

      ```
      {'Code': 1, 'loc_p_head': 49, 'loc_p_peak': 79, 'loc_p_rear': 109, 'loc_q_head': 251, 'loc_q_peak': 273, 'loc_r_peak': 290, 'loc_s_peak': 319, 'loc_s_rear': 349, 'loc_t_head': 430, 'loc_t_onset': 488, 'loc_t_peak': 546, 'loc_t_end': 608, 'loc_t_rear': 674}
      ```

   7. 新版本-插值算法get_matrix250805，输入txt文件，输出N维列表的插值结果，每个元素是100*100的numpy矩阵。

      ```
      {'Code': 1, 'matrixes': [[-0.06385507 -0.0674381  -0.07101494 ...  0.04193594  0.04415166
         0.04637808]
       [-0.0624479  -0.06635338 -0.07025133 ...  0.04301142  0.04494049
         0.04687904]
       [-0.0610416  -0.06526678 -0.06948311 ...  0.0440879   0.0457317
         0.04738377]
       ...
       [-0.13825101 -0.14457775 -0.15089888 ... -0.27946924 -0.25600592
        -0.23252104]
       [-0.14673915 -0.15317546 -0.15960808 ... -0.28228633 -0.25908382
        -0.23585691]
       [-0.15521927 -0.16176467 -0.16830834 ... -0.28509046 -0.26215136
        -0.239185  ]]}
      ```

   8. 新版本-15个新参数get_mts250805，输入txt文件或行字符串的列表、插值矩阵、13个时刻点的列表，输出15个参数的字典。

      ```
      {'Code': 1, 'mfm_TT_ta': 177.7, 'mfm_ag_tta': 157.0, 'mfm_TT_tta': 7.0, 'mfm_TT_areaf': 0.041, 'space_twavedt_iv': 17.8, 'space_twavedt_ur': 1.8, 'time_tpnrt1': 0.33, 'mfm_QR_ra': 73.7, 'pcdm_QR_rca': 342.7, 'mfm_ag_qrsa': 322.0, 'pcdm_QR_qra': 183.0, 'space_qrswv_r': 0, 'space_qrswv_q': 0, 'mfm_ag_qrstta': 165.0, 'amplitude_RT': 1.511}
      ```

4. ParamsServer主函数实现细节：

   1. 执行步骤：从全部心磁中随机抽取了**10例数据**，分别执行上述**8个算法**，保存和打印输出结果，统计**运行时间**，每个具体步骤见**代码注释**；

   2. 输入模板：8个算法都在Server类中，调用函数时包含输入细节，一些输入模板如下：

      ```
      # 历史版本-2.波形描述参数：6个时刻点的输入模板
      data_dict = {'Q': 252, 'R': 277, 'S': 312, 'T_on': 464, 'T': 535, 'T_end': 570}
      # 历史版本-5.心磁结论：电流参数和心肌缺血评分的输入模板
      paras = {
          'Q_index': -0.022053412822669,
          'S_index': 0.843123855929008,
          'TT_index': 1.83742183051828,
          'Q_angle': -152.1,
          'R_angle': -11.8,
          'S_angle': 160.3,
          'T_angle': -52.5,
          'QRyc': 0,
          'RSyc': 0,
          'Tyc': 0,
          'score': 0.210579261183739}
      # 新版本-3.新参数：13个时刻点的输入模板
      times = [39, 79, 156, 251, 273, 290, 319, 349, 430, 488, 546, 608, 674]
      ```

   3. 输出结果：结果保存在./static/rts10中，每个样本单独新建文件夹，每个算法结果单独保存，保存格式为json，同时打印输出结果

      ```
      # 新版本-2.插值算法: 矩阵太大，采用pkl保存, 不直接打印矩阵，仅打印正确输出的标识
      print(ms['Code'])
      # 输出结果为1
      ```

   4. 运行时间：10个样本的8个算法单独计算，再对**10个样本取平均值**，运行时间保存在./static/runtimes.xlsx，结果如下（已对全部算法进行加速，单位秒）：

      | ID                 | time_raw | waveform | mcgpole   | disps1   | results  | time_new | matrixes | mts_new15 |
      | ------------------ | -------- | -------- | --------- | -------- | -------- | -------- | -------- | --------- |
      | SHLY_2024_002080   | 0.206703 | 0.456287 | 10.150476 | 3.73446  | 0.000034 | 0.24382  | 2.93553  | 2.284456  |
      | BJ_TT_000614       | 0.292589 | 0.637884 | 10.266928 | 4.532651 | 0.000049 | 0.1689   | 3.674602 | 2.46618   |
      | 464-王雅           | 0.204947 | 0.444008 | 9.093475  | 3.5829   | 0.000046 | 0.115139 | 2.803582 | 2.117537  |
      | SL_B_001075-姜声英 | 0.230586 | 0.50562  | 10.109483 | 4.08892  | 0.000059 | 0.129725 | 3.207237 | 2.340035  |
      | SHSY_2023_000467   | 0.270676 | 0.577696 | 12.362285 | 4.446633 | 0.000066 | 0.157782 | 3.538907 | 2.77174   |
      | BJ_TT_002610       | 0.328826 | 0.700888 | 12.962895 | 5.039429 | 0.000046 | 0.185976 | 4.049265 | 2.810105  |
      | AHYK_2023_000644   | 0.193312 | 0.432944 | 10.474998 | 3.529384 | 0.000063 | 0.105517 | 2.883492 | 2.300468  |
      | SL_DSA_001592      | 0.183193 | 0.401839 | 11.022304 | 3.361597 | 0.000039 | 0.100733 | 2.614398 | 2.259849  |
      | 109-张玉龙         | 0.217525 | 0.472813 | 10.820171 | 3.896686 | 0.000047 | 0.122574 | 3.0726   | 2.326003  |
      | BJ_TT_001097       | 0.195797 | 0.428435 | 12.878491 | 3.585208 | 0.000049 | 0.109893 | 2.78686  | 2.48217   |
      | Avg.Time           | 0.232415 | 0.505841 | 11.014151 | 3.979787 | 0.00005  | 0.144006 | 3.156647 | 2.415854  |



