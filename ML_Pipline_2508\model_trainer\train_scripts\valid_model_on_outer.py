''''
@Project：ML_pipline/model_trainer
@File   ：valid_model_on_outer.py
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2023/7/24 10:35
@Discribe：
    在外部验证数据集上测试模型(已经提前训练好保存好模型)
'''

import pickle

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from data_vis_tools.prob_dist import visualize_distribution
from model_trainer.models import get_model_predict_results, plot_folds_results, \
    get_metrics, search_best_threshold, load_config
from model_trainer.models import load_and_prepare_outer_data, run_test_model

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号问题


def run_model_valid_on_folds_and_vote(feature_pkl, id_pkl, selected_feature_pkl, save_res_path, vote=True):
    """
    Run model validation on multiple folds of data.
        This function loads the trained models and their corresponding results from the save results pickle file.
    It then performs model validation on each fold and stores the predictions and probabilities for each fold.
    The predictions and probabilities from each fold are then stacked together to obtain the final predictions.
    The function then performs hard voting by taking the majority vote of the predictions from each fold.
    The function calculates and prints the overall evaluation metrics for the hard voting approach.
    After that, the function performs soft voting by taking the average probability of the predictions from each fold.
    The function searches for the best threshold for the soft voting approach and obtains the final predictions.
    Finally, the function calculates and prints the overall evaluation metrics for the soft voting approach.
    :param feature_pkl: Path to the feature pickle file.
    :param id_pkl: Path to the ID pickle file.
    :param selected_feature_pkl: Path to the selected feature pickle file.
    :param save_res_path: Path to the saved results file（containing the trained models）.
    :return:
    """
    # 加载模型
    with open(save_res_path, 'rb') as f:
        save_res = pickle.load(f)
        models_trained = save_res['models_trained']
        results = save_res['results']
    # note: 在某些测试配置下，selected_feature_pkl在每折上不同，因为不同折使用了不稳定的特征。此时需要追加后续路径。
    # 开始测试 5折循环 5个模型结果及平均
    y_preds, y_probs = [], []
    results_outer_test = []
    for fold, (model, result_dict) in enumerate(zip(models_trained, results)):
        best_threshold = result_dict['valid_result_threshold']['threshold']
        # # 测试optuna保存的模型best_model
        # model = pickle.load(
        #     open('./files/saved_models/' + DATA_REGION + '_features_rfe_F5_s42_fold' + str(
        #         fold) + '_top70best_model.pkl',
        #          'rb'))
        # best_threshold = 0.5
        # 加载测试数据
        test_data = load_and_prepare_outer_data(
            id_pkl=id_pkl,
            features_pkl=feature_pkl,
            selected_features_pkl=selected_feature_pkl)  # SELECTED_FEATURE_PKL + str(fold) + '_top70.pkl')

        x_test, y_test = test_data.drop(columns=['mcg_file', 'label'], inplace=False), test_data['label']

        result_dict_outer_test = get_model_predict_results(model=model, x_test=x_test, y_test=y_test,
                                                           best_threshold=best_threshold, valid=True)
        results_outer_test.append(result_dict_outer_test)
        if vote:
            y_prob, acc, result = run_test_model(model, x_test, y_test, dataset='test', threshold=best_threshold)
            y_pred = np.where(y_prob >= best_threshold, 1, 0)
            y_probs.append(y_prob)
            y_preds.append(y_pred)
    if vote:
        # 将每个模型的预测结果堆叠起来
        y_preds = np.array(y_preds)

        # 进行硬投票（多数表决）
        y_pred_vote_hard = np.apply_along_axis(lambda x: np.argmax(np.bincount(x)), axis=0, arr=y_preds)

        # 计算综合评估指标
        y_prob_mean = np.mean(y_probs, axis=0)  # 获取平均概率
        auc, acc, rec, spec, gmean = get_metrics(y_test, y_pred_vote_hard, y_prob_mean)
        print(f"硬投票综合评估指标：AUC: {auc}, Accuracy: {acc}, Recall: {rec}, Specificity: {spec}, G-Mean: {gmean}")

        # 进行软投票（概率平均）
        y_prob_mean = np.mean(y_probs, axis=0)
        best_threshold_all = search_best_threshold(y_test, y_prob_mean, metric='g-mean')

        y_pred_vote_soft = np.where(y_prob_mean >= best_threshold_all, 1, 0)

        # 计算综合评估指标
        auc, acc, rec, spec, gmean = get_metrics(y_test, y_pred_vote_soft, y_prob_mean)
        print(f"软投票综合评估指标：AUC: {auc}, Accuracy: {acc}, Recall: {rec}, Specificity: {spec}, G-Mean: {gmean}")
        test_data = load_and_prepare_outer_data(
            id_pkl=id_pkl,
            features_pkl=feature_pkl,
            selected_features_pkl=selected_feature_pkl)

        mcg_file = test_data[['mcg_file', 'label']]
        mcg_file['pred'] = list(y_pred_vote_hard)
        mcg_file['prob'] = list(y_prob_mean)
        mcg_file['if_correct'] = mcg_file['label'] == mcg_file['pred']
        return auc, acc, rec, spec, gmean, mcg_file

    # 计算平均结果
    avg = plot_folds_results(results=results_outer_test, mode='test_result')
    return results_outer_test, avg


def save_deployed_model(save_res_path, save_model_name):
    """
    Save the trained models in a deployable format.

    Args:
        :param save_res_path: The path to the pickle file containing the saved results.
        :param save_model_name: The name of the saved model.
    Returns:
        None

    This function reads the trained models and their corresponding results from the save results pickle file.
    It then saves the models in a deployable format as a list of dictionaries, where each dictionary contains
    the best threshold, the model, and the features. The models are saved in a pickle file with the specified
    name in the './files/saved_models/' directory.

    """
    # 读取模型，以部署的形式保存： list of dict:best_threshold, model,features

    # with open(r"./files/saved_models/xgb_LAD_auc0.709_rcl_0.751_spec_0.605_model_file.pkl", "rb") as f:
    #     models_LAD_thresholds = pickle.load(f)
    #
    # with open(r'./files/saved_models/lgbm_LCX_auc0.690_rcl_0.596_spec_0.736_model_file.pkl', "rb") as f:
    #     models_LCX_thresholds = pickle.load(f)
    #
    # with open(r'./files/saved_models/lgbm_RCA_auc0.676_rcl_0.565_spec_0.767_model_file.pkl', "rb") as f:
    #     models_RCA_thresholds = pickle.load(f)

    with open(save_res_path, 'rb') as f:
        save_res = pickle.load(f)
        models_trained = save_res['models_trained']
        results = save_res['results']

    deployed_models = []
    for fold in range(5):
        # 保存模型
        deployed_models.append({'model': models_trained[fold],
                                'best_threshold': results[fold]['valid_result_threshold']['threshold'],
                                'features': models_trained[0].feature_names_in_.tolist()})

    pickle.dump(deployed_models, open('./files/saved_models/tobe_deployed/' + save_model_name + '.pkl', 'wb'))
    return


def outer_valid_v0820(res, id_path, version='20240820_1754_351', upload_name='tmptest'):
    """
    外部验证V0820+
    :param res:
    :return:
    """
    import requests
    # 0820后外部验证没有正确标签，需要传到服务端验证。 此前组装正确格式的 pkl
    id_pkl = id_path  # "./files/data/data_index/data_V0826/Outer_Valid.pkl"
    with open(id_pkl, 'rb') as f:
        outer_valid = pd.DataFrame(pickle.load(f))

    # 创建一个从文件名到标签的映射
    prob_map = dict(zip(res['mcg_file'], res['prob']))
    label_map = dict(zip(res['mcg_file'], res['pred']))

    # 更新 df1 的标签列
    outer_valid.iloc[:, 1] = outer_valid.iloc[:, 0].map(prob_map)
    outer_valid.iloc[:, 2] = outer_valid.iloc[:, 0].map(label_map)
    outer_valid = np.array(outer_valid)

    # 保存
    with open('./files/results/' + upload_name + '.pkl', 'wb') as f:
        pickle.dump(outer_valid, f)
    # 上传测试
    predict_pickle = r'./files/results/' + upload_name + '.pkl'  # 修改此处为待评估模型结果pickle文件地址即可
    file_handle = open(predict_pickle, 'rb')
    response = requests.post('http://**************:8042/calculate',
                             files={'file': open(predict_pickle, 'rb')},
                             data={'version': version})
    file_handle.close()
    print(response.json())
    return response.json

def valid_model(best_res_path, best_fold,
                all_feature=None,selected_feature = None,
                id_pkl = None):

    with open(best_res_path, 'rb') as f:
        best_res = pickle.load(f)

    best_model, best_threshold = (
        best_res['models_trained'][best_fold], \
        best_res['results'][best_fold]['valid_result_threshold']['threshold'])
    print(best_threshold)

    test_data = load_and_prepare_outer_data(
        id_pkl=id_pkl,
        features_pkl=all_feature,
        selected_features_pkl=selected_feature)

    x_test, y_test = test_data.drop(columns=['mcg_file', 'label'], inplace=False), test_data['label']


    x_test['clinic_hospital'] = 5

    y_prob, acc, result = run_test_model(best_model, x_test, y_test, dataset='test', threshold=best_threshold)
    y_pred = np.where(y_prob >= best_threshold, 1, 0)

    # 检查并转换为合法概率值
    if (y_prob < 0).any() or (y_prob > 1).any():
        # 使用sigmoid转换
        def sigmoid(x):
            mask = x >= 0
            result = np.zeros_like(x, dtype=np.float64)
            exp_nx = np.exp(-x[mask])
            result[mask] = 1 / (1 + exp_nx)
            exp_x = np.exp(x[~mask])
            result[~mask] = exp_x / (1 + exp_x)
            return result

        y_prob_output = sigmoid(y_prob)
        y_prob_output = np.clip(y_prob_output, 1e-6, 1 - 1e-6)
    else:
        y_prob_output = y_prob

    res = test_data[['mcg_file', 'label']]
    res['pred'] = list(y_pred)
    res['prob'] = list(y_prob_output)

    with open(id_pkl, 'rb') as f:
        outer_valid = pd.DataFrame(pickle.load(f))

    # 创建一个从文件名到标签的映射
    prob_map = dict(zip(res['mcg_file'], res['prob']))
    label_map = dict(zip(res['mcg_file'], res['pred']))

    # 使用 map 方法更新 df1 的标签列
    outer_valid.iloc[:, 1] = outer_valid.iloc[:, 0].map(prob_map)
    outer_valid.iloc[:, 2] = outer_valid.iloc[:, 0].map(label_map)
    outer_valid = np.array(outer_valid)[:,:3]

    return outer_valid,res

if __name__ == '__main__':

    # <editor-fold desc="临床外部测试">
    model_names = [
        # 'v4/V1129_xgb_S42F5_cons_loss_test', # 一致性数据建模 f1
        # 'v5/V1129_xgb_S42F5_cons_loss_5',  # 1.5 加权 f2
        # 'v5/V1129_xgb_S42F5_cons_loss_test', # 综合思路- 有1置1 f0
        # 'v7/V1129_xgb_S42F5_scale_pos_w',#0
        # 'v7/V1129_xgb_S42F5_smote',#3
        # 'v7/V1129_xgb_S42F5_smote_hospital',#2
        # 'v7/V1129_xgb_S42F5_smote_partial_hospital',#1
        # 'v8/V1211_xgb_S42F5_boruta875_all_f4',
        # 'v8/V1211_xgb_S42F5_boruta875_all_smote_f3',
        # 'v8/V1211_xgb_S42F5_boruta875_cons_f4',
        # 'v8/V1211_xgb_S42F5_boruta875_cons_hospital_smote_f3',
        # 'v8/V1211_xgb_S42F5_boruta875_cons_partial_hospital_smote_f4',
        # 'v8/V1211_xgb_S42F5_boruta875_partial_hospital_smote_f1',
        # 'v8/V1211_xgb_S42F5_boruta875_partial_nbj_hospital_smote_f1',
        # 'v9/V1211_xgb_S42F5_boruta875_all_bj0partial_smote_f0',
        # 'v9/V1211_xgb_S42F5_boruta875_cons_bj0partial_smote_f0',
        # 'v9/V1211_xgb_S42F5_boruta875_cons_bj0.5partial_smote_f1',
        # 'v9/V1211_xgb_S42F5_boruta875_cons_bj1partial_smote_f4',
        'v9/V1211_xgb_S42F5_boruta875_cons_bj0_nly_partial_smote_f0',

        # 'v11/V1211_xgb_S42F5_boruta875_zh_smote_f4',
        # 'v11/V1211_xgb_S42F5_boruta875_zh_smote_f4',
        # 'v11/V1211_xgb_S42F5_boruta875_zh_smote_f4',
        # 'v11/V1211_xgb_S42F5_boruta875_zh_smote_f4',
        # 'v11/V1211_xgb_S42F5_boruta875_zh_smote_f4',

    ]
    selected_features_path = [
        # 'features_V1129F5S42_boruta_cons_top826_clinic5',
        # 'features_V1129F5S42_boruta_cons_top826_clinic5',
        # 'features_V1129F5S42_boruta_cons_top826_clinic5',
        'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top875_clinic5',
        # 'features_V1211F5S42_boruta_cons_top557_clinic5',
        # 'features_V1211F5S42_boruta_cons_top557_clinic5',

    ]
    best_folds = [0]

    # 创建一个ExcelWriter来写入多张Sheet
    output_path = './files/results/output.xlsx'
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        all_predictions = []

        for i in range(len(model_names)):
            params = {
                "all_feature": "./files/saved_features/feature_V241119_outer_clinic_228.pkl",
                "id_pkl": "./files/data/data_index/data_V1114/Clinic_Outer_all_Valid_228.pkl",
                "selected_feature": './files/saved_features/selected_features/' + selected_features_path[i] + '.pkl',
                "best_res_path": "./files/saved_models/tobe_outer_test/" + model_names[i] + ".pkl",
                "best_fold": best_folds[i]
            }
            # 调用模型验证函数
            res, _ = valid_model(**params)

            # 将结果转为DataFrame并添加列名
            df = pd.DataFrame(res, columns=['mcg_file', 'prob', 'pred'])
            visualize_distribution(df['prob'].values,'prob')
            all_predictions.append(df)

            # 写入到Excel的对应Sheet
            sheet_name = f"Model_{i + 1}"
            df.to_excel(writer, index=False, sheet_name=sheet_name)

        # 创建投票结果DataFrame
        vote_df = pd.DataFrame()
        vote_df['mcg_file'] = all_predictions[0]['mcg_file']  # 使用第一个模型的mcg_file

        # 统计投票
        pred_matrix = np.array([df['pred'].values for df in all_predictions])
        vote_counts = np.sum(pred_matrix == 1, axis=0)
        vote_df['vote_result'] = (vote_counts >= 2).astype(int)  # 超过2个模型预测为1的样本，结果为1

        # 添加每个模型的预测结果，方便查看
        for i in range(len(model_names)):
            vote_df[f'model_{i + 1}_pred'] = all_predictions[i]['pred']

        # 写入投票结果
        vote_df.to_excel(writer, index=False, sheet_name='Vote_Result')
    # </editor-fold>


    # <editor-fold desc="一般性外部测试">

    # 配置文件方式运行
    config_file = './files/configs/test_config_V1129.json'
    environment = 'valid_clinic_boruta773'
    valid_params1 = load_config(config_file, environment)

    # 单折模型验证 及 平均结果
    auc, acc, rec, spec, gmean, res1 = run_model_valid_on_folds_and_vote(**valid_params1)

    # 外部验证
    result = outer_valid_v0820(res1, valid_params1['id_pkl'], '20241114_1436_160', upload_name=environment)

    # </editor-fold>

    # <editor-fold desc="临时数据的处理 ---301微循环障碍">
    valid_params = {
        "feature_pkl": "./files/saved_features/features_V0821_all_tspt_2578.pkl",  # 2578是补充了301中的一些数据
        "id_pkl": "./files/data/data_index/301微循环障碍.xlsx",
        "selected_feature_pkl": "./files/saved_features/selected_features/integrated_features_stable_F5_s42_fold_all_top150.pkl",
        "save_res_path": "./files/saved_models/xgb_untuned_integrated_S42_F5_stable150.pkl"
    }
    auc, acc, rec, spec, gmean, res1 = run_model_valid_on_folds_and_vote(**valid_params)
    orig_df = pd.read_excel('./files/data/data_index/301微循环障碍.xlsx')
    res1.drop(columns=['label', 'if_correct'], inplace=True)
    res1_renamed = res1.rename(columns={'mcg_file': '心磁号'})

    res = pd.merge(orig_df, res1_renamed, on='心磁号', how='left')
    res.to_excel('./files/result0829.xlsx', index=False)

    id_pkl = "./files/data/data_index/data_V0624_integrated/S42-F5.pkl"
    with open(id_pkl, 'rb') as f:
        has_label_data = pickle.load(f)
    has_label_data = has_label_data[0]['train_valid']
    has_label_data_renamed = pd.DataFrame(has_label_data).rename(columns={0: '心磁号'})

    res = pd.merge(has_label_data_renamed, res1_renamed, on='心磁号', how='right')
    print(has_label_data)
    # </editor-fold>

    # <editor-fold desc="临时数据的处理 ---一致性模型测试不一致数据">

    # 临时数据的处理 ---一致性模型测试不一致数据 ------------------------------------------------------------------------

    valid_params = {
        "feature_pkl": "./files/saved_features/features_V0821_all_tspt_2534.pkl",
        "id_pkl": "./files/data/data_index/data_tmptest/233例不一致数据用1526一致模型跑20240902.xlsx",
        "selected_feature_pkl": "./files/saved_features/selected_features/features_V0826F5S42_tspt_boruta_top1369.pkl",
        "save_res_path": "./files/saved_models/V0826_xgb_untuned_S42F5_boruta1369.pkl"
    }
    auc, acc, rec, spec, gmean, res1 = run_model_valid_on_folds_and_vote(**valid_params)
    orig_df = pd.read_excel("./files/data/data_index/data_tmptest/233例不一致数据用1526一致模型跑20240902.xlsx")
    res1.drop(columns=['label', 'if_correct'], inplace=True)
    res1_renamed = res1.rename(columns={'mcg_file': 'ID'})

    res = pd.merge(orig_df, res1_renamed, on='ID', how='left')
    res.to_excel('./files/result0903.xlsx', index=False)
    # 看看原来有正确标签的数据
    id_pkl = "./files/data/data_index/data_V0820/S42-F5.pkl"
    with open(id_pkl, 'rb') as f:
        has_label_data = pickle.load(f)
    has_label_data = has_label_data[0]['train_valid']
    has_label_data_renamed = pd.DataFrame(has_label_data).rename(columns={0: 'ID'})

    res = pd.merge(res, has_label_data_renamed, on='ID', how='left')
    print(has_label_data)
    # </editor-fold>

    # <editor-fold desc="嵌入特征模型验证">

    # -------------------------------------------- 嵌入特征模型验证 -----------------------------------------
    params_embed = {
        "feature_pkl": "./files/saved_features/features_V0826F5S42_boruta_top1369_embed96.pkl",
        "id_pkl": "./files/data/data_index/data_V0826_cons/Outer_Valid.pkl",
        "selected_feature_pkl": "./files/saved_features/features_V0826F5S42_boruta_top1369_embed96.pkl",
        "save_res_path": "./files/saved_models/xgb_V0821F5S42_boruta_top1369_embed96.pkl"
    }

    auc, acc, rec, spec, gmean, res1 = run_model_valid_on_folds_and_vote(**params_embed)
    result = outer_valid_v0820(res1, params_embed['id_pkl'], '20240820_1526_307', upload_name='boruta_embed')
    # </editor-fold>


