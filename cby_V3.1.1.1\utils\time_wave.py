"""
Author: <PERSON><PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/03/21 10:40
desc: 时间波组图指标
environment: py36
"""


import numpy as np
from scipy import signal

from utils.data import Data


class TimeWave:

    def __init__(self, data: np.array, rms: np.array, time: dict):
        self.data_dic = np.concatenate((data, np.array([rms])), axis=0)
        self.time_dic = time
        self.indicator = dict()
        '''
        some simple indicators are calculated directly during initializing
        '''
        self.indicator['qsitv'] = self.time_dic['Sr'] - self.time_dic['Qh']
        self.indicator['htrt'] = 60000/data.shape[1]
        self.indicator['pnum'] = self.time_dic['cnt']
        
    def cal_t_amp_ratio(self):
        Tp = self.time_dic['Tp']
        data = self.data_dic[:-1, Tp]
        t_amp_ratio = np.abs(np.max(data) / np.min(data))
        self.indicator['tpnrt1'] = t_amp_ratio
        return 0

    def cal_t_amp_ratio1(self):
        To, Te = self.time_dic['To'], self.time_dic['Te']
        data = self.data_dic[:-1, To:Te+1]
        max_x, min_n = np.max(data), np.min(data)
        t_amp_ratio = np.abs(max_x / min_n)
        self.indicator['tpnrt2'] = t_amp_ratio
        return 0

    def cal_t_strength(self):
        Rp, Tp = self.time_dic['Rp'], self.time_dic['Tp']
        R, T = self.data_dic[:-1, Rp - 10:Rp + 10], self.data_dic[:-1, Tp - 10:Tp + 10]
        t_strength = np.max(R) / np.max(T)
        self.indicator['rtrt'] = t_strength
        return 0

    def cal_t_strength3(self):
        Rp, Tp = self.time_dic['Rp'], self.time_dic['Tp']
        channel = np.argmax(self.data_dic[:36, Rp])
        R, T = self.data_dic[channel, Rp - 10:Rp + 10], self.data_dic[channel, Tp - 10:Tp + 10]
        t_strength = np.max(np.abs(R)) / np.max(np.abs(T))
        self.indicator['rtrt3'] = t_strength
        return 0

    def cal_t_strength_noise(self):
        Qh, Tp = self.time_dic['Qh'], self.time_dic['Tp']
        To, Te = self.time_dic['To'], self.time_dic['Te']
        # channel = np.argmax(np.abs(self.data_dic[:-1,Tp]))
        channel = np.argmax(self.data_dic[:-1, Tp])
        noise = np.abs(self.data_dic[channel, Qh - 30:Qh])
        data = np.abs(self.data_dic[channel, To:Te])
        t_strength_noise = np.max(noise) / np.max(data) * 100
        self.indicator['tnirt'] = t_strength_noise
        return 0

    def cal_t_strength_noise1(self):
        Qh, To, Te = self.time_dic['Qh'], self.time_dic['To'], self.time_dic['Te']
        noise = self.data_dic[-1, Qh - 30:Qh]
        T = self.data_dic[-1, To:Te + 1]
        temp = np.max(noise) / np.max(T)
        t_strength_noise = np.max(temp) * 100
        self.indicator['tnirt1'] = t_strength_noise
        return 0

    def cal_t_strength_qrs(self):
        Qh, Sr = self.time_dic['Qh'], self.time_dic['Sr']
        To, Te = self.time_dic['To'], self.time_dic['Te']
        QRS = self.data_dic[-1, Qh:Sr + 1]
        T = self.data_dic[-1, To:Te + 1]
        t_strength_qrs = np.max(QRS) / np.max(T)
        self.indicator['qrsttrt'] = t_strength_qrs
        return 0

    def cal_st_slope(self):
        Sr, Tp = self.time_dic['Sr'], self.time_dic['Tp']
        S, T = self.data_dic[:-1, Sr], self.data_dic[:-1, Tp]
        temp = np.abs((T - S) * 1000 / (Tp - Sr))
        st_slope = np.max(temp)
        self.indicator['stsl'] = st_slope
        return 0

    def cal_st_slope1(self):
        # Qh, Sr = self.time_dic['Qh'],self.time_dic['Sr']
        To, Te, Tp = self.time_dic['To'], self.time_dic['Te'], self.time_dic['Tp']
        channel = np.argmax(np.abs(self.data_dic[:, Tp]))
        # QRS = self.data_dic[channel,Qh:Sr+1]
        T = self.data_dic[channel, To:Te + 1]
        slope = np.gradient(T, np.arange(Te + 1 - To))
        st_slope = np.max(np.abs(slope))  # /np.max(np.abs(T))
        self.indicator['stsl1'] = st_slope
        return 0

    def cal_t_flatness(self):

        Te, To, Tp = self.time_dic['Te'], self.time_dic['To'], self.time_dic['Tp']
        channel = np.argmax(self.data_dic[:, Tp])
        t_flatness = np.max(self.data_dic[channel, To:Te]) / (Te - To) * 100
        self.indicator['ttflt'] = t_flatness
        return 0

    def cal_t_flatness1(self):
        Te, To, Tp = self.time_dic['Te'], self.time_dic['To'], self.time_dic['Tp']
        channel_index = self.data_dic[:-1, Tp] > 0
        # print(channel_index)
        data = self.data_dic[:-1, :][channel_index, To:Te]
        rms = np.sum(data**2, axis=0)
        t_flatness = np.max(rms)/(Te-To)*10
        self.indicator['ttflt1'] = t_flatness
        return 0

    def cal_t_steep_pos(self):
        To, Tp, Te = self.time_dic['To'], self.time_dic['Tp'], self.time_dic['Te']
        channel = np.argmax(self.data_dic[:, Tp])
        triangle = (Te - To) * self.data_dic[channel, Tp] / 2
        area = np.sum(self.data_dic[channel, To:Te + 1], axis=-1)
        rate = np.abs(area / triangle)
        self.indicator['ttstpp'] = rate
        return 0

    def cal_t_steep_neg(self):
        To, Tp, Te = self.time_dic['To'], self.time_dic['Tp'], self.time_dic['Te']
        channel = np.argmin(self.data_dic[:, Tp])
        triangle = (Te - To) * self.data_dic[channel, Tp] / 2
        area = np.sum(self.data_dic[channel, To:Te + 1], axis=-1)
        rate = np.abs(area / triangle)
        self.indicator['ttstpn'] = rate
        return 0

    def cal_t_steep_ratio(self):
        if 'ttstpn' in self.indicator and 'ttstpp' in self.indicator:
            self.indicator['ttstprt'] = self.indicator['ttstpp'] / self.indicator['ttstpn']
        else:
            To, Tp, Te = self.time_dic['To'], self.time_dic['Tp'], self.time_dic['Te']
            channel_neg = np.argmin(self.data_dic[:, Tp])
            triangle_neg = (Te - To) * self.data_dic[channel_neg, Tp] / 2
            area_neg = np.sum(self.data_dic[channel_neg, To:Te + 1], axis=-1)
            rate_neg = np.abs(area_neg / triangle_neg)
            channel = np.argmax(self.data_dic[:, Tp])
            triangle = (Te - To) * self.data_dic[channel, Tp] / 2
            area = np.sum(self.data_dic[channel, To:Te + 1], axis=-1)
            rate = np.abs(area / triangle)
            self.indicator['ttstprt'] = rate / rate_neg
        return 0

    def cal_st_raise(self):
        Sr, To = self.time_dic['Sr'], self.time_dic['To']
        data = self.data_dic[:-1, Sr:To + 1]
        divide = To - Sr if (To-Sr) else 1
        data = np.abs(np.sum(data, axis=-1) / divide)
        st_raise = np.max(data)
        self.indicator['strs'] = st_raise
        return 0

    def cal_jt_qrs1(self):
        Qh, Sr, Te = self.time_dic['Qh'], self.time_dic['Sr'], self.time_dic['Te']
        qrs = np.sum(self.data_dic[-1:, Qh:Sr + 1])
        jt = np.sum(self.data_dic[-1:, Sr:Te + 1])
        self.indicator['areaqj'] = qrs / jt
        return 0

    def cal_t_smooth(self):
        Sr, Th = self.time_dic['Sr'], self.time_dic['Th']
        if Sr == Th:
            Th += 1
        data = self.data_dic[:-1, Sr:Th]
        dif = np.max(np.max(data, axis=-1) - np.min(data, axis=-1))
        if Th - Sr == 1:
            dif = 1
        data = np.abs(np.diff(np.diff(data)))
        length = max(1,data.shape[1])
        data = np.sum(data, axis=-1) / (dif * length)
        self.indicator['tsm'] = np.max(data) * 1000
        return 0

    def cal_t_smooth1(self):
        Sr, To = self.time_dic['Sr'], self.time_dic['To']
        if Sr == To:
            To += 1
        data = self.data_dic[-1, Sr:To]
        dif = np.max(data) - np.min(data)
        if To - Sr == 1:
            dif = 1
        data = np.abs(np.diff(np.diff(data)))
        data = np.sum(data, axis=-1) / (dif * (To - Sr))
        self.indicator['tsm1'] = np.max(data) * 1000
        return 0

    def cal_timeloc_diff(self):
        To, Te, Tp = self.time_dic['To'], self.time_dic['Te'], self.time_dic['Tp']
        data = self.data_dic[:-1, To:Te]
        max_list = np.max(data, axis=-1)
        index = (max_list > (np.max(max_list) / 3)) | (max_list < (np.min(max_list) / 3))
        if np.count_nonzero(index) >= 12:
            data = data[index, :]
        else:
            data = data[np.argpartition(np.abs(max_list), -12)[-12:], :]
        index_list = np.argmax(np.abs(data), axis=-1)
        self.indicator['tmdf'] = np.max(index_list) - np.min(index_list)
        return 0

    def cal_timeloc_diff2(self):
        To, Te, Tp = self.time_dic['To'], self.time_dic['Te'], self.time_dic['Tp']
        data = self.data_dic[:-1, To:Te]
        max_list = np.max(data, axis=-1)
        min_list = np.min(data, axis=-1)
        index = (max_list > (np.max(max_list) / 3)) | (min_list < (np.min(min_list) / 3))
        if np.count_nonzero(index) >= 12:
            data = data[index, :]
        else:
            data = data[np.argpartition(np.abs(max_list), -12)[-12:], :]
        data_peak_index = np.argmax(np.abs(data), axis=-1)
        self.indicator['tmdf1'] = np.max(data_peak_index) - np.min(data_peak_index)
        return 0

    def cal_st_fluc_score(self):
        Sr, Th = self.time_dic['Sr'], self.time_dic['Th']
        if Sr == Th:
            Th += 1
        data = self.data_dic[:, Sr:Th]
        temp = []
        for i in range(36):
            a = np.sort(np.append(signal.find_peaks(data[i])[0], signal.find_peaks(-data[i])[0]))
            cur = data[i, :] / np.max(np.abs(data[i, :]))
            temp.append(np.sum(np.abs(np.diff(cur[a]))))
            temp[i] += np.abs(cur[0]) + np.abs(cur[-1])
            # temp[i] *= a.shape[0]
        temp = np.array(temp) / (Th - Sr)
        st_fluc_score = np.max(temp) * 100
        self.indicator['stfs'] = st_fluc_score
        return 0

    def cal_all_indicators(self):
        self.cal_t_amp_ratio()
        self.cal_t_amp_ratio1()
        self.cal_st_raise()
        self.cal_t_smooth()
        self.cal_t_smooth1()
        self.cal_jt_qrs1()
        self.cal_st_slope()
        self.cal_st_slope1()
        self.cal_t_flatness()
        self.cal_t_flatness1()
        self.cal_t_strength()
        self.cal_t_strength3()
        self.cal_t_steep_neg()
        self.cal_t_steep_pos()
        self.cal_t_steep_ratio()
        self.cal_timeloc_diff()
        self.cal_timeloc_diff2()
        self.cal_t_strength_noise()
        self.cal_t_strength_noise1()
        self.cal_t_strength_qrs()
        self.cal_st_fluc_score()
        return 0

    def get_indicators(self):
        self.cal_all_indicators()
        return self.indicator


def main(path: str):
    read_data = Data(path)
    data, time, rms = read_data.get()
    time_wave = TimeWave(data, time, rms)
    indicator_dict = time_wave.get_indicators()
    return indicator_dict


if __name__ == '__name__':
    main(r"C:\Users\<USER>\Desktop\20231106work\20230918label\119.txt")