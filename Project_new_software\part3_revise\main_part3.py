'''
@Project ：pythonProject 
@File    ：main_part3.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/11/12 16:16 
'''
import time
from source_recon.source_recon import *
def main_part3(mcg_path, heart_path, time_loc):
    """
    :param mcg_path: 心磁路径
    :param heart_path: 心脏模型路径
    :param time_loc: 时刻点 np.array([Qp, Rp, To,Tp,Te]), 分别是Q峰，R峰，T初，T峰，T末
    :return:
    """
    heart_path = heart_path + '\\' + 'poisson_mesh_lcav.msh'
    with open(mcg_path, 'r') as ff:
        file_data = ff.read()  # 读取的是字符串
    da = []
    for i in file_data.split():  # 把上面字符串记为数字插入
        da.append(float(i))
    datat = []
    for i in range(int(len(da) / 37)):
        datat.append(da[37 * i + 1:37 * (i + 1)])
    mcg_data = np.array(datat)
    source = SourceRecon(heart_path, time_loc, mcg_data)
    source.get_mesh()
    source.model_heart()
    result = source.source_strength_tt()
    movement = np.array([source.move_x, source.move_y, source.move_z])
    return movement, np.array(result)


if __name__ == '__main__':
    start_time = time.time()
    "example 1"
    mcg_path = r'data/BJ_TT_001327.txt'
    time_loc = np.array([278, 305, 510, 580, 612])

    "example 2"
    # mcg_path = r'data/BJ_TT_000516.txt'
    # time_loc = np.array([324, 359, 558, 645, 683])

    heart_path = 'model'
    movement, result = main_part3(mcg_path, heart_path, time_loc)
    # print(movement, result)
    end_time = time.time()
    print('operator time = %f' % (end_time - start_time))
