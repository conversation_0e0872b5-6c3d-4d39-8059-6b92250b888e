import sys
import numpy as np
import cv2


def mcg100(mlines, j1):
    mcgdata = np.array(mlines[j1].split()[1:]).reshape((6, 6))
    mcgdata = mcgdata.astype(float)
    matrix = cv2.resize(mcgdata, (100, 100),
                        interpolation=cv2.INTER_CUBIC)  # 双三次插值
    return matrix


def norms0(Z):
    max_n = float('%4.6f' % np.max(Z))
    min_n = float('%4.6f' % np.min(Z))
    for i1 in range(Z.shape[0]):  # 正负分别归一化
        for j1 in range(Z.shape[1]):
            if Z[i1, j1] < 0:
                Z[i1, j1] = -1 * Z[i1, j1] / min_n
            else:
                Z[i1, j1] = Z[i1, j1] / max_n
    return Z


def fivepos(pn03, matrix):  # 8方位5近邻连续极值
    m = len(pn03[0])
    if m > 1:
        pnps = [pn03[0][0]]
        neighbor = [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1],
                    [1, 0], [1, 1]]
        rnum, cnum = matrix.shape
        for i1 in range(1, m):
            corx, cory = pn03[0][i1][1] - 1, pn03[0][i1][2] - 1
            fiveflag = 1  # 噪声标识
            for nb in neighbor:
                for k1 in range(5):
                    xnew = int(corx + nb[0] * k1)
                    ynew = int(cory + nb[1] * k1)
                    # print(xnew, ynew)
                    if xnew + nb[0] < 0 or xnew + nb[0] > rnum - 1:
                        break
                    if ynew + nb[1] < 0 or ynew + nb[1] > cnum - 1:
                        break
                    if matrix[xnew, ynew] < matrix[xnew + nb[0], ynew + nb[1]]:
                        fiveflag = 0  # 曲率异常
                        break
                if fiveflag == 0:
                    break
            if fiveflag == 1:
                pnps.append(pn03[0][i1])
        pn03[0] = pnps


def ifpos(pn03, matrix):
    neighb = [-1, 0, 1]
    rnum, cnum = matrix.shape
    for corx in range(rnum):
        for cory in range(cnum):
            if matrix[corx, cory] <= 0:  # 正负筛选
                continue
            flag = 1  # 极值标识
            for j1 in neighb:
                if corx + j1 < 0 or corx + j1 > rnum - 1:
                    continue  # 维度内
                for j2 in neighb:
                    if cory + j2 < 0 or cory + j2 > cnum - 1:
                        continue  # 维度内
                    if j1 == 0 and j2 == 0:
                        continue  # 去除自己
                    if matrix[corx, cory] < matrix[corx + j1, cory + j2]:
                        flag = 0  # 严格极小
                        break
                if flag == 0:
                    break
            if flag == 1:  # 局部极值
                pn03[0].append([matrix[corx, cory], corx + 1, cory + 1])  # +1
                # if fivepos(matrix, corx, cory):


def fiveneg(pn03, matrix):  # 8方位连续极值
    n = len(pn03[1])
    if n > 1:
        pnns = [pn03[1][0]]
        neighbor = [[-1, -1], [-1, 0], [-1, 1], [0, -1], [0, 1], [1, -1],
                    [1, 0], [1, 1]]
        rnum, cnum = matrix.shape
        for i1 in range(1, n):
            corx, cory = pn03[1][i1][1] - 1, pn03[1][i1][2] - 1
            fiveflag = 1  # 噪声标识
            for nb in neighbor:
                for k1 in range(5):
                    xnew = int(corx + nb[0] * k1)
                    ynew = int(cory + nb[1] * k1)
                    if xnew + nb[0] < 0 or xnew + nb[0] > rnum - 1:
                        break
                    if ynew + nb[1] < 0 or ynew + nb[1] > cnum - 1:
                        break
                    if matrix[xnew, ynew] > matrix[xnew + nb[0], ynew + nb[1]]:
                        fiveflag = 0  # 曲率异常
                        break
                if fiveflag == 0:
                    break
            if fiveflag == 1:
                pnns.append(pn03[1][i1])
        pn03[1] = pnns


def ifneg(pn03, matrix):
    neighb = [-1, 0, 1]
    rnum, cnum = matrix.shape
    # cnt = 0
    for corx in range(rnum):
        for cory in range(cnum):
            if matrix[corx, cory] >= 0:  # 正负筛选
                continue
            flag = 1  # 极值标识
            for j1 in neighb:
                if corx + j1 < 0 or corx + j1 > rnum - 1:
                    continue  # 维度内
                for j2 in neighb:
                    if cory + j2 < 0 or cory + j2 > cnum - 1:
                        continue  # 维度内
                    if j1 == 0 and j2 == 0:
                        continue  # 去除自己
                    if matrix[corx, cory] > matrix[corx + j1, cory + j2]:
                        flag = 0  # 严格极大
                        break
                if flag == 0:
                    break
            if flag == 1:  # 局部极值
                # cnt += 1
                # print(cnt)
                # if fiveneg(matrix, corx, cory):
                pn03[1].append([matrix[corx, cory], corx + 1, cory + 1])


def sortlis(pn03):
    m = len(pn03[0])
    n = len(pn03[1])
    if m > 1:  # 多正极子排序
        data = np.array(pn03[0])
        idex = np.lexsort((data[:, 2], data[:, 1], -1 * data[:, 0]))
        sorted_data = data[idex]
        pn03[0] = sorted_data.tolist()
    if n > 1:  # 多负极子排序
        data = np.array(pn03[1])
        idex = np.lexsort((data[:, 2], data[:, 1], data[:, 0]))
        sorted_data = data[idex]
        pn03[1] = sorted_data.tolist()


def truncate(pn03, vmin):
    if len(pn03[0]) > 1:
        pnps = [pn03[0][0]]
        for i1 in range(1, len(pn03[0])):
            if pn03[0][i1][0] > vmin:
                pnps.append(pn03[0][i1])
        pn03[0] = pnps
    if len(pn03[1]) > 1:
        pnns = [pn03[1][0]]
        for i1 in range(1, len(pn03[1])):
            if pn03[1][i1][0] < -1 * vmin:
                pnns.append(pn03[1][i1])
        pn03[1] = pnns


def merge(pndn):  # 按顺序合并5近邻极子
    pn = [[], []]
    for j1 in range(len(pndn[0])):  # 正极子列表
        if j1 > len(pndn[0]) - 1:
            break
        pn[0].append(pndn[0][j1])
        if j1 == len(pndn[0]) - 1:
            break
        pnleft = pndn[0][j1 + 1:].copy()
        for k1 in range(len(pnleft)):
            if abs(pndn[0][j1][1] - pnleft[k1][1]) <= 5 and abs(
                    pndn[0][j1][2] - pnleft[k1][2]) <= 5:
                pndn[0].remove(pnleft[k1])
    for j1 in range(len(pndn[1])):  # 负极子列表
        if j1 > len(pndn[1]) - 1:
            break
        pn[1].append(pndn[1][j1])
        if j1 == len(pndn[1]) - 1:
            break
        pnleft = pndn[1][j1 + 1:].copy()
        for k1 in range(len(pnleft)):
            if abs(pndn[1][j1][1] - pnleft[k1][1]) <= 5 and abs(
                    pndn[1][j1][2] - pnleft[k1][2]) <= 5:
                pndn[1].remove(pnleft[k1])
    return pn


def statpn(matrix, vmin=0.3):  # 去除列表中的空字符
    pn03 = [[], []]
    ifpos(pn03, matrix)  # 统计+正负筛选
    ifneg(pn03, matrix)
    sortlis(pn03)  # 排序
    fivepos(pn03, matrix)  # 5近邻曲率筛选
    fiveneg(pn03, matrix)
    # print(len(pn03[0]), len(pn03[1]))
    truncate(pn03, vmin)  # 数值区间筛选
    # print(len(pn03[0]), len(pn03[1]))
    merge(pn03)  # 5近邻极子合并
    # print(len(pn03[0]), len(pn03[1]))
    return pn03


def writepn(pns, files, j):
    m = len(pns[0])
    n = len(pns[1])
    doc = open(files, 'a')
    doc.write('\n%4d %2d %2d,' % (j, m, n))
    for j1 in range(m):
        # print(len(pns[0][j1]))
        # for j2 in range(len(pns[0][j1][0])):
        #     print(pns[0][j1][0][j2])
        doc.write(' %4.6f %3d %3d' %
                  (pns[0][j1][0], pns[0][j1][1], pns[0][j1][2]))
    doc.write(',')  # 分割符
    for j1 in range(n):
        doc.write(' %4.6f %3d %3d' %
                  (pns[1][j1][0], pns[1][j1][1], pns[1][j1][2]))
    doc.close()
    return m, n


if __name__ == '__main__':
    mcgfile = r'D:\Work\001_DataSet\定位总数据集\Text\SHLY_2022_000010.txt'
    plfile = ''  # 极子
    doc = open(mcgfile, 'r')
    mlines = doc.readlines()
    doc.close()
    j = 503  # eg
    matrix = mcg100(mlines, j - 1)  # 扩张心磁
    Z = norms0(matrix)  # 正负归一化
    pn = statpn(Z, vmin=0.3)  # 多极子
    writepn(pn, plfile, j)  # 写入多极子——
