'''
@Project ：pythonProject2025 
@File    ：source_recon.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/1/2 11:21
用于电流源模型还原及计算 QRS-TT时刻的相关参数
'''
import cv2
import pandas as pd
import meshio
import multiprocessing
from .indicator_jiao import *
from .indicator_xu import *
from .fun_collection import *
from mayavi import mlab
import matplotlib.pyplot as plt
mlab.options.offscreen = True
class SourceRecon:
    def __init__(self, heart_path, lcav_path, time_loc, mcg_data, name):
        """
        :param dl:
        :param d_z_max:
        :param bz:
        :param torso: 人体躯体模型
        """
        self.dl = np.arange(0, 0.21, 0.04)
        self.width, self.length, self.num_layers = 176.6, 139.8, 233
        self.cta_x, self.cta_y, self.cta_z, self.cta_z_aver = 1.37e+02, 4.083e+01, 49.52, 57.40  # cta心尖的位置
        self.d_z_max = (176.6 + 40) / 1000
        self.Qp, self.Rp, self.Sp, self.To, self.Tp, self.Te \
            = time_loc[0], time_loc[1], time_loc[2], time_loc[3], time_loc[4], time_loc[5]
        self.name, self.lf_heart, self.lf_cav = name, None, None
        self.heart_path, self.lcav_path = heart_path, lcav_path
        self.heart_points, self.heart_tri, self.heart_treta = None, None, None
        self.lcav_points, self.lcav_tri, self.lcav_treta = None, None, None
        self.mcg_data = mcg_data
        self.mcg_data_interpolate = mcg_interpolate(self.mcg_data)  # 心磁的插值数据
        self.tran_x, self.tran_y = None, None
        self.lcav_cen_points = None
        self.point_qr = None  # 用于计算QR离散度

    def get_mesh(self):
        """
        :return: 获取左心室的点集及网格
        """

        heart_mesh = meshio.read(self.heart_path, file_format='gmsh')  # 表面及内部网格
        self.heart_points = np.asarray(heart_mesh.points * 100)
        self.heart_tri = np.asarray(heart_mesh.cells_dict['triangle'])
        # self.heart_treta = np.asarray(heart_mesh.cells_dict['tetra'])

        # 读取左心室的表面及内部网格，由于在创立网格的时候先对目标点 缩小200，因此这里需要乘以200
        lcav_mesh = meshio.read(self.lcav_path, file_format='gmsh')
        self.lcav_points = np.asarray(lcav_mesh.points * 200)
        self.lcav_tri = np.asarray(lcav_mesh.cells_dict['triangle'])
        self.lcav_treta = np.asarray(lcav_mesh.cells_dict['tetra'])

    def cta2_mcg1(self):
        """
        将cta的心脏2和心磁1对准
        :return: dx, dy 为 cta模型2相较于mcg模型1的偏移量的偏移量
         cta + (dx, dy) = mcg
        """
        qr_range = np.arange(self.Qp, self.Rp + 5)
        text = np.zeros((len(qr_range), 3))
        x_1 = np.mgrid[0.2:20.1: 0.2]
        y_1 = np.mgrid[0.2:20.1:0.2]
        [xi, yi] = np.meshgrid(x_1, y_1)
        for k in qr_range:  # 所有时刻点
            b = self.mcg_data_interpolate[k] * 10 ** (-12)
            index_x, index_y = magnetic_gradient(b, xi, yi)
            text[k - qr_range[0]][0] = k
            text[k - qr_range[0]][1] = index_x * 10
            text[k - qr_range[0]][2] = index_y * 10
        text_min_index = np.argmin(text[:, 2])
        while text_min_index <= len(qr_range) - 1:  # 计算出mcg_x, mcg_y也就是心磁最右下的点源与心尖对齐
            if text_min_index < len(qr_range) - 1 and (text[text_min_index][2] == text[text_min_index + 1][2] and
                    text[text_min_index + 1][1] > text[text_min_index][1]):
                text_min_index += 1
            else:
                if text[text_min_index][1] < np.max(text[:, 1]) - 20:
                    mcg_x, mcg_y = np.max(text[:, 1]) - 10, text[text_min_index][2]
                    break
                else:
                    mcg_x, mcg_y = text[text_min_index][1], text[text_min_index][2]
                    break
        dx, dy = mcg_x - self.cta_x, mcg_y - self.cta_y
        return dx, dy

    def cta_mcg(self):
        """
        cta数据与mcg数据对准
        :return: 给出最后平移完的心脏坐标，使得CTA数据的坐标和MCG的坐标对齐
        """
        self.heart_points[:, 0] = self.heart_points[:, 0] / 512 * self.width
        self.heart_points[:, 1] = self.heart_points[:, 1] / 512 * self.width
        self.heart_points[:, 2] = self.heart_points[:, 2] / self.num_layers * self.length
        self.heart_points = self.heart_points[:, [1, 2, 0]]
        self.lcav_points[:, 0] = self.lcav_points[:, 0] / 512 * self.width
        self.lcav_points[:, 1] = self.lcav_points[:, 1] / 512 * self.width
        self.lcav_points[:, 2] = self.lcav_points[:, 2] / self.num_layers * self.length
        self.lcav_points = self.lcav_points[:, [1, 2, 0]]

        # CT真实心脏点坐标转换成MCG下心脏点坐标需要的加上的偏移量
        dx, dy = self.cta2_mcg1()
        dx, dy = dx + 20, dy - 20  # 由于对准的是心尖，将心脏再向右向下各移20mm
        self.heart_points[:, 0] = self.heart_points[:, 0] + dx
        self.heart_points[:, 1] = self.heart_points[:, 1] + dy
        self.heart_points[:, 2] = self.heart_points[:, 2] + (self.cta_z_aver - self.cta_z)
        self.lcav_points[:, 0] = self.lcav_points[:, 0] + dx
        self.lcav_points[:, 1] = self.lcav_points[:, 1] + dy
        self.lcav_points[:, 2] = self.lcav_points[:, 2] + (self.cta_z_aver - self.cta_z)
        self.heart_points = self.heart_points / 1000
        self.lcav_points = self.lcav_points / 1000
        self.lcav_cen_points = get_treta_center(self.lcav_points[self.lcav_treta[:, 0], :],
                                                self.lcav_points[self.lcav_treta[:, 1], :],
                                                self.lcav_points[self.lcav_treta[:, 2], :],
                                                self.lcav_points[self.lcav_treta[:, 3], :])

    def get_lf(self):
        """
        :return: 整个左心室的lf函数
        """
        self.lf_cav = np.zeros((36, self.lcav_cen_points.shape[0] * 2))
        self.u_lead_lcav = np.zeros((36, self.lcav_cen_points.shape[0] * 2))
        t_1 = dev_lead_field_1(self.lcav_cen_points, self.dl, self.d_z_max)
        t_2 = dev_lead_field_1(self.lcav_cen_points, self.dl, self.d_z_max + 0.05)
        t_3 = dev_lead_field_1(self.lcav_cen_points, self.dl, self.d_z_max + 0.10)
        t = t_1 - 2 * t_2 + t_3
        for i in range(self.lcav_cen_points.shape[0]):
            lf = t[i, :, :]
            self.lf_cav[:, 2 * i: 2 * i + 2] = lf[:, 0:2]
            u_lead, s_lead, vt_lead = np.linalg.svd(self.lf_cav[:, 2 * i: 2 * i + 2])
            self.u_lead_lcav[:, 2 * i: 2 * i + 2] = u_lead[:, 0:2]

    def rap_music(self, b, ii):
        """
        主要针对整个左心室， 使两个点相关性大于0.75的点去除, 取多个点源
        :param b: 磁场 b
        :param ii: 时刻 ii
        :return: 对于单点的室间隔点源还原
        """
        b_1 = b.reshape((36, 1))
        signal_subspace = sig_subspace(b_1)  # 求出磁场b_1的特征向量
        if self.lf_cav is None:
            self.get_lf()

        corr = signal_subspace.T @ self.u_lead_lcav  # 求每个点与信号的相关性
        corr_all = np.sqrt(corr[0, 0::2] ** 2 + corr[0, 1::2] ** 2)  # 求解相关性
        max_index = np.argmax(corr_all)  # 相关性最大的索引
        lf_max = self.lf_cav[:, 2 * max_index: 2 * max_index + 2]  # 相关性最大点的引导场矩阵
        max_miu, max_x1 = sub_corr_1(lf_max, signal_subspace)  # 最大相关性和电流源方向
        cur_dir = max_x1.reshape(1, 2)
        lf_max_1 = lf_max @ max_x1.reshape(2, 1)
        intensity = lf_max_1.T @ b_1 / norm(lf_max_1) ** 2  # 计算电流强度
        point = np.array([[self.lcav_cen_points[max_index][0], self.lcav_cen_points[max_index][1],
                           self.lcav_cen_points[max_index][2]]])
        error = norm(lf_max_1 @ intensity.reshape((intensity.shape[0], 1)) - b.reshape((36, 1))) / norm(b)
        result_1 = [ii, max_index, cur_dir, intensity, point, max_miu, error]
        return result_1

    def source_music_qr(self):
        """
        :return: 直接进行多点源计算，并给出电流源qrs时刻的指标—jiao\Xu
        """
        time_qrs = list(range(self.Qp - 10, self.Sp + 10 + 1))  # Qp - 10 ~~~ Sp + 10
        result = []
        for ii in time_qrs:
            b_mat = (-self.mcg_data[ii, :] * 10 ** (-12)).reshape(6, 6)
            b_mat_1 = b_mat[::-1]
            b = b_mat_1.flatten()
            result_1 = self.rap_music(b, ii)
            result.append(result_1)

        """
            指标生成-jiao
        """
        time, max_index_1, cur_dir_1, inten_1, point_1 = data_process(result)  # 电流源数据预处理
        self.point_qr = point_1[10:self.Rp - (self.Qp - 10) + 1, :]
        q_rotate_angle = get_ind_q_angle(time, cur_dir_1, inten_1, self.Qp, self.name)
        q_angle, q_move = get_ind_q_locate(time, cur_dir_1, inten_1, point_1, self.Qp, self.name, self.lcav_points)
        r_rotate_angle, r_max_angle = get_ind_r_angle(time, cur_dir_1, inten_1, self.Rp, self.name)
        s_angle, y_rate = get_ind_s_angle(time, cur_dir_1, inten_1, point_1, self.Sp, self.name, self.lcav_points)
        q_r_s_loc = get_q_r_s_loc(time, cur_dir_1, inten_1, point_1, self.Qp, self.Rp, self.Sp, self.name,
                                  self.lcav_points)
        q_r_loc, r_move = get_ind_r_locate(time, point_1, self.Qp, self.Rp, self.lcav_points)  # R与Q的位置, yq
        r_index = 0
        if q_r_loc > 0.001 and r_move < 0.432:   # Q与R的上下位置关系， y_{Q} - y_{R}
            r_index = 1

        result_jiao = [q_rotate_angle, q_angle, q_move, r_rotate_angle,
                    r_max_angle, s_angle, y_rate, q_r_loc, r_move, r_index]

        for i in range(len(q_r_s_loc[0]) - 1):  # q, r, s位置的添加
            result_jiao.append(q_r_s_loc[1][i + 1])
        front_q_r, front_r_s, front_q_s \
            = q_r_s_loc[1][2] - q_r_s_loc[1][3], q_r_s_loc[1][3] - q_r_s_loc[1][4], q_r_s_loc[1][2] - q_r_s_loc[1][4]
        long_q_r, long_r_s, long_q_s \
            = q_r_s_loc[1][5] - q_r_s_loc[1][6], q_r_s_loc[1][6] - q_r_s_loc[1][7], q_r_s_loc[1][5] - q_r_s_loc[1][7]
        up_q_r, up_r_s, up_q_s \
            = q_r_s_loc[1][8] - q_r_s_loc[1][9], q_r_s_loc[1][9] - q_r_s_loc[1][10], q_r_s_loc[1][8] - q_r_s_loc[1][10]
        result_jiao = result_jiao \
            + [front_q_r, front_r_s, front_q_s, long_q_r, long_r_s, long_q_s, up_q_r, up_r_s, up_q_s]

        '''
          指标生成-Xu
        '''
        Qp_index, Rp_index, Sp_index = 10, self.Rp - (self.Qp - 10), self.Sp - (self.Qp - 10)
        dir_1 = cur_dir_1 * inten_1
        qrs_max_point = [max_index_1[Qp_index], max_index_1[Rp_index], max_index_1[Sp_index],
                         max_index_1[Sp_index + 5], max_index_1[Qp_index + 10]]
        qrs_inten = np.abs(np.array([inten_1[Qp_index], inten_1[Rp_index], inten_1[Sp_index],
                           inten_1[Qp_index + 5], inten_1[Qp_index + 10]]))
        qx = np.array([dir_1[Qp_index][0], dir_1[Rp_index][0], dir_1[Sp_index][0],
                       dir_1[Qp_index + 5][0], dir_1[Qp_index + 10][0]])
        qy = np.array([dir_1[Qp_index][1], dir_1[Rp_index][1], dir_1[Sp_index][1],
                       dir_1[Qp_index + 5][1], dir_1[Qp_index + 10][1]])
        result_xu_1 = plot(self.lcav_cen_points, qrs_max_point)
        result_xu_2 = plot_rotation(self.lcav_cen_points, qrs_max_point, qrs_inten, qx, qy, self.point_qr)

        result = result_jiao + result_xu_1 + result_xu_2

        return result

    def source_music_tt(self):
        """
        :return: tt段指标结果
        """
        time_tt = list(range(self.To, self.Te + 1))
        time_tt.append(self.Rp)  # 为了增加RT夹角
        result = []
        for ii in time_tt:
            b_mat = (-self.mcg_data[ii, :] * 10 ** (-12)).reshape(6, 6)
            b_mat_1 = b_mat[::-1]
            b = b_mat_1.flatten()
            result_1 = self.rap_music(b, ii)
            # print(result_1)
            # cur_dir, intensity, point = result_1[2], result_1[3], result_1[4]
            result.append(result_1)
        cur_dir = np.squeeze(np.array([row[2] for row in result]))  # 这里用squeeze是为什么？为了只取一个维度
        # print(cur_dir)
        inten = np.squeeze(np.array([row[3] for row in result]))
        points = np.squeeze(np.array([row[4] for row in result]))
        error = np.squeeze(np.array([row[6] for row in result]))
        Tp_error = error[self.Tp - self.Te]
        Tp_points_y = points[self.Tp - self.Te, 1]
        l_cav_high, l_cav_low = np.max(self.lcav_points[:, 1]), np.min(self.lcav_points[:, 1])
        Tp_points_rate = (Tp_points_y - l_cav_low) / (l_cav_high - l_cav_low)
        points = points[:-1]
        result = TT_feature(cur_dir, inten, points, self.To, self.Tp, self.Te)
        return result + [Tp_error, Tp_points_rate]
