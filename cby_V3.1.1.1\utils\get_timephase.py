"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/05/27 16:40
desc: 时相参数算法
environment: py36
run:
    python get_timephase.py SY_TT_002407.txt
option:
    心磁文档、时刻点
"""


import sys
import numpy as np
import time


def gettimephase(mcgfile):
    # Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = gettime(mcgfile)
    # ### 时相参数
    Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, Th, To, Tp, Te, Tr, RR = mcgfile
    Sr = max(Sp, min([Sr, Sp+60, To]))  # 微调
    Qh = min(Qh, Qp)
    Pr = min(Pr, Qh)
    Pp = min(Pp, Pr)
    Ph = min(Ph, Pp)
    Th = min(To, max([Th, 2*To-Tp, Sr]))
    Tr = max(Te, min(Tr, 2*Te-Tp))
    # ST = max(0, Th - Sr)
    PP = Pr - Ph
    QRS = Sr - Qh
    PR = Qh - Ph
    # QRS_TT = QRS / (Tr - Th)
    # HR = 60 * 1000 / RR
    QT = Tr - Qh
    QTc = round(QT / np.sqrt(RR / 1000))
    output = {}
    # output['HR'] = HR
    output['PP'] = PP
    output['QRS'] = QRS
    # output['ST'] = ST
    # output['QRS/TT'] = QRS_TT
    output['PR'] = PR
    output['QT'] = QT
    output['QTc'] = QTc
    return output


if __name__ == '__main__':
    # mcgfile = sys.argv[1]  # id文档
    mcgfile = [114, 144, 174, 230, 249, 277, 297, 325, 454, 488, 552, 604, 638, 722]
    rt = 0
    for i in range(100):
        t1 = time.time()
        results = gettimephase(mcgfile)  # 输入时刻点列表——
        t2 = time.time() - t1
        rt += t2
    # print(results)
    print(rt/100)
