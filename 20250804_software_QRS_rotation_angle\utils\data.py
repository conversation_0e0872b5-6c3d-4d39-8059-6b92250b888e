import numpy as np

# from time_loc import removenull, drawqhsrrp, denoisemw, maindl, statedges, tuningedges, statrmsmi, statpeaks, statwaves
from utils.gettimes import *

class Data:
    def __init__(self, path: str):
        self.path = path
        self.data = None
        self.rms = None
        self.time_loc = None

    def read_data(self):
        if self.data is not None:
            return
        self.data = np.loadtxt(self.path, skiprows=0, delimiter='\t').T[1:]
        self.rms = np.sum(self.data * self.data / self.data.shape[0], axis=0)
        return

    def read_time(self):
        if self.time_loc is not None:
            return
        mcg_file = self.path
        # pqrstimg = '%s/%s_%s.png' % (pqrstdir, item, tline[1])
        doc = open(mcg_file, 'r', encoding='utf-8')
        mcgdata = doc.readlines()
        doc.close()
        # Qh/Sr初步定位
        ls36, lrms, lma, lmi, ma1, mi1 = statwaves(mcgdata)
        Qh, Qhrest, Sr, Srrest = statrmsmi(lrms)  # 定位Qh/Sr, 剩余备选
        # Rp初步定位
        Nrms = noiserms(lrms, Qh, 0.35)  # 计算Qh最大噪声rms
        Rp, Rprest, mainwaves, xss, yss = statpeaks(lrms, ls36, lma, lmi, Qh, Sr, Nrms, 0.5, 10, 0.5, 1 / 14, 5)  # 定位Rp/备选Rp/可靠波
        # writemainw(mainwf, item, xss, yss)  # QRS可靠波
        # Rp/Sp定位
        if len(Rprest) > 0:  # Rp为次大值
            Splis, Spnew = statedges(lrms, lma, lmi, ls36, Rprest, [Sr, 0], Nrms, 0.5, 10, 3, 4, 3, 1)  # 逆序查找波形
            if len(Spnew) > 0:
                Sp = Spnew[0]  # 查找到Sp
                Rp = Rprest[0]  # Rp替换为最大值*
                mainwaves = maindl(mainwaves, Rp)
                k1 = max(Rp - 10 - 1, Qh)
                k2 = min(Rp + 10 + 2, Sp - 8)
                Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
            else:
                Sp = Rprest[0]  # 查找不到，Sp为最大值
                mainwaves = maindl(mainwaves, Rp)
                mainwaves = maindl(mainwaves, Sp)
                k1 = max(Rp - 10 - 1, Qh)
                k2 = min(Rp + 10 + 2, Sp - 8)
                Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
        else:  # Rp为最大值
            mainwaves = maindl(mainwaves, Rp)
            Spnew = getpeak(mainwaves, Rp, ls36, lma, lmi, Sr, 3, 6, 1)  # 1逆序取Sp
            if len(Spnew) > 0:
                Sp = Spnew[0]  # 有可靠波
                mainwaves = maindl(mainwaves, Sp)
                k1 = max(Rp - 10 - 1, Qh)
                k2 = min(Rp + 10 + 2, Sp - 8)
                Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
            else:
                Splis, Spnew = statedges(lrms, lma, lmi, ls36, [Rp, 0], [Sr, 0], Nrms, 0.5, 10, 3, 4, 3, 1)  # 逆序查找波形
                if len(Spnew) > 0:
                    Sp = Spnew[0]  # 查找到Sp
                    k1 = max(Rp - 10 - 1, Qh)
                    k2 = min(Rp + 10 + 2, Sp - 8)
                    Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sp
                else:
                    k1 = max(Rp - 10 - 1, Qh)
                    k2 = min(Rp + 10 + 2, Sr - 8)
                    Rp = calrp(ls36, k1, Rp, k2)  # Rp微调-Qh/Sr
                    Sp = get05(lma, lmi, Rp, Sr, 0.5, 0)  # 查找不到顺序取0.5pT
        # if Sp - Rp < 1:  # RS间期控制
        #     Rp = Sp - 1
        # Qp定位
        Qpnew = getpeak(mainwaves, Rp, ls36, lma, lmi, Qh, 1.8, 12, 0)  # 0顺序取Qp
        if len(Qpnew) > 0:
            Qp = Qpnew[0]  # 有可靠波
            mainwaves = maindl(mainwaves, Qp)
        else:
            Qplis, Qpnew = statedges(lrms, lma, lmi, ls36, [Qh, 0], [Rp, 0], Nrms, 0.5, 10, 3, 4, 1.8, 0)  # 顺序查找波形
            if len(Qpnew) > 0:
                Qp = Qpnew[0]  # 查找到Sp
            else:
                Qp = get05(lma, lmi, Qh, Rp, 0.5, 1)  # 查找不到逆序取0.5pT
        if Rp - Qp < 1:  # QR间期控制
            Rp = Qp + 1
        # Sr修正
        Srrest.reverse()  # 改成顺序
        for j in range(len(Srrest)):
            if Srrest[j][0] > Sp:  # Sp后第一个备选
                Sr = Srrest[j][0]  # 默认为Sr
                break
        Sr = min(Sr, Sp + 60)  # 间期截断
        Sr = tuningedges(lma, lmi, Sp, Sr, 1.2, 1)  # 逆序微调Sr
        # Qh修正
        Qhrest.reverse()
        for j in range(len(Qhrest)):
            if Qhrest[j][0] < Qp:  # Qp前第一个备选
                Qh = Qhrest[j][0]
                break
        Qh = max(Qh, Qp - 35)  # 间期截断
        Qh = tuningedges(lma, lmi, Qh, Qp, 1.2, 0)  # 顺序微调Qh
        # Pp/Tp定位
        k1 = max(Sr + 1, Rp + 100)
        Tp, Tpv = getabsmax(lma, lmi, k1, len(lma))
        lrms0 = lrms[k1:]
        k2 = min(Qh - 1, Rp - 100)
        Pp, Ppv = getabsmax(lma, lmi, 0, k2)
        # Ph/Pr、Th/Tr、To/Te
        k1 = max(0, Pp - 30)
        k2 = min(Pp + 30, Qh)
        Ph, Pr = getrmsmi(lrms, k1, Pp, k2)
        Th, Tr = getthtr(lrms, Sr, Tp, len(lrms))
        Th = tuningedges(lma, lmi, Th, Tp - 20, 1.1, 0)  # 顺序微调Th
        Tr = tuningedges(lma, lmi, Tp + 20, Tr, 1.1, 1)  # 逆序微调Tr
        To, Te = caltt(lma, lmi, Th, Tp, Tr, Tpv / 2)
        Th = max(Th, 2 * To - Tp)  # 过长截断
        Tr = min(Tr, 2 * Te - Tp)
        self.time_loc = {'Ph': Ph, 'Pp': Pp, 'Pr': Pr, 'Qh': Qh,
                         'Qp': Qp, 'Rp': Rp, 'Sp': Sp, 'Sr': Sr,
                         'Th': Th, 'To': To, 'Tp': Tp, 'Te': Te,
                         'Tr': Tr, 'cnt': len(xss), }
        return

    def get(self):
        if self.data is None:
            self.read_data()
        if self.time_loc is None:
            self.read_time()
        return self.data, self.time_loc, self.rms

    def draw():
        return 