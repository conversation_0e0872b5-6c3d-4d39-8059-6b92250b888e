
## 可视化

### 概述
本程序主要是实现心磁向量环, 主函数main_part2


### 修改说明
在原有的part2基础上, 修改了箭头的大小。原本的箭头大小是根据三个时刻点的移动距离取最大，但是发现如果三个时刻的移动距离都很小，
那么箭头就会很小，甚至会看不见。 因此选取的整个波段中移动距离最大值和三个时刻的移动距离最小值做线性组合。

### 主函数输入及输出
直接调用 main_part2.py文件中的函数 main_part2(mcg_path, time_loc)即可
输入：分别为mcg_path及time_loc
其中 mcg_path是心磁路径, 文件后缀为'.txt'文件
time_loc是时刻点的向量，time_loc=np.array([Qh, Qp, R, Sp, Sr]), 分别为Q初，Q峰，R峰，S峰，S末
输出： 在本地输出一个 part2.jpg, 这就是心电向量环的文件
### 例子

```
例1
    mcg_path = r'data/GDNH_2023_000432.txt'
    time_loc = np.array([270, 276, 300, 328, 345])
    main_part2(mcg_path, time_loc)
运行时间为0.229
```





