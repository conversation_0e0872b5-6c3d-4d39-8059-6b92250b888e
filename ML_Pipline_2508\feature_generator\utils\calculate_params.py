'''
@Project ：002_Code_envs 
@File    ：calculate_params.py
@IDE     ：PyCharm 
<AUTHOR> Qu
@Date    ：2023/10/12 14:08 
@Discribe：------
'''

import heapq
import itertools
import math
import warnings

import cv2
import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import minimize
from scipy.signal import find_peaks
from scipy.spatial import ConvexHull
from scipy.stats import entropy, skew, kurtosis
from statsmodels.tsa.stattools import acf

warnings.filterwarnings("ignore")

def interpolate_array(arr, new_size=130):
    # 确保数组是一个numpy数组
    arr = np.array(arr)
    old_size = arr.shape[1]
    # 创建一个新的数组来保存插值后的数据
    new_arr = np.zeros((arr.shape[0], new_size))

    # 对每一行进行插值
    for i in range(arr.shape[0]):
        # 创建一个插值函数
        x = np.linspace(0, 1, old_size)
        y = arr[i, :]
        f = np.interp(np.linspace(0, 1, new_size), x, y)
        new_arr[i, :] = f

    return new_arr


def triangle_area(a, b, c):
    return 0.5 * abs((a[0] - c[0]) * (b[1] - a[1]) - (a[0] - b[0]) * (c[1] - a[1]))


# LTTB下采样
'''
首先将数据分为n个桶，每个桶中包含多个点。在每个桶中，算法选择一个使得与前一个和后一个桶的代表点构成的三角形面积最大的点作为该桶的代表点。
'''


def largest_triangle_three_buckets(data, threshold=130):
    N, M = data.shape
    bucket_size = M // threshold

    sampled_data = []
    for d in range(N):
        sampled_indices = [0]
        for i in range(1, threshold - 1):
            start = i * bucket_size
            end = start + bucket_size
            max_area = -1
            max_area_index = start
            a = (start, data[d, start])
            c = (end, data[d, end])
            for j in range(start + 1, end):
                b = (j, data[d, j])
                area = triangle_area(a, b, c)
                if area > max_area:
                    max_area = area
                    max_area_index = j
            sampled_indices.append(max_area_index)
        sampled_indices.append(M - 1)
        sampled_column = data[d, sampled_indices]
        sampled_data.append(sampled_column)

    return np.array(sampled_data)


# 路径优化

def heuristic(a, b):
    (x1, y1) = a
    (x2, y2) = b
    return abs(x1 - x2) + abs(y1 - y2)


# A*寻路算法
def astar_search(matrix, start, goal):
    # Ensure start and goal are within matrix bounds and navigable
    if (start[0] < 0 or start[0] >= 100 or start[1] < 0 or start[1] >= 100 or
            goal[0] < 0 or goal[0] >= 100 or goal[1] < 0 or goal[1] >= 100 or
            matrix[start[0]][start[1]] == 0 or matrix[goal[0]][goal[1]] == 0):
        return False

    neighbors = [(0, 1), (1, 0), (-1, 0), (0, -1)]

    open_set = set()
    came_from = {}
    g_score = {start: 0}
    f_score = {start: heuristic(start, goal)}
    heap = []

    heapq.heappush(heap, (f_score[start], start))

    while heap:
        current = heapq.heappop(heap)[1]

        if current == goal:
            return True  # Path found

        if current in open_set:
            open_set.remove(current)
        for i, j in neighbors:
            neighbor = current[0] + i, current[1] + j
            tentative_g_score = g_score[current] + 1  # all edges have distance 1

            if 0 <= neighbor[0] < 100 and 0 <= neighbor[1] < 100:  # within matrix bounds
                if matrix[neighbor[0]][neighbor[1]] == 0:  # Ignore blocked grid
                    continue
            else:
                continue  # Ignore grids out of matrix bounds

            if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g_score
                f_score[neighbor] = tentative_g_score + heuristic(neighbor, goal)
                if neighbor not in open_set:
                    open_set.add(neighbor)
                    heapq.heappush(heap, (f_score[neighbor], neighbor))

    return False  # Path not found



def dfs_optimized(matrix, x, y, threshold, w=0.8):
    threshold = w * threshold
    visited = set()
    stack = [(x, y)]
    # 根据阈值的正负设置不同的条件函数
    if threshold > 0:
        condition = lambda val: val >= threshold
    else:
        condition = lambda val: val <= threshold
    while stack:
        x, y = stack.pop()
        if (x, y) in visited or not (0 <= x < len(matrix) and 0 <= y < len(matrix[0])):
            continue
        visited.add((x, y))
        # 检查邻居
        for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < len(matrix) and 0 <= ny < len(matrix[0]) and condition(matrix[int(nx)][int(ny)]):
                stack.append((nx, ny))

    return list(visited)


# 统计量
def line_length(arr, x, y, dx, dy):
    length = 0
    while 0 <= x < arr.shape[0] and 0 <= y < arr.shape[1] and arr[x, y] != 0:
        length += 1
        x += dx
        y += dy
    return length


def angle_between(dx1, dy1, dx2, dy2):
    dot_product = dx1 * dx2 + dy1 * dy2
    magnitude1 = np.sqrt(dx1 ** 2 + dy1 ** 2)
    magnitude2 = np.sqrt(dx2 ** 2 + dy2 ** 2)
    cos_angle = dot_product / (magnitude1 * magnitude2)
    angle = np.degrees(np.arccos(np.clip(cos_angle, -1.0, 1.0)))  # clipping for numerical stability
    return angle


# 长轴短轴角度
def get_longest_shortest_line(arr, x, y):
    # Ensure input coordinates are within array bounds
    if not (0 <= x < arr.shape[0] and 0 <= y < arr.shape[1]):
        raise ValueError("Coordinates out of bounds")

    # Ensure the point itself doesn't have a value of 0
    if arr[x, y] == 0:
        raise ValueError("The point itself cannot have a value of 0")

    # Define search directions: (dx, dy) where dx, dy ∈ {-1, 0, 1}
    directions = [
        (0, 1), (1, 0), (1, 1), (1, -1)
    ]

    max_length = 0
    min_length = float('inf')
    max_dir = (0, 0)
    min_dir = (0, 0)

    # Check each direction and its opposite
    for dx, dy in directions:
        length = (
                line_length(arr, x, y, dx, dy) +
                line_length(arr, x, y, -dx, -dy) -
                1  # Avoid double-counting the center point
        )

        if length > max_length:
            max_length = length
            max_dir = (dx, dy)
        if length < min_length:
            min_length = length
            min_dir = (dx, dy)

    angle = angle_between(max_dir[0], max_dir[1], min_dir[0], min_dir[1])
    return max_length, min_length, angle


#  面积
def get_area(arr):
    return np.count_nonzero(arr)


#  电流
def get_current(mfm_fine):
    xx, yy = np.meshgrid(np.arange(0, 10.0, 0.1), np.arange(0, 10.0, 0.1))
    diff_xx = np.diff(xx, axis=1)
    diff_xx = np.round(np.hstack((diff_xx, diff_xx[:, -1].reshape(-1, 1))), 2)

    diff_yy = np.diff(yy, axis=0)
    diff_yy = np.round(np.vstack((diff_yy, diff_yy[-1, :])), 2)

    diff_mfm_x = np.diff(mfm_fine, axis=1)
    diff_mfm_x = np.hstack((diff_mfm_x, diff_mfm_x[:, -1].reshape(-1, 1)))

    diff_mfm_y = np.diff(mfm_fine, axis=0)
    diff_mfm_y = np.vstack((diff_mfm_y, diff_mfm_y[-1, :]))

    current_x = diff_mfm_y / diff_yy
    current_y = -diff_mfm_x / diff_xx

    mat_magni = np.sqrt(current_x ** 2 + current_y ** 2)
    mat_angle = np.arctan2(current_y, current_x) * (180 / np.pi)

    # 选取非零元素
    non_zero_elements = mat_magni[mat_magni != 0]
    # 计算非零元素的均值
    mean_non_zero = np.mean(non_zero_elements)

    # 最大电流密度的点
    max_magni = np.max(mat_magni)
    temp_mat_magni = np.sort(mat_magni)
    min_value = np.percentile(temp_mat_magni, 10)
    max_value = np.percentile(temp_mat_magni, 90)
    row_max_magni, col_max_magni = np.where(mat_magni == max_magni)
    max_magni = (max_magni - min_value) / (max_value - min_value + 1)
    magni_retuen = mean_non_zero / max_magni

    max_angle = mat_angle[row_max_magni, col_max_magni][0]
    max_x_position = xx[row_max_magni, col_max_magni][0]
    max_y_position = yy[row_max_magni, col_max_magni][0]

    return magni_retuen, max_angle, max_x_position, max_y_position


#  周长
def get_perimeter(arr):
    # 创建边界数组，标记所有与背景相邻的前景像素
    boundaries = np.zeros_like(arr)
    boundaries[1:-1, 1:-1] = (arr[1:-1, 1:-1] != 0) & (
            (arr[:-2, 1:-1] == 0) | (arr[2:, 1:-1] == 0) |
            (arr[1:-1, :-2] == 0) | (arr[1:-1, 2:] == 0)
    )
    boundaries[0, :] = arr[0, :] != 0
    boundaries[-1, :] = arr[-1, :] != 0
    boundaries[:, 0] = arr[:, 0] != 0
    boundaries[:, -1] = arr[:, -1] != 0

    # 获取边界点
    points = np.argwhere(boundaries)
    perimeter = len(points)

    if perimeter == 0:
        return 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0

    center_x = np.mean(points[:, 0])
    center_y = np.mean(points[:, 1])

    # 计算每个点相对于中心点的极坐标角度
    sorted_points = sorted(points, key=lambda point: np.arctan2(point[1] - center_y, point[0] - center_x))
    points_array = np.array(sorted_points)

    k = caculate_k(sorted_points) if sorted_points else 0

    result = find_extreme_points(points_array)
    l, r, t, d = (result['leftmost'], result['rightmost'], result['topmost'], result['bottommost']) if result else (
        0, 0, 0, 0)

    if points_array.size > 0:
        circle_radius, circle_area, circle_center = smallest_enclosing_circle(points_array)
        rect_area, rect_center = smallest_enclosing_rectangle(points_array)
    else:
        circle_radius, circle_area, circle_center = 0, 0, [0,0]
        rect_area, rect_center = 0, [0,0]

    return perimeter, k, l, r, t, d, circle_radius, circle_area, circle_center, rect_area, rect_center


# 斜率
def caculate_k(points):
    k = 0.0
    points = np.vstack([points, points[0]])
    diffs = np.diff(points, axis=0)
    valid_diffs = diffs[(diffs[:, 0] != 0) & (diffs[:, 1] != 0)]
    k = np.sum(valid_diffs[:, 1] / valid_diffs[:, 0])
    return k / len(points)


# 最外维的几个点
def find_extreme_points(points):
    """
    Optimized function to find the leftmost, rightmost, topmost, and bottommost points
    using numpy's vectorized operations.

    :param points: A numpy array of points.
    :return: A dictionary with the leftmost, rightmost, topmost, and bottommost points.
    """
    x_coords, y_coords = points[:, 0], points[:, 1]

    leftmost = points[x_coords.argmin()]
    rightmost = points[x_coords.argmax()]
    topmost = points[y_coords.argmax()]
    bottommost = points[y_coords.argmin()]

    return {
        "leftmost": tuple(leftmost),
        "rightmost": tuple(rightmost),
        "topmost": tuple(topmost),
        "bottommost": tuple(bottommost)
    }


def smallest_enclosing_circle(points):
    """
    Further optimized function to find the smallest circle that encloses all the given points.
    This version tries a different optimization algorithm.

    :param points: A numpy array of points.
    :return: A tuple containing the radius, area, and center of the smallest enclosing circle.
    """

    def objective(center):
        # Adjust the objective function to correctly handle the center
        center = np.array(center)
        return np.max(np.sum((points - center) ** 2, axis=1))

    x_center, y_center = np.mean(points, axis=0)
    center = minimize(objective, [x_center, y_center], method='Nelder-Mead').x  # Using Nelder-Mead algorithm
    radius = np.sqrt(objective(center))
    area = np.pi * radius ** 2
    return radius, area, center

def smallest_enclosing_rectangle(points):
    """
    Optimized function to find the smallest enclosing rectangle for a set of points.

    :param points: A numpy array of points.
    :return: A tuple containing the area and center point of the smallest enclosing rectangle.
    """
    try:
        hull = ConvexHull(points)
        hull_points = points[hull.vertices]
    except:
        # 如果共线不存在凸包返回00
        return 0, (0, 0)
    min_area = float('inf')
    min_area_rect_center = (0, 0)

    for i in range(len(hull_points)):
        edge = hull_points[(i + 1) % len(hull_points)] - hull_points[i]
        normal = np.array([-edge[1], edge[0]])

        # Using vectorized operations for projections
        projections = np.dot(points, normal)
        min_proj, max_proj = projections.min(), projections.max()
        edge_proj = np.dot(points, edge)
        min_edge, max_edge = edge_proj.min(), edge_proj.max()
        area = (max_proj - min_proj) * np.linalg.norm(edge)
        if area < min_area:
            min_area = area
            min_area_rect_center = hull_points[i] + edge / 2 + normal * ((max_proj + min_proj) / 2)
    return min_area, min_area_rect_center


# 内接圆
def find_largest_inscribed_circle(image, threshold=0.5):
    normalized_image = ((image + 1.0) * (255.0 / 2.0)).astype(np.uint8)

    # 将图像二值化：大于阈值的像素设置为255（白色），其他像素设置为0（黑色）
    # 注意：你可能还需要调整阈值范围，比如将它映射到[0, 255]范围
    _, binary_image = cv2.threshold(normalized_image, threshold * 255, 255, cv2.THRESH_BINARY)

    # 计算距离变换
    dist_transform = cv2.distanceTransform(binary_image, cv2.DIST_L2, 5)

    # 找到距离变换的最大值及其位置
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(dist_transform)

    # 计算圆的面积
    radius = max_val
    area = np.pi * (radius ** 2)

    return max_loc[0] - 1, max_loc[1] - 1, area  # 返回圆心坐标和面积


# 距离
def distance(x1, y1, x2, y2):
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


def process_poles(pn, mfm_fine_normalized):
    arr_temp = [[], []]  # 临时数组，用于存放处理过的极子
    drop_arr = []  # 数组，用于存放应该被忽略的极子

    for index, poles in enumerate(pn):
        if not poles:  # 如果没有极子，继续下一次迭代
            continue

        if len(poles) == 1:  # 如果只有一个极子，直接添加
            arr_temp[index].append(poles[0])
        else:
            # 对于多个极子，检查它们是否相连
            for p1, p2 in itertools.combinations(poles, 2):
                point1 = (int(p1[1]), int(p1[2]))
                point2 = (int(p2[1]), int(p2[2]))

                # 检查极子是否相连
                is_connected = astar_search(mfm_fine_normalized, point1, point2)
                if is_connected:
                    max_pole, min_pole = sorted([p1, p2], key=lambda x: x[0], reverse=True)
                    selected_pole = max_pole if index == 0 else min_pole

                    # 如果选定的极子尚未被添加或删除，则添加选定的极子
                    if selected_pole not in drop_arr + arr_temp[index]:
                        arr_temp[index].append(selected_pole)

                    # 删除未选中的极子
                    drop_arr.append(min_pole if selected_pole == max_pole else max_pole)
                else:
                    # 如果极子没有相连，且尚未被添加或删除，则添加它们
                    for pole in [p1, p2]:
                        if pole not in drop_arr + arr_temp[index]:
                            arr_temp[index].append(pole)

    return arr_temp  # 返回处理过的极子

def try_convert_numeric(df):
    non_convertible_cols = []

    for col in df.columns:
        if df[col].dtype == 'object':
            try:
                # 尝试转换为float类型
                df[col] = pd.to_numeric(df[col], errors='coerce')

                # 检查转换后是否有空值(NaN)
                nan_count = df[col].isna().sum()
                if nan_count > 0:
                    print(f"警告: 列 '{col}' 转换后有 {nan_count} 个空值")
                    non_convertible_cols.append({
                        'column': col,
                        'dtype': df[col].dtype,
                        'sample_values': df[col].head().tolist(),
                        'nan_count': nan_count
                    })
            except Exception as e:
                non_convertible_cols.append({
                    'column': col,
                    'dtype': df[col].dtype,
                    'sample_values': df[col].head().tolist(),
                    'error': str(e)
                })

    # 打印无法转换的列的信息
    if non_convertible_cols:
        print("\n以下列无法完全转换为数值型:")
        for col_info in non_convertible_cols:
            print(f"\n列名: {col_info['column']}")
            print(f"当前数据类型: {col_info['dtype']}")
            print(f"样例值: {col_info['sample_values']}")
            if 'nan_count' in col_info:
                print(f"空值数量: {col_info['nan_count']}")
            if 'error' in col_info:
                print(f"错误信息: {col_info['error']}")

    return df

def extract_features(df, i):
    """
    从 pandas DataFrame 中提取统计特征和形态学特征。

    参数:
    df (pandas.DataFrame): 输入的 DataFrame。
    i (int): 用于特征名称的索引。

    返回:
    pandas.DataFrame: 包含所有特征的单行 DataFrame。
    """
    if df.empty:
        raise ValueError("输入DataFrame不能为空")

    # 检查数值型列并尝试转换
    df = try_convert_numeric(df)

    # 再次检查非数值型列
    non_numeric_cols = []
    for col in df.columns:
        if not np.issubdtype(df[col].dtype, np.number):
            non_numeric_cols.append({
                'column': col,
                'dtype': df[col].dtype,
                'sample_values': df[col].head().tolist()
            })

    if non_numeric_cols:
        print("\n转换后仍有以下非数值型列:")
        for col_info in non_numeric_cols:
            print(f"\n列名: {col_info['column']}")
            print(f"数据类型: {col_info['dtype']}")
            print(f"样例值: {col_info['sample_values']}")
        raise ValueError("存在无法转换为数值型的列")

    features = {}

    for column in df.columns:
        col_data = df[column].dropna()

        if len(col_data) < 2:  # 确保有足够的数据点
            continue

        try:
            # 1. 基础统计特征
            stats_dict = {
                f"{column}_均值_{i}": np.mean(col_data),
                f"{column}_方差_{i}": np.var(col_data),
                f"{column}_标准差_{i}": np.std(col_data),
                f"{column}_中位数_{i}": np.median(col_data),
                f"{column}_最大值_{i}": np.max(col_data),
                f"{column}_最小值_{i}": np.min(col_data)
            }

            # 分位数统计
            q25, q75 = np.percentile(col_data, [25, 75])
            stats_dict.update({
                f"{column}_下四分位数_{i}": q25,
                f"{column}_上四分位数_{i}": q75,
                f"{column}_四分位距_{i}": q75 - q25
            })

            # 2. 分布统计特征
            if len(col_data) > 1:
                # 众数计算
                mode_result = stats.mode(col_data)
                mode_val = mode_result[0] if isinstance(mode_result, tuple) else mode_result.mode
                mabs = np.mean(np.abs(col_data))
                distribution_dict = {
                    f"{column}_众数_{i}": mode_val,
                    f"{column}_信息熵_{i}": entropy(col_data.value_counts()) if mabs != 0 else 0,
                    f"{column}_偏度_{i}": skew(col_data) if mabs != 0 else 0,
                    f"{column}_峰度_{i}": kurtosis(col_data) if mabs != 0 else 0,
                    f"{column}_自相关度_{i}": acf(col_data, nlags=1)[1] if mabs != 0 else 0
                }
                stats_dict.update(distribution_dict)

            # 3. 时序特征
            diff_series = np.diff(col_data)
            time_series_dict = {
                f"{column}_Lyapunov指数_{i}": np.mean(np.abs(diff_series)),
                f"{column}_差分均值_{i}": np.mean(diff_series),
                f"{column}_差分标准差_{i}": np.std(diff_series)
            }
            stats_dict.update(time_series_dict)

            # 4. 形态学特征
            # 4.1 趋势特征
            trend_dict = {
                f"{column}_上升比例_{i}": np.mean(diff_series > 0),
                f"{column}_最大连续上升长度_{i}": max_consecutive_length(diff_series > 0),
                f"{column}_最大连续下降长度_{i}": max_consecutive_length(diff_series < 0)
            }

            # 4.2 波动特征
            turning_points = find_turning_points(col_data)
            wave_dict = {
                f"{column}_转折点比例_{i}": len(turning_points) / len(col_data),
                f"{column}_平均波动幅度_{i}": np.mean(np.abs(diff_series)),
                f"{column}_波动率_{i}": np.std(diff_series) / np.mean(np.abs(col_data)) if mabs != 0 else 0
            }

            # 4.3 形状特征
            shape_dict = {
                f"{column}_形状因子_{i}": calculate_shape_factor(col_data) if mabs != 0 else 0,
                f"{column}_峰谷比_{i}": calculate_peak_valley_ratio(col_data) if mabs != 0 else 0,
                f"{column}曲线下总面积_{i}": np.trapz(col_data) if mabs != 0 else 0
            }

            # 更新所有特征
            features.update(stats_dict)
            features.update(trend_dict)
            features.update(wave_dict)
            features.update(shape_dict)

        except Exception as e:
            print(f"处理列 {column} 时发生错误: {str(e)}")
            continue

    return pd.DataFrame(features, index=[0])


def max_consecutive_length(bool_array):
    """计算最大连续True的长度"""
    return max([len(list(g)) for b, g in itertools.groupby(bool_array) if b] or [0])



def find_turning_points(data):
    """找出序列中的转折点"""
    diff_sign = np.diff(np.sign(np.diff(data)))
    return np.where(diff_sign != 0)[0] + 1


def calculate_shape_factor(data):
    """计算形状因子（均方根值/算术平均值）"""
    rms = np.sqrt(np.mean(np.square(data)))
    mean = np.mean(np.abs(data))
    return rms / mean if mean != 0 else 0


def calculate_peak_valley_ratio(data):
    """计算峰谷比"""
    peaks = find_peaks(data)[0]
    valleys = find_peaks(-data)[0]

    if len(peaks) == 0 or len(valleys) == 0:
        return 0

    peak_mean = np.mean(data[peaks]) if peaks.size > 0 else 0
    valley_mean = np.mean(data[valleys]) if valleys.size > 0 else 0

    return abs(peak_mean / valley_mean) if valley_mean != 0 else 0