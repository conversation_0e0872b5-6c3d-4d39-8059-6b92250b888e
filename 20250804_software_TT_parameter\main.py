"""
@ Author: Integrated from work by <PERSON> Xu
@ Date: 2025/5/07
@ Description: 集成单极子和多极子分析功能，对To-Te时间段内的MCG数据进行单极化/多极子的判断

"""

from scipy.ndimage import binary_erosion, binary_dilation
from scipy import ndimage
from scipy.spatial import ConvexHull
from skimage.measure import regionprops, label
from utils.utils_20250219 import *
from utils.fun_colletion import mcg_interpolate
from utils.gettimes1 import gettime
import os
import numpy as np


class MCGAnalyzer:
    def __init__(self):
        # 单极子分析参数
        self.MAX_POLE_RATIO = 4.5  # 正极值比阈值
        self.MIN_POLE_RATIO = 0.33  # 负极值比阈值
        self.MIN_POLE_normal_RATIO = 0.9  # 极值比正常范围下阈值
        self.MAX_POLE_normal_RATIO = 2.1  # 极值比正常范围上阈值
        self.SHAPE_SCALE = 0.2  # 形状指数得分权重
        self.AMPLITUDE_THRESHOLD_RATIO = 0.2  # 幅值阈值比例(最大幅值的20%)

        # 多极子分析参数
        self.config = {
            'interpolation_points': 99,  # 插值点数
            'circularity_threshold': 0.6,  # 圆形度阈值，低于此值被认为是不规则形状
            'pole_threshold': 0.35,  # 磁极检测阈值（最大/最小值的百分比）
            'local_max_min_distance': 5,  # 局部极值检测距离
            'noise_area_ratio': 0.05,  # 连通区域内的噪声过滤面积比阈值
            'intensity_ratio_threshold': 0.5,  # 连通区域内的噪声过滤强度比阈值
            'area2area_strength_ratio_threshold': 0.01,  # 判断是否离散or连通形态改变的之间的连线上的强度阈值
            'threshold_distance': 40}  # 判断连通多极子是否哑铃状的阈值：两个局部极值之间的距离阈值

    # ----- 单极子分析相关函数 -----
    def is_monopole(self, frame_data):
        """
        初步判断该帧数据是否为明显单极子，如果是明显单极子，则单极化指数为1或0.95，否则为0
        明显单极子判断条件包括：
            1. 信号全正/信号全负   指数为1
            2. 该帧极值比>4或者<0.35  指数为0.95
        Args: frame_data: 一帧磁图数据
        Returns:
            bool: 是否为单极子
            str: 单极子类型 ("正单极子", "负单极子","非单极子")
            float: 单极性指数值 (0.0或0.95或1.0)
            float: 极值比
        """
        # 1. 检查是否所有值都是正的或都是负的
        all_positive = np.all(frame_data > 0)
        all_negative = np.all(frame_data < 0)
        if all_positive:
            return True, "正单极子", 1.0, 'Nan'  # 全正则单极性指数为1.0
        elif all_negative:
            return True, "负单极子", 1.0, 'Nan'  # 全负则单极性指数为1.0

        # 2. 若存在正负磁极，则首先检查极值比：0.9-2.1 非单极子 <0.33或>4.5单极子    0.33-0.9以及2.1-4.5之间  后续继续判断,给极值比权重
        flat_data = frame_data.flatten()
        positive_values = flat_data[flat_data > 0]
        negative_values = flat_data[flat_data < 0]
        max_positive = np.max(positive_values)
        min_negative = np.min(negative_values)
        point_ratio = max_positive / abs(min_negative)
        if self.MIN_POLE_normal_RATIO <= point_ratio <= self.MAX_POLE_normal_RATIO:
            return False, "非单极子", 0.0, point_ratio
        if point_ratio > self.MAX_POLE_RATIO:
            return True, "正单极子", 0.95, point_ratio  # 极值比>4.5则单极性指数为0.95
        if point_ratio < self.MIN_POLE_RATIO:
            return True, "负单极子", 0.95, point_ratio  # 极值比<0.33则单极性指数为0.95

        return False, "非单极子", 0.0, point_ratio

    def calculate_polarity_distribution_score(self, frame_data, point_ratio):
        """
        计算极性分布得分
        只考虑绝对值大于该帧最大幅值20%的点
        Args: frame_data: 一帧磁图数据
        Returns:
            float: 极性分布得分 (0.0-0.3)
            str: 主导极性 ("正"或"负")
            float: 极性比例
        """
        flat_data = frame_data.flatten()
        max_amplitude = np.max(np.abs(flat_data))
        threshold = max_amplitude * self.AMPLITUDE_THRESHOLD_RATIO  # 设置阈值为最大幅值的20%
        significant_positive = np.sum((flat_data > threshold))
        significant_negative = np.sum((flat_data < -threshold))
        total_significant = significant_positive + significant_negative

        # 根据极值比确定主导极性
        if point_ratio < self.MIN_POLE_normal_RATIO:
            dominant_pole = "负"
            polarity_ratio = significant_negative / total_significant
        elif point_ratio > self.MAX_POLE_normal_RATIO:
            dominant_pole = "正"
            polarity_ratio = significant_positive / total_significant

        # 将比例映射到0-0.3的范围，0.5(均衡)对应0分，1.0(完全单一极性)对应0.3分
        polarity_score = (polarity_ratio - 0.5) / 0.5 * 0.3
        polarity_score = max(0.0, polarity_score)  # 确保非负

        return polarity_score, dominant_pole

    def analyze_pole_shape(self, bz_data, pole_type):
        """
        分析磁极的形状特征，使用梯度一致性检测磁极是否圆润，即使磁极被边缘截断也能准确评估
        Args:
            bz_data: 2D numpy array, 磁场数据
            pole_type: str, 'positive'或'negative'，指定分析哪种极性的磁极
        """
        # 1. 提取指定极性的磁极区域
        if pole_type == 'positive':
            threshold = 0.1 * np.max(bz_data)  # 使用最大值的10%作为阈值
            pole_mask = bz_data > threshold
            pole_name = "positive_pole"
            # 找到磁极峰值位置
            max_idx = np.argmax(bz_data)
            center_y, center_x = np.unravel_index(max_idx, bz_data.shape)
        else:  # negative pole
            threshold = np.min(bz_data) * 0.9  # 使用最小值的90%作为阈值（保持足够负值）
            pole_mask = bz_data < threshold
            pole_name = "negative_pole"
            # 找到磁极峰值位置
            min_idx = np.argmin(bz_data)
            center_y, center_x = np.unravel_index(min_idx, bz_data.shape)

        # 2. 计算磁场梯度 分析梯度方向的一致性 - 从中心向外辐射
        gy, gx = np.gradient(bz_data)
        y_indices, x_indices = np.where(pole_mask)
        # 计算各点到中心的方向向量
        vectors_to_center = np.array([[y - center_y, x - center_x] for y, x in zip(y_indices, x_indices)])
        norms = np.sqrt(np.sum(vectors_to_center ** 2, axis=1))
        norms[norms == 0] = 1.0
        unit_vectors = vectors_to_center / norms[:, np.newaxis]
        # 计算梯度方向（注意正负磁极的梯度方向处理不同）
        if pole_type == 'positive':
            # 正磁极的梯度应该指向外部，所以取负
            gradient_vectors = np.array([[-gy[y, x], -gx[y, x]] for y, x in zip(y_indices, x_indices)])
        else:
            # 负磁极的梯度应该指向内部，所以保持原样
            gradient_vectors = np.array([[gy[y, x], gx[y, x]] for y, x in zip(y_indices, x_indices)])

        # 单位化梯度向量
        grad_norms = np.sqrt(np.sum(gradient_vectors ** 2, axis=1))
        grad_norms[grad_norms == 0] = 1.0  # 避免除零
        unit_gradients = gradient_vectors / grad_norms[:, np.newaxis]

        # 计算点积，衡量方向一致性
        dot_products = np.sum(unit_vectors * unit_gradients, axis=1)

        # 一致性指标：点积的平均值（越接近1越一致）和标准差（越小越均匀）
        direction_consistency = np.mean(dot_products)
        direction_uniformity = np.std(dot_products)

        return direction_consistency, direction_uniformity

    def check_pole_shape(self, frame_data):
        """
        检查该帧正负磁极的形状
        frame_data: 一帧磁图数据
        Returns: tuple: (shape_issues, shape_abnormal_ratio)
                shape_issues: list, 形状异常的详细信息
                shape_abnormal_ratio: float, 形状异常比例
        """
        # 分析正负磁极形状
        pos_dir_consistency, pos_dir_uniform = self.analyze_pole_shape(frame_data, pole_type='positive')
        neg_dir_consistency, neg_dir_uniform = self.analyze_pole_shape(frame_data, pole_type='negative')

        return pos_dir_consistency, neg_dir_consistency

    def calculate_point_ratio_score(self, point_ratio):
        """
        计算极值比得分
        """
        # 定义正常范围和异常阈值
        normal_lower = self.MIN_POLE_normal_RATIO  # 0.9
        normal_upper = self.MAX_POLE_normal_RATIO  # 2.1
        abnormal_lower = self.MIN_POLE_RATIO  # 0.33
        abnormal_upper = self.MAX_POLE_RATIO  # 4.5

        # 如果在正常范围内，得分为0
        if normal_lower <= point_ratio <= normal_upper:
            score = 0.0

        # 如果已经在极端异常范围外，给满分
        if point_ratio <= abnormal_lower or point_ratio >= abnormal_upper:
            score = 0.5

        # 在过渡区间进行线性插值计算
        if abnormal_lower < point_ratio < normal_lower:  # 在0.33-0.9之间线性插值
            score = 0.5 * (normal_lower - point_ratio) / (normal_lower - abnormal_lower)
        elif normal_upper < point_ratio < abnormal_upper:  # 在2.1-4.5之间线性插值
            score = 0.5 * (point_ratio - normal_upper) / (abnormal_upper - normal_upper)

        return score

    def calculate_monopolarity_index(self, frame_data, point_ratio):
        """
        计算单极性指数  值越大越接近单极子特性
        Args:
            frame_data: 一帧磁图数据
        Returns:
            bool: 是否为单极子
            str: 单极子类型描述
            float: 单极性指数 (0.0 - 1.0)
            dict: 各项得分详情
        """
        point_ratio_score = self.calculate_point_ratio_score(point_ratio)  # 计算极值比得分
        polarity_score, dominant_pole = self.calculate_polarity_distribution_score(frame_data, point_ratio)  # 计算极性分布得分
        pos_dir_consistency, neg_dir_consistency = self.check_pole_shape(frame_data)  # 计算该帧磁图是否圆润
        min_consistency = min(pos_dir_consistency, neg_dir_consistency)
        shape_score = (1.0 - min_consistency) * self.SHAPE_SCALE  # 0表示完全圆润，0.2表示完全不圆润

        # 组合得分，总分不超过0.9
        monopolarity_index = point_ratio_score + polarity_score + shape_score
        monopolarity_index = min(0.89, monopolarity_index)
        score_details = {
            "极值比得分": point_ratio_score,
            "极性分布得分": polarity_score,
            "磁极形状得分": shape_score,
            "极值比": point_ratio,
            "单极性指数": monopolarity_index
        }

        # 确定单极子类型
        if monopolarity_index < 0.5:
            return False, "非单极子", monopolarity_index, score_details
        elif monopolarity_index < 0.9:
            return False, f"{dominant_pole}单极化趋势", monopolarity_index, score_details
        else:
            return True, f"{dominant_pole}单极子", monopolarity_index, score_details

    def _find_local_maxima(self, data, min_distance):
        """
        寻找局部极大值点，只保留强度达到最大极值点70%以上的点
        参数:
            data: 2D numpy array，磁场数据
            min_distance: int，极值点之间的最小距离
        返回:
            list: 局部极大值点坐标列表，格式为 [(y1, x1), (y2, x2), ...]
        """
        # 使用高斯滤波预处理
        smoothed = ndimage.gaussian_filter(data, sigma=0.5)
        max_neighborhood = ndimage.maximum_filter(smoothed, size=min_distance)  # 找到局部最大值
        local_max = (smoothed == max_neighborhood) & (smoothed > 0.3 * np.max(smoothed))
        all_coords = np.argwhere(local_max)  # 获取所有局部极大值点的坐标

        # 如果没有找到极值点，直接返回空列表
        if len(all_coords) == 0:
            return []

        intensities = [smoothed[y, x] for y, x in all_coords]  # 获取所有局部极大值点的强度值
        max_intensity = max(intensities)

        # 筛选出强度达到最大值65%的点
        filtered_coords = []
        for i, (y, x) in enumerate(all_coords):
            if smoothed[y, x] >= 0.65 * max_intensity:
                filtered_coords.append([y, x])

        return filtered_coords

    def _bresenham_line(self, y1, x1, y2, x2):
        """
        使用Bresenham算法生成两点之间的直线
        参数:
            y1, x1: 起点坐标
            y2, x2: 终点坐标
        返回:
            list: 直线上的所有点坐标 [(y, x), ...]
        """
        points = []
        dx = abs(x2 - x1)
        dy = abs(y2 - y1)
        sx = 1 if x1 < x2 else -1
        sy = 1 if y1 < y2 else -1
        err = dx - dy
        while True:
            points.append((y1, x1))
            if x1 == x2 and y1 == y2:
                break
            e2 = 2 * err
            if e2 > -dy:
                err -= dy
                x1 += sx
            if e2 < dx:
                err += dx
                y1 += sy

        return points

    def _calculate_concavity(self, region):
        """
        计算区域的凹度，通过计算凸包填充率来评估
        凸包填充率 = 区域面积 / 凸包面积
        凹度 = 1 - 凸包填充率 (较高的值表示更凹的形状)
        特别对月牙形状敏感
        参数: region: regionprops对象，表示连通区域
        返回: float: 凹度得分，0-1之间，值越大表示越凹
        """
        # 获取区域坐标点
        coords = region.coords
        # 计算凸包
        hull = ConvexHull(coords)
        # 计算凸包面积
        hull_area = hull.volume  # 2D情况下volume其实就是面积
        # 计算原始区域面积
        region_area = region.area

        # 计算凸包填充率
        if hull_area > 0:
            convex_filling_ratio = region_area / hull_area
        else:
            convex_filling_ratio = 1.0

        # 计算凹度：1减去凸包填充率
        concavity = abs(1.0 - convex_filling_ratio)

        return min(concavity, 1.0)  # 确保不超过1.0

    def _check_regions_connection(self, regions, bz_data, pole_type):
        """
        检查两个连通区域之间是否有极性不同的信号或信号强度过弱
        通过找到两个区域之间的最近点对来检查连接性
        参数:
            regions: 连通区域列表
            bz_data: 原始磁场数据
            pole_type: 'positive'或'negative'，表示极性
        返回:
            bool: True表示连通多极子(中间无极性不同的值且信号强度充足)，False表示离散多极子(中间有极性不同的值或信号强度过弱)
        """
        if len(regions) < 2:
            return True

        # 取最大的两个区域
        areas = [r.area for r in regions]
        largest_indices = np.argsort(areas)[-2:]  # 获取面积最大的两个区域的索引
        region1 = regions[largest_indices[1]]  # 最大区域
        region2 = regions[largest_indices[0]]  # 第二大区域

        # 获取两个区域的坐标点
        coords1 = region1.coords  # 区域1的所有像素坐标，形状为 (n, 2)，每行是 [y, x]
        coords2 = region2.coords  # 区域2的所有像素坐标

        # 找出两个区域之间的最近点对
        min_distance = float('inf')
        closest_point1 = None
        closest_point2 = None

        # 为了提高效率，如果区域像素太多，可以采样处理
        stride = max(1, min(len(coords1), len(coords2)) // 500)  # 采样步长，限制最多处理约500个点
        for i in range(0, len(coords1), stride):
            y1, x1 = coords1[i]
            for j in range(0, len(coords2), stride):
                y2, x2 = coords2[j]
                dist = (y2 - y1) ** 2 + (x2 - x1) ** 2  # 欧氏距离的平方
                if dist < min_distance:
                    min_distance = dist
                    closest_point1 = (y1, x1)
                    closest_point2 = (y2, x2)

        # 使用Bresenham算法生成两个最近点之间的直线
        y1, x1 = closest_point1
        y2, x2 = closest_point2
        points = self._bresenham_line(y1, x1, y2, x2)

        # 检查连接线上的值
        if pole_type == 'positive':
            is_connected = True
            for y, x in points:
                current_value = bz_data[y, x]
                if current_value <= self.config["area2area_strength_ratio_threshold"] * np.max(bz_data):
                    is_connected = False
                    break
        else:  # 'negative'
            is_connected = True
            for y, x in points:
                current_value = bz_data[y, x]
                if current_value >= self.config["area2area_strength_ratio_threshold"] * np.min(bz_data):
                    is_connected = False
                    break

        return is_connected

    def analyze_multipole_shape(self, bz_data):
        """
        分析磁极形状，计算多极子得分，并区分多极子类型
        参数: bz_data: 2D numpy array, 磁场数据
        返回: dict: 包含正负磁极形态分析结果
        """
        # 分析正负极形态
        pos_shape_info = self._analyze_single_pole(bz_data, 'positive')
        neg_shape_info = self._analyze_single_pole(bz_data, 'negative')
        pos_shape_score = pos_shape_info["形态得分"]
        neg_shape_score = neg_shape_info["形态得分"]

        # 确定多极子类型
        multipole_type = "无"

        # 1. 检查是否有分化的连通区域（直接判断为离散多极子）
        if pos_shape_info["4分化程度"] == 1.0 and pos_shape_score >= neg_shape_score:
            multipole_type = "正极子分化状形态改变"
            dominant_info = pos_shape_info
        elif neg_shape_info["4分化程度"] == 1.0 and neg_shape_score > pos_shape_score:
            multipole_type = "负极子分化状形态改变"
            dominant_info = neg_shape_info

        # 2. 如果没有分化的连通区域
        elif pos_shape_score >= 0.65 and neg_shape_score < 0.6:
            dominant_info = pos_shape_info
            if dominant_info["连通区域数"] > 1:
                # 检查连通区域之间是否有负值信号
                if dominant_info["是连通形态改变"]:
                    if dominant_info["连通多极子类型"] == "狭长":
                        multipole_type = "正极子连通狭长形态改变"
                    else:  # 哑铃状
                        multipole_type = "正极子连通哑铃状形态改变"
                else:
                    multipole_type = "正极子分化状形态改变"
            elif dominant_info["连通区域数"] == 1:  # 单连通判断为连通多极子
                if dominant_info["连通多极子类型"] == "狭长":
                    multipole_type = "正极子连通狭长形态改变"
                else:  # 哑铃状
                    multipole_type = "正极子连通哑铃状形态改变"

        elif pos_shape_score < 0.65 and neg_shape_score >= 0.6:
            dominant_info = neg_shape_info
            if dominant_info["连通区域数"] > 1:
                # 检查连通区域之间是否有正值信号
                if dominant_info["是连通形态改变"]:
                    if dominant_info["连通多极子类型"] == "狭长":
                        multipole_type = "负极子连通狭长形态改变"
                    else:  # 哑铃状
                        multipole_type = "负极子连通哑铃状形态改变"
                else:
                    multipole_type = "负极子分化状形态改变"
            elif dominant_info["连通区域数"] == 1:  # 单连通判断为连通多极子
                if dominant_info["连通多极子类型"] == "狭长":
                    multipole_type = "负极子连通狭长形态改变"
                else:  # 哑铃状
                    multipole_type = "负极子连通哑铃状形态改变"

        elif pos_shape_score >= 0.65 and neg_shape_score >= 0.6:
            if pos_shape_score >= neg_shape_score:
                dominant_info = pos_shape_info
                if dominant_info["连通区域数"] > 1:
                    # 检查连通区域之间是否有负值信号
                    if dominant_info["是连通形态改变"]:
                        if dominant_info["连通多极子类型"] == "狭长":
                            multipole_type = "正极子连通狭长形态改变"
                        else:  # 哑铃状
                            multipole_type = "正极子连通哑铃状形态改变"
                    else:
                        multipole_type = "正极子分化状形态改变"
                elif dominant_info["连通区域数"] == 1:  # 单连通区域判断为连通多极子
                    if dominant_info["连通多极子类型"] == "狭长":
                        multipole_type = "正极子连通狭长形态改变"
                    else:  # 哑铃状
                        multipole_type = "正极子连通哑铃状形态改变"
            else:
                dominant_info = neg_shape_info
                if dominant_info["连通区域数"] > 1:
                    if dominant_info["是连通形态改变"]:
                        if dominant_info["连通多极子类型"] == "狭长":
                            multipole_type = "负极子连通狭长形态改变"
                        else:  # 哑铃状
                            multipole_type = "负极子连通哑铃状形态改变"
                    else:
                        multipole_type = "负极子分化状形态改变"
                elif dominant_info["连通区域数"] == 1:  # 单连通区域判断为连通多极子
                    if dominant_info["连通多极子类型"] == "狭长":
                        multipole_type = "负极子连通狭长形态改变"
                    else:  # 哑铃状
                        multipole_type = "负极子连通哑铃状形态改变"

        else:  # 正负磁极都小于阈值
            if pos_shape_score <= neg_shape_score:
                dominant_info = neg_shape_info
            else:
                dominant_info = pos_shape_info
            multipole_type = "正常偶极子"

        return {
            "形态得分": dominant_info["形态得分"],
            "多极子类型": multipole_type
        }

    def _analyze_single_pole(self, bz_data, pole_type):
        """
        分析单个磁极的形状特征和多极子类型特征
        参数:
            bz_data: 2D numpy array, 磁场数据
            pole_type: str, 'positive'或'negative'，指定分析哪种极性，
        返回:
            dict: 形状分析结果
        """
        # 提取指定极性的磁极区域
        if pole_type == 'positive':
            threshold = self.config['pole_threshold'] * np.max(bz_data)
            pole_mask = bz_data > threshold
            max_idx = np.argmax(bz_data)
            center_y, center_x = np.unravel_index(max_idx, bz_data.shape)
        else:  # 负极
            threshold = self.config['pole_threshold'] * np.min(bz_data)
            pole_mask = bz_data < threshold
            min_idx = np.argmin(bz_data)
            center_y, center_x = np.unravel_index(min_idx, bz_data.shape)

        pole_mask = binary_erosion(pole_mask, iterations=1)
        pole_mask = binary_dilation(pole_mask, iterations=1)

        # 计算连通区域数 分析所有区域的几何特征
        labeled_mask, num_regions = label(pole_mask, return_num=True)
        regions = regionprops(labeled_mask)

        # 初始化为所有区域，默认不过滤
        filtered_regions = regions
        filtered_num_regions = num_regions
        filtered_mask = pole_mask.copy()

        # 仅当连通区域数大于1时才进行噪声过滤
        if num_regions > 1:
            # 过滤噪声区域
            valid_regions = []
            areas = [r.area for r in regions]
            max_area_idx = np.argmax(areas)
            max_area = areas[max_area_idx]

            # 计算最大连通区域中的最大强度值
            max_region = regions[max_area_idx]
            max_region_coords = max_region.coords
            if pole_type == 'positive':
                max_region_max_intensity = np.max(bz_data[max_region_coords[:, 0], max_region_coords[:, 1]])
            else:  # 负极
                max_region_max_intensity = np.min(bz_data[max_region_coords[:, 0], max_region_coords[:, 1]])

            filtered_mask = np.zeros_like(pole_mask, dtype=bool)
            for i, region in enumerate(regions):
                # 1. 计算区域的强度统计
                region_coords = region.coords
                if pole_type == 'positive':
                    region_max_intensity = np.max(bz_data[region_coords[:, 0], region_coords[:, 1]])
                    intensity_ratio = region_max_intensity / max_region_max_intensity
                else:  # 负极
                    region_max_intensity = np.min(bz_data[region_coords[:, 0], region_coords[:, 1]])
                    intensity_ratio = region_max_intensity / max_region_max_intensity if max_region_max_intensity < 0 else 0
                center_y, center_x = region.centroid

                # 2. 计算面积比例
                area_ratio = region.area / max_area  # 小连通区域/大连通区域面积

                # 3. 判断该连通区域是否为噪声区域
                is_noise = (area_ratio < self.config['noise_area_ratio'] and (
                        abs(intensity_ratio) < self.config['intensity_ratio_threshold']))
                if not is_noise:
                    valid_regions.append(region)
                    region_mask = labeled_mask == region.label
                    filtered_mask = filtered_mask | region_mask

            # 更新过滤后的区域和数量
            filtered_regions = valid_regions
            filtered_num_regions = len(valid_regions)

        # 只在过滤后的掩码区域内查找局部极值点
        if pole_type == 'positive':
            masked_data = np.where(filtered_mask, bz_data, -np.inf)  # 非区域内设为负无穷
            normalized_data = masked_data / np.max(masked_data) if np.max(masked_data) != -np.inf else masked_data
            local_extrema = self._find_local_maxima(normalized_data, self.config['local_max_min_distance'])
        else:  # 负极
            masked_data = np.where(filtered_mask, -bz_data, -np.inf)  # 非区域内设为负无穷
            normalized_data = masked_data / np.max(masked_data) if np.max(masked_data) != -np.inf else masked_data
            local_extrema = self._find_local_maxima(normalized_data, self.config['local_max_min_distance'])

        # 检查多极子区域之间的连接性
        is_connected_multipole = self._check_regions_connection(filtered_regions, bz_data, pole_type)

        # 判断连通多极子的类型（狭长或哑铃状）
        connected_multipole_type = "无"
        if is_connected_multipole and filtered_num_regions > 1:
            connected_multipole_type = "哑铃"  # 有多个连通区域，但是非分化，则为哑铃
        elif is_connected_multipole and filtered_num_regions == 1 and len(local_extrema) > 1:
            connected_multipole_type = self._check_extrema_distribution(local_extrema,
                                                                        self.config['threshold_distance'])
        elif is_connected_multipole and filtered_num_regions == 1:
            connected_multipole_type = "狭长"  # 默认单连通区域为狭长型

        # 如果检测到多个连通区域且不是连通的（即为离散的），则分化程度为1
        if filtered_num_regions > 1 and not is_connected_multipole:
            differentiation_degree = 1.0
        elif filtered_num_regions > 1 and is_connected_multipole:
            differentiation_degree = 0.8
        else:
            differentiation_degree = 0.0

        # 使用最大的区域进行分析
        if filtered_regions:
            filtered_areas = [r.area for r in filtered_regions]
            max_region_idx = np.argmax(filtered_areas)
            main_region = filtered_regions[max_region_idx]
        else:
            # 如果所有区域都被过滤掉，则使用原始最大区域
            areas = [r.area for r in regions]
            max_region_idx = np.argmax(areas)
            main_region = regions[max_region_idx]
            filtered_num_regions = 1  # 至少保留一个区域

        # 1. 计算凹度得分 (使用凸包填充比例)
        concavity = self._calculate_concavity(main_region)
        if concavity >= 0.4:  # 如果凹度得分达到或超过0.4，得分为1
            concavity_score = 1.0
        elif concavity <= 0.1:  # 如果凹度得分小于等于0.1，线性映射到0-0.5区间
            concavity_score = (concavity / 0.1) * 0.5
        else:  # 如果凹度得分在0.1到0.4之间，线性映射到0.5-1区间
            concavity_score = 0.5 + ((concavity - 0.1) / 0.3) * 0.5

        # 2. 计算偏心率（细长程度）
        main_eccentricity = main_region.eccentricity if hasattr(main_region, 'eccentricity') else 0

        # 3. 计算面积得分
        total_area = bz_data.shape[0] * bz_data.shape[1]
        main_area_ratio = main_region.area / total_area
        if main_area_ratio >= 0.7:  # 如果面积比例达到或超过0.7，得分为1
            main_area_score = 1.0
        elif main_area_ratio <= 0.3:  # 如果面积比例小于等于0.3，线性映射到0-0.5区间
            main_area_score = (main_area_ratio / 0.3) * 0.5
        else:  # 如果面积比例在0.3到0.7之间，线性映射到0.5-1区间
            main_area_score = 0.5 + ((main_area_ratio - 0.3) / 0.4) * 0.5

        # 4. 综合:形态得分: 凹度、偏心率、面积得分和分化程度 取最高
        if differentiation_degree == 1.0:  # 2个联通区： 分裂多极子
            irregularity_score = 0.9
        elif differentiation_degree == 0.8:  # 2个联通区： 连通多极子
            irregularity_score = 0.8
        else:  # 1个联通区：正常计算
            irregularity_score = concavity_score * 0.3 + main_eccentricity * 0.5 + main_area_score * 0.2

        return {
            "形态得分": irregularity_score,
            "1凹度得分": concavity_score,
            "2偏心率": main_eccentricity,
            "3面积得分": main_area_score,
            "4分化程度": differentiation_degree,
            "连通区域数": filtered_num_regions,  # 使用过滤后的区域数
            "局部极值点数": len(local_extrema),
            "局部极值点": local_extrema,
            "掩码": pole_mask,
            "中心": (center_y, center_x),
            "是连通形态改变": is_connected_multipole,
            "连通多极子类型": connected_multipole_type  # 新增连通多极子类型
        }

    def _check_extrema_distribution(self, local_extrema, threshold_distance):

        if len(local_extrema) <= 1:
            return "狭长"  # 只有一个极值点，肯定是狭长型

        # 计算所有点对之间的最大距离
        max_distance = 0
        for i in range(len(local_extrema)):
            y1, x1 = local_extrema[i]
            for j in range(i + 1, len(local_extrema)):
                y2, x2 = local_extrema[j]
                dist = np.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2)
                max_distance = max(max_distance, dist)

        # 根据最大距离判断类型
        if max_distance >= threshold_distance:
            return "哑铃状"
        else:
            return "狭长"

    def analyze_at_timepoint(self, bz_inter_data_all, time_point):
        """
        分析指定时间点的单极性指数和多极子指数
        Args:
            bz_inter_data_all: 心磁图插值后数据  100*100*N
            time_point: 时间点索引
        Returns:
            dict: 包含单极性指数和多极子指数的字典
        """
        # 先判断是否为明显单极子
        bz_inter_data = bz_inter_data_all[:, :, time_point]
        is_mono_point, pole_type_point, mono_index, point_ratio = self.is_monopole(bz_inter_data)

        # 如果是明显单极子，直接返回结果
        if is_mono_point:
            return {
                "单极性指数": mono_index,
                "多极子得分": 0.0,
                "磁图类型": pole_type_point
            }

        # 如果极值比在正常范围内，即非单极子
        if self.MIN_POLE_normal_RATIO <= point_ratio <= self.MAX_POLE_normal_RATIO:
            mono_index = 0.0
            type_description = "非单极子"
        else:
            # 如不是明显单极子，则进行详细计算
            is_mono_point, type_description, mono_index, _ = self.calculate_monopolarity_index(bz_inter_data, point_ratio)

        # 只有在非单极子状态下才进行多极子分析
        if mono_index < 0.5:  # 非单极子或单极化趋势
            multipole_analysis = self.analyze_multipole_shape(bz_inter_data)
            multipole_score = multipole_analysis["形态得分"]

            # 处理输出的多极子类型
            multipole_type = multipole_analysis["多极子类型"]
            if "正极子" in multipole_type:
                multipole_type = multipole_type.replace("正极子", "正")
            elif "负极子" in multipole_type:
                multipole_type = multipole_type.replace("负极子", "负")

            if "连通狭长形态改变" in multipole_type:
                multipole_type = multipole_type.replace("连通狭长形态改变", "连通狭长形")
            elif "连通哑铃状形态改变" in multipole_type:
                multipole_type = multipole_type.replace("连通哑铃状形态改变", "连通哑铃型")
            elif "分化状形态改变" in multipole_type:
                multipole_type = multipole_type.replace("分化状形态改变", "分化多极子")

            return {
                "单极性指数": mono_index,
                "多极子得分": multipole_score,
                "磁图类型": multipole_type if mono_index < 0.5 else type_description
            }
        else:
            # 如果是单极子或单极化趋势，多极子得分为0
            return {
                "单极性指数": mono_index,
                "多极子得分": 0.0,
                "磁图类型": type_description
            }

    def get_centroid(self, px, py, nx, ny):
        '''计算磁极中心点'''
        center_x = (px + nx) / 2
        center_y = (py + ny) / 2
        return center_x, center_y

    def calculate_TT_parma(self, bz_inter_data_all, time_point):
        """
        分析T波段的稳定性
        """
        bz_inter_data = bz_inter_data_all[:, :, time_point]
        px, py, nx, ny = get_dipole(bz_inter_data)
        dx = px - nx
        dy = py - ny
        angle = calangle(dx, dy)
        centroid = self.get_centroid(px, py, nx, ny)

        return angle, centroid

    def analyze_time_range(self, bz_inter_data_all, start_time_idx, end_time_idx, Tp):
        """
        分析从起始时间到结束时间范围内的所有时刻的单极性指数和多极子指数、Tp时刻磁图、TT段稳定性
        Args:
            bz_inter_data_all: 心磁图差值后数据100*100*N
            start_time_idx: 起始时间索引
            end_time_idx: 结束时间索引
        Returns:
            dict: 每个时刻的分析结果
        """
        time_points = range(start_time_idx, end_time_idx + 1)

        # 计算每个时刻的指数
        time_indices, centroids, angles = {}, [], []
        for time_idx in time_points:
            analysis_result = self.analyze_at_timepoint(bz_inter_data_all, time_idx)
            time_indices[f"T{time_idx}"] = analysis_result

            angle, centroid = self.calculate_TT_parma(bz_inter_data_all, time_idx)  # 计算TT段稳定性  计算磁极角度、质心位置
            angles.append(angle)
            centroids.append(centroid)

        # 计算T波峰（Tp）时刻磁图
        Tp_reslut = self.analyze_at_timepoint(bz_inter_data_all, Tp)
        Tp_type = Tp_reslut["磁图类型"]

        return time_indices, angles, centroids, Tp_type

    def filter_edge_artifacts(self, time_indices, max_edge_range=25):
        """
        自适应过滤数据首尾的不稳定帧，限制检测范围。
        参数:
            time_indices: 以时间点为键，单极化指数为值的字典
            max_edge_range: 检测边缘效应的最大时刻点数量
        返回:
            过滤后的字典，已移除不稳定帧
        """
        time_points = sorted(time_indices.keys(), key=lambda x: int(x[1:]))
        values = np.array([time_indices[t]["单极性指数"] for t in time_points])

        diffs = np.abs(np.diff(values))
        threshold = 3 * np.median(diffs)
        unstable_indices = np.where(diffs > threshold)[0]
        total_length = len(time_points)
        first_quarter = min(int(total_length / 5), max_edge_range)
        last_quarter_start = max(total_length - max_edge_range, int(4 * total_length / 5))

        # 找出开头部分的最后一个不稳定帧
        start_idx = 0
        for i in unstable_indices:
            if i < first_quarter:  # 只考虑前5分之1且不超过最大边缘范围
                start_idx = max(start_idx, i + 1)

        # 找出结尾部分的第一个不稳定帧
        end_idx = len(time_points) - 1
        for i in reversed(unstable_indices):
            if i >= last_quarter_start:  # 只考虑后5分之1且不超过最大边缘范围
                end_idx = min(end_idx, i)

        filtered_dict = {t: time_indices[t] for t in time_points[start_idx:end_idx + 1]}

        return filtered_dict

    def calculate_segment_monopole_index(self, segment_data, Tp):
        """
        计算一个时间段的整体单极化指数
        使用加权平均方法，距离Tp越近，该帧单极化指数的权重越高，越远，该帧单极化指数的权重越低
        Args:
            segment_data: 该时间段内的分析结果字典
            Tp: Tp波峰时间点索引
        Returns:
            float: 整体单极化指数
        """
        # 计算每个时间点的权重 (距离Tp越近，权重越大)
        weights = {}
        max_distance = 1
        for time_key in segment_data:
            t_idx = int(time_key[1:])
            distance = abs(t_idx - Tp)
            max_distance = max(max_distance, distance)
            weights[time_key] = distance

        # 距离越近，权重越大，并归一化
        total_weight = 0
        weighted_index = 0
        for time_key in weights:
            weights[time_key] = 1 + max_distance - weights[time_key]
            total_weight += weights[time_key]

        if total_weight > 0:
            for time_key in weights:
                weights[time_key] /= total_weight

        for time_key in segment_data:
            weighted_index += segment_data[time_key]["单极性指数"] * weights[time_key]

        return weighted_index

    def calculate_segment_multipole_index(self, segment_data, Tp):
        """
        计算一个时间段的整体多极子指数
        使用加权平均方法，距离Tp越近，该帧多极子指数的权重越高，越远，该帧多极子指数的权重越低
        Args:
            segment_data: 该时间段内的分析结果字典
            Tp: Tp波峰时间点索引
        Returns:
            float: 整体多极子指数
        """
        # 计算每个时间点的权重 (距离Tp越近，权重越大)
        weights = {}
        max_distance = 1
        for time_key in segment_data:
            t_idx = int(time_key[1:])
            distance = abs(t_idx - Tp)
            max_distance = max(max_distance, distance)
            weights[time_key] = distance

        # 距离越近，权重越大，并归一化
        total_weight = 0
        weighted_index = 0
        for time_key in weights:
            weights[time_key] = 1 + max_distance - weights[time_key]
            total_weight += weights[time_key]

        if total_weight > 0:
            for time_key in weights:
                weights[time_key] /= total_weight

        for time_key in segment_data:
            weighted_index += segment_data[time_key]["多极子得分"] * weights[time_key]

        return weighted_index

    def check_multipole_coexistence(self, pre_tp, tp, post_tp):
        """
        检查三个时段中是否同时存在分化多极子与连通多极子或分化多极子与正常偶极子，
        这种共存表明磁场结构不稳定
        """
        segments = [pre_tp, tp, post_tp]

        # 如果分化多极子与连通多极子共存，或分化多极子与正常偶极子共存，则不稳定
        has_separated = any("分化" in segment for segment in segments)
        has_connected = any("连通" in segment for segment in segments)
        has_normal = any("正常" in segment for segment in segments)
        if has_separated and has_connected:
            return True
        if has_separated and has_normal:
            return True
        return False

    def check_consecutive_normal_dipoles(self, filtered_indices, min_consecutive=8):
        """
        检查TT段全程是否有连续8帧或以上的正常偶极子
        """
        if not filtered_indices:
            return False

        # 按时间顺序排序所有时间点
        time_points = sorted(filtered_indices.keys(), key=lambda x: int(x[1:]))

        # 检查连续的正常偶极子
        max_consecutive = 0
        current_consecutive = 0

        for tp in time_points:
            mag_type = filtered_indices[tp]["磁图类型"]
            if mag_type == "正常偶极子":
                current_consecutive += 1
            else:
                max_consecutive = max(max_consecutive, current_consecutive)
                current_consecutive = 0

        # 检查最后一个序列
        max_consecutive = max(max_consecutive, current_consecutive)

        # 如果连续出现次数达到要求，返回True
        return max_consecutive >= min_consecutive

    def calculate_distance(self, x1, y1, x2, y2):
        '''计算两点之间的欧氏距离'''
        return round(math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2))

    def analyze_tt_stability(self, pre_tp_magnetogram, tp_magnetogram, post_tp_magnetogram, angles, centroids, filtered_indices, angle_threshold, angle_range_threshold, position_threshold):
        """
        分析TT段的稳定性
        """
        stability_result = {
            "稳定性结果": "",
            "角度差异稳定": None,
            "角度范围稳定": None,
            "位置稳定": None,
            "最大角度差异": None,
            "角度范围": None,
            "最大位置差异": None
        }

        # 全程正常/全程狭长/正常狭长混合
        magnetogram_types = [pre_tp_magnetogram, tp_magnetogram, post_tp_magnetogram]
        pre_monopole_trend = "单极化趋势" in pre_tp_magnetogram and tp_magnetogram == "正常" and post_tp_magnetogram == "正常"   # 检查波峰前是否为单极化趋势，且波峰和波峰后为正常
        post_monopole_trend = pre_tp_magnetogram == "正常" and tp_magnetogram == "正常" and "单极化趋势" in post_tp_magnetogram  # 检查波峰后是否为单极化趋势，且波峰和波峰前为正常

        all_normal_dipole = all("正常" in segment for segment in magnetogram_types)
        all_narrow_dipole = all("狭长" in segment for segment in magnetogram_types)
        has_normal = "正常" in magnetogram_types
        has_narrow = any("狭长" in mag_type for mag_type in magnetogram_types)
        all_types_valid = all(mag_type == "正常" or "狭长" in mag_type for mag_type in magnetogram_types)
        only_normal_and_narrow = has_normal and has_narrow and all_types_valid
        if all_normal_dipole or all_narrow_dipole or only_normal_and_narrow or pre_monopole_trend or post_monopole_trend:              # 进一步判断夹角和位置是否在正常范围
            angle_diffs = []
            for i in range(1, len(angles)):
                diff = min(abs(angles[i] - angles[i - 1]), 360 - abs(angles[i] - angles[i - 1]))
                angle_diffs.append(diff)
            max_angle_diff = round(max(angle_diffs)) if angle_diffs else 0
            is_angle_diff_stable = max_angle_diff <= angle_threshold

            angle_range = 0
            if angles:
                complex_angles = [math.cos(math.radians(angle)) + 1j * math.sin(math.radians(angle)) for angle in angles]
                max_diff = 0
                for i in range(len(angles)):
                    for j in range(i + 1, len(angles)):
                        diff = math.degrees(abs(math.atan2(
                            complex_angles[i].real * complex_angles[j].imag - complex_angles[i].imag * complex_angles[
                                j].real,
                            complex_angles[i].real * complex_angles[j].real + complex_angles[i].imag * complex_angles[
                                j].imag)))
                        max_diff = max(max_diff, diff)
                angle_range = round(max_diff, 2)
            is_angle_range_stable = angle_range <= angle_range_threshold

            position_diffs = []
            for i in range(1, len(centroids)):
                dist = self.calculate_distance(centroids[i][0], centroids[i][1], centroids[i - 1][0], centroids[i - 1][1])
                position_diffs.append(dist)
            max_position_diff = round(max(position_diffs), 2) if position_diffs else 0
            is_position_stable = max_position_diff <= position_threshold

            is_stable = is_angle_diff_stable and is_angle_range_stable and is_position_stable
            stability_result["最大角度差异"] = max_angle_diff
            stability_result["角度范围"] = angle_range
            stability_result["最大位置差异"] = max_position_diff
            stability_result["稳定性结果"] = "稳定" if is_stable else "不稳定"
        else:
            magnetogram_types = [pre_tp_magnetogram, tp_magnetogram, post_tp_magnetogram]
            # 1. 检查是否全是单极子或单极化趋势
            any_monopoles = all("单极" in segment for segment in magnetogram_types)
            if any_monopoles:
                stability_result["稳定性结果"] = "不予判断"
                return stability_result

            # 2. 检查是否有从分化多极子到连通多极子/正常偶极子的转变
            has_transition = self.check_multipole_coexistence(pre_tp_magnetogram, tp_magnetogram, post_tp_magnetogram)
            if has_transition:
                stability_result["稳定性结果"] = "不稳定"
                return stability_result

            # 3. 检查TT段全程是否有连续5帧的正常偶极子
            has_consecutive_normal = self.check_consecutive_normal_dipoles(filtered_indices, min_consecutive=5)
            if has_consecutive_normal:
                stability_result["稳定性结果"] = "不稳定（正异常交替）"
                return stability_result

            # 4. 如果三段都是连通多极子（全是连通哑铃/哑铃狭长并存，全是狭长形的已在前面剔除），检查主极子角度变化
            all_connected_multipoles = all("连通" in segment for segment in magnetogram_types)
            if all_connected_multipoles:
                angle_diffs = []
                for i in range(1, len(angles)):
                    diff = min(abs(angles[i] - angles[i - 1]), 360 - abs(angles[i] - angles[i - 1]))
                    angle_diffs.append(diff)
                max_angle_diff = round(max(angle_diffs)) if angle_diffs else 0

                # 如果角度变化超过阈值，判定为不稳定
                if max_angle_diff > angle_threshold:
                    stability_result["稳定性结果"] = "不稳定"
                    stability_result["最大角度差异"] = max_angle_diff
                else:
                    stability_result["稳定性结果"] = "稳定"
                    stability_result["最大角度差异"] = max_angle_diff
            else:
                # 正异常交替（三段里同时存在"正常"和其他类型）
                has_normal = "正常" in magnetogram_types
                has_abnormal = any(mag != "正常" for mag in magnetogram_types)
                if has_normal and has_abnormal:
                    stability_result["稳定性结果"] = "不稳定"
                else:
                    stability_result["稳定性结果"] = "不予判断"

        return stability_result

    def segment_time_points(self, time_indices, Tp):
        """
        将时间点划分为三个段：Tp波峰前、Tp波峰、Tp波峰后
        """
        pre_tp = {}  # Tp波峰前
        tp = {}  # Tp波峰
        post_tp = {}  # Tp波峰后

        # Tp波峰点定义为Tp前后各3个时间点的范围
        tp_range = (Tp - 3, Tp + 3)

        for time_key, value in time_indices.items():
            t_idx = int(time_key[1:])  # 提取时间点索引
            if t_idx < tp_range[0]:
                pre_tp[time_key] = value
            elif tp_range[0] <= t_idx <= tp_range[1]:
                tp[time_key] = value
            else:
                post_tp[time_key] = value

        return {"pre_tp": pre_tp, "tp": tp, "post_tp": post_tp}

    def check_consecutive_types(self, segment_data, min_consecutive):
        """
        检查段内是否存在连续min_consecutive个或以上的各种单极化/多极化情况
        """
        if not segment_data:
            return []
        time_points = sorted(segment_data.keys(), key=lambda x: int(x[1:]))
        all_types = []
        for tp in time_points:
            mag_type = segment_data[tp]["磁图类型"]
            if mag_type != "正常偶极子" and mag_type != "非单极子":
                all_types.append(mag_type)
        if len(all_types) < min_consecutive:
            return []

        # 计算每种类型的连续出现情况
        unique_types = set(all_types)
        satisfied_types = []
        for type_name in unique_types:
            # 跳过"正常偶极子"和"非单极子"
            if type_name == "正常偶极子" or type_name == "非单极子":
                continue
            max_consecutive = 0
            current_consecutive = 0
            for tp in time_points:
                current_type = segment_data[tp]["磁图类型"]
                if current_type == type_name:
                    current_consecutive += 1
                else:
                    max_consecutive = max(max_consecutive, current_consecutive)
                    current_consecutive = 0

            # 检查最后一个序列
            max_consecutive = max(max_consecutive, current_consecutive)

            # 如果连续出现次数达到要求，添加到结果列表
            if max_consecutive >= min_consecutive:
                satisfied_types.append(type_name)

        return satisfied_types

    def analyze_segment_magnetogram(self, segment_data, min_consecutive):
        """
        分析一个时间段的磁图类型，收集所有符合条件的单/多极化情况
        """
        if not segment_data:
            return "无数据"

        satisfied_types = self.check_consecutive_types(segment_data, min_consecutive=min_consecutive)
        if not satisfied_types:
            return "正常"
        return "、".join(satisfied_types)

    def analyze_file(self, bz_inter_data_all, To, Te, Tp, angle_threshold, angle_range_threshold, position_threshold):
        """
        分析单个MCG文件的To-Te波段
        Args:
            bz_inter_data_all: 心磁图插值后数据  100*100*N
        Returns:
            dict: 分析结果
        """
        # 获取从To到Te的分析结果
        time_indices, angles, centroids, Tp_type = self.analyze_time_range(bz_inter_data_all, To, Te, Tp)
        filtered_indices = self.filter_edge_artifacts(time_indices, max_edge_range=25)
        segments = self.segment_time_points(filtered_indices, Tp)

        # 计算整个波段的单极化指数和多极子指数
        overall_monopole_index = self.calculate_segment_monopole_index(filtered_indices, Tp)
        overall_multipole_index = self.calculate_segment_multipole_index(filtered_indices, Tp)

        # 分析各段磁图类型
        pre_tp_magnetogram = self.analyze_segment_magnetogram(segments["pre_tp"], min_consecutive=4)
        tp_magnetogram = self.analyze_segment_magnetogram(segments["tp"], min_consecutive=1)
        post_tp_magnetogram = self.analyze_segment_magnetogram(segments["post_tp"], min_consecutive=3)

        # 判断TT段稳定性
        stability_info = self.analyze_tt_stability(pre_tp_magnetogram, tp_magnetogram, post_tp_magnetogram,
                                                   angles, centroids, filtered_indices, angle_threshold,
                                                   angle_range_threshold, position_threshold)

        result_dict = {
            "TT_Stability": stability_info["稳定性结果"],
            "TT_UnipolarIndex": overall_monopole_index,
            "TT_MultipolarIndex": overall_multipole_index,
            "T_PeakType": Tp_type
        }
        return result_dict


if __name__ == "__main__":
    with open('mcg\AHYK_2023_000037.pkl', 'rb') as f:
        result = pickle.load(f)
    bz_inter_data_all = np.stack(result, axis=2)  # 100*100*N 的插值后心磁数据
    start_time = time.time()
    To, Te, Tp = 553, 662, 613
    analyzer = MCGAnalyzer()
    result_dict = analyzer.analyze_file(bz_inter_data_all, To, Te, Tp, angle_threshold=30, angle_range_threshold=50, position_threshold=18)
    end_time = time.time()
    execution_time = end_time - start_time
    print(result_dict["TT_Stability"],
        result_dict['TT_UnipolarIndex'],
        result_dict['TT_MultipolarIndex'],
        result_dict['T_PeakType'])
    print(f"代码运行时间：{execution_time:.4f} 秒")
