# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_imr_analysis
date: 2025/1/8 上午10:11
desc:
"""
import warnings
from pathlib import Path
from typing import List, Tuple
from collections import Counter,defaultdict
from joblib import Parallel, delayed
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from boruta import BorutaPy
from sklearn.ensemble import RandomForestClassifier
from imblearn.over_sampling import SMOTE

plt.rcParams['font.family'] = 'sans-serif'
warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号问题


def stable_boruta_selection(X_hybrid, y_for_selection, rf_base, n_features, balance_before_selection=True,
                            n_iterations=10, sample_fraction=0.8):
    # 预构建特征索引映射，减少重复查找
    feature_names = X_hybrid.columns
    feature_idx_map = {name: idx for idx, name in enumerate(feature_names)}

    # 转换为NumPy数组，减少pandas操作开销
    X_np = X_hybrid.values
    y_np = y_for_selection.values

    # 初始化结果存储
    feature_counts = defaultdict(int)
    feature_importance_sum = defaultdict(float)

    # 定义单次Boruta迭代函数，用于并行化
    def run_boruta_iteration(i, X_np, y_np, feature_names, rf_base, balance_before_selection, sample_fraction):
        print(f"\t\tStable Boruta selection iteration {i}")
        # 随机采样
        indices = np.random.choice(
            len(y_np),
            size=int(len(y_np) * sample_fraction),
            replace=True
        )
        X_sample = X_np[indices]
        y_sample = y_np[indices]

        # 检查样本类别平衡
        sample_class_counts = Counter(y_sample)
        if len(sample_class_counts) > 1:
            sample_imbalance_ratio = max(sample_class_counts.values()) / min(sample_class_counts.values())
            if balance_before_selection and sample_imbalance_ratio > 2.0:
                try:
                    smote = SMOTE(random_state=i)
                    X_sample, y_sample = smote.fit_resample(X_sample, y_sample)
                except Exception as smote_e:
                    print(f"\t\tSMOTE failed in iteration {i}: {smote_e}")
                    return [], []

        # 单次Boruta
        boruta = BorutaPy(
            estimator=rf_base,
            n_estimators=100,  # 固定值代替'auto'，减少调优开销
            max_iter=200,  # 降低迭代次数，加速收敛
            verbose=0,  # 减少日志输出
            random_state=i,
            perc=100,
            alpha=0.01,
            early_stopping=True
        )

        try:
            boruta.fit(X_sample, y_sample)
            confirmed = [feature_names[j] for j in np.where(boruta.support_)[0]]
            importance_scores = [(feature_names[j], boruta.ranking_[j]) for j in range(len(feature_names))]
            return confirmed, importance_scores
        except Exception as e:
            print(f"\tBoruta iteration {i} failed: {str(e)}")
            return [], []

    # 并行执行Boruta迭代
    results = Parallel(n_jobs=-1, verbose=1)(
        delayed(run_boruta_iteration)(
            i, X_np, y_np, feature_names, rf_base, balance_before_selection, sample_fraction
        ) for i in range(n_iterations)
    )

    # 聚合结果
    for confirmed, importance_scores in results:
        for feature in confirmed:
            feature_counts[feature] += 1
        for feature, ranking in importance_scores:
            feature_importance_sum[feature] += ranking

    # 计算稳定性得分
    stability_scores = {}
    for feature in feature_names:
        selection_rate = feature_counts[feature] / n_iterations
        avg_importance = feature_importance_sum[feature] / max(feature_counts[feature], 1)
        stability_scores[feature] = selection_rate * (1 / avg_importance) if avg_importance > 0 else 0

    # 转换为pandas Series
    stability_series = pd.Series(stability_scores)

    # 选择最稳定的特征
    selected_stable = stability_series[stability_series >= stability_series.quantile(0.5)].index.tolist()

    # 补充或裁剪特征
    if len(selected_stable) < n_features:
        remaining = stability_series.sort_values(ascending=False).index.tolist()
        remaining = [f for f in remaining if f not in selected_stable]
        selected_stable.extend(remaining[:n_features - len(selected_stable)])
    elif len(selected_stable) > n_features:
        selected_stable = stability_series.nlargest(n_features).index.tolist()

    final_features = selected_stable

    # 计算稳定性指标
    stability_metric = np.mean([feature_counts[f] / n_iterations for f in final_features])

    print(f"\tStability metric: {stability_metric:.3f}")
    return final_features

class FeatureSelector:
    def __init__(self, excel_path: str, feature_dir: str, use_smote: bool = True, use_mixup: bool = True,
                 balance_config=None):
        """
        初始化特征选择器

        Parameters:
        -----------
        excel_path : str
            Excel文件路径
        feature_dir : str
            特征目录路径
        use_smote : bool
            是否使用SMOTE（向后兼容）
        use_mixup : bool
            是否使用Mixup（向后兼容）
        balance_config : BalancedSelectionConfig, optional
            平衡选择配置对象
        """
        self.use_smote = use_smote
        self.use_mixup = use_mixup
        self.excel_path = excel_path
        self.feature_dir = Path(feature_dir)
        self.data = None
        self.features_df = None  # 存储完整特征DataFrame
        self.feature_names = None  # 特征名列表
        self.results = {}
        self.X_selected_scaled = None
        self.selected_features = None
        self.y = None

        # 平衡选择配置
        if balance_config is None:
            try:
                from .balanced_selection_config import BalancedSelectionConfig
                self.balance_config = BalancedSelectionConfig()
            except ImportError:
                # 如果配置文件不存在，使用默认设置
                self.balance_config = None
        else:
            self.balance_config = balance_config

    def correlation_feature_selection(self,X, var_threshold=0.01, corr_threshold=0.8, method='spearman',
                                            perform_correlation=False):
        """
        修复并改进的特征选择函数，结合了方差分析和高相关性剔除。

        该方法首先移除低方差特征，然后在剩余特征中，根据相关性剔除冗余特征。
        在处理相关特征时，它会保留方差较大的特征，因为在无监督的场景下，
        通常假设方差越大，包含的信息越多。

        参数:
        X : np.ndarray or array-like, shape (n_samples, n_features)
            输入特征矩阵。
        var_threshold : float, default=0.01
            方差阈值。标准化后，方差低于此值的特征将被剔除。
        corr_threshold : float, default=0.8
            相关性阈值。特征对的相关性绝对值高于此值时，将被视为高度相关。
        method : str, default='spearman'
            相关系数计算方法，可选 'spearman' 或 'pearson'。
        perform_correlation : bool, default=True
            是否执行相关性剔除。如果为 False，则只进行方差筛选。

        返回:
        np.ndarray
            被选中的特征的索引数组，按升序排列。
        """
        from sklearn.preprocessing import StandardScaler
        from scipy.stats import rankdata
        if not isinstance(X, np.ndarray):
            X = np.array(X)

        if X.shape[1] == 0:
            return np.array([])

        # 步骤1: 标准化和方差分析
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        variances = np.var(X_scaled, axis=0)

        # 识别并筛选出高方差特征的索引
        high_var_mask = variances >= var_threshold
        features_to_analyze = np.where(high_var_mask)[0]

        # 如果没有特征通过方差筛选，或只剩下一个，则直接返回
        if len(features_to_analyze) < 2:
            return features_to_analyze

        # 如果用户选择不进行相关性剔除，则直接返回通过方差筛选的特征
        if not perform_correlation:
            return np.array(sorted(features_to_analyze))

        # 步骤2: 计算高方差特征的相关性矩阵
        # 仅对高方差特征的子集进行计算
        X_high_var = X_scaled[:, features_to_analyze]

        if method == 'spearman':
            # 优化：使用向量化操作计算排名，更高效
            X_ranks = rankdata(X_high_var, axis=0)
            # 修复：移除有精度风险的 .astype(np.float16)
            corr_matrix = np.corrcoef(X_ranks.T)
        else:  # 'pearson'
            # 修复：移除有精度风险的 .astype(np.float16)
            corr_matrix = np.corrcoef(X_high_var.T)

        # 将对角线设置为0，避免自相关影响
        np.fill_diagonal(corr_matrix, 0)

        # 步骤3: 基于方差和相关性进行特征选择
        removed_indices_corr = set()

        # 特征重要性（此处为方差）仅用于排序
        feature_importance = variances[features_to_analyze]
        # 按重要性（方差）降序获取特征在相关性矩阵中的索引
        feature_order_in_corr = np.argsort(-feature_importance)

        for i in feature_order_in_corr:
            if i in removed_indices_corr:
                continue

            # 找出与当前特征高度相关的其他特征
            highly_corr_indices = np.where(np.abs(corr_matrix[i]) > corr_threshold)[0]

            # 将这些高度相关的特征（除了当前特征i本身）加入待移除集合
            # 因为我们是按重要性顺序遍历的，所以当前特征i是其相关组中最重要的
            partners_to_remove = set(highly_corr_indices) - {i}
            removed_indices_corr.update(partners_to_remove)

        # 步骤4: 确定最终保留的特征
        # 优化：使用集合运算简化逻辑
        all_indices_corr = set(range(X_high_var.shape[1]))
        selected_indices_corr = all_indices_corr - removed_indices_corr

        # 将相关性矩阵中的索引映射回原始特征索引
        final_selected_indices = [features_to_analyze[i] for i in selected_indices_corr]

        return np.array(sorted(final_selected_indices))


    def remove_collinear_features(self, data, target_col, threshold=0.95, remove_cols=None, variance_threshold=0.0):
        """
        移除零方差特征和高度共线性的特征，带进度显示
        """
        if remove_cols is None:
            remove_cols = []

        # 复制数据，只保留数值列
        df = data.copy()
        cols_to_exclude = remove_cols + [target_col]

        # 只选择数值型列
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        features = [col for col in numeric_columns if col not in cols_to_exclude]

        if len(features) == 0:
            raise ValueError("No numeric features found in the dataset")

        print(f"Initial number of features: {len(features)}")
        print(f"Excluded non-numeric features: {len(data.columns) - len(cols_to_exclude) - len(features)}")

        # 第一步：移除零方差特征
        print("\nCalculating variances...")
        variances = {}
        for feat in features:
            variances[feat] = df[feat].var()

        variances = pd.Series(variances)

        # 确定阈值
        # constant_features = variances[variances <= variance_threshold].index.tolist()
        # 百分比阈值剔除
        # 计算方差阈值：使用指定百分位数
        variance_threshold = variance_threshold * 100
        if variance_threshold > 0:  # 只有当百分比阈值大于0时才进行剔除，避免误操作
            print(f"Calculating variance threshold based on {variance_threshold}th percentile...")
            variance_threshold_value = np.percentile(variances, variance_threshold)  # 计算指定百分位的数值作为阈值
            print(
                f"Variance percentile threshold: {variance_threshold}%,  Threshold value: {variance_threshold_value:.4f}")  # 打印百分比阈值和计算出的数值阈值

            # 移除方差小于等于百分位阈值的特征
            constant_features = variances[variances <= variance_threshold_value].index.tolist()
        else:
            print("Variance percentile threshold is set to 0, no variance-based feature removal will be performed.")
            constant_features = []  # 如果百分比阈值为0，则不剔除任何特征

        # 打印零方差特征信息
        if len(constant_features) > 0:
            print(f"\nFound {len(constant_features)} zero/low variance features")

        # 移除零方差特征
        features = [f for f in features if f not in constant_features]
        print(f"Features after removing zero/low variance: {len(features)}")

        # 第二步：使用QR分解进行特征选择
        print("\nCalculating feature correlations spearman method...")
        # 准备特征矩阵
        X = df[features].values

        # 应用QR分解选择特征
        selected_indices = self.correlation_feature_selection(X)
        selected_features = [features[i] for i in selected_indices]

        # 汇总信息
        print("\nFeature Selection Summary:")
        print(f"Initial features: {len(data.columns) - len(cols_to_exclude)}")
        print(f"Removed non-numeric features: {len(data.columns) - len(cols_to_exclude) - len(numeric_columns)}")
        print(f"Removed zero/low variance features: {len(constant_features)}")
        print(f"Final number of features: {len(selected_features)}")

        # 如果需要，打印被排除的非数值型特征
        non_numeric_features = [col for col in data.columns if
                                col not in numeric_columns and col not in cols_to_exclude]
        if non_numeric_features:
            print("\nExcluded non-numeric features:")
            for feat in non_numeric_features[:10]:  # 只打印前10个
                print(f"- {feat} (type: {data[feat].dtype})")
            if len(non_numeric_features) > 10:
                print(f"... and {len(non_numeric_features) - 10} more")

        return selected_features

    def _apply_smote_for_selection(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """
        为特征选择应用SMOTE平衡技术

        Parameters:
        -----------
        X : pd.DataFrame
            特征矩阵
        y : pd.Series
            目标变量

        Returns:
        --------
        tuple: (平衡后的特征矩阵, 平衡后的标签)
        """
        try:
            from imblearn.over_sampling import SMOTE, BorderlineSMOTE, ADASYN
            from collections import Counter

            # 检查最小类别样本数，确保有足够样本进行SMOTE
            class_counts = Counter(y)
            min_samples = min(class_counts.values())

            # 根据配置选择SMOTE方法
            if self.balance_config is not None:
                smote_method = self.balance_config.smote_method
                smote_params = self.balance_config.get_smote_params()
            else:
                smote_method = 'auto'
                smote_params = {'k_neighbors': 5, 'sampling_strategy': 'auto', 'random_state': 42}

            # 调整k_neighbors以适应样本数
            smote_params['k_neighbors'] = min(smote_params['k_neighbors'], min_samples - 1)

            # 选择SMOTE方法
            if smote_method == 'auto':
                if min_samples < 6:
                    sampler_class = BorderlineSMOTE
                    print(f"\t\tAuto-selected BorderlineSMOTE (min_samples: {min_samples})")
                else:
                    sampler_class = SMOTE
                    print(f"\t\tAuto-selected SMOTE (min_samples: {min_samples})")
            elif smote_method == 'borderline':
                sampler_class = BorderlineSMOTE
                print(f"\t\tUsing BorderlineSMOTE...")
            elif smote_method == 'adasyn':
                sampler_class = ADASYN
                print(f"\t\tUsing ADASYN...")
            else:
                sampler_class = SMOTE
                print(f"\t\tUsing standard SMOTE...")

            # 创建采样器
            if sampler_class == BorderlineSMOTE:
                sampler = sampler_class(
                    sampling_strategy=smote_params['sampling_strategy'],
                    k_neighbors=smote_params['k_neighbors'],
                    random_state=smote_params['random_state']
                )
            else:
                sampler = sampler_class(**smote_params)

            # 应用SMOTE
            X_resampled, y_resampled = sampler.fit_resample(X.values, y.values)

            # 转换回DataFrame和Series
            X_balanced = pd.DataFrame(
                X_resampled,
                columns=X.columns
            )
            y_balanced = pd.Series(y_resampled)

            return X_balanced, y_balanced

        except Exception as e:
            print(f"\t\tSMOTE failed: {str(e)}, using original data")
            return X, y

    def _evaluate_feature_selection_quality(self, X: pd.DataFrame, y: pd.Series,
                                           selected_features: List[str]) -> dict:
        """
        评估特征选择的质量

        Parameters:
        -----------
        X : pd.DataFrame
            原始特征矩阵
        y : pd.Series
            目标变量
        selected_features : List[str]
            选择的特征列表

        Returns:
        --------
        dict: 评估指标
        """
        from sklearn.model_selection import cross_val_score
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import classification_report

        try:
            # 使用选择的特征训练模型
            X_selected = X[selected_features]
            rf = RandomForestClassifier(
                n_estimators=100,
                class_weight='balanced',
                random_state=42
            )

            # 交叉验证评估
            cv_scores = cross_val_score(rf, X_selected, y, cv=5, scoring='f1_macro')

            return {
                'cv_mean_f1': cv_scores.mean(),
                'cv_std_f1': cv_scores.std(),
                'n_features': len(selected_features),
                'feature_ratio': len(selected_features) / X.shape[1]
            }
        except Exception as e:
            print(f"\t\tFeature quality evaluation failed: {str(e)}")
            return {
                'cv_mean_f1': 0.0,
                'cv_std_f1': 0.0,
                'n_features': len(selected_features),
                'feature_ratio': len(selected_features) / X.shape[1]
            }

    def hybrid_feature_selection(self, X, y, n_features_target, random_state=42):
        """
        使用卡方检验和Fisher分数的混合特征选择

        Parameters:
        -----------
        X : pd.DataFrame
            特征矩阵
        y : pd.Series
            目标变量
        n_features_target : int
            目标特征数量
        random_state : int
            随机种子

        Returns:
        --------
        pd.DataFrame
            选择后的特征矩阵
        list
            选择的特征名称列表
        """
        from sklearn.feature_selection import chi2, SelectKBest
        from sklearn.preprocessing import MinMaxScaler
        import numpy as np

        print("\tStarting hybrid feature selection...")
        n_features_to_select = min(n_features_target, X.shape[1])  # 保持与原ReliefF相同的比例
        print(f"\tTargeting {n_features_to_select} features...")

        try:
            # 1. 首先用卡方检验初筛（因为计算快）
            # 注意：卡方检验要求特征非负
            scaler = MinMaxScaler()
            X_scaled = pd.DataFrame(
                scaler.fit_transform(X),
                columns=X.columns,
                index=X.index
            )

            # 使用卡方检验选择特征
            chi2_selector = SelectKBest(chi2, k=min(n_features_to_select * 2, X.shape[1]))
            chi2_selector.fit(X_scaled, y)
            chi2_scores = pd.Series(chi2_selector.scores_, index=X.columns)
            chi2_selected = chi2_scores.nlargest(min(n_features_to_select * 2, X.shape[1])).index

            print(f"\tFeatures selected by Chi2: {len(chi2_selected)}")

            # 2. 在卡方选择的特征上计算Fisher分数
            # Fisher分数计算函数
            def fisher_score(x, y):
                classes = np.unique(y)
                total_mean = np.mean(x)
                total_var = np.var(x)
                if total_var == 0:
                    return 0

                num = 0
                den = 0

                for c in classes:
                    x_c = x[y == c]
                    n_c = len(x_c)
                    mean_c = np.mean(x_c) if n_c > 0 else 0
                    var_c = np.var(x_c) if n_c > 0 else 0

                    num += n_c * (mean_c - total_mean) ** 2
                    den += n_c * var_c

                if den == 0:
                    return 0
                return num / den

            # 计算Fisher分数
            fisher_scores = {}
            X_chi2_selected = X_scaled[chi2_selected]

            for feature in chi2_selected:
                fisher_scores[feature] = fisher_score(X_chi2_selected[feature].values, y.values)

            fisher_scores = pd.Series(fisher_scores)
            final_selected = fisher_scores.nlargest(n_features_to_select).index.tolist()

            print(f"\tFinal features selected: {len(final_selected)}")
            X_selected = X[final_selected]

            return X_selected, final_selected

        except Exception as e:
            print(f"\tHybrid feature selection failed: {str(e)}")
            print("\tFalling back to original feature set")
            return X, X.columns.tolist()

    def select_features(self, X: pd.DataFrame, y: pd.Series, n_features: int = 150,
                       balance_before_selection: bool = True) -> Tuple[np.ndarray, List[str]]:
        """
        使用三阶段特征选择方法：共线性删除、ReliefF和Stable Boruta
        流程：
        1. 去除高度共线性特征
        2. 使用ReliefF进行初步特征筛选，保留约20倍目标特征数
        3. 在多次重采样上运行Boruta,选择最终稳定特征

        Parameters:
        -----------
        X : pd.DataFrame
            特征矩阵
        y : pd.Series
            目标变量
        n_features : int
            目标特征数量
        balance_before_selection : bool
            是否在特征选择前进行类别平衡处理
        """
        from collections import defaultdict, Counter
        print(f"\tStarting feature selection with {X.shape[1]} features")

        # 检查类别分布
        class_counts = Counter(y)
        print(f"\tOriginal class distribution: {dict(class_counts)}")

        # 计算不平衡比例
        max_count = max(class_counts.values())
        min_count = min(class_counts.values())
        imbalance_ratio = max_count / min_count
        print(f"\tImbalance ratio: {imbalance_ratio:.2f}")

        # 如果数据严重不平衡且启用平衡处理，则先进行SMOTE
        X_for_selection = X.copy()
        y_for_selection = y.copy()

        if balance_before_selection and imbalance_ratio > 2.0:
            print(f"\tDetected imbalanced data (ratio: {imbalance_ratio:.2f}), applying SMOTE...")
            X_for_selection, y_for_selection = self._apply_smote_for_selection(X, y)
            print(f"\tAfter SMOTE: {X_for_selection.shape[0]} samples")
            print(f"\tBalanced class distribution: {dict(Counter(y_for_selection))}")

        # 1. 处理缺失值/无穷值
        # X = X.fillna(0) # 在前置位填充过了
        initial_features = [col for col in X_for_selection.columns if col not in ['mcg_file']]
        X_hybrid = X_for_selection[initial_features]

        # # 2. 去除共线性特征 -------------------------------------------------
        print("\tRemoving collinear features...")
        selected_features = self.remove_collinear_features(
            data=X_for_selection,
            target_col=None,
            threshold=0.9,
            remove_cols=['mcg_file'],
            variance_threshold=0.1
        )
        # # -----------------------------------------------------------------

        X_filtered = X_for_selection[selected_features]
        print(f"\tFeatures after collinearity removal: {X_filtered.shape[1]}")
        # 主程序中的调用部分
        print("\tStarting hybrid feature pre-selection...")
        n_target_features = min(int(n_features * 15), X_filtered.shape[1])  # 保持原有的特征数量目标

        try:
            X_selected, selected_features = self.hybrid_feature_selection(
                X_filtered,
                y_for_selection,
                n_target_features,
                random_state=42
            )
            print(f"\tFeatures selected: {len(selected_features)}")
            X_hybrid = X_selected  # 保持变量名一致性
            hybrid_selected = selected_features  # 保持变量名一致性

        except Exception as e:
            print(f"\tFeature selection failed: {str(e)}")
            print("\tFalling back to original feature set")
            X_hybrid = X_filtered
            hybrid_selected = X_filtered.columns.tolist()
        # -------------------------------------------------------------------
        # 4. Stable Boruta特征选择
        print("\tStarting Stable Boruta selection...")

        # 基础随机森林模型
        rf = RandomForestClassifier(
            n_estimators=100,
            max_depth=5,
            n_jobs=-1,
            class_weight='balanced',
            random_state=42
        )
        rf_base = RandomForestClassifier(
            n_estimators=500,  # 平衡稳定性和速度
            max_depth=15,  # 加深捕捉细致交互
            min_samples_split=3,  # 敏感捕捉弱特征
            min_samples_leaf=5,
            class_weight="balanced",  # 平衡数据需设为"balanced"
            n_jobs=-1,  # 并行加速
            random_state=42,
            oob_score=False  # 大数据可启用以辅助验证
        )
        try:
            final_features = stable_boruta_selection(X_hybrid,y_for_selection,rf_base,n_features, balance_before_selection)
            # n_iterations = 10  # 重采样次数
            # sample_fraction = 0.8  # 每次采样的数据比例
            # feature_counts = defaultdict(int)  # 记录特征被选中次数
            # feature_importance_sum = defaultdict(float)  # 累积重要性分数
            #
            # for i in range(n_iterations):
            #     # 随机采样
            #     print("\t\t Stable Boruta selection iteration", i)
            #     indices = np.random.choice(
            #         len(y_for_selection),
            #         size=int(len(y_for_selection) * sample_fraction),
            #         replace=True
            #     )
            #     X_sample = X_hybrid.iloc[indices]
            #     y_sample = y_for_selection.iloc[indices]
            #
            #     # 如果当前样本仍然不平衡，再次应用SMOTE
            #     sample_class_counts = Counter(y_sample)
            #     if len(sample_class_counts) > 1:  # 确保有多个类别
            #         sample_imbalance_ratio = max(sample_class_counts.values()) / min(sample_class_counts.values())
            #
            #         if balance_before_selection and sample_imbalance_ratio > 2.0:
            #             try:
            #                 X_sample, y_sample = self._apply_smote_for_selection(
            #                     pd.DataFrame(X_sample, columns=X_hybrid.columns),
            #                     pd.Series(y_sample)
            #                 )
            #             except Exception as smote_e:
            #                 print(f"\t\tSMOTE failed in iteration {i}: {smote_e}")
            #
            #     # 单次Boruta
            #     boruta = BorutaPy(
            #         estimator=rf_base,
            #         n_estimators='auto',
            #         max_iter=500,
            #         verbose=2,
            #         random_state=i,
            #         perc=100,#
            #         alpha=0.01,#
            #         early_stopping = True
            #     )
            #     try:
            #         boruta.fit(X_sample.values, y_sample.values)
            #         # 记录确认的特征
            #         confirmed = X_hybrid.columns[boruta.support_].tolist()
            #         for feature in confirmed:
            #             feature_counts[feature] += 1
            #             feature_importance_sum[feature] += boruta.ranking_[
            #                 list(X_hybrid.columns).index(feature)
            #             ]
            #     except Exception as e:
            #         print(f"\tBoruta iteration {i} failed: {str(e)}")
            #         continue
            #
            # # 计算特征的稳定性得分
            # stability_scores = {}
            # for feature in X_hybrid.columns:
            #     selection_rate = feature_counts[feature] / n_iterations
            #     avg_importance = (
            #             feature_importance_sum[feature] /
            #             max(feature_counts[feature], 1)  # 避免除零
            #     )
            #     # 结合选择频率和平均重要性
            #     # 避免除以零的情况
            #     if avg_importance == 0:
            #         stability_scores[feature] = 0  # 特征重要性为0时，稳定性也应该为0
            #     else:
            #         stability_scores[feature] = selection_rate * (1 / avg_importance)
            #
            # # 转换为pandas Series便于排序
            # stability_series = pd.Series(stability_scores)
            #
            # # 选择最稳定的特征
            # selected_stable = stability_series[
            #     stability_series >= stability_series.quantile(0.5)  # 因为大部分都是1 可能导致quant也是1
            #     ].index.tolist()
            #
            # # 如果stable特征数量不够，从高分特征中补充
            # if len(selected_stable) < n_features:
            #     remaining = stability_series.sort_values(ascending=False).index.tolist()
            #     remaining = [f for f in remaining if f not in selected_stable]
            #     selected_stable.extend(remaining[:n_features - len(selected_stable)])
            #
            # # 如果特征过多，保留分数最高的
            # if len(selected_stable) > n_features:
            #     selected_stable = stability_series.nlargest(n_features).index.tolist()
            #
            # final_features = selected_stable
            #
            # # 计算稳定性指标
            # stability_metric = np.mean([
            #     feature_counts[f] / n_iterations
            #     for f in final_features
            # ])
            #
            # print(f"\tStability metric: {stability_metric:.3f}")
        except Exception as e:
            print(f"\tStable Boruta selection failed: {str(e)}")
            print("\tFalling back to basic Random Forest importance")
            # 回退到简单的随机森林特征重要性
            rf.fit(X_hybrid.values, y.values)
            importances = pd.Series(
                rf.feature_importances_,
                index=X_hybrid.columns
            )
            final_features = importances.nlargest(n_features).index.tolist()

        print(f"\tFinal selected features: {len(final_features)}")

        # 评估特征选择质量
        quality_metrics = self._evaluate_feature_selection_quality(X, y, final_features)
        print(f"\tFeature selection quality - F1: {quality_metrics['cv_mean_f1']:.3f} ± {quality_metrics['cv_std_f1']:.3f}")

        # 添加特征选择过程的详细信息
        selection_info = {
            'initial_features': len(X.columns),
            'final_features': len(final_features),
            # 'stability_metric': stability_metric if 'stability_metric' in locals() else None,
            'quality_metrics': quality_metrics,
            'imbalance_handled': balance_before_selection,
            # 'feature_selection_rates': {
            #     f: feature_counts[f] / n_iterations
            #     for f in final_features
            # } if 'feature_counts' in locals() else None
        }
        print("\tFeature selection summary:", {k: v for k, v in selection_info.items()
                                              if k not in ['feature_selection_rates']})

        return X[final_features].values, final_features


if __name__ == "__main__":
    ...
