
### 概述
运行文件：main.py

输入
心磁图数据: .txt文件
时刻点: Qp, Rp, Sp, To, Te, Tp,当其中有任意一个时刻点为None时，则会自行计算时刻点

输出：
生成一个包含80个电流源参数的Excel文件，包括： 第1列ID名，第2列-第81列是80个电流源参数

单个文件运行时间：6.38 秒


目录/
├── main.py                      # 主程序文件
├── function/                     # 功能模块文件夹
│   ├── new_time_locs.py         # 时刻点计算模块
│   └── source_recon.py          # 源重建模块
│   └── ...
├── model/                       # 心脏模型文件夹
│   ├── heart_point_lcav_mesh.msh
│   └── heart_point.msh
│   └── ...
├── data/                       # 输入数据文件夹
│   ├── data1.txt               # 心磁图数据文件
│   ├── data2.txt
│   └── ...
└── API.md                      # 说明文档   ₍˄·͈༝·͈˄*₎◞ 