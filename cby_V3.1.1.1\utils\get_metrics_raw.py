"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

file: 
date: 2024/01/25 16:46
desc: 指标生成汇总
"""


from utils.utils import *
from utils.get_diagnosises import *
from utils.cal_interpolates import cal_interpolate
from utils.gettimes import gettime
import os
import datetime
import shutil


# def metrics_mfm_pcdm725(ysb, rdmef, rdmef1, ctrds, ctrds1, root_path):
def metrics_mfm_pcdm725(mtsdir, rgt1, ids, mcgdir, ctrds, ctrds1):
    '''等磁图和电流密度图指标计算(202402定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    ctrd_qr1, ctrd_rs1, ctrd_tt1 = ctrds1
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    Zqr1 = extractmt(ctrd_qr1)
    Zrs1 = extractmt(ctrd_rs1)
    Ztt1 = extractmt(ctrd_tt1)
    # now = datetime.datetime.now()
    # tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # # tardir = '%s/jys_folder' % root_path
    # unzip_file(ysb, tardir)
    # mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    # mtsdir = '%s/metrics_mfm_pcdm' % tardir
    # new_folder(mtsdir)
    # shutil.copyfile(rdmef, '%s/readme_mfm.txt' % mtsdir)
    # shutil.copyfile(rdmef1, '%s/readme_pcdm.txt' % mtsdir)
    # for item in os.listdir(mcgdir):
    #     if item[-4:] == 'xlsx':
    #         biao1 = '%s/%s' % (mcgdir, item)
    # ids = get_idstms(biao1, 'Sheet1', mode='id')
    mflis, mslis, dflis, dslis = def_mts_intp_post(mtsdir)
    mflis1, mslis1 = def_mts_pcdm(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+rgt1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        # print(mfm_file)
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        # mfm
        mtsall = get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt)
        diagsall = get_rks_prop_intp_post(mtsall)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
        dslis = stat_mtstrs_intp_pre(diagsall, dslis, ids[i11])
        # pcdm
        dfs = pcdm_default()
        mtsall1 = get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1)
        mslis1 = stat_mtstrs_intp_pre(mtsall1, mslis1, ids[i11])
    write_strlis(mslis, mflis)
    write_strlis(dslis, dflis)
    write_strlis(mslis1, mflis1)
    # shutil.rmtree(mcgdir)
    return mtsdir


def metrics_mfm_pcdm(ysb, rdmef, rdmef1, ctrds, ctrds1, root_path):
    '''等磁图和电流密度图指标计算(202402定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    ctrd_qr1, ctrd_rs1, ctrd_tt1 = ctrds1
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    Zqr1 = extractmt(ctrd_qr1)
    Zrs1 = extractmt(ctrd_rs1)
    Ztt1 = extractmt(ctrd_tt1)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # tardir = '%s/jys_folder' % root_path
    unzip_file(ysb, tardir)
    mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    mtsdir = '%s/metrics_mfm_pcdm' % tardir
    new_folder(mtsdir)
    shutil.copyfile(rdmef, '%s/readme_mfm.txt' % mtsdir)
    shutil.copyfile(rdmef1, '%s/readme_pcdm.txt' % mtsdir)
    for item in os.listdir(mcgdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (mcgdir, item)
    ids = get_idstms(biao1, 'Sheet1', mode='id')
    mflis, mslis, dflis, dslis = def_mts_intp_post(mtsdir)
    mflis1, mslis1 = def_mts_pcdm(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        # print(mfm_file)
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        # mfm
        mtsall = get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt)
        diagsall = get_rks_prop_intp_post(mtsall)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
        dslis = stat_mtstrs_intp_pre(diagsall, dslis, ids[i11])
        # pcdm
        dfs = pcdm_default()
        mtsall1 = get_pcdm_mts(timelis0, matrixes, dfs, Zqr1, Zrs1, Ztt1)
        mslis1 = stat_mtstrs_intp_pre(mtsall1, mslis1, ids[i11])
    write_strlis(mslis, mflis)
    write_strlis(dslis, dflis)
    write_strlis(mslis1, mflis1)
    shutil.rmtree(mcgdir)
    return mtsdir


def metrics_pcdm(ysb, rdmef, ctrds, root_path):
    '''等磁图和电流密度图指标计算(202402定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # tardir = '%s/jys_folder' % root_path
    unzip_file(ysb, tardir)
    mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    mtsdir = '%s/metrics_pcdm' % tardir
    new_folder(mtsdir)
    shutil.copyfile(rdmef, '%s/readme_pcdm.txt' % mtsdir)
    for item in os.listdir(mcgdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (mcgdir, item)
    ids = get_idstms(biao1, 'Sheet1', mode='id')
    mflis, mslis = def_mts_pcdm(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        # print(mfm_file)
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        dfs = pcdm_default()
        mtsall = get_pcdm_mts(timelis0, matrixes, dfs, Zqr, Zrs, Ztt)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
    write_strlis(mslis, mflis)
    shutil.rmtree(mcgdir)
    return mtsdir


def def_mts_pcdm(tardir):
    '''电流密度图指标的文件列表和标题字符串列表'''
    subfolds = ['ischemic_pcdm_QR', 'ischemic_pcdm_RS', 'ischemic_pcdm_TT']
    new_folder(['%s/%s' % (tardir, subfolds[i2]) for i2 in range(len(subfolds))])
    mflis, mslis = [[], [], []], [[], [], []]
    txs2 = ['metrics_ctdistrib', 'metrics_posi', 'metrics_jumprot', 'metrics_red', 'metrics_angle']
    titles2 = ['id, qs, n0, n1, n2, n3, n4, center, mt2', 'id, mctd, mcar, mcre9, areac, aread', 'id, wg, nums, pm, nm, r, v, mse, jnq1, jnq2, jnq3, jnq4, jvq1, jvq2, jvq3, jvq4, af, ad, v1, mse1, saf, sad', 'id, dpma, dpav, dpmse, dvma, dvav, dvmse, mdama, mdaav, mdamse', 'id, qca, qia, rca, ria, iama, iami, iaav, iamse, nq1, nq2, nq3, nq4, qra, q, m1, m2, a1, a2']
    for i2 in range(len(subfolds)):
        for j2 in range(len(txs2)):
            mflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs2[j2]))
            mslis[i2].append(titles2[j2])
    return mflis, mslis


def get_pcdm_mts(timelis0, matrixes, dfs, Zqr, Zrs, Ztt):
    '''
    等磁图和电流密度图指标: QR, RS, TT
    timelis: 时刻点
    mf: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    frfile = cal_fr(matrixes, dfs, q_peak, r_peak)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Zqr, nsik=5)
    mtsall = [[cdfile, mpfile, mjfile, mrfile, mafile]]
    frfile = cal_fr(matrixes, dfs, r_peak, s_peak)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Zrs, nsik=5)
    mtsall.append([cdfile, mpfile, mjfile, mrfile, mafile])
    frfile = cal_fr(matrixes, dfs, t_onset, t_end)
    mafile, mjfile, mpfile, mrfile, cdfile = writemetric1(frfile, Ztt, t_idx=t_peak-t_onset, mode='tt', nsik=5)
    mtsall.append([cdfile, mpfile, mjfile, mrfile, mafile])
    return mtsall


def get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt):
    '''
    插值后指标: disp, QR, RS, TT
    timelis: 时刻点
    mf: 最大幅值
    Zqr: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    mlfile = writedisp(matrixes, timelis0, mcglines)[0]
    mlfile = mlfile[:-3] + [cal_noqrs(mlfile[-3:])]
    mtsall = [[mlfile]]
    frfile, rtfile = cal_frrt(matrixes, q_peak, r_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zqr, nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile, rtfile = cal_frrt(matrixes, r_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zrs, nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile, rtfile = cal_frrt(matrixes, t_onset, t_end)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt, t_idx=t_peak-t_onset, mode='tt', nsik=5)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    return mtsall


def metrics_intp_post(ysb, rdmef, ctrds, root_path):
    '''插值后指标计算(202310定版)'''
    ctrd_qr, ctrd_rs, ctrd_tt = ctrds
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Ztt = extractmt(ctrd_tt)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # tardir = '%s/jys_folder' % root_path
    unzip_file(ysb, tardir)
    mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    mtsdir = '%s/metrics_intp_post' % tardir
    new_folder(mtsdir)
    shutil.copyfile(rdmef, '%s/readme_intp_post.txt' % mtsdir)
    for item in os.listdir(mcgdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (mcgdir, item)
    ids = get_idstms(biao1, 'Sheet1', mode='id')
    mflis, mslis, dflis, dslis = def_mts_intp_post(mtsdir)
    # i11 = 0
    for i11 in range(len(ids)):
        print('start:', i11+1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        mcglines = get_lines(mfm_file)
        timelis = gettime(mfm_file)
        timelis0 = [timelis[j11] for j11 in [4, 5, 6, 9, 10, 11]]
        Qp, Te = timelis0[0], timelis0[5]
        matrixes = cal_interpolate(mfm_file, Qp, Te)
        mf = get_mfs(get_lines(mfm_file), Qp, Te)
        timelis0 = change_QS(timelis0, matrixes, mf)
        mtsall = get_mfm_mts_intp_post(timelis0, matrixes, mcglines, Zqr, Zrs, Ztt)
        diagsall = get_rks_prop_intp_post(mtsall)
        mslis = stat_mtstrs_intp_pre(mtsall, mslis, ids[i11])
        dslis = stat_mtstrs_intp_pre(diagsall, dslis, ids[i11])
    write_strlis(mslis, mflis)
    write_strlis(dslis, dflis)
    shutil.rmtree(mcgdir)
    return mtsdir


def def_mts_intp_post(tardir):
    '''插值后指标的文件列表和标题字符串列表'''
    subfolds = ['ischemic_mfm_disp', 'ischemic_mfm_QR', 'ischemic_mfm_RS', 'ischemic_mfm_TT']
    new_folder(['%s/%s' % (tardir, subfolds[i2]) for i2 in range(len(subfolds))])
    mflis, mslis = [[], [], [], []], [[], [], [], []]
    mflis[0].append('%s/%s/metrics.txt' % (tardir, subfolds[0]))
    mslis[0].append('id, Q R S T, QRS TT, noqrs')
    txs2 = ['metrics_ctdistrib', 'metrics_dipole', 'metrics_jumprot', 'metrics_multipole', 'metrics_pointto', 'metrics_poleform']
    titles2 = ['id, qs, n0, n1, n2, n3, n4, center, mt2', 'id, didma didav didmse, dicd dicar dicre9', 'id, wg nums, pm nm, r v mse, jnq1 jnq2 jnq3 jnq4, jvq1 jvq2 jvq3 jvq4, af ad, v1 mse1, saf sad', 'id, dppma dppav dppmse dpnma dpnav dpnmse, pnpma pnpflu pnnma pnnflu', '', 'id, epma epav epmse enma enav enmse']
    for i2 in range(1, len(subfolds)):
        for j2 in range(len(txs2)):
            mflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs2[j2]))
            if j2 != 4:
                mslis[i2].append(titles2[j2])
    mslis[1].insert(-1, 'id, qa ra, nq1 nq2 nq3 nq4, qra q m1 m2 a1 a2, areaf aread')
    mslis[2].insert(-1, 'id, ra sa, nq1 nq2 nq3 nq4, rsa q m1 m2 a1 a2, areaf aread')
    mslis[3].insert(-1, 'id, ta, tt50, tt75, nq1 nq2 nq3 nq4, tta q m1 m2 a1 a2, areaf aread')
    dflis, dslis = [[], [], [], []], [[], [], [], []]
    dflis[0].append('%s/%s/percents.txt' % (tardir, subfolds[0]))
    dflis[0].append('%s/%s/diagnosis_all.txt' % (tardir, subfolds[0]))
    dslis[0].extend(['id, R, T, QRS, TT, noqrs', 'id, percent'])
    txs1 = ['diagnosis_center', 'diagnosis_better', 'diagnosis_available', 'diagnosis_auxiliary', 'diagnosis_subdivision']
    txs2 = [['id, center', 'id, enmse', 'id, qa, ra, qra, qrdmse, qrdma, dpnav, dpnmse, dppav, pnnflu, dppmse, pnnma, enma, nm', 'id, areaf, aread, qrcar, qrcd, qrdav, dpnma, dppma, epmse, epma, epav, wg, pm, saf, af, mse', 'id, subdivision'], ['id, center', 'id, ', 'id, ra, sa, rsa, rsdma, rsdav', 'id, aread, rscar, rscd, dpnma, dppav, epmse, epav, pm, v1, mse', 'id, subdivision'], ['id, center', 'id, ta, tt75, tta, areaf, aread, ttcd, v, ad, af, v1, mse', 'id, tt50, ttdmse, ttdma, dppav, dppmse, dppma, enmse, enma, epmse, epma, wg, nums, pm, nm, saf, sad, r, mse1', 'id, ttcar, dpnav, dpnma, pnpflu, pnnflu, enav, epav', 'id, subdivision']]
    for i2 in range(1, len(subfolds)):
        for j2 in range(len(txs1)):
            dflis[i2].append('%s/%s/%s.txt' % (tardir, subfolds[i2], txs1[j2]))
            dslis[i2].append(txs2[i2-1][j2])
        dflis[i2].append('%s/%s/combinediagnosis.txt' % (tardir, subfolds[i2]))
        dslis[i2].append('id, ct, av, au, al')
    return mflis, mslis, dflis, dslis


def metrics_intp_pre(ysb, rdmef, ctrds, root_path):
    '''插值前指标计算(202308定版), 输出结果文档20240125原始代码'''
    ctrd_qr, ctrd_rs, ctrd_qrs, ctrd_tt = ctrds
    Zqr = extractmt(ctrd_qr)
    Zrs = extractmt(ctrd_rs)
    Zqrs = extractmt(ctrd_qrs)
    Ztt = extractmt(ctrd_tt)
    now = datetime.datetime.now()
    tardir = '%s/%d%02d%02d%02d%02d%02d' % (root_path, now.year, now.month, now.day, now.hour, now.minute, now.second)
    # tardir = '%s/jys_folder' % root_path
    unzip_file(ysb, tardir)
    mcgdir = '%s/%s' % (tardir, os.listdir(tardir)[0])
    mtsdir = '%s/metrics_intp_pre' % tardir
    new_folder(mtsdir)
    shutil.copyfile(rdmef, '%s/readme_intp_pre.txt' % mtsdir)
    for item in os.listdir(mcgdir):
        if item[-4:] == 'xlsx':
            biao1 = '%s/%s' % (mcgdir, item)
    ids, timelis = get_idstms(biao1, 'Sheet1')
    metricfiles, metricstrs = def_mts_intp_pre(mtsdir)
    for i11 in range(len(ids)):
        print('start:', i11+1, 'left:', len(ids)-1-i11)
        mfm_file = '%s/%s.txt' % (mcgdir, ids[i11])
        mcglines = get_lines(mfm_file)
        Qp, Te = timelis[i11][0], timelis[i11][5]
        matrixes, mf = cv_interpolate(mfm_file, Qp, min(Te, len(mcglines)-10))
        timelis[i11] = change_QS(timelis[i11], matrixes, mf)
        mtsall = get_mfm_mts_intp_pre(timelis[i11], matrixes, mcglines, Zqr, Zrs, Zqrs, Ztt)
        metricstrs = stat_mtstrs_intp_pre(mtsall, metricstrs, ids[i11])
    write_strlis(metricstrs, metricfiles)
    shutil.rmtree(mcgdir)
    return mtsdir


def def_mts_intp_pre(tardir):
    '''插值前指标的文件列表和标题字符串列表'''
    mflis, mslis = [[], [], [], [], []], [[], [], [], [], []]
    mflis[0].append('%s/dispersion_metrics.txt' % tardir)
    mflis[0].append('%s/dispersion_ranks.txt' % tardir)
    mslis[0].extend(['id, Q R S T, QRS TT, noq nor nos', 'id, Q R S T, QRS TT'])
    txs1 = ['QR_', 'RS_', 'QRS_', 'TT_']
    txs2 = ['centerdistrib', 'dipole', 'jumprot', 'mulripole', 'pointto', 'poleform']
    titles2 = ['id, qs, n0, n1, n2, n3, na', 'id, didma didav didmse, dicd dicar dicre9', 'id, wg nums, pm nm, r v mse, jnq1 jnq2 jnq3 jnq4, jvq1 jvq2 jvq3 jvq4, af ad, v1 mse1, saf sad', 'id, dppma dppav dppmse dpnma dpnav dpnmse, pnpma pnpflu pnnma pnnflu', '', 'id, epma epav epmse enma enav enmse']
    for i2 in range(len(txs1)):
        for j2 in range(len(txs2)):
            mflis[i2+1].append('%s/%s%s.txt' % (tardir, txs1[i2], txs2[j2]))
            if j2 != 4:
                mslis[i2+1].append(titles2[j2])
    mslis[1].insert(-1, 'id, qa ra, nq1 nq2 nq3 nq4, qra q m1 m2 a1 a2, areaf aread')
    mslis[2].insert(-1, 'id, ra sa, nq1 nq2 nq3 nq4, rsa q m1 m2 a1 a2, areaf aread')
    mslis[3].insert(-1, 'id, qa sa, nq1 nq2 nq3 nq4, qsa q m1 m2 a1 a2, areaf aread')
    mslis[4].insert(-1, 'id, ta, tt50, tt75, nq1 nq2 nq3 nq4, tta q m1 m2 a1 a2, areaf aread')
    return mflis, mslis


def get_mfm_mts_intp_pre(timelis0, matrixes, mcglines, Zqr, Zrs, Zqrs, Ztt):
    '''
    插值前指标: disp, QR, RS, QRS, TT
    timelis: 时刻点
    mflis: 最大幅值
    ctrdfile: 中心轨迹等级分布图谱
    输出指标列表:
        --disp离散度
        mlfile, rksfile
        指标， 等级
        --QR/RS/QRS/TT正负指向图
        mjfiles, mpfiles, mdfiles, mefiles, mffiles, cdfile
        跳转, 正负指向, 偶极子, 多极子, 极子形态, 中心轨迹等级分布
    '''
    q_peak, r_peak, s_peak, t_onset, t_peak, t_end = timelis0[0], timelis0[1], timelis0[2], timelis0[3], timelis0[4], timelis0[5]
    mlfile, rksfile = writedisp(matrixes, timelis0, mcglines)
    mtsall = [[mlfile, rksfile]]
    frfile, rtfile = cal_frrt(matrixes, q_peak, r_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zqr)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile, rtfile = cal_frrt(matrixes, r_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zrs)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile, rtfile = cal_frrt(matrixes, q_peak, s_peak)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Zqrs)
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    frfile, rtfile = cal_frrt(matrixes, t_onset, t_end)
    mpfile, mjfile, mdfile, mefile, mffile, cdfile = writemetric(frfile, rtfile, Ztt, t_idx=t_peak-t_onset, mode='tt')
    mtsall.append([cdfile, mdfile, mjfile, mefile, mpfile, mffile])
    return mtsall


def stat_mtstrs_intp_pre(mtsall, mtstrs, item):
    '''统计指标-字符串'''
    for i2 in range(len(mtsall)):  # 4
        for j2 in range(len(mtsall[i2])):  # 6
            mtstrs[i2][j2] += '\n%s, %s' % (item, str(mtsall[i2][j2])[1:-1])
    return mtstrs

