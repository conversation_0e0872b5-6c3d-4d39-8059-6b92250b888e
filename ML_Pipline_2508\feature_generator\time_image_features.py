"""
@Project ：ML_Pipline
@File    ：time_image_features.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2024/8/19 上午10:03
@Discribe：
    对原 complex features 即4W多特征的特征提取类替代。
    这类特征属于对磁图特征的时域展延。
"""
import os
import time
from multiprocessing import Pool

import numpy as np
import pandas as pd

from feature_generator.utils.frame_feature_extraction import (split_result, replace_key, temporal_interp_data,
                                                              extract_features_from_pic,
                                                              extract_features)
import logging

from scipy.ndimage import uniform_filter

from feature_generator.utils.calculate_params import *
from feature_generator.utils.writemp import statpn

class TimeImageFeatures_old:
    """图特征在时域上的特征提取类"""

    def __init__(self, basic_args_dict, label, w_list, num_chunks=15):
        self.basic_args_dict = basic_args_dict
        self.label = label
        self.w_list = w_list
        self.num_chunks = num_chunks
        self.l_tt, self.l_qr, self.l_rs, self.l_st,self.l_pt = 130, 20, 20, 150,320
        # self.tasks_old = [
        #     ('t-onset', 't-end', self.l_tt, 'tt_dim_3'),
        #     ('q-peak', 'r-peak', self.l_qr, 'qr_dim_3'),
        #     ('r-peak', 's-peak', self.l_rs, 'rs_dim_3'),
        #     ('s-peak', 't-end', self.l_st, 'st_dim_3'),
        # ]
        self.tasks = [
            ('p-r', 's-peak', self.l_qr, 'qr_dim_3'),
            ('r-peak', 's-r', self.l_rs, 'rs_dim_3'),
            ('s-r', 't-onset', self.l_st, 'st_dim_3'),
            ('t-head', 't-r', self.l_tt, 'tt_dim_3'),  # t_head t-r
            ('p-head', 't-r', self.l_pt, 'pt_dim_3')# 追加了P波头到T波尾全段
        ]

    def process_band_features(self, iso_tasks_frame_features, tasks):
        start = 0
        tasks_frame_features = {}
        for _, _, duration, dim_label in tasks:
            end = start + duration
            band = iso_tasks_frame_features[start:end]
            band_key = dim_label.split('_')[0]
            tasks_frame_features[dim_label] = replace_key(band, 'replace__me__', band_key)
            start = end
        return tasks_frame_features

    def resample_wave_tasks(self, tasks, signal_channels, result_peak):
        """定义提取波段特征的任务 """
        # 获取波段时长上的同构：桶插值或下采样，提取磁图特征 ----------------
        arr_list = [
            temporal_interp_data(
                signal_channels=signal_channels,
                start_times=result_peak[start_label],
                end_times=result_peak[end_label],
                threshold=duration
            )
            for (start_label, end_label, duration, dim_label) in tasks
        ]
        return arr_list

    @staticmethod
    def unified_normalization(arrlist):
        """
        波段内归一化方案：保持正负值的物理相对关系，每个波段独立归一化
        输入：包含x个波段的list，每个元素为ndarray (36, timelength)
        输出：归一化后的同结构list，值域约[-1, 1]，每个波段独立归一化
        """
        normalized_list = []
        for arr in arrlist:
            # 计算当前波段的最大绝对值
            max_abs = np.max(np.abs(arr))

            # 防止除零（当当前波段所有数据均为0时）
            if max_abs == 0:
                normalized_list.append(arr)  # 如果最大绝对值为0，则保持原始波段数据
            else:
                normalized_arr = arr / max_abs  # 对当前波段进行归一化
                normalized_list.append(normalized_arr)

        return normalized_list
    def get_complex_features(self, basic_args_dict=None, label=None, w_list=None,
                             num_chunks=15) -> pd.DataFrame:
        # w_list = w_list or [0.9, 0.8, 0.7, 0.6, 0.5, 0.4]
        w_list = w_list or [0.5]
        basic_args_dict = basic_args_dict or self.basic_args_dict

        # 获取时刻点以及基础数据 ----------------------------
        data_frame = basic_args_dict['data_frame']
        signal_channels = np.array(data_frame.iloc[:, 1:]).T  # 从basic_param_dict导入的dataframe需要经过的操作
        result_peak = basic_args_dict['result_peak']

        # 同构磁图+归一化 ----------------------------------------
        t1 = time.time()
        arr_list = self.resample_wave_tasks(self.tasks, signal_channels, result_peak)
        unified_normalization_list = self.unified_normalization(arr_list)
        print("  1.同构磁图：", time.time() - t1)

        # 提取磁图特征 处理一共320帧数据，每帧需要插值 --------------
        t1 = time.time()
        iso_tasks_data_chunks = split_result(np.concatenate(unified_normalization_list, axis=1), num_chunks)
        iso_tasks_frame_features = []  # 每个task的数据，时间拉长或压缩成固定长度，然后提取每个时刻点的6*6 数据的插值后单帧特征
        with Pool(processes=num_chunks) as pool:
            test_result = pool.starmap(extract_features_from_pic,
                                       [(chunk, "replace__me__", w_list) for chunk in iso_tasks_data_chunks])
            for f in test_result:
                iso_tasks_frame_features.extend(f)
        print("  2.提取同构后磁图特征：", time.time() - t1)

        # 拆分波段特征(命名) ------------------------------------
        tasks_frame_features = self.process_band_features(iso_tasks_frame_features, self.tasks)

        # 定义特征-数据-标签字典 -------------------------
        t1 = time.time()
        features_data = {
            'mcg_file': os.path.basename(basic_args_dict['file_path']).replace('.txt', ''),
            'Data': {
                **tasks_frame_features
            },
            'label': label
        }
        # 从时间同构-插值精细化数据特征+时序特征 中 再获取特征 ----------------------------------------
        all_features = self.get_all_features(pre_features=features_data)
        print("  3.图特征在时间轴上变化特征：", time.time() - t1)
        return all_features


    def get_all_features(self, pre_features):
        pre_features = [pre_features]
        features_3d = self._get_3d_features(pre_features, self.w_list)
        features_3d.insert(0, "label", pre_features[0]['label'])
        features_3d.insert(0, 'mcg_file', pre_features[0]['mcg_file'])

        return features_3d

    def _get_3d_features(self, pre_features, w_list_params=None):
        """获取不同阈值下3d参数："""
        arr = []
        w_list = w_list_params or self.w_list
        with Pool(processes=len(w_list)) as pool:
            results = pool.starmap(self._process_single_param, [(w_list_i, pre_features) for w_list_i in w_list])
            for result in results:
                arr.append(result)

        return pd.concat(arr, axis=1)

    def _process_single_param(self, w_list_i, pre_features):
        param_types = ['tt_dim_3', 'qr_dim_3', 'rs_dim_3', 'st_dim_3','pt_dim_3']
        features = []
        for param_type in param_types:
            data, _, _, keys = self.get_params(pre_features, key=param_type, w_list=[w_list_i])
            df = pd.DataFrame(data[0], columns=keys)
            df_ext = extract_features(df, w_list_i)
            features.append(df_ext)
        return pd.concat(features, axis=1)

    def get_params(self, pkl_data,  key=None, w_list=None):
        """负责 3D 和 2D 参数 字典对齐"""

        if w_list is None:
            w_list = [0.9]

        title = '+'.join(map(str, w_list))

        Train_data = []
        Train_label = []
        all_keys = []

        # Extracting parameters from each dictionary in pkl_data
        for dict_arr in pkl_data:
            mcg_label = dict_arr['label']
            # mcg_filename = dict_arr['mcg_file']
            arr_one = []
            all_keys = []  # 重置 all_keys 为每个 dict_arr

            # Extracting parameters for the given key
            for dict_arr2 in dict_arr['Data'][key]:
                name_part = key.replace('dim_3', '')
                all_keys = []  # 重置 all_keys 为每个 dict_arr2
                temp, keys = self.extract_params(dict_arr2, name_part, w_list)
                arr_one.append(np.array(temp))
                all_keys.extend(keys)

            Train_data.append(np.array(arr_one))
            Train_label.append(mcg_label)

        return np.array(Train_data), np.array(Train_label), title, all_keys



    def extract_params(self, dict_arr2, name_part, w_list):
        arr_temp = []
        data_pos = dict_arr2['pos']
        data_neg = dict_arr2['neg']
        keys = []
        for w in w_list:
            keys.extend([
                f'{name_part}max_magni_{w}',
                f'{name_part}max_angle_{w}',
                f'{name_part}max_x_position_{w}',
                f'{name_part}max_y_position_{w}',
                f'{name_part}max_mcg_value',
                f'{name_part}mean_mcg_value',
                f'{name_part}min_mcg_value',
                f'{name_part}points_large',
                f'{name_part}points_small',
                f'{name_part}pos_0_x',
                f'{name_part}pos_1_x',
                f'{name_part}pos_0_y',
                f'{name_part}pos_1_y',
                f'{name_part}neg_0_x',
                f'{name_part}neg_1_x',
                f'{name_part}neg_0_y',
                f'{name_part}neg_1_y'
            ])
            arr_temp.extend([
                dict_arr2['params'][f'{name_part}max_magni_{w}'],
                dict_arr2['params'][f'{name_part}max_angle_{w}'],
                dict_arr2['params'][f'{name_part}max_x_position_{w}'],
                dict_arr2['params'][f'{name_part}max_y_position_{w}'],
                dict_arr2['params'][f'{name_part}max_mcg_value'],
                dict_arr2['params'][f'{name_part}mean_mcg_value'],
                dict_arr2['params'][f'{name_part}min_mcg_value'],
                dict_arr2['params'][f'{name_part}points_large'],
                dict_arr2['params'][f'{name_part}points_small'],
                dict_arr2['params'][f'{name_part}pos_0_x'],
                dict_arr2['params'][f'{name_part}pos_1_x'],
                dict_arr2['params'][f'{name_part}pos_0_y'],
                dict_arr2['params'][f'{name_part}pos_1_y'],
                dict_arr2['params'][f'{name_part}neg_0_x'],
                dict_arr2['params'][f'{name_part}neg_1_x'],
                dict_arr2['params'][f'{name_part}neg_0_y'],
                dict_arr2['params'][f'{name_part}neg_1_y']
            ])
            # Adding threshold values
            for i in range(2):
                pos_value = data_pos[f'{name_part}pos_{w}_{i}']
                neg_value = data_neg[f'{name_part}neg_{w}_{i}']
                arr_temp.extend([v for key, v in pos_value.items()])
                arr_temp.extend([v for key, v in neg_value.items()])

                keys.extend([f'pos_{i}_' + key for key in list(pos_value.keys())])
                keys.extend([f'neg_{i}_' + key for key in list(neg_value.keys())])
        return arr_temp, keys





class TimeImageFeatures:
    """图特征在时域上的特征提取类"""

    def __init__(self, basic_args_dict, label, w_list, num_chunks=15):
        self.basic_args_dict = basic_args_dict
        self.label = label
        self.w_list = w_list
        self.num_chunks = num_chunks
        self.w_list_frame_extraction = w_list # 用于read_file_plot3的分位数
        self.num_chunks_frame_proc = num_chunks # 用于并行化read_file_plot3调用
        self.l_tt, self.l_qr, self.l_rs, self.l_st,self.l_pt = 130, 20, 20, 150,320
        self.tasks = [
            ('t-onset', 't-end', self.l_tt, 'tt_dim_3'),
            ('q-peak', 'r-peak', self.l_qr, 'qr_dim_3'),
            ('r-peak', 's-peak', self.l_rs, 'rs_dim_3'),
            ('s-peak', 't-end', self.l_st, 'st_dim_3'),
        ]
        # self.tasks = [
        #     ('p-r', 's-peak', self.l_qr, 'qr_dim_3'),
        #     ('r-peak', 's-r', self.l_rs, 'rs_dim_3'),
        #     ('s-r', 't-onset', self.l_st, 'st_dim_3'),
        #     ('t-head', 't-r', self.l_tt, 'tt_dim_3'),  # t_head t-r
        #     # ('p-head', 't-r', self.l_pt, 'pt_dim_3')# 追加了P波头到T波尾全段
        # ]

    def extract_features_from_pic(self,arr_chunk_of_frames,  # 从'arr'重命名
                                          # part, # 移除'part'，这里不能正确识别
                                          w_list=[0.7, 0.5, 0.3]):
        """
        处理一组6x6帧的数据块。
        每帧被调整大小，read_file_plot3提取特征。
        read_file_plot3现在返回带有通用"FRAME_..."名称的扁平字典。
        """
        n_cols = arr_chunk_of_frames.shape[1]  # 此数据块中的帧数
        frame_features_list = [None] * n_cols
        for i in range(n_cols):
            tmp_data_6x6 = arr_chunk_of_frames[:, i].reshape(6, 6)
            tmp_data_resized_100x100 = cv2.resize(tmp_data_6x6, (100, 100), interpolation=cv2.INTER_CUBIC)

            single_frame_features_dict = self._process_single_mfm_frame(  # 调用重构版本
                mfm_fine_norm=tmp_data_resized_100x100,
                data_original_6x6=tmp_data_6x6,
                target_quantiles_for_calc=w_list
            )
            frame_features_list[i] = single_frame_features_dict
        return frame_features_list  # 字典列表，数据块中每帧一个字典

    @staticmethod
    def _format_mfm_frame_feature_name(base_metric: str,
                                       quantile_tag: float = None,
                                       pole_type: str = None,  # "POS" 或 "NEG"
                                       pole_id: int = None  # 0表示主要，1表示次要
                                       ) -> str:
        name_parts = ["FRAME"]  # 以帧特征的通用前缀开始
        name_parts.append(base_metric.upper())
        if quantile_tag is not None:
            name_parts.append(f"Q{str(quantile_tag).replace('.', 'P')}")  # 0.7对应Q0P7
        if pole_type:
            name_parts.append(pole_type.upper())
        if pole_id is not None:
            name_parts.append(f"POLE{pole_id}")
        return "_".join(name_parts)

    @staticmethod
    def _get_default_zero_features_for_pole_static(): # 设为静态方法以便需要时更容易访问
        pole_feature_bases = [
            "MCGMAXLENGTH", "MCGMINLENGTH", "MCGS", "MCGPERIMETER", "ANGLE", "VALUE",
            "INNERCIRCLEX", "INNERCIRCLEY", "INNERCIRCLES", "MCGSUM", "DISTSUM", "K",
            "CIRCLERANGE", "LX", "LY", "RX", "RY", "TX", "TY", "DX", "DY",
            "CIRCLERADIUS", "CIRCLEAREA", "CIRCLECENTERX", "CIRCLECENTERY",
            "RECTAREA", "RECTCENTERX", "RECTCENTERY"
        ]
        return {base: 0.0 for base in pole_feature_bases} # 返回浮点零值


    @staticmethod
    def _process_single_mfm_frame( # 重新命名，因为它现在是主要的帧处理器
        mfm_fine_norm: np.ndarray,
        data_original_6x6: np.ndarray,
        # 此列表仅包含应计算极点/电流特征的分位数
        target_quantiles_for_calc: list # 例如，[0.5] 或 [0.7, 0.5] 如果以后需要
    ):
        """
        处理单个MFM帧。
        计算一次全局特征。
        仅为`target_quantiles_for_calc`中指定的分位数计算分位数相关特征（电流、极点）。
        """
        flat_feature_dict = {}
        # 如果我们确保计算总是产生值或NaN，则不严格需要default_pole_zeros
        # 但是，如果子计算失败，确保所有预期键都存在是很好的。
        # 对于这个最小版本，我们将直接计算和存储。

        # --- 基本帧统计和全局空间特征的子集 ---
        # 对应于修订的extract_params中的global_param_bases_condensed（8个特征）
        flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("MAXMCGVALRAW")] = np.max(data_original_6x6)
        flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("MINMCGVALRAW")] = np.min(data_original_6x6)
        flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("MEANMCGVALRAW")] = np.mean(
            data_original_6x6)

        center_y, center_x = mfm_fine_norm.shape[0] // 2, mfm_fine_norm.shape[1] // 2
        max_radius_for_polar = min(center_x, center_y)

        flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("POLARSTD")] = \
            np.std(cv2.warpPolar(mfm_fine_norm, (max_radius_for_polar, 360), (center_x, center_y), max_radius_for_polar,
                                 cv2.WARP_POLAR_LINEAR)) if max_radius_for_polar > 0 else 0.0


        gx = cv2.Sobel(mfm_fine_norm, cv2.CV_64F, 1, 0, ksize=3)
        gy = cv2.Sobel(mfm_fine_norm, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude_map = np.sqrt(gx ** 2 + gy ** 2)
        flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("GRADIENTMAGNITUDEMEAN")] = np.mean(
            gradient_magnitude_map)
        flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("GRADIENTSTD")] = np.std(
            gradient_magnitude_map)

        g_mfm_fine_grad_y_as_gxy = np.gradient(mfm_fine_norm, axis=1)
        g_gx_dx, _ = np.gradient(gx)
        _, g_gy_dy = np.gradient(gy)
        curvature_numerator = (g_gx_dx * gy ** 2 - 2 * gx * gy * g_mfm_fine_grad_y_as_gxy + g_gy_dy * gx ** 2)
        curvature_denominator = (gx ** 2 + gy ** 2 + 1e-9) ** 1.5
        valid_curvature_mask = curvature_denominator > 1e-12
        curvature_map = np.zeros_like(mfm_fine_norm)
        if np.any(valid_curvature_mask):
            curvature_map[valid_curvature_mask] = curvature_numerator[valid_curvature_mask] / curvature_denominator[
                valid_curvature_mask]
            flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("CURVATUREMEAN")] = np.nanmean(
                curvature_map[valid_curvature_mask])
        else:
            flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("CURVATUREMEAN")] = 0.0

        window_size = 5
        local_mean_map = uniform_filter(mfm_fine_norm, size=window_size, mode='reflect')
        local_sqr_mean_map = uniform_filter(mfm_fine_norm ** 2, size=window_size, mode='reflect')
        local_variance_map = np.maximum(0, local_sqr_mean_map - local_mean_map ** 2)
        flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("LOCALVARIANCEMEAN")] = np.mean(
            local_variance_map)
        # 注意：POINTSLARGERAW, POINTSSMALLRAW, VORTICITYMEAN, GRADIENTDIRECTIONENTROPY, LOCALVARIANCESTD
        # 在之前的"13个全局"中，但不在"extract_params选择的8个"中。
        # 如果extract_params不选择它们，_process_single_mfm_frame_minimal就不应该计算它们。
        if not target_quantiles_for_calc: # 如果列表为空或None
            return flat_feature_dict


        pos_data_norm = mfm_fine_norm[mfm_fine_norm > 1e-9]
        neg_data_abs_norm = np.abs(mfm_fine_norm[mfm_fine_norm < -1e-9])

        thresholds_cache = {}
        for current_quantile_val in target_quantiles_for_calc:
            w_p_calc = np.quantile(pos_data_norm, current_quantile_val) if len(pos_data_norm) > 0 else 0.0
            w_n_calc = np.quantile(neg_data_abs_norm, current_quantile_val) if len(neg_data_abs_norm) > 0 else 0.0
            thresholds_cache[current_quantile_val] = {'wp': w_p_calc, 'wn': w_n_calc}
            w_p = thresholds_cache[current_quantile_val]['wp']
            w_n = thresholds_cache[current_quantile_val]['wn']

            mfm_filtered = np.zeros_like(mfm_fine_norm)  # 为每个分位数初始化
            if w_p > 1e-9:
                mask_pos = (mfm_fine_norm > w_p)
                if np.any(mask_pos): mfm_filtered[mask_pos] = mfm_fine_norm[mask_pos]
            if w_n > 1e-9:
                mask_neg = (mfm_fine_norm < -w_n)  # 注意：这里用-w_n作为负阈值
                if np.any(mask_neg): mfm_filtered[mask_neg] = mfm_fine_norm[mask_neg]

            # 电流特征的子集（2个特征）
            if np.any(mfm_filtered != 0):  # 仅在mfm_filtered不全为零时计算
                max_magni, max_angle, _, _ = get_current(mfm_filtered)
            else:
                max_magni, max_angle = 0.0, 0.0
            flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("MAXCURRENTMAGNITUDE",
                                                                               quantile_tag=current_quantile_val)] = max_magni
            flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name("MAXCURRENTANGLE",
                                                                               quantile_tag=current_quantile_val)] = max_angle

            # 极点提取
            poles_from_pos = []
            if w_p > 1e-9 and np.any(mfm_filtered > 0):  # 检查mfm_filtered的正部分是否存在
                poles_from_pos = process_poles(statpn(mfm_filtered, vmin=w_p), mfm_filtered)[0]

            poles_from_neg = []
            if w_n > 1e-9 and np.any(mfm_filtered < 0):  # 检查mfm_filtered的负部分是否存在
                poles_from_neg = process_poles(statpn(mfm_filtered, vmin=w_n), mfm_filtered)[
                    1]  # statpn的vmin期望正阈值

            all_poles_data = [poles_from_pos, poles_from_neg]
            pole_geom_bases_to_calc = ["MCGS", "K", "ANGLE", "VALUE", "CIRCLERANGE"]

            for pole_polarity_idx, poles_of_one_polarity in enumerate(all_poles_data):
                current_pole_type_str = "POS" if pole_polarity_idx == 0 else "NEG"
                pole_instance_idx = 0  # 仅主要极点

                # 用默认值初始化
                for base_metric_name in pole_geom_bases_to_calc:
                    key = TimeImageFeatures._format_mfm_frame_feature_name(base_metric_name, current_quantile_val,
                                                                           current_pole_type_str, pole_instance_idx)
                    flat_feature_dict[key] = 0.0

                if pole_instance_idx < len(poles_of_one_polarity) and poles_of_one_polarity[pole_instance_idx]:
                    single_pole_info = poles_of_one_polarity[pole_instance_idx]
                    # ... (5个压缩特征的主要极点几何计算的其余部分)
                    # ... (如_process_single_mfm_frame_minimal中所示)
                    # 根据找到的极点更新极点中心位置
                    flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name(f"{current_pole_type_str}CENTERX", quantile_tag=current_quantile_val, pole_id=pole_instance_idx)] = single_pole_info[1]
                    flat_feature_dict[TimeImageFeatures._format_mfm_frame_feature_name(f"{current_pole_type_str}CENTERY", quantile_tag=current_quantile_val, pole_id=pole_instance_idx)] = single_pole_info[2]


                    pole_threshold_val, geom_pole_center_x, geom_pole_center_y = single_pole_info[0], int(
                        single_pole_info[1]) - 1, int(single_pole_info[2]) - 1
                    dfs_threshold = w_p if current_pole_type_str == "POS" else w_n
                    if dfs_threshold <= 1e-9: continue

                    region_coords_list = dfs_optimized(mfm_filtered, geom_pole_center_x, geom_pole_center_y,
                                                       dfs_threshold, w=current_quantile_val)
                    if not region_coords_list or len(region_coords_list) < 4: continue

                    region_mask_for_geom = np.zeros_like(mfm_filtered)
                    # ... (填充region_mask_for_geom，计算dist_sum - 尽管dist_sum不在压缩集中)
                    for r_coord, c_coord in region_coords_list:  # 构建掩码
                        r_int, c_int = int(r_coord), int(c_coord)
                        if 0 <= r_int < mfm_filtered.shape[0] and 0 <= c_int < mfm_filtered.shape[1]:
                            region_mask_for_geom[r_int, c_int] = mfm_filtered[r_int, c_int]

                    mcg_s_val = get_area(region_mask_for_geom)
                    if mcg_s_val < 4: continue

                    mcg_max_len, mcg_min_len, main_angle = get_longest_shortest_line(region_mask_for_geom,
                                                                                     geom_pole_center_x,
                                                                                     geom_pole_center_y)
                    perimeter_val, k_elongation, _, _, _, _, _, _, _, _, _ = get_perimeter(region_mask_for_geom)

                    temp_geom_features_minimal = {
                        "MCGS": mcg_s_val, "K": k_elongation, "ANGLE": main_angle,
                        "VALUE": pole_threshold_val,
                        "CIRCLERANGE": (4 * np.pi * mcg_s_val) / (
                                    perimeter_val ** 2 + 1e-9) if perimeter_val > 1e-9 else 0.0
                        # 检查perimeter_val
                    }
                    for base_metric_name, geom_val in temp_geom_features_minimal.items():
                        if base_metric_name in pole_geom_bases_to_calc:
                            key = TimeImageFeatures._format_mfm_frame_feature_name(base_metric_name,
                                                                                   current_quantile_val,
                                                                                   current_pole_type_str,
                                                                                   pole_instance_idx)
                            flat_feature_dict[key] = geom_val

        return flat_feature_dict
    def resample_wave_tasks(self, tasks, signal_channels, result_peak):
        """定义提取波段特征的任务"""
        # 获取波段时长上的同构：桶插值或下采样，提取磁图特征 ----------------
        arr_list = [
            temporal_interp_data(
                signal_channels=signal_channels,
                start_times=result_peak[start_label],
                end_times=result_peak[end_label],
                threshold=duration
            )
            for (start_label, end_label, duration, dim_label) in tasks
        ]
        return arr_list

    @staticmethod
    def unified_normalization(arrlist):
        """
        波段内归一化方案：保持正负值的物理相对关系，每个波段独立归一化
        输入：包含x个波段的list，每个元素为ndarray (36, timelength)
        输出：归一化后的同结构list，值域约[-1, 1]，每个波段独立归一化
        """
        normalized_list = []
        for arr in arrlist:
            # 计算当前波段的最大绝对值
            max_abs = np.max(np.abs(arr))

            # 防止除零（当当前波段所有数据均为0时）
            if max_abs == 0:
                normalized_list.append(arr)  # 如果最大绝对值为0，则保持原始波段数据
            else:
                normalized_arr = arr / max_abs  # 对当前波段进行归一化
                normalized_list.append(normalized_arr)

        return normalized_list



    def extract_params(self, frame_feature_dict_from_read_file_plot3: dict,
                       w_list_for_selection: list  # 为哪些分位数选择特征
                       ) -> (list, list):
        """
        修订版：选择更加压缩的预定义帧特征子集。
        """
        selected_values = []
        selected_keys_bases = []

        # --- 策略A.1：减少global_param_bases ---
        # 保留：原始MCG的均值/标准差、极坐标标准差、梯度幅值均值、梯度标准差、曲率均值、局部方差均值
        # 原始：12个，减少到：7个
        global_param_bases_condensed = [
            "MAXMCGVALRAW", "MINMCGVALRAW", "MEANMCGVALRAW",  # 保留原始值统计
            "POLARSTD", "GRADIENTMAGNITUDEMEAN", "GRADIENTSTD", "CURVATUREMEAN",
            "LOCALVARIANCEMEAN"  # 示例选择，根据需要调整
        ]
        for base in global_param_bases_condensed:
            key = self._format_mfm_frame_feature_name(base)
            selected_values.append(frame_feature_dict_from_read_file_plot3.get(key, 0.0))
            selected_keys_bases.append(key)

        # --- 策略A.2：减少quantile_current_bases ---
        # 保留：电流特征的幅值和角度
        # 原始：4个，减少到：2个
        quantile_current_bases_condensed = ["MAXCURRENTMAGNITUDE", "MAXCURRENTANGLE"]

        # --- 策略A.3：大幅减少pole_geometry_bases和极点数量 ---
        # 保留：面积、伸长率(K)、方向(角度)、强度(值)、圆度
        # 原始：每个极点27个，减少到：每个极点5个
        pole_geometry_bases_condensed = [
            "MCGS", "K", "ANGLE", "VALUE", "CIRCLERANGE"
        ]

        for w_float in w_list_for_selection:  # 如果w_list_for_selection是[0.5]，此循环将运行一次
            # 添加压缩的电流特征
            for base in quantile_current_bases_condensed:
                key = self._format_mfm_frame_feature_name(base, quantile_tag=w_float)
                selected_values.append(frame_feature_dict_from_read_file_plot3.get(key, 0.0))
                selected_keys_bases.append(key)

            # 仅为主要POS和主要NEG极点添加压缩的极点几何特征
            # 原始：POS/NEG，POLE0/POLE1。减少到：POS/NEG，仅POLE0。
            for pole_type_str in ["POS", "NEG"]:
                pole_id_int = 0  # 仅主要极点（POLE0）
                for base_geom_metric in pole_geometry_bases_condensed:
                    key = self._format_mfm_frame_feature_name(base_geom_metric,
                                                              quantile_tag=w_float,
                                                              pole_type=pole_type_str,
                                                              pole_id=pole_id_int)
                    selected_values.append(frame_feature_dict_from_read_file_plot3.get(key, 0.0))
                    selected_keys_bases.append(key)

        return selected_values, selected_keys_bases
    def get_params(self, pkl_data_single_file, task_key_like_tt_dim_3=None, w_list_for_selection=None):
        """
        pkl_data_single_file: 这是一个ECG记录（包含'Data'、'label'、'mcg_file'的字典）。
        task_key_like_tt_dim_3: 例如，'tt_dim_3'，用于获取该段的帧特征列表。
        w_list_for_selection: 要选择特征的分位数。

        返回:
            time_series_data (np.array): 形状为(num_frames_in_segment, num_selected_frame_features)
            final_feature_names_for_timeseries (list): MFMIMGTS_{SEGMENT}_{FRAME_FEATURE_BASE}的列表
        """
        if w_list_for_selection is None:
            w_list_for_selection = [0.5]  # 来自get_complex_features的默认值

        # 这是特定段（task_key_like_tt_dim_3）的帧特征字典列表
        # 此列表中的每个字典都有类似"MFMIMGTS_TT_FRAME_MAXMCGVAL"的键
        frames_data_for_segment = pkl_data_single_file['Data'][task_key_like_tt_dim_3]

        segment_prefix_from_task_key = task_key_like_tt_dim_3.split('_')[0].upper()  # TT, QR等

        all_selected_series_values = []  # 构建(num_frames, num_features)数组
        final_feature_names_for_timeseries = []  # 存储DataFrame列名，用于外部`extract_features`

        first_frame = True
        for frame_dict_with_mfmgts_prefix in frames_data_for_segment:
            # 去除MFMIMGTS_{SEGMENT}_前缀，为extract_params_revised获取通用FRAME_键
            generic_frame_feature_dict = {}
            prefix_to_strip = f"MFMIMGTS_{segment_prefix_from_task_key}_"
            for prefixed_key, value in frame_dict_with_mfmgts_prefix.items():
                if prefixed_key.startswith(prefix_to_strip):
                    generic_key = prefixed_key[len(prefix_to_strip):]
                    generic_frame_feature_dict[generic_key] = value
                else:  # 如果process_band_features_revised正确工作，这不应该发生
                    generic_frame_feature_dict[prefixed_key] = value

            selected_values_for_frame, selected_keys_bases_for_frame = self.extract_params(
                generic_frame_feature_dict, w_list_for_selection
            )
            all_selected_series_values.append(selected_values_for_frame)

            if first_frame:
                # 为这些时间序列列构造最终名称
                for base_key in selected_keys_bases_for_frame:
                    # base_key类似"FRAME_MAXMCGVAL"
                    # 最终名称是"MFMIMGTS_TT_FRAME_MAXMCGVAL"
                    final_feature_names_for_timeseries.append(f"MFMIMGTS_{segment_prefix_from_task_key}_{base_key}")
                first_frame = False

        return np.array(all_selected_series_values), final_feature_names_for_timeseries

    def process_band_features(self,
                                      all_frames_feature_list: list,
                                      # 来自extract_features_from_pic的扁平字典列表
                                      tasks: list  # self.tasks
                                      ) -> dict:
        """
        将帧特征列表拆分回原始ECG段
        并重命名特征以包含段和MFMIMGTS前缀。
        输出: {'tt_dim_3': [tt的帧字典列表], 'qr_dim_3': [...]}
             其中每个帧字典都有类似"MFMIMGTS_TT_FRAME_MAXMCGVAL"的键
        """
        output_band_features_data = {}  # 存储每个波段的特征字典列表
        current_frame_idx = 0

        for (original_start_label, original_end_label,
             resampled_duration, task_output_key) in tasks:  # task_output_key是'tt_dim_3'等

            segment_code = task_output_key.split('_')[0].upper()  # TT, QR, RS, ST, PT

            # 获取对应于此段重采样持续时间的帧
            segment_frames_feature_list = all_frames_feature_list[
                                          current_frame_idx: current_frame_idx + resampled_duration
                                          ]

            renamed_segment_frames_list = []
            for frame_feature_dict in segment_frames_feature_list:
                renamed_frame_dict = {}
                for generic_frame_key, value in frame_feature_dict.items():
                    # generic_frame_key是"FRAME_MAXMCGVAL"
                    # 我们想要"MFMIMGTS_TT_FRAME_MAXMCGVAL"
                    # generic_frame_key已经有来自_format_mfm_frame_feature_name的FRAME_前缀
                    final_name = f"MFMIMGTS_{segment_code}_{generic_frame_key}"
                    renamed_frame_dict[final_name] = value
                renamed_segment_frames_list.append(renamed_frame_dict)

            output_band_features_data[task_output_key] = renamed_segment_frames_list
            current_frame_idx += resampled_duration

        return output_band_features_data

    @staticmethod
    def _try_convert_numeric_static(df):  # 设为静态或独立实用程序
        # (你的try_convert_numeric的实现)
        # 现在，让我们假设它工作并返回转换后的df
        df_conv = df.copy()
        for col in df_conv.columns:
            try:
                df_conv[col] = pd.to_numeric(df_conv[col])
            except ValueError:
                print(f"警告：列{col}无法完全转换为数值。")
                # 决定如何处理：填充NaN、引发错误，或如果某些非数值是可接受的则保持原样（对于统计不太可能）
        return df_conv

    @staticmethod
    def _max_consecutive_length_static(series_bool):  # series_bool类似(diff_series > 0)
        # (你的max_consecutive_length的实现)
        """
           计算布尔序列中True值的最大连续长度。
       """
        if not isinstance(series_bool, np.ndarray) or series_bool.ndim != 1:
            # 处理输入不是1D numpy数组的情况，尽管类型提示应该指导这一点
            return 0
        if series_bool.size == 0:  # 空输入序列
            return 0
        if not series_bool.any():  # 完全没有True值
            return 0

        # 原始逻辑：
        # padded_series = np.concatenate(([False], series_bool, [False]))
        # where_true_indices = np.where(padded_series)[0]
        # diff_indices = np.diff(where_true_indices)
        # run_lengths = diff_indices[::2] # 如果diff_indices很短，这是有问题的切片

        # 计算True运行长度的更稳健方法
        # 找到True块的开始和结束
        bounded = np.concatenate(([0], series_bool.astype(int), [0]))
        # 差异识别True序列的开始和结束
        # 开始：diff为1的地方。结束：diff为-1的地方
        diffs = np.diff(bounded)
        run_starts = np.where(diffs > 0)[0]
        run_ends = np.where(diffs < 0)[0]

        if run_starts.size == 0 or run_ends.size == 0:  # 没有找到True的运行
            # 如果series_bool全为False（已捕获）或全为True，可能发生这种情况
            if series_bool.all():  # 如果全为True，长度就是序列长度本身
                return series_bool.size
            return 0  # 否则，没有True的运行

        run_lengths = run_ends - run_starts

        if run_lengths.size == 0:
            return 0
        else:
            return np.max(run_lengths)

    @staticmethod
    def _find_turning_points_static(series_data):
        # (你的find_turning_points的实现)
        # 示例简单实现：
        diffs = np.diff(np.sign(np.diff(series_data)))
        return np.where(np.abs(diffs) > 0)[0] + 1  # 转折点的索引

    @staticmethod
    def _calculate_shape_factor_static(series_data):
        # (你的calculate_shape_factor的实现)
        if np.std(series_data) == 0: return 0  # 避免常数序列的除零
        return np.sqrt(np.mean(series_data ** 2)) / np.mean(np.abs(series_data))  # 示例：RMS / 平均绝对值

    @staticmethod
    def _calculate_peak_valley_ratio_static(series_data):
        # (你的calculate_peak_valley_ratio的实现)
        # 这需要稳健的峰/谷检测。
        # 现在，占位符：
        if len(series_data) < 3: return 1.0
        peaks = series_data[TimeImageFeatures._find_turning_points_static(series_data)]  # 简化
        valleys = series_data[TimeImageFeatures._find_turning_points_static(-series_data)]  # 简化
        if len(peaks) == 0 or len(valleys) == 0 or np.mean(valleys) == 0: return 1.0
        return np.mean(peaks) / np.mean(valleys) if np.mean(valleys) != 0 else 1.0

    def _extract_temporal_stats_from_series_df(self, df_time_series: pd.DataFrame, w_filter_val: float) -> pd.DataFrame:
        """
        修订版：提取更加压缩的时间统计集合。
        """
        if df_time_series.empty:
            return pd.DataFrame()

        df_numeric = self._try_convert_numeric_static(df_time_series)
        all_temporal_features = {}
        quantile_suffix_str = f"W{str(w_filter_val).replace('.', 'P')}"

        for input_col_name in df_numeric.columns:
            col_data = df_numeric[input_col_name].dropna()

            if len(col_data) < 3:  # 一些修订的统计需要至少3个点（例如，用于转折点）
                # 对于非常短的序列，许多统计变为NaN或意义较小。
                # 如果序列太短，为预期的压缩特征填充NaN
                condensed_temp_stats_bases = [
                    "MEAN", "STD", "MAX", "MIN",  # 基本
                    "SKEW", "KURT",  # 分布
                    "MEANABSDIFF", "PROPINCR",  # 基于差异
                    "TURNPNTRATIO", "SHAPEFACTOR",  # 形态学
                    "TRENDSLOPE", "SAMPLEENTROPY"  # 从"简洁"列表添加
                ]
                for base_stat_name in condensed_temp_stats_bases:
                    all_temporal_features[f"{input_col_name}_{base_stat_name}_{quantile_suffix_str}"] = np.nan
                continue

            # --- 压缩的时间统计 ---
            # 1. 基本统计（保留4个）
            all_temporal_features[f"{input_col_name}_MEAN_{quantile_suffix_str}"] = np.mean(col_data)
            all_temporal_features[f"{input_col_name}_STD_{quantile_suffix_str}"] = np.std(col_data)
            all_temporal_features[f"{input_col_name}_MAX_{quantile_suffix_str}"] = np.max(col_data)
            all_temporal_features[f"{input_col_name}_MIN_{quantile_suffix_str}"] = np.min(col_data)

            mabs = np.mean(np.abs(col_data))  # 用于条件计算

            # 2. 分布统计（如果序列允许，保留2个）
            if mabs != 0 and len(col_data.unique()) > 1:
                all_temporal_features[f"{input_col_name}_SKEW_{quantile_suffix_str}"] = skew(col_data)
                all_temporal_features[f"{input_col_name}_KURT_{quantile_suffix_str}"] = kurtosis(col_data)
            else:
                all_temporal_features[f"{input_col_name}_SKEW_{quantile_suffix_str}"] = np.nan
                all_temporal_features[f"{input_col_name}_KURT_{quantile_suffix_str}"] = np.nan

            # 3. 基于差异的统计（保留2个）
            diff_series = np.diff(col_data)  # 已检查len(col_data) >=2，所以diff_series有>=1个元素
            if len(diff_series) > 0:
                all_temporal_features[f"{input_col_name}_MEANABSDIFF_{quantile_suffix_str}"] = np.mean(
                    np.abs(diff_series))
                all_temporal_features[f"{input_col_name}_PROPINCR_{quantile_suffix_str}"] = np.mean(diff_series > 0)
            else:  # 如果len(col_data) >=2，这不应该发生
                all_temporal_features[f"{input_col_name}_MEANABSDIFF_{quantile_suffix_str}"] = np.nan
                all_temporal_features[f"{input_col_name}_PROPINCR_{quantile_suffix_str}"] = np.nan

            # 4. 形态学统计（保留2个 + 从"简洁"列表中的2个 = 选择4个）
            # 保留TURNPNTRATIO, SHAPEFACTOR。添加TRENDSLOPE, SAMPLEENTROPY
            # 移除：PEAKVALLEYRATIO, AREAUNDERCURVE（面积与均值相关）
            turning_points = self._find_turning_points_static(col_data)
            all_temporal_features[f"{input_col_name}_TURNPNTRATIO_{quantile_suffix_str}"] = len(turning_points) / len(
                col_data) if len(col_data) > 0 else 0.0
            all_temporal_features[
                f"{input_col_name}_SHAPEFACTOR_{quantile_suffix_str}"] = self._calculate_shape_factor_static(col_data)

            # 从"简洁列表"想法添加：
            # 趋势斜率
            x_coords = np.arange(len(col_data))
            try:
                slope_val, _ = np.polyfit(x_coords, col_data, 1)
                all_temporal_features[f"{input_col_name}_TRENDSLOPE_{quantile_suffix_str}"] = slope_val
            except (
            np.linalg.LinAlgError, ValueError):  # 处理polyfit失败的情况（例如，如果未删除，所有NaN）
                all_temporal_features[f"{input_col_name}_TRENDSLOPE_{quantile_suffix_str}"] = np.nan

            # 样本熵（确保你有稳健的实现或使用库）
            # 样本熵计算的占位符：
            try:
                # from some_entropy_library import sample_entropy # 示例
                # current_sample_entropy = sample_entropy(col_data, m=2, r=0.2*np.std(col_data))
                # 现在，一个占位符值。用实际计算替换。
                # 如果使用tsfresh的：from tsfresh.feature_extraction.feature_calculators import sample_entropy
                from tsfresh.feature_extraction.feature_calculators import \
                    sample_entropy as ts_sample_entropy  # 避免名称冲突
                current_sample_entropy = ts_sample_entropy(col_data)
            except Exception:  # 捕获样本熵计算的错误
                current_sample_entropy = np.nan
            all_temporal_features[f"{input_col_name}_SAMPLEENTROPY_{quantile_suffix_str}"] = current_sample_entropy

        return pd.DataFrame(all_temporal_features, index=[0])
    def _orchestrate_temporal_extraction(self,
                                         single_file_data_struct: dict,  # 这类似pre_features[0]
                                         w_list_for_temporal_filtering: list
                                         ) -> pd.DataFrame:
        """
        为单个ECG文件的所有段和所有w_filters编排时间特征提取。
        这替换了_get_3d_features和_process_single_param。
        """
        all_final_features_for_file = []  # 保存每个w_filter的DataFrame的列表

        # 在w_list_for_temporal_filtering上并行化
        # 每个w_filter值导致所有段/所有选定帧时间序列的一组时间特征

        # starmap中工作函数的参数
        # 工作器将为所有段处理一个w_filter_val
        starmap_args = []
        for w_filter_val in w_list_for_temporal_filtering:
            starmap_args.append(
                (single_file_data_struct, w_filter_val, self.tasks)  # 传递self.tasks
            )

        # 使用Pool进行并行处理
        # 进程数可以是len(w_list_for_temporal_filtering)或固定数字
        # 如果len(w_list_for_temporal_filtering)很小（例如，1或2），Pool开销可能不值得。
        # 让我们假设它可以> 1。
        num_processes = min(len(w_list_for_temporal_filtering), os.cpu_count() or 1)  # 合理的并行性

        if num_processes > 0 and len(w_list_for_temporal_filtering) > 0:  # 确保有工作要做
            with Pool(processes=5) as pool:
                # _worker_process_single_w_filter需要是静态方法或顶级函数
                # 以便multiprocessing.Pool可以pickle。或使用辅助类。
                # 为简单起见，让我们使其成为调用实例方法的静态方法。
                # 这很棘手。一个常见模式是传递'self'或必要的实例方法/数据。
                # 替代方案：在这里循环并调用实例方法，如果在w_filters上并行化不是主要目标，
                # 或者如果_worker_process_single_w_filter内的工作已经并行。

                # 更简单：迭代和调用，然后连接。如果此循环很慢，可以添加并行性。
                # results_from_w_filters = []
                # for w_filter_val in w_list_for_temporal_filtering:
                #     result_df = self._worker_process_single_w_filter_instance_method(
                #         single_file_data_struct, w_filter_val, self.tasks
                #     )
                #     results_from_w_filters.append(result_df)

                # 使用带有静态方法辅助器的starmap：
                results_from_w_filters = pool.starmap(
                    TimeImageFeatures._static_worker_process_single_w_filter,
                    [(self, arg_tuple[0], arg_tuple[1], arg_tuple[2]) for arg_tuple in starmap_args]  # 传递self
                )

            for result_df in results_from_w_filters:
                if not result_df.empty:
                    all_final_features_for_file.append(result_df)

        if not all_final_features_for_file:
            return pd.DataFrame()  # 如果没有生成特征，返回空

        # 将不同w_filters的DataFrame并排连接
        return pd.concat(all_final_features_for_file, axis=1)

    @staticmethod
    def _static_worker_process_single_w_filter(instance,  # TimeImageFeatures的'self'
                                               single_file_data_struct: dict,
                                               w_filter_val: float,
                                               tasks_list: list):
        """由multiprocessing.Pool调用的静态方法。"""
        return instance._worker_process_single_w_filter_instance_method(
            single_file_data_struct, w_filter_val, tasks_list
        )

    def _worker_process_single_w_filter_instance_method(self,
                                                        single_file_data_struct: dict,
                                                        w_filter_val: float,
                                                        tasks_list: list  # self.tasks
                                                        ) -> pd.DataFrame:
        """
        为所有定义的ECG段处理一个w_filter值。
        这是旧_process_single_param的主体。
        """
        # param_types_task_keys = ['tt_dim_3', 'qr_dim_3', 'rs_dim_3', 'st_dim_3', 'pt_dim_3'] # 来自self.tasks

        all_segment_temporal_features_for_this_w = []

        for (_, _, _, task_key) in tasks_list:  # task_key是'tt_dim_3'等

            time_series_array, column_names_for_df = self.get_params(  # 使用修订的get_params
                single_file_data_struct,
                task_key_like_tt_dim_3=task_key,
                w_list_for_selection=[w_filter_val]
            )

            if time_series_array.size == 0:  # 此段/w_filter没有时间序列数据
                # print(f"警告：任务{task_key}与w_filter {w_filter_val}没有时间序列数据")
                # 创建空DF或带有NaN特征的DF以确保concat工作
                # 现在，只是跳过，如果其他有数据，concat将处理空DF。
                continue

            df_for_temporal_extraction = pd.DataFrame(time_series_array, columns=column_names_for_df)

            # 调用新的集成时间特征提取方法
            df_temporally_extracted = self._extract_temporal_stats_from_series_df(
                df_for_temporal_extraction,
                w_filter_val
            )
            all_segment_temporal_features_for_this_w.append(df_temporally_extracted)

        if not all_segment_temporal_features_for_this_w:
            return pd.DataFrame()

        return pd.concat(all_segment_temporal_features_for_this_w, axis=1)

    def get_all_features_final_version(self, list_of_single_file_data_structs: list,
                                       w_list_for_temporal_filtering: list) -> pd.DataFrame:
        if not list_of_single_file_data_structs:
            return pd.DataFrame()

        single_file_data = list_of_single_file_data_structs[0]  # 假设一次一个文件

        final_features_df = self._orchestrate_temporal_extraction(
            single_file_data,
            w_list_for_temporal_filtering
        )

        if final_features_df.empty:  # 处理没有生成特征的情况
            # 创建仅包含ID和标签的DataFrame以维护结构
            return pd.DataFrame({
                'mcg_file': [single_file_data.get('mcg_file', 'unknown')],
                'label': [single_file_data.get('label', 'unknown_label')]
            })

        # 插入MCG_ID和标签
        final_features_df.insert(0, "label", single_file_data['label'])
        final_features_df.insert(0, 'mcg_file', single_file_data['mcg_file'])
        return final_features_df

    def get_complex_features(self, basic_args_dict=None, label=None,
                             w_list_quantiles_for_frame_features=None,
                             w_list_filters_for_temporal_selection=None,
                             num_chunks_override=None) -> pd.DataFrame:

        w_list_frame_extraction = w_list_quantiles_for_frame_features or self.w_list_frame_extraction
        # w_list_temporal_filter = w_list_filters_for_temporal_selection or [0.5]
        current_num_chunks = num_chunks_override or self.num_chunks_frame_proc

        basic_args_dict = basic_args_dict or self.basic_args_dict
        current_label = label or self.label

        data_frame_raw = basic_args_dict.get('data_frame')
        if data_frame_raw is None: raise ValueError("在basic_args_dict中未找到data_frame")
        signal_channels_raw = np.array(data_frame_raw.iloc[:, 1:]).T  # (num_channels, total_time_original_signal)
        result_peak = basic_args_dict.get('result_peak')
        if result_peak is None: raise ValueError("在basic_args_dict中未找到result_peak")

        file_path_for_id = basic_args_dict.get('file_path', 'unknown_file.txt')
        mcg_file_id = os.path.basename(file_path_for_id).replace('.txt', '')

        t_start_prep = time.time()
        resampled_segments_list = self.resample_wave_tasks(self.tasks, signal_channels_raw, result_peak)
        normalized_resampled_segments_list = self.unified_normalization(resampled_segments_list)
        # normalized_resampled_segments_list中的每个元素是(num_channels=36, fixed_resampled_length)
        print(f"  TimeImageFeatures: 重采样和归一化耗时: {time.time() - t_start_prep:.2f}s")

        concatenated_frames_data = np.concatenate(normalized_resampled_segments_list, axis=1)
        # concatenated_frames_data形状: (36, total_resampled_frames_across_all_segments)
        if concatenated_frames_data.size == 0:
            print(f"警告: {mcg_file_id}的连接帧数据为空。无法提取特征。")
            return pd.DataFrame({'mcg_file': [mcg_file_id], 'label': [current_label]})

        t_start_frame_feat = time.time()
        data_chunks_for_pic_extraction = split_result(concatenated_frames_data, current_num_chunks)
        all_frames_raw_features_list = []

        actual_processes_for_chunks = min(current_num_chunks, os.cpu_count() or 1, len(data_chunks_for_pic_extraction))
        if actual_processes_for_chunks > 0 and len(data_chunks_for_pic_extraction) > 0:
            with Pool(processes=5) as pool:
                list_of_list_of_dicts = pool.starmap(
                    self.extract_features_from_pic,  # 静态方法调用
                    [(chunk, w_list_frame_extraction) for chunk in data_chunks_for_pic_extraction if chunk.size > 0]
                    # 确保数据块不为空
                )
                for single_chunk_result_list in list_of_list_of_dicts:
                    all_frames_raw_features_list.extend(single_chunk_result_list)
        elif len(data_chunks_for_pic_extraction) > 0:  # 如果不使用池，则串行执行
            for chunk in data_chunks_for_pic_extraction:
                if chunk.size > 0:
                    all_frames_raw_features_list.extend(
                        self.extract_features_from_pic(chunk, w_list_frame_extraction))
        print(
            f"  TimeImageFeatures: {mcg_file_id}的帧特征提取耗时: {time.time() - t_start_frame_feat:.2f}s")

        if not all_frames_raw_features_list:
            print(f"警告: {mcg_file_id}未提取到原始帧特征。返回最小DataFrame。")
            return pd.DataFrame({'mcg_file': [mcg_file_id], 'label': [current_label]})

        tasks_frame_features_named_and_segmented = self.process_band_features(  # 调用实例方法
            all_frames_raw_features_list, self.tasks
        )

        data_for_final_temporal_extraction = {
            'mcg_file': mcg_file_id,
            'Data': tasks_frame_features_named_and_segmented,
            'label': current_label
        }

        t_start_temporal_feat = time.time()
        final_features_df = self.get_all_features_final_version(  # 调用实例方法
            [data_for_final_temporal_extraction],
            w_list_frame_extraction
        )
        print(
            f"  TimeImageFeatures: {mcg_file_id}的最终时间特征提取耗时: {time.time() - t_start_temporal_feat:.2f}s")

        return final_features_df
