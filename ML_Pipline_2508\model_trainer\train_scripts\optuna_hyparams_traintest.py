"""
@Project：ML_pipline/model_trainer
@File   ：optuna_hyparams_traintest.py
@IDE    ：PyCharm
<AUTHOR>
@Date   ：2023/7/24 10:35
@Discribe：
     参数搜索并保存结果,使用保存的超参数，训练模型并在测试集上测试。(非外部)
     0823记录： 目前的调优未见明显实际效果提升  可能是搜索轮数太少所致。
"""

import pickle

from matplotlib import pyplot as plt

from model_optuner.optimizer import XGBOptimizer
from model_trainer.models import (load_and_prepare_data, process_safe_features, Models,
                                  get_model_predict_results, plot_folds_results)

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号问题

if __name__ == '__main__':
    # ------------------------------------------------  配置 ------------------------------------------------------
    # 基础配置
    DATA_REGION = "integrated"  # "consistency" "integrated"
    MODEL_NAME = 'xgb'
    # 路径配置
    FEATURE_PKL = "./files/saved_features/integrated_all_features_stable_F5_s42_fold_all.pkl"
    ID_PKL = 'D:/wyh/Code/daily_debug/ML_Pipline/files/data/data_index/data_V0624_' + DATA_REGION + '/S42-F5.pkl'
    SELECTED_FEATURE_PKL = './files/saved_features/selected_features/' + 'integrated' + '_all_features_stable_F5_s42_fold_all_top300.pkl'
    OPTUNA_STORE = r'sqlite:///./files/logs/optuna/' + DATA_REGION + '_stable_all_top300'
    LOG_DIR = r"D:\wyh\Code\daily_debug\ML_Pipline\files\logs\tensorboard\tensorboard_" + DATA_REGION + "_stable_all_top300"

    # 0820后
    FEATURE_PKL = "./files/saved_features/features_V0821_all_tspt_2534.pkl"
    ID_PKL = "./files/data/data_index/data_V0820/S42-F5.pkl"
    SELECTED_FEATURE_PKL = "./files/saved_features/selected_features/features_V0821F5S42_tspt_stable_top150.pkl"
    OPTUNA_STORE = r'sqlite:///./files/logs/optuna/V0821F5S42_tspt_stable_top150'
    LOG_DIR = r"/logs/tensorboard/tensorboard_V0821F5S42_tspt_stable_top150"

    # -----------------------------------------  参数搜索并保存结果 -------------------------------------------------
    # 开始搜索优化超参数
    best_params = []
    for folder in range(5):
        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl=ID_PKL,
            features_pkl=FEATURE_PKL,
            selected_features_pkl=SELECTED_FEATURE_PKL,
            fold=folder)

        # 获取无异常字符的安全特征  # LGBM需要
        safe_train_data = process_safe_features(train_data, mode=MODEL_NAME)
        safe_test_data = process_safe_features(test_data, mode=MODEL_NAME)
        safe_valid_data = process_safe_features(valid_data, mode=MODEL_NAME)
        safe_train_valid_data = process_safe_features(train_valid_data, mode=MODEL_NAME)

        optimizer = XGBOptimizer(safe_train_valid_data, train_valid_data['label'],
                                 safe_test_data, test_data['label'],
                                 fold=folder, task=DATA_REGION + f'_features_V0821F5S42_stable_fold{folder}_top150',
                                 optuna_store=OPTUNA_STORE, log_dir=LOG_DIR,
                                 load_if_exists=True)

        best_trial = optimizer.optimize(n_trials=500)
        best_params.append(best_trial.params)

    # 保存搜索结果 xgb_V0821F5S42_tspt_stable_top150.pkl
    with open(r'./files/saved_hyperparams/' + MODEL_NAME + '_' + 'V0821F5S42_tspt_stable_top150' + '.pkl', 'wb') as f:
        pickle.dump(best_params, f)

    # ---------------------------------- 使用保存的超参数，训练模型并在测试集上测试。(非外部)---------------------------------
    try:
        with open('./files/saved_hyperparams/' + MODEL_NAME + '_' + 'V0821F5S42_tspt_stable_top150' + '.pkl',
                  'rb') as f:
            best_params = pickle.load(f)
    except:  # 如果不存在，就用默认值
        best_params = [None, None, None, None, None]

    # 在训练-测试集预测{综合模型/一致性模型}
    results = []
    models_trained = []

    for fold in range(5):
        # 加载模型与参数
        models = Models()
        models.params = best_params[fold]
        models.params['seed'] = 2023
        models.params['tree_mothod'] = 'hist'
        models.xgb_model()
        model, MODEL_NAME = models.model, models.model_name
        # 加载数据
        train_data, test_data, valid_data, train_valid_data = load_and_prepare_data(
            id_pkl=ID_PKL,
            features_pkl=FEATURE_PKL,
            selected_features_pkl=SELECTED_FEATURE_PKL,
            fold=fold)
        # 获取无异常字符的安全特征
        safe_train_data, safe_test_data, safe_valid_data, safe_train_valid_data = [
            process_safe_features(x, mode=MODEL_NAME) for x in [train_data, test_data, valid_data, train_valid_data]]
        # 模型拟合
        model.fit(safe_train_valid_data, train_valid_data['label'])
        # 获取预测结果
        result_dict = get_model_predict_results(model=model,
                                                x_train=safe_train_valid_data,
                                                y_train=train_valid_data['label'],
                                                x_test=safe_test_data,
                                                y_test=test_data['label'],
                                                x_valid=safe_valid_data,
                                                y_valid=valid_data['label'],
                                                )

        results.append(result_dict)
        models_trained.append(model)

    # 可视化训练结果
    avg = plot_folds_results(results)
    # 保存模型
    save_path = './files/saved_models/' + MODEL_NAME + '_tuned_' + 'V0821F5S42_tspt_stable_top150' + '.pkl'
    save_res = {'models_trained': models_trained, 'results': results}
    pickle.dump(save_res, open(save_path, 'wb'))
