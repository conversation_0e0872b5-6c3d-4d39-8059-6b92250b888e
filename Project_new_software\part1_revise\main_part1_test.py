"""
@ Author: <PERSON>
@ E-mail: <EMAIL>
@ Date: 2024/11/13 10:05
主要用来测试运行时间
"""
from func.ventricle_tool_no import *
from func.fun_colletion import *
pi = math.pi
import time


class Data:
    def __init__(self, file_name):
        self.file_name = file_name

    def read_mcg(self):
        with open(self.file_name, 'r') as ff:
            file = ff.read()  # 读取的是字符串
        da = []
        for i in file.split():  # 把上面字符串记为数字插入
            da.append(float(i))
        datat = []
        for i in range(int(len(da) / 37)):
            datat.append(da[37 * i + 1:37 * (i + 1)])
        mcg_data = np.array(datat)
        return mcg_data


def main_part1(mcg_path, heart_path, time_loc):
    """
    :param mcg_path: 心磁路径
    :param heart_path: 心脏模型路径
    :param time_loc: 时刻点 np.array([Qp, Rp, Sp]), 分别是Q峰，R峰， S峰
    :return:
    """
    mesh_path, lcav_path, lcav_no_path, rcav_path = \
        heart_path + '\\heart_point.msh', heart_path + '\\heart_point_lcav_mesh.msh', \
            heart_path + '\\poisson_mesh_lcav.msh', heart_path + '\\heart_point_rcav_mesh.msh'
    DataProcess = Data(mcg_path)
    mcg_data = DataProcess.read_mcg()
    Qp, Rp, Sp = time_loc[0], time_loc[1], time_loc[2]
    check_time = np.array([Qp, Sp + 1])
    time_1 = time.time()
    recon = SurfaceRecon(mesh_path, lcav_path, Qp, Rp, Sp, check_time, mcg_data)
    time_2 = time.time()
    print('object_time=%f' % (time_2 - time_1))
    recon.get_mesh()
    time_3 = time.time()
    print('get_mesh_time=%f' % (time_3 - time_2))
    recon.model_heart_No_CTA(z_A=57.4, z_B=49.52)
    time_4 = time.time()
    print('model_heart_time=%f' % (time_4 - time_3))
    recon.get_lf()
    time_5 = time.time()
    print('get_lf_time=%f' % (time_5 - time_4))
    result = recon.source_strength()
    time_6 = time.time()
    print('get_result_time=%f' % (time_6 - time_5))
    return np.array(result)


if __name__ == '__main__':
    start_time = time.time()
    heart_path = 'model'

    'example 1'
    # mcg_path = r'data\\PLAG_2024_000059.txt'
    # time_loc = np.array([311, 335, 361])

    'example 2'
    mcg_path = r'data\\BJ_TT_000516.txt'
    time_loc = np.array([324, 359, 388])
    result = main_part1(mcg_path, heart_path, time_loc)
    print(result)
    end_time = time.time()
    print('operator time = %f' % (end_time - start_time))
