'''
@Project ：ML_Pipline 
@File    ：optimizer.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/7/29 9:38 
@Discribe：改自qdg,综合版本：用optuna寻找超参数的工具类与方法。
'''
import os
import pickle
from datetime import datetime
from functools import partial

import numpy as np
import optuna
import tensorflow as tf
from imblearn.metrics import specificity_score
from lightgbm import LGBMClassifier
from pytorch_tabnet.tab_model import TabNetClassifier
from sklearn.metrics import balanced_accuracy_score
from sklearn.metrics import recall_score, confusion_matrix
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import StratifiedKFold
from xgboost import XGBClassifier

from model_optuner.metrics import get_metrics


def calculate_g_mean(y_true, y_pred):
    """
    计算G-mean

    参数:
    - y_true: 真实标签的数组
    - y_pred: 预测标签的数组

    返回:
    - G-mean值
    """
    # 计算混淆矩阵
    conf_matrix = confusion_matrix(y_true, y_pred)

    # 提取混淆矩阵的元素
    TP = conf_matrix[1, 1]  # 真正例
    TN = conf_matrix[0, 0]  # 真负例
    FP = conf_matrix[0, 1]  # 假正例
    FN = conf_matrix[1, 0]  # 假负例

    # 计算真正率（TPR）和真负率（TNR）
    TPR = TP / (TP + FN) if (TP + FN) else 0
    TNR = TN / (TN + FP) if (TN + FP) else 0

    # 计算G-mean
    G_mean = np.sqrt(TPR * TNR)

    return G_mean


class TensorBoardLogger:
    def __init__(self, log_dir):
        self.writer = tf.summary.create_file_writer(log_dir)

    def log_metrics(self, metrics, step, prefix=""):
        with self.writer.as_default():
            for key, value in metrics.items():
                tf.summary.scalar(f"{prefix}/{key}", value, step=step)
            self.writer.flush()


class XGBOptimizer():
    def __init__(self, x_train, y_train, x_test, y_test, fold, task, optuna_store, log_dir, load_if_exists=False):
        self.best_score = float('-inf')
        self.best_model = None
        self.x_train = x_train
        self.y_train = y_train
        self.x_test = x_test
        self.y_test = y_test
        self.load_if_exists = load_if_exists
        self.optuna_store = optuna_store
        self.results_dict = {
            'train': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []},
            'test': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []},
            'val': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []}
        }
        self.modelname = 'XGB'
        self.fold = fold
        self.task = task
        self.log_dir = log_dir
        log_dir = os.path.join(log_dir, f"{self.modelname}_fold_{self.fold}_{self.task}" + datetime.now().strftime(
            '%Y%m%d-%H%M%S'))
        os.makedirs(log_dir, exist_ok=True)
        self.tb_logger = TensorBoardLogger(log_dir)


    def objective_xgb(self, trial):
        # 超参数优化
        params = {
            'n_estimators': trial.suggest_int("n_estimators", 50, 500),
            'learning_rate': trial.suggest_float("learning_rate", 0.01, 0.5),
            'max_depth': trial.suggest_int('max_depth', 2, 10),
            'min_child_weight': trial.suggest_int('min_child_weight', 2, 10),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.5, 1),
            'subsample': trial.suggest_float('subsample', 0.2, 1),
            'gamma': trial.suggest_float('gamma', 0, 10),
            'lambda': trial.suggest_float('lambda', 0, 15),
            'alpha': trial.suggest_float('alpha', 0, 15),
            'scale_pos_weight': trial.suggest_float('scale_pos_weight', 0.2, 4),
            'max_delta_step': trial.suggest_int('max_delta_step', 0, 15),
            'seed': 2023,
            "tree_method": "hist",
        }
        # 初始化累加字典
        sum_train_metrics = {}
        sum_test_metrics = {}
        sum_val_metrics = {}
        n_splits = 5  # 或者你使用的 cv.split 的实际分割数
        cv = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=2023)
        auc_scores = []
        # 转换为 NumPy 数组
        X_np = self.x_train.to_numpy()
        y_np = self.y_train.to_numpy()

        for train_idx, test_idx in cv.split(X_np, y_np):
            # 使用 NumPy 数组切片
            X_train, X_val = X_np[train_idx], X_np[test_idx]
            y_train, y_val = y_np[train_idx], y_np[test_idx]
            # XGBoost模型
            model = XGBClassifier(**params)
            model.fit(X_train, y_train, verbose=False)

            # 模型预测
            train_prob = model.predict_proba(X_train)[:, 1]  # 获取正类的概率
            train_pred = model.predict(X_train)

            val_prob = model.predict_proba(X_val)[:, 1]  # 获取正类的概率
            val_pred = model.predict(X_val)

            test_prob = model.predict_proba(self.x_test)[:, 1]  # 获取正类的概率
            test_pred = model.predict(self.x_test)

            train_metrics = get_metrics(y_train, train_pred, train_prob)
            val_metrics = get_metrics(y_val, val_pred, val_prob)
            test_metrics = get_metrics(self.y_test, test_pred, test_prob)

            for key in train_metrics:
                sum_train_metrics[key] = sum_train_metrics.get(key, 0) + train_metrics[key]
                sum_test_metrics[key] = sum_test_metrics.get(key, 0) + test_metrics[key]
                sum_val_metrics[key] = sum_val_metrics.get(key, 0) + val_metrics[key]

            val_balance_acc = balanced_accuracy_score(y_val, val_pred)
            gmean = calculate_g_mean(y_val, val_pred)
            rec = recall_score(y_val, val_pred)
            spec = specificity_score(y_val, val_pred)
            auc = roc_auc_score(y_val, val_pred)

            metrics = rec + spec - abs(rec - spec)
            auc_scores.append(auc)

        # 可以自定义一个组合评分，例如平均对数损失的负值和平均AUC的组合
        combined_score = np.mean(auc_scores)
        # 计算平均指标
        avg_train_metrics = {key: value / n_splits for key, value in sum_train_metrics.items()}
        avg_test_metrics = {key: value / n_splits for key, value in sum_test_metrics.items()}
        avg_val_metrics = {key: value / n_splits for key, value in sum_val_metrics.items()}

        self.tb_logger.log_metrics(avg_train_metrics, step=trial.number, prefix="train")
        self.tb_logger.log_metrics(avg_test_metrics, step=trial.number, prefix="test")
        self.tb_logger.log_metrics(avg_val_metrics, step=trial.number, prefix="val")

        # for metric, dataset in zip([avg_train_metrics, avg_test_metrics, avg_val_metrics], ['train', 'test', 'val']):
        #     for k, v in metric.items():
        #         self.results_dict[dataset][k].append(v)
        # metricsboard(self.results_dict)
        # 检查当前得分是否超过最佳得分
        if combined_score > self.best_score:
            self.best_score = combined_score
            self.best_model = model
            # 保存最佳模型
            with open("./files/saved_models/" + self.task + "best_model.pkl", 'wb') as f:
                pickle.dump(model, f)
        return combined_score

    def optimize(self, n_trials=1000):
        study = optuna.create_study(storage=self.optuna_store, load_if_exists=self.load_if_exists, direction="maximize",
                                    study_name=f"{self.modelname}_fold_{self.fold}_{self.task}")
        study.optimize(partial(self.objective_xgb), n_trials=n_trials, show_progress_bar=True)
        return study.best_trial


class LGBMOptimizer():
    def __init__(self, x_train, y_train, x_test, y_test, fold, task, optuna_store, log_dir, load_if_exists=False):
        self.x_train = x_train
        self.y_train = y_train
        self.x_test = x_test
        self.y_test = y_test
        self.optuna_store = optuna_store
        self.load_if_exists = load_if_exists
        self.results_dict = {
            'train': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []},
            'test': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []},
            'val': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []}
        }
        self.modelname = 'LGBM'
        self.fold = fold
        self.task = task
        self.log_dir = log_dir
        log_dir = os.path.join(log_dir, f"{self.modelname}_fold_{self.fold}_{self.task}" + datetime.now().strftime(
            '%Y%m%d-%H%M%S'))
        os.makedirs(log_dir, exist_ok=True)

        print(log_dir)
        self.tb_logger = TensorBoardLogger(log_dir)

    def objective_xgb(self, trial):
        # 超参数优化
        params = {
            "n_estimators": trial.suggest_int("n_estimators", 50, 150),
            "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
            "num_leaves": trial.suggest_int("num_leaves", 10, 100),
            "max_depth": trial.suggest_int("max_depth", 3, 8),
            "min_data_in_leaf": trial.suggest_int("min_data_in_leaf", 0, 100),
            "lambda_l1": trial.suggest_float("lambda_l1", 0, 10),
            "lambda_l2": trial.suggest_float("lambda_l2", 0, 10),
            "min_gain_to_split": trial.suggest_float("min_gain_to_split", 0.1, 5),
            "subsample": trial.suggest_float("subsample", 0, 0.9),
            "colsample_bytree": trial.suggest_float("colsample_bytree", 0.5, 0.9),
            "feature_fraction": trial.suggest_float("feature_fraction", 0.5, 0.9),
            "min_child_weight": trial.suggest_float("min_child_weight", 0, 10),
            "random_state": 2023,
        }
        # 初始化累加字典
        sum_train_metrics = {}
        sum_test_metrics = {}
        sum_val_metrics = {}
        n_splits = 10  # 或者你使用的 cv.split 的实际分割数
        cv = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=2023)
        auc_scores = []
        # 转换为 NumPy 数组
        X_np = self.x_train.to_numpy()
        y_np = self.y_train.to_numpy()

        for train_idx, test_idx in cv.split(X_np, y_np):
            # 使用 NumPy 数组切片
            X_train, X_val = X_np[train_idx], X_np[test_idx]
            y_train, y_val = y_np[train_idx], y_np[test_idx]
            # XGBoost模型
            model = LGBMClassifier(**params)
            model.fit(X_train, y_train)

            # 模型预测
            train_prob = model.predict_proba(X_train)[:, 1]  # 获取正类的概率
            train_pred = model.predict(X_train)

            val_prob = model.predict_proba(X_val)[:, 1]  # 获取正类的概率
            val_pred = model.predict(X_val)

            test_prob = model.predict_proba(self.x_test)[:, 1]  # 获取正类的概率
            test_pred = model.predict(self.x_test)

            train_metrics = get_metrics(y_train, train_pred, train_prob)
            val_metrics = get_metrics(y_val, val_pred, val_prob)
            test_metrics = get_metrics(self.y_test, test_pred, test_prob)

            for key in train_metrics:
                sum_train_metrics[key] = sum_train_metrics.get(key, 0) + train_metrics[key]
                sum_test_metrics[key] = sum_test_metrics.get(key, 0) + test_metrics[key]
                sum_val_metrics[key] = sum_val_metrics.get(key, 0) + val_metrics[key]

            val_balance_acc = balanced_accuracy_score(y_val, val_pred)
            gmean = calculate_g_mean(y_val, val_pred)
            auc_scores.append(gmean)

        # 可以自定义一个组合评分，例如平均对数损失的负值和平均AUC的组合
        combined_score = np.mean(auc_scores)
        # 计算平均指标
        avg_train_metrics = {key: value / n_splits for key, value in sum_train_metrics.items()}
        avg_test_metrics = {key: value / n_splits for key, value in sum_test_metrics.items()}
        avg_val_metrics = {key: value / n_splits for key, value in sum_val_metrics.items()}

        self.tb_logger.log_metrics(avg_train_metrics, step=trial.number, prefix="train")
        self.tb_logger.log_metrics(avg_test_metrics, step=trial.number, prefix="test")
        self.tb_logger.log_metrics(avg_val_metrics, step=trial.number, prefix="val")

        # for metric, dataset in zip([avg_train_metrics, avg_test_metrics, avg_val_metrics], ['train', 'test', 'val']):
        #     for k, v in metric.items():
        #         self.results_dict[dataset][k].append(v)
        # metricsboard(self.results_dict)
        return combined_score

    def optimize(self, n_trials=500):
        study = optuna.create_study(storage=self.optuna_store, load_if_exists=self.load_if_exists, direction="maximize",
                                    study_name=f"{self.modelname}_fold_{self.fold}_{self.task}")
        study.optimize(partial(self.objective_xgb), n_trials=n_trials)
        return study.best_trial


class TabNetOptimizer():
    def __init__(self, x_train, y_train, x_test, y_test, fold, task, optuna_store, log_dir, load_if_exists=False):
        self.x_train = x_train
        self.y_train = y_train
        self.x_test = x_test
        self.y_test = y_test
        self.optuna_store = optuna_store
        self.load_if_exists = load_if_exists
        self.results_dict = {
            'train': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []},
            'test': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []},
            'val': {'Acc': [], 'Auc': [], 'Sen': [], 'Spe': [], 'Mcc': [], 'GMean': []}
        }
        self.modelname = 'TabPFN'
        self.fold = fold
        self.task = task
        self.log_dir = log_dir

        log_dir = os.path.join(log_dir, f"{self.modelname}_fold_{self.fold}_{self.task}" + datetime.now().strftime(
            '%Y%m%d-%H%M%S'))
        os.makedirs(log_dir, exist_ok=True)
        self.tb_logger = TensorBoardLogger(log_dir)

    def objective_tabnet(self, trial):
        # Hyperparameter optimization
        params = {
            "n_d": trial.suggest_int("n_d", 8, 64),
            "n_a": trial.suggest_int("n_a", 8, 64),
            "n_steps": trial.suggest_int("n_steps", 3, 10),
            "gamma": trial.suggest_float("gamma", 1.0, 2.0),
            "n_independent": trial.suggest_int("n_independent", 1, 5),
            "n_shared": trial.suggest_int("n_shared", 1, 5),
            "lambda_sparse": trial.suggest_float("lambda_sparse", 1e-4, 1e-1),
            "optimizer_params": {"lr": trial.suggest_float("lr", 1e-4, 1e-2)},
            "momentum": trial.suggest_float("momentum", 0.01, 0.4),
            'seed': 2023
        }

        # Initialize cumulative metrics
        sum_train_metrics = {}
        sum_test_metrics = {}
        sum_val_metrics = {}
        n_splits = 5  # The actual number of splits for your CV
        cv = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=2023)
        auc_scores = []

        X_np = self.x_train.to_numpy()
        y_np = self.y_train.to_numpy()

        for train_idx, test_idx in cv.split(X_np, y_np):
            X_train, X_val = X_np[train_idx], X_np[test_idx]
            y_train, y_val = y_np[train_idx], y_np[test_idx]

            # TabNet model
            model = TabNetClassifier(**params)
            model.fit(
                X_train=X_train, y_train=y_train,
                eval_set=[(X_val, y_val)],
                max_epochs=1000,
                batch_size=512,
                virtual_batch_size=256,
                num_workers=0,
                drop_last=False
            )

            # Model prediction
            train_pred, train_prob = model.predict(X_train), model.predict_proba(X_train)[:, 1]
            val_pred, val_prob = model.predict(X_val), model.predict_proba(X_val)[:, 1]
            test_pred, test_prob = model.predict(self.x_test), model.predict_proba(self.x_test)[:, 1]

            train_metrics = get_metrics(y_train, train_pred, train_prob)
            val_metrics = get_metrics(y_val, val_pred, val_prob)
            test_metrics = get_metrics(self.y_test, test_pred, test_prob)

            for key in train_metrics:
                sum_train_metrics[key] = sum_train_metrics.get(key, 0) + train_metrics[key]
                sum_test_metrics[key] = sum_test_metrics.get(key, 0) + test_metrics[key]
                sum_val_metrics[key] = sum_val_metrics.get(key, 0) + val_metrics[key]

            gmean = calculate_g_mean(y_val, val_pred)
            val_balance_acc = balanced_accuracy_score(y_val, val_pred)
            auc_scores.append(val_balance_acc)

        combined_score = np.mean(auc_scores)
        avg_train_metrics = {key: value / n_splits for key, value in sum_train_metrics.items()}
        avg_test_metrics = {key: value / n_splits for key, value in sum_test_metrics.items()}
        avg_val_metrics = {key: value / n_splits for key, value in sum_val_metrics.items()}

        self.tb_logger.add_scalars("metrics/train", avg_train_metrics, trial.number)
        self.tb_logger.add_scalars("metrics/test", avg_test_metrics, trial.number)
        self.tb_logger.add_scalars("metrics/val", avg_val_metrics, trial.number)

        return combined_score

    def optimize(self, n_trials=100):
        study = optuna.create_study(storage=self.optuna_store, load_if_exists=self.load_if_exists, direction="maximize",
                                    study_name=f"{self.modelname}_fold_{self.fold}_{self.task}")
        study.optimize(lambda trial: self.objective_tabnet(trial), n_trials=n_trials)
        return study.best_trial


