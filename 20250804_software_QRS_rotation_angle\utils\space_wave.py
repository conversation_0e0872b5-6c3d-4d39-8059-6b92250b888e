"""
Author: b<PERSON><PERSON><PERSON>
email: <EMAIL>

date: 2024/03/21 10:40
desc: 空间波组图指标
environment: py36
"""


import numpy as np
import collections

from utils.data import Data


class SpaceWave:

    def __init__(self, data: np.array, time: dict):
        self.data_dic = data
        self.time_dic = time
        self.indicator = dict()
        self.Weight_matrix = np.array([[1, 1, 1, 1, 1, 1], [1, 4, 4, 4, 4, 1], [1, 4, 9, 9, 4, 1], [1, 4, 9, 9, 4, 1], [1, 4, 4, 4, 4, 1], [1, 1, 1, 1, 1, 1],])

    @staticmethod
    def trans_1d_2d(index: int):
        row_index = 3 - index // 6
        col_index = index % 6 - 3
        if row_index <= 0:
            row_index -= 1
        if col_index >= 0:
            col_index += 1
        return col_index, row_index

    @staticmethod
    def find_zero_line(data: np.array):
        """
        data: np.array, shape(6,6)
        return: [[[x_1,x_2],[y_1,y_2]],...]
        """
        draw_cors = []
        for row in range(6):
            for col in range(6):
                for row_add, col_add in [[0, 1], [1, 0]]:
                    row_next = row + row_add
                    col_next = col + col_add
                    if row_next >= 6 or col_next >= 6:
                        continue
                    if data[row, col] * data[row_next, col_next] < 0:
                        draw_cors.append([[col_next, col + 1], [6 - row_next, 5 - row]])
        return draw_cors

    @staticmethod
    def find_longest_path(draw_cors):
        """
        draw_cors: [[[x_1,x_2],[y_1,y_2]],...]
        return: (index_end, path_count, index_start)
        """
        edges = collections.defaultdict(list)
        for x_index, y_index in draw_cors:
            edges[(x_index[0], y_index[0])].append((x_index[1], y_index[1]))
            edges[(x_index[1], y_index[1])].append((x_index[0], y_index[0]))
        res = ((0, 0), 0, (0, 0))
        for x in range(7):
            for y in range(7):
                if (x == 0 or x == 6 or y == 0 or y == 6) and (x, y) in edges:
                    wait_list = collections.deque([((x, y), 0, (x, y))])
                else:
                    continue
                seen = set()

                while wait_list:
                    end, cnt, start = wait_list.popleft()
                    seen.add(end)
                    if cnt > res[1]:
                        res = (end, cnt, start)
                    for new_end in edges[end]:
                        if new_end not in seen:
                            wait_list.append((new_end, cnt + 1, start))
        return res

    def cal_rt_diff_num(self):
        Rp, Tp = self.time_dic['Rp'], self.time_dic['Tp']
        data_Rp, data_Tp = self.data_dic[:, Rp], self.data_dic[:, Tp]
        self.indicator['rtsgnum'] = np.count_nonzero(data_Rp * data_Tp < 0)
        return 0

    def cal_rt_diff_num2(self):
        Rp, Tp = self.time_dic['Rp'], self.time_dic['Tp']
        data_Rp, data_Tp = self.data_dic[:, Rp], self.data_dic[:, Tp]
        rt_diff = data_Rp * data_Tp < 0
        rt_diff = rt_diff.reshape((6, 6))
        rt_diff_num = np.sum(rt_diff * self.Weight_matrix)
        self.indicator['rtsgnum2'] = rt_diff_num
        return 0

    def strong_pos_r(self):
        Rp = self.time_dic['Rp']
        data = np.max(np.abs(self.data_dic[:, Rp - 5:Rp + 5]), axis=-1)
        pos = np.argmax(data)
        col, row = self.trans_1d_2d(pos)
        self.indicator['posi_R'] = (col, row)
        return 0

    def strong_pos_t(self):
        Tp = self.time_dic['Tp']
        data = np.max(np.abs(self.data_dic[:, Tp - 5:Tp + 5]), axis=-1)
        pos = np.argmax(data)
        col, row = self.trans_1d_2d(pos)
        self.indicator['posi_T'] = (col, row)
        return 0

    def strong_pos_q(self):
        Qp = self.time_dic['Qp']
        data = np.max(np.abs(self.data_dic[:, Qp - 5:Qp + 5]), axis=-1)
        pos = np.argmax(data)
        col, row = self.trans_1d_2d(pos)
        self.indicator['posi_Q'] = (col, row)
        return 0

    def strong_pos_t_area(self):
        To, Te = self.time_dic['To'], self.time_dic['Te']
        data = np.sum(np.abs(self.data_dic[:, To:Te + 1]), axis=-1)
        pos = np.argmax(data)
        col, row = self.trans_1d_2d(pos)
        self.indicator['posi_area_tt'] = (col, row)
        return 0

    def strong_pos_qrs_area(self):
        Qh, Sr = self.time_dic['Qh'], self.time_dic['Sr']
        data = np.abs(np.sum(self.data_dic[:, Qh:Sr + 1], axis=-1))
        pos = np.argmax(data)
        col, row = self.trans_1d_2d(pos)
        self.indicator['posi_area_qrs'] = (col, row)
        return 0

    def cal_cor_line_diag_qrs(self):
        Qh, Sr = self.time_dic['Qh'], self.time_dic['Sr']
        data = self.data_dic[:, Qh:Sr]
        data = np.sum(data, axis=-1)
        data = data.reshape((6, 6))
        cors = self.find_zero_line(data)
        end, _, start = self.find_longest_path(cors)
        united = np.array(end + start)
        res1 = np.sum(np.square(united - np.array([6, 0, 0, 6])))
        res2 = np.sum(np.square(united - np.array([0, 6, 6, 0])))
        score = 72 - min(res1, res2)
        self.indicator['zeroqrs'] = score
        return 0

    def cal_cor_line_diag(self, indi_type: str = 'Rp'):
        time = self.time_dic[indi_type]
        data = self.data_dic[:, time-5:time+5]
        index = np.argmax(np.abs(data), axis=-1)
        data = data[np.arange(36), index].reshape((6, 6))
        cors = self.find_zero_line(data)
        end, _, start = self.find_longest_path(cors)
        united = np.array(end+start)
        res1 = np.sum(np.square(united-np.array([6, 0, 0, 6])))
        res2 = np.sum(np.square(united-np.array([0, 6, 6, 0])))
        score = 72 - min(res1, res2)
        if indi_type == 'Rp':
            self.indicator['zeroR'] = score
        else:
            self.indicator['zeroT'] = score
        return 0

    def cal_zero_line_rot(self, angle_type: int = 0):
        Rp, Tp = self.time_dic['Rp'], self.time_dic['Tp']
        Rp_data = self.data_dic[:, Rp - 5:Rp + 5]
        a = np.argmax(np.abs(Rp_data), axis=-1)
        Rp_data = Rp_data[np.arange(36), a].reshape((6, 6))
        Tp_data = self.data_dic[:, Tp - 5:Tp + 5]
        a = np.argmax(np.abs(Tp_data), axis=-1)
        Tp_data = Tp_data[np.arange(36), a].reshape((6, 6))

        Rp_cors = self.find_zero_line(Rp_data)
        Tp_cors = self.find_zero_line(Tp_data)

        R_end, _, R_start = self.find_longest_path(Rp_cors)
        T_end, _, T_start = self.find_longest_path(Tp_cors)

        R_angle = np.angle(complex(R_end[0] - R_start[0], R_end[1] - R_start[1]), deg=True)
        T_angle = np.angle(complex(T_end[0] - T_start[0], T_end[1] - T_start[1]), deg=True)
        if angle_type == 0:
            if R_angle == 90:
                R_angle *= -1
            if T_angle == 90:
                T_angle *= -1
            angle = T_angle - R_angle
            self.indicator['zeroRTrot'] = angle
        else:
            angle = abs(T_angle - R_angle)
            if 90 < angle < 180:
                angle -= 90
            if angle == 180:
                angle = 0
            self.indicator['zeroRTrot1'] = angle
        return 0

    def cal_all_indicators(self):
        self.cal_rt_diff_num()
        self.cal_rt_diff_num2()
        self.strong_pos_q()
        self.strong_pos_r()
        self.strong_pos_t()
        self.strong_pos_qrs_area()
        self.strong_pos_t_area()
        self.cal_cor_line_diag_qrs()
        self.cal_cor_line_diag()
        self.cal_cor_line_diag(indi_type='Tp')
        self.cal_zero_line_rot()
        self.cal_zero_line_rot(angle_type=1)
        return 0

    def get_indicators(self):
        self.cal_all_indicators()
        return self.indicator


def main(path: str):
    read_data = Data(path)
    data, time, _ = read_data.get()
    space_wave = SpaceWave(data, time)
    indicator_dict = space_wave.get_indicators()
    return indicator_dict


if __name__ == '__main__':
    main(r"C:\Users\<USER>\Desktop\20231106work\20230918label\119.txt")