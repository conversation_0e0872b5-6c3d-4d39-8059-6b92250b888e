
### 概述
1. 文件目录说明：
   1. main.py：主功能函数，包含1个全部算法接口的类：QRS_rotate
   2. utils文件夹：算法背后的全部功能函数，是一个package，实现具体功能的所有代码都存放在里面；
   3. mcg文件夹：存放心磁文件

2. 运行环境:
   conda install -y numpy=1.26.4 scipy=1.10.0 pandas=2.2.3 scikit-learn=1.6.0 scikit-image=0.19.3 matplotlib=3.7.0 shapely=2.0.7 pillow=9.4.0 openpyxl=3.1.5 joblib=1.1.1

3. 输入：原始心磁数据
   输出：ST_Diagnosis，elevation_channels(抬高的通道)，depression_channels(压低的通道)，ST_Elevation_Area，ST_Depression_Area
   例：输入：  AHYK_2023_000046.txt
              13个时刻点：Ph, Pp, Pr, Qh, Qp, Rp, Sp, Sr, <PERSON>h, <PERSON>, Tp, <PERSON>, Tr = 148, 178, 208, 314, 326, 346, 366, 400, 475, 475, 558, 620, 665
       输出：2 [20, 21, 26] [29, 30, 35, 36] ['下方区域', '右边区域', '中间区域'] ['下方区域', '左边区域', '中间区域']

4. 描述：
    1. 极值比正常和不正常都会进行下一轮判断，但是分级阈值不一样
    2. 基线为  baseline = cal_iqr0(cal_iqr0(Tr + Ph) + Pr)，最小不小于-0.15，最大不超过0.15
    3. 如果检测到通道（13，14，19，20，21，25，26，27，28，31，32，33，34，35，36）有T波倒置或双向，
    那么有T波倒置的通道单独进行判断：J点到T波峰之间的数据如果有80%超过阈值x，则认为抬高；如果有80%低于阈值x，则认为压低
    4. 保持原先的3个相邻通道异常就算异常的逻辑。同时增加新的逻辑：如果有2个相邻的通道异常，并且非对角线相邻而是上下左右直接相邻，那么也算作异常。

5. 代码运行时间：0.5420 秒





