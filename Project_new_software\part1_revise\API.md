
## 电流源轨迹可视化

### 概述

本程序主要是还原QRS段的电流源在心脏模型上的轨迹。红色段是QR段，绿色段是RS段，Q、R、S三个点使用白色小圆点标出。并在轨迹上添加了箭头表示电源的移动方向。


### 主函数输入及输出

直接调用 main_part1.py文件中的函数 main_part1(mcg_path, heart_path, time_loc)即可
输入：分别为mcg_path, heart_path, time_loc
其中 mcg_path是心磁路径, 文件后缀为'.txt'文件
heart_path为心脏模型所在的文件夹，比如本part1路径下的'model'
time_loc是时刻点的向量，time_loc=np.array([Qp, Rp, Sp]), 分别为Q峰，R峰，S峰

输出： (1) 心脏需要移动的位置，这是一个1*3的向量，分别代表x,y,z需要移动的位置
      (2)    向量 N * 4 的向量，记录了Q波峰到S波峰期间，点源的时刻点和坐标。第一列是时刻点，第二至四列是点的xyz坐标。

运行时间为2.470701s(包括文件的读取和结果的生成)


### 例子

```
例1
    mcg_path = r'data\\PLAG_2024_000059.txt'
    heart_path = 'model'
    time_loc = np.array([311, 335, 361])
    result = main_part1(mcg_path, heart_path, time_loc)

输出为
运行时间 2.470701s
[22.7284726   5.17198486  7.88]
[[3.11000000e+02 1.30537619e-01 9.13949772e-02 1.12067032e-01]
 [3.12000000e+02 1.29119530e-01 8.56731140e-02 1.15841033e-01]
 [3.13000000e+02 1.29631526e-01 8.17394570e-02 1.16817395e-01]
 [3.14000000e+02 1.31614235e-01 7.77477509e-02 1.20092285e-01]
 [3.15000000e+02 1.29924043e-01 6.90212348e-02 1.19805768e-01]
 [3.16000000e+02 1.33827369e-01 6.41815936e-02 1.17466765e-01]
 [3.17000000e+02 1.36733393e-01 5.93553363e-02 1.18248279e-01]
 [3.18000000e+02 1.51113750e-01 5.32380412e-02 9.05124023e-02]
 [3.19000000e+02 1.58473345e-01 5.48280156e-02 8.91546958e-02]
 [3.20000000e+02 1.63382440e-01 6.29293384e-02 9.07785843e-02]
 [3.21000000e+02 1.65623719e-01 7.17389743e-02 9.01728942e-02]
 [3.22000000e+02 1.65623719e-01 7.17389743e-02 9.01728942e-02]
 [3.23000000e+02 1.65623719e-01 7.17389743e-02 9.01728942e-02]
 [3.24000000e+02 1.65623719e-01 7.17389743e-02 9.01728942e-02]
 [3.25000000e+02 1.65623719e-01 7.17389743e-02 9.01728942e-02]
 [3.26000000e+02 1.65623719e-01 7.17389743e-02 9.01728942e-02]
 [3.27000000e+02 1.61008283e-01 8.28751386e-02 7.85886270e-02]
 [3.28000000e+02 1.61008283e-01 8.28751386e-02 7.85886270e-02]
 [3.29000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.30000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.31000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.32000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.33000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.34000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.35000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.36000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.37000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.38000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.39000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.40000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.41000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.42000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.43000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.44000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.45000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.46000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.47000000e+02 1.51864736e-01 9.75298719e-02 8.37795013e-02]
 [3.48000000e+02 1.51833279e-01 9.73677984e-02 8.92280203e-02]
 [3.49000000e+02 1.51549393e-01 9.69253733e-02 9.93898579e-02]
 [3.50000000e+02 1.10437704e-01 5.66116890e-02 8.74860674e-02]
 [3.51000000e+02 1.16332983e-01 5.67233002e-02 9.94823996e-02]
 [3.52000000e+02 1.22189407e-01 5.63957805e-02 1.10740704e-01]
 [3.53000000e+02 1.22189407e-01 5.63957805e-02 1.10740704e-01]
 [3.54000000e+02 1.22189407e-01 5.63957805e-02 1.10740704e-01]
 [3.55000000e+02 1.22189407e-01 5.63957805e-02 1.10740704e-01]
 [3.56000000e+02 1.22189407e-01 5.63957805e-02 1.10740704e-01]
 [3.57000000e+02 1.28040243e-01 5.63571034e-02 1.16549800e-01]
 [3.58000000e+02 1.28040243e-01 5.63571034e-02 1.16549800e-01]
 [3.59000000e+02 1.28040243e-01 5.63571034e-02 1.16549800e-01]
 [3.60000000e+02 1.28040243e-01 5.63571034e-02 1.16549800e-01]
 [3.61000000e+02 1.28040243e-01 5.63571034e-02 1.16549800e-01]]

