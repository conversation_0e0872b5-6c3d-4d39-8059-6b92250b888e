"""
@ Author: <PERSON> Xu
@ E-mail: <EMAIL>
@ Date: 2025/8/5 16:57
"""
import pickle
from utils.get_metrics import *
from utils.QRS_tool import QRS_stable_Analyzer, QRSMorphologyAnalyzer


class QRS_rotate:
    def __init__(self):
        # ============================= 阈值定义 ==============================
        # 负磁极位移相关阈值
        self.MIN_NEGATIVE_POLE_DISPLACEMENT = 20  # 负磁极最小位移阈值（像素）

        # QR段转动异常检测阈值
        self.MIN_REVERSAL_ANGLE = 30  # 折返转动中 逆时针总转动有效角度阈值
        self.MIN_CLOCKWISE_ANGLE = 22  # 折返转动中 顺时针总转动有效角度阈值
        self.MAX_FRAME_JUMP_ANGLE = 90  # 前后帧跳转角度阈值
        self.MIN_QR_TOTAL_ANGLE = 50  # QR段最小总转动角度阈值

        # RS段转动阈值
        self.MIN_RS_ROTATION_ANGLE = 50  # RS段最小转动角度

        # R时刻相关阈值
        self.R_QUADRANT_MIN = 20  # R波所处正常区域最小角度
        self.R_QUADRANT_MAX = 100  # R波所处正常区域最大角度
        self.S_QUADRANT_MAX_1 = 90  # S波所处正常区域最大角度(三峰分明)
        self.S_QUADRANT_MIN_2 = 135  # S波所处异常区域最小角度(非三峰分明)
        self.S_QUADRANT_MAX_3 = 315  # S波所处异常区域最大角度(非三峰分明)
        self.Q_QUADRANT_MIN = 225  # Q波所处正常区域最小角度
        self.Q_QUADRANT_MAX = 340  # Q波所处正常区域最大角度
        self.NEAR_RP_THRESHOLD = 3  # 临近Rp时刻的阈值（时刻点数）

        # 通用阈值
        self.MIN_SIGNIFICANT_ANGLE_DIFF = 1  # 最小有效角度变化

    # ===================================================== 计算工具 ====================================================
    def get_dipole(self, matrix):
        '''计算偶极子坐标'''
        px, py = np.unravel_index(np.argmax(matrix), matrix.shape)
        nx, ny = np.unravel_index(np.argmin(matrix), matrix.shape)
        return px, py, nx, ny

    def calculate_angle(self, px, py, nx, ny):
        '''计算正磁极指向负磁极的角度'''
        dx = ny - py
        dy = -(nx - px)
        # 计算角度，返回值范围[-π, π]，转换为[0, 2π]
        angle = np.arctan2(dy, dx)
        if angle < 0:
            angle += 2 * np.pi
        return np.degrees(angle)

    def normalize_angle_diff(self, angle_diff):
        '''标准化角度差，处理角度跨越问题'''
        if angle_diff > 180:
            angle_diff -= 360
        elif angle_diff < -180:
            angle_diff += 360
        return angle_diff

    def detect_first_quadrant_timing(self, angles, qp_time):
        '''检测何时转入安全区，返回转入时刻的索引'''
        for i, angle in enumerate(angles):
            if self.R_QUADRANT_MIN <= angle <= self.R_QUADRANT_MAX:
                return i + qp_time  # 返回绝对时刻
        return None  # 从未转入第一象限

    def calculate_negative_pole_displacement(self, segment):
        '''计算负磁极相对于起始位置的最大偏离距离'''
        if len(segment) < 2:
            return 0
        _, _, start_nx, start_ny = self.get_dipole(segment[0])  # 获取起始位置的负磁极坐标
        max_deviation = 0
        for matrix in segment:
            _, _, nx, ny = self.get_dipole(matrix)
            deviation = np.sqrt((nx - start_nx) ** 2 + (ny - start_ny) ** 2)
            max_deviation = max(max_deviation, deviation)

        return max_deviation

    def detect_reversal_rotation(self, angle_diffs):
        '''检测折返转动和跳转模式：
        - 折返转动：先逆时针 → 顺时针
        - 跳转：先逆时针 → 顺时针 → 又逆时针

        返回: (is_reversal, is_jump, info)
        '''
        if len(angle_diffs) < 3:
            return False, False, None

        ccw_total = 0  # 逆时针累积角度
        cw_total = 0  # 顺时针累积角度
        ccw_after_cw = 0  # 在顺时针转动后的逆时针累积角度

        has_significant_ccw = False  # 是否有显著的初始逆时针转动
        has_significant_cw = False  # 是否有显著的顺时针转动

        for diff in angle_diffs:
            if abs(diff) < self.MIN_SIGNIFICANT_ANGLE_DIFF:
                continue

            if diff > 0:  # 逆时针
                if not has_significant_cw:  # 如果还没有显著顺时针转动
                    ccw_total += diff
                    if ccw_total >= self.MIN_REVERSAL_ANGLE:
                        has_significant_ccw = True
                else:  # 如果已经有了显著顺时针转动，这是后续的逆时针
                    ccw_after_cw += diff

            elif diff < 0 and has_significant_ccw:  # 在有显著逆时针转动后的顺时针
                cw_total += abs(diff)
                if cw_total >= self.MIN_CLOCKWISE_ANGLE:
                    has_significant_cw = True

        # 判断是否为跳转：在有显著顺时针转动后，又有显著逆时针转动
        is_jump = (has_significant_ccw and has_significant_cw and
                   ccw_after_cw >= self.MIN_REVERSAL_ANGLE)
        if is_jump:
            return False, True, f"先逆时针{ccw_total:.1f}°→顺时针{cw_total:.1f}°→又逆时针{ccw_after_cw:.1f}°"

        # 如果先有显著逆时针转动，后有显著顺时针转动，且没有后续显著逆时针，则认为是折返
        if has_significant_ccw and has_significant_cw:
            return True, False, f"先逆时针{ccw_total:.1f}°后顺时针{cw_total:.1f}°"

        return False, False, None

    def find_valley_position(self, signals, start_time, end_time, wave):
        '''找到Q波峰和R波峰之间的真正波谷位置 - 基于包络线方法'''
        morphology_analyzer = QRSMorphologyAnalyzer()
        # 提取包络线并平滑
        region_signals = [sig[start_time:end_time + 1] for sig in signals]
        region_length = end_time - start_time + 1
        lower_env, _ = morphology_analyzer.extract_envelope(region_signals, wave)
        smooth_lower = morphology_analyzer.smooth_envelope(lower_env, window_length=min(7, region_length // 2 * 2 + 1))

        # 在Q波峰后1个时刻到R波峰前1个时刻之间寻找最接近基线的点
        search_start = max(1, 1)  # Q波峰后至少1个时刻
        search_end = min(len(smooth_lower) - 1, len(smooth_lower) - 1)  # R波峰前至少1个时刻

        # 在下包络线中找最接近基线(0)的点
        min_abs_value = float('inf')
        valley_index = None
        for i in range(search_start, search_end):
            abs_value = abs(smooth_lower[i])  # 距离基线的绝对距离
            if abs_value < min_abs_value:
                left_check = (i == search_start) or (abs(smooth_lower[i - 1]) >= abs_value)
                right_check = (i == search_end - 1) or (abs(smooth_lower[i + 1]) >= abs_value)
                if left_check and right_check:
                    min_abs_value = abs_value
                    valley_index = i

        if valley_index is None:  # 如果没找到明显的波谷
            return None
        return start_time + valley_index

    def is_jump_at_q_valley(self, jump_index, qp_time, rp_time, signals):
        '''判断跳转是否发生在Q波谷位置'''
        actual_time = qp_time + jump_index + 1

        # 分别计算上包络线和下包络线的波谷时刻点
        upper_valley_time = self.find_valley_position(signals, qp_time, rp_time, "upper")
        lower_valley_time = self.find_valley_position(signals, qp_time, rp_time, "lower")

        # 检查是否在上包络线波谷附近
        upper_valley_match = False
        if upper_valley_time is not None:
            upper_valley_start = max(upper_valley_time - 2, qp_time)
            upper_valley_end = min(upper_valley_time + 2, rp_time - 1)  # 不能超过R波峰前
            upper_valley_match = upper_valley_start <= actual_time <= upper_valley_end

        # 检查是否在下包络线波谷附近
        lower_valley_match = False
        if lower_valley_time is not None:
            lower_valley_start = max(lower_valley_time - 3, qp_time)
            lower_valley_end = min(lower_valley_time + 3, rp_time - 1)  # 不能超过R波峰前
            lower_valley_match = lower_valley_start <= actual_time <= lower_valley_end

        # 只要满足其中一个条件即可
        return upper_valley_match or lower_valley_match

    def detect_frame_jump(self, angle_diffs, wave_state, qp_time, rp_time, signals):
        '''检测前后帧跳转：前后帧跳转大于等于90度'''
        for i, diff in enumerate(angle_diffs):
            if abs(diff) >= self.MAX_FRAME_JUMP_ANGLE:
                valley = self.is_jump_at_q_valley(i, qp_time, rp_time, signals)  # 如果是三峰分明且跳转发生在Q波谷位置，则不算异常
                if wave_state == "三峰分明" and valley:
                    continue  # 跳过这个跳转，不认为是异常
                else:
                    return True, f"第{i + 1}帧跳转{abs(diff):.1f}°"
        return False, None

    def detect_clockwise_rotation(self, angle_diffs):
        '''检测顺时针转动：从Q开始就是顺时针转，且转动了一定程度'''
        clockwise_total = 0
        consecutive_ccw_count = 0  # 连续逆时针帧数
        consecutive_ccw_total = 0  # 连续逆时针累积角度
        for diff in angle_diffs:
            if abs(diff) < self.MIN_SIGNIFICANT_ANGLE_DIFF:
                continue
            if diff < 0:  # 顺时针
                clockwise_total += abs(diff)
                # 重置逆时针计数
                consecutive_ccw_count = 0
                consecutive_ccw_total = 0
            else:  # 逆时针且大于 MIN_SIGNIFICANT_ANGLE_DIFF
                consecutive_ccw_count += 1
                consecutive_ccw_total += diff
                # 检查是否满足停止条件：连续3帧且累积角度>10°
                if consecutive_ccw_count >= 3 and consecutive_ccw_total > 10:
                    break

        if clockwise_total >= self.MIN_CLOCKWISE_ANGLE:
            return True, f"顺时针转动{clockwise_total:.1f}°"

        return False, None

    def calculate_total_rotation_angle(self, angles):
        '''计算总转动角度：从起始角度到过程中任意时刻的最大角度差'''
        if len(angles) < 2:
            return 0

        start_angle = angles[0]
        max_rotation = 0

        for angle in angles[1:]:
            # 计算从起始角度到当前角度的角度差
            angle_diff = angle - start_angle
            # 处理角度跨越问题
            angle_diff = self.normalize_angle_diff(angle_diff)
            # 取绝对值，记录最大转动幅度
            max_rotation = max(max_rotation, abs(angle_diff))

        return max_rotation

    def recalculate_qp_time(self, signals, original_qp_time, rp_time):
        '''
        重新计算Qp时刻点。当wave_state不是"三峰分明"且q_status不是"normal"时调用
        '''
        # 1. 计算所有通道在原Qp到Rp之间的最大正值和最小负值
        max_positive = float('-inf')
        min_negative = float('inf')
        for channel_signal in signals:
            segment = channel_signal[original_qp_time:rp_time + 1]
            channel_max = np.max(segment)
            channel_min = np.min(segment)
            if channel_max > max_positive:
                max_positive = channel_max
            if channel_min < min_negative:
                min_negative = channel_min

        # 2. 计算阈值：(最大正值 + |最小负值|) / 15
        amplitude_sum = max_positive + abs(min_negative)
        threshold = round(amplitude_sum / 15, 2)

        # 3. 提取原Qp到Rp之间的信号段用于包络线计算
        morphology_analyzer = QRSMorphologyAnalyzer()
        region_signals = [sig[original_qp_time:rp_time + 1] for sig in signals]
        lower_env, upper_env = morphology_analyzer.extract_envelope(region_signals, "both")

        # 4. 在上包络线中寻找达到阈值的第一个时刻点
        upper_threshold_time = None
        for i in range(len(upper_env)):
            if upper_env[i] >= threshold:
                upper_threshold_time = original_qp_time + i
                break

        # 5. 在下包络线中寻找达到阈值的第一个时刻点（取绝对值）
        lower_threshold_time = None
        for i in range(len(lower_env)):
            if abs(lower_env[i]) >= threshold:
                lower_threshold_time = original_qp_time + i
                break

        # 6. 取时刻点更小的那个作为新的Qp时刻点
        candidate_times = []
        if upper_threshold_time is not None:
            candidate_times.append(upper_threshold_time)
        if lower_threshold_time is not None:
            candidate_times.append(lower_threshold_time)
        if candidate_times:
            new_qp_time = min(candidate_times)
            return new_qp_time
        else:
            return original_qp_time

    def _calculate_segment_angles(self, segment):
        '''计算某个段的角度序列和角度差序列'''
        angles = []
        for matrix in segment:
            px, py, nx, ny = self.get_dipole(matrix)
            angle = self.calculate_angle(px, py, nx, ny)
            angles.append(angle)

        angle_diffs = []
        for i in range(1, len(angles)):
            diff = angles[i] - angles[i - 1]
            diff = self.normalize_angle_diff(diff)
            angle_diffs.append(diff)

        return angles, angle_diffs

    def _analyze_qr_segment(self, qr_segment, angles, angle_diffs, qp_time, rp_time, signals, wave_state):
        '''分析QR段的转动情况'''
        anomalies = []
        special_anomalies = []  # 用于存储特殊的异常类型

        # a.检查Q起始角度
        qp_angle = angles[0]
        if not (self.Q_QUADRANT_MIN <= qp_angle <= self.Q_QUADRANT_MAX):
            anomalies.append("Q起始角度异常")

        # b.检查负磁极位移
        negative_pole_displacement = self.calculate_negative_pole_displacement(qr_segment)
        if negative_pole_displacement < self.MIN_NEGATIVE_POLE_DISPLACEMENT:
            anomalies.append("负磁极不转")

        # c. 检测QR总转动角度是否不足
        total_angle = self.calculate_total_rotation_angle(angles)
        if total_angle < self.MIN_QR_TOTAL_ANGLE:
            anomalies.append("QR转动不足")

        # d. 检测折返转动
        is_reversal, is_jump, rotation_info = self.detect_reversal_rotation(angle_diffs)
        if is_reversal:
            special_anomalies.append("折返转动")
        elif is_jump:
            special_anomalies.append("角度跳转")

        # e. 检测前后帧跳转
        is_jump, jump_info = self.detect_frame_jump(angle_diffs, wave_state, qp_time, rp_time, signals)
        if is_jump:
            special_anomalies.append("前后帧跳转")

        # f. 检测顺时针转动
        is_clockwise, clockwise_info = self.detect_clockwise_rotation(angle_diffs)
        if is_clockwise:
            anomalies.append("顺时针转动")

        # g. 检测R时刻是否在规定区域
        rp_angle = angles[-1]  # Rp时刻的角度
        if not (self.R_QUADRANT_MIN <= rp_angle <= self.R_QUADRANT_MAX):
            special_anomalies.append("R时刻方向异常")

        # h. 检测是否临近Rp时刻才转到第一象限
        first_quadrant_time = self.detect_first_quadrant_timing(angles, qp_time)
        if first_quadrant_time is not None:
            time_before_rp = rp_time - first_quadrant_time
            if time_before_rp <= self.NEAR_RP_THRESHOLD:
                special_anomalies.append("临近R时刻才转到位")

        # 如果所有异常都是特殊异常，则视为正常
        if anomalies or special_anomalies:
            if not anomalies and special_anomalies:
                return {
                    "status": "正常",
                    "reason": "QR段转动正常"
                }
            else:
                all_anomalies = anomalies + special_anomalies
                combined_reason = " & ".join(all_anomalies)
                return {
                    "status": "异常",
                    "reason": combined_reason
                }

        # 3. 正常情况
        return {
            "status": "正常",
            "reason": "QR段转动正常"
        }

    def _analyze_rs_segment(self, angles, wave_state):
        '''分析RS段的转动情况'''
        # a. 计算RS段总转动角度  检查是否有转动
        total_rotation = self.calculate_total_rotation_angle(angles)
        if total_rotation < self.MIN_RS_ROTATION_ANGLE:
            return {
                "status": "异常",
                "reason": "RS段转动不足"
            }

        # b. 检查Sp时刻角度是否符合要求
        sp_angle = angles[-1]
        if wave_state == "三峰分明":  # 三峰分明时：需要转出第一象限（角度 > 90°）
            if sp_angle <= self.S_QUADRANT_MAX_1:
                return {
                    "status": "异常",
                    "reason": "Sp时刻未转出第一象限"
                }
        else:  # 非三峰分明时：角度不能在[0-135°, 315°-360°]范围内
            if not (self.S_QUADRANT_MIN_2 <= sp_angle <= self.S_QUADRANT_MAX_3):
                return {
                    "status": "异常",
                    "reason": rf"Sp时刻角度在禁止区域[{self.S_QUADRANT_MIN_2},{self.S_QUADRANT_MAX_3}°]"
                }

        return {
            "status": "正常",
            "reason": "RS段转动正常"
        }

    def QRS_rotate(self, Ms, tm0, ls36, wave_state, q_status):
        '''分析QRS段角度转动情况，包括QR段和RS段'''
        try:
            qp_time, rp_time, sp_time = int(tm0[4]), int(tm0[5]), int(tm0[6])
            original_qp_time = qp_time
            if q_status != "normal":
                qp_time = self.recalculate_qp_time(ls36, original_qp_time, rp_time)

            qr_segment = Ms[qp_time:rp_time + 1]
            rs_segment = Ms[rp_time + 1:sp_time + 1]

            # ========================================== QR段分析 =====================================================
            qr_angles, qr_angle_diffs = self._calculate_segment_angles(qr_segment)
            qr_result = self._analyze_qr_segment(qr_segment, qr_angles, qr_angle_diffs, qp_time, rp_time, ls36, wave_state)

            # =========================================== RS段分析 ====================================================
            rs_angles, rs_angle_diffs = self._calculate_segment_angles(rs_segment)
            rs_result = self._analyze_rs_segment(rs_angles, wave_state)

            return qr_result, rs_result

        except Exception as e:
            return {"status": "错误", "reason": f"处理过程中出现错误: {str(e)}"}


if __name__ == "__main__":
    mcg_file = 'mcg/AHYK_2023_000046.txt'               # 原始心磁数据
    with open('mcg\AHYK_2023_000046.pkl', 'rb') as f:
        result = pickle.load(f)                         # 插值后数据
    bz_inter_data_all = np.stack(result, axis=2)
    ts = [148, 178, 208, 314, 326, 346, 366, 400, 475, 475, 558, 620, 665]  # 13个时刻点

    # =========================================判断是否三峰分明==========================================
    start_time = time.time()
    analyzer_wave = QRS_stable_Analyzer()
    ls36, q_status, s_status = analyzer_wave.analyze_qrs_with_channel_check(mcg_file, ts)
    QRS_WaveStatus = "三峰分明" if q_status == "normal" and s_status == "normal" else f"{q_status},{s_status}"

    # =========================================计算QR、RS转动============================================
    analyzer_angle = QRS_rotate()
    Ms = [bz_inter_data_all[:, :, i] for i in range(bz_inter_data_all.shape[2])]
    qr_result, rs_result = analyzer_angle.QRS_rotate(Ms, ts, ls36, QRS_WaveStatus, q_status)
    QR_Rotation, QR_Reason, RS_Rotation, RS_Reason = qr_result["status"], qr_result["reason"], rs_result["status"], rs_result["reason"]
    end_time = time.time()
    execution_time = end_time - start_time
    print(QRS_WaveStatus, QR_Rotation, QR_Reason, RS_Rotation, RS_Reason)
    print(f"代码运行时间：{execution_time:.4f} 秒")