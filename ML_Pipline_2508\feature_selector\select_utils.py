"""
@Project ：ML_Pipline 
@File    ：select_utils.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/8/1 上午8:49 
@Discribe：
"""

import pickle
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import tqdm
import xgboost as XGB
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import RFE, SelectKBest, chi2, RFECV
from sklearn.feature_selection import SelectFromModel
from sklearn.linear_model import LassoCV, LogisticRegression
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler, MaxAbsScaler, RobustScaler, Normalizer, \
    PowerTransformer, QuantileTransformer, FunctionTransformer
from sklearn.utils import resample
from multiprocessing import Pool
from boruta import BorutaPy
from sklearn.linear_model import ElasticNetCV


plt.rcParams["axes.unicode_minus"] = False  # 解决图像中的"-"负号的乱码问题
# plt.rcParams['font.family'] = 'AR PL UMing CN'
plt.rcParams['font.family'] = 'AR PL UMing CN'
plt.rcParams['font.family'] = 'SimHei'
# plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体


# 删除部分特征
def preprocess_data(data, label_col, columns_to_drop):
    X = data.drop(columns=columns_to_drop + [label_col], errors='ignore')
    y = data[label_col].astype(int)
    return X, y


def process_iteration(i, X_train, y_train, model):
    X_resampled, y_resampled = resample(X_train, y_train, random_state=i)
    selector = SelectFromModel(estimator=model).fit(X_resampled, y_resampled)
    return selector.get_support()


def stability_selection(train_data, valid_data, label_col, final_n_features, model_mode='xgb',
                        n_iterations=100, selection_threshold=0.5, n_jobs=30):
    """
    稳定性选择算法（Stability Selection）
    :param train_data: 训练数据集
    :param valid_data: 验证数据集
    :param label_col: 标签列名称
    :param final_n_features: 最终选择的特征数量
    :param model_mode: 模型类型，'logistic' 或 'random_forest'
    :param n_iterations: 重采样次数
    :param selection_threshold: 特征选择频率阈值
    :param n_jobs: 并行计算的工作进程数，-1表示使用所有可用的CPU
    :return: 选择的特征列表和结果字典
    """

    columns_to_drop = ['mcg_file', 'filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x', 'label_y']

    # 预处理训练和验证数据
    X_train, y_train = preprocess_data(train_data, label_col, columns_to_drop)
    X_valid, y_valid = preprocess_data(valid_data, label_col, columns_to_drop)

    # 根据model_mode选择模型
    if model_mode == 'logistic':
        model = LogisticRegression(penalty='l1', solver='saga', max_iter=10000)
    elif model_mode == 'random_forest':
        model = RandomForestClassifier(n_estimators=150, random_state=42)
    elif model_mode == 'xgb':
        model = XGB.XGBClassifier(n_estimators=100)
    else:
        raise ValueError("Unsupported model_mode. Choose 'logistic', 'random_forest', or 'xgb'.")

    # 初始化选择特征的计数器
    selected_features_count = np.zeros(X_train.shape[1])

    # for i in tqdm.tqdm(range(n_iterations)):
    #     # 重采样训练数据
    #     X_resampled, y_resampled = resample(X_train, y_train, random_state=i)
    #     selector = SelectFromModel(estimator=model).fit(X_resampled, y_resampled)
    #     # 记录每次选择的特征
    #     selected_features_count += selector.get_support()

    # 使用 multiprocessing.Pool 来并行处理
    with Pool(processes=n_jobs) as pool:
        results = pool.starmap(process_iteration,
                               [(i, X_train, y_train, model) for i in range(n_iterations)])

    # 累加每次迭代返回的特征选择结果
    for support in results:
        selected_features_count += support
    # selected_features_count = parallel_feature_selection(n_iterations, X_train, y_train, model)

    # 计算特征选择的频率
    selected_features_freq = selected_features_count / n_iterations
    selected_features = X_train.columns[selected_features_freq >= selection_threshold]

    # 如果选择的特征数大于目标特征数，按频率降序选择前final_n_features个特征
    if len(selected_features) > final_n_features:
        selected_features = selected_features[
            np.argsort(-selected_features_freq[selected_features_freq >= selection_threshold])[:final_n_features]
        ]
    # 更新X_train和X_valid以只包含选定的特征
    X_train_selected = X_train[selected_features]
    X_valid_selected = X_valid[selected_features]

    # 训练最终模型并评估
    final_model = model.fit(X_train_selected, y_train)
    train_accuracy = accuracy_score(y_train, final_model.predict(X_train_selected))
    valid_accuracy = accuracy_score(y_valid, final_model.predict(X_valid_selected))

    results = {
        'train_accuracy': train_accuracy,
        'valid_accuracy': valid_accuracy,
        'selected_features_freq': selected_features_freq
    }
    # print(f"Stability Selection: {results}")
    return selected_features.tolist(), results


def rfe_feature_selection(train_data, valid_data, label_col, final_n_features):
    """
    RFE特征选择
    :param train_data: 训练数据集
    :param valid_data: 验证数据集
    :param label_col: 标签列名称
    :param final_n_features: 最终选择的特征数量
    :return: 选择的特征列表
    """

    def preprocess_data(data, label_col, columns_to_drop):
        X = data.drop(columns=columns_to_drop + [label_col], errors='ignore')
        y = data[label_col].astype(int)
        return X, y

    columns_to_drop = ['mcg_file', 'filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x', 'label_y']

    # 预处理训练和验证数据
    X_train, y_train = preprocess_data(train_data, label_col, columns_to_drop)
    X_valid, y_valid = preprocess_data(valid_data, label_col, columns_to_drop)

    X_train = X_train.iloc[:, :600]
    X_valid = X_valid.iloc[:, :600]

    # 初始化特征数量
    current_n_features = X_train.shape[1]

    # 设定步骤和目标特征数量
    steps = [(20000, 10000), (10000, 5000), (5000, 2000), (2000, 500), (1000, 200), (500, 100), (200, 20),
             (final_n_features, 1)]

    # 保存每步的特征数量和相应的模型精度
    results = []
    for target_features, step in steps:
        while current_n_features > target_features:
            current_step = min(current_n_features - target_features, step)
            # print("current_n_features:",current_n_features,"target_features:",
            #       target_features,"step:",step,"current_step:",current_step,
            #       "min_features_to_select",step)
            estimator_params = {"tree_method": "hist", } if current_n_features <= 30000 else {}

            # 常规RFE
            rfe_selector = RFE(estimator=XGB.XGBClassifier(**estimator_params),
                               n_features_to_select=max(target_features, current_n_features - current_step),
                               step=current_step, verbose=9999)

            # 训练RFE选择器
            rfe_selector.fit(X_train, y_train)

            # 获取被选中的特征名称
            selected_features = X_train.columns[rfe_selector.support_]
            print(len(selected_features))
            # 更新X_train和X_valid以只包含选定的特征
            X_train = X_train[selected_features]
            X_valid = X_valid[selected_features]
            current_n_features = X_train.shape[1]

            # # 用更新后的特征集重新训练模型并评估模型精度
            # model = XGB.XGBClassifier(**estimator_params)
            # model.fit(X_train, y_train)
            #
            # train_accuracy = accuracy_score(y_train, model.predict(X_train))
            # valid_accuracy = accuracy_score(y_valid, model.predict(X_valid))
            #
            # # 记录当前特征数量和相应的精度
            # results.append((current_n_features, train_accuracy, valid_accuracy, selected_features))
            #
            # print(f"Step completed: Target = {target_features}, Current = {current_n_features}, "
            #       f"Train Accuracy = {train_accuracy}, Valid Accuracy = {valid_accuracy}")

    return selected_features.tolist(), results


def rfecv_feature_selection(train_data, valid_data, label_col, final_n_features):
    """
    RFE特征选择
    :param train_data: 训练数据集
    :param valid_data: 验证数据集
    :param label_col: 标签列名称
    :param final_n_features: 最终选择的特征数量
    :return: 选择的特征列表
    """

    def preprocess_data(data, label_col, columns_to_drop):
        X = data.drop(columns=columns_to_drop + [label_col], errors='ignore')
        y = data[label_col].astype(int)
        return X, y

    columns_to_drop = ['mcg_file', 'filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x', 'label_y']

    # 预处理训练和验证数据
    X_train, y_train = preprocess_data(train_data, label_col, columns_to_drop)
    X_valid, y_valid = preprocess_data(valid_data, label_col, columns_to_drop)

    X_train = X_train.iloc[:, :]
    X_valid = X_valid.iloc[:, :]

    # 初始化特征数量
    current_n_features = X_train.shape[1]
    for step in [500, 100, 50]:
        # 保存每步的特征数量和相应的模型精度
        results = []
        estimator_params = {"tree_method": "hist"} if current_n_features <= 30000 else {}
        # RFECV特征选择
        rfe_selector = RFECV(
            estimator=XGB.XGBClassifier(**estimator_params),
            step=step,
            min_features_to_select=final_n_features,
            scoring='accuracy',  # 可以根据需要更改评分标准
            verbose=9999,
            n_jobs=5
        )

        # 训练RFE选择器
        rfe_selector.fit(X_train, y_train)

        # 获取被选中的特征名称
        selected_features = X_train.columns[rfe_selector.support_]
        # 打印最优特征数量和所选特征位置
        print("Optimal number of features : %d" % rfe_selector.n_features_)

        # 更新X_train和X_valid以只包含选定的特征
        X_train = X_train[selected_features]
        X_valid = X_valid[selected_features]

        # 用更新后的特征集重新训练模型并评估模型精度
        model = XGB.XGBClassifier(**estimator_params)
        model.fit(X_train, y_train)

        train_accuracy = accuracy_score(y_train, model.predict(X_train))
        valid_accuracy = accuracy_score(y_valid, model.predict(X_valid))

        # 记录当前特征数量和相应的精度
        results.append((current_n_features, train_accuracy, valid_accuracy, selected_features))

        print(f"Step completed: Target = {len(selected_features)}, Current = {current_n_features}, "
              f"Train Accuracy = {train_accuracy}, Valid Accuracy = {valid_accuracy}")

    return selected_features.tolist(), results


def chi2_feature_selection(df, label_col, final_n_features=100):
    df = df.iloc[:, 1:]
    X = df.drop(label_col, axis=1)
    try:
        X = df.drop(['mcg_file'], axis=1)
        X = X.drop(['filename_1', 'filename_2', 'filename_3', 'filename_4'], axis=1)
    except KeyError:
        pass
    X = X.fillna(10000)
    X += 1000

    y = df[label_col]
    chi2_selector = SelectKBest(chi2, k=final_n_features)
    chi2_selector.fit(X, y)
    selected_features = X.columns[chi2_selector.get_support()]
    return selected_features


def lasso_feature_selection(df, label_col, final_n_features=100):
    # Prepare the data
    df = df.iloc[:, 1:]
    X = df.drop(label_col, axis=1)
    X = X.drop(['filename_1', 'filename_2', 'filename_3', 'filename_4'], axis=1)
    X = X.fillna(0)
    y = df[label_col]

    # Apply Lasso feature selection
    lasso = LassoCV().fit(X, y)
    importance = np.abs(lasso.coef_)
    idx_third = importance.argsort()[-final_n_features]
    threshold = importance[idx_third] + 0.01
    sfm = SelectFromModel(lasso, threshold=threshold).fit(X, y)
    selected_features = X.columns[sfm.get_support()]

    return selected_features


# 将 compute_correlation 函数定义为全局函数
def compute_correlation(df, feature, label_col):
    """

    :param df:
    :param feature:
    :param label_col:
    :return:
    """
    # 确保没有 NaN 值
    feature_values = df[feature].values
    label_values = df[label_col].values
    return np.corrcoef(feature_values, label_values)[0, 1]


def pearson_correlation_selection(df, label_col, final_n_features=100):
    """
    :param df:
    :param label_col:
    :param final_n_features:
    :return:
    """
    # 准备数据：删除标签列和 'mcg_file' 列，只保留数值类型的列
    X = df.drop(columns=[label_col, 'mcg_file'], errors='ignore')
    X = X.select_dtypes(include=['int', 'float'])
    X = X.select_dtypes(include=['int', 'float']).fillna(0)
    y = df[label_col]

    # 计算每个特征与标签列的相关系数
    correlation_with_label = X.apply(lambda x: x.corr(y))

    # 对相关系数进行排序，并选择相关性最高的特征
    top_features = correlation_with_label.abs().sort_values(ascending=False).head(final_n_features).index.tolist()

    return top_features


def remove_zero_variance_features(df):
    """
    删除具有 0 方差的特征（即在所有观察中值不变的特征）。

    参数:
    df (pandas.DataFrame): 待处理的 DataFrame。

    返回:
    pandas.DataFrame: 已删除 0 方差特征的 DataFrame。
    """
    variance = df.var()
    variance = df.drop(columns=['label'], errors='ignore').var()
    columns_to_drop = variance[variance == 0].index
    df = df.drop(columns_to_drop, axis=1)
    print(f"Columns with 0 variance: {columns_to_drop}")
    return df


def remove_columns_without_tt(df):
    """

    :param df:
    :return:
    """
    # 使用列表推导式选择包含'tt'的列
    cols_with_tt = [col for col in df.columns if 'tt' in col]
    # 用这些列创建一个新的DataFrame
    df_with_tt = df[cols_with_tt]
    df_with_tt['label'] = df['label']
    return df_with_tt


def get_data(id_pkl, features_pkl, fold=0):
    # 加载数据------------------
    with open(id_pkl, "rb") as f:
        id_df = pickle.load(f)

    with open(features_pkl, "rb") as f:
        features_df = pickle.load(f)

    # 数据格式转换---------------
    # try:
    #     features_df['qr_div_qt'] = features_df['qr_div_qt'].astype('float32')
    #     features_df['rs_div_qt'] = features_df['rs_div_qt'].astype('float32')
    #     features_df['tt_div_qt'] = features_df['tt_div_qt'].astype('float32')
    #     features_df['qrs_div_qt'] = features_df['qrs_div_qt'].astype('float32')
    #     features_df['RT_euclidean_distance'] = features_df['RT_euclidean_distance'].astype('float32')
    #     features_df['RT_cosine_similarity'] = features_df['RT_cosine_similarity'].astype('float32')
    #     features_df['max_min_simlar_distance'] = features_df['max_min_simlar_distance'].astype('float32')
    # finally:
    #     pass
    # columns_to_convert = [col for col in features_df.columns if col not in ['mcg_file', 'label','label_x']]
    # features_df[columns_to_convert] = features_df[columns_to_convert].apply(pd.to_numeric, errors='coerce',
    #                                                                         downcast='float')
    print("数据加载")
    # 读取fold (某折) 数据-----------
    folder_data = id_df[fold]
    train_valid = folder_data['train_valid']
    if 'train' in folder_data:
        train = folder_data['train']
    else:
        train = folder_data['train_valid']
    if 'valid' in folder_data:
        valid = folder_data['valid']
    elif 'val' in folder_data:
        valid = folder_data['val']
    else:
        valid = folder_data['test']
    test = folder_data['test']

    # 获取数据的id(文件名)，并根据id从特征总集中提取相应的 特征-标签
    train_id = train[:, 0].tolist()
    test_id = test[:, 0].tolist()
    valid_id = valid[:, 0].tolist()
    train_valid_id = train_valid[:, 0].tolist()

    train_data = features_df[features_df['mcg_file'].isin(train_id)]
    test_data = features_df[features_df['mcg_file'].isin(test_id)]
    valid_data = features_df[features_df['mcg_file'].isin(valid_id)]
    train_valid_data = features_df[features_df['mcg_file'].isin(train_valid_id)]

    print("标签加载")

    # 将所有数据的 'label' 赋值为 folder_data 中对应的第一列值
    def assign_labels(data, ids, folder_data):
        # todo  确保这里可行  没有warning
        tmp_data = data.copy()
        labels_dict = {row[0]: row[1] for row in folder_data}
        tmp_data.loc[:, 'label'] = tmp_data['mcg_file'].map(labels_dict)
        return tmp_data

    train_data = assign_labels(train_data, train_id, train)
    test_data = assign_labels(test_data, test_id, test)
    valid_data = assign_labels(valid_data, valid_id, valid)
    train_valid_data = assign_labels(train_valid_data, train_valid_id, train_valid)

    return train_data, test_data, valid_data, train_valid_data, features_df


def boruta_feature_selection(train_data, valid_data, label_col, final_n_features, max_iter=100, n_jobs=-1):
    """
    使用BorutaPy库实现的Boruta特征选择算法
    :param train_data: 训练数据集
    :param valid_data: 验证数据集
    :param label_col: 标签列名称
    :param final_n_features: 最终选择的特征数量
    :param max_iter: 最大迭代次数
    :param n_jobs: 并行计算的工作进程数
    :return: 选择的特征列表和结果字典
    """
    columns_to_drop = ['mcg_file', 'filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x', 'label_y']

    # 预处理数据
    X_train, y_train = preprocess_data(train_data, label_col, columns_to_drop)
    X_valid, y_valid = preprocess_data(valid_data, label_col, columns_to_drop)
    X_train, y_train = X_train.fillna(0), y_train.fillna(0)
    X_valid, y_valid = X_valid.fillna(0), y_valid.fillna(0)
    # 初始化随机森林模型
    rf = RandomForestClassifier(n_estimators=100, n_jobs=n_jobs, max_depth=5)

    # 初始化BorutaPy
    boruta_selector = BorutaPy(
        estimator=rf,
        n_estimators='auto',
        max_iter=max_iter,
        verbose=2,
        random_state=42,
        alpha=0.05,
       # perc=95
    )

    # 训练Boruta模型
    boruta_selector.fit(np.array(X_train), np.array(y_train))

    # 获取选定的特征
    selected_features = X_train.columns[boruta_selector.support_].tolist()
    tentative_features = X_train.columns[boruta_selector.support_weak_].tolist()

    # 如果选择的特征数大于目标特征数，按ranking_降序选择前final_n_features个特征
    if len(selected_features) > final_n_features:
        feature_ranking = pd.Series(boruta_selector.ranking_, index=X_train.columns)
        selected_features = feature_ranking[feature_ranking == 1].sort_values().index[:final_n_features].tolist()

    # 更新X_train和X_valid以只包含选定的特征
    X_train_selected = X_train[selected_features]
    X_valid_selected = X_valid[selected_features]

    # 训练最终模型并评估
    final_model = rf.fit(X_train_selected, y_train)
    train_accuracy = accuracy_score(y_train, final_model.predict(X_train_selected))
    valid_accuracy = accuracy_score(y_valid, final_model.predict(X_valid_selected))

    # 计算特征选择频率
    feature_importance = np.zeros(len(X_train.columns))
    feature_importance[boruta_selector.support_] = 1
    feature_importance[boruta_selector.support_weak_] = 0.5

    results = {
        'train_accuracy': train_accuracy,
        'valid_accuracy': valid_accuracy,
        'selected_features_freq': dict(zip(X_train.columns, feature_importance))
    }

    print('Features in the green area:', selected_features)
    print('Features in the blue area:', tentative_features)

    return selected_features, results


def elastic_net_feature_selection(train_data, valid_data, label_col, final_n_features, max_iter=200, n_jobs=-1):
    """
    使用Elastic Net进行特征选择
    :param train_data: 训练数据集
    :param valid_data: 验证数据集
    :param label_col: 标签列名称
    :param final_n_features: 最终选择的特征数量
    :param max_iter: 最大迭代次数
    :param n_jobs: 并行计算的工作进程数
    :return: 选择的特征列表和结果字典
    """
    columns_to_drop = ['mcg_file', 'filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x', 'label_y']


    X_train, y_train = preprocess_data(train_data, label_col, columns_to_drop)
    X_valid, y_valid = preprocess_data(valid_data, label_col, columns_to_drop)
    X_train, y_train = X_train.fillna(0), y_train.fillna(0)
    X_valid, y_valid = X_valid.fillna(0), y_valid.fillna(0)

    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_valid_scaled = scaler.transform(X_valid)

    # 初始化ElasticNetCV，使用交叉验证选择最佳参数
    enet_cv = ElasticNetCV(
        l1_ratio=[.2, .4, .6, .8,1],
        n_alphas=100,
        cv=5,
        random_state=42,
        max_iter=max_iter,
        n_jobs=n_jobs
    )
    print('开始训练Elastic Net模型')
    # 训练Elastic Net模型
    enet_cv.fit(X_train_scaled, y_train)

    # 获取特征重要性（系数的绝对值）
    feature_importance = pd.Series(np.abs(enet_cv.coef_), index=X_train.columns)

    # 选择非零系数的特征
    nonzero_features = feature_importance[feature_importance != 0].index.tolist()

    # 如果非零特征数量大于目标特征数，选择系数绝对值最大的前final_n_features个特征
    if len(nonzero_features) > final_n_features:
        selected_features = feature_importance.sort_values(ascending=False)[:final_n_features].index.tolist()
    else:
        selected_features = nonzero_features

    # 使用选定的特征训练最终模型
    X_train_selected = X_train[selected_features]
    X_valid_selected = X_valid[selected_features]

    # 使用随机森林作为最终模型（与原boruta保持一致）
    final_model = RandomForestClassifier(n_estimators=100, n_jobs=n_jobs, max_depth=5)
    final_model.fit(X_train_selected, y_train)

    # 计算训练集和验证集的准确率
    train_accuracy = accuracy_score(y_train, final_model.predict(X_train_selected))
    valid_accuracy = accuracy_score(y_valid, final_model.predict(X_valid_selected))

    # 计算特征选择频率（这里用系数的归一化绝对值代替）
    normalized_importance = feature_importance / feature_importance.max()

    results = {
        'train_accuracy': train_accuracy,
        'valid_accuracy': valid_accuracy,
        'selected_features_freq': dict(zip(X_train.columns, normalized_importance)),
        'best_alpha': enet_cv.alpha_,
        'best_l1_ratio': enet_cv.l1_ratio_
    }

    # 类似于boruta的输出
    print('Selected features:', selected_features)
    print(f'Number of selected features: {len(selected_features)}')
    print(f'Best alpha: {enet_cv.alpha_:.6f}')
    print(f'Best l1_ratio: {enet_cv.l1_ratio_:.6f}')

    return selected_features, results

def execute_selection_on_folds(id_pkl_path, features_pkl_path, feature_num, label_name, fold):
    # 划分数据集
    train_data, test_data, valid_data, train_valid_data, df_features = get_data(id_pkl_path, features_pkl_path,
                                                                                fold=fold)

    # 递归特征消除
    # features, record = rfecv_feature_selection(train_data=train_data, valid_data=valid_data,
    #                                          label_col=label_name, final_n_features=feature_num)
    # 稳定重采样选择
    # features, record = stability_selection(train_valid_data, test_data,
    #                                        label_name, final_n_features=feature_num,
    #                                        n_iterations=50, selection_threshold=0.5, model_mode='random_forest')

    # 确定只使用一致性数据----
    excel_path = "./files/data/data_index/data_V1114/cons_plus/诊断模型数据12.3新增.xlsx"
    sheet_data = pd.read_excel(excel_path, sheet_name='总表')
    cons_sheet = sheet_data[['心磁号', '造影', '人工']]
    # 选出cons_sheet中 造影结果和人工一致的数据
    cons_sheet = cons_sheet[cons_sheet['造影'] == cons_sheet['人工']]
    # 选出train_valid_data中心磁号在cons_sheet中的数据
    train_valid_data = train_valid_data[train_valid_data['mcg_file'].isin(cons_sheet['心磁号'])]

    # 在elastic net之前使用 ---------------------------------------------------
    kept_features = remove_collinear_features(
        data=train_valid_data,
        target_col=label_name,
        threshold=0.95,
        remove_cols=['mcg_file']
    )
    # 更新数据
    train_valid_data = train_valid_data[kept_features + [label_name] + ['mcg_file']]
    test_data = test_data[kept_features + [label_name] + ['mcg_file']]
    #
    # # 然后进行elastic net特征选择
    # enet_selected_features, enet_results = elastic_net_feature_selection(
    #     train_data=train_valid_data,
    #     valid_data=test_data,
    #     label_col=label_name,
    #     final_n_features=2000,
    #     max_iter=1000,
    #     n_jobs=10
    # )
    # features, record = enet_selected_features,enet_results

    # 继续后续的boruta选择
    # train_valid_data = train_valid_data_filtered[enet_selected_features + [label_name] + ['mcg_file']]
    # test_data = test_data_filtered[enet_selected_features + [label_name] + ['mcg_file']]

    features, record = boruta_feature_selection(
        train_valid_data,
        test_data,
        label_name,
        final_n_features=feature_num,
        max_iter=100
    )

    # 补充回删除的列标
    features.append('label')
    features.append('mcg_file')
    selected_features = df_features[features]
    return selected_features, record


def stable_res_extract(record_results, df_features, mean_weights_threshold=0.5, std_weights_threshold=0.1, top_n=300,
                       vis=True):
    """
    从稳定选择的特征频率中，筛选出平均权重和标准差都超过阈值的特征
    :param record_results: 5折上的稳定选择特征频率
    :param df_features: 原始特征数据表
    :param mean_weights_threshold:
    :param std_weights_threshold:
    :param n:
    :param vis:
    :return: 高值特征
    """
    # 准备列名
    columns_to_drop = ['mcg_file', 'filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x', 'label_y']
    X = df_features.drop(columns=columns_to_drop + ['label'], errors='ignore')

    freq_on_folds = []
    # 观察 freq 分布
    for current_fold in range(5):
        current_freq_arr = record_results[current_fold]['selected_features_freq']
        freq_on_folds.append(current_freq_arr)

    freq_on_folds = pd.DataFrame(freq_on_folds)
    # return freq_on_folds
    # print(freq_on_folds.shape, X.shape)
    # freq_on_folds.columns = X.columns

    # 计算每个特征的平均权重和标准差
    mean_weights = freq_on_folds.mean(axis=0)
    std_weights = freq_on_folds.std(axis=0)

    # 筛选出重要且高重复的特征，例如平均权重大于某个阈值且标准差小于某个阈值
    important_features = (mean_weights > mean_weights_threshold) & (std_weights < std_weights_threshold)
    filtered_features = freq_on_folds.loc[:, important_features]

    # 对特征按平均权重排序
    sorted_indices = mean_weights[important_features].sort_values(ascending=False).index
    sorted_filtered_features = filtered_features[sorted_indices]

    # 取前 n 个特征
    top_n_features = sorted_indices[:top_n]

    # 提取前 n 个特征的所有数据
    top_features_data = df_features[top_n_features.tolist()]

    if vis:
        # 可视化筛选后的特征
        plt.figure(dpi=200)
        sns.heatmap(sorted_filtered_features.T, cmap='YlOrRd', cbar=True)
        plt.xlabel('Fold')
        plt.ylabel('Feature')
        plt.title('Important and Consistent Features')
        # plt.xticks(ticks=np.arange(len(sorted_filtered_features.columns)), labels=sorted_filtered_features.columns,
        #            rotation=90)
        plt.yticks(rotation=0)
        plt.xticks(fontsize=2, rotation=90)
        plt.tight_layout()
        plt.show()

    return top_features_data


def remove_multicollinearity(data, threshold=0.9):
    """
    移除数据中多重共线性的特征。

    参数:
    data (pandas.DataFrame): 包含特征的数据框。
    threshold (float): 相关系数阈值，用于识别高度相关的特征。

    返回:
    pandas.DataFrame: 移除了高度相关特征后的数据框。
    """
    # 计算特征间的相关系数矩阵
    corr_matrix = data.corr().abs()

    # 找到相关系数高于阈值的特征对
    high_corr_var = np.where(corr_matrix > threshold)
    high_corr_var = [(corr_matrix.columns[x], corr_matrix.columns[y]) for x, y in zip(*high_corr_var) if
                     x != y and x < y]

    # 移除相关特征
    to_drop = set()
    for var_pair in high_corr_var:
        # 只添加其中一个变量到移除列表
        to_drop.add(var_pair[1])

    reduced_data = data.drop(columns=to_drop)
    print(f"Removed {len(to_drop)} correlated features.")
    return reduced_data


def remove_collinear_features(data, target_col, threshold=0.95, remove_cols=None, variance_threshold=0.0):
    """
    移除零方差特征和高度共线性的特征，带进度显示
    """
    if remove_cols is None:
        remove_cols = []

    # 复制数据，只保留数值列
    df = data.copy()
    cols_to_exclude = remove_cols + [target_col]

    # 只选择数值型列
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    features = [col for col in numeric_columns if col not in cols_to_exclude]

    if len(features) == 0:
        raise ValueError("No numeric features found in the dataset")

    print(f"Initial number of features: {len(features)}")
    print(f"Excluded non-numeric features: {len(data.columns) - len(cols_to_exclude) - len(features)}")

    # 第一步：移除零方差特征（添加进度条）
    print("\nCalculating variances...")
    variances = {}
    for feat in tqdm.tqdm(features, desc="Variance calculation"):
        variances[feat] = df[feat].var()

    variances = pd.Series(variances)
    constant_features = variances[variances <= variance_threshold].index.tolist()

    # 打印零方差特征信息
    if len(constant_features) > 0:
        print(f"\nFound {len(constant_features)} zero/low variance features")

    # 移除零方差特征
    features = [f for f in features if f not in constant_features]
    print(f"Features after removing zero/low variance: {len(features)}")

    # 第二步：分批计算相关性以降低内存使用
    if len(features) > 1:
        print("\nCalculating correlations...")

        # 初始化要删除的特征集合
        to_drop = set()
        batch_size = 1000  # 可以根据内存调整这个值

        # 计算需要处理的批次数
        n_batches = (len(features) + batch_size - 1) // batch_size

        # 使用tqdm创建进度条
        with tqdm.tqdm(total=n_batches, desc="Correlation calculation") as pbar:
            for i in range(0, len(features), batch_size):
                batch_features = features[i:i + batch_size]

                # 确保数据是数值型
                batch_data = df[batch_features].astype(float).values
                all_data = df[features].astype(float).values

                # 标准化数据
                batch_std = np.std(batch_data, axis=0)
                all_std = np.std(all_data, axis=0)

                # 避免除以零
                batch_std[batch_std == 0] = 1
                all_std[all_std == 0] = 1

                batch_data = (batch_data - np.mean(batch_data, axis=0)) / batch_std
                all_data = (all_data - np.mean(all_data, axis=0)) / all_std

                # 计算相关性矩阵
                corr_matrix = np.dot(batch_data.T, all_data) / (len(df) - 1)

                # 将相关性矩阵转换为DataFrame
                corr_matrix = pd.DataFrame(corr_matrix, index=batch_features, columns=features)

                # 遍历当前批次中的每个特征
                for col in batch_features:
                    if col not in to_drop:  # 如果特征还没有被标记为删除
                        # 获取与当前特征的相关性
                        correlations = corr_matrix.loc[col]

                        # 找出高相关特征
                        high_corr_features = correlations[
                            (correlations.abs() > threshold) &
                            (correlations.abs() < 1.0)
                            ].index.tolist()

                        if high_corr_features:
                            to_drop.add(col)

                pbar.update(1)

                # 清理内存
                del batch_data, all_data, corr_matrix

        # 更新特征列表
        features = [f for f in features if f not in to_drop]

        print(f"\nRemoved {len(to_drop)} collinear features")

    # 汇总信息
    print("\nFeature Selection Summary:")
    print(f"Initial features: {len(data.columns) - len(cols_to_exclude)}")
    print(f"Removed non-numeric features: {len(data.columns) - len(cols_to_exclude) - len(numeric_columns)}")
    print(f"Removed zero/low variance features: {len(constant_features)}")
    print(f"Removed collinear features: {len(to_drop) if 'to_drop' in locals() else 0}")
    print(f"Final number of features: {len(features)}")

    # 如果需要，打印被排除的非数值型特征
    non_numeric_features = [col for col in data.columns if col not in numeric_columns and col not in cols_to_exclude]
    if non_numeric_features:
        print("\nExcluded non-numeric features:")
        for feat in non_numeric_features[:10]:  # 只打印前10个
            print(f"- {feat} (type: {data[feat].dtype})")
        if len(non_numeric_features) > 10:
            print(f"... and {len(non_numeric_features) - 10} more")

    return features
class DataScaler:
    """数据标准化方法类"""

    def __init__(self, data=None):
        self.data = data  # data could be DataFrame
        self.scaled_data = None
        self.scalers = {
            "StandardScaler": StandardScaler(),
            "MinMaxScaler": MinMaxScaler(),
            "MaxAbsScaler": MaxAbsScaler(),
            "RobustScaler": RobustScaler(),
            "Normalizer": Normalizer(),
            "PowerTransformer (Yeo-Johnson)": PowerTransformer(method='yeo-johnson'),
            "QuantileTransformer (normal)": QuantileTransformer(output_distribution='normal'),
            "SigmoidTransformer": FunctionTransformer(func=lambda x: 1 / (1 + np.exp(-x)))
        }

    def scale_data(self, data=None, method='StandardScaler'):
        """优先用传入的数据，再寻初始化数据. 执行数据标准化"""
        if data is not None:
            self.data = data
        if self.data is None:
            raise ValueError("Missing data.")

        if method not in self.scalers:
            raise ValueError(f"Unknown scaler method: {method}. Available methods: {list(self.scalers.keys())}.")
        scaler = self.scalers[method]
        self.scaled_data = scaler.fit_transform(self.data.fillna(0))
        return self.scaled_data
