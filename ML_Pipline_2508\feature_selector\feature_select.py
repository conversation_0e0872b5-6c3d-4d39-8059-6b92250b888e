"""
@Project ：ML_Pipline
@File    ：feature_select.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2024/7/16 16:09
@Discribe：
    改自qdg同学的特征选择代码。源代码可能会有点乱..
    使用id_pkl(分样好的数据标记集含标签) 与 FEATURE_PKL(数据特征集) 进行特征选择
    旧版本的特征选择，
"""
import os
import pickle

import numpy as np
import pandas as pd
from tqdm import tqdm

from feature_selector.select_utils import (execute_selection_on_folds, get_data, stable_res_extract,
                                           remove_zero_variance_features, remove_multicollinearity, DataScaler)
from feature_selector.stats_vis import DimensionalityReductionVisualizer


class FeatureSelector:
    def __init__(self, data_id_path=None, all_features_path=None, feature_num=50, seed=42, save_flag=False):
        self.data_id_path = data_id_path
        self.all_features_path = all_features_path
        self.feature_num = feature_num
        self.seed = seed
        self.save_flag = save_flag
        self.record_results = None
        self.columns_to_drop = ['mcg_file', 'filename_1', 'filename_2', 'filename_3', 'filename_4', 'label_x',
                                'label_y']

    def run_stable_select(self):
        """
        运行稳定性选择
        :return:
        """
        # 初始化参数
        seed = self.seed
        feature_num = self.feature_num
        save_flag = self.save_flag
        data_id_path = self.data_id_path
        all_features_path = self.all_features_path

        selected_features_results = []
        record_results = []
        for current_fold in tqdm(range(5)):
            # 执行特征选择
            selected_features, record = execute_selection_on_folds(
                data_id_path,
                all_features_path,
                feature_num,
                'label',
                current_fold
            )

            selected_features_results.append(selected_features)
            record_results.append(record)

            # 保存特征结果
            if save_flag:
                feature_filename = f'integrated_CI_features_stable_F5_s{seed}_fold{current_fold}.pkl'
                feature_file_path = os.path.join('./files/saved_features/selected_features', feature_filename)
                with open(feature_file_path, 'wb') as f:
                    pickle.dump(selected_features, f)

        self.record_results = record_results

        return selected_features_results, record_results

    def post_stable(self, data_id_path, all_features_path, record_results, seed, save_flag=False):
        """
        读取stable方法选出的特征并分析
        :param data_id_path:
        :param all_features_path:
        :param record_results:
        :param seed:
        :param save_flag:
        :return:
        """
        data_id_path, all_features_path, seed, save_flag = self.data_id_path, self.all_features_path, self.seed, self.save_flag
        record_results = self.record_results

        if not record_results:
            print("请先执行run_stable_select生成record_results")

        selected_features_results = []
        for i in range(5):
            with open("./files/saved_features/selected_features/integrated_CI_features_stable_F5_s42_fold" + str(
                    i) + ".pkl",
                      "rb") as f:
                selected_features = pickle.load(f)
            selected_features_results.append(selected_features)

        train_data, test_data, valid_data, train_valid_data, df_features = get_data(data_id_path, all_features_path,
                                                                                    fold=0)
        features_df = stable_res_extract(record_results, df_features, top_n=150, vis=True)

        if save_flag:
            # 保存，从5折中选出的 相对稳定的特征
            with open('./files/saved_features/selected_features/integrated_CI_features_stable_F5_s' + str(
                    seed) + '_fold_all_top75' + '.pkl', 'wb') as f:
                pickle.dump(features_df, f)

    def run_rfecv_select(self):
        pass

    def post_rfecv(self):
        """
        由于RFECV方法每次选完还是1w多特征，需要进行二次筛选
        :return:
        """
        from functools import reduce

        # 读取观察结果
        data_id_path, all_features_path, seed, save_flag = self.data_id_path, self.all_features_path, self.seed, self.save_flag
        record_results = self.record_results

        if not record_results:
            print("请先执行run_rfecv_select生成record_results")
        selected_features_results = []
        # 获取该特征下的数据以及对应标签
        train_data, test_data, valid_data, train_valid_data, df_features = get_data(data_id_path,
                                                                                    all_features_path,
                                                                                    fold=0)

        for i in range(5):
            with open(
                    "./files/saved_features/selected_features/integrated_features_rfecv_F5_s42_fold" + str(i) + ".pkl",
                    "rb") as f:
                selected_features = pickle.load(f)
            selected_features_results.append(selected_features)

        # 找出共有的列名 只保留共有的列
        all_columns = [set(df.columns) for df in selected_features_results]
        common_columns = list(reduce(lambda x, y: x.intersection(y), all_columns))
        merged_df = df_features[common_columns]
        # 先移除非特征列
        columns_to_drop = self.columns_to_drop
        merged_df_drop = merged_df.drop(columns=columns_to_drop + ['label'], errors='ignore')
        # 删除 0 方差的特征
        merged_df_v0 = remove_zero_variance_features(merged_df_drop)
        # 删除 多重共线的特征
        merged_df_v1 = remove_multicollinearity(merged_df_v0, 0.8)

        # 给merged_df补充label和mcg_file标签
        merged_df_with_label = merged_df_v1.join(df_features[['label', 'mcg_file']])

        # 根据mcg_file与train_data进行匹配
        merged_df_train_with_label = merged_df_with_label.merge(train_data[['mcg_file']], on='mcg_file', how='inner')

        # 把训练集标签label根据mcg_file重新赋给merged_df_train_with_label
        merged_df_train = merged_df_train_with_label.drop(columns=columns_to_drop + ['label'], errors='ignore')

        # PCA特征降维及可视化

        # 数据标准化
        scaler = DataScaler(merged_df_train)
        scaled_data = scaler.scale_data(method='SigmoidTransformer')

        # 降维可视化
        visualizer = DimensionalityReductionVisualizer(scaled_data, train_data['label'].values)
        visualizer.visualize(dim_method='pca')

    def post_rfe_select(self, record_results, seed):
        """
        通过result 来回溯保存想要数量的特征结果
                适用于传统RFE筛选出的结果
        :param record_results:
        :param seed:
        :return:
        """

        # 保存特定数量的特征结果
        features_pkl = 'D:/wyh/Code/daily_debug/ML_Pipline/saved_features/June_integrated_dataset_feature_all.pkl'
        id_pkl = 'D:/wyh/Code/daily_debug/ML_Pipline/data/data_index/data_V0624_integrated/S42-F5.pkl'
        feature_num = 70  # 特征数量
        fold = 0
        _, _, _, _, df_features = get_data(id_pkl, features_pkl, fold=0)

        # 初始化一个列表来存储每一折的selected_features结果
        selected_features_per_fold = []

        # 遍历每一折
        for fold_results in record_results:
            # 初始化一个列表来存储当前折特征数量为70的特征名
            features_at_70 = []
            for feature_count, _, _, features in fold_results:
                if feature_count == 200:
                    features_at_70.extend(features)  # 收集当前折特征数量为70的所有特征名

            # 从df_features中选择当前折特征数量为70的特征名对应的列
            if features_at_70:  # 确保列表不为空
                selected_features = df_features[features_at_70]
                selected_features_per_fold.append(selected_features)
            else:
                # 如果没有特征数量为70的记录，可以添加空DataFrame或其他占位符
                selected_features_per_fold.append(pd.DataFrame())

        # 输出或处理每一折的selected_features
        for i, features_df in enumerate(selected_features_per_fold, 1):
            print(f"Features for Fold {i}:")
            print(features_df.head())  # 打印每个DataFrame的前几行作为示例
            # 保存特征结果
            with open(r'./saved_features/selected_features/consistency_features_rfe_F5_s' + str(seed) + '_fold' + str(
                    i - 1) + '_top200' + '.pkl', 'wb') as f:
                pickle.dump(features_df, f)

    def test_rfecv(self, merged_df_with_label):
        """
        测试降维后rfecv特征的训练与结果
        :param merged_df_with_label:
        :return:
        """

        # 测试降维后rfecv特征的训练与结果  407特征未经过PCA等降维 ----------------------------------------------------
        from model_trainer.models import Models, get_model_predict_results, plot_folds_results
        all_features_path, data_id_path, columns_to_drop = self.all_features_path, self.data_id_path, self.columns_to_drop
        results = []
        models_trained = []

        for fold in range(5):
            train_data, test_data, valid_data, train_valid_data, df_features = get_data(data_id_path,
                                                                                        all_features_path,
                                                                                        fold=fold)
            merged_df_train_with_label = merged_df_with_label.merge(train_data[['mcg_file']], on='mcg_file',
                                                                    how='inner')
            merged_df_test_with_label = merged_df_with_label.merge(test_data[['mcg_file']], on='mcg_file', how='inner')

            merged_df_train_x = merged_df_train_with_label.drop(columns=columns_to_drop + ['label'], errors='ignore')
            merged_df_train_y = train_data['label'].values

            merged_df_test_x = merged_df_test_with_label.drop(columns=columns_to_drop + ['label'], errors='ignore')
            merged_df_test_y = test_data['label'].values
            models = Models()
            models.xgb_model()
            model, model_name = models.model, models.model_name
            model.fit(merged_df_train_x, merged_df_train_y)

            result_dict = get_model_predict_results(model=model,
                                                    x_train=merged_df_train_x,
                                                    y_train=merged_df_train_y,
                                                    x_test=merged_df_test_x,
                                                    y_test=merged_df_test_y,
                                                    x_valid=merged_df_test_x,
                                                    y_valid=merged_df_test_y,
                                                    )
            results.append(result_dict)
            models_trained.append(model)

            # 可视化训练结果
        avg = plot_folds_results(results)

    def gmm_feature_aug(self):
        # a) 软聚类特征：
        # - 使用GMM对数据进行聚类，然后使用每个数据点属于各个聚类的概率作为新特征。
        # - 这可以捕捉数据的多模态特性。
        # b) 异常分数：
        # - 计算每个数据点的对数似然作为异常分数。
        # - 这可以作为一个新的特征，表示数据点的"不寻常"程度。
        pass


def get_feature_composition(final_features_df,
                            ci_features_path='./files/saved_features/June_integrated_CI_nonstats_feature_all.pkl'):
    """选择后特征的组成成分占比分析"""
    from feature_generator.utils.feature_rigist import FeatureRegistry

    fg = FeatureRegistry()
    fg.reload_from_file("./files/configs/feature_registry_V2.json")
    selected_features_ts = FeatureRegistry.filter_features_for_function('get_ts_features')
    selected_features_stat = FeatureRegistry.filter_features_for_function('get_stat_features')
    selected_features_ti = FeatureRegistry.filter_features_for_function('get_ti_features')
    with open(ci_features_path, 'rb') as f:
        f_ci = pickle.load(f)
    selected_features_ci = f_ci.drop(columns=['label', 'mcg_file']).columns.tolist()

    cur_features_list = [col for col in final_features_df.columns if col.startswith(('xh_', 'J_'))]

    # 计算每个列表与 DataFrame 列的重合数量
    feature_df = final_features_df
    overlap_ts = len(set(selected_features_ts) & set(feature_df.columns))
    overlap_stat = len(set(selected_features_stat) & set(feature_df.columns))
    overlap_ti = len(set(selected_features_ti) & set(feature_df.columns))
    overlap_ci = len(set(selected_features_ci) & set(feature_df.columns))
    overlap_cur = len(set(cur_features_list) & set(feature_df.columns))

    groups = ['ts', 'stats', 'ti', 'ci', 'cur']
    overlaps = [overlap_ts, overlap_stat, overlap_ti, overlap_ci, overlap_cur]

    # 绘制直方图
    plt.figure(figsize=(10, 6), dpi=200)
    plt.bar(groups, overlaps)
    plt.title('Overlap of DataFrame Column with Lists')
    plt.xlabel('Lists')
    plt.ylabel('Number of Overlapping Elements')

    # 添加数值标签
    for i, v in enumerate(overlaps):
        plt.text(i, v, str(v), ha='center', va='bottom')

    plt.show()

    # 计算并输出占比
    total_df_elements = len(feature_df.columns)
    percentages = [overlap / total_df_elements * 100 for overlap in overlaps]

    for i, percentage in enumerate(percentages, 1):
        print(f"List {i} overlap percentage: {percentage:.2f}%")
    return percentages


if __name__ == '__main__':
    # 常规特征选择 --------------------------------------------------------
    params = {
        'seed': 42,
        'feature_num': 200,
        'data_id_path': './files/data/data_index/data_V1114/cons_plus/S42-F5.pkl',
        'all_features_path': './files/saved_features/feature_V241119_all_1860.pkl',
    }

    train_data, test_data, valid_data, train_valid_data, df_features = get_data(id_pkl=params['data_id_path'],
                                                                                features_pkl=params[
                                                                                    'all_features_path'],
                                                                                fold=0)

    fg = FeatureSelector(**params)
    # test= df_features[["mcg_file","label"]]

    selected_features_results, record_results = fg.run_stable_select()

    # 确认df_feature
    final_features_df = stable_res_extract(record_results, df_features, top_n=2000, vis=True,
                                           mean_weights_threshold=0.3, std_weights_threshold=1)
    print(final_features_df.shape)
    print(df_features.shape)
    # final_features_df.drop(columns=['clinic_diabetes'], inplace=True)
    # final_features_df['clinic_diabetes']

    # 选择后特征的组成成分占比分析 /保存被选中特征  -----------------------------------------------------------------
    get_feature_composition(final_features_df)
    # se_features = df_features[freq_on_folds.columns]
    with open('./files/saved_features/selected_features/features_V1119F5S42_els_top969.pkl', 'wb') as f:
        pickle.dump(final_features_df, f)



    # 不同阈值下final_features_df 快速试一下训练精度。 --------------------------------------------------------------
    from model_trainer.train_scripts.train_model_on_folds import train_model_on_stable_folds

    test_res = []
    for r in [0,0.2, 0.4, 0.6, 0.8, 0.9]:
        final_features_df = stable_res_extract(record_results, df_features, top_n=1400, vis=False,
                                               mean_weights_threshold=r, std_weights_threshold=1)
        print(len(final_features_df.columns))
        params = {
            "feature_pkl": './files/saved_features/features_all_cur_V0905_2534.pkl',
            "id_pkl": "./files/data/data_index/data_V0826_cons/S42-F5.pkl",
            "selected_feature_pkl": './files/saved_features/features_all_cur_V0905_2534.pkl',
            "save_res_path": "./files/saved_models/els_boruta_178.pkl",
            'selected_feature_list': final_features_df.columns,
        }
        results, avg0 = train_model_on_stable_folds(**params, save_flag=True, mode='valid_result_threshold')
        print(avg0)
        test_res.append((r, len(final_features_df.columns), avg0))

    print('test_res:')
    for r, l, avg in test_res:
        print(f'mean_weights_threshold={r:.2f}, feature_num={l}, valid_result_threshold={np.mean(avg):.4f}')

    # 绘制test_res结果
    import matplotlib.pyplot as plt
    import seaborn as sns

    sns.set_style('whitegrid')
    fig, ax = plt.subplots(figsize=(12, 6))

    threshold, feature_num, valid_result = zip(*test_res)
    ax.plot(threshold, np.mean(valid_result, axis=1), marker='o', label='valid_result')
    ax2 = ax.twinx()
    ax2.plot(threshold, feature_num, marker='x', label='feature_num', color='r')
    ax2.set_ylim([0, 1400])
    ax.set_xlabel('mean_weights_threshold')
    ax.set_ylabel('valid_result_threshold')
    ax2.set_ylabel('feature_num')
    ax.legend(loc='upper left')
    ax2.legend(loc='upper right')
    plt.title('test_res')
    plt.show()


    # -------------------------------------- 计算其他随机种子的结果并保存 --------------
    seeds = [7, 21, 42, 63, 84]
    for seed in seeds:
        params = {
            'seed': 42,
            'feature_num': 50,
            'data_id_path': './files/data/data_index/data_V0820/S' + str(seed) + '-F5.pkl',
            'all_features_path': './files/saved_features/features_V0821_all_tspt_2534.pkl',
        }

        train_data, test_data, valid_data, train_valid_data, df_features = get_data(id_pkl=params['data_id_path'],
                                                                                    features_pkl=params[
                                                                                        'all_features_path'],
                                                                                    fold=0)
        fg = FeatureSelector(**params)
        selected_features_results, record_results = fg.run_stable_select()
        # 确认df_feature
        final_features_df = stable_res_extract(record_results, df_features, top_n=150, vis=True)
        with open('./files/saved_features/selected_features/features_V0821F5S' + str(seed) + '_tspt_stable_top150.pkl',
                  'wb') as f:
            pickle.dump(final_features_df, f)

    # -------------------------------------- 计算特征交叉以及相关性 热图----
    # # 计算features_df.columns 的特征中，在f1.columns中的特征的占比，在f2.columns中的特征的占比
    # features_set = set(df_features.columns)
    # f1_set = set(f1.columns)
    # f2_set = set(f2.columns)
    # # 计算交集
    # common_with_f1 = features_set.intersection(f1_set)
    # common_with_f2 = features_set.intersection(f2_set)
    # # 计算占比
    # proportion_with_f1 = len(common_with_f1) / len(features_set)
    # proportion_with_f2 = len(common_with_f2) / len(features_set)
    #
    #
    # import seaborn as sns
    # import numpy as np
    # df1 = df_features.reset_index(drop=True)
    # df2 = f2.drop(columns=['label','mcg_file']).reset_index(drop=True)
    #
    # # df3 = df2.select_dtypes(exclude=[np.number])  # 保留df2中的数值类型列
    # combined_df = pd.concat([df1, df2], axis=1)
    # corr_matrix = combined_df.corr()
    #
    # # 仅提取 df1 列与 df2 列之间的相关性部分
    # relevant_corr = corr_matrix.iloc[:df1.shape[1], df1.shape[1]:]
    #
    # # 绘制热图
    # plt.figure(figsize=(16, 10),dpi=300)
    #
    # sns.heatmap(relevant_corr.T.astype(float), cmap='coolwarm', fmt=".2f")
    # plt.title(f'Correlations between ML-Features and CI')
    # plt.tight_layout()
    # plt.show()

